<template>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="190px" size="default" v-loading="loading" @submit.prevent>
        <el-row>
            <el-col :span="24">
                <span class="title">>>>法律诉讼及合规风险（该数据来源于中石化产融数智平台，仅供参考。如有疑问，请拨打电话95388转6或在线客服咨询）</span>
            </el-col>
            <el-divider></el-divider>
        </el-row>
        <el-row class="grid-row" style="margin-top: 10px">
            <el-col :span="24" class="grid-cell">
                <el-table 
                    :data="tableData" 
                    class="lui-table"
                    border
                    stripe
                    height="calc(100vh - 300px)"
                    size="default" 
                    highlight-current-row>
                    <el-table-column type="index" header-align="center" align="center" width="60">
                    </el-table-column>
                    <el-table-column prop="entName" label="企业名称" header-align="center" align="left" min-width="220">
                    </el-table-column>
                    <el-table-column prop="sourceName" label="风险分类" header-align="center" align="center" min-width="150">
                    </el-table-column>
                    <el-table-column prop="riskLevel" label="风险等级" header-align="center" align="center" min-width="150">
                    </el-table-column>
                    <el-table-column prop="varName" label="风险名称" header-align="center" align="left" min-width="150">
                    </el-table-column>
                    <el-table-column label="详情" header-align="center" align="center" min-width="100">
                        <template #default="scope">
                            <el-button size="small" class="lui-table-button" type="primary" @click="viewData(scope.row)">查看
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="预警规则" header-align="center" align="left" min-width="150">
                        <template #default="scope">
                            <span>变量名称：{{scope.row.varName}}</span><br>
                            <span>时间颗粒：{{scope.row.varPeriod}}</span><br>
                            <span>判断条件：{{scope.row.threshold}}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
    </el-form>
    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="showDialog"
        v-model="showDialog"
        title="详情"
        top="5vh"
        width="60%">
        <cbsfxView :varDetail="varDetail"></cbsfxView>
    </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axiosUtil from "@lib/axiosUtil";
import cbsfxView from "./cbsfxView.vue";
const props = defineProps({
    params: {
        type: Object,
        default: () => ({})
    },
});
const emit = defineEmits([]);
const tableData = ref([]);

const showDialog = ref(false);
const varDetail = ref([]);
onMounted(() => {
    queryCbsfxList();
});
// 获取承包商风险列表
const queryCbsfxList = () => {
    axiosUtil.get('/backend/extApi/getCbsfxList', {
        CBSDWQC: props.params.CBSDWQC
    }).then((res) => {
        // tableData.value = [
        //     { "entName": "公司A", "sourceName": "传感器1", "riskLevel": 3, "varName": "温度", "warnDate": "2025-06-25", "ruleId": "R001", "varPeriod": "小时", "threshold": 30,
        //         "varDetail": [
        //             [{key: '列1',value: '数据1'}, {key: '列2',value: '数据1'}, {key: '列3',value: '数据1'}],
        //             [{key: '列1',value: '数据1'}, {key: '列2',value: '数据1'}, {key: '列3',value: '数据1'}],
        //             [{key: '列1',value: '数据1'}, {key: '列2',value: '数据1'}, {key: '列3',value: '数据1'}],
        //         ]
        //     },
        //     { "entName": "公司B", "sourceName": "传感器2", "riskLevel": 2, "varName": "湿度", "warnDate": "2025-06-25", "ruleId": "R002", "varPeriod": "天", "threshold": 60,
        //         "varDetail":[
        //             [{key: '列1'}, {key: '列2'}, {key: '列3'}],
        //         ]
        //      },
        //     { "entName": "公司C", "sourceName": "传感器3", "riskLevel": 4, "varName": "压力", "warnDate": "2025-06-25", "ruleId": "R003", "varPeriod": "周", "threshold": 100 },
        //     { "entName": "公司D", "sourceName": "传感器4", "riskLevel": 1, "varName": "流量", "warnDate": "2025-06-25", "ruleId": "R004", "varPeriod": "分钟", "threshold": 50 }
        // ]
        if(res.data && res.data.data && res.data.data){
            tableData.value = res.data.data.result;
        }else{
            ElMessage.error("接口调用失败！");
        }
    });
};
// 查看
const viewData = (row) => {
    if(row.varDetail && row.varDetail.length > 0){
        varDetail.value = row.varDetail;
        showDialog.value = true;
    }else{
        ElMessage.warning("该记录无详情查看！");
    }
    
}

defineExpose({});
</script>

<style scoped>
.el-divider--horizontal {
    margin: 12px 0;
}
.title{
    font-size: 16px;
    font-weight: bold;
}
</style>