<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="formData.MBMC" placeholder="请输入模板名称"></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select
              v-model="formData.MBLX"
              value-key="value"
              placeholder="请选择模板类型"
              clearable
              filterable
          >
            <el-option
                v-for="item in templateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
       <div style="display: flex">
         <el-button type="primary" @click="getDataList"><el-icon><Search/></el-icon>查询</el-button>
         <el-button type="primary" class="lui-button-add" @click="addTemplate">
           <el-icon><Plus/></el-icon>新增
         </el-button>
         <!-- <el-button type="primary" @click="exportExcel">导出</el-button> -->
       </div>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table :data="tableData" height="calc(100vh - 250px)" border
                class="lui-table"
                stripe v-loading="loading">
        <EleProTableColumn v-for="prop in tableColumn" :col="prop" :key="prop.columnKey">
          <template #opration="{ row }">
            <el-button @click="viewMbmx(row)" class="lui-table-button" size="small"> 详情</el-button>
            <el-button
                v-if="row.STATUS == '0' || row.STATUS == '2'"
                @click="deleteRow(row)"
                class="lui-table-button"
            >
              删除</el-button
            >
            <el-button
                v-if="row.STATUS == '0' || row.STATUS == '2'"
                @click="editMbmx(row)"
                class="lui-table-button"
            >
              编辑</el-button
            >
            <el-button
                v-if="row.STATUS == '0' || row.STATUS == '2'"
                @click="enabledRow(row)"
                class="lui-table-button"
            >
              启用</el-button
            >
            <el-button
                v-if="row.STATUS == '1'"
                @click="disabledRow(row)"
                class="lui-table-button"
            >
              禁用</el-button
            >
            <el-button
                v-if="row.STATUS == '1'"
                @click="copyMbxx(row)"
                class="lui-table-button"
            >
              复制</el-button
            >
          </template>
        </EleProTableColumn>
      </el-table>
      <el-pagination
          @size-change="paginationObj.page == 1 ? getDataList() : ((paginationObj.page = 1), getDataList())"
          @current-change="getDataList"
          v-model:current-page="paginationObj.page"
          :page-sizes="[10, 20, 50, 100]"
          v-model:page-size="paginationObj.size"
          :total="paginationObj.total"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          background>
      </el-pagination>
    </div>

    <el-dialog
        title="模板创建"
        v-model="dwkhdialogVisible"
        width="85%"
        top="3vh"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        destroy-on-close
        @close="onClose"
    >
      <mbbjkp :isNew="isNew" :edit="editStatus" :MBID="editDataId" @close="onClose" />
      <template #footer>
        <el-button @click="onClose">返回</el-button>
      </template>
    </el-dialog>
  </el-form>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import axiosUtil from "@src/lib/axiosUtil.js";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {
  getZrmbglPaging, //准入模板分页查询
  getZrmbglDetail, //模板明细
  getZrmbglDisabled, //模板禁用
  getZrmbglEnabled, //模板启用
  deleteZrmbgl, //模板删除
  postZrmbglCopy, //模板复制
} from "@src/api/sccbsgl.js";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import mbbjkp from "./mbbjkp.vue";
// 上册查询条件
const formData = ref({
  MBMC: "",
  MBLX: "",
});
// 翻页对象
const paginationObj = ref({
  page: 1,
  size: 10,
  total: 100,
});
/**序号 */
const indexMethod = (index) => {
  return index + paginationObj.value.size * (paginationObj.value.page - 1) + 1;
};
// 模板类型下拉选项
const templateOptions = ref([
  {
    value: "QY",
    label: "企业",
  },
  {
    value: "DW",
    label: "队伍",
  },
]);
const tableColumn = ref([
  { label: "序号", type: "index", width: 55, align: "center", index: indexMethod },
  {
    label: "模板类型",
    prop: "MBLXMC",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "模板名称",
    prop: "MBMC",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "描述",
    prop: "MBMS",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作人",
    prop: "CZR",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作时间",
    prop: "CZSJ",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作",
    align: "center",
    showOverflowTooltip: true,
    width: 250,
    slot: "opration",
  },
]);
// 表格数据
const tableData = ref([]);

/**获取数据 */
const loading = ref(false);
const getDataList = () => {
  const { page, size } = paginationObj.value;
  loading.value = true;
  getZrmbglPaging({
    ...formData.value,
    page,
    size,
  })
    .then(({ data }) => {
      tableData.value = data.list ?? [];
      paginationObj.value.total = data.total ?? [];
    })
    .catch(() => {
      ElMessage({
        message: "查询模板列表失败",
        type: "error",
      });
    }).finally(() => {
      loading.value = false;
    })
};
onMounted(() => {
  getDataList();
});

// 禁用
const disabledRow = ({ MBID }) => {
  getZrmbglDisabled({
    id: MBID,
  })
    .then((result) => {
      ElMessage.success("禁用成功");
      getDataList();
    })
    .catch((err) => {
      ElMessage.error("禁用失败");
    });
};

// 启用
const enabledRow = ({ MBID }) => {
  getZrmbglEnabled({
    id: MBID,
  })
    .then((result) => {
      ElMessage.success("启用成功");
      getDataList();
    })
    .catch((err) => {
      ElMessage.error("启用失败");
    });
};

// 弹窗visible
const dwkhdialogVisible = ref(false);
// 模板ID，弹窗用
const editDataId = ref("");
// 是否为编辑状态
const editStatus = ref(true);
// 模板创建
const isNew = ref(false);
const addTemplate = () => {
  isNew.value = true;
  dwkhdialogVisible.value = true;
};
// 查看模板明细(详情)
const viewMbmx = ({ MBID }) => {
  editDataId.value = MBID;
  editStatus.value = false;
  dwkhdialogVisible.value = true;
};
// 模板编辑
const editMbmx = ({ MBID }) => {
  editDataId.value = MBID;
  dwkhdialogVisible.value = true;
};

// 删除模板
const deleteRow = ({ MBID }) => {
  ElMessageBox.confirm("确认删除该模板？", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then((result) => {
      deleteZrmbgl({
        id: MBID,
      })
        .then((result) => {
          ElMessage.success("删除成功");
          getDataList();
        })
        .catch((err) => {
          ElMessage.error("删除失败");
        });
    })
    .catch((err) => {});
};

//关闭对话框
const onClose = () => {
  /* params.ZYLB=[]
    params.khqj=[]
    params.SJYWBM="",
    params.PJDX="",
    params.JDLX="" */
  editStatus.value = true;
  editDataId.value = null;
  isNew.value = false;
  dwkhdialogVisible.value = false;
  getDataList();
  // dwkhdialogVisible.value = false;
};
/**导出Excel */
const exportExcel = () => {
  let par = {
    pageNum: paginationObj.value.currentPage,
    pageSize: paginationObj.value.pageSize,
    total: paginationObj.value.total,
    LOGINNAME: paginationObj.value.appUser.loginName,
    mbmc: paginationObj.value.mbmc,
    mblx: paginationObj.value.mblx,
  };
  let KHMC = "引进模板管理";
  let finparams = {
    title: KHMC,
    name: KHMC,
    params: par,
    column: [
      [
        {
          field: "MBLXMC",
          title: "模板类型",
          width: 300,
          halign: "center",
          align: "center",
        },
        {
          field: "MBMC",
          title: "模板名称",
          width: 300,
          halign: "center",
          align: "center",
        },
        { field: "MBMS", title: "描述", width: 300, halign: "center", align: "center" },
        { field: "CZR", title: "操作人", width: 300, halign: "center", align: "center" },
        {
          field: "CZSJ",
          title: "操作时间",
          width: 300,
          halign: "center",
          align: "center",
        },
      ],
    ],
  };
  axiosUtil.exportExcel("/sldwgl/mbgl/exportExcel", finparams);
};
// 复制模板
const copyMbxx = ({ MBID }) => {
  postZrmbglCopy({
    id: MBID,
  })
    .then((res) => {
      ElMessage({
        message: "复制成功",
        type: "success",
      });
      getDataList();
    })
    .catch((err) => {
      ElMessage({
        message: "复制失败",
        type: "error",
      });
    });
};
</script>
<style lang="scss" scoped>
.el-container {
  height: 100%;
  padding: 20px;
  background: #fff;
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}
.el-table .el-button + .el-button {
  margin-left: 0;
}
</style>
