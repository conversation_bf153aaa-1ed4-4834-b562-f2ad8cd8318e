<template>
    <div  class="dashboard-img" style="font-size:30px;">
        <el-main style="padding:5px;">
            <div class="content" align="center">
                <div class="content-center1" align="left">
                    <div class="content-center-left1">
                        <div class="content-in1">
                            <div class="my-panel2">
                                <div class="my-panel2-head">
                                    <div @click="changeDBTab('1')"
                                        :class="{ 'chooseTab': chooseDBTab == '1', 'noChooseTab': chooseDBTab != '1' }">待办工作
                                    </div>
                                    <div @click="changeDBTab('2')"
                                        :class="{ 'chooseTab': chooseDBTab == '2', 'noChooseTab': chooseDBTab != '2' }">已办工作
                                    </div>
                                </div>
                                <div class="my-panel2-body" style="height:calc(100vh - 180px);">
                                    <task-list :key="{ chooseDBTab, status }" :status="status"></task-list>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </el-main>
    </div>
</template>

<script>

import {
    defineComponent,
    toRefs,
    reactive,
    ref,
    getCurrentInstance,
    onMounted
}
    from 'vue'
import { useRoute, useRouter } from "vue-router";
import TaskList from "@views/workflow/TaskList.vue";
export default defineComponent({
    name: "dashboard",
    components: {
        TaskList
    },
    setup() {
        const route111 = useRoute();
        const router111 = useRouter();
        const state = reactive({
            chooseDBTab: '1',
            status: '1'
        })
        const changeDBTab = (tab) => {
            state.chooseDBTab = tab;
            if (tab == '1') {
                state.status = '1';
            } else {
                state.status = '3';
            }
        }
        return {
            aa: ref("Hello"),
            ...toRefs(state),
            changeDBTab
        }
    }
})
</script>
<style scoped>
.content {
    background-color: #FFFF !important;
}

.content-center1 {
    width: 99%;
    height: calc(100% - 0px);
}

.content-center-left1 {
    width: 100%;
    float: right;
    margin: 5px 5px 5px 5px;
    height: calc(100% - 15.5px);
    border-left: 1px solid #b5b6b8;
    border-right: 1px solid #b5b6b8;
    /* border-top: 1px solid #b5b6b8;
		border-bottom: 1px solid #b5b6b8; */
}

.content-center-left2 {
    width: 100%;
    float: right;
    margin: 5px 5px 5px 5px;
    height: calc(50% - 15.5px);
    border-left: 1px solid #b5b6b8;
    border-right: 1px solid #b5b6b8;
    /* border-bottom: 1px solid #b5b6b8;
		border-top: 1px solid #b5b6b8; */
}

.topTitle {
    font-size: 20px;
    font-weight: bold;
    color: #2384B7;
    line-height: 40px;
}

.content-in1 {
    width: 100%;
    float: left;
    height: 100%;
    border-right: 1px solid #b5b6b8;
    border-top: 1px solid #b5b6b8;
    border-bottom: 1px solid #b5b6b8;
}

.content-in2 {
    width: 30%;
    float: right;
    height: 100%;
    border-left: 1px solid #b5b6b8;
    border-top: 1px solid #b5b6b8;
    border-bottom: 1px solid #b5b6b8;
    /* border: 1px solid #b5b6b8; */
}

.my-panel2 {
    height: 100%;
    padding: 5px 0px;
    width: 100%;
}

.my-panel2-head {
    font-size: 18px;
    color: #2384B7;
    font-weight: bold;
    margin-left: 0px;
    border-bottom: 2px solid #2384B7;
    padding-bottom: 8px;
    padding-left: 5px;
    height: 20px;
}

.my-panel2-body {
    width: 100%;
    height: calc(100% - 41px);
}

.liclass {
    margin-right: 7px;
}

.chooseTab {
    cursor: pointer;
    float: left;
    height: 27px;
    border-bottom: 3px solid red;
    padding: 0px 20px;
    /* background: url(../../../static/img/arrow.png) 50% 22px no-repeat; */
}

.noChooseTab {
    cursor: pointer;
    color: #909399;
    float: left;
    height: 27px;
    padding: 0px 20px;
    /* border-bottom: 3px solid red; */
}

.dot {
    font-size: 16px;
    font-weight: bolder;
}

.colorRed {
    color: red;
    font-weight: bold;
    margin-right: 5px;
    font-size: 15px;
}</style>