<template>
  <div class="container">
    <div style="text-align: center;margin-top: 15px">
        <span style="font-size: 25px">
          {{ formData.BT }}
        </span>
    </div>
    <el-divider></el-divider>
    <div style="margin-top: 15px;text-align: center">
      <span style="margin-left: 15px">维护单位：{{ formData.ORGNA_NAME }}</span>
      <span style="margin-left: 15%">维护人：{{ formData.USER_NAME }}</span>
      <span style="margin-left: 15%">维护时间：{{ formData.WHSJ }}</span>
      <span style="margin-left: 15%">阅览数量：{{ formData.DJCS }}</span>
    </div>
    <div style="width: 100%;margin-top: 15px;height: 600px;overflow: auto">
      <div style="width: 1000px;margin: 0px auto">
        <div class="editor-content-view" v-html="formData.GZRWNR"></div>
        <div>
          <span>相关附件：</span>
          <vsfileupload :key="XXFBID" :editable="false" :busId="XXFBID" :showTip="false"></vsfileupload>
        </div>
      </div>

    </div>
  </div>
</template>

<script>

import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted
}
  from 'vue'
import axiosUtil from "@src/lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import {ElMessage, ElMessageBox} from 'element-plus';
import Editor from '../Editor/index.vue';
import comFun from '@src/lib/comFun.js';
import Vsfileupload from "../../components/vsfileupload";


export default defineComponent({
  components: {Editor, Vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
    XXFBID: {
      type: String,
      required: true
    },
    GZLX: {
      type: String,
      required: true
    },
    pageFlag: {
      type: String,
      required: true
    }

  },

  setup(props, context) {
    const state = reactive({

      maxSize: '2048',
      userInfo: vsAuth.getAuthInfo().permission,
      formData: {
        BT: "",
        GZRWNR: "",
        ZT: "0",
        XXFBID: props.XXFBID,
        WHR: vsAuth.getAuthInfo().permission.userId,
        WHSJ: null,
        ORGNA_NAME: vsAuth.getAuthInfo().permission.orgnaName,
        SFXYFK: "0",
        QSBM: null,
        DJCS: '0'
      },

      formSend: {
        XXFBID: "", //人员id
        currentPage: 1,
        pageSize: 10,
      },
      isView: false,
      qsbmArr: [], //签收部门
      ywbmArr: [], //业务部门
      ejdwArr: [], //二级单位
      checkAllYwbm: false,
      checkAllEj: false,
      // 富文本相关---------------
      indexFwb: 0,
      viewAble: false,
      emailForm: {
        msg: ""
      },
      fileUploadData: {
        busType: "demo",
        busId: props.XXFBID,
        standbyField0: "file_fwb_zp",
      },
      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显示
      content: null,

      header: {
        // token: sessionStorage.token
      }, // 有的图片服务器要求请求头需要有token
    })


    const emailForm = reactive({
      test_msg: ''
    })
    const getMsg = (val) => {
      state.formData.GZRWNR = val
    }
    // 关闭窗口
    const closeForm = () => {
      context.emit("closeForm")
    }


    // 查询
    const getFormData = async () => {
      let params = {
        XXFBID: props.XXFBID
      }
      await axiosUtil.get('/backend/xxfb/common/selectGzxfList', params).then((res) => {
        if (res.data.list.length > 0) {
          state.formData = {...res.data.list[0]}
          state.XSFS = res.data.list[0].XSFS
        }
      });

    }

    const save = async () => {
      let param = {
        XXFBID: state.formData.XXFBID,
        DJCS: parseInt(state.formData.DJCS) + 1
      }
      await axiosUtil.post('/backend/xxfb/common/saveXxfb', param).then(res => {
        console.log('保存结果', res);

      })

    }


    onMounted(() => {
      let params = props.params
      if (props.pageFlag == 'edit' || props.pageFlag == 'view') {
        getFormData()
        if (props.pageFlag == 'view') {
          getFormData().then(value => {
            save();
          });
        }
      }
      state.formData.WHSJ = comFun.getNowTime();
    })

    return {
      ...toRefs(state),
      getFormData,
      getMsg,
      closeForm,
      save,
    }
  }
})
</script>

<style scoped>


.editor-content-view p,
.editor-content-view li {
  white-space: pre-wrap; /* 保留空格 */
}

.editor-content-view blockquote {
  border-left: 8px solid #d0e5f2;
  padding: 10px 10px;
  margin: 10px 0;
  background-color: #f1f1f1;
}

.editor-content-view code {
  font-family: monospace;
  background-color: #eee;
  padding: 3px;
  border-radius: 3px;
}
.editor-content-view pre>code {
  display: block;
  padding: 10px;
}

:deep(.editor-content-view table) {
  border-collapse: collapse;
}
:deep(.editor-content-view td,
.editor-content-view th) {
  border: 1px solid #ccc;
  min-width: 50px;
  height: 20px;
}
:deep(.editor-content-view th) {
  background-color: #f1f1f1;
}

.editor-content-view ul,
.editor-content-view ol {
  padding-left: 20px;
}

.editor-content-view input[type="checkbox"] {
  margin-right: 5px;
}
</style>