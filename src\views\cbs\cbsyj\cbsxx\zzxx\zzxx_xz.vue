<template>
  <div style="height: calc(100% - 40px);">
    <el-form
      :model="state"
      ref="vForm"
      label-width="0"
      :inline="false"
      size="default"
      style="height: 100%"
      class="lui-page"
      v-loading="state.loading"
    >
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="userName">
            <el-input ref="input45296" placeholder="资质信息名称或编号" @input="getDataList"
                      v-model="state.listQuery.MCBH" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="1" class="grid-cell" style="margin-left: auto">
          <el-button type="primary" class="lui-button-add" @click="addRow"><el-icon><Plus/></el-icon>添加</el-button>
        </el-col>
        <!-- 添加批量操作按钮 -->
        <el-col :span="2" class="grid-cell" v-if="multipleSelection.length > 0">
          <el-button type="danger" @click="batchAdd"><el-icon><Plus/></el-icon>选择</el-button>
        </el-col>
      </el-row>
      <el-table
          class="lui-table"
        highlight-current-row
        size="default"
        ref="table"
        fit
        height="50vh"
        :border="false"
        :data="state.tableData"
        @selection-change="handleSelectionChange"
      >
        <!-- 添加多选框列 -->
        <el-table-column
          type="selection"
          width="55"
          :selectable="row => !row.edit"
        >
        </el-table-column>

        <el-table-column
          label="序号"
          type="index"
          width="55"
          align="center"
        />

        <el-table-column
          label="资质信息名称"
          prop="ZSMC"
          align="center"
          min-width="150"
        >
          <template #default="{ row, $index }">
            <el-form-item
              v-if="$index > -1"
              :prop="`tableData.${$index}.ZSMC`"
              :rules="[
                {
                  required: true,
                  message: '请输入资质信息名称',
                  trigger: 'blur',
                },
                {
                  max: 128,
                  message: '最多输入128个字符',
                  trigger: 'change',
                },
              ]"
            >
              <el-input
                v-if="row.edit"
                v-model="row.ZSMC"
                maxlength="128"
                placeholder="请输入"
              ></el-input>
              <div v-else>{{ row.ZSMC }}</div>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column
          label="证书编号"
          prop="ZSBH"
          align="center"
          min-width="150"
        >
          <template #default="{ row, $index }">
            <el-form-item
              v-if="$index > -1"
              :prop="`tableData.${$index}.ZSBH`"
              :rules="[
                {
                  required: true,
                  message: '请输入证书编号',
                  trigger: 'blur',
                },
                {
                  max: 64,
                  message: '最多输入64个字符',
                  trigger: 'change',
                },
              ]"
            >
              <el-input
                v-if="row.edit"
                v-model="row.ZSBH"
                maxlength="64"
                placeholder="请输入"
              ></el-input>
              <div v-else>{{ row.ZSBH }}</div>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column
          label="资质等级"
          prop="ZSDJ"
          align="center"
          width="150"
        >
          <template #default="{ row, $index }">
            <el-form-item
              v-if="$index > -1"
              :prop="`tableData.${$index}.ZSDJ`"
              :rules="[
                {
                  max: 40,
                  message: '最多输入40个字符',
                  trigger: 'change',
                },
              ]"
            >
              <el-input
                v-if="row.edit"
                v-model="row.ZSDJ"
                maxlength="40"
                placeholder="请输入"
              ></el-input>
              <div v-else>{{ row.ZSDJ }}</div>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column
          label="证书到期日期"
          prop="YXQJS"
          align="center"
          width="150"
        >
          <template #default="{ row }">
            <el-date-picker
              v-if="row.edit"
              v-model="row.YXQJS"
              type="date"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
            <div v-else>{{ formatDate(row.YXQJS) }}</div>
          </template>
        </el-table-column>

        <el-table-column
          label="附件"
          prop="fileList"
          header-align="center"
          align="left"
          show-overflow-tooltip="true"
          width="150"
        >
          <template #default="{ row, $index }">
            <vsfileupload
              :maxSize="10"
              :index="$index"
              :editable="row.edit+''==='true'"
              :busId="row.ZSZSJID"
              :key="row.ZSZSJID"
              ywlb="DWZTBGFJ"
              busType="dwxx"
              :limit="100"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="150"
          fixed="right"
          v-if="props.editable"
        >
          <template #default="{ row, $index }">
            <div v-if="row.edit">
              <el-button class="lui-table-button" @click="saveRow(row,$index)" :loading="state.saving.has('S'+$index)">
                <el-icon><Select/></el-icon>保存
              </el-button>
            </div>
            <div v-else>
              <!-- <el-button class="lui-table-button" @click="chooseData(row, $index)">选择</el-button> -->
              <el-button class="lui-table-button" @click="editRow(row, $index)">编辑</el-button>
              <el-button class="lui-table-button" @click="delRow(row, $index)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script setup>
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import { axios,auth } from "@src/assets/core/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { v4 as uuidv4 } from "uuid";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import axiosUtil from "../../../../../lib/axiosUtil";
import comFun from "../../../../../lib/comFun";
import { Search, Select, Plus} from '@element-plus/icons-vue'

const getFileList = (res) => {
  state.tableData[res.index].fileList = res.fileList;
};

// 日期转化
const formatDate = (dateStr)=> {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

const props = defineProps({
  currentRow: {
    type: Object,
    defaultData: () => {},
  },
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  },
  DWYWID:{
    type: String,
    default: ''
  },
});

const multipleSelection = ref([])
// 处理选择变化
const handleSelectionChange = (val)=> {
  multipleSelection.value = val;
}
// 批量添加
const batchAdd = ()=> {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条数据');
    return;
  }

  // 将选中的所有行数据通过事件发送给父组件
  emit('updateChooseData', multipleSelection.value);
}

const addRow = () => {
  const userinfo = auth.getPermission();
  let userParams={
    CJRZH: props.TYXYDM,
    CJRXM: userinfo.userName,
    CJDWID: userinfo.orgnaId,
    CJSJ: comFun.getNowTime(),
    SHZT: '1',
  }
  let params={
    edit: true,
    ZSZSJID: comFun.newId(),
    ZSCYZLXDM: 'CBS',
    ...userParams,
  }
  state.tableData.push(params)
}

const editRow = (row) => {
  row.edit=true
}

const DWYWID = ref(props.DWYWID)

const currentRow = ref(props.currentRow)
const saveRow = (row,index) => {
  let fields=['ZSMC','ZSBH']
  let props=[]
  fields.forEach(item=>{
    props.push(`tableData.${index}.${item}`)
  })
  vForm.value.validateField(props).then(res=>{
    state.saving.add('S'+index)
    const UUID = uuidv4().replace(/-/g, "");
    let params={
      ...row,
      DWYWID:DWYWID.value,
      ZSYWID:UUID,
      ZSCYZBS:DWYWID.value,
      XGRZH: userinfo.userLoginName,
      XGSJ: comFun.getNowTime(),
    }
    axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveZzzsXxx',{tableData: [params]}).then(r=>{
      row.edit=false
      state.saving.delete('S'+index)
      ElMessage.success('保存成功')
      emit('updateEditData',row)
    })

  }).catch(msg=>{
    console.log(msg)
  })
  const userinfo = auth.getPermission();

}

const delRow = (row, index) => {
  ElMessageBox.confirm(
      '确定删除该条数据?',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(() => {
    let params={
      ZSZSJID: row.ZSZSJID,
      SHZT: ''
    }
    axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveZzzsXxx',{tableData: [params]}).then(r=>{
      state.tableData.splice(index,1)
      ElMessage.success('删除成功')
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除',
    })
  })
}

const state = reactive({
  chooseVisible:false,
  tableData: [],
  listQuery:{},
  saving: new Set(),
  loading: false,
});

const emit=defineEmits(['updateChooseData','updateEditData'])
const chooseData = (row, index) => {
  emit('updateChooseData',row)
};

const getDataList = () => {
  const userinfo = auth.getPermission();
  let params = {
    ...state.listQuery,
    ZSCYZLXDM: 'CBS',
    CJRZH:userinfo.userLoginName,
    orgId: userinfo.orgnaId,
    TYXYDM: props.TYXYDM,
  }
  state.loading = true
  axiosUtil.get('/backend/sccbsgl/cbsyj/ytxxx/selectZzzsXxx', params).then((res) => {
    state.tableData = res.data || []
    state.loading = false
  })
}

onMounted(()=>{
  getDataList()
})


const vForm = ref(null);
const validateForm = () => {
  return vForm.value.validate();
};
defineExpose({
  validateForm,
});
</script>

<style scoped>
:deep(.el-table-fixed-column--right) {
  background-color: rgba(255, 255, 255, 1) !important;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form-item__error) {
  top: 50%;
  transform: translateY(-50%);
  left: 40px;
}

.lui-page {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.lui-search-form {
  margin-bottom: 20px;
}

.lui-button-add {
  margin-right: 10px;
}

.lui-table {
  width: 100%;
  margin-top: 20px;
}

.lui-table-button {
  padding: 5px 10px;
  margin: 0 2px;
}
</style>
