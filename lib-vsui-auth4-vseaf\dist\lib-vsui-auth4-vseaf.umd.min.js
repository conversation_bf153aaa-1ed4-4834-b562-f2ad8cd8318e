!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("jsencrypt"),require("@vsui/lib-jsext")):"function"==typeof define&&define.amd?define(["jsencrypt","@vsui/lib-jsext"],t):"object"==typeof exports?exports.vsuiauth4vseaf=t(require("jsencrypt"),require("@vsui/lib-jsext")):e.vsuiauth4vseaf=t(e.jsencrypt,e["@vsui/lib-jsext"])}(this,(function(e,t){return function(){"use strict";var o={558:function(e){e.exports=t},35:function(t){t.exports=e}},a={};function s(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}};return o[e](r,r.exports,s),r.exports}s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,{a:t}),t},s.d=function(e,t){for(var o in t)s.o(t,o)&&!s.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var r={};return function(){s.d(r,{default:function(){return Y}});const e="lib-vsui-auth4-vseaf",t="background:#0f0;color:red;";var o={prefix:e,log:function(){if(Y.getConfig().DEBUG){let o=Array.from(arguments),a=o[0],s=[`%c${e}:%c${a}`,t,""];o.splice(0,1),s=s.concat(o),console.log.apply(console,s)}}};function a(e){if(!e||!e.length)return[];e=JSON.parse(JSON.stringify(e));let t={};for(let o of e)t[o.resId]=o;for(let o=0;o<e.length;o++)e[o].resPid&&t[e[o].resPid]&&(t[e[o].resPid].children||(t[e[o].resPid].children=[]),t[e[o].resPid].children.push(e[o]),e.splice(o,1),o--);return e}var n={code:"vueauth4vseaf-1001",message:"登录失败,账号或密码错误,异常为:{0}"},i={code:"vueauth4vseaf-1002",message:"登录过程返回数据格式错误"},u={code:"vueauth4vseaf-1003",message:"未知鉴权模式,找不到对应的登录过程"},g={code:"vueauth4vseaf-1101",message:"获取用户信息过程返回数据格式错误"},c={code:"vueauth4vseaf-1103",message:"服务端未能获取到用户信息,异常为:{0}"},f={code:"vueauth4vseaf-1104",message:"向后端vseaf框架发送请求产生网络错误,异常为:{0},如果异常为404意味着vseaf的地址配置有误,请检查配置"},l={code:"vueauth4vseaf-1105",message:"转换资源树出错,异常为:{0}"},m={code:"vueauth4vseaf-1106",message:"调用自定义过程获取用户信息失败:{0}"},d={code:"vueauth4vseaf-1301",message:"服务端未能正常退出,异常为:{0}"},h={code:"vueauth4vseaf-1302",message:"退出过程返回数据格式错误"},p={code:"vueauth4vseaf-1303",message:"未知鉴权模式,找不到对应的退出过程"},v={code:"vueauth4vseaf-1401",message:"Axios检测协议中存在未登录标志"},$={code:"vueauth4vseaf-1402",message:"Axios检测协议中告知无权限访问该接口"},C={code:"vueauth4vseaf-1501",message:"获取RSA加密公钥失败,异常为:{0}"},A={code:"vueauth4vseaf-1502",message:"获取RSA加密公钥返回数据格式错误"},I={code:"vueauth4vseaf-1503",message:"获取RSA加密公钥为空值"},N={code:"vueauth4vseaf-1601",message:"获取鉴权模式过程返回数据格式错误"},P={code:"vueauth4vseaf-1602",message:"服务端未能获取到鉴权模式,异常为:{0}"},w={code:"vueauth4vseaf-1603",message:"获取鉴权模式为空值"},y={code:"vueauth4vseaf-1702",message:"检测到使用未注册的登陆模式[{0}]进行页面跳转,请检查该插件是否支持该登陆模式"};let S={};var k={initBasePath({basepath:e}){S.BasePath=e},RestFulAPI:{Interface:{Encrypt:{RSA:{getPublicKeyAddr:function(){return S.BasePath+"/secretKey"}}},AuthInfo:{authMode:{getSecureType:function(){return S.BasePath+"/secureType"}},userInfo:{getCurrentUser:function(){return S.BasePath+"/vseaf/currentUser"}},DEV:{getLoginAddr:function(){return"/apidata/auth/login"}},CAS:{getLoginAddr:function(){return S.BasePath+"/home/<USER>"},getLogoutAddr:function(){return S.BasePath+"/logout/cas?target={backurl}"}},SIAM:{getLoginAddr:function(){return S.BasePath+"/home/<USER>"},getLogoutAddr:function(){return S.BasePath+"/logout/siam"}},SECURITY:{getLoginAddr:function(){return S.BasePath+"/login"},getLogoutAddr:function(){return S.BasePath+"/logout"}},OAuth2:{},JWT:{}}}}};var E=s(35),L=s.n(E);var b={unknown:{login:function(){return new Promise(((e,t)=>{t(u)}))},logout:function(){return new Promise(((e,t)=>{t(p)}))}},CAS:{toLoginPage:function(){let e=window.location.href,t=`${location.protocol}//${location.host}`,o=(e.replace(`${t}`,""),k.RestFulAPI.Interface.AuthInfo[Y.getConfig().authMode].getLoginAddr());o=o.format({backurl:encodeURIComponent(e),prefix:encodeURIComponent(`${t+Y.getConfig().whereVSEAF}`)}),location.href=o},logout:function(){return new Promise(((e,t)=>{try{let t=`${location.protocol}//${location.host}`,o=(Y.getConfig().router,Y.getConfig().router.options.history.base),a=k.RestFulAPI.Interface.AuthInfo[Y.getConfig().authMode].getLogoutAddr();a=a.format({backurl:encodeURIComponent(t+o)}),e(),location.href=a}catch(e){t(e)}}))}},SIAM:{toLoginPage:function(){let e=window.location.href,t=`${location.protocol}//${location.host}`,o=(e.replace(`${t}`,""),k.RestFulAPI.Interface.AuthInfo[Y.getConfig().authMode].getLoginAddr());o=o.format({backurl:encodeURIComponent(e),prefix:encodeURIComponent(`${t+Y.getConfig().whereVSEAF}`)}),location.href=o},logout:function(){return new Promise(((e,t)=>{try{let t=k.RestFulAPI.Interface.AuthInfo[Y.getConfig().authMode].getLogoutAddr();e(),location.href=t}catch(e){t(e)}}))}},SECURITY:{toLoginPage:function(){Y.getConfig().router.push("/login")},login:function(e,t){return new Promise(((a,s)=>{const r=k.RestFulAPI.Interface.AuthInfo[Y.getConfig().authMode].getLoginAddr();o.log(`使用[用户名:${e}，密码：${t}]登录，restAPI地址为：${r}`),function(){const e=k.RestFulAPI.Interface.Encrypt.RSA.getPublicKeyAddr();return o.log(`获取公钥，restAPI地址为：${e}`),new Promise(((t,a)=>{U.get(e).then((e=>{if(!(e&&e.data&&e.data.meta))throw o.log(`${A.message}`),A;if(o.log(`服务器端数据返回，状态码为：${e.data.meta.status}`),!e.data.meta.success){let t=JSON.parse(JSON.stringify(C));throw t.message=t.message.format(e.data.meta.message),o.log(`${t.message}`),t}if(o.log(`获取公钥为：${e.data.data.publicKey}`),!e.data.data.publicKey||""==e.data.data.publicKey)throw I;t(e.data.data.publicKey)})).catch((e=>{a(e)}))}))}().then((u=>{let g=new(L());g.setPublicKey(u),t=g.encrypt(t),U.post(r,{secureName:e,secureSecret:t}).then((e=>{if(!(e&&e.data&&e.data.meta))throw o.log(`${i.message}`),i;if(o.log(`服务器端数据返回，状态码为：${e.data.meta.status}`),!e.data.meta.success){let t=JSON.parse(JSON.stringify(n));throw t.message=t.message.format(e.data.meta.message),o.log(`${t.message}`),t}o.log("用户名密码验证成功"),a()})).catch((e=>{s(e)}))})).catch((e=>{s(e)}))}))},logout:function(){const e=Y.getConfig(),t=k.RestFulAPI.Interface.AuthInfo[e.authMode].getLogoutAddr();return o.log(`退出restAPI地址为：${t}`),new Promise(((a,s)=>{U.post(t,{}).then((t=>{if(!(t&&t.data&&t.data.meta))throw o.log(`${h.message}`),h;if(o.log(`服务器端数据返回，状态码为：${t.data.meta.status}`),!t.data.meta.success){let e=JSON.parse(JSON.stringify(d));throw e.message=e.message.format(t.data.meta.message),o.log(`${e.message}`),e}o.log("账户退出成功"),e.store.commit(`${e.storeName}/logOut`),a()})).catch((e=>{s(e)}))}))}},DEV:{toLoginPage:function(){Y.getConfig().router.push("/login")},login:function(e,t){return o.log(`用户名:${e},密码：${t}`),new Promise(((o,s)=>{const r=k.RestFulAPI.Interface.AuthInfo.DEV.getLoginAddr(),n=Y.getConfig();U.get(r).then((s=>{if(!s||!s.data)throw new Error("没有返回信息");{let r=s.data.data,i=r.realName,u=t,g="",c=r,f=a(r.resList),l={userName:e,realName:i,passwd:u,token:g,permission:c,modulesTree:f};n.store.commit(`${n.storeName}/login`,l),o(l)}})).catch((e=>{s(new Error(`登录过程失败<br/>${e.message}`))}))}))},logout:function(){const e=Y.getConfig();return new Promise(((t,o)=>{try{e.store.commit(`${e.storeName}/logOut`),t()}catch(e){o(e)}}))}}};const M={request:function(e){return o.log("进入axios请求拦截器"),e.headers["V-Access-Ticket"]="lib-vsui-auth4-vseaf",o.log("离开axios请求拦截器"),e},requestError:function(e){throw o.log(`!!!axios请求拦截器出错,出错原因:${e.message},导致抛出异常`),e},response:function(e){if(o.log("进入axios应答拦截器"),e&&e.data&&e.data.meta)if(e.data.meta.success)o.log("axios应答拦截器获知数据获取成功");else{if(o.log("axios应答拦截器获知数据获取失败"),610==e.data.meta.status){if(o.log(`axios应答拦截器检查是因为用户未登录，导致接口无法访问，将调用登陆跳转过程，并尝试(因为后续操作可能引发其他异常)抛出异常代码:${v.code}`),!b[Y.getConfig().authMode]){let e=JSON.parse(JSON.stringify(y));throw e.message=e.message.format(Y.getConfig().authMode),e}throw b[Y.getConfig().authMode].toLoginPage(),v}if(403==e.data.meta.status)throw o.log(`axios应答拦截器检查是因为没有访问接口的权限，导致接口无法访问，将抛出异常代码:${v.code}`),$}else o.log("axios应答拦截器检查发现返回的数据格式存在问题");return o.log("离开axios应答拦截器"),e},responseError:function(e){throw o.log(`!!!axios应答拦截器出错,出错原因:${e.message},导致抛出异常`),e}};var x=M;let U,O=0,T=0;const D="/403";function R(e){const t=Y.getAuthInfo(),a=t.userName,s=t.permission;t.isLogined;let r=e.meta?.permission;if(o.log(`验证用户是否有向"${e.path}"路由跳转的权限,参数to=${e.path},userName=${a},userPermission=${s}`),void 0===r||null==r)return o.log(`"${e.path}"路由不存在meta或meta.permission，因此无需鉴权`),!0;if("function"==typeof r){o.log(`"${e.path}"路由为自定义函数鉴权，验证开始`);let t=!1;try{t=r(e,a,s)}catch(e){o.log(`尝试调用路由鉴权函数失败,原因是${e.message}`)}return t}if(r instanceof Array){o.log(`"${e.path}"路由为函数数组(严格模式)鉴权，验证开始`);let t=!1;if(!a||""==a)return o.log("用户名不存在"),t;0==r.length&&o.log("用户权限列表为空");for(let a of r.values()){for(let r of s.resList.values())if(a==r.resId&&r.resPath){let a=Y.getConfig().router.match(r.resPath);if(e.path==a.path){t=!0,o.log("找到用户拥有的资源编号对应的访问地址与路由的匹配");break}}if(t)break}return t}if("boolean"==typeof r&&!0===r){o.log(`"${e.path}"路由为布尔模式(宽松模式)鉴权，验证开始`);let t=!1;if(!a||""==a)return t;for(let a of s.resList.values())if(a.resPath){let s=Y.getConfig().router.match(a.resPath);if(e.path==s.path){t=!0,o.log("找到用户拥有访问地址与路由的匹配");break}}return t}return o.log(`"${e.path}"路由定义了未被支持鉴权方式`),!0}var J=s(558);const F={unknown:{auth:function(){o.log("未知登陆模式无法使用服务器端验证")}},CAS:{auth:V},SIAM:{auth:V},JWT:{auth:V},SECURITY:{auth:V},Other:{auth:V},DEV:{auth:function(e,t,a){const s=Y.getConfig();let r=s.store.getters[`${s.storeName}/getLoginUserName`],n="";if(""!=r)B(e,t,a);else{if(r=J.Cookie.Cookie.getCookie(J.Cookie.CookieDef.userName,J.Cookie.CookieDef.domain,J.Cookie.CookieDef.path),n=J.Cookie.Cookie.getCookie(J.Cookie.CookieDef.passwd,J.Cookie.CookieDef.domain,J.Cookie.CookieDef.path),""==r||""==n)return void Y.getAuthInfo().auth.toLoginPage();b[s.authMode].login(r,n).then((o=>{B(e,t,a)})).catch((e=>{o.log(`[vsdev模式]${e.message}`)}))}}}};function V(e,t,s){const r=Y.getAuthInfo(),n=Y.getConfig();let i=r.userName;i&&""!=i?(o.log("前端已存在用户登陆信息,无权限直接403"),s(D)):(o.log("前端不存在用户登陆信息,尝试从后端VSEAF框架获取用户信息"),function(){const e=Y.getConfig();return new Promise(((t,s)=>{let r=e[e.customCfg]?.getUserInfo;if(o.log("准备向后端VSEAF发送获取用户信息的请求"),r&&"function"==typeof r)o.log(`发现用户自定义获取用户信息过程，该过程为Promise对象，您应在resolve回调函数中传递object类型的参数，对象为：${String.raw`
            {
                userName:"登录账号,即用户名,不可留空",
                realName:"用户真实名称，如：张三"
                passwd:"用户密码,为安全考虑,可留空",
                token:"无状态模式下的凭据信息,可留空",
                permission:保存后端返回的完整用户信息,不可留空，数据结构请参考vsnpm文档,
                modulesTree:用户菜单资源数组,请转换后按照固定结构传入,不可留空,数据结构请参考vsnpm文档
            }
            `}，您如果后端使用胜软VSEAF框架，当用户未登录状态下获取用户信息时，会返回带有610状态码的JSON数据，会自动触发跳转登陆页面的过程，\n            如果不是VSEAF框架，如果您想自动触发未登录情况下的自动跳转，您需要模拟出带610的json数据，格式：\n                {\n                    "meta": \n                    {   \n                        "message": "No authentication information .",\n                        "success": false,\n                        "status": 610\n                    }\n                }。\n            使用该过程！`),r().then((a=>{let{userName:s,realName:r,passwd:n,token:i,permission:u,modulesTree:g}=a,c={userName:s,realName:r,passwd:n,token:i,permission:u,modulesTree:g};e.store.commit(`${e.storeName}/login`,c),o.log("将用户信息写入VUEX，可用vueDevTools查看VUEX中前缀为vsui-auth4-的状态值"),t(c)})).catch((e=>{let t=JSON.parse(JSON.stringify(m));t.message=t.message.format(e.message),o.log(`${t.message}`),s(t)}));else{let r=k.RestFulAPI.Interface.AuthInfo.userInfo.getCurrentUser();o.log(`使用内置获取用户信息的方法，请求地址为：${r}`),U.get(r).then((r=>{if(r=r.data,o.log("发送获取用户信息的请求"),r&&r.meta)if(r.meta.success){let n=r.data,i=r.data.userName,u=r.data.realName,g=r.data.passwd,c=r.data.token,f=r.data.resList,m=[];o.log(`获取用户信息成功，返回数据为：${JSON.stringify(n)}`);try{f&&0!=f.length?(o.log("转换资源树开始"),m=a(n.resList||m),o.log("转换资源树完成")):o.log("发现资源树数据为空或资源菜单个数为0,这可能是用户未分配资源权限导致")}catch(e){let t=JSON.parse(JSON.stringify(l));t.message=t.message.format(e.message),o.log(`${t.message}`),s(t)}let d={userName:i,realName:u,passwd:g,token:c,permission:n,modulesTree:m};e.store.commit(`${e.storeName}/login`,d),o.log("将用户信息写入VUEX，可用vueDevTools查看VUEX中前缀为vsui-auth4-的状态值"),t(d)}else{let e=JSON.parse(JSON.stringify(c));e.message=e.message.format(r.meta.message),o.log(`${e.message}`),s(e)}else o.log(`${g.message}`),s(g)})).catch((e=>{let t=JSON.parse(JSON.stringify(f));t.message=t.message.format(e.message),e.code==v.code&&s(e),e.code==y.code?s(e):s(t)}))}}))}().then((({name:a,passwd:r,token:n,permission:i,modulesTree:u})=>{o.log("从后端VSEAF框架获取用户信息正常完成,已获取用户信息与用户权限"),B(e,t,s)})).catch((e=>{o.log(`获取用户信息的请求被异常中断,错误编号:${e.code},错误信息:${e.message}`),e.code==c?(o.log("根据错误编号判定为用户未登录,跳转入登陆界面"),b[n.authMode].toLoginPage()):e.code==v.code?o.log(`截获异常代码：${v.code},表示AXIOS拦截器已经处理了返回数据验证为未登录情况下的登陆跳转,此处未登陆异常不被处理`):e.code==m.code?o.log(`截获异常代码：${m.code},表示自定义获取用户过程出现了错误，您如果能看到此信息，说明用户未登录，且您没有在请求的返回数据中带上610标记，因此该未登陆异常无法被处理`):o.log("根据错误编号判定为请求过程网络或服务器端存在问题,请排查此情况")})))}function B(e,t,a){R(e)?(o.log(`再次鉴权验证结果[有权访问],%c跳至${e.path}页面%c`,"color:#0f0;"),a()):(o.log("再次鉴权验证结果[无权访问],%c跳至403页面","color:#F02D2D;"),a(D))}var j={beforeEach:function(e,t,a){o.log(`%c!!!进入权限组件路由拦截器["${e.path}"]%c，正常鉴权后会输出 %c!!!离开权限组件路由拦截器["${e.path}"]%c ，如未输出此文字，可能有其他拦截器二次处理`,"color:#0f0;","","color:#0f0;","");try{!function(e,t,a){o.log("初次鉴权验证开始");let s=R(e);if(o.log("初次鉴权验证结果"+(s?"[允许访问]":"[无权访问]")),s)o.log(`跳入${e.path}页面开始[authInterceptor]`),a(),o.log(`跳入${e.path}页面,如本过程完毕未跳入正常页面，请检查是否有其他拦截器存在[authInterceptor]`);else{o.log("初次鉴权无权限可能是没有用户数据造成，将尝试使用鉴权模式对应的服务端鉴权再次鉴权！");let s="unknown";if(Y.getConfig().authMode==s){o.log("服务器端未告知鉴权模式，组件正在等待服务器返回鉴权模式"),o.log("无权限访问情况下，尝试使用服务器端鉴权模式进行再次验证"),o.log(`当前鉴权模式为${Y.getConfig().authMode},等待服务器端返回的正确模式`);let r=0,n=window.setInterval((()=>{r+=5,Y.getConfig().authMode!=s&&(o.log(`经过${r}毫秒后，获取到服务端返回的鉴权模式为：${Y.getConfig().authMode}`),clearInterval(n),F[Y.getConfig().authMode]?(o.log(`存在对${Y.getConfig().authMode}鉴权模式指定的鉴权方式，将使用此类型指定的服务端鉴权方式`),F[Y.getConfig().authMode].auth(e,t,a)):(o.log(`不存在对${Y.getConfig().authMode}鉴权模式指定的鉴权方式，将使用内置通用的服务端鉴权方式`),F.Other.auth(e,t,a)))}),5)}else o.log("服务器端已告知鉴权模式，组件将验证该鉴权模式的服务端鉴权方式是否存在"),F[Y.getConfig().authMode]?(o.log(`存在对${Y.getConfig().authMode}鉴权模式指定的服务端鉴权方式，将使用此类型指定的服务端鉴权方式`),F[Y.getConfig().authMode].auth(e,t,a)):(o.log(`不存在对${Y.getConfig().authMode}鉴权模式指定的服务端鉴权方式，将使用内置通用的服务端鉴权方式`),F.Other.auth(e,t,a))}}(e,t,a)}catch(e){o.log(`权限组件路由拦截器异常,错误信息为${e.message},跳转入登陆页面`)}},afterEach:function(e){o.log(`%c!!!离开权限组件路由拦截器["${e.path}"]`,"color:#0f0;")}};let q;var K={strict:!1,namespaced:!0,state:{isLogined:!1,userInfo:{userName:"",realName:"",passwd:"",token:"",permission:{},modulesTree:[]}},actions:{},mutations:{login:function(e,t){e.isLogined=!0,e.userInfo=t},logOut:function(e){e.isLogined=!1,e.userInfo={userName:"",realName:"",passwd:"",token:"",permission:{},modulesTree:[]}}},getters:{getLoginUserName:(e,t,o,a)=>e.userInfo.userName,getLoginUserRealName:(e,t,o,a)=>e.userInfo.realName,getUserInfoPermission:e=>e.userInfo.permission,getLoginUserToken:e=>e.userInfo.token,getModulesTree:e=>e.userInfo.modulesTree}};let G;let X={};var Y={init:async function(e){const{router:t,store:a,axios:s,whereVSEAF:r,isDev:n,DEBUG:i,customCfg:u}=e,g=e[u]||void 0;try{return X={router:t,store:a,axios:s,whereVSEAF:r,DEBUG:i,isDev:n},X.customCfg=u,X.customCfg&&(X[X.customCfg]=g),X.DEBUG=i||!1,X.DEBUG&&o.log(`控制台前缀为：${o.prefix}，组件支持SECURITY、CAS、SIAM、DEV四种登陆模式，如登陆模式为其他，请访问VSNPM并查询关键字：@vsui/lib-vsui-auth4-vseaf-plugins`),X.isDev=n||!1,X.authMode=X.isDev?"DEV":"unknown",k.initBasePath({basepath:X.whereVSEAF,userInfoPath:X[X.customCfg]?.userInfo?.apiPath}),function(e){q=e,q.beforeEach(j.beforeEach),q.afterEach(j.afterEach),o.log("路由拦截器注册完毕")}(t),function(e){U=e,O=U.interceptors.request.use(x.request,x.requestError),T=U.interceptors.response.use(x.response,x.responseError),o.log(`axios拦截器注册完毕，请求拦截器序号为：${O}，应答拦截器序号为：${T}`)}(s),X.isDev||function(){const e=k.RestFulAPI.Interface.AuthInfo.authMode.getSecureType();return new Promise(((t,a)=>{U.get(e).then((e=>{if(!(e&&e.data&&e.data.meta))throw o.log(`${N.message}`),N;if(!e.data.meta.success){let t=JSON.parse(JSON.stringify(P));throw t.message=t.message.format(e.data.meta.message),o.log(`${t.message}`),t}if(o.log(`获取登陆模式为：${e.data.data.secureType}`),!e.data.data.secureType||""==e.data.data.secureType)throw w;t(e.data.data.secureType)})).catch((e=>{a(e)}))}))}().then((e=>{X.authMode=e.toUpperCase()})).catch((e=>{X.DEBUG&&o.log(`初始化过程中，获取登陆模式有误，异常为：${e.message}}`)})),X.storeName=function(e){G=e;let t="vsui-auth4-";return t+=(new Date).getTime(),e.registerModule(t,K),t}(a),X.DEBUG&&o.log("初始化鉴权模块完毕！"),!0}catch(e){throw"初始化鉴权模块出错："+e.message}},getConfig:()=>X,getAuthInfo:()=>({isLogined:""!=X.store.getters[`${X.storeName}/getLoginUserName`]&&null!=X.store.getters[`${X.storeName}/getLoginUserName`],userName:X.store.getters[`${X.storeName}/getLoginUserName`],realName:X.store.getters[`${X.storeName}/getLoginUserRealName`],permission:X.store.getters[`${X.storeName}/getUserInfoPermission`],modulesTree:X.store.getters[`${X.storeName}/getModulesTree`],token:X.store.getters[`${X.storeName}/getLoginUserToken`],auth:b[X.authMode],authMode:X.authMode,storeName:X.storeName}),getAuthDefine:()=>b}}(),r=r.default}()}));
//# sourceMappingURL=lib-vsui-auth4-vseaf.umd.min.js.map