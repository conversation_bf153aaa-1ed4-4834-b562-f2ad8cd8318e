<template>
  <el-form ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" v-show="true" prop="SSDWMC">
          <el-input style="width:100%;" placeholder="请输入企业名称" v-model="listQuery.DWMC" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>

      <!-- <el-col :span="6" class="grid-cell">
        <el-form-item label="" v-show="true" prop="XMMC">
          <el-input style="width:100%;" placeholder="请输入采购项目名称" v-model="listQuery.XMMC" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col> -->

      <!--        <el-col :span="6" class="grid-cell">-->
      <!--          <el-form-item label="有无标底：" v-show="true" prop="YWBD">-->
      <!--            <el-select ref="select14540" v-model="listQuery.YWBD" class="full-width-input" clearable>-->
      <!--              <el-option v-for="(item, index) in [{label:'有',value:'1'},{label:'无',value:'0'}]" :key="index"-->
      <!--                         :label="item.label"-->
      <!--                         :value="item.value" :disabled="item.disabled"></el-option>-->
      <!--            </el-select>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->

      <el-col :span="4" class="grid-cell">
        <div class="static-content-item" v-show="true" style="display: flex;padding-left: 10px;text-align: right;">
          <el-button ref="button91277" @click="getDataList" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
          <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData">
            <el-icon>
              <Plus/>
            </el-icon>
            新增
          </el-button>
        </div>
      </el-col>

    </el-row>

    <el-row ref="grid71868" :gutter="12" v-show="true">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper" v-show="true">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
            <el-table-column v-if="true" prop="YWLXDMMC" label="业务类型" :fixed="false" align="center" header-align="center"
                             :show-overflow-tooltip="true" width="100"></el-table-column>
            <el-table-column v-if="true" prop="YWBT" label="业务标题" :fixed="false" align="left"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column v-if="true" prop="CJRXM" label="办理人" :fixed="false" align="left" header-align="center"
                             :show-overflow-tooltip="true" width="150"></el-table-column>
            <el-table-column v-if="true" prop="CJSJ" label="办理时间" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200"></el-table-column>
            <el-table-column v-if="true" prop="CZ" label="操作" align="center" width="150">
              <template #default="scope">
                <div v-if="scope.row.SHZT==='0'">
                  <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑</el-button>
                  <el-button size="small" class="lui-table-button" type="primary" @click="delRow(scope.row)">删除</el-button>
                </div>
                <div v-else>
                  <el-button size="small" class="lui-table-button" type="primary" @click="viewData(scope.row)">查看</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
  onUnmounted
}
  from 'vue'
import TabFun from "@src/lib/tabFun";
// 请求方法 get,post,put,del,downloadFile
import vsAuth from "@src/lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "@src/lib/comFun";
import axiosUtil from "../../../lib/axiosUtil";
import AuditFrame from "../../workflow/AuditFrame";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import zxzy from "./xzzy/index";
import {mixin} from "@src/assets/core/index";
const {vsuiRoute, vsuiEventbus} = mixin();
export default defineComponent({
  name: '',
  components: {AuditFrame,Search,Plus,Upload},
  props: {},
  setup() {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        page: 1,
        size: 10,
        DWMC:'',
        LY:'CBSBL',//承包商补录
      },
      tableData: [],
      total: 0,
      rules: {},
      dialogVisible: false,
      params: {},
      processParams: {
        activityId: "new",
        processId: "ZTB_ZBSQLC",
        Processversion: "1"
      }
    })


    const getDataList = () => {
      let params={
        ...state.listQuery,
        orgnaId: state.userInfo.orgnaId,
      }
      axiosUtil.get('/backend/sccbsgl/cbsyj/getYwxxCg', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    // 编辑
    const editRow = (row) => {
      let editable = false;
      if (row.SHZT == '0') {
        editable = true;
      }
      if (row.DWLX === 'DW') {
        TabFun.addTabByRoutePath(`队伍信息${row.YWLXDMMC}`, '/contractors/yrsqxxIndex', {
          DWYWID: row.DWYWID,
          YWLXDM: 'CBSBL',
          backPath: '/contractors/zrfqIndex',
          JGDWYWID: row.JGDWYWID,
          editable: editable,
          from:'CBSBL'
        },);
        return false;
      }
      const {DWYWID: uuId, MBID, MBLX, ZYFLDM, YWLXDM, EXTENSION} = row;
      TabFun.addTabByRoutePath(
          `承包商信息${row.YWLXDMMC}`,
          "/contractors/cbsjbxxIndex",
          {
            uuId,
            MBID,
            MBLX,
            ZYFLDM,
            YWLXDM: YWLXDM ?? 'ZR',
            JGDWYWID: row.JGDWYWID,
            EXTENSION,
            editable: editable,
            from:'CBSBL'
          },
          {}
      );
      vsuiEventbus.emit("reloadCbsjbxx", {
        uuId,
        MBID,
        MBLX,
        ZYFLDM,
        YWLXDM: YWLXDM ?? 'ZR',
        JGDWYWID: row.JGDWYWID,
        EXTENSION,
        editable: editable
      });

    }
    //删除
    const delRow = (row) => {
      ElMessageBox.confirm("确定删除此数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        axiosUtil.del('/backend/sccbsgl/cbsyj/deleteCbsyj', {params: {DWYWID:row.DWYWID,}}).then((result) => {
                  ElMessage.success("删除成功");
                  getDataList();
                }).catch((err) => {
                  ElMessage.error("删除失败");
                });
          })
          .catch(() => {
            // catch error
          });
    }

    // 新增
    const addData = () => {
      TabFun.addTabByCustomName("专业选择", "zxzy", zxzy, {LY: 'CBSBL',ZRLX: 'CBSBL'}, {});
    }
    //查看
    const viewData = (row) => {
      editRow(row)
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }


    onMounted(() => {
      getDataList();
      vsuiEventbus.on("reloadCbsblList", getDataList);
    })

    onUnmounted(() => {
      vsuiEventbus.off("reloadCbsblList", getDataList);
    });

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      editRow,
      delRow,
      closeForm,
      addData,
      viewData
    }
  }
})

</script>

<style scoped>
.container {
  padding: 8px 5px;
}
</style>
