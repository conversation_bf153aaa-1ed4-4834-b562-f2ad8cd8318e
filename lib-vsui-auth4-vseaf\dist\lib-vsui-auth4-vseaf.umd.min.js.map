{"version": 3, "file": "lib-vsui-auth4-vseaf.umd.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,EAAQG,QAAQ,aAAcA,QAAQ,oBAC9B,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,YAAa,mBAAoBJ,GACf,iBAAZC,QACdA,QAAwB,eAAID,EAAQG,QAAQ,aAAcA,QAAQ,oBAElEJ,EAAqB,eAAIC,EAAQD,EAAgB,UAAGA,EAAK,oBAR3D,CASGO,MAAM,SAASC,EAAiCC,GACnD,O,+CCVAN,EAAOD,QAAUO,G,eCAjBN,EAAOD,QAAUM,ICCbE,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaX,QAGrB,IAAIC,EAASO,EAAyBE,GAAY,CAGjDV,QAAS,IAOV,OAHAa,EAAoBH,GAAUT,EAAQA,EAAOD,QAASS,GAG/CR,EAAOD,QCpBfS,EAAoBK,EAAI,SAASb,GAChC,IAAIc,EAASd,GAAUA,EAAOe,WAC7B,WAAa,OAAOf,EAAgB,SACpC,WAAa,OAAOA,GAErB,OADAQ,EAAoBQ,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRN,EAAoBQ,EAAI,SAASjB,EAASmB,GACzC,IAAI,IAAIC,KAAOD,EACXV,EAAoBY,EAAEF,EAAYC,KAASX,EAAoBY,EAAErB,EAASoB,IAC5EE,OAAOC,eAAevB,EAASoB,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3EX,EAAoBY,EAAI,SAASK,EAAKC,GAAQ,OAAOL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,iECM/F,MAAMI,EAAO,uBAKPC,EAAa,6BA0BnB,OACID,OAAAA,EACAE,IAjBJ,WACI,GAAG,cAAmBC,MAAM,CACxB,IAAIC,EAAOC,MAAMC,KAAKC,WAAYC,EAAIJ,EAAK,GAGvCK,EAAQ,CADG,KAAKT,OAAYQ,IACRP,EAXhB,IAYRG,EAAKM,OAAO,EAAE,GACdD,EAAQA,EAAQE,OAAOP,GAEvBQ,QAAQV,IAAIW,MAAMD,QAAQH,MCiClC,SAASK,EAAwBC,GAChC,IAAKA,IAASA,EAAKC,OAClB,MAAO,GAEND,EAAKE,KAAKC,MAAMD,KAAKE,UAAUJ,IAC/B,IAAIK,EAAc,GAClB,IAAK,IAAIC,KAAQN,EAClBK,EAAYC,EAAKC,OAASD,EAEzB,IAAK,IAAIE,EAAI,EAAGA,EAAIR,EAAKC,OAAQO,IAC9BR,EAAKQ,GAAGC,QAAUJ,EAAYL,EAAKQ,GAAGC,UACnCJ,EAAYL,EAAKQ,GAAGC,QAAQC,WAClCL,EAAYL,EAAKQ,GAAGC,QAAQC,SAAW,IAEtCL,EAAYL,EAAKQ,GAAGC,QAAQC,SAASC,KAAKX,EAAKQ,IAC/CR,EAAKL,OAAOa,EAAG,GACfA,KAGD,OAAOR,EClFV,MAC2B,CAACY,KAAK,qBAAqBC,QAAQ,wBAD9D,EAEwB,CAACD,KAAK,qBAAqBC,QAAQ,gBAF3D,EAG0B,CAACD,KAAK,qBAAqBC,QAAQ,qBAH7D,EAI8B,CAACD,KAAK,qBAAqBC,QAAQ,oBAJjE,EAMmC,CAACD,KAAK,qBAAqBC,QAAQ,wBANtE,EAO2B,CAACD,KAAK,qBAAqBC,QAAQ,8DAP9D,EAQ0C,CAACD,KAAK,qBAAqBC,QAAQ,mBAR7E,EAS6B,CAACD,KAAK,qBAAqBC,QAAQ,uBAThE,EAU8B,CAACD,KAAK,qBAAqBC,QAAQ,qBAVjE,EAWyB,CAACD,KAAK,qBAAqBC,QAAQ,gBAX5D,EAY2B,CAACD,KAAK,qBAAqBC,QAAQ,qBAZ9D,EAauB,CAACD,KAAK,qBAAqBC,QAAQ,qBAb1D,EAcwB,CAACD,KAAK,qBAAqBC,QAAQ,wBAd3D,EAe4C,CAACD,KAAK,qBAAqBC,QAAQ,uBAf/E,EAgBuC,CAACD,KAAK,qBAAqBC,QAAQ,qBAhB1E,EAiBoC,CAACD,KAAK,qBAAqBC,QAAQ,gBAjBvE,EAkB8B,CAACD,KAAK,qBAAqBC,QAAQ,oBAlBjE,EAmBmC,CAACD,KAAK,qBAAqBC,QAAQ,wBAnBtE,EAoB4B,CAACD,KAAK,qBAAqBC,QAAQ,aApB/D,EAsB6C,CAACD,KAAK,qBAAqBC,QAAQ,4CCrBhF,IAAIC,EAAO,GAEX,OACIC,cAAa,SAACC,IACVF,EAAOG,SAASD,GAEpBE,WAAW,CACPC,UAAU,CAENC,QAAQ,CACJC,IAAI,CACAC,iBAAiB,WAAW,OAAOR,EAAOG,SAAS,gBAG3DM,SAAS,CACLC,SAAS,CACLC,cAAc,WAAW,OAAOX,EAAOG,SAAS,gBAEpDS,SAAS,CAKLC,eAAe,WAAW,OAAOb,EAAOG,SAAS,uBAErDW,IAAI,CACAC,aAAa,WAAW,MAAO,wBAEnCC,IAAI,CACAD,aAAa,WAAW,OAAOf,EAAOG,SAAS,gDAC/Cc,cAAc,WAAW,OAAOjB,EAAOG,SAAS,iCAEpDe,KAAK,CACDH,aAAa,WAAW,OAAOf,EAAOG,SAAS,gDAC/Cc,cAAc,WAAW,OAAOjB,EAAOG,SAAS,iBAEpDgB,SAAS,CACLJ,aAAa,WAAW,OAAOf,EAAOG,SAAS,UAC/Cc,cAAc,WAAW,OAAOjB,EAAOG,SAAS,YAEpDiB,OAAO,GAGPC,IAAI,O,qBCHpB,MAlCe,CACXC,QAAQ,CACJC,MAAM,WACF,OAAO,IAAIC,SAAQ,CAACC,EAAQC,KACxBA,EAAO,OAGfC,OAAO,WACH,OAAO,IAAIH,SAAQ,CAACC,EAAQC,KACxBA,EAAO,QAInBV,IAAI,CACAY,YCdR,WACI,IAAIC,EAAMC,OAAOC,SAASC,KACtBC,EAAQ,GAAGF,SAASG,aAAaH,SAASI,OAE1CC,GADKP,EAAIQ,QAAQ,GAAGJ,IAAU,IACnB,gCAAsC,cAAmBvB,UAAUK,gBAClFqB,EAAWA,EAASE,OAAO,CACvB,QAAUC,mBAAmBV,GAC7B,OAASU,mBAAmB,GAAGN,EAAQ,cAAmBO,gBAE9DT,SAASC,KAAKI,GDMVT,OCER,WACI,OAAO,IAAIH,SAAQ,CAACC,EAAQC,KACxB,IAEI,IAAIO,EAAQ,GAAGF,SAASG,aAAaH,SAASI,OAE1CjC,GADG,cAAmBuC,OACb,cAAmBA,OAAOC,QAAQC,QAAQC,MACnDC,EAAY,gCAAsC,cAAmBnC,UAAUO,gBACnF4B,EAAYA,EAAUP,OAAO,CAAC,QAAUC,mBAAmBN,EAAQ/B,KACnEuB,IACAM,SAASC,KAAKa,EAElB,MAAMC,GAEFpB,EAAOoB,SDdf5B,KAAK,CACDU,YEnBR,WACI,IAAIC,EAAMC,OAAOC,SAASC,KACtBC,EAAQ,GAAGF,SAASG,aAAaH,SAASI,OAE1CC,GADKP,EAAIQ,QAAQ,GAAGJ,IAAU,IACnB,gCAAsC,cAAmBvB,UAAUK,gBAClFqB,EAAWA,EAASE,OAAO,CACvB,QAAUC,mBAAmBV,GAC7B,OAASU,mBAAmB,GAAGN,EAAQ,cAAmBO,gBAE9DT,SAASC,KAAKI,GFWVT,OEJR,WACI,OAAO,IAAIH,SAAQ,CAACC,EAAQC,KACxB,IAEI,IAAImB,EAAY,gCAAsC,cAAmBnC,UAAUO,gBACnFQ,IACAM,SAASC,KAAKa,EAElB,MAAMC,GAEFpB,EAAOoB,SFHf3B,SAAS,CACLS,YGjBR,WACI,cAAmBa,OAAO5C,KAAK,WHiB3B0B,MGLR,SAAewB,EAAUC,GACtB,OAAO,IAAIxB,SAAQ,CAACC,EAAQC,KACvB,MAAMU,EAAW,gCAAmC,cAAmB1B,UAAUK,eACjFkC,EAAO5E,IAAI,UAAU0E,QAAeC,mBAA0BZ,KCkGrE,WACG,MAAMc,EAAe,sDAErB,OADAD,EAAO5E,IAAI,mBAAmB6E,KACvB,IAAI1B,SAAQ,CAACC,EAAQC,KACXyB,EAAMtF,IAAIqF,GAAcE,MAAKC,IACtC,KAAIA,GAAQA,EAAKC,MAAQD,EAAKC,KAAKC,MAqB/B,MADAN,EAAO5E,IAAI,GAAG,aACR,EAnBN,GADA4E,EAAO5E,IAAI,iBAAiBgF,EAAKC,KAAKC,KAAKC,WACxCH,EAAKC,KAAKC,KAAKE,QAUlB,CACI,IAAIC,EAAwCtE,KAAKC,MAAMD,KAAKE,UAAU,IAGtE,MAFAoE,EAAwC3D,QAAQ2D,EAAwC3D,QAAQuC,OAAOe,EAAKC,KAAKC,KAAKxD,SACtHkD,EAAO5E,IAAI,GAAGqF,EAAwC3D,WAChD2D,EAXN,GADAT,EAAO5E,IAAI,SAASgF,EAAKC,KAAKA,KAAKK,cAC/BN,EAAKC,KAAKA,KAAKK,WAAsC,IAA1BN,EAAKC,KAAKA,KAAKK,UAE1C,MAAM,EAEVlC,EAAQ4B,EAAKC,KAAKA,KAAKK,cAchCC,OAAMd,IACLpB,EAAOoB,SD9HXe,GAAkBT,MAAKU,IAEnB,IAAIC,EAAU,IAAI,KAClBA,EAAQC,aAAaF,GACrBd,EAASe,EAAQA,QAAQf,GACzBG,EAAMc,KAAK7B,EAAS,CAAC,WAAaW,EAAS,aAAeC,IAAWI,MAAKC,IACtE,KAAIA,GAAQA,EAAKC,MAAQD,EAAKC,KAAKC,MAkB/B,MADAN,EAAO5E,IAAI,GAAG,aACR,EAfN,GADA4E,EAAO5E,IAAI,iBAAiBgF,EAAKC,KAAKC,KAAKC,WACxCH,EAAKC,KAAKC,KAAKE,QAMlB,CACI,IAAIS,EAAuB9E,KAAKC,MAAMD,KAAKE,UAAU,IAGrD,MAFA4E,EAAuBnE,QAAQmE,EAAuBnE,QAAQuC,OAAOe,EAAKC,KAAKC,KAAKxD,SACpFkD,EAAO5E,IAAI,GAAG6F,EAAuBnE,WAC/BmE,EARNjB,EAAO5E,IAAI,aACXoD,OAcTmC,OAAMd,IACLpB,EAAOoB,SAEZc,OAAMd,IACLpB,EAAOoB,UH5BXnB,OGwCP,WACG,MAAMwC,EAAW,cACXtB,EAAY,gCAAmCsB,EAAWzD,UAAUO,gBA6B1E,OA5BAgC,EAAO5E,IAAI,gBAAgBwE,KAChB,IAAIrB,SAAQ,CAACC,EAAQC,KAC5ByB,EAAMc,KAAKpB,EAAU,IAAIO,MAAKC,IAC1B,KAAIA,GAAQA,EAAKC,MAAQD,EAAKC,KAAKC,MAmB/B,MADAN,EAAO5E,IAAI,GAAG,aACR,EAhBN,GADA4E,EAAO5E,IAAI,iBAAiBgF,EAAKC,KAAKC,KAAKC,WACxCH,EAAKC,KAAKC,KAAKE,QAOlB,CACI,IAAIW,EAA0BhF,KAAKC,MAAMD,KAAKE,UAAU,IAGxD,MAFA8E,EAA0BrE,QAAQqE,EAA0BrE,QAAQuC,OAAOe,EAAKC,KAAKC,KAAKxD,SAC1FkD,EAAO5E,IAAI,GAAG+F,EAA0BrE,WAClCqE,EATNnB,EAAO5E,IAAI,UACX8F,EAAWE,MAAMC,OAAO,GAAGH,EAAWI,oBACtC9C,OAcTmC,OAAMd,IACLpB,EAAOoB,WHlEfhC,IAAI,CACAc,YKhBR,WACI,cAAmBa,OAAO5C,KAAK,WLgB3B0B,MKPR,SAAewB,EAAUC,GAGrB,OADAC,EAAO5E,IAAI,OAAO0E,QAAeC,KAC1B,IAAIxB,SAAQ,CAACC,EAAQC,KACxB,MAAMU,EAAW,mDACX+B,EAAW,cACjBhB,EAAMtF,IAAIuE,GAAUgB,MAAKC,IACrB,IAAGA,IAAMA,EAAKC,KAUP,MAAM,IAAIkB,MAAM,UAVH,CAChB,IAAIlB,EAAOD,EAAKC,KAAKA,KAGjBmB,EAAWnB,EAAKmB,SAAUC,EAAS1B,EAAU2B,EAAQ,GAAIC,EAAatB,EACtEuB,EAAc,EAAoBvB,EAAKwB,SACvCC,EAAY,CAAEhC,SAAAA,EAAU0B,SAAAA,EAAUC,OAAAA,EAAQC,MAAAA,EAAOC,WAAAA,EAAYC,YAAAA,GACjEV,EAAWE,MAAMC,OAAO,GAAGH,EAAWI,kBAAmBQ,GACzDtD,EAAQsD,OAGbnB,OAAMoB,IACLtD,EAAO,IAAI8C,MAAO,cAAcQ,EAAMjF,mBLX1C4B,OKoBR,WACI,MAAMwC,EAAW,cACjB,OAAO,IAAI3C,SAAQ,CAACC,EAAQC,KACxB,IACIyC,EAAWE,MAAMC,OAAO,GAAGH,EAAWI,oBACtC9C,IAEJ,MAAMqB,GACFpB,EAAOoB,UCrDnB,MAGMmC,EAAiB,CAEnBC,QAAQ,SAASlF,GAIb,OAHAiD,EAAO5E,IAAI,gBACX2B,EAAOmF,QAPW,mBACE,uBAOpBlC,EAAO5E,IAAI,gBACJ2B,GAEXoF,aAAa,SAASJ,GAGlB,MADA/B,EAAO5E,IAAI,wBAAwB2G,EAAMjF,kBACnCiF,GAEVK,SAAS,SAASA,GAId,GAHApC,EAAO5E,IAAI,gBAGRgH,GAAUA,EAAS/B,MAAM+B,EAAS/B,KAAKC,KAEtC,GAAG8B,EAAS/B,KAAKC,KAAKE,QAClBR,EAAO5E,IAAI,0BAEX,CAEA,GADA4E,EAAO5E,IAAI,sBACmB,KAA3BgH,EAAS/B,KAAKC,KAAKC,OACtB,CAEI,GADAP,EAAO5E,IAAI,qEAAqE,WAC7EiH,EAAS,cAAmB5E,UAI3B,CACA,IAAI6E,EAAyCnG,KAAKC,MAAMD,KAAKE,UAAU,IAEvE,MADAiG,EAAyCxF,QAAQwF,EAAyCxF,QAAQuC,OAAO,cAAmB5B,UACtH6E,EAGV,MARID,EAAS,cAAmB5E,UAAUkB,cAQpC,EAEL,GAA8B,KAA3ByD,EAAS/B,KAAKC,KAAKC,OAGvB,MADAP,EAAO5E,IAAI,6CAA6C,UAClD,OAMd4E,EAAO5E,IAAI,6BAGf,OADA4E,EAAO5E,IAAI,gBACJgH,GAGXG,cAAc,SAASR,GAGnB,MADA/B,EAAO5E,IAAI,wBAAwB2G,EAAMjF,kBACnCiF,IAKd,QCnEA,IAAI7B,EAAMsC,EAAa,EAAEC,EAAc,EC0BtC,MAAMC,EAAK,OAaZ,SAASC,EAAqBC,GAC1B,MAAMC,EAAW,gBACX/C,EAAW+C,EAAS/C,SACpBgD,EAAiBD,EAASlB,WACdkB,EAASE,UAC3B,IAAIpB,EAAWiB,EAAGtC,MAAMqB,WAMxB,GALA3B,EAAO5E,IAAI,YAAYwH,EAAGI,qBAAqBJ,EAAGI,iBAAiBlD,oBAA2BgD,UAKvE,IAAd,GAAuC,MAAZnB,EAGhC,OADA3B,EAAO5E,IAAI,IAAIwH,EAAGI,0CACX,EAGN,GAAwB,mBAAf,EAA0B,CACpChD,EAAO5E,IAAI,IAAIwH,EAAGI,wBAClB,IAAIC,GAAO,EACX,IACIA,EAAOtB,EAAWiB,EAAG9C,EAASgD,GAElC,MAAMI,GACFlD,EAAO5E,IAAI,mBAAmB8H,EAAEpG,WAEpC,OAAOmG,EAIN,GAAGtB,aAAsBpG,MAAM,CAChCyE,EAAO5E,IAAI,IAAIwH,EAAGI,6BAClB,IAAIC,GAAO,EACX,IAAInD,GAAoB,IAAVA,EAGV,OADAE,EAAO5E,IAAI,UACJ6H,EAEW,GAAnBtB,EAAWzF,QACV8D,EAAO5E,IAAI,YAEf,IAAI,IAAIoB,KAASmF,EAAWwB,SAC5B,CACI,IAAI,IAAIC,KAAQN,EAAejB,QAAQsB,SAEnC,GAAG3G,GAAO4G,EAAK5G,OAAO4G,EAAKC,QAAQ,CAE/B,IAAIC,EAAU,cAAmB9D,OAAO+D,MAAMH,EAAKC,SAEnD,GAAGT,EAAGI,MAAMM,EAAUN,KACtB,CACIC,GAAO,EACPjD,EAAO5E,IAAI,4BACX,OAIZ,GAAG6H,EAAQ,MAEf,OAAOA,EAEN,GAAuB,kBAAd,IAAsC,IAAbtB,EAAkB,CACrD3B,EAAO5E,IAAI,IAAIwH,EAAGI,6BAClB,IAAIC,GAAO,EACX,IAAInD,GAAoB,IAAVA,EAAc,OAAOmD,EACnC,IAAI,IAAIO,KAAOV,EAAejB,QAAQsB,SAClC,GAAGK,EAAIH,QACP,CACI,IAAIC,EAAU,cAAmB9D,OAAO+D,MAAMC,EAAIH,SAClD,GAAGT,EAAGI,MAAMM,EAAUN,KAAK,CACvBC,GAAO,EACPjD,EAAO5E,IAAI,oBACX,OAIZ,OAAO6H,EAIP,OADAjD,EAAO5E,IAAI,IAAIwH,EAAGI,uBACX,E,aC1Hf,MAAMS,EAAU,CACZpF,QAAQ,CACJqF,KAAK,WACD1D,EAAO5E,IAAI,sBAGnB2C,IAAI,CACA2F,KAAKC,GAET1F,KAAK,CACDyF,KAAKC,GAETvF,IAAI,CACAsF,KAAKC,GAETzF,SAAS,CACLwF,KAAKC,GAETC,MAAM,CACFF,KAAKC,GAET9F,IAAI,CACA6F,KAgER,SAAed,EAAGpH,EAAKqI,GACnB,MAAM3C,EAAW,cACjB,IAAI4C,EAAW5C,EAAWE,MAAM2C,QAAQ,GAAG7C,EAAWI,8BAA8B0C,EAAS,GAE7F,GAAa,IAAVF,EACCG,EAAWrB,EAAGpH,EAAKqI,OAIvB,CAGI,GAFAC,EAAS,EAAAI,OAAAA,OAAA,UAAwB,EAAAA,OAAA,mBAA0B,EAAAA,OAAA,iBAAwB,EAAAA,OAAA,gBACnFF,EAAS,EAAAE,OAAAA,OAAA,UAAwB,EAAAA,OAAA,iBAAwB,EAAAA,OAAA,iBAAwB,EAAAA,OAAA,gBACpE,IAAVJ,GAAwB,IAAVE,EAEb,YADA,gBAAqBN,KAAK/E,cAG9B0D,EAASnB,EAAWzD,UAAUa,MAAMwF,EAASE,GAAU7D,MAAK2B,IACxDmC,EAAWrB,EAAGpH,EAAKqI,MACpBlD,OAAMd,IACLG,EAAO5E,IAAI,YAAYyE,EAAI/C,kBAzEvC,SAAS6G,EAAaf,EAAGpH,EAAKqI,GASzB,MAAMhB,EAAW,gBACX3B,EAAW,cAClB,IAAI4C,EAAWjB,EAAS/C,SAErBgE,GAAoB,IAAVA,GACT9D,EAAO5E,IAAI,wBACXyI,EAAKnB,KAIL1C,EAAO5E,IAAI,kCL/ClB,WAEG,MAAM8F,EAAW,cA2GjB,OA1GW,IAAI3C,SAAQ,CAACC,EAAQC,KAE5B,IAAI0F,EAAajD,EAAWA,EAAWkD,YAAYC,YAEnD,GADArE,EAAO5E,IAAI,yBACR+I,GAAoC,mBAAhB,EACnBnE,EAAO5E,IAAI,kEAAkEkJ,OAAOC,GAAG;;;;;;;;;4bAoBvFJ,IAAehE,MAAKqE,IAChB,IAAI,SAAG1E,EAAQ,SAAE0B,EAAQ,OAAEC,EAAM,MAAEC,EAAK,WAAEC,EAAU,YAAEC,GAAgB4C,EAClE1C,EAAY,CAAEhC,SAAAA,EAAU0B,SAAAA,EAAUC,OAAAA,EAAQC,MAAAA,EAAOC,WAAAA,EAAYC,YAAAA,GACjEV,EAAWE,MAAMC,OAAO,GAAGH,EAAWI,kBAAmBQ,GACzD9B,EAAO5E,IAAI,sDACXoD,EAAQsD,MACTnB,OAAMoB,IACL,IAAI0C,EAAyBtI,KAAKC,MAAMD,KAAKE,UAAU,IACvDoI,EAAyB3H,QAAQ2H,EAAyB3H,QAAQuC,OAAO0C,EAAMjF,SAC/EkD,EAAO5E,IAAI,GAAGqJ,EAAyB3H,WACvC2B,EAAOgG,UAKX,CACA,IAAI7F,EAAI,0DACRoB,EAAO5E,IAAI,uBAAuBwD,KAClCsB,EAAMtF,IAAIgE,GAAKuB,MAAKC,IAGZ,GAFAA,EAAKA,EAAKC,KACVL,EAAO5E,IAAI,eACPgF,GAAQA,EAAKE,KAEb,GAAGF,EAAKE,KAAKE,QAAQ,CACjB,IAAImB,EAAavB,EAAKC,KAAMP,EAAWM,EAAKC,KAAKP,SAAU0B,EAAWpB,EAAKC,KAAKmB,SAAUC,EAASrB,EAAKC,KAAKoB,OAAQC,EAAQtB,EAAKC,KAAKqB,MAAOG,EAAQzB,EAAKC,KAAKwB,QAASD,EAAY,GACrL5B,EAAO5E,IAAI,kBAAkBe,KAAKE,UAAUsF,MAExC,IAEQE,GAAyB,GAAhBA,EAAQ3F,QAKjB8D,EAAO5E,IAAI,WACXwG,EAAc,EAAoBD,EAAWE,SAASD,GACtD5B,EAAO5E,IAAI,YANX4E,EAAO5E,IAAI,sCASnB,MAAM8H,GAEF,IAAIwB,EAAsCvI,KAAKC,MAAMD,KAAKE,UAAU,IACpEqI,EAAsC5H,QAAQ4H,EAAsC5H,QAAQuC,OAAO6D,EAAEpG,SACrGkD,EAAO5E,IAAI,GAAGsJ,EAAsC5H,WACpD2B,EAAOiG,GAGX,IAAI5C,EAAY,CAAEhC,SAAAA,EAAU0B,SAAAA,EAAUC,OAAAA,EAAQC,MAAAA,EAAOC,WAAAA,EAAYC,YAAAA,GACjEV,EAAWE,MAAMC,OAAO,GAAGH,EAAWI,kBAAmBQ,GACzD9B,EAAO5E,IAAI,sDACXoD,EAAQsD,OAEZ,CACA,IAAI6C,EAA+BxI,KAAKC,MAAMD,KAAKE,UAAU,IAC7DsI,EAA+B7H,QAAQ6H,EAA+B7H,QAAQuC,OAAOe,EAAKE,KAAKxD,SAC/FkD,EAAO5E,IAAI,GAAGuJ,EAA+B7H,WAC7C2B,EAAOkG,QAIX3E,EAAO5E,IAAI,GAAG,aACdqD,EAAO,MAEhBkC,OAAMoB,IACL,IAAI6C,EAAuBzI,KAAKC,MAAMD,KAAKE,UAAU,IACrDuI,EAAuB9H,QAAQ8H,EAAuB9H,QAAQuC,OAAO0C,EAAMjF,SACxEiF,EAAMlF,MAAM,QAEX4B,EAAOsD,GAERA,EAAMlF,MAAM,OAEX4B,EAAOsD,GAGPtD,EAAOmG,UKvDnBC,GAAgB1E,MAAK,EAAG2E,KAAAA,EAAMrD,OAAAA,EAAQC,MAAAA,EAAOC,WAAAA,EAAYC,YAAAA,MAErD5B,EAAO5E,IAAI,qCACX6I,EAAWrB,EAAGpH,EAAKqI,MACpBlD,OAAMd,IACLG,EAAO5E,IAAI,uBAAuByE,EAAIhD,aAAagD,EAAI/C,WAEpD+C,EAAIhD,MAAM,GAETmD,EAAO5E,IAAI,0BAEXiH,EAASnB,EAAWzD,UAAUkB,eAE1BkB,EAAIhD,MAAM,OAEdmD,EAAO5E,IAAI,UAAU,wDAEjByE,EAAIhD,MAAM,OAEdmD,EAAO5E,IAAI,UAAU,gFAGrB4E,EAAO5E,IAAI,uCAsC3B,SAAS6I,EAAWrB,EAAGpH,EAAKqI,GAEpBlB,EAAqBC,IAKrB5C,EAAO5E,IAAI,sBAAsBwH,EAAGI,WAAW,eAC/Ca,MALA7D,EAAO5E,IAAI,2BAA2B,kBACtCyI,EAAKnB,ICtFb,MAzBwB,CAEtBqC,WAAW,SAASnC,EAAIpH,EAAMqI,GAC5B7D,EAAO5E,IAAI,qBAAqBwH,EAAGI,uCAAuCJ,EAAGI,iCACjE,cACA,GACA,cACA,IACZ,KCdJ,SAAyBJ,EAAIpH,EAAMqI,GAE/B7D,EAAO5E,IAAI,YACX,IAAI6H,EAAON,EAAqBC,GAEhC,GADA5C,EAAO5E,IAAI,YAAW6H,EAAO,SAAS,WACnCA,EACCjD,EAAO5E,IAAI,KAAKwH,EAAGI,6BACnBa,IACA7D,EAAO5E,IAAI,KAAKwH,EAAGI,2DAEnB,CACAhD,EAAO5E,IAAI,6CACX,IAAI4J,EAAgB,UACpB,GAAG,cAAmBvH,UAAUuH,EAChC,CACIhF,EAAO5E,IAAI,+BACX4E,EAAO5E,IAAI,+BACX4E,EAAO5E,IAAI,UAAU,cAAmBqC,0BACxC,IAAIwH,EAAY,EACZC,EAAMrG,OAAOsG,aAAY,KACzBF,GAAa,EACV,cAAmBxH,UAAUuH,IAE5BhF,EAAO5E,IAAI,KAAK6J,uBAAiC,cAAmBxH,YACpE2H,cAAcF,GACXzB,EAAU,cAAmBhG,WAE5BuC,EAAO5E,IAAI,MAAM,cAAmBqC,wCACpCgG,EAAU,cAAmBhG,UAAUiG,KAAKd,EAAIpH,EAAMqI,KAGtD7D,EAAO5E,IAAI,OAAO,cAAmBqC,uCACrCgG,EAAUG,MAAMF,KAAKd,EAAIpH,EAAMqI,OAIzC,QAGF7D,EAAO5E,IAAI,sCACRqI,EAAU,cAAmBhG,WAE5BuC,EAAO5E,IAAI,MAAM,cAAmBqC,2CACpCgG,EAAU,cAAmBhG,UAAUiG,KAAKd,EAAIpH,EAAMqI,KAGtD7D,EAAO5E,IAAI,OAAO,cAAmBqC,0CACrCgG,EAAUG,MAAMF,KAAKd,EAAIpH,EAAMqI,KDhCzCwB,CAAgBzC,EAAGpH,EAAKqI,GAE1B,MAAMX,GAEJlD,EAAO5E,IAAI,oBAAoB8H,EAAEpG,qBAGrCwI,UAAU,SAASC,GACjBvF,EAAO5E,IAAI,qBAAqBmK,EAAWvC,SACjC,iBE1Bd,IAAIwC,ECHJ,OAEIC,QAAQ,EAMRC,YAAY,EACZC,MAAM,CAKJ5C,WAAU,EAIVpF,SAAS,CAACmC,SAAS,GAAG0B,SAAS,GAAGC,OAAO,GAAGC,MAAM,GAAGC,WAAW,GAAGC,YAAY,KAGjFgE,QAAQ,GAIRC,UAAU,CAcRvH,MAAM,SAASqH,EAAMG,GAEnBH,EAAM5C,WAAU,EAGhB4C,EAAMhI,SAASmI,GAQjBC,OAAO,SAASJ,GAEdA,EAAM5C,WAAU,EAChB4C,EAAMhI,SAAS,CAACmC,SAAS,GAAG0B,SAAS,GAAGC,OAAO,GAAGC,MAAM,GAAGC,WAAW,GAAGC,YAAY,MAMzFmC,QACA,CAMEiC,iBAAiB,CAACL,EAAM5B,EAAQkC,EAAUC,IAAsBP,EAAMhI,SAASmC,SAK9EqG,qBAAqB,CAACR,EAAM5B,EAAQkC,EAAUC,IAAsBP,EAAMhI,SAAS6D,SAKpF4E,sBAAsBT,GAAeA,EAAMhI,SAASgE,WAKpD0E,kBAAkBV,GAAeA,EAAMhI,SAAS+D,MAMhD4E,eAAeX,GACNA,EAAMhI,SAASiE,cC1F9B,IAAI2E,ECMJ,IAAIC,EAAW,GAwFf,MAtFW,CAENC,KAAKC,eAAejH,GAEjB,MAAM,OAAED,EAAM,MAAC4B,EAAK,MAACuF,EAAK,WAAEpH,EAAU,MAACqH,EAAK,MAACvL,EAAK,UAAC+I,GAAa3E,EAC1DoH,EAAOpH,EAAQ2E,SAAYrK,EACjC,IAgCI,OA/BAyM,EAAW,CACPhH,OAAAA,EACA4B,MAAAA,EACAuF,MAAAA,EACApH,WAAAA,EACAlE,MAAAA,EACAuL,MAAAA,GAEJJ,EAAWpC,UAAUA,EAClBoC,EAAWpC,YACVoC,EAAWA,EAAWpC,WAAayC,GAEvCL,EAAWnL,MAAMA,IAAO,EACxBmL,EAAWnL,OAAM2E,EAAO5E,IAAI,UAAU4E,EAAO9E,qGAE7CsL,EAAWI,MAAMA,IAAO,EACxBJ,EAAW/I,SAAS+I,EAAWI,MAAM,MAAM,UAC3C,eAAqB,CAAC3J,SAASuJ,EAAWjH,WAAWuH,aAAaN,EAAWA,EAAWpC,YAAYzG,UAAUoJ,UH3B1H,SAAcvH,GACVgG,EAAOhG,EACPgG,EAAOT,WAAW,cAClBS,EAAOF,UAAU,aACjBtF,EAAO5E,IAAI,aGwBH,CAAWoE,GRtBvB,SAAcmH,GAOVzG,EAAMyG,EAGNnE,EAAatC,EAAM8G,aAAa/E,QAAQgF,IAAI,UAAyB,gBACrExE,EAAcvC,EAAM8G,aAAa5E,SAAS6E,IAAI,WAA0B,iBACxEjH,EAAO5E,IAAI,yBAAyBoH,cAAyBC,KQWrDgE,CAAUE,GACNH,EAAWI,OXgI1B,WACG,MAAMM,EAAgB,yDACtB,OAAO,IAAI3I,SAAQ,CAACC,EAAQC,KACxByB,EAAMtF,IAAIsM,GAAe/G,MAAKC,IAC1B,KAAIA,GAAQA,EAAKC,MAAQD,EAAKC,KAAKC,MAoB/B,MADAN,EAAO5E,IAAI,GAAG,aACR,EAnBN,IAAGgF,EAAKC,KAAKC,KAAKE,QAUlB,CACI,IAAI2G,EAA+BhL,KAAKC,MAAMD,KAAKE,UAAU,IAG7D,MAFA8K,EAA+BrK,QAAQqK,EAA+BrK,QAAQuC,OAAOe,EAAKC,KAAKC,KAAKxD,SACpGkD,EAAO5E,IAAI,GAAG+L,EAA+BrK,WACvCqK,EAXN,GADAnH,EAAO5E,IAAI,WAAWgF,EAAKC,KAAKA,KAAK+G,eACjChH,EAAKC,KAAKA,KAAK+G,YAAwC,IAA3BhH,EAAKC,KAAKA,KAAK+G,WAE3C,MAAM,EAEV5I,EAAQ4B,EAAKC,KAAKA,KAAK+G,eAchCzG,OAAMd,IACLpB,EAAOoB,SWxJHwH,GAAclH,MAAK1C,IACf+I,EAAW/I,SAASA,EAAS6J,iBAC9B3G,OAAMd,IACL2G,EAAWnL,OAAM2E,EAAO5E,IAAI,uBAAuByE,EAAI/C,eAG/D0J,EAAWlF,UDzCvB,SAAcF,GACVmF,EAAMnF,EACN,IAAIE,EAAU,cAId,OAFAA,IADS,IAAIiG,MAAOC,UAEpBpG,EAAMqG,eAAenG,EAAU,GACxBA,ECmCsB,CAAUF,GAC/BoF,EAAWnL,OAAM2E,EAAO5E,IAAI,eACrB,EAEX,MAAM8H,GACF,KAAO,aAAaA,EAAEpG,UAI9B4K,UAAS,IACElB,EAEXmB,YAAW,KAGE,CACL5E,UAAiF,IAAtEyD,EAAWpF,MAAM2C,QAAQ,GAAGyC,EAAWlF,+BACoB,MAAtEkF,EAAWpF,MAAM2C,QAAQ,GAAGyC,EAAWlF,8BACvCxB,SAAS0G,EAAWpF,MAAM2C,QAAQ,GAAGyC,EAAWlF,8BAChDE,SAASgF,EAAWpF,MAAM2C,QAAQ,GAAGyC,EAAWlF,kCAChDK,WAAW6E,EAAWpF,MAAM2C,QAAQ,GAAGyC,EAAWlF,mCAClDM,YAAY4E,EAAWpF,MAAM2C,QAAQ,GAAGyC,EAAWlF,4BACnDI,MAAM8E,EAAWpF,MAAM2C,QAAQ,GAAGyC,EAAWlF,+BAC7CoC,KAAKrB,EAASmE,EAAW/I,UACzBA,SAAS+I,EAAW/I,SACpB6D,UAAUkF,EAAWlF,YAgB7BsG,cAAa,IACFvF,G", "sources": ["webpack://vsuiauth4vseaf/webpack/universalModuleDefinition", "webpack://vsuiauth4vseaf/external umd \"@vsui/lib-jsext\"", "webpack://vsuiauth4vseaf/external umd \"jsencrypt\"", "webpack://vsuiauth4vseaf/webpack/bootstrap", "webpack://vsuiauth4vseaf/webpack/runtime/compat get default export", "webpack://vsuiauth4vseaf/webpack/runtime/define property getters", "webpack://vsuiauth4vseaf/webpack/runtime/hasOwnProperty shorthand", "webpack://vsuiauth4vseaf/./logger/index.js", "webpack://vsuiauth4vseaf/./lib/comFun.js", "webpack://vsuiauth4vseaf/./exception/define.js", "webpack://vsuiauth4vseaf/./rest/config/index.js", "webpack://vsuiauth4vseaf/./modes/index.js", "webpack://vsuiauth4vseaf/./modes/define/CAS.js", "webpack://vsuiauth4vseaf/./modes/define/SIAM.js", "webpack://vsuiauth4vseaf/./modes/define/SECURITY.js", "webpack://vsuiauth4vseaf/./modes/common/index.js", "webpack://vsuiauth4vseaf/./modes/define/Dev.js", "webpack://vsuiauth4vseaf/./axios/axios.interceptor.js", "webpack://vsuiauth4vseaf/./axios/index.js", "webpack://vsuiauth4vseaf/./router/router.permission.js", "webpack://vsuiauth4vseaf/./router/router.authby.define.js", "webpack://vsuiauth4vseaf/./router/router.interceptor.js", "webpack://vsuiauth4vseaf/./router/router.auth.js", "webpack://vsuiauth4vseaf/./router/index.js", "webpack://vsuiauth4vseaf/./store/store.auth.js", "webpack://vsuiauth4vseaf/./store/index.js", "webpack://vsuiauth4vseaf/./index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jsencrypt\"), require(\"@vsui/lib-jsext\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"jsencrypt\", \"@vsui/lib-jsext\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"vsuiauth4vseaf\"] = factory(require(\"jsencrypt\"), require(\"@vsui/lib-jsext\"));\n\telse\n\t\troot[\"vsuiauth4vseaf\"] = factory(root[\"jsencrypt\"], root[\"@vsui/lib-jsext\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__35__, __WEBPACK_EXTERNAL_MODULE__558__) {\nreturn ", "module.exports = __WEBPACK_EXTERNAL_MODULE__558__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__35__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "\r\nimport VSAuth from \"../index.js\"\r\n\r\n/**\r\n * 认证组件控制台输出的前缀文字\r\n */\r\nconst prefix=\"lib-vsui-auth4-vseaf\";\r\n\r\n/**\r\n * 认证组件控制台输出的前缀颜色\r\n */\r\nconst prefix_style=\"background:#0f0;color:red;\";\r\n\r\n/**\r\n * 调试内容的默认样式\r\n */\r\nconst msg_style=\"\";\r\n\r\n/**\r\n * 模拟console.log函数，函数签名为(msg,style1,style2,style3,.....)\r\n * 默认输出内容为 %c认证组件前缀:msg, msg内容会按照参数添加样式，无样式参数时使用msg_style作为默认样式\r\n */\r\nfunction log(){\r\n    if(VSAuth.getConfig().DEBUG){\r\n        let args = Array.from(arguments), msg=args[0];\r\n        //给消息前缀和消息添加默认样式\r\n        let msg_format=`%c${prefix}:%c${msg}`;\r\n        let newArgs=[msg_format,prefix_style,msg_style];\r\n        args.splice(0,1);//把参数数组中的msg去掉，仅保留其他的style参数\r\n        newArgs=newArgs.concat(args);  //把style参数添加到新的参数列表中\r\n        //新的参数结构如下 [消息,前缀样式,msg默认样式,style1,style2,style3,...]     \r\n        console.log.apply(console,newArgs)\r\n    }\r\n}\r\n\r\n\r\n\r\nexport default {\r\n    prefix,\r\n    log,\r\n};", " /***\r\n *  @author:cuiliang \r\n *  @email: <EMAIL>\r\n *  @date：20220506\r\n *  @version:V1.1.0\r\n *  @description:\r\n *  该模块开放函数在需要的地方引入即可，禁止全局引入，因为全局引入后您仍需要在每个使用的模块中单独引入\r\n *  该模块为浏览器全屏模式与正常模式开关函数，内置状态机制，初始为正常模式，有如下功能：\r\n *  1：convertDataToModuleTree 将获取的权限数据（均是平级，但在数据中由父子描述）进行父子树状结构的生成，形成具有树状结构的模块结构\r\n * \r\n *  @example:\r\n *  无\r\n * \r\n *  @interface:\r\n *  convertDataToModuleTree(json)\r\n * \r\n * \r\n */ \r\n\r\n\r\n/**\r\n  * 将返回的平级模块数组转变为具有树形层级的对象，\r\n  * \r\n  * 原始数据结构为：\r\n  * [\r\n  *     {\r\n\t\t\t\"resId\": \"6DB5044BD1C9493B88471241B76F4FBD\",//资源ID\r\n\t\t\t\"resName\": \"审计查询\",\r\n\t\t\t\"iconClass\": \"fa fa-tachometer\",\r\n\t\t\t\"resPid\": \"0\",//父资源ID，为0则没有父资源\r\n\t\t},\r\n\t\t{\r\n\t\t\t\"resId\": \"67385875FCA14FC0972D6665B6CD95FA\",\r\n\t\t\t\"resName\": \"资源管理\",\r\n\t\t\t\"resPath\": \"/authm/resource\",\r\n\t\t\t\"iconClass\": \"fa fa-podcast\",\r\n\t\t\t\"resPid\": \"6DB5044BD1C9493B88471241B76F4FBD\",\r\n\t\t\t\r\n        },\r\n        ...]\r\n    转变后：\r\n    [\r\n        {\r\n\t\t\t\"resId\": \"6DB5044BD1C9493B88471241B76F4FBD\",\r\n\t\t\t\"resName\": \"审计查询\",\r\n\t\t\t\"resPid\": \"0\", //这里是父级标记\r\n\t\t\t\"iconClass\": \"fa fa-tachometer\",\r\n            \"children\":[\r\n                {\r\n                    \"resId\": \"67385875FCA14FC0972D6665B6CD95FA\",\r\n\t\t\t\t\t\"resPid\": \"6DB5044BD1C9493B88471241B76F4FBD\",\r\n                    \"resName\": \"资源管理\",\r\n\t\t\t\t\t\"iconClass\": \"fa fa-podcast\",\r\n                    \"resPath\": \"/authm/resource\",\r\n                    \r\n                   \r\n                },\r\n                ...\r\n            ]\r\n        },\r\n        ...\r\n    ]\r\n  * @param {JSON} list \r\n  */\r\nfunction convertDataToModuleTree(list){\r\n\tif (!list || !list.length) {\r\n\t\treturn []\r\n\t  }\r\n\t  list=JSON.parse(JSON.stringify(list));\r\n\t  let treeListMap = {};\r\n\t  for (let item of list) {\r\n\t\ttreeListMap[item.resId] = item\r\n\t  }\r\n\t  for (let i = 0; i < list.length; i++) {\r\n\t\tif (list[i].resPid && treeListMap[list[i].resPid]) {\r\n\t\t  if (!treeListMap[list[i].resPid].children) {\r\n\t\t\ttreeListMap[list[i].resPid].children = []\r\n\t\t  }\r\n\t\t  treeListMap[list[i].resPid].children.push(list[i]);\r\n\t\t  list.splice(i, 1);\r\n\t\t  i--\r\n\t\t}\r\n\t  }\r\n\t  return list\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\nexport {\r\n\tconvertDataToModuleTree,\r\n}", "\r\nexport default {\r\n    login_usernameorpwdErr:{code:\"vueauth4vseaf-1001\",message:\"登录失败,账号或密码错误,异常为:{0}\"},\r\n    login_dataFormatErr:{code:\"vueauth4vseaf-1002\",message:\"登录过程返回数据格式错误\"},//登陆用户名密码返回数据格式错误\r\n    login_unknownAuthMode:{code:\"vueauth4vseaf-1003\",message:\"未知鉴权模式,找不到对应的登录过程\"},\r\n    getUserInfo_dataFormatErr:{code:\"vueauth4vseaf-1101\",message:\"获取用户信息过程返回数据格式错误\"},\r\n    //getUserInfo_netWorkErr:{code:\"vueauth4vseaf-1102\",message:\"获取用户信息过程网络错误\"},\r\n    getUserInfo_serverReturnFailed:{code:\"vueauth4vseaf-1103\",message:\"服务端未能获取到用户信息,异常为:{0}\"},\r\n    getUserInfo_netWorkErr:{code:\"vueauth4vseaf-1104\",message:\"向后端vseaf框架发送请求产生网络错误,异常为:{0},如果异常为404意味着vseaf的地址配置有误,请检查配置\"},\r\n    getUserInfo_convertorModuleTreeFailed:{code:\"vueauth4vseaf-1105\",message:\"转换资源树出错,异常为:{0}\"},\r\n    getUserInfo_customGetErr:{code:\"vueauth4vseaf-1106\",message:\"调用自定义过程获取用户信息失败:{0}\"},\r\n    logout_serverReturnFailed:{code:\"vueauth4vseaf-1301\",message:\"服务端未能正常退出,异常为:{0}\"},\r\n    logout_dataFormatErr:{code:\"vueauth4vseaf-1302\",message:\"退出过程返回数据格式错误\"},\r\n    logout_unknownAuthMode:{code:\"vueauth4vseaf-1303\",message:\"未知鉴权模式,找不到对应的退出过程\"},\r\n    axios_watchUnlogin:{code:\"vueauth4vseaf-1401\",message:\"Axios检测协议中存在未登录标志\"},\r\n    axios_watchNoAccess:{code:\"vueauth4vseaf-1402\",message:\"Axios检测协议中告知无权限访问该接口\"},\r\n    encrypt_getPublicKey_serverReturnFailed:{code:\"vueauth4vseaf-1501\",message:\"获取RSA加密公钥失败,异常为:{0}\"},\r\n    encrypt_getPublicKey_dataFormatErr:{code:\"vueauth4vseaf-1502\",message:\"获取RSA加密公钥返回数据格式错误\"},\r\n    encrypt_getPublicKey_keyIsEmpty:{code:\"vueauth4vseaf-1503\",message:\"获取RSA加密公钥为空值\"},\r\n    getAuthMode_dataFormatErr:{code:\"vueauth4vseaf-1601\",message:\"获取鉴权模式过程返回数据格式错误\"},\r\n    getAuthMode_serverReturnFailed:{code:\"vueauth4vseaf-1602\",message:\"服务端未能获取到鉴权模式,异常为:{0}\"},\r\n    getAuthMode_modeIsEmpty:{code:\"vueauth4vseaf-1603\",message:\"获取鉴权模式为空值\"},\r\n    authMode_unRegisteredMode:{code:\"vueauth4vseaf-1701\",message:\"未注册的登陆模式,请检查插件使用是否正确\"},\r\n    authMode_unRegisteredModeWhenToLoginPage:{code:\"vueauth4vseaf-1702\",message:\"检测到使用未注册的登陆模式[{0}]进行页面跳转,请检查该插件是否支持该登陆模式\"}\r\n    //logout_netWorkErr:{code:\"vueauth4vseaf-1302\",message:\"用户退出过程网络错误\"},\r\n   \r\n}", "\r\n\r\nlet config={};\r\n\r\nexport default {\r\n    initBasePath({basepath}){\r\n        config.BasePath=basepath;\r\n    },\r\n    RestFulAPI:{\r\n        Interface:{\r\n            \r\n            Encrypt:{\r\n                RSA:{\r\n                    getPublicKeyAddr:function(){return config.BasePath+\"/secretKey\"},\r\n                },\r\n            },\r\n            AuthInfo:{\r\n                authMode:{\r\n                    getSecureType:function(){return config.BasePath+\"/secureType\"},\r\n                },\r\n                userInfo:{\r\n                    /**\r\n                     * \r\n                     * @returns 可配置的获取用户信息接口，默认为vseaf提供的用户接口\r\n                     */\r\n                    getCurrentUser:function(){return config.BasePath+\"/vseaf/currentUser\";}\r\n                },\r\n                DEV:{\r\n                    getLoginAddr:function(){return \"/apidata/auth/login\"},\r\n                },\r\n                CAS:{\r\n                    getLoginAddr:function(){return config.BasePath+\"/home/<USER>\"},\r\n                    getLogoutAddr:function(){return config.BasePath+\"/logout/cas?target={backurl}\"}\r\n                },\r\n                SIAM:{\r\n                    getLoginAddr:function(){return config.BasePath+\"/home/<USER>\"},\r\n                    getLogoutAddr:function(){return config.BasePath+\"/logout/siam\"}\r\n                },\r\n                SECURITY:{\r\n                    getLoginAddr:function(){return config.BasePath+\"/login\"},\r\n                    getLogoutAddr:function(){return config.BasePath+\"/logout\"}\r\n                },\r\n                OAuth2:{\r\n\r\n                },\r\n                JWT:{\r\n\r\n                }, \r\n            },\r\n\r\n        }\r\n    }\r\n}", "\r\n\r\nimport {toLoginPage as toDEVLoginPage,login as VSDEV_login,logout as VSDEV_logout} from \"./define/Dev.js\";\r\nimport {toLoginPage as toSECURITYLoginPage,login as VSECURITY_login,logout as VSECURITY_logout} from \"./define/SECURITY.js\";\r\nimport {toLoginPage as toCASLoginPage,logout as CAS_logout} from \"./define/CAS.js\";\r\nimport {toLoginPage as toSIAMLoginPage,logout as SIAM_logout} from \"./define/SIAM.js\";\r\nimport inner_exception from \"../exception/define\";\r\n\r\nconst modesDef={\r\n    unknown:{\r\n        login:function(){\r\n            return new Promise((resolve,reject)=>{\r\n                reject(inner_exception.login_unknownAuthMode);\r\n            })\r\n        },\r\n        logout:function(){\r\n            return new Promise((resolve,reject)=>{\r\n                reject(inner_exception.logout_unknownAuthMode);\r\n            })\r\n        }\r\n    },\r\n    CAS:{\r\n        toLoginPage:toCASLoginPage,//后端定义的登陆地址\r\n        logout:CAS_logout,\r\n    },\r\n    SIAM:{\r\n        toLoginPage:toSIAMLoginPage,//后端定义的登陆地址\r\n        logout:SIAM_logout,\r\n    },\r\n    \r\n    SECURITY:{\r\n        toLoginPage:toSECURITYLoginPage,\r\n        login:VSECURITY_login,\r\n        logout:VSECURITY_logout,\r\n    },\r\n    DEV:{\r\n        toLoginPage:toDEVLoginPage,\r\n        login:VSDEV_login,\r\n        logout:VSDEV_logout,\r\n    }\r\n}\r\n\r\nexport default modesDef;", "import pathDef from \"../../rest/config/index\";\r\nimport VSAuth from \"../../index.js\"\r\n\r\n\r\n/**\r\n * 跳转到登录页面\r\n * @returns 无返回\r\n */\r\nfunction toLoginPage() {\r\n    let url = window.location.href;\r\n    let baseurl=`${location.protocol}//${location.host}`;\r\n    let path=url.replace(`${baseurl}`,\"\");\r\n    let loginURL = pathDef.RestFulAPI.Interface.AuthInfo[VSAuth.getConfig().authMode].getLoginAddr();//cas登录地址\r\n    loginURL = loginURL.format({\r\n        \"backurl\":encodeURIComponent(url),\r\n        \"prefix\":encodeURIComponent(`${baseurl+VSAuth.getConfig().whereVSEAF}`)\r\n    });\r\n    location.href=loginURL;\r\n}\r\n\r\n\r\n/**\r\n * 退出函数\r\n * @returns Promise\r\n */\r\nfunction logout(){\r\n    return new Promise((resolve,reject)=>{\r\n        try\r\n        {          \r\n            let baseurl=`${location.protocol}//${location.host}`;\r\n            let aa=VSAuth.getConfig().router;\r\n            let basepath=VSAuth.getConfig().router.options.history.base;\r\n            let logoutURL = pathDef.RestFulAPI.Interface.AuthInfo[VSAuth.getConfig().authMode].getLogoutAddr();//退出地址\r\n            logoutURL = logoutURL.format({\"backurl\":encodeURIComponent(baseurl+basepath)})\r\n            resolve();\r\n            location.href=logoutURL;\r\n        }\r\n        catch(err)\r\n        {\r\n            reject(err);\r\n        }\r\n    });\r\n}\r\n\r\nexport {toLoginPage,logout}", "import pathDef from \"../../rest/config/index\";\r\nimport VSAuth from \"../../index.js\"\r\n\r\n/**\r\n * 跳转到登录页面\r\n * @returns 无返回\r\n */\r\nfunction toLoginPage() {\r\n    let url = window.location.href;\r\n    let baseurl=`${location.protocol}//${location.host}`;\r\n    let path=url.replace(`${baseurl}`,\"\");\r\n    let loginURL = pathDef.RestFulAPI.Interface.AuthInfo[VSAuth.getConfig().authMode].getLoginAddr();//SIAM登录地址\r\n    loginURL = loginURL.format({\r\n        \"backurl\":encodeURIComponent(url),\r\n        \"prefix\":encodeURIComponent(`${baseurl+VSAuth.getConfig().whereVSEAF}`)\r\n    });\r\n    location.href=loginURL;\r\n}\r\n\r\n/**\r\n * 退出函数\r\n * @returns Promise\r\n */\r\nfunction logout(){\r\n    return new Promise((resolve,reject)=>{\r\n        try\r\n        {          \r\n            let logoutURL = pathDef.RestFulAPI.Interface.AuthInfo[VSAuth.getConfig().authMode].getLogoutAddr();//退出地址\r\n            resolve();\r\n            location.href=logoutURL;\r\n        }\r\n        catch(err)\r\n        {\r\n            reject(err);\r\n        }\r\n    });\r\n}\r\n\r\nexport {toLoginPage,logout}", "import path from \"../../rest/config/index\";\r\nimport logger from \"../../logger\"\r\nimport VSAuth from \"../../index.js\"\r\nimport {Axios} from \"../../axios/index.js\";\r\nimport {getRSAPublicKey} from \"../common/index.js\";\r\nimport inner_exception from \"../../exception/define\";\r\n//import JsEncrypt from \"jsencrypt\";\r\nimport JSEncrypt from 'jsencrypt';\r\n\r\n\r\n/**\r\n * 跳转到登录页面\r\n * @returns 无返回\r\n */\r\nfunction toLoginPage(){\r\n    VSAuth.getConfig().router.push(\"/login\");\r\n}\r\n\r\n\r\n\r\n/**\r\n * 用户登录\r\n * 在获取数据前如果登陆已经失效，获取的数据为html内容，此时已经在axios库中对登录失效进行了拦截并做了跳转\r\n * @param {String} userName 用户名\r\n * @param {String} passWord 密码\r\n * @returns {Promise} 返回登录过程异步对象，后可跟自定义处理或错误处理，如login(\"cuiliang\",\"1\").then(resp=>{}).catch(e){alert(e.message)}\r\n */\r\nfunction login(userName, passWord) {\r\n   return new Promise((resolve,reject)=>{\r\n        const loginURL = path.RestFulAPI.Interface.AuthInfo[VSAuth.getConfig().authMode].getLoginAddr();//登录验证地址\r\n        logger.log(`使用[用户名:${userName}，密码：${passWord}]登录，restAPI地址为：${loginURL}`);\r\n        getRSAPublicKey().then(publickey=>{\r\n\r\n            let encrypt = new JSEncrypt();\r\n            encrypt.setPublicKey(publickey);\r\n            passWord=encrypt.encrypt(passWord);\r\n            Axios.post(loginURL,{\"secureName\":userName,\"secureSecret\":passWord}).then(resp=>{ \r\n                if (resp && resp.data && resp.data.meta)\r\n                {\r\n                    logger.log(`服务器端数据返回，状态码为：${resp.data.meta.status}`);\r\n                    if(resp.data.meta.success)\r\n                    {\r\n                        logger.log(`用户名密码验证成功`);\r\n                        resolve();\r\n                    }\r\n                    else\r\n                    {\r\n                        let login_usernameorpwdErr=JSON.parse(JSON.stringify(inner_exception.login_usernameorpwdErr));\r\n                        login_usernameorpwdErr.message=login_usernameorpwdErr.message.format(resp.data.meta.message);\r\n                        logger.log(`${login_usernameorpwdErr.message}`);\r\n                        throw login_usernameorpwdErr;\r\n                    }\r\n                }\r\n                else { \r\n                    logger.log(`${inner_exception.login_dataFormatErr.message}`);\r\n                    throw inner_exception.login_dataFormatErr;\r\n                }\r\n            }).catch(err=>{\r\n                reject(err);\r\n            })\r\n        }).catch(err=>{\r\n            reject(err);\r\n        })\r\n        \r\n        \r\n    });\r\n    return result;\r\n}\r\n\r\n/**\r\n * 退出函数\r\n * @returns Promise\r\n */\r\n function logout(){\r\n    const authConfig=VSAuth.getConfig();\r\n    const logoutURL = path.RestFulAPI.Interface.AuthInfo[authConfig.authMode].getLogoutAddr();//退出地址\r\n    logger.log(`退出restAPI地址为：${logoutURL}`);\r\n    let result=new Promise((resolve,reject)=>{\r\n        Axios.post(logoutURL,{}).then(resp=>{ \r\n            if (resp && resp.data && resp.data.meta)\r\n            {\r\n                logger.log(`服务器端数据返回，状态码为：${resp.data.meta.status}`);\r\n                if(resp.data.meta.success)\r\n                {\r\n                    logger.log(`账户退出成功`);\r\n                    authConfig.store.commit(`${authConfig.storeName}/logOut`);\r\n                    resolve();\r\n                }\r\n                else\r\n                {\r\n                    let logout_serverReturnFailed=JSON.parse(JSON.stringify(inner_exception.logout_serverReturnFailed));\r\n                    logout_serverReturnFailed.message=logout_serverReturnFailed.message.format(resp.data.meta.message);\r\n                    logger.log(`${logout_serverReturnFailed.message}`);\r\n                    throw logout_serverReturnFailed;\r\n                }\r\n            }\r\n            else{\r\n                logger.log(`${inner_exception.logout_dataFormatErr.message}`);\r\n                throw inner_exception.logout_dataFormatErr;\r\n            }\r\n        }).catch(err=>{\r\n            reject(err);\r\n        })\r\n    })\r\n    return result;\r\n}\r\n\r\n\r\nexport {toLoginPage,login,logout}", "import {convertDataToModuleTree as moduleTreeConvertor} from \"../../lib/comFun\";\r\nimport VSAuth from \"../../index.js\"\r\nimport inner_exception from \"../../exception/define\";\r\nimport logger from \"../../logger\";\r\nimport path from \"../../rest/config/index\";\r\nimport {Axios} from \"../../axios/index.js\";\r\n\r\n\r\n/**\r\n * 直接向后台获取用户信息与用户权限信息\r\n * @returns {Promise} 返回获取用户权限的异步对象，resolve({name,passwd,token,permission,modulesTree}),reject({code,message})\r\n */\r\n function getPermission(){\r\n\r\n    const authConfig=VSAuth.getConfig();\r\n    let result=new Promise((resolve,reject)=>{\r\n       \r\n        let _getUserInfo=authConfig[authConfig.customCfg]?.getUserInfo;\r\n        logger.log(`准备向后端VSEAF发送获取用户信息的请求`);\r\n        if(_getUserInfo&&typeof(_getUserInfo)==\"function\"){\r\n            logger.log(`发现用户自定义获取用户信息过程，该过程为Promise对象，您应在resolve回调函数中传递object类型的参数，对象为：${String.raw`\r\n            {\r\n                userName:\"登录账号,即用户名,不可留空\",\r\n                realName:\"用户真实名称，如：张三\"\r\n                passwd:\"用户密码,为安全考虑,可留空\",\r\n                token:\"无状态模式下的凭据信息,可留空\",\r\n                permission:保存后端返回的完整用户信息,不可留空，数据结构请参考vsnpm文档,\r\n                modulesTree:用户菜单资源数组,请转换后按照固定结构传入,不可留空,数据结构请参考vsnpm文档\r\n            }\r\n            `}，您如果后端使用胜软VSEAF框架，当用户未登录状态下获取用户信息时，会返回带有610状态码的JSON数据，会自动触发跳转登陆页面的过程，\r\n            如果不是VSEAF框架，如果您想自动触发未登录情况下的自动跳转，您需要模拟出带610的json数据，格式：\r\n                {\r\n                    \"meta\": \r\n                    {   \r\n                        \"message\": \"No authentication information .\",\r\n                        \"success\": false,\r\n                        \"status\": 610\r\n                    }\r\n                }。\r\n            使用该过程！`);\r\n            _getUserInfo().then(userData=>{\r\n                let {  userName, realName, passwd, token, permission, modulesTree } = userData;\r\n                let loginData = { userName, realName, passwd, token, permission, modulesTree };\r\n                authConfig.store.commit(`${authConfig.storeName}/login`, loginData);\r\n                logger.log(`将用户信息写入VUEX，可用vueDevTools查看VUEX中前缀为vsui-auth4-的状态值`);\r\n                resolve(loginData);\r\n            }).catch(error=>{\r\n                let getUserInfo_customGetErr=JSON.parse(JSON.stringify(inner_exception.getUserInfo_customGetErr));\r\n                getUserInfo_customGetErr.message=getUserInfo_customGetErr.message.format(error.message);\r\n                logger.log(`${getUserInfo_customGetErr.message}`);\r\n                reject(getUserInfo_customGetErr);\r\n                \r\n                \r\n            })\r\n        }\r\n        else{\r\n            let url=path.RestFulAPI.Interface.AuthInfo.userInfo.getCurrentUser();\r\n            logger.log(`使用内置获取用户信息的方法，请求地址为：${url}`);\r\n            Axios.get(url).then(resp=>{\r\n                    resp=resp.data;\r\n                    logger.log(`发送获取用户信息的请求`);\r\n                    if (resp && resp.meta)\r\n                    {\r\n                        if(resp.meta.success){\r\n                            let permission = resp.data, userName = resp.data.userName, realName = resp.data.realName, passwd = resp.data.passwd, token = resp.data.token, resList=resp.data.resList, modulesTree=[];\r\n                            logger.log(`获取用户信息成功，返回数据为：${JSON.stringify(permission)}`);\r\n                                \r\n                                try\r\n                                {\r\n                                    if(!resList||resList.length==0){\r\n                                        logger.log(`发现资源树数据为空或资源菜单个数为0,这可能是用户未分配资源权限导致`);\r\n                                    }\r\n                                    else\r\n                                    {\r\n                                        logger.log(`转换资源树开始`);\r\n                                        modulesTree = moduleTreeConvertor(permission.resList||modulesTree);\r\n                                        logger.log(`转换资源树完成`);\r\n                                    }\r\n                                }\r\n                                catch(e)\r\n                                {\r\n                                    let getUserInfo_convertorModuleTreeFailed=JSON.parse(JSON.stringify(inner_exception.getUserInfo_convertorModuleTreeFailed));\r\n                                    getUserInfo_convertorModuleTreeFailed.message=getUserInfo_convertorModuleTreeFailed.message.format(e.message);\r\n                                    logger.log(`${getUserInfo_convertorModuleTreeFailed.message}`);\r\n                                    reject(getUserInfo_convertorModuleTreeFailed);\r\n                                }\r\n\r\n                                let loginData = { userName, realName, passwd, token, permission, modulesTree };\r\n                                authConfig.store.commit(`${authConfig.storeName}/login`, loginData);\r\n                                logger.log(`将用户信息写入VUEX，可用vueDevTools查看VUEX中前缀为vsui-auth4-的状态值`);\r\n                                resolve(loginData);\r\n                        }\r\n                        else{\r\n                            let getUserInfo_serverReturnFailed=JSON.parse(JSON.stringify(inner_exception.getUserInfo_serverReturnFailed));\r\n                            getUserInfo_serverReturnFailed.message=getUserInfo_serverReturnFailed.message.format(resp.meta.message);\r\n                            logger.log(`${getUserInfo_serverReturnFailed.message}`);\r\n                            reject(getUserInfo_serverReturnFailed);\r\n                        } \r\n                            \r\n                    }else{\r\n                        logger.log(`${inner_exception.getUserInfo_dataFormatErr.message}`);\r\n                        reject(inner_exception.getUserInfo_dataFormatErr);\r\n                    }\r\n            }).catch(error=>{\r\n                let getUserInfo_netWorkErr=JSON.parse(JSON.stringify(inner_exception.getUserInfo_netWorkErr));\r\n                getUserInfo_netWorkErr.message=getUserInfo_netWorkErr.message.format(error.message);\r\n                if(error.code==inner_exception.axios_watchUnlogin.code)\r\n                {\r\n                    reject(error);\r\n                }\r\n                if(error.code==inner_exception.authMode_unRegisteredModeWhenToLoginPage.code)\r\n                {\r\n                    reject(error);\r\n                }\r\n                else{\r\n                    reject(getUserInfo_netWorkErr);\r\n                }\r\n                \r\n            });\r\n        }\r\n    }) ;\r\n    return result;\r\n}\r\n\r\n\r\n/**\r\n * 获取RSA加密方式的公钥\r\n */\r\n function getRSAPublicKey(){\r\n    const publicKeyURL = path.RestFulAPI.Interface.Encrypt.RSA.getPublicKeyAddr();//公钥获取地址\r\n    logger.log(`获取公钥，restAPI地址为：${publicKeyURL}`);\r\n    return new Promise((resolve,reject)=>{\r\n        let result1= Axios.get(publicKeyURL).then(resp=>{\r\n            if (resp && resp.data && resp.data.meta){\r\n                logger.log(`服务器端数据返回，状态码为：${resp.data.meta.status}`);\r\n                if(resp.data.meta.success)\r\n                {\r\n                    logger.log(`获取公钥为：${resp.data.data.publicKey}`);\r\n                    if(!resp.data.data.publicKey|| resp.data.data.publicKey==\"\")\r\n                    {\r\n                        throw inner_exception.encrypt_getPublicKey_keyIsEmpty;\r\n                    }\r\n                    resolve(resp.data.data.publicKey);\r\n                }\r\n                else\r\n                {\r\n                    let encrypt_getPublicKey_serverReturnFailed=JSON.parse(JSON.stringify(inner_exception.encrypt_getPublicKey_serverReturnFailed));\r\n                    encrypt_getPublicKey_serverReturnFailed.message=encrypt_getPublicKey_serverReturnFailed.message.format(resp.data.meta.message);\r\n                    logger.log(`${encrypt_getPublicKey_serverReturnFailed.message}`);\r\n                    throw encrypt_getPublicKey_serverReturnFailed;\r\n                }\r\n            }\r\n            else{\r\n                logger.log(`${inner_exception.encrypt_getPublicKey_dataFormatErr.message}`);\r\n                throw inner_exception.encrypt_getPublicKey_dataFormatErr;\r\n            }\r\n        }).catch(err=>{\r\n            reject(err)\r\n        })\r\n    });\r\n}\r\n\r\n/**\r\n * 获取服务器端设置的鉴权模式\r\n */\r\n function getAuthMode(){\r\n    const secureTypeURL = path.RestFulAPI.Interface.AuthInfo.authMode.getSecureType();//登陆模式获取地址\r\n    return new Promise((resolve,reject)=>{\r\n        Axios.get(secureTypeURL).then(resp=>{\r\n            if (resp && resp.data && resp.data.meta){\r\n                if(resp.data.meta.success)\r\n                {\r\n                    logger.log(`获取登陆模式为：${resp.data.data.secureType}`);\r\n                    if(!resp.data.data.secureType|| resp.data.data.secureType==\"\")\r\n                    {\r\n                        throw inner_exception.getAuthMode_modeIsEmpty;\r\n                    }\r\n                    resolve(resp.data.data.secureType);\r\n                }\r\n                else\r\n                {\r\n                    let getAuthMode_serverReturnFailed=JSON.parse(JSON.stringify(inner_exception.getAuthMode_serverReturnFailed));\r\n                    getAuthMode_serverReturnFailed.message=getAuthMode_serverReturnFailed.message.format(resp.data.meta.message);\r\n                    logger.log(`${getAuthMode_serverReturnFailed.message}`);\r\n                    throw getAuthMode_serverReturnFailed;\r\n                }\r\n            }\r\n            else{\r\n                logger.log(`${inner_exception.getAuthMode_dataFormatErr.message}`);\r\n                throw inner_exception.getAuthMode_dataFormatErr;\r\n            }\r\n        }).catch(err=>{\r\n            reject(err)\r\n        })\r\n    });\r\n}\r\n\r\nexport {getPermission,getRSAPublicKey,getAuthMode}", "/**\r\n * by cuiliang on 20200612\r\n * \r\n * dev鉴权模式，适用于开发模式下，如未准备好权限中心或权限中心数据，而又想快速搭建框架即用户基础数据则使用此模式\r\n * \r\n * 此模式以本地文件系统为虚拟数据接口，访问/apidata/auth/login文件获取用户权限信息\r\n * \r\n */\r\n\r\n\r\nimport path from \"../../rest/config/index\";\r\nimport {convertDataToModuleTree as moduleTreeConvertor} from \"../../lib/comFun\";\r\nimport VSAuth from \"../../index.js\"\r\nimport {Axios} from \"../../axios/index.js\";\r\nimport logger from \"../../logger\"\r\n\r\n/**\r\n * 跳转到登录页面\r\n * @returns 无返回\r\n */\r\nfunction toLoginPage(){\r\n    VSAuth.getConfig().router.push(\"/login\");\r\n}\r\n\r\n /**\r\n     * 用户登录\r\n     * @param {String} username 用户名\r\n     * @param {String} password 密码\r\n     * @returns Promise\r\n     */\r\nfunction login(userName, passWord )\r\n{\r\n    logger.log(`用户名:${userName},密码：${passWord}`);\r\n    return new Promise((resolve,reject)=>{\r\n        const loginURL = path.RestFulAPI.Interface.AuthInfo.DEV.getLoginAddr();\r\n        const authConfig=VSAuth.getConfig();\r\n        Axios.get(loginURL).then(resp => {\r\n            if(resp&&resp.data ){\r\n                let data = resp.data.data;\r\n                \r\n                \r\n                let realName = data.realName, passwd = passWord, token = \"\", permission = data;\r\n                let modulesTree = moduleTreeConvertor(data.resList);\r\n                let loginData = { userName, realName, passwd, token, permission, modulesTree };\r\n                authConfig.store.commit(`${authConfig.storeName}/login`, loginData);\r\n                resolve(loginData)\r\n            }\r\n            else { throw new Error(\"没有返回信息\")}\r\n        }).catch(error=> {\r\n            reject(new Error( `登录过程失败<br/>${error.message}`));})\r\n    });\r\n        \r\n}\r\n\r\n/**\r\n * 退出函数\r\n * @returns Promise\r\n */\r\nfunction logout(){\r\n    const authConfig=VSAuth.getConfig();\r\n    return new Promise((resolve,reject)=>{\r\n        try{\r\n            authConfig.store.commit(`${authConfig.storeName}/logOut`);\r\n            resolve();\r\n        }\r\n        catch(err){\r\n            reject(err);\r\n        }\r\n\r\n    })\r\n}\r\n\r\nexport {toLoginPage,login,logout};", "/**\r\n * POWER by vue-router ^3.1.3\r\n * 路由拦截器，可在路由跳转过程中进行拦截处理，\r\n * Created in 20200118 by cuiliang 崔良于2020年1月18日，公司18周年纪念\r\n *\r\n *\r\n */\r\n\r\nimport VSAuth from \"../index.js\"\r\nimport modesDef from \"../modes/index.js\"\r\nimport inner_exception from \"../exception/define\";\r\nimport logger from \"../logger\";\r\n\r\nconst v_access_ticket_key=\"V-Access-Ticket\";\r\nconst v_access_ticket_value=\"lib-vsui-auth4-vseaf\";\r\n\r\nconst axiosInterceptor={\r\n\r\n    request:function(config){\r\n        logger.log(`进入axios请求拦截器`);\r\n        config.headers[v_access_ticket_key]=v_access_ticket_value;\r\n        logger.log(`离开axios请求拦截器`);\r\n        return config;\r\n    },\r\n    requestError:function(error)\r\n    {\r\n        logger.log(`!!!axios请求拦截器出错,出错原因:${error.message},导致抛出异常`);\r\n        throw error;\r\n    },\r\n    response:function(response){\r\n        logger.log(`进入axios应答拦截器`);\r\n        \r\n            \r\n        if(response&&response.data&&response.data.meta)\r\n        {\r\n            if(response.data.meta.success){\r\n                logger.log(`axios应答拦截器获知数据获取成功`);\r\n            }\r\n            else{\r\n                logger.log(`axios应答拦截器获知数据获取失败`);\r\n                if(response.data.meta.status==610)\r\n                {\r\n                    logger.log(`axios应答拦截器检查是因为用户未登录，导致接口无法访问，将调用登陆跳转过程，并尝试(因为后续操作可能引发其他异常)抛出异常代码:${inner_exception.axios_watchUnlogin.code}`);\r\n                    if(modesDef[VSAuth.getConfig().authMode])\r\n                    {\r\n                        modesDef[VSAuth.getConfig().authMode].toLoginPage();\r\n                    }\r\n                    else{\r\n                        let authMode_unRegisteredModeWhenToLoginPage=JSON.parse(JSON.stringify(inner_exception.authMode_unRegisteredModeWhenToLoginPage));\r\n                        authMode_unRegisteredModeWhenToLoginPage.message=authMode_unRegisteredModeWhenToLoginPage.message.format(VSAuth.getConfig().authMode);\r\n                        throw authMode_unRegisteredModeWhenToLoginPage;\r\n                    }\r\n                    \r\n                    throw inner_exception.axios_watchUnlogin;\r\n                }\r\n                else if(response.data.meta.status==403)\r\n                {\r\n                    logger.log(`axios应答拦截器检查是因为没有访问接口的权限，导致接口无法访问，将抛出异常代码:${inner_exception.axios_watchUnlogin.code}`);\r\n                    throw inner_exception.axios_watchNoAccess;\r\n                }\r\n            }\r\n        }\r\n        else\r\n        {\r\n            logger.log(`axios应答拦截器检查发现返回的数据格式存在问题`);\r\n        }\r\n        logger.log(`离开axios应答拦截器`);\r\n        return response;\r\n        \r\n    },\r\n    responseError:function(error)\r\n    {\r\n        logger.log(`!!!axios应答拦截器出错,出错原因:${error.message},导致抛出异常`);\r\n        throw error;\r\n    },\r\n\r\n}\r\n\r\nexport default axiosInterceptor;", "/*\r\nby cuiliang\r\n20180914\r\n用途：对axios库的请求，应答进行拦截，对请求与应答过程中出现的错误进行统一处理\r\n\r\n\r\n*/\r\nimport axiosInterceptor from \"./axios.interceptor.js\";\r\nimport logger from \"../logger\";\r\n\r\n\r\nlet Axios,requestIndex=0,responseIndex=0;\r\n\r\nfunction init(axios)\r\n{\r\n    /*Axios=axios.create(\r\n        {\r\n            responseType: 'json',\r\n            timeout: 50000,}\r\n        );*/\r\n    Axios=axios;\r\n    //Axios.interceptors.request.handlers.unshift({fulfilled:axiosInterceptor.request,rejected:axiosInterceptor.requestError});\r\n    //Axios.interceptors.response.handlers.unshift({fulfilled:axiosInterceptor.response,rejected:axiosInterceptor.responseError});\r\n    requestIndex=Axios.interceptors.request.use(axiosInterceptor.request,axiosInterceptor.requestError);\r\n    responseIndex=Axios.interceptors.response.use(axiosInterceptor.response,axiosInterceptor.responseError);\r\n    logger.log(`axios拦截器注册完毕，请求拦截器序号为：${requestIndex}，应答拦截器序号为：${responseIndex}`);\r\n}\r\n\r\n\r\nexport { Axios,init as initAxios}\r\n\r\n", "import VSAuth from \"../index.js\"\r\nimport logger from \"../logger\"\r\n\r\n/*********************\r\n * by cuiliang @ 20200507\r\n * \r\n * 举例：以下是路由定义\r\n *  {\r\n          path: \"路由地址\",\r\n          name: \"路由名称\",\r\n          component:()=> import(\"文件地址\"),\r\n          \r\n          meta: { title: \"页面标题\", permission: 鉴权 }\r\n    },\r\n *  请注意permission鉴权\r\n *\r\n *  鉴权方式：\r\n *  1：支持自定义函数鉴权，\r\n *      a:内置验证登陆，使用checkLogined，路由定义中meta: { title: \"\", permission: checkLogined }\r\n *      b:或者自定义鉴权过程，参数为：\r\n *          to 要跳到的路由地址\r\n *          userName 当前用户用户名\r\n *          userPermission 用户拥有的权限信息\r\n *  2：支持Boolean值\r\n *      a:true 需要鉴权，鉴权过程首先验证用户是否登陆，然后验证前往的地址是否在用户可用的资源列表中\r\n *      b:false 无需鉴权，不做任何权限验证，不验证用户是否登陆，也不验证地址可用性\r\n *  3：支持array值\r\n *      a:明确指定用户拥有某些资源值（资源间为或关系）时可以访问此地址，指定路由的资源值列表reslist，验证用户所拥有资源userResList是否存在于列表resList中,且用户的资源地址是使用此路由地址解析的\r\n *          为了处理两个模块使用同一个路由（动态路由），路由匹配的前提下还需要验证用户拥有的权限是哪个模块的\r\n *      b:空数组[],无权访问\r\n *  \r\n *  特殊情况：\r\n *  1：定义为：meta: { title: \"页面标题\"}，无鉴权定义默认为无需鉴权\r\n * \r\n * \r\n */\r\n\r\n const _403=\"/403\";\r\n \r\n\r\n\r\n/**\r\n * 验证是否有向to的路由跳转的权限，此函数在首次进入页面时可能会\r\n * 1：走两次\r\n *      开发者设定要进入的路由需要鉴权，则首次会走本地鉴权，发现无权限时（此时因前端无用户数据可能造成不准确的情况），将联合后端用户数据再次鉴权，因此会走两次鉴权过程\r\n * 2：走一次\r\n *      开发者设定要进入的路由需要鉴权，且已经经历过其他需要鉴权的路由跳入，因前面的路由已经获取了用户数据，因此只走一次\r\n * @param {Route} to 要切换的路由对象\r\n * @returns {Boolean} 返回鉴权结果，true:有权限;false:无权限\r\n */\r\nfunction checkAccessByRouteTo(to){\r\n    const authInfo = VSAuth.getAuthInfo();\r\n    const userName = authInfo.userName;\r\n    const userPermission = authInfo.permission;\r\n    const isLogined = authInfo.isLogined;\r\n    let permission=to.meta?.permission;\r\n    logger.log(`验证用户是否有向\"${to.path}\"路由跳转的权限,参数to=${to.path},userName=${userName},userPermission=${userPermission}`);\r\n    \r\n    /**\r\n     * 当permission未定义时\r\n     */\r\n    if(typeof(permission)==\"undefined\"||permission==null)\r\n    {\r\n        logger.log(`\"${to.path}\"路由不存在meta或meta.permission，因此无需鉴权`);\r\n        return true\r\n    }\r\n    //自定义验证权限，即开发者在路由定义处使用自定义函数处理鉴权过程\r\n    else if(typeof(permission)===\"function\"){\r\n        logger.log(`\"${to.path}\"路由为自定义函数鉴权，验证开始`);\r\n        let access=false;\r\n        try{\r\n            access=permission(to,userName,userPermission)\r\n        }\r\n        catch(e){\r\n            logger.log(`尝试调用路由鉴权函数失败,原因是${e.message}`);\r\n        }\r\n        return access\r\n        \r\n    }\r\n    //指定路由的资源值列表reslist，验证用户所拥有资源userResList是否存在于列表resList中,且用户的资源地址是使用此路由地址解析的\r\n    else if(permission instanceof Array){\r\n        logger.log(`\"${to.path}\"路由为函数数组(严格模式)鉴权，验证开始`);\r\n        let access=false;\r\n        if(!userName||userName==\"\")\r\n        {\r\n            logger.log(`用户名不存在`);\r\n            return access\r\n        }\r\n        if(permission.length==0){\r\n            logger.log(`用户权限列表为空`);\r\n        }\r\n        for(let resId of permission.values())\r\n        {\r\n            for(let res2 of userPermission.resList.values())\r\n            {\r\n                if(resId==res2.resId&&res2.resPath){\r\n                    //找到用户当前资源的解析路由\r\n                    let routerDef=VSAuth.getConfig().router.match(res2.resPath);\r\n                    //验证当前路由与用户拥有此权限的解析路由是否为一个\r\n                    if(to.path==routerDef.path)\r\n                    {\r\n                        access=true;\r\n                        logger.log(`找到用户拥有的资源编号对应的访问地址与路由的匹配`);\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            if(access) break;\r\n        }\r\n        return access;\r\n    }\r\n    else if(typeof(permission)==\"boolean\"&&permission===true){\r\n        logger.log(`\"${to.path}\"路由为布尔模式(宽松模式)鉴权，验证开始`);\r\n        let access=false;\r\n        if(!userName||userName==\"\") return access;\r\n        for(let res of userPermission.resList.values()){\r\n            if(res.resPath)\r\n            {\r\n                let routerDef=VSAuth.getConfig().router.match(res.resPath);\r\n                if(to.path==routerDef.path){\r\n                    access=true;\r\n                    logger.log(`找到用户拥有访问地址与路由的匹配`);\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        return access;\r\n    }\r\n    else{\r\n        logger.log(`\"${to.path}\"路由定义了未被支持鉴权方式`);\r\n        return true;\r\n    }\r\n    \r\n}\r\n\r\nexport {checkAccessByRouteTo,_403}", "import {getPermission} from \"../modes/common/index.js\"\r\nimport {checkAccessByRouteTo,_403} from \"./router.permission.js\"\r\nimport modesDef from \"../modes/index.js\"\r\nimport VSAuth from \"../index.js\"\r\nimport inner_exception from \"../exception/define.js\";\r\nimport logger from \"../logger\"\r\nimport {Cookie} from \"@vsui/lib-jsext\"\r\n\r\nconst authByDef={\r\n    unknown:{\r\n        auth:function(){\r\n            logger.log(`未知登陆模式无法使用服务器端验证`);\r\n        } \r\n    },\r\n    CAS:{\r\n        auth:authByServer,\r\n    },\r\n    SIAM:{\r\n        auth:authByServer,\r\n    },\r\n    JWT:{\r\n        auth:authByServer,\r\n    },\r\n    SECURITY:{\r\n        auth:authByServer,   \r\n    },\r\n    Other:{\r\n        auth:authByServer,\r\n    },\r\n    DEV:{\r\n        auth:VSDEV,\r\n    }\r\n}\r\n\r\n/**\r\n * 登录模式下的鉴权（含静默登陆初始化用户权限信息）\r\n * @param {Route} to 要跳入的路由地址\r\n * @param {Route} from 来源路由地址\r\n * @param {function(path)} next 下一跳的处理函数next(path)\r\n */\r\nfunction authByServer(to,from,next){\r\n    /************************************************\r\n     * 此过程会在初次鉴权为false的情况后进来，因为初次鉴权有可能没有用户信息，\r\n     * 此时需要依赖获取后端数据后再次进行鉴权，\r\n     * \r\n     * \r\n     * \r\n     * \r\n     ***********************************************/\r\n     const authInfo = VSAuth.getAuthInfo();\r\n     const authConfig=VSAuth.getConfig();\r\n    let username = authInfo.userName;\r\n    //初次鉴权为false，且已经存在用户数据时，此时判定结果准确无误，因此可直接跳至403\r\n    if(username&&username!=\"\"){ \r\n        logger.log(`前端已存在用户登陆信息,无权限直接403`);\r\n        next(_403)\r\n    }\r\n    //初次鉴权为false，但没有用户信息作为依据进行判定，则尝试从服务器获取用户信息进行判定后确定用户是否有权限\r\n    else{\r\n        logger.log(`前端不存在用户登陆信息,尝试从后端VSEAF框架获取用户信息`);\r\n        getPermission().then(({ name, passwd, token, permission, modulesTree })=>{\r\n            \r\n            logger.log(`从后端VSEAF框架获取用户信息正常完成,已获取用户信息与用户权限`);\r\n            _innerAuth(to,from,next)\r\n        }).catch(err=>{\r\n            logger.log(`获取用户信息的请求被异常中断,错误编号:${err.code},错误信息:${err.message}`);\r\n            //服务器端获取用户信息失败,则视为服务器端没有用户会话\r\n            if(err.code==inner_exception.getUserInfo_serverReturnFailed)\r\n            {\r\n                logger.log(`根据错误编号判定为用户未登录,跳转入登陆界面`);\r\n                //因为在axios过程中已经验证了登陆模式是否存在了，这里不再验证\r\n                modesDef[authConfig.authMode].toLoginPage();\r\n            }\r\n            else if(err.code==inner_exception.axios_watchUnlogin.code)\r\n            {\r\n                logger.log(`截获异常代码：${inner_exception.axios_watchUnlogin.code},表示AXIOS拦截器已经处理了返回数据验证为未登录情况下的登陆跳转,此处未登陆异常不被处理`);\r\n            }\r\n            else if(err.code==inner_exception.getUserInfo_customGetErr.code)\r\n            {\r\n                logger.log(`截获异常代码：${inner_exception.getUserInfo_customGetErr.code},表示自定义获取用户过程出现了错误，您如果能看到此信息，说明用户未登录，且您没有在请求的返回数据中带上610标记，因此该未登陆异常无法被处理`);\r\n            }\r\n            else{\r\n                logger.log(`根据错误编号判定为请求过程网络或服务器端存在问题,请排查此情况`);\r\n            }\r\n        })\r\n    }\r\n}\r\n\r\n/**\r\n * 开发模式下模式下的鉴权（含静默登陆初始化用户权限信息）\r\n * @param {Route} to 要跳入的路由地址\r\n * @param {Route} from 来源路由地址\r\n * @param {function(path)} next 下一跳的处理函数next(path)\r\n */\r\nfunction VSDEV(to,from,next){\r\n    const authConfig=VSAuth.getConfig();\r\n    let username = authConfig.store.getters[`${authConfig.storeName}/getLoginUserName`],password=\"\";\r\n    //已登录（非页面刷新模式进入）,验证用户登陆后直接鉴权\r\n    if(username!=\"\"){\r\n        _innerAuth(to,from,next);\r\n    }\r\n    //页面刷新进入或直接url进入,尝试静默登陆,再做鉴权\r\n    else\r\n    {\r\n        username=Cookie.Cookie.getCookie(Cookie.CookieDef.userName,Cookie.CookieDef.domain,Cookie.CookieDef.path)\r\n        password=Cookie.Cookie.getCookie(Cookie.CookieDef.passwd,Cookie.CookieDef.domain,Cookie.CookieDef.path)\r\n        if(username==\"\"||password==\"\"){\r\n            VSAuth.getAuthInfo().auth.toLoginPage();\r\n            return;\r\n        }\r\n        modesDef[authConfig.authMode].login(username,password).then(loginData=>{\r\n            _innerAuth(to,from,next);\r\n        }).catch(err=>{\r\n            logger.log(`[vsdev模式]${err.message}`);\r\n        })\r\n        \r\n    }\r\n}\r\n\r\n\r\nfunction _innerAuth(to,from,next)\r\n{\r\n    if(!checkAccessByRouteTo(to)){\r\n        logger.log(`再次鉴权验证结果[无权访问],%c跳至403页面`,\"color:#F02D2D;\");\r\n        next(_403)\r\n    }\r\n    else{\r\n        logger.log(`再次鉴权验证结果[有权访问],%c跳至${to.path}页面%c`,\"color:#0f0;\");\r\n        next();\r\n    }\r\n}\r\n\r\nexport {authByDef};", "/**\r\n * POWER by vue-router ^3.1.3\r\n * 路由拦截器，可在路由跳转过程中进行拦截处理，\r\n * Created in 20200118 by cuiliang 崔良于2020年1月18日，公司18周年纪念\r\n *\r\n *\r\n */\r\n\r\nimport logger from \"../logger\"\r\nimport {authInterceptor} from \"./router.auth.js\";\r\n\r\n\r\n//使用钩子函数对路由进行权限跳转\r\nconst routesInterceptor={\r\n  //使用钩子函数对路由进行权限跳转\r\n  beforeEach:function(to, from, next){\r\n    logger.log(`%c!!!进入权限组件路由拦截器[\"${to.path}\"]%c，正常鉴权后会输出 %c!!!离开权限组件路由拦截器[\"${to.path}\"]%c ，如未输出此文字，可能有其他拦截器二次处理`,\r\n                \"color:#0f0;\",\r\n                \"\",\r\n                \"color:#0f0;\",\r\n                \"\");\r\n    try{\r\n      authInterceptor(to,from,next);\r\n    }\r\n    catch(e)\r\n    {\r\n      logger.log(`权限组件路由拦截器异常,错误信息为${e.message},跳转入登陆页面`)\r\n    }\r\n  },\r\n  afterEach:function(transition) {\r\n    logger.log(`%c!!!离开权限组件路由拦截器[\"${transition.path}\"]`,\r\n              \"color:#0f0;\",\r\n              );\r\n  },\r\n}\r\n\r\n\r\n\r\nexport default routesInterceptor;", "import {authByDef} from \"./router.authby.define\";\r\nimport {checkAccessByRouteTo,checkLogined} from \"./router.permission.js\";\r\nimport VSAuth from \"../index.js\"\r\nimport logger from \"../logger\"\r\n\r\n\r\n\r\nfunction authInterceptor(to, from, next){\r\n    //进行路由鉴权，不判断用户是否登陆，因为有些页面无需登录\r\n    logger.log(`初次鉴权验证开始`);\r\n    let access=checkAccessByRouteTo(to);\r\n    logger.log(`初次鉴权验证结果${access?\"[允许访问]\":\"[无权访问]\"}`);\r\n    if(access){\r\n        logger.log(`跳入${to.path}页面开始[authInterceptor]`);\r\n        next();\r\n        logger.log(`跳入${to.path}页面,如本过程完毕未跳入正常页面，请检查是否有其他拦截器存在[authInterceptor]`);\r\n    }\r\n    else{\r\n        logger.log(`初次鉴权无权限可能是没有用户数据造成，将尝试使用鉴权模式对应的服务端鉴权再次鉴权！`);\r\n        let defaultAuthMode=\"unknown\";\r\n        if(VSAuth.getConfig().authMode==defaultAuthMode)\r\n        {\r\n            logger.log(`服务器端未告知鉴权模式，组件正在等待服务器返回鉴权模式`);\r\n            logger.log(`无权限访问情况下，尝试使用服务器端鉴权模式进行再次验证`);\r\n            logger.log(`当前鉴权模式为${VSAuth.getConfig().authMode},等待服务器端返回的正确模式`);\r\n            let elapsedTime=0\r\n            let timer=window.setInterval(()=>{\r\n                elapsedTime+=5;\r\n                if(VSAuth.getConfig().authMode!=defaultAuthMode)\r\n                {\r\n                    logger.log(`经过${elapsedTime}毫秒后，获取到服务端返回的鉴权模式为：${VSAuth.getConfig().authMode}`);\r\n                    clearInterval(timer);\r\n                    if(authByDef[VSAuth.getConfig().authMode])\r\n                    {\r\n                        logger.log(`存在对${VSAuth.getConfig().authMode}鉴权模式指定的鉴权方式，将使用此类型指定的服务端鉴权方式`);\r\n                        authByDef[VSAuth.getConfig().authMode].auth(to, from, next);\r\n                    }\r\n                    else{\r\n                        logger.log(`不存在对${VSAuth.getConfig().authMode}鉴权模式指定的鉴权方式，将使用内置通用的服务端鉴权方式`);\r\n                        authByDef.Other.auth(to, from, next);\r\n                    }\r\n                    \r\n                }\r\n            },5)\r\n        }\r\n        else{\r\n            logger.log(`服务器端已告知鉴权模式，组件将验证该鉴权模式的服务端鉴权方式是否存在`);\r\n            if(authByDef[VSAuth.getConfig().authMode])\r\n            {\r\n                logger.log(`存在对${VSAuth.getConfig().authMode}鉴权模式指定的服务端鉴权方式，将使用此类型指定的服务端鉴权方式`);\r\n                authByDef[VSAuth.getConfig().authMode].auth(to, from, next);\r\n            }\r\n            else{\r\n                logger.log(`不存在对${VSAuth.getConfig().authMode}鉴权模式指定的服务端鉴权方式，将使用内置通用的服务端鉴权方式`);\r\n                authByDef.Other.auth(to, from, next);\r\n            }\r\n            \r\n        }\r\n            \r\n       \r\n        \r\n    }\r\n}\r\n\r\n\r\nexport {authInterceptor};", "import routesInterceptor from \"./router.interceptor.js\";\r\nimport logger from \"../logger\";\r\n\r\n\r\n\r\nlet Router;\r\n\r\nfunction init(router){\r\n    Router=router;\r\n    Router.beforeEach(routesInterceptor.beforeEach);\r\n    Router.afterEach(routesInterceptor.afterEach);\r\n    logger.log(`路由拦截器注册完毕`);\r\n}\r\n\r\nexport {Router,init as initRouter}\r\n", "\r\n\r\nexport default\r\n{\r\n    strict: process.env.NODE_ENV !== 'production',\r\n    /**\r\n     * getters访问：getters['APP/isAdmin']\r\n     * actions访问：dispatch('APP/login')\r\n     * mutations访问：commit('APP/login')\r\n     */\r\n    namespaced: true,\r\n    state:{\r\n\r\n      /**\r\n       * 是否登陆\r\n       */\r\n      isLogined:false,\r\n      /**\r\n       * 登录信息 \r\n       */\r\n      userInfo:{userName:\"\",realName:\"\",passwd:\"\",token:\"\",permission:{},modulesTree:[]},\r\n      \r\n    },\r\n    actions:{\r\n      \r\n\r\n    },\r\n    mutations:{\r\n\r\n      \r\n\r\n     /**\r\n      * 请在登录后调用此函数进行全局账户信息初始化\r\n      * 在VUE对象内使用this.$store.commit('APP/login',userName,userPasswd,permission,ownModulesTree)访问本函数\r\n      * 该函中第一个参数作为this的上下文自动传入，不用显示传入\r\n      * @param {string} userName  用户账号\r\n      * @param {string} 用户密码（加密后的）\r\n      * @param {[]} permission 数据的userMap节点\r\n      * @param {[]} ownModulesTree 对userMap节点进行convertDataToModuleTree转换后的模块树\r\n      * \r\n      */\r\n      login:function(state,userinfo)\r\n      {\r\n        state.isLogined=true;\r\n        // console.log(`APP-Store内部接收到ownModulesTree为:${JSON.stringify(userinfo.modulesTree)}`)\r\n        // console.log(`userName=${userinfo.name}`)\r\n        state.userInfo=userinfo;\r\n      },\r\n      \r\n      /**\r\n       * 用户退出，会清除userinfo信息，并重置isLogined=false;\r\n       * 在VUE对象内使用this.$store.commit('APP/logOut')访问本函数\r\n       * 该函中第一个参数作为this的上下文自动传入，不用显示传入\r\n       */\r\n      logOut:function(state)\r\n      {\r\n        state.isLogined=false;\r\n        state.userInfo={userName:\"\",realName:\"\",passwd:\"\",token:\"\",permission:{},modulesTree:[]};\r\n      },\r\n      \r\n      \r\n\r\n    },\r\n    getters:\r\n    {\r\n      \r\n      /**\r\n       * 获取登陆用户名，返回用户名或空\r\n       * 在VUE对象内使用this.$store.getters['APP/getLoginUserName']访问该函数\r\n       */\r\n      getLoginUserName:(state,getters,rootState,rootGetters)=>{return state.userInfo.userName},\r\n      /**\r\n       * 获取登陆用户的真名，返回真名或空\r\n       * 在VUE对象内使用this.$store.getters['APP/getLoginUserRealName']访问该函数\r\n       */\r\n       getLoginUserRealName:(state,getters,rootState,rootGetters)=>{return state.userInfo.realName},\r\n      /**\r\n       * 获取userInfo.permission\r\n       * 在VUE对象内使用this.$store.getters['APP/getUserInfoPermission']访问该函数\r\n       */\r\n      getUserInfoPermission:state=>{return state.userInfo.permission},\r\n      /**\r\n       * 获取登陆用户token，返回用户token或空\r\n       * 在VUE对象内使用this.$store.getters['APP/getLoginUserToken']访问该函数\r\n       */\r\n      getLoginUserToken:state=>{return state.userInfo.token},\r\n     \r\n      /**\r\n       * 获取登陆用户的可用资源树,\r\n       * 在VUE对象内使用this.$store.getters['APP/getModulesTree']访问该函数\r\n       */\r\n      getModulesTree:state=>{\r\n        return state.userInfo.modulesTree\r\n      },\r\n\r\n\r\n      \r\n           \r\n    }\r\n  }", "import authStore from \"./store.auth.js\";\r\n\r\nlet Store;\r\n\r\n\r\nfunction init(store){\r\n    Store=store;\r\n    let storeName=\"vsui-auth4-\";\r\n    let hash=new Date().getTime();\r\n    storeName=storeName+hash;\r\n    store.registerModule(storeName,authStore);\r\n    return storeName;\r\n}\r\n\r\n\r\nexport { Store,init as initStore}", "import {initRouter} from \"./router/index.js\";\r\nimport {initAxios} from \"./axios/index.js\";\r\nimport {initStore} from \"./store/index.js\";\r\nimport modesDef from \"./modes/index.js\";\r\nimport restDef from \"./rest/config/index.js\"\r\nimport {getAuthMode} from \"./modes/common/index.js\";\r\nimport logger from \"./logger\";\r\n\r\nlet AuthConfig={};\r\n\r\nconst Auth={\r\n    \r\n     init:async function(options)\r\n     {\r\n        const { router,store,axios, whereVSEAF,isDev,DEBUG,customCfg} = options;\r\n        const detail=options[customCfg]||undefined;\r\n        try{\r\n            AuthConfig={\r\n                router,\r\n                store,\r\n                axios,\r\n                whereVSEAF,\r\n                DEBUG,\r\n                isDev,\r\n            };\r\n            AuthConfig.customCfg=customCfg;\r\n            if(AuthConfig.customCfg) {\r\n                AuthConfig[AuthConfig.customCfg] = detail;\r\n            }\r\n            AuthConfig.DEBUG=DEBUG||false;\r\n            AuthConfig.DEBUG?logger.log(`控制台前缀为：${logger.prefix}，组件支持SECURITY、CAS、SIAM、DEV四种登陆模式，如登陆模式为其他，请访问VSNPM并查询关键字：@vsui/lib-vsui-auth4-vseaf-plugins`):\"\";\r\n\r\n            AuthConfig.isDev=isDev||false;\r\n            AuthConfig.authMode=AuthConfig.isDev?\"DEV\":\"unknown\";\r\n            restDef.initBasePath({basepath:AuthConfig.whereVSEAF,userInfoPath:AuthConfig[AuthConfig.customCfg]?.userInfo?.apiPath});\r\n            initRouter(router);\r\n            initAxios(axios);\r\n            if(!AuthConfig.isDev)\r\n            {\r\n                \r\n                getAuthMode().then(authMode=>{\r\n                    AuthConfig.authMode=authMode.toUpperCase();\r\n                }).catch(err=>{\r\n                    AuthConfig.DEBUG?logger.log(`初始化过程中，获取登陆模式有误，异常为：${err.message}}`):\"\";\r\n                })\r\n            }\r\n            AuthConfig.storeName=initStore(store);\r\n            AuthConfig.DEBUG?logger.log(\"初始化鉴权模块完毕！\"):\"\";\r\n            return true;\r\n        }\r\n        catch(e){\r\n            throw (\"初始化鉴权模块出错：\"+e.message);\r\n        }\r\n        \r\n    },\r\n    getConfig(){\r\n        return AuthConfig;\r\n    },\r\n    getAuthInfo()\r\n    {\r\n        \r\n        let info={\r\n            isLogined:(AuthConfig.store.getters[`${AuthConfig.storeName}/getLoginUserName`]==\"\"||\r\n            AuthConfig.store.getters[`${AuthConfig.storeName}/getLoginUserName`]==null)?false:true,\r\n            userName:AuthConfig.store.getters[`${AuthConfig.storeName}/getLoginUserName`],\r\n            realName:AuthConfig.store.getters[`${AuthConfig.storeName}/getLoginUserRealName`],\r\n            permission:AuthConfig.store.getters[`${AuthConfig.storeName}/getUserInfoPermission`],\r\n            modulesTree:AuthConfig.store.getters[`${AuthConfig.storeName}/getModulesTree`],\r\n            token:AuthConfig.store.getters[`${AuthConfig.storeName}/getLoginUserToken`],\r\n            auth:modesDef[AuthConfig.authMode],\r\n            authMode:AuthConfig.authMode,\r\n            storeName:AuthConfig.storeName,\r\n        };\r\n        return info;\r\n    },\r\n    /**\r\n     * 获取内置鉴权模式，可对内置鉴权模式进行扩展，扩展时实现三个函数:\r\n     *  function toLoginPage(),\r\n     *  Promise function login(userName, passWord),\r\n     *  Promise function logout(),\r\n     *  例如：如扩展服务端名为Auth1的鉴权方式：\r\n     *  Auth.getAuthDefine().Auth1={\r\n     *    toLoginPage(){todo},\r\n     *    login(userName, passWord){return new Promise((resolve,reject)=>{todo});},\r\n     *    logout(){return new Promise((resolve,reject)=>{todo});}\r\n     *  }\r\n     */\r\n    getAuthDefine(){\r\n        return modesDef;\r\n    }\r\n    \r\n\r\n}\r\n\r\n\r\n\r\nexport default Auth;"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "this", "__WEBPACK_EXTERNAL_MODULE__35__", "__WEBPACK_EXTERNAL_MODULE__558__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "prefix", "prefix_style", "log", "DEBUG", "args", "Array", "from", "arguments", "msg", "newArgs", "splice", "concat", "console", "apply", "convertDataToModuleTree", "list", "length", "JSON", "parse", "stringify", "treeListMap", "item", "resId", "i", "resPid", "children", "push", "code", "message", "config", "initBasePath", "basepath", "BasePath", "RestFulAPI", "Interface", "Encrypt", "RSA", "getPublicKeyAddr", "AuthInfo", "authMode", "getSecureType", "userInfo", "getCurrentUser", "DEV", "getLoginAddr", "CAS", "getLogoutAddr", "SIAM", "SECURITY", "OAuth2", "JWT", "unknown", "login", "Promise", "resolve", "reject", "logout", "toLoginPage", "url", "window", "location", "href", "baseurl", "protocol", "host", "loginURL", "replace", "format", "encodeURIComponent", "whereVSEAF", "router", "options", "history", "base", "logoutURL", "err", "userName", "passWord", "logger", "publicKeyURL", "A<PERSON>os", "then", "resp", "data", "meta", "status", "success", "encrypt_getPublicKey_serverReturnFailed", "public<PERSON>ey", "catch", "getRSAPublicKey", "publickey", "encrypt", "setPublicKey", "post", "login_usernameorpwdErr", "authConfig", "logout_serverReturnFailed", "store", "commit", "storeName", "Error", "realName", "passwd", "token", "permission", "modulesTree", "resList", "loginData", "error", "axiosInterceptor", "request", "headers", "requestError", "response", "modes", "authMode_unRegisteredModeWhenToLoginPage", "responseError", "requestIndex", "responseIndex", "_403", "checkAccessByRouteTo", "to", "authInfo", "userPermission", "isLogined", "path", "access", "e", "values", "res2", "resPath", "routerDef", "match", "res", "authByDef", "auth", "authByServer", "Other", "next", "username", "getters", "password", "_innerAuth", "<PERSON><PERSON>", "_getUserInfo", "customCfg", "getUserInfo", "String", "raw", "userData", "getUserInfo_customGetErr", "getUserInfo_convertorModuleTreeFailed", "getUserInfo_serverReturnFailed", "getUserInfo_netWorkErr", "getPermission", "name", "beforeEach", "defaultAuthMode", "elapsedTime", "timer", "setInterval", "clearInterval", "authInterceptor", "after<PERSON>ach", "transition", "Router", "strict", "namespaced", "state", "actions", "mutations", "userinfo", "logOut", "getLoginUserName", "rootState", "rootGetters", "getLoginUserRealName", "getUserInfoPermission", "getLoginUserToken", "getModulesTree", "Store", "AuthConfig", "init", "async", "axios", "isDev", "detail", "userInfoPath", "<PERSON><PERSON><PERSON><PERSON>", "interceptors", "use", "secureTypeURL", "getAuthMode_serverReturnFailed", "secureType", "getAuthMode", "toUpperCase", "Date", "getTime", "registerModule", "getConfig", "getAuthInfo", "getAuthDefine"], "sourceRoot": ""}