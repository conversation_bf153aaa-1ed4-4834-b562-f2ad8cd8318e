<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <div class="title-text">{{ modelValue.BT }}股权穿透分析清单</div>
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="YGSMC">
            <el-input ref="input45296" placeholder="请输入原公司名称" v-model="listQuery.YGSMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CTGSMC">
            <el-input ref="input45296" placeholder="请输入穿透公司名称" v-model="listQuery.CTGSMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              过滤
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(500px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="YGSMC" label="原公司名称" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CTGSMC" label="穿透公司名称" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column label="股权关系" align="center">
                <el-table-column prop="LJ" label="路径" align="center"
                                 :show-overflow-tooltip="true" width="100"></el-table-column>
                <el-table-column prop="KG" label="控股" align="center"
                                 :show-overflow-tooltip="true" width="100"></el-table-column>
              </el-table-column>
              <el-table-column prop="RZGX" label="任职关系" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="GQRZGX" label="股权任职关系" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="120" fixed="right">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">结果查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="股权穿透查重详情"
        z-index="1000"
        top="5vh"
        width="1600px">
      <div>
        <bsccdbxqView v-if="dialogVisible" :params="xqParams" @close="dialogVisible=false"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import bsccdbxqView from "@views/gqct/gqctgl/gqctglChartXqView";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,bsccdbxqView},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: props.params.editable,
      GQCTJLID: props.params.id,
      FXQDID: props.params.FXQDID,
      XMID: props.params.xmid,
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      xqParams: {},
      dialogVisible: false,
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        GQCTJLID: state.GQCTJLID,
        FXQDID: state.FXQDID,
      }
      axiosUtil.get('/backend/gqct/gqctgl/getGqctglSecondList', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const viewRow = (row) => {
      state.xqParams = {editable: false, id: row.GQCTJLID, FXQDID: row.FXQDID, operation: 'view',YGSMC:row.YGSMC,CTGSMC:row.CTGSMC,GQGX:row.LJ,RZGX:row.RZGX,GQRZGX:row.GQRZGX}
      state.dialogVisible = true
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      viewRow

    }
  }

})
</script>

<style scoped>
.title-text {
  font-size: 20px;
  margin-bottom: 40px;
  width: 100%;
  text-align: center;
  color: #980404;
}
</style>
