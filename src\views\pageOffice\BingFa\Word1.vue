<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');
const open_params = ref('');
function OnPageOfficeCtrlInit() {
    // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
    pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function AfterDocumentOpened() {
    // PageOffice的文档打开后事件回调函数
    //当前事件如果正确触发了，则说明当前文件已正确在线打开，则需要更新数据库字段的editor的值为""
    const id = open_params.value.id;
    const userName = open_params.value.userName;
    updateEditorById(id,userName);
}

function OnBeforeBrowserClosed(){
    // 此处可以执行窗口关闭前需要执行的业务逻辑代码
    //关闭前根据id重新修改数据库字段的editor的值为空
    const id = open_params.value.id;
    const userName = "";
    updateEditorById(id,userName);

    pageofficectrl.CloseWindow(true);//必须。否则窗口不会关闭。
}
function Save() {
    pageofficectrl.SaveFilePage = "/BingFa/save?fileName=" + open_params.value.fileName;
    // 在这里写您保存前的代码
    pageofficectrl.WebSave();
    // 在这里写您保存后的代码，比如判断保存结果pageofficectrl.CustomSaveResult
}

function openFile() {
    // 发起GET请求到后端Controller的路由
    return request({
        url: '/BingFa/Word1',
        method: 'get',
        params: open_params.value
    });
}

async function updateEditorById(id,userName) {
    // 发起PUT请求到后端Controller的路由
    const response = await request({
        url: '/BingFa/updateEditorById',
        method: 'put',
        params: {
            id: id,
            userName: userName,
            // 其他需要更新的字段...
        }
    });
    if (response > 0) {
        console.log("数据库中editor字段更新成功！");
    }
}

onMounted(() => {
    //获取openWindow方法第三个参数的值并且转成JSON对象
    open_params.value = JSON.parse(pageofficectrl.WindowParams);
    // 请求后端打开文件
    openFile().then(response => {
        poHtmlCode.value = response;
    });
    //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
    window.POPageMounted = { OnPageOfficeCtrlInit, AfterDocumentOpened, Save,OnBeforeBrowserClosed};//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
</template>