<!-- 评委单项目评价-编辑页 -->
<template>
    <div>
        <el-form :model="formData" ref="vForm" :rules="rules" label-position="right"
            label-width="110px" size="default" status-icon>
            <el-row :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <el-form-item label=" 项目名称:" label-width="100px" prop="XMMC">
                        <!-- <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable
                            :disabled="editable == false"></el-input> -->
                        <span>{{formData.XMMC}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="12">
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="项目类别:" prop="XMLB">
                        <!-- <el-select v-model="formData.XMLB" class="full-width-input" clearable :disabled="editable == false">
                            <el-option v-for="(item, index) in XMLBOptions" :key="index" :label="item.DMMC"
                                :value="item.DMXX"></el-option>
                        </el-select> -->
                        <span>{{formData.XMLB}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="所属单位:" prop="SSDW">
                        <!-- <el-select v-model="formData.SSDW" class="full-width-input" clearable :disabled="editable == false">
                            <el-option v-for="(item, index) in SSDWOptions" :key="index" :label="item.DMMC"
                                :value="item.DMXX"></el-option>
                        </el-select> -->
                        <span>{{formData.SSDW}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="项目编码:" label-width="110px" prop="XMBM">
                        <!-- <el-input v-model="formData.XMBM" type="text" placeholder="请输入"
                            :disabled="editable == false"></el-input> -->
                        <span>{{formData.XMBM}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="12">
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="评审时间:" prop="PBSJ">
                        <span>{{ dateFormat(formData.PBSJ)}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="评审地点:" prop="PBDD">
                        <span>{{formData.PBDD}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <div class="zdTitle">评委明细</div>
                <el-table  ref="datatable91634" :data="tableData"  :border="true" :show-summary="false"
                    size="default" :stripe="false" :highlight-current-row="true" :cell-style="{ padding: '10px 0 ' }">
                    <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
                    <el-table-column prop="ZJXM" label="专家姓名" align="center" min-width="160"></el-table-column>

                    <el-table-column v-for="item in ZJPJDMList" :key="item" :prop="item.DMXX" header-align="center" align="center" :label="item.DMMC" width="100">
                            <template v-if="item.BYZD1 == 'select'" #default="scope">
                                <el-select v-model="scope.row[item.DMXX]"  placeholder="请选择" :disabled="!editable" clearable>
                                    <el-option v-for="item in JSON.parse(item.BYZD3)" :key="item.CBM.trim()" :label="item.CV" :value="item.CBM.trim()">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-else #default="scope">
                                <el-input v-model="scope.row[item.DMXX]" :disabled="!editable" type="text" placeholder="请输入" clearable></el-input>
                            </template>

					</el-table-column>
                    <!-- ZJPJDMList -->


                    <!-- <el-table-column prop="KQ" label="考勤" align="center" min-width="100">
                        <template #default="scope">
                            <el-select v-model="scope.row.KQ" placeholder="请选择" clearable v-if="editable != false">
                                <el-option v-for="item in kqOptions" :key="item.DMXX" :label="item.DMMC" :value="item.DMXX">
                                </el-option>
                            </el-select>
                            <span v-else>{{ scope.row.KQ }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="ZL" label="质量" align="center" min-width="100">

                    </el-table-column>
                    <el-table-column prop="JL" label="纪律" align="center" min-width="100">
                        <template #default="scope">
                            <el-input v-if="editable != false" v-model="scope.row.JL" type="text" placeholder="请输入"
                                clearable></el-input>
                            <span v-else>{{ scope.row.TJ }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="GZTD" label="工作态度" align="center" min-width="100">
                        <template #default="scope">
                            <el-select v-model="scope.row.GZTD" placeholder="请选择" clearable v-if="editable != false">
                                <el-option v-for="item in gztdOptions" :key="item.DMXX" :label="item.DMMC"
                                    :value="item.DMXX">
                                </el-option>
                            </el-select>
                            <span v-else>{{ scope.row.GZTD }}</span>
                        </template>
                    </el-table-column> -->
                    <el-table-column prop="MXBZ" label="备注" align="center" min-width="100">
                        <template #default="scope">
                            <el-input v-model="scope.row.MXBZ" :disabled="!editable" type="text" placeholder="请输入" clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <el-row>
                <div class="zdTitle">业务资料</div>
              <VsFileUploadTable style="width: 100%;height: 300px" YWLX="PWPJYWZL" :key="params.id" :busId="params.id" v-model:fileTableData="fileTableData" viewType="YL" :editable="editable"/>

              <!-- <el-col :span="24" class="grid-cell">
                  <div class="container-wrapper">
                  <el-table ref="dataTable1" :data="ckywzlData" height="200px"
                      :style="{width: '100%'}" :border="true" :show-summary="false" size="small" :stripe="true"
                      :highlight-current-row="true" :cell-style="{padding: '5px 0 '}">
                      <el-table-column v-if="true" prop="LX" label="类型" fixed="left" align="left"
                      :formatter="formatterValue" :show-overflow-tooltip="true"><template
                          #default="scope"><span>{{scope.row['LX']}}:</span>
                            <span style="color:#909399;margin-left:20px">可以上传多个文件</span>
                            </template></el-table-column>
                        <el-table-column fixed="right" class-name="data-table-buttons-column" align="'center'" label="操作"
                        min-width="50%">
                        <template #default="scope">
                            <vsfileupload :busId="scope.row.id" :key="scope.row.id" ywlb="ZJCKFJ" busType="zjcrkxx" :limit="100" :editable="editable" v-model:files="formData.fileList"></vsfileupload>
                        </template>
                        </el-table-column>
                    </el-table>
                    </div>
                </el-col> -->
            </el-row>
        </el-form>
        <div style="width: 100%;display: flex;justify-content: center;">
            <el-row :gutter="12" style="width: 300px;">
                <el-col v-if="editable" :span="8">
                    <el-button type="success" @click="validateForm('0')">暂存</el-button>
                </el-col>
                <el-col v-if="editable" :span="8">
                    <el-button type="primary" @click="validateForm('1')">提交</el-button>
                </el-col>
                <el-col :span="8">
                    <el-button @click="closeForm()">返回</el-button>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script>
import {
    defineComponent,
    toRefs,
    reactive,
    getCurrentInstance,
    onMounted
}
    from 'vue'
import axiosUtil from "../../../lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth.js";
import comFun from "../../../lib/comFun";
import { ElMessage } from 'element-plus'
import Vsfileupload from "../../components/vsfileupload";
import VsFileUploadTable from "../../components/vsFileUploadTable";
export default defineComponent({
    components: { Vsfileupload ,VsFileUploadTable},
    props: {
        params: {
            type: Object,
            required: true
        }
    },

    setup(props, context) {
        const state = reactive({
            editable: props.params.editable,
            operation: props.params.operation,
            id: props.params.id,
            XMBS: props.params.XMBS,
            HYBS: props.params.HYBS,

            userInfo: vsAuth.getAuthInfo().permission,
            formData: {
                XMLB: '',
                SSDW: '',
                XMBM: '',
                XMMC: '',
                PBSJ: '',
                PBDD: '',
            },
            pwpj: {
                    PWPJBS: props.params.id, // 评委评价标识
                    PBHYBS: props.params.HYBS, // 评标会议标识
                    PJLX:'', // 评价类型(中介机构对专家)；(招标人对专家）
                    BZ:'', // 备注
                    CJR:'', // 创建人
                    CJSJ:'', // 创建时间
                    XGR:'', // 修改人
                    XGSJ:'', // 修改时间
                    SHR:'', // 审核人
                    SHRQ:'', // 审核日期
                    SHZT:'', // 审核状态：0保存；1提交；2审核通过；
                    JGDM:'', // 机构代码
                    PSDXBS: props.params.XMBS, // 评审对象标识
                },
            tableData:[],
            tableDataMx:[],
            rules: {

            },

            ywzlData:[
                {
                    LX:'相关附件',
                    WJMC: '可以上传多个文件'
                }
            ],
            XMLBOptions: [],
            SSDWOptions: [],
            ZJPJDMList: [],//动态字段
          fileTableData:[]
        })

    /**日期格式化 */
    const dateFormat = (time) =>{
        if(time){
            return new Date(time).format('yyyy-MM-dd')
        }
        return ''
    }

    /**从代码表获取码值 */
    const getDMBData = async (DMLBID, resList) => {
        console.log(resList);
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }

    // 项目会议信息
    const getFormData =  async() => {
        let listQuery = { }
        if (state.operation == 'edit') {
            listQuery.id = state.id
        }else{
            listQuery.XMBS = state.XMBS
            listQuery.HYBS = state.HYBS
        }
        let res = await axiosUtil.get('/backend/zjgl/pwpb/pwpjList', listQuery)
        console.log("formData",res);
        if (res.data) {
            state.formData = res.data.list[0]
        }
    }

    //评价列表
    const getDataList = async () => {
        let listQuery = {}
        if (state.operation !== 'add') {
            listQuery.id = state.id
        }else{
            listQuery.XMBS = state.XMBS
            listQuery.HYBS = state.HYBS
        }
        let res = await axiosUtil.get('/backend/zjgl/pwpb/pwpjMxList', listQuery)
        console.log(" res.data", res.data);
        state.tableDataMx = res.data
        // state.tableDataMx =[{PWPJMXBS:'123',PFBZBM:'KQ',PJBZJG:'1'},{PWPJMXBS:'123',PFBZBM:'ZL',PJBZJG:'2'},{PWPJMXBS:'123',PFBZBM:'JL',PJBZJG:'66'},{PWPJMXBS:'234',PFBZBM:'KQ',PJBZJG:'5'},{PWPJMXBS:'234',PFBZBM:'ZL',PJBZJG:'6'}]
    }


    const transData1 = () =>{
        let temp = {};
        let pjmxList = [];
        if(state.tableDataMx&&state.tableDataMx.length>0){
            for(var i=0;i<state.tableDataMx.length;i++){
                if (!state.tableDataMx[i].PWPJBS) { // 没有评价标识，第一次打分
                    state.tableDataMx[i].PWPJBS = state.id
                }
                if (!state.tableDataMx[i].PWPJMXBS) { // 没有明细标识，第一次打分
                    state.tableDataMx[i].PWPJMXBS = comFun.newId()
                }
                if(!temp[state.tableDataMx[i].PWPJMXBS]){//去重取明细标识数组
                    temp[state.tableDataMx[i].PWPJMXBS] = 'already'
                    pjmxList.push(state.tableDataMx[i].PWPJMXBS)
                    state.tableData.push({PWPJBS: state.tableDataMx[i].PWPJBS,PWPJMXBS: state.tableDataMx[i].PWPJMXBS,PBPWBS: state.tableDataMx[i].PBPWBS,ZJXM: state.tableDataMx[i].ZJXM, MXBZ: state.tableDataMx[i].MXBZ})
                }
            }
            console.log('1111===',pjmxList);
            for(var i=0;i<state.tableData.length;i++){
                for(var j=0;j<state.tableDataMx.length;j++){
                    if(state.tableData[i].PWPJMXBS==state.tableDataMx[j].PWPJMXBS){
                        if(state.tableDataMx[j].PFBZBM){
                            state.tableData[i][state.tableDataMx[j].PFBZBM]=state.tableDataMx[j].PJBZJG
                        }
                    }
                }
            }
        }
    }
    //列转行
    const transData2 = () =>{
            var tableDataMx=[];
            for(var i=0;i<state.tableData.length;i++){
                for(var j=0;j<state.ZJPJDMList.length;j++){
                    var aa={
                        PWPJDFBS:comFun.newId(),
                        PWPJMXBS:state.tableData[i].PWPJMXBS?state.tableData[i].PWPJMXBS:comFun.newId,
                            PFBZBM:state.ZJPJDMList[j].DMXX,
                            PFBZMC:state.ZJPJDMList[j].DMMC,
                            PJBZJG:state.tableData[i][state.ZJPJDMList[j].DMXX]
                    }
                   tableDataMx.push(aa);
                }
            }
            // console.log("tableDataMx",tableDataMx);
            state.tableDataMx = tableDataMx
    }

    const instance = getCurrentInstance()

        const download = () => {

        }
    const closeForm = () => {
        context.emit("closeForm")
    }

    const formatDate = (param) => {
      let date = new Date(param);
      let Y = date.getFullYear() + '-';
      let M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-';
      let D = date.getDate() < 10 ? '0' + date.getDate() + ' ' : date.getDate() + ' ';
      let h = date.getHours() < 10 ? '0' + date.getHours() + ':' : date.getHours() + ':';
      let m = date.getMinutes() < 10 ? '0' + date.getMinutes() + ':' : date.getMinutes() + ':';
      let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
      return Y + M + D + h + m + s;
    }

    const validateForm = (type) => {
        instance.proxy.$refs['vForm'].validate(valid => {
            if (valid) {
                transData2();
                state.pwpj.CJR = state.userInfo.userLoginName
                state.pwpj.CJSJ = formatDate(new Date);
                state.pwpj.XGR = state.userInfo.userLoginName
                state.pwpj.XGSJ = formatDate(new Date);
                state.pwpj.SHR = state.userInfo.userLoginName
                state.pwpj.SHRQ = formatDate(new Date);
                state.pwpj.SHZT = type;
                const params = {
                    pwpj: state.pwpj,
                    pwpjMx: state.tableData,
                    pwpjDf: state.tableDataMx,
                }
                if (state.pwpj && state.tableData.length > 0 && state.tableDataMx.length > 0) {
                    axiosUtil.post('/backend/zjgl/pwpb/savePwpj', params).then(res=>{
                        if(res.message){
                            var msg = type == '0' ? '保存成功' : '提交成功'
                            ElMessage({
                                message: msg,
                                type: 'success',
                            })
                            closeForm();
                        }
                    }).catch(err=>{
                        ElMessage({
                            message: '发生异常'+err,
                            type: 'info',
                        })
                    })
                }else{
                    ElMessage({
                        message: '数据不全，请先维护评委明细信息',
                        type: 'error',
                    })
                }

            } else {
                ElMessage({
                    message: '请完善页面信息',
                    type: 'error',
                })
            }
            })
    }

        onMounted(async () => {
            await getDMBData('ZJPJZD', 'ZJPJDMList')
            await getDataList();
            await transData1();
            await getFormData();
        })
        return {
            ...toRefs(state),
            validateForm,
            dateFormat,
            getDataList,
            getFormData,
            closeForm
        }
    }
})
</script>

<style scoped>
.zdTitle {
    background-color: #E4E6F6;
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    font-weight: 600;
    margin-bottom: 20px;
}
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: unset;
}

/deep/ .el-collapse-item__header {
  background-color: #F2F3F5;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
}
:deep(.required .el-form-item__label):before {
    content: "*";
    color: #F56C6C;
    margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
    content: "*";
    color: #F56C6C;
    margin-right: 4px;
}
</style>

