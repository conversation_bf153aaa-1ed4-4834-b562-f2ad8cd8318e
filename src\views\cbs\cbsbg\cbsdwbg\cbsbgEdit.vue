<template>
  <div v-loading="loading" class="container">
    <el-tabs v-model="ActiveTab" type="border-card" style="height: calc(100% - 70px)">
      <el-tab-pane :name="item.SJMBBM" :label="item.SJMBMC" v-for="(item,index) in compList" :key="index">
        <div class="tab-pane-content">
          <component
              v-if="formData[item.SJMBBM] || item.SJMBBM==='CBSYJ'"
              v-model="formData"
              :ref="(el) => setRefMap(el, item.SJMBBM)"
              :is="ComponentDic[item.SJMBBM]"
              :defaultData="formData[item.SJMBBM]"
              :row="formData[item.SJMBBM]"
              :DWYWID="DWYWID"
              :LSDWYWID="LSDWYWID"
              :TYXYDM="formData?.JBXX?.TYXYDM"
              :BGJL="bgInfo"
              YWLXDM="BG"
              :editable="editable"
              :resultTableData="resultFormData?.[item.SJMBBM]"
          ></component>
        </div>
      </el-tab-pane>
    </el-tabs>

    <div style="align-items: center;justify-content: center;display: flex;background-color: white;height: 70px" v-if="editable&&!value">
      <el-button size="default" type="success" @click="saveData('save')">保存</el-button>
      <el-button size="default" type="primary" @click="saveData('submit')">提交</el-button>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw} from "vue";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import jbxxTab from "@views/cbs/cbsbg/commonTab/jbxx/jbxxTab";
import dwxxTab from "@views/cbs/cbsbg/commonTab/dwxx/dwxxTab";
import zzxxTab from "@views/cbs/cbsbg/commonTab/zzxx/zzxxTab";
import xkxx from "@views/cbs/templateManagement/DataTemplateManagement/xkxx/xkxx.vue"
import txzs from "@views/cbs/templateManagement/DataTemplateManagement/txzs/txzs.vue"
import ryxxTab from "@views/cbs/cbsbg/commonTab/ryxx/ryxxTab";
import yjxxTab from "@views/cbs/cbsbg/commonTab/yjxx/yjxxTab";
import jcxx from "@views/cbs/templateManagement/DataTemplateManagement/jcxx/jcxx.vue"
import zscq from "@views/cbs/templateManagement/DataTemplateManagement/zscq/zscq.vue"
import fwxx from "@views/cbs/templateManagement/DataTemplateManagement/fwxx/fwxx.vue"
import tdxxTab from "@views/cbs/cbsbg/commonTab/tdxx/tdxxTab";
import sqwtxx from "@views/cbs/templateManagement/DataTemplateManagement/sqwtxx/sqwtxx.vue"
import yrxxTab from "@views/cbs/cbsbg/commonTab/yrxx/yrxxTab";
import tycList from "@views/cbs/tyc/tycList.vue"
import zdxxTab from "@views/cbs/cbsbg/commonTab/zdxx/zdxxTab";
import aqhbTab from "@views/cbs/cbsbg/commonTab/aqhb/aqhbTab";
import {ElLoading, ElMessage} from "element-plus";
import comFun from "@lib/comFun";
import {auth, mixin} from "@core";
import sbxxTab from "@views/cbs/cbsbg/commonTab/sbxx/sbxxTab";
import clxxTab from "@views/cbs/cbsbg/commonTab/clxx/clxxTab";
import tabFun from "@lib/tabFun";
import axios from "axios";
import api from "@src/api/lc";
import cbsyjTab from "@views/cbs/cbsbg/commonTab/yjxx/cbsyjTab";
import bwtrxxComp from "@views/cbs/cbsbg/commonTab/bwtrxx/bwtrxxTab";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    value: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params?.editable,
      DWYWID: props.params?.id,
      LSDWYWID: '',
      ActiveTab: 'JBXX',
      formData: {},
      resultFormData: {},
      ComponentDic: {
        JBXX: markRaw(jbxxTab),
        ZZXX: markRaw(zzxxTab),
        XKXX: markRaw(xkxx),
        TXZS: markRaw(txzs),
        RYXX: markRaw(ryxxTab),
        SBXX: markRaw(sbxxTab),
        YJXX: markRaw(yjxxTab),
        JCXX: markRaw(jcxx),
        ZSCQ: markRaw(zscq),
        CLXX: markRaw(clxxTab),
        FWXX: markRaw(fwxx),
        TDXX: markRaw(tdxxTab),
        SQWTXX: markRaw(sqwtxx),
        DWXX: markRaw(dwxxTab),
        YRSQXX: markRaw(yrxxTab),
        QYFXXX: markRaw(tycList),
        ZDXX: markRaw(zdxxTab),
        AQHB: markRaw(aqhbTab),
        CBSYJ: markRaw(cbsyjTab),
        BWTRXX: markRaw(bwtrxxComp),
      },
      refMap: {},

      compList: [],
      MBMXList: [],

      bgInfo: {},

    })

    const getFormData = (DWYWID, resForm, isLs) => {
      let params = {
        DWYWID: DWYWID
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/cbsyj/getTeamInfo', params).then((res) => {
        let resData = state.MBMXList.reduce((t, i) => {
          t[i.SJMBBM] ? t[i.SJMBBM].push(i) : (t[i.SJMBBM] = [i]);
          return t;
        }, {})
        Object.entries(res.data || {}).forEach(([key, value]) => {
          if (value) {
            //排除null
            if (Array.isArray(value)) {
              //list的话且不为空数组的时赋值
              if (value.length || isLs) resData[key] = value;
            } else {
              resData[key] = value;
            }
          }
        })

        state[resForm] = resData
        state.loading = false
      })
    }

    const loadBgxxForm = () => {
      let params = {
        DWYWID: state.DWYWID
      }
      axiosUtil.get('/backend/sccbsgl/dwxxbg/selectBgxxByDwid', params).then(res => {
        if (res.data) {
          state.LSDWYWID = res.data.LSDWYWID
          state.bgInfo = res.data
          getTabList()
        } else {
          ElMessage.error('变更信息查询失败')
        }
      })
    }

    const getTabList = () => {
      let params = {
        DWYWID: state.DWYWID,
        MBLX: 'QY',
        BGLX: state.bgInfo.BGLX
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/dwxxbg/selecCbsTabList', params).then((res) => {
        let tab = res.data.tabList || []
        state.MBMXList = res.data.MBMXList || []
        state.MBMXList = state.MBMXList.map((i) => ({
          ...i,
          ZYMC: i.ZYFLMC,
          ZYBM: i.ZYFLDM,
          SHZT: "0",
        }))
        // let MBLX = res.data.MBLX || []
        state.compList = [{SJMBMC: "承包商基本信息", SJMBBM: "JBXX"}]
        // if (MBLX.map(i => i.MBLX).includes('DW')) {
        //   state.compList.push({SJMBMC: "队伍信息", SJMBBM: "DWXX"})
        // } else {
        //   state.compList.push({SJMBMC: "引入申请信息", SJMBBM: "YRSQXX"})
        // }

        state.compList.push(...tab)
        state.compList.push({SJMBMC: "业绩信息", SJMBBM: "CBSYJ"});
        state.compList.push({SJMBMC: "被委托人信息", SJMBBM: "BWTRXX"});
        getFormData(state.DWYWID, 'formData')
        getFormData(state.LSDWYWID, 'resultFormData', true)
      })
    }
    const {vsuiEventbus} = mixin()

    const saveData = (type) => {
      if (type === 'save') {
          submitForm(type).then(() => {
            ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
            state.loading = false
          })
      } else {
          validateForm().then(res => {
            if (res) {
              submitForm(type).then(() => {
                  tabFun.closeNowTab();
                  vsuiEventbus.emit("reloadBGTableData", {});
                  ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
              })
            }
          })
      }
    }

    const submitProcess = () => {
      let jbxx = state.formData.JBXX;
      let processName = jbxx.CBSDWQC+'-信息变更'

      let processId = 'CBS_XXBG'

      if (!processId) {
        ElMessage({message: '提交失败，找不到对应流程请重试！', type: 'error',})
        return
      }

      let _params = {};
      _params.strSystemCode = 'AUTHM_CENTER';
      _params.processId = processId;
      _params.engineType = 'vs';
      _params.activityId = 'new';
      _params.processInstanceName = processName;
      _params.businessId = state.DWYWID;
      _params.apprValue = "1";
      _params.apprResult = "提交";
      _params.loginName = state.userInfo.userLoginName;
      _params.userOrgId = state.userInfo.orgnaId;
      let ld = ElLoading.service({target: "#auditDiv", text: "正在提交数据，请稍后...",});
      axios({
        method: "post",
        url: api.createTask(),
        data: "varJson=" + JSON.stringify(_params),
      }).then((res) => {
        ld.close();
        if (res.data && res.data.result == 1) {
          ElMessage({message: '提交成功！', type: 'success',})
          tabFun.closeNowTab();
          vsuiEventbus.emit("reloadBGTableData", {});
        } else {
          state.loading=false
          ElMessage({message: '提交失败！', type: 'error',})
        }
      }).catch((error) => {
        state.loading=false
        ld.close();
        ElMessage({message: '提交失败！', type: 'error',})
      });

    }

    const submitForm = (type) => {
      return new Promise(resolve => {
        state.loading = true

        let params = formatData({...state.formData})
        params.BGMXList = getBGMXList()
        params.BGJLID = state.bgInfo.BGJLID

        params.type=type
        params.BGLX=state.bgInfo.BGLX


        console.log('最后的提交', params)
        axiosUtil.post('/backend/sccbsgl/dwxxbg/saveCbsBgxx', params).then(res => {
          resolve()
        })
      })


    }

    const getBGMXList = () => {
      let res = []
      Object.values(state.refMap).forEach(item => {
        if (item.BGXX) {
          res.push(...item.BGXX)
        }
      })
      res.forEach(item => {
        item.BGLY = 'CBS'
        item.BGMXID = comFun.newId()
        item.BGJLID = state.bgInfo.BGJLID
        item.DWYWID = state.DWYWID
      })
      return res
    }

    const formatData = (params) => {
      const userinfo = auth.getPermission();

      const updateUserInfo = {
        XGRZH: userinfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }

      const setDefaultValue = (data) => {
        Object.entries(updateUserInfo).forEach(([key, value]) => {
          if (!data[key]) {
            data[key] = value;
          }
        })
      }

      Object.entries(params).forEach(([key, value]) => {
        if (value) {
          //排除null
          if (Array.isArray(value)) {
            //list的话且不为空数组的时赋值
            value.forEach((x) => {
              x.DWYWID = state.DWYWID;
              setDefaultValue(x);
            });
          } else if (value instanceof Object) {
            setDefaultValue(value);
          }
        }
      })
      params.ZZXX?.forEach((x) => {
        x.ZSDLDM = "ZZZS";
        x.ZSCYZBS = params.YRSQXX.DWYWID
      });
      params.TXZS?.forEach((x) => {
        x.ZSDLDM = "TXZS";
        x.ZSCYZBS = params.YRSQXX.DWYWID
      });
      params.XKXX?.forEach((x) => {
        x.ZSDLDM = "XKZS";
        x.ZSCYZBS = params.YRSQXX.DWYWID
      });
      params.RYXX?.forEach(x => {
        if (x.ZSXX) {
          x.ZSXX.forEach(v => {
            v.DWYWID = state.DWYWID
            setDefaultValue(v);
          })
        }
      })

      let form = {
        cbsjbxx: params.JBXX,
        cbsdwxx: params.YRSQXX,
        cbszs: (params.ZZXX ?? []).concat(params.TXZS ?? []).concat(params.XKXX ?? []),
        cbsdwyj: params.YJXX,

        cbsdwzscq: params.ZSCQ,
        cbsdwclxx: params.CLXX,
        cbsdwfwxx: params.FWXX,
        cbsdwtdxx: params.TDXX,
        cbsdwzdxx: params.ZDXX,
        cbsdwaqhb: params.AQHB,

        cbsdwsb: params.SBXX,
        cbsdwjcqk: params.JCXX,
        cbsdwcy: params.RYXX,
        bczt: '0', //"0保存；1提交；2审核通过；"
        DWYWID: state.DWYWID,

        cbsyj: params.CBSYJ,
        bwtrxx:  params.BWTRXX,
      }
      form.cbszs.forEach(x => x.ZSCYZLXDM = 'CBS')
      return form
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        Promise.all(Object.values(state.refMap).filter(i => i.validateForm).map(i => i.validateForm())).then((result) => {
          ElMessage.success('校验成功')
          resolve(true)
        }).catch((err) => {
          ElMessage.error(Object.values(err)[0]?.[0]?.message)
          resolve(false)
        })
      })
    }

    const setRefMap = (el, name) => {
      state.refMap[name] = el
    }

    onMounted(() => {
      if (!props.params) {
        ElMessage.warning('参数缺失请关闭该页签重新打开！')
        return;
      }
      loadBgxxForm()
    })

    return {
      ...toRefs(state),
      setRefMap,
      saveData

    }
  }

})
</script>

<style scoped>
.container {
  height: calc(100vh - 100px);
  background-color: #fff;
}

.container .el-tabs {
  height: 100%;
}

.container .el-tabs ::v-deep .el-tabs__header {
  margin-bottom: 0;
}

.container .el-tabs ::v-deep .el-tabs__content {
  height: calc(100% - 60px);
}

.tab-pane-content {
  height: 100%;
}

:deep(.dataChange) {
  color: #F56C6C;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-table .warning-row) {
  --el-table-tr-bg-color: #f8bcbc;
}

:deep(.el-table .success-row) {
  --el-table-tr-bg-color: #e2fad4;
}


</style>
