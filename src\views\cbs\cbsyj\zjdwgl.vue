<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="data.zjdwmc" placeholder="请输入队伍名称"
                    @keyup.enter="query"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="10" class="grid-cell" style="display: flex;align-items: center">
        <el-button type="primary" @click="query"><el-icon><Search/></el-icon>查询</el-button>
        <el-button type="primary" class="lui-button-add" @click="add"><el-icon><Plus/></el-icon>新增</el-button>
        <el-button type="primary" @click="exportData"><el-icon><Upload/></el-icon>导出</el-button>
        <el-button v-if="returnFlag" @click="goChildrenBack">返回</el-button>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          highlight-current-row
          size="default"
          height="calc(100vh - 250px)"
          ref="table"
          fit
          border
          :data="data.tableData"
          v-loading="tableLoading">
        <EleProTableColumn
            v-for="prop in data.tableColumn"
            :col="prop"
            :key="prop.columnKey">
          <template #zt="{row,$index}">
            <div>
              <el-tag class="ml-2" type="success" v-if="row.SYZT == '1'">已使用</el-tag>
              <el-tag class="ml-2" type="warning" v-else>未使用</el-tag>
            </div>
          </template>
          <template #opration="{row,$index}">
            <div>
              <el-button class="lui-table-button" @click="edit(row,$index)">编辑</el-button>
              <el-button class="lui-table-button" :disabled="deleteLoading" @click="remove(row,$index)">删除</el-button>
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </div>
    <el-dialog
        v-model="data.saveVisible"
        title="数据维护"
        width="30%"
        custom-class="lui-dialog"
    >
      <el-form
          class="lui-card-form"
          ref="saveFormRef"
          :model="data.saveForm"
          :rules="rules"
          label-width="120px"
          size="default"
          status-icon
      >
        <el-row :gutter="0" class="grid-row">
          <el-col :span="24" class="grid-cell">
            <el-form-item label="队伍名称" prop="DWMC" >
              <el-input v-model="data.saveForm.DWMC" />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell">
            <el-form-item label="队伍编码" prop="DWBM">
              <el-input v-model="data.saveForm.DWBM" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
            <span class="dialog-footer">
              <el-button @click="data.saveVisible = false">取消</el-button>
              <el-button type="primary" :disabled="confirmLoading" @click="save">
                确定
              </el-button>
            </span>
      </template>
    </el-dialog>
  </el-form>
</template>
<script setup>
import {onMounted, reactive, ref, defineEmits, defineProps} from "vue";
import {ElMessage, ElMessageBox} from 'element-plus';
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {deleteZjdwglRemove, getZjdwglQuery, postZjdwglSave} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import {v4 as uuidv4} from "uuid";
import comFun from "@src/lib/comFun";
import {getZjdwglExport} from "../../../api/sccbsgl";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
const emits = defineEmits(["goChildren","queryChildrenCompanys"]);
const props = defineProps({
    returnFlag: {
        type: String,
        defaultData: () => '',
    },
});
const data = reactive({
    saveForm: {
        DWWYBS: null,
        DWMC: null,
        DWBM: null,
        SYZT: '0',
        CJRZH: null,
        CJRXM: null,
        CJDWID: null,
        CJSJ: null,
        XGRZH: null,
        XGSJ: null,
        SHZT: '0',//审核状态：0保存；1提交；2审核通过；,
        DELETE_FLAG: '0'//删除标志  删除：1；默认0
    },
    currentUser: {},
    defaultForm: {
        DWWYBS: null,
        DWMC: null,
        DWBM: null,
        SYZT: '0',
        CJRZH: null,
        CJRXM: null,
        CJDWID: null,
        CJSJ: null,
        XGRZH: null,
        XGSJ: null,
        SHZT: '0',//审核状态：0保存；1提交；2审核通过；,
        DELETE_FLAG: '0'//删除标志  删除：1；默认0
    },
    saveVisible: false,
    zjdwmc: '',
    tableData: [],
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 55,
            align: "center"
        },
        {
            label: "企业名称",
            prop: "QYMC",
            align: "center",
            showOverflowTooltip: true,
        },
        {
            label: "队伍名称",
            prop: "DWMC",
            align: "center",
            // slot: "faultHours",
        },
        {
            label: "编码",
            prop: "DWBM",
            align: "center",
        },
        {
            label: "创建时间",
            prop: "CJSJ",
            align: "center",
            width: 150,
        },
        {
            label: "状态",
            prop: "SHZT",
            align: "center",
            width: 150,
            slot: 'zt'
        },
        {
            label: "操作",
            align: "center",
            width: 150,
            fixed: "right",
            slot: "opration",
        },
    ],
})


const goChildrenBack = ()=>{
    emits('goChildrenBack')
}

const exportData = ()=>{
    getZjdwglExport({dwmc: data.zjdwmc,orgid: data.currentUser.ORGNA_ID},`子级单位信息${new Date().getTime()}.xlsx`);
}

const rules = reactive({
    DWMC: [
        { required: true, message: '请输入队伍名称', trigger: 'blur' },
    ],
    DWBM: [
        { required: true, message: '请输入队伍编码', trigger: 'blur' },
    ],
});
const tableLoading = ref(false);
const query = () => {
    tableLoading.value = true;
    getZjdwglQuery({dwmc: data.zjdwmc,orgid: data.currentUser.ORGNA_ID}).then(res => {
        data.tableData = res.data
    }).catch((err) => {
        console.log(err);
    }).finally(() => {
        tableLoading.value = false
    })

}
const edit = (row,index) => {
    data.saveForm = {
        ...row
    }
    data.saveVisible = true;

}
const add = () => {
    data.saveForm = JSON.parse(JSON.stringify(data.defaultForm))
    data.saveVisible = true;
}
const saveFormRef = ref(null);
const confirmLoading = ref(false);
const save = ()=>{
    saveFormRef.value.validate().then(res=>{
        let type = data.saveForm.DWWYBS? 'edit': 'add'
        if(type == 'add'){
            data.saveForm.CJSJ = comFun.getNowTime();
            data.saveForm.CJRXM = data.currentUser.USER_NAME;
            data.saveForm.CJRZH = data.currentUser.USER_LOGINNAME;
            data.saveForm.DWWYBS = uuidv4().replace(/-/g,'')
            data.saveForm.CJDWID = data.currentUser.ORGNA_ID
            data.saveForm.SYZT = '0'
        }else{
            data.saveForm.XGSJ = comFun.getNowTime();
            data.saveForm.XGRZH = data.currentUser.USER_LOGINNAME;
        }
        confirmLoading.value = true;
        postZjdwglSave(data.saveForm).then(res=>{
            ElMessage({
                type: 'success',
                message: '保存成功!'
            });
            emits('queryChildrenCompanys')
            query();
            data.saveVisible = false;
        }).catch(e=>{
            ElMessage({
                type: 'error',
                message: '保存失败!'
            });
        }).finally(() => {
            confirmLoading.value = false
        })
    }).catch(e=>{

    })
}
const deleteLoading = ref(false);
const remove = (row, index) => {
    ElMessageBox.confirm('是否确认删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        deleteLoading.value = true;
        deleteZjdwglRemove({id: row.DWWYBS, status: row.SYZT}).then(res => {
            ElMessage({
                type: 'success',
                message: '删除成功!'
            });
            emits('queryChildrenCompanys')
            query();
        }).catch(e => {
            ElMessage({
                type: 'error',
                message: '删除失败!'
            });
        }).finally(() => {
            deleteLoading.value = true;
        })        
    }).catch(() => {
    });
}

const getUserInfo = ()=>{
    let user = VSAuth.getAuthInfo().permission
    getCommonGetUserByParam({userName: user.userLoginName}).then(res=>{
        data.currentUser = res.data;
        query()
    })
}


onMounted(() => {
    getUserInfo();
})
</script>

<style scoped>
.header {
    padding: 10px 10px 0;
    height: 50px;
    line-height: 50px;
}

.main {
    height: calc(100% - 55px);
    padding: 10px;
}
</style>
