<template>
  <div class="zhyy-list-container">
    <div class="zhyy-list-main">
      <el-row
        class="zhyy-list-searchArea"
        style="padding: 8px; margin-bottom: 0; border: 0;white-space:nowrap;"
      >
        <el-col :span="5" style="min-width:100px">
          <label>姓名：</label>
          <el-input
            style="width: 150px"
            class="filter-item"
            placeholder="请输入人员姓名"
            v-model="listQuery.XM"
            clearable
            size
          >
          </el-input>
        </el-col>

        <el-col :span="5" style="min-width:100px">
          <label>账号：</label>
          <el-input
            style="width: 150px"
            class="filter-item"
            clearable
            placeholder="请输入登录账号"
            v-model="listQuery.USER_LOGINNAME"
          >
          </el-input>
        </el-col>

        <el-col :span="5" style="min-width:100px">
          <label>二级单位：</label>
          <el-input
              style="width: 150px"
              class="filter-item"
              clearable
              placeholder="请输入二级单位"
              v-model="listQuery.ORGNA_TWO_NAME"
          >
          </el-input>
        </el-col>

        <el-col :span="5" style="margin-left: 15px;min-width:100px">
          <el-button style="margin-left: 15px" type="primary" icon="el-icon-search" @click="processSearch"
            >查询</el-button
          >
        </el-col>
      </el-row>
      <el-row class="zhyy-list-tableArea">
        <el-table
          border
          highlight-current-row
          ref="listTable"
          height="calc(100vh - 300px)"
          :data="tableData"
          :row-key="getRowKey"
          :header-cell-style="{ background: '#F4F7FA' }"
          @row-click="onSelect"
        >
          <el-table-column
            align="center"
            label="序号"
            type="index"
            min-width="5%"
            width="50px"
            :index="indexMethod"
          >
          </el-table-column>
          
          <el-table-column
            min-width="5%"
            prop="USER_NAME"
            align="right"
            header-align="center"
            label="人员姓名"
          ></el-table-column>
          <el-table-column
            min-width="5%"
            prop="USER_LOGINNAME"
            align="right"
            header-align="center"
            label="登录账号"
          ></el-table-column>
          <el-table-column
            min-width="5%"
            prop="ORGNA_NAME"
            align="right"
            header-align="center"
            label="组织机构"
          ></el-table-column>
          <el-table-column
            min-width="5%"
            prop="ORGNA_TWO_NAME"
            align="right"
            header-align="center"
            label="二级单位"
          ></el-table-column>
          <el-table-column
            min-width="10%"
            width=150
            label="操作"
            align="center"
            header-align="center"
          >
            <template #default="scope">
              <el-button  @click="onChangeLogin(scope.row.USER_LOGINNAME)" type="text" size="mini">切换登录</el-button>
            </template>
          </el-table-column>
        </el-table>
         <div class="zhyy-list-paginationArea">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="listQuery.size"
            :current-page="listQuery.page"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </el-row>
    </div>
  </div>
</template>

<script>
import axiosUtil from "../../lib/axiosUtil";
import comFun from "@src/lib/comFun";
import {store} from "@core";
export default {
  name: "index",
  components: {},
  data() {
    return {
      total: null,
      tableData: undefined,
      listQuery: {
        page: 1,
        size: 10,
        XM: "XXXX",
        USER_LOGINNAME: "",
      },
      
    };
  },
  mounted() {
      this.loadListData();
  },
  methods: {
    indexMethod(index) {
      return (
        index + this.listQuery.size * (this.listQuery.page - 1) + 1
      );
    },
    
    /**
     * @description: 加载列表数据
     * @param {*}
     * @return {*}
     */
    async loadListData() {

      var pageData =  await axiosUtil.get("/backend/common/selectUserList", this.listQuery)
      this.tableData = pageData.data.list;
      this.total = pageData.data.total;
    },
    
    processSearch() {
      this.listQuery.page = 1;
      this.loadListData();
    },

    /**
     * @description: 变更每页条数时自动加载数据
     * @param {*}
     * @return {*}
     */
    handleSizeChange(val) {
      this.listQuery.size = val;
      this.loadListData();
    },
    /**
     * @description: 点击某一页时自动加载数据
     * @param {*}
     * @return {*}
     */
    handleCurrentChange(val) {
      this.listQuery.page = val;
      this.loadListData();
    },
    /*async onChangeLogin(username){*/
    /*  let params={*/
    //     USER_LOGINNAME: username
    //   }
    //   axiosUtil.get('/backend/test/changeWxhToBxl',params).then(res=>{
    //     sessionStorage.setItem('devUserName',username)
    //     store.state.vsUserInfo=null
    //     location.reload();
    //   })
    //
    //
    // }

    async onChangeLogin(username){
      // VSAuth.logout().then(()=>{
      localStorage.removeItem("userName")
      localStorage.removeItem("permission")
      localStorage.removeItem("isLogin")
      // })
      let pageData = await axiosUtil.get("/backend/login/backendLogin", { userName: username});
      if(pageData.meta.success){
        //this.$router.push({ path: '/gcyztb/zbsqList' })
        location.reload();
      }else{
        this.$message({type: 'error',message: '切换失败!'});
      }
    }
  },
};
</script>

<style scope>
*/deep/.el-dialog {
  max-height: calc(100vh - 1px);
  overflow: auto;
}
*/deep/.el-dialog__body {
  max-height: calc(90vh - 10px);
  overflow: auto;
  padding: 10px 10px !important;
}
.el-dialog__wrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: 0;
}
</style>
