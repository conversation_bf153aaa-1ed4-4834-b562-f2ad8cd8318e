<template>

    <div>
        <VuePdfEmbed :source="source"></VuePdfEmbed>
    </div>

</template>

<script>
    import {defineComponent, onMounted, reactive, ref, toRefs} from 'vue';
    import VuePdfEmbed from 'vue-pdf-embed';
    import {ElMessage} from "element-plus";


    export default defineComponent({
        name: '',
        components: {VuePdfEmbed},
        props: {
            pdfUrl: String,
        },
        setup(props, {emit}) {
            const state = reactive({
                source: props.pdfUrl, //预览pdf文件地址
            });

            onMounted(() => {

            });

            return {
                ...toRefs(state),
            }
        }

    })

</script>


<style lang="css" scoped>

    .page-tool {
        position: absolute;

        bottom: 35px;

        padding-left: 15px;

        padding-right: 15px;

        display: flex;

        align-items: center;

        background: rgb(66, 66, 66);

        color: white;

        border-radius: 19px;

        z-index: 100;

        cursor: pointer;

        margin-left: 50%;

        transform: translateX(-50%);

    }

    .page-tool-item {
        padding: 8px 15px;
        padding-left: 10px;
        cursor: pointer;
    }

</style>
