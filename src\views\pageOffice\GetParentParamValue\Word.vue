<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');
const userName=ref("");

function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("关闭", "Close", 21);
}

function AfterDocumentOpened() {
	// PageOffice的文档打开后事件回调函数
	userName.value = pageofficectrl.WindowParams;
}

function Close() {
	pageofficectrl.CloseWindow();
}

function openFile() {
	return request({
		url: '/GetParentParamValue/Word',
		method: 'get',
	})
}
onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit, AfterDocumentOpened,Close };//其中OnPageOfficeCtrlInit必须

})
</script>
<template>
	<div class="Word">
		<div>
			<font color="red">父页面传递过来的参数:</font><input type="text" id="userName" v-model="userName" name="userName" />
		</div>
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
