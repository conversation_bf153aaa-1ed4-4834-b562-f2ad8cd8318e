<template>
  <div v-loading="loading">
    <zbfaTab class="tab-pane" ref="zbfaTab" :params="params" v-model="formData"/>
    <div style="width: 100%;margin-top: 10px;margin-bottom: 10px;justify-content: center;display: flex">
      <el-button size="default" type="success" @click="saveData('save')" v-if="editable">保存</el-button>
      <el-button size="default" type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
      <el-button size="default" @click="closeForm">返回</el-button>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import zbfaTab from "@views/zbxsgl/xssqgl/commonTab/zbfaTab";


export default defineComponent({
  name: '',
  components: {zbfaTab},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      FAID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        SFFBD: '1',

        XMXX: {},
        XSWJ: {
          WJID: comFun.newId(),
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
          SHZT: '0',
          YWZT: '1',
          SFWSPS: '0'
        },
        BDList: [],
        DWList: []
      },

    })

    const getFormData = () => {
      let params = {
        FAID: state.FAID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/xssqgl/selectXssqById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      })
    }


    const saveData = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(() => {
          submitForm(type)
        }).catch(msg => {
          ElMessage.error(msg)
        })
      }

    }

    const submitForm = (type) => {
      // let params = {
      //   ...state.formData,
      //   XSWJ: {
      //     ...state.formData.XSWJ,
      //     WJID: state.WJID,
      //     XGRZH: state.userInfo.userLoginName,
      //     XGSJ: comFun.getNowTime(),
      //   },
      // }
      // if (type === 'submit') {
      //   params.XSWJ.SHZT = '2'
      // }
      // state.loading = true
      // axiosUtil.post('/backend/xsgl/xssqgl/saveXswjxx', params).then(res => {
      //   ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
      //   closeForm()
      //   state.loading = false
      // })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return Promise.all([
        instance.proxy.$refs['zbwjTab'].validateForm(),
      ])
    }

    const closeForm = () => {
      emit('close')
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    onMounted(() => {
      getFormData()
      getDMBData("XSFS", "XSFSOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData
    }
  }

})
</script>

<style scoped>
.tab-pane {
  height: 65vh;
  overflow: auto;
}
</style>
