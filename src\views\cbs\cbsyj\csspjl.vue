<!--
 * @Description: 选商申请表
 * @Author: WLF (<EMAIL>)
 * @Date: 2024-12-13 14:53:03
 * @LastEditTime: 2024-12-15 16:36:00
 * @FilePath: \jhscgl-xieyun-front\src\views\cbs\cbsyj\csspjl.vue
 * @copyright : Copyright (c) 2024 wlf
-->
<template>
    <div>
        <div>
            <div id="printMe">
                <el-descriptions title="西北油田项目选商申请表" :column="6" border style="padding: 10px;" class="custom-descriptions">
                    <el-descriptions-item label="申报单位" width="250px" :span="6" label-align="right">{{ xmxx.SSDWMC }}</el-descriptions-item>
                    <el-descriptions-item label="项目名称" :span="6" label-align="right">{{ xmxx.XMMC }}</el-descriptions-item>
                    <el-descriptions-item label="项目主要内容" :span="6" label-align="right">
                        <div v-for="(item,index) in BDList" :key="index">
                            <el-row :style="{width: '100%','border-bottom': index < BDList.length - 1 ? '1px solid RGB(227,234,248)' : 'none'}">
                                {{item.BDMC+ '：' + item.BDNRSM }}
                            </el-row>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="项目批复文件或立项说明" :span="6" label-align="right">{{ xmxx.PFHLXSM }}</el-descriptions-item>
                    <el-descriptions-item label="预计项目金额（万元）" width="250px" :span="2" label-align="right">{{ xmxx.YJJE }}</el-descriptions-item>
                    <el-descriptions-item label="服务期限" :span="2" width="200px" label-align="right">{{ xmxx.FWQX }}</el-descriptions-item>
                    <el-descriptions-item label="选取中标人数量" width="200px" :span="2" label-align="right">{{ xmxx.XQZBRSL }}</el-descriptions-item>
                    <el-descriptions-item label="选商方式" :span="6" label-align="right" align="center">
                        <el-checkbox-group v-model="checkedLabels">
                            <el-checkbox label="GKZB" size="large" @click.prevent>公开招标</el-checkbox>
                            <el-checkbox label="YQZB" size="large" @click.prevent>邀请招标</el-checkbox>
                            <el-checkbox label="JB" size="large" @click.prevent>竞标</el-checkbox>
                            <el-checkbox label="JJ" size="large" @click.prevent>竞价</el-checkbox>
                            <el-checkbox label="GKJB" size="large" @click.prevent>公开竞标</el-checkbox>
                            <el-checkbox label="GKJJ" size="large" @click.prevent>公开竞价</el-checkbox>
                            <el-checkbox label="DJTP" size="large" @click.prevent>独家谈判</el-checkbox>
                        </el-checkbox-group>
                    </el-descriptions-item>
                    <el-descriptions-item label="拟邀请承包商名称" :span="6" label-align="right">
                        <div v-for="(item,index) in DWList" :key="index">
                            <el-row :style="{width: '100%','border-bottom': index < BDList.length - 1 ? '1px solid RGB(227,234,248)' : 'none'}">
                                {{item.CBSDWQC}}
                            </el-row>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item width="250px" label="计划发标时间" :span="3" label-align="right">{{ formatDate(xmxx.JHFBSJ,true) }}</el-descriptions-item>
                    <el-descriptions-item width="200px" label="计划开标（谈判）时间" :span="3" label-align="right">{{ formatDate(xmxx.JHKBSJ,true) }}</el-descriptions-item>
                    <el-descriptions-item label="申报意见（选商方式选用说明）" :span="6" label-align="right">{{ xmxx.SBYJ }}</el-descriptions-item>
                    <div v-for="(item,index) in tasksMonitorList" :key="index" class="paging" ref="pagingRefs">
                        <el-descriptions-item :label="item.BUSINESSNAME" :span="6" label-align="right">
                            <div>
                                {{ formTitle(item.APPRRESULT,'审核结果：') + formString(item.APPRRESULT) +
                                formTitle(item.OPINION,'；审查意见：') + formString(item.OPINION) || ''}}
                            </div>
                            <el-row justify="end">
                                <el-col :span="6">
                                    <span style="font-weight: bold;font-size: 15px;">{{ item.CLR }}</span>
                                    {{ formatDate(item.BLSJ,false) }}
                                </el-col>
                            </el-row>
                        </el-descriptions-item>
                    </div>

                </el-descriptions>
            </div>
            <el-row justify="center">
                <el-col :span="2">
                    <el-button type="warning" @click="printing" v-print="'#printMe'">打印</el-button>
                </el-col>
            </el-row>
            <el-row justify="end" style="margin-bottom: 20px;">
                <el-col :span="6">
                    <div style="color: red;font-size: 10px;">注：如需导出可点击打印后另存为PDF文件</div>
                    <div style="color: red;font-size: 10px;margin-left: 20px;">如流程审核节点过长时边框不适配，可适当调整缩放与边距</div>
                </el-col>
            </el-row>
        </div>

    </div>
</template>

<script>

import { defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, nextTick,ref } from "vue";
import axiosUtil from "@lib/axiosUtil";
import { ElMessage, ElMessageBox } from "element-plus";
export default defineComponent({
    name: '',
    components: {},
    props: {
        params: Object
    },
    setup(props, { emit }) {
        const state = reactive({
            xmxx: {},
            BDList: [],
            DWList: [],
            tasksMonitorList: [],
            checkedLabels: []
        })

        const loadPageList = () => {
            let params = {
                FAID: props.params.ID,
                processId: props.params.WORKFLOWID,
                processInstanceId: props.params.WORKFLOWINSTANCEID
            }
            axiosUtil.get('/backend/xsgl/xssqgl/queryXssqjl', params).then((res) => {
                state.xmxx = res.data.xsInfo?.XMXX
                state.xmxx.XQZBRSL = res.data.xsInfo.XQZBRSL
                state.xmxx.PFHLXSM = res.data.xsInfo.PFHLXSM
                state.xmxx.FWQX = res.data.xsInfo.FWQX
                state.xmxx.SBYJ = res.data.xsInfo.SBYJ
                state.BDList = res.data.xsInfo?.BDList
                state.xmxx.JHKBSJ = res.data.xsInfo.XSWJ.JHKBSJ
                state.xmxx.JHFBSJ = res.data.xsInfo.XSWJ.JHFBSJ
                state.checkedLabels.push(res.data.xsInfo?.XSFS)
                state.DWList = res.data.xsInfo.DWList
                state.xmxx.YJJE = res.data.xsInfo.BDList.reduce((sum, item) => {
                    if (item.YJJE !== null && item.YJJE !== undefined) {
                    return sum + parseFloat(item.YJJE);
                    }
                    return sum;
                }, 0);
                state.tasksMonitorList = res.data.workInfo.tasksMonitorList

            })
        }
        const formatDate = (dateString,flag) => {
            if(dateString){
                const date = new Date(dateString);

                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                if(flag){
                    return `${year}-${month}-${day} ${hours}:${minutes}`;
                }else{
                    return `${year}年${month}月${day}日 ${hours}时${minutes}分`;
                }
            }
        }
        const formString = (str) => {
            return str ? str : ''
        }
        const formTitle = (strA,strB) =>{
            if(strA){
                return strB
            }else{
                return ''
            }
        }
        const PAGE_HEIGHT = 1100 // A4纸高度
        const pagingRefs = ref([]);
        const printing = () => {
            nextTick(() => {
                // const splitDoms = document.getElementsByClassName('paging')
                const splitDoms =  pagingRefs.value;
                console.log("🚀~~~ -->splitDoms",splitDoms)
                let startY = 0 // 占用A4纸的高度，从每页第一个poetry div的top值开始累加
                for (let i = 0; i < splitDoms.length; i++) {
                    const splitDom = splitDoms[i]
                    const splitValue = splitDom.getBoundingClientRect()
                    console.log("🚀~~~ -->splitDom.getBoundingClientRect()",splitDom.getBoundingClientRect())
                    if (startY === 0) {
                        startY = splitValue.top
                    }
                    const pageHeight = splitValue.bottom - startY
                    // 当加上当前div的高度大于A4纸高度时，给前一个div加上分页标识
                    if (pageHeight > PAGE_HEIGHT) {
                        console.log("🚀~~~ -->i",i)
                        startY = 0
                        if (i > 0) {
                            splitDoms[i - 1].style.pageBreakBefore = 'always'; // 给前一个元素添加分页符
                        }
                    }
                }
            });
        }
        onMounted(() => {
            loadPageList()
        })

        return {
            ...toRefs(state),
            loadPageList,
            formatDate,
            formString,
            formTitle,
            printing
        }
    }

})
</script>

<style scoped>
*>>>.custom-descriptions .el-descriptions__header {
  display: flex;
  justify-content: center;
}
*>>>.el-checkbox__input {
  -webkit-print-color-adjust: exact;
  -moz-print-color-adjust: exact;
  print-color-adjust: exact;
}
@page {
    size: auto A4 landscape;
    margin-top: 20mm;
}

@media print {
    .section {
        page-break-before: always;
        margin-top: 0;
    }
}
.paging{
    word-wrap:break-word;
}
::v-deep .el-descriptions__label {
  background-color: transparent !important;
}
</style>
