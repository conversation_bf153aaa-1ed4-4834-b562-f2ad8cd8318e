<template>
    <div>
        <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
                 size="default">
            <el-row>
                <el-col :span="18" class="grid-cell">
                    <span>请选择承包商/队伍的资质要求</span>
                </el-col>

                <el-col :span="6" class="grid-cell" v-if="editable">
                    <div class="static-content-item" style="text-align: right">
                        <el-button ref="button91277" @click="addRow" type="primary">
                            新增
                        </el-button>
                        <el-button ref="button91277" @click="saveData" type="primary">
                            确认
                        </el-button>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <div class="container-wrapper">
                        <el-table ref="datatable91634" :data="tableData" height="400px"
                                  :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                                  @selection-change="handleSelectionChange"
                                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                            <el-table-column prop="YWBM" label="专业" align="center"
                                             :show-overflow-tooltip="true" min-width="160">
                                <template #default="{row,$index}">
                                    <el-form-item label="" label-width="0" >
                                        <el-cascader v-model="row.YWBM" :options="ZRZYTree" filterable :disabled="!editable"
                                                     :props="{label:'ZYMC',value:'ZYBM',emitPath: false}" clearable
                                                        style="width: 80%" @change="ywbmChange(row.YWBM, $index)" />
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="CZ" label="操作" align="center" width="100" fixed="right"
                                             v-if="editable">
                                <template #default="scope">
                                    <el-button size="small" class="lui-table-button" type="primary"
                                               @click="delZy(scope.$index)">删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-col>
            </el-row>

            <el-row v-if="tableData.length > 1">
                <el-col :span="16" class="grid-cell">
                    <el-form-item label="要求承包商/队伍：" prop="YWFWGZ" label-width="150px">
                        <el-radio-group v-model="formData.YWFWGZ" :disabled="!editable">
                            <el-radio label="or">具备以上任意一种资质（只要具备以上任意一种资质，承包商/队伍就能参加投标）</el-radio>
                            <el-radio label="and">具备以上全部资质（必须具备以上全部资质的承包商/队伍才能参加投标）</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>

    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import axiosUtil from "@lib/axiosUtil";
    import {ElMessage} from "element-plus";
    import comFun from "@lib/comFun";

    export default defineComponent({
        name: '',
        components: {},
        props: {
            modelValue: {
                type: Object,
                default: {}
            }
        },
        setup(props, {emit}) {
            const state = reactive({
                editable: props.modelValue.editable,
                listQuery: {},
                tableData: [],
                total: 0,
                checkList: [],
                formData: {},
                ZRZYTree: [],
                zrzyList: [],
            });

            const getZrzyList = () => {
                let params = {};
                axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
                    state.zrzyList = [...res.data];
                    state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0');
                });
            };

            const ywbmChange = (val, index) => {
                if(val) {
                    state.zrzyList.forEach(it => {
                        if(it.ZYBM === val) {
                            state.tableData[index].YWMC = it.ZYMC;
                        }
                    });
                }
            };

            const saveData = () => {
                if (state.tableData.length === 0) {
                    ElMessage.warning('请添加专业');
                    return
                }
                if (!state.formData.YWFWGZ && state.tableData.length > 1) {
                    ElMessage.warning('请选择“要求承包商/队伍”');
                    return
                }
                let error = '';
                for(let i = 0; i < state.tableData.length; i++) {
                    if(!state.tableData[i].YWBM) {
                        error = '第' + (i+1) + '行专业未选择！';
                        break;
                    }
                }
                if(error) {
                    ElMessage.warning(error);
                    return
                }

                let CBSSSZY = state.tableData.map((i) => i.YWMC).join(",");

                let params = {
                    YWFWGZ: state.formData.YWFWGZ,
                    CBSSSZY: CBSSSZY,
                    tableData: state.tableData
                };
                emit('submit', params)
            };

            const addRow = () => {
                state.tableData.push({
                    YWFWID: comFun.newId(),
                    FAID: props.modelValue.FAID,
                })
            };

            const delZy = (index) => {
                state.tableData.splice(index, 1)
            };

            const closeForm = () => {
                emit('close')
            };

            onMounted(() => {
                getZrzyList();
                state.tableData = props.modelValue.dwywfwTable;
                state.formData.YWFWGZ = props.modelValue.YWFWGZ;
            });

            return {
                ...toRefs(state),
                closeForm,
                saveData,
                addRow,
                ywbmChange,
                delZy
            }
        }

    })
</script>

<style scoped>

</style>
