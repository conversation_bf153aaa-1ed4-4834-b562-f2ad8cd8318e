<script setup>
import request from '@/utils/request'
import { ref, onMounted } from 'vue'
import { POBrowser } from "js-pageoffice"

const titleText = ref('');
const userName= ref("zhangsan");

onMounted(async () => {
    try {
        const response = await request({
            url: '/index',
            method: 'get',
        });
        titleText.value = response;
    } catch (error) {
        console.error('Failed to fetch title:', error);
    }
});

function open_pageoffice() {
	POBrowser.openWindow("/SetDrByUserWord2/Word", 'width=1200px;height=800px;',userName.value);
}
</script>

<template>
	<div class="Word">
		<form id="form1">
			<div style=" text-align:center;">
				<div>请选择登录用户：</div>
				<br />
				<select name="userName" v-model="userName">
					<option value="zhangsan">A部门经理</option>
					<option value="lisi" >B部门经理</option>
				</select><br /><br />
				<input type="button" @click="open_pageoffice()" value="打开文件" /><br /><br />
				<div style=" color:Red;">不同的用户登录后，在文档中可以编辑的区域不同</div>
			</div>
		</form>
	</div>
</template>

