<template>
  <el-form ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
<!--      <el-col :span="6" class="grid-cell">-->
<!--        <el-form-item label="" v-show="true" prop="SSDWMC">-->
<!--          <el-input style="width:100%;" placeholder="请输入所属单位" v-model="listQuery.SSDWMC" type="text" clearable>-->
<!--          </el-input>-->
<!--        </el-form-item>-->
<!--      </el-col>-->

<!--      <el-col :span="6" class="grid-cell">-->
<!--        <el-form-item label="" v-show="true" prop="XMMC">-->
<!--          <el-input style="width:100%;" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>-->
<!--          </el-input>-->
<!--        </el-form-item>-->
<!--      </el-col>-->

<!--      <el-col :span="4" class="grid-cell">-->
<!--        <div class="static-content-item" v-show="true" style="display: flex;padding-left: 10px;text-align: right;">-->
<!--          <el-button ref="button91277" @click="getDataList" type="primary">-->
<!--            <el-icon>-->
<!--              <Search/>-->
<!--            </el-icon>-->
<!--            查询-->
<!--          </el-button>-->
<!--          <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData">-->
<!--            <el-icon>-->
<!--              <Plus/>-->
<!--            </el-icon>-->
<!--            新增-->
<!--          </el-button>-->
<!--        </div>-->
<!--      </el-col>-->

    </el-row>

    <el-row ref="grid71868" :gutter="12" v-show="true">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper" v-show="true">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 200px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false" v-loading="loading"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
            <el-table-column v-if="true" prop="ROLE_NAME" label="角色名称" align="left" header-align="center"
                             :show-overflow-tooltip="true" min-width="120"></el-table-column>
            <el-table-column v-if="true" prop="ROLE_CODE" label="角色编码" align="left" header-align="center"
                             :show-overflow-tooltip="true" min-width="120"></el-table-column>
            <!-- <el-table-column v-if="true" prop="ORGNA_NAME" label="所属单位" align="left" header-align="center"
                             :show-overflow-tooltip="true" min-width="120"></el-table-column> -->
            <el-table-column v-if="true" prop="ROLE_DESC" label="角色描述" align="left" header-align="center"
                             :show-overflow-tooltip="true" min-width="120"></el-table-column>
            <el-table-column v-if="true" prop="CZ" label="操作" align="center" width="150">
              <template #default="scope">
                <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑人员</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog
        custom-class="lui-dialog"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="title"
        @closed="closeForm"
        top="1vh"
        z-index="1000"
        width="1500px">
      <div>
        <Ryxz :params="params" @closeDialog="closeForm"></Ryxz>
      </div>
    </el-dialog>
  </el-form>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
}
  from 'vue'

// 请求方法 get,post,put,del,downloadFile
import vsAuth from "@src/lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "@src/lib/comFun";
import Ryxz from "./ryxz";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import axiosUtil from "../../../lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {Search,Plus,Upload,Ryxz},
  props: {},
  setup() {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      rules: {},
      dialogVisible: false,
      params: {},
      loading: false,
      title:''

    })


    const getDataList = () => {
      console.log(state.userInfo)
      state.loading=true
      let params={
        ...state.listQuery,
        loginName: state.userInfo.userLoginName
      }
      axiosUtil.get('/backend/workFlow/yhpz/selectJsList', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
        state.loading=false
      });
    }


    // 编辑
    const editRow = (row) => {
      state.title=row.ROLE_NAME+"-角色人员配置"
      state.params = {editable: true, id: row.ROLE_ID, operation: 'edit'}
      state.dialogVisible = true
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }


    onMounted(() => {
      getDataList();
    })

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      editRow,
      closeForm
    }
  }
})

</script>

<style scoped>
.container {
  padding: 8px 5px;
}
:deep(.lui-card-form .el-form-item__label){
  border-bottom: none;
}
:deep(.el-cascader-menu__wrap.el-scrollbar__wrap) {
  height: 400px !important;
}
</style>
