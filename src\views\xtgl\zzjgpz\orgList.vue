<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0" size="default">
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="8" class="grid-cell">
          <div class="card">
            <div class="card-header">
               <span v-for="(item,index) in getOrgNameList(checkNode.ORGNA_FLOOR_NAME)">
                  <span class="orgName" @click="orgNameClick(index)">{{item}}</span>
                  <span style="margin-right: 3px;margin-left: 3px"
                        v-if="getOrgNameList(checkNode.ORGNA_FLOOR_NAME).length-1!==index">></span>
                </span>
            </div>
            <div class="card-body">
              <div style="height: 100%">
                <el-tree
                    ref="orgTree"
                    :expand-on-click-node="false"
                    :data="orgTreeList"
                    node-key="ORGNA_ID"
                    :props="{label:'ORGNA_NAME',children: 'children'}"
                    @node-click="checkOrg"/>

              </div>
            </div>
          </div>


        </el-col>
        <el-col :span="16" class="grid-cell">
          <div class="static-content-item" style="display: flex;height: 40px">
<!--            <el-button ref="button91277" @click="getDataList" type="primary"><el-icon><Search/></el-icon>查询</el-button>-->
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData"><el-icon><Plus/></el-icon>添加</el-button>
          </div>
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="childOrgList" height="calc(100vh - 180px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column v-if="true" prop="ORGNA_NAME" label="组织机构名称" align="left"
                               :show-overflow-tooltip="true" min-width="150"></el-table-column>
              <el-table-column v-if="true" prop="ORGNA_ORDER" label="排序号" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column v-if="true" prop="CZ" label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button  size="small" class="lui-table-button"  type="primary" @click="editRow(scope.row)">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>

      <el-dialog
          custom-class="lui-dialog"
          :close-on-click-modal="false"
          v-if="dialogVisible"
          v-model="dialogVisible"
          title="组织机构编辑"
          @closed="closeForm"
          z-index="1000"
          width="1200px">
        <div>
          <orgEdit v-if="dialogVisible" :params="params" @close="closeForm"></orgEdit>
        </div>
      </el-dialog>


    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import {Plus} from '@element-plus/icons-vue'
import orgEdit from "./orgEdit";
import comFun from "../../../lib/comFun";

export default defineComponent({
  name: '',
  components: {Plus,orgEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      childOrgList:[],
      orgList:[],
      orgTreeList:[],
      listQuery: {
        page: 1,
        size: 10
      },
      total: 0,
      params:{},
      rootNode:{
        ORGNA_ID:'0',
        ORGNA_NAME: '机构',
        ORGNA_FLOOR_NAME:'0',
        ORGNA_FLOOR_ID:'0'
      },
      checkNode:{},
      dialogVisible: false

    })
    const instance = getCurrentInstance()

    const getOrgList = (pid) => {
      let params={
        pid
      }
      axiosUtil.get('/backend/common/selectOrgListByPid', params).then((res) => {
        let data=res.data || []
        if(pid){
          state.childOrgList=data
        }else {
          state.orgList=JSON.parse(JSON.stringify(data))
          state.orgTreeList = treeData(data,'ORGNA_ID','PORGNA_ID','children',state.rootNode.ORGNA_ID)
          nextTick(()=>{
            instance.proxy.$refs['orgTree'].setCurrentKey(state.checkNode.ORGNA_ID,true)
          })
        }
      });
    }
    const checkOrg = (node) => {
      state.checkNode=node
      getOrgList(node.ORGNA_ID)
    }

    const addData = () => {
      state.params={
        editable: true,
        id: comFun.newId(),
        operation: 'add',
        pNode: state.checkNode
      }
      state.dialogVisible=true
    }

    const editRow = (row) => {
      state.params={
        editable: true,
        id: row.ORGNA_ID,
        operation: 'edit',
        pNode: state.checkNode
      }
      state.dialogVisible=true
    }

    const getOrgNameList = (name) => {
      let res=[]
      if(name){
        res=name.split(',')
      }
      for (let i = 0; i < res.length; i++) {
        if(res[i]===state.rootNode.ORGNA_ID){
          res[i]=state.rootNode.ORGNA_NAME
        }
      }
      return res
    }

    const orgNameClick = (index) => {
      let nodeId=state.checkNode.ORGNA_FLOOR_ID.split(',')[index]
      if(nodeId===state.rootNode.ORGNA_ID){
        state.checkNode=state.rootNode
      }else {
        state.checkNode=state.orgList.find(item=>item.ORGNA_ID===nodeId)
      }
      getOrgList(state.checkNode.ORGNA_ID)
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      state.dialogVisible=false
      getOrgList()
      getOrgList(state.checkNode.ORGNA_ID)
    }

    /**
     * 数据转树型结构
     * @param data
     * @param id id名称
     * @param parentId 父id名称
     * @param childName 生成的子节点名称
     * @param root 根节点的值
     * @returns {*}
     */
    const treeData = (data, id, parentId, childName, root) => {
      let cloneData = data
      return cloneData.filter((father) => {
        father[childName] = cloneData.filter((child) => {
          return father[id] === child[parentId]
        })
        return father[parentId] === root
      })
    }

    onMounted(() => {
      state.checkNode=state.rootNode
      getOrgList()
      getOrgList(state.rootNode.ORGNA_ID)
    })

    return {
      ...toRefs(state),
      indexMethod,
      checkOrg,
      getOrgNameList,
      closeForm,
      addData,
      editRow,
      orgNameClick

    }
  }

})
</script>

<style scoped>
.card{
  box-shadow: rgba(0, 0, 0, 0.12) 0 0 12px;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
}

.card-header{
  padding: 20px;
  border-bottom: 1px solid #eaeaf1;
}
.card-body{
  flex: 1;
  padding: 10px;
  overflow: auto;
}

.orgName{
  cursor: pointer;
}
.orgName:hover{
  color: #409EFF;
}
</style>
