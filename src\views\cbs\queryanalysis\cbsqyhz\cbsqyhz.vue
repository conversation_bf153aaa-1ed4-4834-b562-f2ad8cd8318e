<template>
  <el-form class="lui-page" label-position="left" size="default">
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="8" v-if="ZYBMS.length > 0">
        <el-form-item label="当前专业">
                    <span style="color: red">
                        {{ ZYBMS[ZYBMS.length - 1]['name'] }}
                    </span>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <div style="display:flex;">
          <el-button type="primary" v-if="ZYBMS.length > 0" @click="goParent">返回上一层</el-button>
          <el-button type="primary" @click="exportData"><el-icon><Upload/></el-icon>导出</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row ref="grid71868" :gutter="12">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)" v-loading="loading"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="ZYMC" label="专业名称" align="center"
                             :show-overflow-tooltip="true" width="160">
              <template #default="{row}">
                <el-button v-if="row.ZYMC!=='合计'" size="large" link type="primary" @click="goChildren(row)">{{row['ZYMC']}}</el-button>
              </template>
            </el-table-column>
            <el-table-column v-for="(item,index) in FWQYOptions" :prop="item.DMXX" :label="item.DMMC" :key="index" align="center"
                             :show-overflow-tooltip="true" min-width="160">
              <template #default="{row}">
                <el-button v-if="row[item.DMXX] && row.ZYMC!=='合计'" size="large" link type="primary" @click="goTeam(row,item.DMXX)">{{ row[item.DMXX] }}</el-button>
                <span v-else>{{row[item.DMXX] || 0}}</span>
              </template>
            </el-table-column>
          </el-table>

        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import {mixin} from "@core";
import tabFun from "@lib/tabFun";

export default defineComponent({
  name: '',
  components: {Upload},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
      },
      ZYBMS: [],
      tableData: [],

      FWQYOptions: [],
      loading: false
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        ZYBM: state.ZYBMS[state.ZYBMS.length-1]?.value
      }
      state.loading=true
      axiosUtil.get('/backend/sccbsgl/report/dwqyhz/selectDwqyhzList', params).then((res) => {
        let HJ={
          ZYMC: '合计'
        }
        state.tableData = res.data
        state.tableData.forEach(item=>{
          state.FWQYOptions.forEach(ii=>{
            if(HJ[ii.DMXX]){
              HJ[ii.DMXX]+=item[ii.DMXX] || 0
            }else {
              HJ[ii.DMXX]=item[ii.DMXX] || 0
            }
          })
        })
        state.tableData.unshift(HJ)
        state.loading=false
      });
    }

    const {vsuiEventbus} = mixin()
    const goTeam = (row, QY) => {
      vsuiEventbus.emit('reloadTeamList', {zybm: row.ZYBM, qy: QY})
      tabFun.addTabByRoutePath('企业信息查询', '/query-analysis/dwmx', {zybm: row.ZYBM, qy: QY},null);
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
      getDataList()
    }

    const goChildren = (row) => {
      state.ZYBMS.push({
        name: row.ZYMC,
        value: row.ZYBM
      })
      getDataList();
    }

    const goParent = () => {
      state.ZYBMS.splice(state.ZYBMS.length - 1, 1);
      getDataList();
    }

    onMounted(() => {
      getDMBData("FWQY", "FWQYOptions")
    })

    return {
      ...toRefs(state),
      getDataList,
      goChildren,
      goParent,
      goTeam

    }
  }

})
</script>

<style scoped>

</style>
