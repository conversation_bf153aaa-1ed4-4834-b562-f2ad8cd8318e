<template>
  <div>
    <el-form :model="modelValue" ref="vForm" :rules="rulesXm" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <el-row :gutter="0" class="grid-row">

        <el-col :span="24" class="grid-cell" v-if="hasXm">
          <el-form-item label="项目名称：" prop="XMID">
            <el-input v-model="modelValue.XMMC" type="text" placeholder="请选择" clearable :disabled="true">
              <template #append v-if="editable">
                <el-button @click="chooseCcxm">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="标题：" prop="BT">
            <el-input v-model="modelValue.BT" type="text" placeholder="请输入标题" clearable >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="创建人：" prop="CJRXM">
            <div style="margin-left: 10px">{{ modelValue.CJRXM }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="创建时间：" prop="CJSJ">
            <div style="margin-left: 10px">{{ modelValue.CJSJ }}</div>
          </el-form-item>
        </el-col>

<!--        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="招标文件：" prop="ZBWJ">
            <vsfileupload ref="vsfileupload" style="margin-left: 10px;min-width: 200px" :busId="params.id"
                          :key="params.id" v-model:files="XMfileList"
                          :editable="false" ywlb="zbwj"/>
            <el-button type="primary" @click="getXmzbWj" v-if="editable">提取</el-button>
          </el-form-item>
        </el-col>-->

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="承包商：" prop="ZBWJ">
            <div style="display: flex;gap: 10px;margin-bottom: 10px">
              <el-button size="default" type="primary" @click="addZbgs()" v-if="editable">添加</el-button>
              <el-button size="default" type="primary" @click="addZbgs()" v-if="editable">导入</el-button>
            </div>
            <el-table ref="datatable91634" :data="modelValue.qyList" height="200px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
              <el-table-column prop="CBSDWQC" label="公司名称" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="160">
                <template #default="{row}">
                  <el-input
                      v-model="row.CBSDWQC" placeholder="请输入公司全称" :disabled="!editable"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="MCJY" label="名称校验" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="160">
                <template #default="{row}">
                  <span>{{ row.MCJY }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="150" fixed="right" v-if="editable">
                <template #default="{row}">
                  <el-button size="small" class="lui-table-button" type="primary" @click="handleDelete(row)">删除
                  </el-button>
                </template>
              </el-table-column>

            </el-table>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogCCXMVisible"
        v-model="dialogCCXMVisible"
        title="项目选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <ccxmChoose @close="dialogCCXMVisible=false" @submit="getCcxmRes"/>
      </div>
    </el-dialog>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsfileupload from "@views/components/vsfileupload";
import ccxmChoose from "@views/bscc/xmbsccgl/ccxmChoose";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import vsAuth from "@lib/vsAuth";


export default defineComponent({
  name: '',
  components: {vsfileupload,ccxmChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: props.params.editable,
      hasXm: props.params.hasXm,
      userInfo: vsAuth.getAuthInfo().permission,
      rules: {
        BT: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      rulesXm: {
        XMID: [{
          required: true,
          message: '字段值不可为空',
        }],
        BT: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      XMfileList: [],
      DBfileList: [],

      dialogCCXMVisible: false
    })

    const chooseCcxm = () => {
      state.dialogCCXMVisible=true
    }

    const getCcxmRes = (value) => {
      props.modelValue.XMID=value.XMID
      props.modelValue.XMMC=value.XMMC
      props.modelValue.BT=value.XMMC+'-股权分析'
      state.dialogCCXMVisible=false
    }

    const instance = getCurrentInstance()
    const getXmzbWj = () => {
      if(!props.modelValue.XMID){
        ElMessage.warning('请选择查重项目')
        return
      }
      let params={
        XMID: props.modelValue.XMID,
        CCWJID: props.params.id
      }
      axiosUtil.get('/backend/bscc/bsccgl/extractXmzbwj',params).then(res=>{
        instance.proxy.$refs['vsfileupload'].loadFileList()
        ElMessage.success('提取成功')
      })
    }

    const deleteFile = (row) => {
      instance.proxy.$refs['dbvsfileupload'].handleRemove(row)
      instance.proxy.$refs['dbvsfileupload'].loadFileList()
    }

    const handleDelete = (row) => {
      props.modelValue.qyList.splice(props.modelValue.qyList.indexOf(row), 1)
    }

    const addZbgs = () => {
      const newRow = {
        CBSDWQC: '',
        MCJY: '待校验'
      }
      props.modelValue.qyList.push(newRow)
    }

    const previewFile = (row) => {
      instance.proxy.$refs['dbvsfileupload'].handlePreview(row)
    }

    const validateForm = () => {
      return new Promise((resolve, reject) => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            if(props.modelValue.qyList.length<2){
              reject('承包商信息中，最少两家承包商')
            }else {
              props.modelValue.qyList.forEach(item=>{
                if(item.MCJY !== '通过'){
                  reject('承包商名称存在未校验或非法')
                }
              })
              resolve(true)
            }
          } else {
            reject('请完善页面信息')
          }
        })
      })
    }

    const getCCQDList = () => {
      let CCQDList="";
      // state.XMfileList.forEach(ii=>{
      props.modelValue.qyList.forEach(iii=>{
          CCQDList = CCQDList + iii.CBSDWQC+",";
        })
      if(CCQDList){
        CCQDList = CCQDList.substring(0, CCQDList.lastIndexOf(","));
      }
      // })
      return CCQDList
    }

    const getCCQDDataList = () => {
      return props.modelValue.qyList
    }

    const setCCQDList = (data) => {
      props.modelValue.qyList=data
    }

    const getXmid = () => {
      return props.modelValue.XMID;
    }


    onMounted(() => {
    })

    return {
      ...toRefs(state),
      chooseCcxm,
      getCcxmRes,
      getXmzbWj,
      //deleteFile,
      handleDelete,
      addZbgs,
      //previewFile,
      validateForm,
      getCCQDList,
      getCCQDDataList,
      setCCQDList,
      getXmid,

    }
  }

})
</script>

<style scoped>

</style>
