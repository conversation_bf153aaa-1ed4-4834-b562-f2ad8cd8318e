<template>
  <el-form :model="formData" :rules="rules" ref="vForm" label-position="right" label-width="180px" size="default" class="lui-card-form">
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="项目类别" prop="PSBF_XMLB">
          <el-radio v-for="item in XMLBList" :key="item.DMXX" v-model="formData.PSBF_XMLB" 
            :label="item.DMXX" @change="onXMLBChange" style="margin-left: 10px;">
            {{ item.DMMC }}
          </el-radio>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="是否生产类" prop="PSBF_SFSCL">
          <el-radio v-for="item in SFSCLList" :key="item.DMXX" v-model="formData.PSBF_SFSCL" :label="item.DMXX" style="margin-left: 10px;">
            {{ item.DMMC }}
          </el-radio>
        </el-form-item>
      </el-col>
    </el-row>
    
    <!-- 当项目类别为"石油工程及其他"或"工程建设"时显示 -->
    <template v-if="formData.PSBF_XMLB === 'SYGC_QT' || formData.PSBF_XMLB === 'GCJS'">
      <el-row class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="报价方式" prop="PSBF_BJFS">
            <el-radio v-for="item in BJFSList" :key="item.DMXX" v-model="formData.PSBF_BJFS" :label="item.DMXX" style="margin-left: 10px;">
              {{ item.DMMC }}
            </el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="评分办法" prop="PSBF_PFBF">
            <el-radio v-for="item in PFBFList" :key="item.DMXX" v-model="formData.PSBF_PFBF" :label="item.DMXX" style="margin-left: 10px;">
              {{ item.DMMC }}
            </el-radio>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="评标方式" prop="PSBF_PBFS">
          <el-radio v-for="item in PBFSList" :key="item.DMXX" v-model="formData.PSBF_PBFS" :label="item.DMXX" style="margin-left: 10px;">
            {{ item.DMMC }}
          </el-radio>
        </el-form-item>
      </el-col>
    </el-row>
    
    <!-- 当项目类别为"石油工程-固定标价"时显示 -->
    <template v-if="formData.PSBF_XMLB === 'SYGC_GDBJ'">
      <el-row class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="同意下浮率" prop="PSBF_TYXFL">
            <el-input
              v-model="formData.PSBF_TYXFL"
              @input="onInputTYXFL"
              placeholder="请输入"
              style="width: 120px;"
            ></el-input>
            <span style="margin-left: 10px; font-size: large;">%</span>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="评分算法设置">
          <el-button type="link" @click="openScoreSetting" style="border: none;">
            <el-icon :size="18" style="color: green; margin-right: 5px;"><EditPen /></el-icon>算法设置
          </el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <el-row style="line-height: 50px;">
    <el-col :span="24" style="text-align: center; position: fixed; bottom: 9px; left: 0; right: 0; z-index: 100; background-color: white;">
      <el-button size="default" type="primary" @click="saveData">保存</el-button>
      <el-button size="default" @click="close">关闭</el-button>
    </el-col>
  </el-row>

  <el-dialog
    custom-class="lui-dialog"
    :close-on-click-modal="false"
    v-if="showDialog"
    v-model="showDialog"
    :title="dialogTitle"
    fullscreen>
    <zhpffEdit v-if="dialogLx === 'ZHPF'" :data="formData" @closeDialogpsbf="closeDialogpsbf" @confirmDialogData="confirmDialogData"></zhpffEdit>
    <jjcgpbEdit v-if="dialogLx === 'JJCGPB'" :data="formData" @closeDialogpsbf="closeDialogpsbf" @confirmDialogData="confirmDialogData"></jjcgpbEdit>
    <gdbjEdit v-if="dialogLx === 'SYGC_GDBJ'" :data="formData" @closeDialogpsbf="closeDialogpsbf" @confirmDialogData="confirmDialogData"></gdbjEdit>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import { EditPen } from '@element-plus/icons-vue'
import zhpffEdit from "./zhpffEdit.vue";
import jjcgpbEdit from "./jjcgpbEdit.vue";
import gdbjEdit from "./gdbjEdit.vue";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(["closeDialog","reload"]);
const formData = ref({});

const rules = ref({});
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 1000*60*60*24;
};

onMounted(() => {
  formData.value = Object.assign({}, props.data);
  getDmlbList();
});

// 保存
const vForm = ref(null);
const saveData = () => {
  if(!formData.value.PSBF_XMLB){
    ElMessage.warning('请先选择项目类别!');
    return;
  }
  if(!formData.value.PSBF_SFSCL){
    ElMessage.warning('请先选择是否生产类!');
    return;
  }
  if(!formData.value.PSBF_PBFS){
    ElMessage.warning('请先选择评标方式!');
    return;
  }

  if(formData.value.PSBF_XMLB == 'SYGC_GDBJ'){
    if(!formData.value.PSBF_TYXFL){
      ElMessage.warning('请先填写同意下浮率!');
      return;
    }
  }else{
    if(!formData.value.PSBF_BJFS){
      ElMessage.warning('请先选择报价方式!');
      return;
    }
    if(!formData.value.PSBF_PFBF){
      ElMessage.warning('请先选择评分办法!');
      return;
    }
  }

 //处理算法设置
  if(formData.value.PSBF_XMLB == 'SYGC_GDBJ' || formData.value.PSBF_PFBF == 'JJCGPB'){
    // 置空固定标价评标办法以外的算法设置数据
    formData.value.PFSF_JSBQZ = '';
    formData.value.PFSF_JGJCDF = '';
    formData.value.PFSF_BDBZSF = '';
    formData.value.PFSF_BDBZXF = '';
    formData.value.PFSF_JCDFQZ = '';
    formData.value.PFSF_BJKF = '';
    formData.value.PFSF_BJJF = '';
    formData.value.PFSF_BJSF = '';
    formData.value.PFSF_BJXF = '';
    formData.value.PFSF_BJFDNKF = '';
    formData.value.PFSF_BJFDNJF = '';
    formData.value.PFSF_BJFDWKF = '';
    formData.value.PFSF_BJFDWJF = '';
    formData.value.PFSF_XJZJQZ = '';
    formData.value.PFSF_DJZJQZ = '';
    formData.value.PFSF_DBBF = '';
    formData.value.PFSF_LBZBJS = '';
  }
  if(formData.value.PSBF_PFBF == 'ZHPF'){
    // 置空综合评分法,竞价采购评标办法以外的算法设置数据
    formData.value.PFSF_PBBF = '';
    formData.value.PFSF_GDJJDBBF = '';
    formData.value.PFSF_GDJJLBZBJS = '';
  }

  axiosUtil.post('/backend/xsgl/xswj/savePsbf', formData.value).then(res=>{
    if(res.data.success){
      emit('reload');
      emit('closeDialog');
      ElMessage.success('保存成功！');
    }else{
      ElMessage.error('保存失败！');
    }
  })
}

const BJFSList = ref([]);
const PBFSList = ref([]);
const PFBFList = ref([]);
const SFSCLList = ref([]);
const XMLBList = ref([]);
const getDmlbList = () => {
  axiosUtil.get('/backend/dmbpz/selectDmlbidList', { DMLBID: 'PSBF' })
    .then(res => {
      const dataList = res.data;
      // 清空之前的列表
      BJFSList.value = [];
      PBFSList.value = [];
      PFBFList.value = [];
      SFSCLList.value = [];
      XMLBList.value = [];
      // 遍历数据并分类
      dataList.forEach(item => {
        switch (item.DMLBID) {
          case 'PSBF_BJFS':
            BJFSList.value.push(item);
            break;
          case 'PSBF_PBFS':
            PBFSList.value.push(item);
            break;
          case 'PSBF_PFBF':
            PFBFList.value.push(item);
            break;
          case 'PSBF_SFSCL':
            SFSCLList.value.push(item);
            break;
          case 'PSBF_XMLB':
            XMLBList.value.push(item);
            break;
          default:
            break;
        }
      });
    })
    .catch(error => {
      console.error('获取数据失败:', error);
    });
}

const close = () => {
  emit('closeDialog');
}

const onInputTYXFL = (val) => {
  // 只允许输入数字和最多两位小数
  let newVal = String(val)
    .replace(/[^\d.]/g, '') // 只保留数字和小数点
    .replace(/^\./g, '') // 不允许以小数点开头
    .replace(/\.{2,}/g, '.') // 只保留第一个小数点
    .replace('.', '$#$').replace(/\./g, '').replace('$#$', '.'); // 只保留第一个小数点
  // 限制小数点后最多两位
  if (newVal.indexOf('.') > -1) {
    newVal = newVal.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
  }
  formData.value.PSBF_TYXFL = newVal;
};

// 打开评分算法设置
const showDialog = ref(false);
const dialogTitle = ref('');
const dialogLx = ref('');
const openScoreSetting = () => {
  if(!formData.value.PSBF_XMLB){
    ElMessage.warning('请先选择项目类别!');
    return;
  }
  if((formData.value.PSBF_XMLB == 'SYGC_QT' || formData.value.PSBF_XMLB == 'GCJS') && !formData.value.PSBF_PFBF){
    ElMessage.warning('请先选择评分办法!');
    return;
  }

  if(formData.value.PSBF_XMLB == 'SYGC_GDBJ'){
    dialogTitle.value = "算法设置-固定标价";
    dialogLx.value = formData.value.PSBF_XMLB;
    showDialog.value = true;
  } else if ((formData.value.PSBF_XMLB == 'SYGC_QT' || formData.value.PSBF_XMLB == 'GCJS') && formData.value.PSBF_PFBF == 'ZHPF') {
    dialogTitle.value = "算法设置-综合评分法";
    dialogLx.value = formData.value.PSBF_PFBF;
    showDialog.value = true;
  } else if ((formData.value.PSBF_XMLB == 'SYGC_QT' || formData.value.PSBF_XMLB == 'GCJS') && formData.value.PSBF_PFBF == 'JJCGPB') {
    dialogTitle.value = "算法设置-竞价采购评标办法";
    dialogLx.value = formData.value.PSBF_PFBF;
    showDialog.value = true;
  }
};

const closeDialogpsbf = () => {
  showDialog.value = false;
}

const confirmDialogData = (dialogData) => {
  showDialog.value = false;
  formData.value = dialogData
}

const onXMLBChange = () => {
  if (formData.value.PSBF_XMLB === 'SYGC_GDBJ') {
    formData.value.PSBF_BJFS = '';
    formData.value.PSBF_PFBF = '';
  } else {
    formData.value.PSBF_TYXFL = '';
  }
};

defineExpose({});

</script>

<style scoped>
.lui-card-form {
  height: calc(100vh - 160px);
  overflow-y: auto;
  padding-bottom: 80px; /* 新增，避免底部fixed按钮遮挡内容 */
}

::v-deep .el-input__inner{
  border: 1px solid gray;
  text-align: center;
}

</style>