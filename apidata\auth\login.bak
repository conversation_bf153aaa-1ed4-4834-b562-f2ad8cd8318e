﻿{
    "meta":{
        "success":true,
        "status":200,
        "message":""
    },
    "data":{
        "realName": "管理员",
        "userName": "admin",
        "passwd": "",
        "token": "",
        "orgnaName": "中国石油化工股份有限公司",
        "resList": [
            {
                "resId": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
                "resName": "Dashboard",
                "iconClass": "fa fa-tachometer",
                "resPath": "/dashboard"
            },
            {
                "resId": "9B45ADRVGSD45GDED4FTTWSSW128JHFA",
                "resPid": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
                "resName": "控制台",
                "iconClass": "fa fa-podcast",
                "resPath": "/dashboard/workplace"
            },
            {
                "resId": "9B45ADRVGSD45GD4FTGSW128JHSRGDW",
                "resPid": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
                "resName": "分析页",
                "iconClass": "fa fa-pie-chart",
                "resPath": "/dashboard/analysis"
            },
            {
                "resId": "9B45ADRSDFRG28JGRT56GWDFSDEFW33F",
                "resPid": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
                "resName": "监控页",
                "iconClass": "fa fa-globe",
                "resPath": "/dashboard/monitor"
            },
            {
                "resId": "SD4FV7GFDFT53JMD285H7B3EWDH90X",
                "resName": "示例页面",
                "iconClass": "fa fa-wpforms"
            },
            {
                "resId": "SFDS5A7D8C3DDFT535GFSD285HH23REW",
                "resPid": "SD4FV7GFDFT53JMD285H7B3EWDH90X",
                "resName": "表单页面",
                "iconClass": "fa fa-wpforms"
            },
            {
                "resId": "9B4DDF44NY75CNWD5GFDGJKLNGTTW456",
                "resPid": "SFDS5A7D8C3DDFT535GFSD285HH23REW",
                "resName": "简单表单",
                "iconClass": "fa fa-wpforms",
                "resPath": "/example-pages/form/simple-form"
            },
            {
                "resId": "FDDFDFD55ESNY75CNWD5GFDER4RER5WWR",
                "resPid": "SFDS5A7D8C3DDFT535GFSD285HH23REW",
                "resName": "复杂表单",
                "iconClass": "fa fa-wpforms",
                "resPath": "/example-pages/form/complex-form"
            },
            {
                "resId": "D8D8FJKDKEKFJK75CNWD5D8DJFEKJE9D9",
                "resPid": "SFDS5A7D8C3DDFT535GFSD285HH23REW",
                "resName": "分步表单",
                "iconClass": "fa fa-wpforms",
                "resPath": "/example-pages/form/step-form"
            },
            {
                "resId": "FE99FD9FID9F3DDFT535FD8D8FS88S8DE",
                "resPid": "SD4FV7GFDFT53JMD285H7B3EWDH90X",
                "resName": "列表页面",
                "iconClass": "fa fa-list-alt"
            },
            {
                "resId": "FDDFDFD55ESNY75CNWD5GFDER4RRWEI0EER3",
                "resPid": "FE99FD9FID9F3DDFT535FD8D8FS88S8DE",
                "resName": "简单列表",
                "iconClass": "fa fa-list-alt",
                "resPath": "/example-pages/list/simple-list"
            },
            {
                "resId": "FE8E8D55ESNY75CNWD5GFDERFE3IFIEINNE",
                "resPid": "FE99FD9FID9F3DDFT535FD8D8FS88S8DE",
                "resName": "复杂列表",
                "iconClass": "fa fa-list-alt",
                "resPath": "/example-pages/list/complex-list"
            },
            {
                "resId": "FFEIJKH3J3K2JKJ2KL3JG9F9G9I9G8FGJI",
                "resPid": "SD4FV7GFDFT53JMD285H7B3EWDH90X",
                "resName": "结果页面",
                "iconClass": "fa fa-diamond"
            },
            {
                "resId": "KXISI45C8SWENM3J4J4KMS9SI234KRDKJK2P",
                "resPid": "FFEIJKH3J3K2JKJ2KL3JG9F9G9I9G8FGJI",
                "resName": "成功页",
                "iconClass": "iconfont icon-fort-awesome",
                "resPath": "/example-pages/result/success"
            },
            {
                "resId": "C7DKSJEHEWRJKJ4H5ND9C9FD9SKS9CKSLDFJ",
                "resPid": "FFEIJKH3J3K2JKJ2KL3JG9F9G9I9G8FGJI",
                "resName": "失败页",
                "iconClass": "iconfont icon-fort-awesome",
                "resPath": "/example-pages/result/fail"
            },
            {
                "resId": "DKECHHEMNEMSKGIUEHFJCUD4HJDBEJ3U3D",
                "resPid": "SD4FV7GFDFT53JMD285H7B3EWDH90X",
                "resName": "异常页面",
                "iconClass": "fa fa-pagelines"
            },
            {
                "resId": "DKLEKJGJDLDKEI4K5JK6JKDFIDK3KJE9F93J",
                "resPid": "DKECHHEMNEMSKGIUEHFJCUD4HJDBEJ3U3D",
                "resName": "403",
                "iconClass": "iconfont icon-fort-awesome",
                "resPath": "/example-pages/exception/403"
            },
            {
                "resId": "KEIDKEGHLAOQURNFDJKSJKEEKDJFKEKJFJKE",
                "resPid": "DKECHHEMNEMSKGIUEHFJCUD4HJDBEJ3U3D",
                "resName": "404",
                "iconClass": "iconfont icon-fort-awesome",
                "resPath": "/example-pages/exception/404"
            },
            {
                "resId": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resPid": "SD4FV7GFDFT53JMD285H7B3EWDH90X",
                "resName": "扩展组件",
                "iconClass": "fa fa-microchip"
            },
            {
                "resId": "KLJSAKJEIGHHBNXCMNDJSHFLWSIEHJFHKEWH",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "打印插件",
                "iconClass": "fa fa-print",
                "resPath": "/example-pages/extension/printer"
            },
            {
                "resId": "KFJAKSEJGHKJGVSKJHEHGJKHSFJDKHFIEMJD",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "excel插件",
                "iconClass": "fa fa-file-excel-o",
                "resPath": "/example-pages/extension/excel"
            },
            {
                "resId": "KLJEKGJKHSKJNMNCVXHBESWFTJRIOTUIEJGF",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "拖拽插件",
                "iconClass": "fa fa-hand-paper-o",
                "resPath": "/example-pages/extension/dragsort"
            },
            {
                "resId": "KJTGHEWIVISNJELWGJOXSHKSJGXIEOJGSKJE",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "地图插件",
                "iconClass": "fa fa-map-o",
                "resPath": "/example-pages/extension/map"
            },
            {
                "resId": "KSDGJXMNCBHJWGFKJHJKSDHJFHQWLGOJSOJEV",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "富文本框",
                "iconClass": "fa fa-ship",
                "resPath": "/example-pages/extension/editor"
            },
            {
                "resId": "KDJSFKLEJGKJEKJWIJVKCMXNBKJSKJGKEGES",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "标签",
                "iconClass": "fa fa-tags",
                "resPath": "/example-pages/extension/tag"
            },
            {
                "resId": "KLSJAGKLEJGHMCVNMBHRJEURYHUITYBNSJNWL",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "消息提示",
                "iconClass": "fa fa-comments",
                "resPath": "/example-pages/extension/message"
            },
            {
                "resId": "KDJAGKENGMHJGJSHGUXHJSWKEHGFKHEGJWEJ",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "步骤条",
                "iconClass": "fa fa-leaf",
                "resPath": "/example-pages/extension/steps"
            },
            {
                "resId": "FDKAGJEKGJKNVMXNBJHJDFGHJDJKZHOIEWRG",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "空状态",
                "iconClass": "fa fa-star-half-o",
                "resPath": "/example-pages/extension/empty"
            }
            ,
            {
                "resId": "KJGXMNMVUYHUBENMBHNXDJHJKVHNXWJRHGFJC",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "滚动数字",
                "iconClass": "fa fa-shield",
                "resPath": "/example-pages/extension/count-up"
            }
            ,
            {
                "resId": "KDSJGKENGMXJBVNSMDFVGNJHJEHWGJMNMSXNN",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "树形下拉",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/extension/tree-select"
            },

            {
                "resId": "LKGIEEGUUYUGHKJFDBNNCBHHJFDKHGEURHGUH",
                "resPid": "KDSJAKLEJFIEJKGIUEHFSKJEKJVIGJRFKS",
                "resName": "文件列表",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/extension/file"
            },
            {
                "resId": "LKJIJEJGLKEJGKJEWIGJKREJGKRLEJGKJRKJK",
                "resName": "个人中心",
                "iconClass": "fa fa-microchip"
            },
            {
                "resId": "LKJGIJEGKJGFDGKRLENCMBNJDKSKLJGEIJKGC",
                "resPid": "LKJIJEJGLKEJGKJEWIGJKREJGKRLEJGKJRKJK",
                "resName": "个人资料",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/user/profile"
            },
            {
                "resId": "KGUYRIEUGYURYGUHDJHBUGERJGHJDGFUHEJRHSW",
                "resPid": "LKJIJEJGLKEJGKJEWIGJKREJGKRLEJGKJRKJK",
                "resName": "我的消息",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/user/message"
            },
            {
                "resId": "YUIERYUHJFDHGVBJDHGKHUHJGFJDKHJDHGEIRUGW",
                "resName": "系统管理",
                "iconClass": "fa fa-microchip"
            },
            {
                "resId": "KGUIEUGUFHBJVBJHFGRUGHKJHGJSHFJDSHFJDSHGJJ",
                "resPid": "YUIERYUHJFDHGVBJDHGKHUHJGFJDKHJDHGEIRUGW",
                "resName": "用户管理",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/system/user"
            },
            {
                "resId": "UREGFHBCKHJGHEIURWIOQJHGJHGJSNVBCXMNHJDHJS",
                "resPid": "YUIERYUHJFDHGVBJDHGKHUHJGFJDKHJDHGEIRUGW",
                "resName": "菜单管理",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/system/menu"
            },
            {
                "resId": "UIGHEUERGFYHJFDHGSHGJKHGJHDJGHSDKJHSJHHJHJ",
                "resPid": "YUIERYUHJFDHGVBJDHGKHUHJGFJDKHJDHGEIRUGW",
                "resName": "机构管理",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/system/organization"
            },
            {
                "resId": "JHGURUEYTUHGJHBHFDGSDGAEREWUTIUGOFDUHOJBOH",
                "resPid": "YUIERYUHJFDHGVBJDHGKHUHJGFJDKHJDHGEIRUGW",
                "resName": "批量选择",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/system/choose"
            },
            {
                "resId": "KJGIREUGOTIUYNBJJCHBKFDUGYEIGYUISYUDYWIRGI",
                "resPid": "YUIERYUHJFDHGVBJDHGKHUHJGFJDKHJDHGEIRUGW",
                "resName": "树形表格",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/system/table"
            },
            {
                "resId": "KGRUEYUIFDHJKBMCHJFHGUETGUHFJKHGSKFUHJRHGJ",
                "resPid": "YUIERYUHJFDHGVBJDHGKHUHJGFJDKHJDHGEIRUGW",
                "resName": "案卷调整",
                "iconClass": "fa fa-tree",
                "resPath": "/example-pages/system/document"
            },
            {
                "resId": "FDDFFF8CE6GSSDDAFRRC379DFS1123FQ",
                "resName": "框架二开",
                "iconClass": "fa fa-thumbs-o-up",
                "resPath": "/vsui-sdk/index"
            },
            {
                "resId": "S5FDD8SS6GYCDFSSAWC37911DFWQDFDA",
                "resPid": "FDDFFF8CE6GSSDDAFRRC379DFS1123FQ",
                "resName": "API使用",
                "iconClass": "fa fa-bomb",
                "resPath": "/vsui-sdk/vsui-api/index"
            },
            {
                "resId": "SE3WWSFFWF56FFAGFDSAFDEEFFGH8S",
                "resPid": "S5FDD8SS6GYCDFSSAWC37911DFWQDFDA",
                "resName": "API定义",
                "iconClass": "fa fa-bomb",
                "resPath": "/vsui-sdk/vsui-api/define"
            },
            {
                "resId": "SE3456YFGYCDFSDFF56DFWQFGBG0PDSA",
                "resPid": "S5FDD8SS6GYCDFSSAWC37911DFWQDFDA",
                "resName": "头部操作API",
                "iconClass": "fa fa-bomb",
                "resPath": "/vsui-sdk/vsui-api/header"
            },
            {
                "resId": "DFR5DH69S6GHFSSAWC37911JUC341D2S",
                "resPid": "S5FDD8SS6GYCDFSSAWC37911DFWQDFDA",
                "resName": "工作区API",
                "iconClass": "fa fa-bomb",
                "resPath": "/vsui-sdk/vsui-api/content"
            },
            {
                "resId": "S34VYCDFSSGNM9911DDCD4FSCDHYILSE",
                "resPid": "FDDFFF8CE6GSSDDAFRRC379DFS1123FQ",
                "resName": "UI扩展",
                "iconClass": "fa fa-coffee",
                "resPath": "/vsui-sdk/vsui-ui-extend/index"
            },
            {
                "resId": "SDSSVSGFGNMGFG1DDGEWFJHDHJHG89SS",
                "resPid": "S34VYCDFSSGNM9911DDCD4FSCDHYILSE",
                "resName": "扩展坞",
                "iconClass": "fa fa-crosshairs",
                "resPath": "/vsui-sdk/vsui-ui-extend/dock"
            },
            {
                "resId": "ASSVB6CGFDJLTYNMTB1DD84FSCFSSCVE",
                "resPid": "S34VYCDFSSGNM9911DDCD4FSCDHYILSE",
                "resName": "扩展演示",
                "iconClass": "fa fa-crosshairs",
                "resPath": "/vsui-sdk/vsui-ui-extend/demo"
            },
            
            {
                "resId": "SDE6BFS11DDCD4FSFGJ89LD4FGSCV6LI",
                "resPid": "FDDFFF8CE6GSSDDAFRRC379DFS1123FQ",
                "resName": "Event监听",
                "iconClass": "fa fa-deaf",
                "resPath": "/vsui-sdk/vsui-event/index"
            },
            {
                "resId": "SDBFS1FDFRGHBHHH89LDKLGSFGFFE4CV",
                "resPid": "SDE6BFS11DDCD4FSFGJ89LD4FGSCV6LI",
                "resName": "事件定义",
                "iconClass": "fa fa-hand-rock-o",
                "resPath": "/vsui-sdk/vsui-event/define"
            },
            {
                "resId": "SFEG4FSFGJFGGBH8FDGFD6AFV21FL0XS",
                "resPid": "SDE6BFS11DDCD4FSFGJ89LD4FGSCV6LI",
                "resName": "事件订阅",
                "iconClass": "fa fa-snowflake-o",
                "resPath": "/vsui-sdk/vsui-event/listen"
            },
            {
                "resId": "SDEFM991FFRT6FSRTREDFSBNL9L0GSD",
                "resPid": "FDDFFF8CE6GSSDDAFRRC379DFS1123FQ",
                "resName": "UI主题色",
                "iconClass": "fa fa-th-large",
                "resPath": "/vsui-ui/element-plus/themetest"
            },
            {
                "resId": "SE5VM991FS4FSRDGSBNL9L0GDFHJ8",
                "resPid": "FDDFFF8CE6GSSDDAFRRC379DFS1123FQ",
                "resName": "胜软VSNPM镜像",
                "iconClass": "fa fa-th-large",
                "resPath": "/loadPage?url=http://10.68.7.155:7002/"
            },
            {
                "resId": "SESDE1FS4GGRDGSBTGGDFHJTGEFGHEX",
                "resPid": "FDDFFF8CE6GSSDDAFRRC379DFS1123FQ",
                "resName": "VSNPM上的vsui",
                "iconClass": "fa fa-th-large",
                "resPath": "/loadPage?url=http://10.68.7.155:7002/browse/keyword/@VSUI"
            },
            {
                "resId": "9B45A7D8C37A4CA7BD46C285612318E3",
                "resName": "关于框架",
                "iconClass": "fa fa-heart",
                "resPath": "/aboutme/aboutme-index"
            },
            {
                "resId": "3SRVGT6HMKLC2856FDR5VBGHYWSWD",
                "resPid": "9B45A7D8C37A4CA7BD46C285612318E3",
                "resName": "使用说明",
                "iconClass": "fa fa-wpexplorer",
                "resPath": "/aboutme/aboutme-index"
            },
            {
                "resId": "12TYDDEGSRFFF56FGBGTDDSEFGTF87K",
                "resPid": "9B45A7D8C37A4CA7BD46C285612318E3",
                "resName": "NPM使用",
                "iconClass": "fa fa-grav",
                "resPath": "/aboutme/vsnpm"
            }
        ] 
    }
}