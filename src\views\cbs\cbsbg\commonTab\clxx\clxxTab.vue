<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        :model="tableData"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        class="lui-page"
        :disabled="!editable">
<!--                  <div style="color: red">-->
<!--                    {{ BGXX }}-->
<!--                  </div>-->
      <el-table ref="dataTable" :data="[...tableData,...getDelRow()]" height="500px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column v-for="(item,index) in tableColumn" :key="index"
                         :prop="item.prop" :label="item.label" :align="item.align" :width="item.width"
                         :min-width="item.minWidth"
                         :show-overflow-tooltip="item.showOverflowTooltip">
          <template #default="{row,$index}" v-if="item.slot==='fileList'">
            <vsfileupload
                :maxSize="10"
                :index="$index"
                :ref="addRefs($index)"
                :editable="false"
                :busId="row.CLID"
                :key="row.CLID"
                ywlb="CLFJ"
                busType="dwxx"
                :limit="100"
            ></vsfileupload>
          </template>
          <template #default="{row,$index}" v-else-if="item.slot==='format'">
            {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='change'">
            <div :class="{dataChange : isChangeT(row.CLWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.CLWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.CLWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span>{{ row[item.prop] }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='formatChange'">
            <div :class="{dataChange : isChangeT(row.CLWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.CLWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.CLWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span> {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='info'">
            <el-tooltip :content="row[item.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right" v-if="editable">
          <template #default="{row,$index}">
            <div v-if="tableRowClassName({row})!=='warning-row'">
              <el-button v-if="editable" class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="editRow(row, $index)">查看</el-button>
              <el-button v-if="editable" class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button v-if="editable" class="lui-table-button" @click="deleteRow(row, $index)">删除</el-button>
            </div>
            <div v-else>
              已删除
            </div>
          </template>
        </el-table-column>

      </el-table>

    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        title="查看车辆信息"
        v-model="editVisible"
        width="1200px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        destroy-on-close>
      <clEdit :editData="editData" @close="editVisible = false" :editable="false"/>
    </el-dialog>


  <el-dialog
      custom-class="lui-dialog"
      title="信息项选择"
      v-model="chooseVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      top="50px"
      width="1200px">
    <clxxXz
        :key="editIndex"
        :currentRow="currentRow"
        @updateChooseData="updateChooseData"
        @updateEditData="updateEditData"
        @close="chooseVisible = false"
        :TYXYDM="TYXYDM"
    />
  </el-dialog>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, ref, watch} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import clEdit from "@views/cbs/templateManagement/DataTemplateManagement/clxx/clEdit.vue";
import clxxXz from "@views/cbs/templateManagement/DataTemplateManagement/clxx/clxx_xz";
import vsfileupload from "@views/components/vsfileupload";
import {InfoFilled} from "@element-plus/icons-vue";
import comFun from "@lib/comFun";

export default defineComponent({
  name: '',
  components: {vsfileupload, InfoFilled,clEdit,clxxXz},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    TYXYDM: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      chooseVisible:false,
      tableData: [],
      tableColumn: [
        {
          label: "专业名称",
          prop: "ZYMC",
          align: "center",
          showOverflowTooltip: true,
          width: 100,
        },
        {
          label: "业务要求",
          prop: "YWYQ",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "录入资料说明",
          prop: "LRZLSM",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "信息项",
          prop: "XXXMC",
          align: "center",
          showOverflowTooltip: true,
          width: 150,
        },

        {
          label: "车辆名称",
          prop: "CLMC",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "车型",
          prop: "CLLXMC",
          align: "center",
          width: 120,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "吨(座)位",
          prop: "DZW",
          align: "center",
          width: 120,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "车牌号",
          prop: "CPH",
          align: "center",
          width: 120,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "品牌型号",
          prop: "PPXH",
          align: "center",
          width: 120,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "规格型号",
          prop: "GGXH",
          align: "center",
          width: 120,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "购置时间",
          prop: "GZSJ",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "备注",
          prop: "BZ",
          align: "left",
          headerAlign: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "附件",
          prop: "fileList",
          headerAlign: "center",
          align: "left",
          slot: "fileList",
          width: 200,
        },
      ],
      // 弹窗
      editVisible: false,
    })

    const currentRow = ref({});
    const refs = ref([]);
    const addRefs = (id) => {
      return (el) => {
        refs.value[id] = el;
      };
    }
    const updateChooseData = (val) => {
      changeData(currentRow.value, val, editIndex.value, false)
    };

    const changeData = (oldRow, newRow, index, visible) => {
      let params = {
        newId: oldRow.CLID,
        oldId: newRow.CLZSJID,
        cover: true
      }

      axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
        oldRow.CLZSJID = newRow.CLZSJID
        oldRow.CPH = newRow.CPH
        oldRow.CLMC = newRow.CLMC
        oldRow.SCCJ = newRow.SCCJ
        oldRow.PPXH = newRow.PPXH
        oldRow.CLLXMC = newRow.CLLXMC
        oldRow.DZW = newRow.DZW
        oldRow.GGXH = newRow.GGXH
        oldRow.DJH = newRow.DJH
        oldRow.FDJBH = newRow.FDJBH
        oldRow.NJRQ = newRow.NJRQ
        oldRow.YXQJSRQ = newRow.YXQJSRQ
        oldRow.NJJG = newRow.NJJG
        oldRow.GDSYNX = newRow.GDSYNX
        oldRow.GZSJ = newRow.GZSJ
        oldRow.BDBH = newRow.BDBH
        oldRow.BDJG = newRow.BDJG
        oldRow.BZ = newRow.BZ
        oldRow.CLLXDM = newRow.CLLXDM
        refs.value[editIndex.value].loadFileList()
        state.chooseVisible = visible;
      })
    }

    watch(() => state.tableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])
    const initBgInfo = () => {
      if (state.tableData && props.resultTableData) {
        let res = []
        let BGQBS = props.resultTableData.map(i => i.CLWYBS)
        let BGHBS = state.tableData.map(i => i.CLWYBS)
        let allBS = new Set([...BGQBS, ...BGHBS])
        allBS.forEach(item => {
          if (BGQBS.includes(item) && BGHBS.includes(item)) {
            let BGQXX = props.resultTableData.find(i => i.CLWYBS === item)
            let BGHXX = state.tableData.find(i => i.CLWYBS === item)

            let isBg = false
            let dbsj = []
            let checkProp = ['CPH', 'CLMC', 'SCCJ', 'PPXH', 'CLLXMC', 'DZW', 'GGXH', 'DJH', 'FDJBH',
              'NJRQ', 'YXQJSRQ', 'NJJG', 'GDSYNX', 'GZSJ', 'BDBH', 'BDJG','BZ','CLLXDM']
            checkProp.forEach(ii => {
              if ((BGQXX[ii] || '') !== (BGHXX[ii] || '')) {
                dbsj.push({
                  BGQ: BGQXX[ii] || '',
                  BGH: BGHXX[ii] || '',
                  ZDMC: ii
                })
                isBg = true

              }
            })
            if (isBg) {
              res.push({
                YWLX: 'CLXX',
                BGZT: 'BG',
                WYBS: item,
                BGXQ: dbsj
              })
            }

          } else if (BGQBS.includes(item) && !BGHBS.includes(item)) {
            res.push({
              YWLX: 'CLXX',
              BGZT: 'SC',
              WYBS: item,
            })
          } else if (!BGQBS.includes(item) && BGHBS.includes(item)) {
            res.push({
              YWLX: 'CLXX',
              BGZT: 'XZ',
              WYBS: item,
            })
          }
        })
        BGXX.value = res
      }

    }

    const getDelRow = () => {
      if(props.resultTableData && state.tableData){
        return props.resultTableData.filter(item => !state.tableData.find(ii => ii.CLWYBS === item.CLWYBS))
      }else {
        return []
      }
    }

    const tableRowClassName = ({row,index}) => {
      let info=BGXX.value.find(ii=>ii.WYBS===row.CLWYBS) || {}
      if (info.BGZT==='XZ'){
        return "success-row"
      }else if(info.BGZT==='SC'){
        return "warning-row"
      }

    }

    const isChangeT = (CLWYBS, prop) => {
      let info = BGXX.value.find(item => item.WYBS === CLWYBS && item.BGZT === 'BG')
      if (info) {
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if(res){
          res.BGMS = `${res.BGQ} 变更为 ${res.BGH}`
          return res
        }
      }
      return null
    }


    const updateEditData = (row) => {
      state.tableData.forEach((item, index) => {
        if (item.CLZSJID === row.CLZSJID) {
          changeData(item, row, index, true)
        }
      })
    }

    const chooseRow = (row, index) => {
      currentRow.value = row;
      editIndex.value = index;
      state.chooseVisible = true;
    };


    watch(() => props.defaultData, (val) => {
          if (val) {
            val.forEach((x) => {
              const UUID = comFun.newId()
              x.CLID = x.CLID || UUID;
              x.CLWYBS = x.CLWYBS || UUID;
            });
          }
          state.tableData = val;
        },
        {
          immediate: true,
        }
    );

    const editIndex = ref(0);
    const editData = ref({});
    const editRow = (row, index) => {
      editData.value = row;
      editIndex.value = index;
      state.editVisible = true;
    };


    const insertRow = (row, index) => {
      const UUID = comFun.newId()
      state.tableData.splice(index + 1, 0, {
        CLID: UUID,
        CLWYBS: UUID,
        ZYMC: row.ZYMC,
        ZYBM: row.ZYBM,
        XXXMC: row.XXXMC,
        MBMXID: row.MBMXID,
        YWYQ: row.YWYQ,
        LRZLSM: row.LRZLSM,
        XXX: "",
        XMMC: "",
        JCQK: ""
      });
    };

    const deleteRow = (row, index) => {
      ElMessageBox.confirm("是否删除此条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        state.tableData.splice(index, 1);
        ElMessage({
          message: "删除成功!",
          type: "success",
        });
      }).catch(() => {
      });
    };

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      getDelRow,
      tableRowClassName,
      addRefs,
      isChangeT,
      chooseRow,
      insertRow,
      deleteRow,
      editRow,
      editIndex,
      currentRow,
      updateChooseData,
      updateEditData,
      editData,
      BGXX

    }
  }

})
</script>

<style scoped>

</style>
