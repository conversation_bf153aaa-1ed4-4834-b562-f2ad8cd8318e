<template>
  <div v-loading="loading" class="container">
    <el-form
      :model="form"
      ref="vForm"
      :rules="vFormRules"
      label-position="left"
      label-width="140px"
      size="default"
      class="lui-card-form"
      style="height: calc(100vh - 238px)"
    >
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="企业全称" prop="CBSDWQC">
            <el-input
                v-model="form.CBSDWQC"
                disabled
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item label="队伍名称" prop="DWMC">
            <el-input
                v-model="form.DWMC"
                disabled
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item label="申请服务范围" prop="ZYMC">
            <el-input v-model="form.ZYMC" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item label="删除专业" prop="DELZYMC">
            <el-select v-model="form.DELZYARR" 
                multiple
                :disabled="!editable"
                placeholder="请选择">
              <el-option
                  v-for="item in zyList"
                  :key="item.zybm"
                  :label="item.zymc"
                  :value="item.zybm"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="align-items: center;justify-content: center;display: flex;background-color: white;height: 70px" v-if="editable">
      <el-button size="default" type="success" @click="saveData('save')">保存</el-button>
      <el-button size="default" type="primary" @click="saveData('submit')">提交</el-button>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw} from "vue";
import vsFileUploadTable from "@views/components/vsFileUploadTable";
import {getCommonSelectDMB} from "@src/api/common";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import {ElLoading, ElMessage} from "element-plus";
import {auth, mixin} from "@core";
import tabFun from "@lib/tabFun";

export default defineComponent({
  name: 'dwzyscEdit',
  components: {
    vsFileUploadTable
  },
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      form: {},
      zyList: []
    })
    const initForm = () => {
      let params = {
        DWYWID: props.params.id,
      }
      state.loading = true;
      axiosUtil.get('/backend/sccbsgl/dwxxbg/queryDwzyscForm', params).then((res) => {
        state.form = res.data.form;
        if(res.data.form.DELZYBM){
          state.form.DELZYARR = state.form.DELZYBM.split(',');
        }
        let bmArr = state.form.ZYBM.split(',');
        let mcArr = state.form.ZYMC.split(',');
        state.zyList = [];
        for(let i=0;i<bmArr.length;i++){
          state.zyList.push({
            zybm: bmArr[i],
            zymc: mcArr[i],
          });
        }
        state.loading = false;
      })
    }

    const {vsuiEventbus} = mixin();

    const saveData = (type) => {
      let params = {};
      if(!state.form.DELZYARR || state.form.DELZYARR.length === 0){
        ElMessage.warning('请选择要删除的专业！');
        return;
      }
      let bgmx = [];
      for(let i=0;i<state.form.DELZYARR.length;i++){
        for(let k=0;k<state.zyList.length;k++){
          if(state.form.DELZYARR[i] === state.zyList[k].zybm){
            bgmx.push({
              BYZD1: state.zyList[k].zybm,
              BYZD2: state.zyList[k].zymc,
            });
          }
            
        }
      }
      params.bgmx = bgmx;
      params.DWYWID = state.form.DWYWID;
      params.BGJLID = state.form.BGJLID;
      params.businessId = props.params.id;
      state.loading = true;
      axiosUtil.post('/backend/sccbsgl/dwxxbg/saveDwzysc', params).then(res => {
        ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`);
        state.loading = false;
      })
    }

    onMounted(() => {
      if (!props.params) {
        ElMessage.warning('参数缺失请关闭该页签重新打开！')
        return;
      }
      initForm()
    })

    return {
      ...toRefs(state),
      saveData
    }
  }

})
</script>

<style scoped>
.container {
  height: calc(100vh - 100px);
  background-color: #fff;
}
</style>
