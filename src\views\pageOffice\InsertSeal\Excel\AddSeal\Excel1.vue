<template>
	<div class="Excel">
	  <div style="height: 800px; width: auto" v-html="poHtmlCode" />
	</div>
</template>

<script>
	import axios from 'axios';
	  export default{
	    name: 'Excel',
	    data(){
	      return {
	        poHtmlCode: '',
	
	      }
	    },
	    created: function(){
	      //由于vue中的axios拦截器给请求加token都得是ajax请求，所以这里必须是axios方式去请求后台打开文件的controller
	      axios.post("/api/InsertSeal/Excel/AddSeal/Excel1").then((response) => {
	        this.poHtmlCode = response.data;
	
	      }).catch(function (err) {
	        console.log(err)
	      })
	    },
	    methods:{
	      //控件中的一些常用方法都在这里调用，比如保存，打印等等
			Save() {
				pageofficectrl.WebSave();
				alert("文件保存成功。");
			},

			InsertSeal() {
				try {
					pageofficectrl.zoomseal.AddSeal();
				} catch (e) {
				}
			},

			DeleteSeal() {
				var iCount = pageofficectrl.zoomseal.Count;//获取当前文档中加盖的印章数量
				if (iCount > 0) {
					pageofficectrl.zoomseal.Item(iCount - 1).DeleteSeal();//删除最后一个印章，Item 参数下标从 0 开始
					alert("成功删除了最新加盖的印章。");
				} else {
					alert("请先在文档中加盖印章后，再执行删除操作。");
				}
			},

			VerifySeal() {
				pageofficectrl.zoomseal.VerifySeal();
			},

			ChangePsw() {
				pageofficectrl.zoomseal.ShowSettingsBox();
			}
	    },
	    mounted: function(){
	      // 将vue中的方法赋值给window
			window.Save = this.Save;
			window.InsertSeal = this.InsertSeal;
			window.DeleteSeal = this.DeleteSeal;
			window.VerifySeal = this.VerifySeal;
			window.ChangePsw = this.ChangePsw;
	    }
	}
</script>

