<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="CKZJ">
          <el-input v-model="listQuery.CKZJ" type="text" placeholder="请输入出库专家" style="width:100%;" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="CKYYSM">
          <el-input v-model="listQuery.CKYYSM" type="text" placeholder="请输入出库原因" style="width:100%;"
                    clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell" style="display: flex;gap: 10px;text-align: right;">
        <div class="ml10 static-content-item">
          <el-button type="primary" @click="queryDialogVisible=true">
            <el-icon>
              <Search/>
            </el-icon>
            高级查询
          </el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="primary" @click="queryData">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="primary" @click="exportExcel">
            <el-icon>
              <Upload/>
            </el-icon>
            导出
          </el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="primary" class="lui-button-add" @click="openDialog(null)">
            <el-icon>
              <Plus/>
            </el-icon>
            新增
          </el-button>
        </div>
      </el-col>
    </el-row>
    <el-row ref="grid71868" :gutter="12" v-show="true">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper">
          <el-table ref="dataTable" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                    :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                    :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" :index="indexMethod"></el-table-column>
            <el-table-column prop="CKZJ" label="出库专家" align="center" :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column prop="CKYYSM" label="出库原因" align="center" :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column prop="SQSJ" label="申请日期" align="center" :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column prop="SHZT" label="办理状态" align="center" :show-overflow-tooltip="true">
              <template
                  #default="scope">
                <span v-if="scope.row['SHZT']==='0'">保存</span>
                <span v-else-if="scope.row['SHZT']==='1'">提交</span>
                <span v-else-if="scope.row['SHZT']==='2'">审核通过</span>
              </template>
            </el-table-column>
            <el-table-column prop="CJSJ" label="办理时间" align="center" :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column fixed="right" class-name="data-table-buttons-column" align="center" label="操作"
                             :width="150">
              <template #default="scope">
                <div v-if="scope.row['SHZT']==='0'">
                  <el-button class="lui-table-button" size="small" type="primary" @click="openDialog(scope.row)">编辑
                  </el-button>
                  <el-button class="lui-table-button" size="small" type="primary" @click="delRowData(scope.row.GLZJID)">
                    删除
                  </el-button>
                </div>
                <div v-else>
                  <el-button class="lui-table-button" size="small" type="primary" @click="viewData(scope.row)">查看
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes" class="lui-pagination"
                         @size-change="getDataList(seniorQuery)" @current-change="getDataList(seniorQuery)"
                         :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog
        custom-class="lui-dialog"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="专家出库新增"
        @closed="closeForm"
        top="2vh"
        z-index="1000"
        width="1200px">
      <div>
        <zjckForm :key="GLZJID" :params="params" @closeForm="closeForm"/>
      </div>
    </el-dialog>
    <el-dialog z-index="1000" v-model="queryDialogVisible" title="高级查询条件设置" width="800px" class="dialogClass"
               append-to-body custom-class="lui-dialog">
      <tcgjcx @executeQuery="executeQuery"/>
    </el-dialog>
  </el-form>

</template>

<script>
import {
  defineComponent,
  ref,
  shallowRef,
  toRefs,
  reactive,
  getCurrentInstance,
  watchEffect,
  onMounted
}
  from 'vue'
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import {mixin} from '@src/assets/core';
import zjckForm from "./zjckEdit.vue";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "../../../lib/comFun";
import {Search, Upload, Plus} from '@element-plus/icons-vue'
import Tcgjcx from "./tcgjcx";

export default defineComponent({
  components: {Tcgjcx, zjckForm, Search, Upload, Plus},
  props: {},
  setup() {
    const state = reactive({
      GLZJID: '',
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        CKZJ: null,
        CKYYSM: null,
        HDFL: 'CK',
        page: 1,
        size: 10,
      },
      dialogVisible: false,
      queryDialogVisible: false,
      params: null,
      tableData: [],
      rules: {},
      total: 0,
      seniorQuery: null
    })
    const methods = {}
    const instance = getCurrentInstance()
    const submitForm = () => {
      instance.proxy.$refs['vForm'].validate(valid => {
        if (!valid) return
        //TODO: 提交表单
      })
    }
    const resetForm = () => {
      instance.proxy.$refs['vForm'].resetFields()
    }
    const openDialog = (value) => {
      if (value) {
        state.params = {editable: true, id: value.GLZJID, operation: 'edit'}
      } else {
        state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      }
      state.dialogVisible = true
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }
    const getDataList = (value) => {
      let params = value ? {
        ...value,
        page: state.listQuery.page,
        size: state.listQuery.size,
        HDFL: 'CK',
      } : state.listQuery
      axiosUtil.post('/backend/zjgl/zjckgl/queryZjckList', params).then((res) => {
        state.tableData = res.data ? res.data : []
        if (state.tableData.length === 0) {
          state.total = 0
        } else {
          state.total = state.tableData[0].TOTAL
        }

      });
    }
    const exportExcel = () => {
      let column = [[
        {field: 'CKZJ', title: '出库专家', width: 280, halign: 'center', align: 'left'},
        {field: 'CKYYSM', title: '出库原因', width: 280, halign: 'center', align: 'left'},
        {field: 'SQSJ', title: '申请日期', width: 280, halign: 'center', align: 'left'},
        {field: 'BLZT', title: '办理状态', width: 280, halign: 'center', align: 'left'},
        {field: 'CJSJ', title: '办理时间', width: 400, halign: 'center', align: 'left'},
      ]]
      let finparams = {
        title: '专家出库信息',
        name: '专家出库信息',
        params: state.listQuery,
        column: column
      }
      axiosUtil.exportExcel('/backend/commonExport/zjckxx', finparams)
    }
    const viewData = (value) => {
      state.params = {editable: false, id: value.GLZJID, operation: 'view'}
      state.dialogVisible = true
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const delRowData = (GLZJID) => {
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        axiosUtil.get('/backend/zjgl/zjckgl/deleteZjckxxByGlzjid', {GLZJID}).then((res) => {
          getDataList()
          ElMessage({
            message: '删除成功',
            customClass: "myMessageClass",
            type: 'success',
          })
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }
    const executeQuery = (value) => {
      if (value) {
        state.seniorQuery = value
        getDataList(value)
      }
      state.queryDialogVisible = false
    }
    const queryData = () => {
      state.seniorQuery = null
      getDataList()
    }
    onMounted(() => {
      getDataList()
    })
    return {
      ...toRefs(state),
      ...methods,
      submitForm,
      resetForm,
      openDialog,
      closeForm,
      executeQuery,
      queryData,
      getDataList, delRowData, viewData, indexMethod, exportExcel
    }
  }
})

</script>

<style scoped>

</style>
