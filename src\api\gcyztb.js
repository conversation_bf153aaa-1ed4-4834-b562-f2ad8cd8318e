import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"


// 工程院招投标-采购文件管理-查询招标评价模板
// @method getCgwjglSelectZbpjmb
// @type get
// @return url
//getCgwjglSelectZbpjmb: `/gcyztb/cgwjgl/selectZbpjmb`,

// eslint-disable-next-line
export function getCgwjglSelectZbpjmb(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/cgwjgl/selectZbpjmb`, params)
}
// 工程院招投标-采购文件管理-查询采购文件信息
// @method getCgwjglSelectCGWJ
// @type get
// @return url
//getCgwjglSelectCGWJ: `/gcyztb/cgwjgl/selectCGWJ`,

// eslint-disable-next-line
export function getCgwjglSelectCGWJ(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/cgwjgl/selectCGWJ`, params)
}
// 工程院招投标-采购文件管理-查询评标标准
// @method getCgwjglSelectPbbz
// @type get
// @return url
//getCgwjglSelectPbbz: `/gcyztb/cgwjgl/selectPbbz`,

// eslint-disable-next-line
export function getCgwjglSelectPbbz(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/cgwjgl/selectPbbz`, params)
}
// 工程院招投标-采购文件管理-查询采购文件模板数据
// @method getCgwjglSelectCgwjMbsj
// @type get
// @return url
//getCgwjglSelectCgwjMbsj: `/gcyztb/cgwjgl/selectCgwjMbsj`,

// eslint-disable-next-line
export function getCgwjglSelectCgwjMbsj(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/cgwjgl/selectCgwjMbsj`, params)
}
// 工程院招投标-采购文件管理-查询招标评价模板明细
// @method getCgwjglSelectZbpjmbMx
// @type get
// @return url
//getCgwjglSelectZbpjmbMx: `/gcyztb/cgwjgl/selectZbpjmbMx`,

// eslint-disable-next-line
export function getCgwjglSelectZbpjmbMx(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/cgwjgl/selectZbpjmbMx`, params)
}
// 工程院招投标-采购文件管理-查询招标评价模板分页
// @method getCgwjglSelectZbpjmbPage
// @type get
// @return url
//getCgwjglSelectZbpjmbPage: `/gcyztb/cgwjgl/selectZbpjmbPage`,

// eslint-disable-next-line
export function getCgwjglSelectZbpjmbPage(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/cgwjgl/selectZbpjmbPage`, params)
}
// 工程院招投标-采购文件管理-保存采购文件
// @method postCgwjglSaveCgwj
// @type post
// @return url
//postCgwjglSaveCgwj: `/gcyztb/cgwjgl/saveCgwj`,

// eslint-disable-next-line
export function postCgwjglSaveCgwj(params) {
    return axiosUtil.post(`${baseUrl}/gcyztb/cgwjgl/saveCgwj`, params)
}
// 工程院招投标-招标申请管理-删除招标项目
// @method deleteZbsqglDeleteZbxm
// @type del
// @return url
//deleteZbsqglDeleteZbxm: `/gcyztb/zbsqgl/deleteZbxm`,

// eslint-disable-next-line
export function deleteZbsqglDeleteZbxm(params) {
    return axiosUtil.del(`${baseUrl}/gcyztb/zbsqgl/deleteZbxm`, params)
}
// 工程院招投标-招标申请管理-查询招标项目列表
// @method getZbsqglSelectZbxmList
// @type get
// @return url
//getZbsqglSelectZbxmList: `/gcyztb/zbsqgl/selectZbxmList`,

// eslint-disable-next-line
export function getZbsqglSelectZbxmList(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbsqgl/selectZbxmList`, params)
}
// 工程院招投标-招标申请管理-查询招标项目
// @method getZbsqglSelectZbxm
// @type get
// @return url
//getZbsqglSelectZbxm: `/gcyztb/zbsqgl/selectZbxm`,

// eslint-disable-next-line
export function getZbsqglSelectZbxm(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbsqgl/selectZbxm`, params)
}
// 工程院招投标-招标申请管理-保存招标项目
// @method postZbsqglSaveZbxm
// @type post
// @return url
//postZbsqglSaveZbxm: `/gcyztb/zbsqgl/saveZbxm`,

// eslint-disable-next-line
export function postZbsqglSaveZbxm(params) {
    return axiosUtil.post(`${baseUrl}/gcyztb/zbsqgl/saveZbxm`, params)
}
// 工程院招投标-招标变更管理-查询评标标准变更
// @method getZbbgglSelectPbbzBG
// @type get
// @return url
//getZbbgglSelectPbbzBG: `/gcyztb/zbbggl/selectPbbzBG`,

// eslint-disable-next-line
export function getZbbgglSelectPbbzBG(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbbggl/selectPbbzBG`, params)
}
// 工程院招投标-招标变更管理-查询采购文件变更信息
// @method getZbbgglSelectCGWJBG
// @type get
// @return url
//getZbbgglSelectCGWJBG: `/gcyztb/zbbggl/selectCGWJBG`,

// eslint-disable-next-line
export function getZbbgglSelectCGWJBG(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbbggl/selectCGWJBG`, params)
}
// 工程院招投标-招标变更管理-保存招标项目
// @method postZbbgglSaveZbxm
// @type post
// @return url
//postZbbgglSaveZbxm: `/gcyztb/zbbggl/saveZbxm`,

// eslint-disable-next-line
export function postZbbgglSaveZbxm(params) {
    return axiosUtil.post(`${baseUrl}/gcyztb/zbbggl/saveZbxm`, params)
}
// 工程院招投标-招标变更管理-写回变更数据
// @method getZbbgglBackZbsqBG
// @type get
// @return url
//getZbbgglBackZbsqBG: `/gcyztb/zbbggl/backZbsqBG`,

// eslint-disable-next-line
export function getZbbgglBackZbsqBG(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbbggl/backZbsqBG`, params)
}
// 工程院招投标-招标变更管理-查询采购文件模板变更数据
// @method getZbbgglSelectCgwjMbsjBG
// @type get
// @return url
//getZbbgglSelectCgwjMbsjBG: `/gcyztb/zbbggl/selectCgwjMbsjBG`,

// eslint-disable-next-line
export function getZbbgglSelectCgwjMbsjBG(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbbggl/selectCgwjMbsjBG`, params)
}
// 工程院招投标-招标变更管理-发起招标申请变更
// @method getZbbgglBeginZbsqBG
// @type get
// @return url
//getZbbgglBeginZbsqBG: `/gcyztb/zbbggl/beginZbsqBG`,

// eslint-disable-next-line
export function getZbbgglBeginZbsqBG(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbbggl/beginZbsqBG`, params)
}
// 工程院招投标-招标变更管理-保存采购文件
// @method postZbbgglSaveCgwj
// @type post
// @return url
//postZbbgglSaveCgwj: `/gcyztb/zbbggl/saveCgwj`,

// eslint-disable-next-line
export function postZbbgglSaveCgwj(params) {
    return axiosUtil.post(`${baseUrl}/gcyztb/zbbggl/saveCgwj`, params)
}
// 工程院招投标-招标变更管理-查询招标申请变更数据
// @method getZbbgglSelectZbsqBG
// @type get
// @return url
//getZbbgglSelectZbsqBG: `/gcyztb/zbbggl/selectZbsqBG`,

// eslint-disable-next-line
export function getZbbgglSelectZbsqBG(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbbggl/selectZbsqBG`, params)
}
// 工程院招投标-组会评委抽取-保存评委抽取管理数据
// @method postZhpwcqSavePwcqglData
// @type post
// @return url
//postZhpwcqSavePwcqglData: `/gcyztb/zhpwcq/savePwcqglData`,

// eslint-disable-next-line
export function postZhpwcqSavePwcqglData(params) {
    return axiosUtil.post(`${baseUrl}/gcyztb/zhpwcq/savePwcqglData`, params)
}
// 工程院招投标-项目运行监控-查询招标项目监控列表
// @method getXmyxjkSelectZbxmjkList
// @type get
// @return url
//getXmyxjkSelectZbxmjkList: `/gcyztb/xmyxjk/selectZbxmjkList`,

// eslint-disable-next-line
export function getXmyxjkSelectZbxmjkList(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/xmyxjk/selectZbxmjkList`, params)
}
// 工程院招投标-通用-代码表树查询
// @method getCommonsSelectDMBTree
// @type get
// @return url
//getCommonsSelectDMBTree: `/gcyztb/commons/selectDMBTree`,

// eslint-disable-next-line
export function getCommonsSelectDMBTree(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/commons/selectDMBTree`, params)
}
// 工程院招投标-通用-代码表多个查询
// @method getCommonsSelectDMBMulti
// @type get
// @return url
//getCommonsSelectDMBMulti: `/gcyztb/commons/selectDMBMulti`,

// eslint-disable-next-line
export function getCommonsSelectDMBMulti(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/commons/selectDMBMulti`, params)
}
// 工程院招投标-通用-代码表查询
// @method getCommonsSelectDMB
// @type get
// @return url
//getCommonsSelectDMB: `/gcyztb/commons/selectDMB`,

// eslint-disable-next-line
export function getCommonsSelectDMB(params) {
    return axiosUtil.get(`${baseUrl}/cbsxx/common/selectDMB`, params)
}
// 工程院招投标-中标结果维护-保存中标结果
// @method postZbjgwhSavaZbjg
// @type post
// @return url
//postZbjgwhSavaZbjg: `/gcyztb/zbjgwh/savaZbjg`,

// eslint-disable-next-line
export function postZbjgwhSavaZbjg(params) {
    return axiosUtil.post(`${baseUrl}/gcyztb/zbjgwh/savaZbjg`, params)
}
// 工程院招投标-中标结果维护-查询中标结果明细
// @method getZbjgwhSelectZbjgmx
// @type get
// @return url
//getZbjgwhSelectZbjgmx: `/gcyztb/zbjgwh/selectZbjgmx`,

// eslint-disable-next-line
export function getZbjgwhSelectZbjgmx(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbjgwh/selectZbjgmx`, params)
}
// 工程院招投标-中标结果维护-查询中标结果
// @method getZbjgwhSelectZbjg
// @type get
// @return url
//getZbjgwhSelectZbjg: `/gcyztb/zbjgwh/selectZbjg`,

// eslint-disable-next-line
export function getZbjgwhSelectZbjg(params) {
    return axiosUtil.get(`${baseUrl}/gcyztb/zbjgwh/selectZbjg`, params)
}




