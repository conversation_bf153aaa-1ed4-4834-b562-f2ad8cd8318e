<template>
  <div v-loading="loading">
    <div class="banner-context">
      <div class="banner-title">{{ showTitle }}</div>
      <div class="banner-info">
        <div>项目名称：{{ `${formData.XMXX.XMMC || ''}` }}</div>
        <div style="display: flex;justify-content: space-between">
          <div>项目编号：{{ formData.XMXX.XMBH }}</div>
          <div style="margin-left: 30px">国家授时中心时间：{{ formattedTime }}</div>
        </div>

      </div>
<!--      <div></div>-->
      <el-button size="default" type="primary" @click="updatePage">刷新</el-button>
    </div>

    <div class="page-context">
      <div class="step-context">
        <el-steps style="max-width: 100%" :active="pageState[type].active" align-center finish-status="success">
          <el-step :title="item.title" align-center v-for="(item,index) in stepList" :key="index">
            <template #description>
              <div style="color: black" v-for="(ii,ind) in item.items" :key="index+'-'+ind">
                {{ ii.title }}：
                <el-button size="small" link type="primary" @click="clickButton(item,ii,index)">
                  【{{ getButtonLabel(item, ii, index) }}】
                  <span v-if="ii.button===pageState[type].showZCKPage"
                        style="width: 8px;height: 8px;border-radius: 50%;background-color: #67C23A"></span>
                </el-button>
              </div>
            </template>

          </el-step>
        </el-steps>
      </div>

      <div class="form-context">
        <div id="form-main" class="form-main">
          <div class="from-tabs">
            <div class="tabs-item check-tab" v-if="role==='ZCR'">主窗口
            </div>
            <div class="tabs-item check-tab" v-if="role==='PW' || role==='TBR'">公示窗口
            </div>
          </div>
          <div class="form-panel" v-if="role==='ZCR'">
            <component :is="pageZCRComponent[pageState[type].showZCKPage]" v-if="pageZCRComponent[pageState[type].showZCKPage]"
                       :params="businessParams" :fromParams="params" :parentForm="formData" :key="randomId+'_1'"
                       @changePageState="changePageState" @saveFromData="saveFromData" @nextStep="nextStep"/>
          </div>
          <div class="form-panel" v-if="role==='PW'">
            <component :is="pagePWComponent[pageState[type].showGSCKPage]" v-if="pagePWComponent[pageState[type].showGSCKPage]"
                       :params="businessParams" :fromParams="params" :parentForm="formData" :key="randomId+'_2'"
                       @changePageState="changePageState" @saveFromData="saveFromData"/>
            <div v-else>
              <el-empty description="请等待主持人操作"/>
            </div>
          </div>
          <div class="form-panel" v-if="role==='TBR'">
            <component :is="pageTBRComponent[pageState[type].showGSCKPage]" v-if="pageTBRComponent[pageState[type].showGSCKPage]"
                       :params="businessParams" :fromParams="params" :parentForm="formData" :key="randomId+'_3'"
                       @changePageState="changePageState" @saveFromData="saveFromData"/>
            <div v-else>
              <el-empty description="请等待主持人操作"/>
            </div>
          </div>
        </div>
        <div id="form-divider" class="form-divider"></div>
        <div id="form-message" class="form-message">
          <div class="from-tabs">
            <div :class="`tabs-item ${pageState[type].checkMessageTab==='XXCK' ? 'check-tab' : ''}`"
                 @click="changeTab('checkMessageTab','XXCK')">消息窗口
            </div>
            <div :class="`tabs-item ${pageState[type].checkMessageTab==='WJCKCK' ? 'check-tab' : ''}`"
                 @click="changeTab('checkMessageTab','WJCKCK')" v-if="formData.BSJY_SFGS==='1' && SFLX!=='CBS'">文件查看窗口
              <el-icon @click="dialogWJVisible=true">
                <FullScreen/>
              </el-icon>
            </div>
          </div>
          <div class="form-panel" v-show="pageState[type].checkMessageTab==='XXCK'">
            <xxckPage ref="xxckPage" :params="formData" :key="formData.KPBYXID"/>
          </div>

          <div class="form-panel" v-show="pageState[type].checkMessageTab==='WJCKCK'">
            <fileViewWindow style="width: 1200px" :params="formData" :key="formData.KPBYXID"
                            v-if="formData.BSJY_SFGS==='1'"/>
          </div>
        </div>
      </div>


    </div>

        <el-dialog
            custom-class="lui-dialog"
            :close-on-click-modal="false"
            v-if="dialogQDVisible"
            v-model="dialogQDVisible"
            title=""
            @closed=""
            z-index="1000"
            top="5vh"
            fullscreen
            :show-close="false"
            width="1200px">
          <div>
            <pwqdEdit v-if="dialogQDVisible" :params="formData" @close="closeQd"/>
          </div>
        </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-model="dialogWJVisible"
        title="文件查看"
        @closed=""
        z-index="1000"
        top="5vh"
        width="1400px">
      <div>
        <fileViewWindow style="width: 1200px" :params="formData" :key="formData.JLID"
                        v-if="formData.BSJY_SFGS==='1'"/>
      </div>
    </el-dialog>


  </div>
</template>

<script>

import {
  defineComponent,
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeMount,
  markRaw,
  onUnmounted
} from "vue";
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import {ElMessage} from "element-plus";
import fileViewWindow from "@views/kpbyx/common/wjck/fileViewWindow";
import xxckPage from "@views/kpbyx/common/xxck/xxckPage";
import tabFun from "@lib/tabFun";
import {mixin} from "@core";
import {FullScreen, Message} from "@element-plus/icons-vue";

import kbddPage from "@views/kpbyx/common/kbdd/kbddPage";
import tbrqdEdit from "@views/kpbyx/common/tbrqd/tbrqdEdit";
import bsjmEdit from "@views/kpbyx/common/bsjm/bsjmEdit";
import cbsxEdit from "@views/kpbyx/common/cbsx/cbsxEdit";
import kbjlEdit from "@views/kpbyx/common/kbjl/kbjlEdit";
import bdzgxjEdit from "@views/kpbyx/common/bdzgxj/bdzgxjEdit";
import hymmEdit from "@views/kpbyx/common/hymm/hymmEdit";
import pwqdqrEdit from "@views/kpbyx/common/pwqd/pwqdqrEdit";
import pwqdEdit from "@views/kpbyx/common/pwqd/pwqdEdit";
import pwzrxzEdit from "@views/kpbyx/common/pwzr/pwzrxzEdit";
import pwzrtpEdit from "@views/kpbyx/common/pwzr/pwzrtpEdit";
import zgcsQkEdit from "@views/kpbyx/common/zgcs/zgcsQkEdit";
import zgcsEdit from "@views/kpbyx/common/zgcs/zgcsEdit";
import jsfdfEdit from "@views/kpbyx/common/jsfdf/jsfdfEdit";
import jsfdfHzEdit from "@views/kpbyx/common/jsfdf/jsfdfHzEdit";
import tjbEdit from "@views/kpbyx/common/tjb/tjbEdit";
import xqbEdit from "@views/kpbyx/common/xqb/xqbEdit";
import zbjgwhEdit from "@views/kpbyx/common/zbjgwh/zbjgwhEdit";
import pbbgEdit from "@views/kpbyx/common/pbbg/pbbgEdit";
import zbjgbEdit from "@views/kpbyx/common/zbjgb/zbjgbEdit";
import tbdwyjEdit from "@views/kpbyx/common/tbdwyj/tbdwyjEdit";
import pwkhEdit from "@views/kpbyx/common/pwkh/pwkhEdit";
import cbskhEdit from "@views/kpbyx/common/cbskh/cbskhEdit";

export default defineComponent({
  name: '',
  components: {fileViewWindow, xxckPage, FullScreen, Message,pwqdEdit},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: props.params?.editable,
      KPBYXID: props.params?.id,
      role: props.params?.role,
      type: props.params?.type || 'null',
      randomId: comFun.newId(),
      stepTypes: {
        ZBKB: [
          {title: '签到', items: [{title: '投标人签到', button: 'TBRQD', num: 0}]},
          {title: '解密', items: [{title: '标书解密', button: 'BSJM', num: 1}]},
          {title: '唱标', items: [{title: '唱标顺序', button: 'CBSX', num: 2}]},
          {title: '开标记录', items: [{title: '开标记录情况', button: 'KBJLQK', num: 3}]},
          {title: '标的或最高限价', items: [{title: '标的或最高限价', button: 'BDHZGXJ', num: 4}]}
        ],
        ZBPB: [
          {
            title: '评标准备',
            items: [
              { title: '设置评标会密码', button: 'SZPBHMM', num: 0 },
              { title: '评委签到', button: 'PWQD', num: 1 },
              { title: '设置评委主任', button: 'SZWPJRZ', num: 2 }
            ]
          },
          {
            title: '资格初审',
            items: [{ title: '资格初审', button: 'ZGCS', num: 3 }]
          },
          {
            title: '技术分打分',
            items: [
              { title: '技术分评分明细', button: 'JSFPFMX', num: 4 },
              { title: '技术分评分汇总', button: 'JSFPFHZ', num: 5 }
            ]
          },
          {
            title: '综合分打分',
            items: [
              { title: '综合分评分明细', button: 'ZHFPFMX', num: 6 },
              { title: '综合分评分汇总', button: 'ZHFPFHZ', num: 7 }
            ]
          },
          {
            title: '结果计算与评标汇总',
            items: [
              { title: '投井表', button: 'TJB', num: 8 },
              { title: '单项工程计算明细', button: 'DXGCMX', num: 9 },
              { title: '评标汇总', button: 'PBHZ', num: 10 }
            ]
          },
          {
            title: '定中标',
            items: [
              { title: '选、弃标', button: 'XQB', num: 11 },
              { title: '中标结果维护', button: 'ZBJGWH', num: 12 },
              { title: '评标报告', button: 'PBBG', num: 13 },
              { title: '招标结果表', button: 'ZBJGB', num: 14 },
              { title: '投标单位意见', button: 'TBDWYJ', num: 15 }
            ]
          },
          {
            title: '其它',
            items: [
              { title: '评委考核', button: 'PWKH', num: 16 },
              { title: '承包商考核', button: 'CBSKH', num: 17 }
            ]
          }
        ]
      },
      stepTitles: {
        ZBKB: '开标大厅',
        ZBPB: '评标大厅',
      },
      showTitle: '',

      stepList: [],
      formData: {
        KPBYXID: props.params?.id,
        JLID: props.params?.JLID,
        XMXX: {},
        RYXX: {},
        KBXX: {},
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',
      },
      pageState: {
        ZBKB: {
          active: -1,
          stepNum: -1,
          checkMessageTab: 'XXCK',
          SFSZMM: '0',
          showZCKPage: '',
          showGSCKPage: '',
        },
        ZBPB: {
          active: -1,
          stepNum: -1,
          checkMessageTab: 'XXCK',
          SFSZMM: '0',
          showZCKPage: '',
          showGSCKPage: '',
        },
        null:{}
      },

      pageZCRComponent: {

        // HYMM: markRaw(hymmszEdit),
        KBDD: markRaw(kbddPage), //开标等待
        TBRQD: markRaw(tbrqdEdit), //投标人签到
        BSJM: markRaw(bsjmEdit), //标书解密
        CBSX: markRaw(cbsxEdit), //唱标顺序
        KBJLQK: markRaw(kbjlEdit), //开标记录情况
        BDHZGXJ: markRaw(bdzgxjEdit), //投标最高限价
        SZPBHMM: markRaw(hymmEdit), //设置评标会密码
        PWQD: markRaw(pwqdqrEdit), //评委签到
        SZWPJRZ: markRaw(pwzrxzEdit), //设置评委主任
        ZGCS: markRaw(zgcsQkEdit), //资格初审
        JSFPFMX: markRaw(jsfdfHzEdit), //技术分打分明细
        JSFPFHZ: markRaw(jsfdfHzEdit), //技术分打分明细
        TJB: markRaw(tjbEdit), //投井表
        XQB: markRaw(xqbEdit), //选、弃标
        ZBJGWH: markRaw(zbjgwhEdit), //中标结果维护
        PBBG: markRaw(pbbgEdit), //评标报告
        ZBJGB: markRaw(zbjgbEdit), //招标结果表
        TBDWYJ: markRaw(tbdwyjEdit), //投标单位意见
        PWKH: markRaw(pwkhEdit), //评委考核
        CBSKH: markRaw(cbskhEdit), //承包商考核
      },
      pagePWComponent: {
        KBDD: markRaw(kbddPage), //开标等待
        SZWPJRZ: markRaw(pwzrtpEdit), //设置评委主任
        ZGCS: markRaw(zgcsEdit), //资格初审
        JSFPFMX: markRaw(jsfdfEdit), //技术分打分明细
        PBBG: markRaw(pbbgEdit), //评标报告
        ZBJGB: markRaw(zbjgbEdit), //招标结果表
      },
      pageTBRComponent: {
        KBDD: markRaw(kbddPage), //开标等待
        TBRQD: markRaw(tbrqdEdit), //投标人签到
        BSJM: markRaw(bsjmEdit), //标书解密

      },
      businessParams: {},
      loading: false,
      dialogQDVisible: false,
      dialogWJVisible: false,

      timeOffset: 0,
      KBSJOffset: 0,
      formattedTime: '',


      activeStepNum: -1,
    })

    const getFormData = () => {
      let params = {
        KPBYXID: state.KPBYXID,
        role: state.role,
        loginName: vsAuth.getAuthInfo().permission.userLoginName,
        userId: vsAuth.getAuthInfo().permission.userId
      }
      state.loading = true
      axiosUtil.get('/backend/kpbyx/kpbdt/selectPbyxById', params).then(res => {
        state.formData = res.data
        /**
         * TODO: 测试固定报价，后面删除
         */
        state.formData.XMXX.BJFS='GDBJ'
        if (!state.formData.RYXX) {
          ElMessage.error('人员身份校验失败')
          return
        } else if (state.role === 'PW' && ['PBZJ','DWDB'].includes(state.formData.RYXX.CHSF) && state.formData.RYXX.SFQD !== '1' && state.formData.QD_WCZT !== '1') {
          state.dialogQDVisible = true
        }
        if (state.formData.PBYXXX) {
          let pageState = JSON.parse(state.formData.PBYXXX)
          setBusinessParams(state.role === 'ZCR' ? pageState[state.type].showZCKPage : pageState[state.type].showGSCKPage)
          state.pageState = pageState
        }
        const serverTimestamp = new Date(state.formData.XMXX.SJ).getTime()
        const clientTimestamp = Date.now()
        state.timeOffset = serverTimestamp - clientTimestamp
        initStep()
        if(state.pageState[state.type].stepNum===-1){
          initPage()
        }
        state.loading = false
      })

    }

    const getXMXX = () => {
      state.loading = true
      let params = {
        JLID: props.params.JLID
      }
      axiosUtil.get('/backend/kpbyx/kpbdt/selectPbxmxxByJlid', params).then(res => {
        state.formData.XMXX = res.data
        const serverTimestamp = new Date(state.formData.XMXX.SJ).getTime()
        const KBSJ = new Date(state.formData.XMXX.KBSJ).getTime()
        const clientTimestamp = Date.now()
        state.timeOffset = serverTimestamp - clientTimestamp
        state.KBSJOffset = KBSJ - clientTimestamp
        initStep()
        initPage()
        state.loading = false
      })
    }

    const initPage = () => {
      if (state.KBSJOffset > 0) {
        setBusinessParams('KBDD')
        state.pageState[state.type].showZCKPage = 'KBDD'
        return
      }

      if (['ZBKB'].includes(state.type)) {
        nextStep('开始开标，正在进行投标人签到')
      } else if (['ZBPB'].includes(state.type)) {
        nextStep('开始设置评标会议密码')
      }
    }

    const initStep = () => {
      if (['ZBKB'].includes(state.type)) {
        state.stepList = state.stepTypes.ZBKB
        state.showTitle = state.stepTitles.ZBKB
      } else if (['ZBPB'].includes(state.type)) {
        state.stepList = state.stepTypes.ZBPB
        let JGJSRow = state.stepList.find(item => item.title === '结果计算与评标汇总')
        if(state.formData.XMXX.BJFS==='GDBJ'){
          JGJSRow.items=JGJSRow.items.filter(item=>item.button==='TJB')
        }else {
          JGJSRow.items=JGJSRow.items.filter(item=>item.button!=='TJB')
        }
        let i=0
        for(let item of state.stepList){
          for(let ii of item.items){
            ii.index=i
            i++
          }
        }
        state.showTitle = state.stepTitles.ZBPB
      }

      if(props.params.busType && state.pageState[state.type].stepNum>=0){
        let item1 = state.stepList.find(item => item.items.find(ii => ii.button === props.params.busType))
        if(item1){
          let item2 = item1.items.find(item => item.button === props.params.busType)
          if(item2){
            clickButton(item1, item2, null)
          }
        }
      }

    }

    const changeTab = (type, newTab) => {
      if (state.pageState[state.type][type] !== newTab) {
        state.pageState[state.type][type] = newTab
      }
    }

    const getButtonLabel = (item, ii, index) => {
      if (state.pageState[state.type].stepNum > ii.num) {
        return '查看'
      } else if (state.pageState[state.type].stepNum === ii.num) {
        return '进行中'
      } else {
        return '未处理'
      }
    }

    const clickButton = (item, ii, index) => {
      /**
       * TODO: 暂时关闭页面跳转限制，后续启用
       */
      // if (state.pageState[state.type].stepNum < ii.num) {
      //   ElMessage.warning(`请先完成${ii.title}之前环节`)
      //   return
      // }

      setBusinessParams(ii.button)
      if (state.role === 'ZCR') {
        state.pageState[state.type].showZCKPage = ii.button
      } else {
        state.pageState[state.type].showGSCKPage = ii.button
      }
    }


    /**
     * 设置业务页面传递参数
     * @param showPage
     */
    const setBusinessParams = (showPage) => {
      let params = {
        KPBYXID: state.KPBYXID,
        JLID: state.formData.JLID,
        KBJLID: state.formData.KBJLID,
      }
      if (showPage === 'KBDD') {
        params.KBSJOffset = state.KBSJOffset
      }else if (['SZPBHMM','PWQD','SZWPJRZ','ZGCS','JSFPFMX'].includes(showPage)) {
        params.PBHYBS = state.formData.XMXX.PBHYBS
      }
      state.businessParams = params
    }

    const nextStep = (msg) => {
      state.pageState[state.type].stepNum++
      state.activeStepNum = state.pageState[state.type].stepNum
      if (msg) {
        saveMessage(state.pageState[state.type].stepNum, msg)
      }
      let nextIndex = state.stepList.findIndex(item => item.items.find(ii => ii.num === state.pageState[state.type].stepNum))
      if (nextIndex >= 0) {
        state.pageState[state.type].active = nextIndex
        let showPage = state.stepList[nextIndex].items.find(item => item.num === state.pageState[state.type].stepNum).button
        setBusinessParams(showPage)
        state.pageState[state.type].showGSCKPage = state.pageState[state.type].showZCKPage = showPage
        submitForm()
      } else {
        state.pageState[state.type].active++
        state.formData.SHZT = '1'
        submitForm()
        console.error('结束运行')
        //结束运行
      }
    }

    const saveMessage = (stepNum, msg) => {
      let nowIndex = state.stepList.findIndex(item => item.items.find(ii => ii.num === stepNum))
      if (nowIndex >= 0) {
        let nowPage = state.stepList[nowIndex].items.find(item => item.num === stepNum)
        let nextStepNum = stepNum + 1
        let nextIndex = state.stepList.findIndex(item => item.items.find(ii => ii.num === nextStepNum))
        let nextPage = null
        if (nextIndex >= 0) {
          nextPage = state.stepList[nextIndex].items.find(item => item.num === nextStepNum)
        }

        let params = {
          ID: comFun.newId(),
          KPBYXID: state.KPBYXID,
          YWLX: nowPage.button,
          DQJD: nowPage.title,
          XYJD: nextPage ? nextPage.title : '结束',
          NR: msg,
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
          SHZT: '1',
          YWZT: '1'
        }
        saveXcyxjd(params)
      }

    }

    const changePageState = (page) => {
      for (let key in page) {
        state.pageState[state.type][key] = page[key]
      }
      submitForm()
    }

    const saveFromData = (from, page) => {
      for (let key in from) {
        state.formData[key] = from[key]
      }
      for (let key in page) {
        state.pageState[state.type][key] = page[key]
      }
    }
    const {vsuiRoute, vsuiEventbus} = mixin();
    const submitForm = () => {
      let params = {
        ...state.formData,
        PBYXXX: JSON.stringify(state.pageState)
      }
      axiosUtil.post('/backend/kpbyx/kpbdt/savePbyxxx', params).then(res => {
        if (props.params.operation === 'add') {
          props.params.operation = 'edit'
        }
        if(!params.KBJLID){
          getFormData()
        }
        vsuiEventbus.emit("reloadYxList")
      })
    }

    const instance = getCurrentInstance()
    const saveXcyxjd = (params) => {
      axiosUtil.post('/backend/kpbyx/xxck/saveXsglXcyxjd', params).then(res => {
        if (instance.proxy.$refs['xxckPage']) {
          instance.proxy.$refs['xxckPage'].getDataList()
        }
      })
    }

    const closeTab = () => {
      if (state.role === 'ZCR') {
        tabFun.openNewTabClose('评标运行（主持人）', 'pbyxList', {role: 'ZCR'}, {})
      } else if (state.role === 'PW') {
        tabFun.openNewTabClose('评标运行（评委）', 'pbyxPwList', {role: 'PW'}, {})
      }
    }

    const closeQd = () => {
      state.dialogQDVisible = false
      getFormData()
    }

    const initDrag = () => {
      const divider = document.getElementById('form-divider');
      const box1 = document.getElementById('form-main');
      const box2 = document.getElementById('form-message');
      const container = document.querySelector('.form-context');
      let isResizing = false

      divider.addEventListener('mousedown', () => {
        isResizing = true
      })

      window.addEventListener('mousemove', (e) => {
        if (!isResizing) return
        const containerRect = container.getBoundingClientRect()
        const dividerRect = divider.getBoundingClientRect()

        const offset = e.clientX - containerRect.left
        const box1Width = offset - (dividerRect.width / 2)
        const box2Width = containerRect.width - box1Width - dividerRect.width - 20

        if (box1Width >= 300 && box2Width >= 300) {
          box1.style.width = `${box1Width}px`;
          box2.style.width = `${box2Width}px`;
        }
      });

      window.addEventListener('mouseup', () => {
        isResizing = false
      })
    }

    const startRealTimeClock = () => {
      setInterval(() => {
        const syncedTime = Date.now() + state.timeOffset
        state.formattedTime = new Date(syncedTime).toLocaleString()
      }, 1000)
    }

    const updatePage = () => {
      getFormData()
      state.randomId = comFun.newId()
    }

    onMounted(() => {
      initDrag()
      startRealTimeClock()
      if (!props.params?.id) {
        ElMessage.warning('验证信息缺失，请重新进入大厅')
        state.loading = true
        tabFun.closeNowTab()
        return
      }
      if (props.params.operation !== 'add') {
        getFormData()
      } else {
        getXMXX()
      }
    })

    return {
      ...toRefs(state),
      changeTab,
      getButtonLabel,
      clickButton,
      changePageState,
      saveFromData,
      nextStep,
      closeTab,
      getFormData,
      closeQd,
      saveXcyxjd,
      updatePage
    }
  }

})
</script>

<style scoped>
.banner-context {
  height: 80px;
  border: 1px solid #c2c5c9;
  padding-left: 20px;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.banner-info {
  font-weight: bolder;
  color: black;
}

.banner-title {
  font-size: 30px;
  color: #b90404;
}

.page-context {
  background-color: white;
  height: calc(100vh - 200px);
  width: 100%;
}

:deep(.el-steps) {
  --el-text-color-primary: #f68f09;
}

.step-context {
  height: 130px;
  padding-top: 10px;
  width: 100%;
  z-index: 666;
}

.form-context {
  height: calc(100% - 150px);
  margin-left: 20px;
  margin-right: 20px;
  display: flex;
  gap: 10px;
  width: calc(100% - 40px);
  flex-shrink: 0;
  z-index: 888;
}

.form-divider {
  width: 5px;
  background-color: #ccc;
  cursor: ew-resize;
}

.form-main {
  height: 100%;
  width: calc(100% - 330px);
}

.form-message {
  height: 100%;
  width: 300px;
}

.from-tabs {
  display: flex;
  gap: 10px;
}

.tabs-item {
  height: 30px;
  color: #8c939d;
  font-weight: bolder;
  border: 1px solid #c2c5c9;
  padding-left: 20px;
  padding-right: 20px;
  line-height: 30px;
  cursor: pointer;
  z-index: 999;
}

.tabs-item:hover {
  color: #6eb5f6;
}

.check-tab {
  color: #2A96F9;
  border: 1px solid #2A96F9;
}


.form-panel {
  border: 1px solid #c2c5c9;
  height: calc(100% - 30px);
  width: 100%;
  overflow: auto;
}
</style>
