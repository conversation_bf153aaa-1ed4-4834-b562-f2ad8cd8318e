<template>
    <el-form :model="formData" ref="vForm" class="lui-card-form" label-position="right" label-width="180px" size="default" v-loading="loading" >
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标项目名称：" >
                    <el-input v-model="formData.XMMC" placeholder="请输入" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="资金来源：" >
                    <el-input v-model="formData.ZJLYMC" placeholder="请输入" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标时间：" >
                    <el-input v-model="formData.KBSJ" placeholder="请输入" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row" style="margin-top: 10px">
            <el-col :span="24" class="grid-cell">
                <el-table 
                    :data="tableData" 
                    class="lui-table"
                    border
                    stripe
                    size="default" 
                    height="calc(100vh - 500px)"
                    highlight-current-row>
                    <el-table-column prop="DWMC" label="单位/队伍名称" header-align="center" align="left" min-width="280">
                        <template #default="scope">
                            {{scope.row.CBSDWQC}}-{{scope.row.DWMC}}
                        </template>
                    </el-table-column>
                    <el-table-column label="征信" align="center" min-width="100">
                        <template #default="scope">
                            <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row,'ZX')">查看
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="风险" align="center" min-width="100">
                        <template #default="scope">
                            <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row,'FX')">查看
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="承包商回函结果" align="center" min-width="200">
                        <template #default="scope">
                            <el-radio-group v-model="scope.row.SFCJTB" disabled>
                                <el-radio v-for="(item,index) in hhxxList" :key="index" :label="item.DMXX">{{item.DMMC}}</el-radio>
                            </el-radio-group>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
            <el-col :span="24" style="text-align: center;">
                <el-button @click="closeForm">返回</el-button>
            </el-col>
        </el-row>
    </el-form>
    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="showDialog"
        v-model="showDialog"
        :title="title"
        top="5vh"
        width="80%">
        <cbszxView v-if="dialogType === 'ZX'" :params="dialogParam"></cbszxView>
        <cbsfxList v-if="dialogType === 'FX'" :params="dialogParam"></cbsfxList>
    </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axiosUtil from "@lib/axiosUtil";
import cbszxView from "@views/cbs/queryanalysis/cbsda/cbszxView.vue";
import cbsfxList from "@views/cbs/queryanalysis/cbsda/cbsfxList.vue";
const props = defineProps({
    params: {
        type: Object,
        default: ()=>({})
    }
});
const emit = defineEmits(["close"]);
const loading = ref(false);
const formData = ref({});
const tableData = ref([]);

const showDialog = ref(false);
const dialogType = ref('');
const dialogParam = ref({});
const title = ref('');
onMounted(() => {
    queryDMBList();
    initFormData();
});

const initFormData = () => {
    loading.value = true;
    axiosUtil.get('/backend/xsgl/hhqkqr/queryHhqrForm', {
        ggid: props.params.id
    }).then(res=>{
        loading.value = false;
        formData.value = res.data.form;
        tableData.value = res.data.list;
    }).catch((err) => {
        loading.value = false;
    });
}

// 回函信息类型
const hhxxList = ref([]);
const queryDMBList = () => {
    axiosUtil.get('/backend/cbsxx/common/selectDMB', {
        DMLBID: "HHXX"
    }).then(res=>{
        hhxxList.value = res.data;
    })
}

const viewRow = (row,type) => {
    if(type === 'ZX'){
        title.value = '征信';
    }else if(type === 'FX'){
        title.value = '风险';
    }
    dialogType.value = type;
    dialogParam.value = {
        CBSDWQC: row.CBSDWQC
    };
    showDialog.value = true;
}

const closeForm = () => {
    emit('close')
}
defineExpose({});
</script>

<style scoped>
</style>