<template>
	<el-card style="height: calc(100vh - 105px); overflow-y: auto">
		<el-tabs v-model="activeName" type="card">
			<el-tab-pane label="业务导航" name="ywdh">
				<div v-for="(i,index) in ywList" :key="index">
					<el-row class="title">{{i.title}}：</el-row>
					<el-row v-for="(item,iindex) in i.children" :key="iindex" :gutter="20">
						<div style="width:200px; text-align: right;">
							<span>{{item.title}}</span>
						</div>
						<div style="margin-left: 20px;">
							{{item.text}}
							<el-button style="font-size: 14px;" @click="chooseData(item)" type="text">{{item.btn}}</el-button>
						</div>
					</el-row>
				</div>
			</el-tab-pane>
		</el-tabs>

		<el-tabs v-model="bottomActiveName" type="card" style="margin-top: 15px">
			<el-tab-pane label="准入草稿" name="zrcg">
				<cgxxList v-show="bottomActiveName === 'zrcg'"></cgxxList>
			</el-tab-pane>
			<el-tab-pane label="变更草稿" name="bgcg">
				<bgcgList v-show="bottomActiveName === 'bgcg'"></bgcgList>
			</el-tab-pane>
			<el-tab-pane label="备案草稿" name="bacg">
				<baca v-show="bottomActiveName === 'bacg'"></baca>
			</el-tab-pane>
		</el-tabs>
	</el-card>
</template>
<script setup>
import { onMounted, ref, markRaw, getCurrentInstance,computed, nextTick } from "vue";
import {ElMessage, ElMessageBox} from "element-plus";
import {BellFilled} from '@element-plus/icons-vue'
import TabFun from "@src/lib/tabFun";
import {getCbsyjGetTeamInfoByOrgId, getCbszxCopyAddInfo, getCbsyjGetYwxxCg} from "@src/api/sccbsgl";
import vsAuth from "@lib/vsAuth";
import zxzy from "@views/cbs/cbsyj/xzzy";
import cbsxx from "../cbsyj/cbsxx/index.vue"
// 承包商变更
import cbsbgList from "@views/cbs/cbsbg/cbsdwbg/cbsbgList.vue";
// 队伍变更
import dwbgList from "@views/cbs/cbsbg/cbsdwbg/dwbgList.vue";
// 备案
import baca from "@views/cbs/cbsyj/cbsxx/baca.vue"
import cgxxList from "@views/cbs/cgxx/cgxxList.vue"
import bgcgList from "@views/cbs/cbsbg/bgcgList";
// 临时准入
import xzzyList from "@views/cbs/cbslszr/xzzyList.vue";
// 队伍合并
import dwhb from "@views/cbs/cbsdwhb";
// 区域变更
import qybgList from "@views/cbs/cbsbg/cbsqybg/qybgList";
// 复审
import dwfsList from "@views/cbs/cbsbg/cbsdwbg/dwfsList.vue"
const emit = defineEmits([])
const props = defineProps({});

const activeName = ref("ywdh");
const bottomActiveName = ref("zrcg");
const listQuery = ref({
	page: 1,
	size: 10,
	orgnaId: vsAuth.getAuthInfo().permission.orgnaId
});
const tableData = ref([]);
const total = ref(0);

const ywList = [
	// 承包商信息去掉专业
	// 三个草稿放到下面表格（准入草稿、变更草稿、备案草稿）
	// 承包商变更要把所有的信息都带出来
	// 增项申请查询AB类的队伍，C类增项查询C类的队伍
	// 增项也要把所有的专业的信息都带出来，不允许修改
	// 队伍合并是将自己承包商的队伍合并到其他承包商，本队伍显示取消投标资格
	// 人员证书一键更新，从其他系统获取数据，根据身份证同步证书信息
	// 承包商复审，本年新上功能，把准入的信息重新审核一遍（不清楚能不能修改）
	// C类专业适用范围变更（这个漏掉了）
	// 补充：适用范围变更为服务区域变更
	// 分公司临时投标资格申请选AB类专业，推荐单位选机关部门和二级单位
	// 二级单位临时投标资格申请选AB类专业，推荐单位选二级单位
	// C类专业临时投标资格申请选C类专业，推荐单位选机关部门和二级单位
	// 三种备案不选择专业，新建一套表，模版的所有tab页都带出来
	{title: '正式投标资格管理',children:[
		{title: '正式投标资格申请',text:'办理某个专业长期准入，请从这里',btn:'发起申请>>>',name:'zxzy',page:markRaw(zxzy),props: {ZRLX: 'AB'}},
		{title: '企业信息变更申请',text:'营业执照、企业资质信息等资料到期或发生变化后，请从这里',btn:'发起申请>>>',name: 'cbsxxbg',page: markRaw(cbsbgList),props: {ZRLX: 'AB'}},
		{title: '队伍信息变更申请',text:'办理了长期准入的队伍，队伍资质、人员、设备等信息到期或发生变化后，请从这里',btn:'发起申请>>>',name: 'dwxxbg',page: markRaw(dwbgList),props: {ZRLX: 'AB'}},
		{title: '队伍增项申请',text:'已经办理了长期准入的队伍，申请增加新的投标专业，请从这里',btn:'发起申请>>>',method: 'cbsdwzx',props: {ZRLX: 'AB'}},
		{title: '队伍合并申请',text:'办理中石化、中石油单位内部队伍合并（由被合并方发起），请从这里',btn:'发起申请>>>',name: 'dwhb',page: markRaw(dwhb),props: {}},
		{title: '人员证书一键更新',text:'若人员证书信息发生变化，需要更新时，请点击',btn:'人员证件一键更新>>>',status:'running'},
		{title: '承包商复审申请',text:'按照年度复审管理要求，接到复审通知后，请从这里',btn:'发起申请>>>',name: 'dwxxbg',page: markRaw(dwfsList),props: {}},
	]},
	{title: 'C类专业正式投标资格管理',children:[
		{title: 'C类专业正式投标资格申请',text:'办理某个专业长期准入，请从这里',btn:'发起申请>>>',name:'zxzy',page:markRaw(zxzy),props: {ZRLX: 'C'}},
		{title: 'C类专业临时投标资格申请',text:'营业执照、企业资质信息等资料到期或发生变化后，请从这里',btn:'发起申请>>>',name:'zxzy',page:markRaw(xzzyList),props: {ZRLX: 'C'}},
		{title: 'C类企业信息变更申请',text:'办理了长期准入的队伍，队伍资质、人员、设备等信息到期或发生变化后，请从这里',btn:'发起申请>>>',name: 'cbsxxbg',page: markRaw(cbsbgList),props: {ZRLX: 'C'}},
		{title: 'C类队伍信息变更申请',text:'已经办理了长期准入的队伍，申请增加新的投标专业，请从这里',btn:'发起申请>>>',name: 'dwxxbg',page: markRaw(dwbgList),props: {ZRLX: 'C'}},
		{title: 'C类队伍增项申请',text:'办理中石化、中石油单位内部队伍合并（由被合并方发起），请从这里',btn:'发起申请>>>',method: 'cbsdwzx',props: {ZRLX: 'C'}},
		{title: 'C类专业适用范围变更',text:'投标专业的适用范围发生变化，请从这里',btn:'发起申请>>>',name: 'qybg',page: markRaw(qybgList),data:{},props: {}},
	]},
	{title: '临时投标资格管理',children:[
		{title: '分公司临时投标资格申请',text:'无长期准入，由相关部门/二级单位为某一特定项目推荐准入，请从这里',btn:'发起申请>>>',name: 'zxzy',page: markRaw(xzzyList),data:{},props: {ZRLX: 'AB',LSZRLX:'FGS'}},
		{title: '二级单位临时投标资格申请',text:'无长期准入，由二级单位为某一特定项目推荐准入（科研楼项目低于100万，其他项目低于50万），请从这里',btn:'发起申请>>>',name: 'zxzy',page: markRaw(xzzyList),data:{},props: {ZRLX: 'AB',LSZRLX:'EJDW'}},
	]},
	{title: '备案管理管理',children:[
		{title: '公开招标单位备案',text:'总部电子招投标系统中的单位，备案请从这里',btn:'发起申请>>>',name: 'cbsxx',page: markRaw(cbsxx),data:{},props: {YWLXDM: 'GKZB'}},
		{title: '分包商备案',text:'属于西北油田分公司长期分包商，备案请从这里',btn:'发起申请>>>',name: 'cbsxx',page: markRaw(cbsxx),data:{},props: {YWLXDM:'CQBA'}},
		{title: '分包商备案',text:'为某项目的分包商，备案请从这里',btn:'发起申请>>>',name: 'cbsxx',page: markRaw(cbsxx),data:{},props: {YWLXDM:'XMBA'}},
	]},
]

onMounted(() => {
	getDwInfo();
	getDataList();
});


const instance = getCurrentInstance()
const chooseData = (info) => {
	if(info.method && instance.proxy[info.method]){
		instance.proxy[info.method](info)
	}
	if(info.page){
		TabFun.addTabByCustomName(info.title, info.name, info.page, info.props?info.props:{}, {});
	}
}
// 队伍增项
const DWXX = ref({});
const cbsdwzx = (val) => {
	if (!DWXX.value.DWYWID) {
		ElMessage({
			message: "请先进行正式投标资格申请",
			type: "warning",
		});
		return false
	}
	TabFun.addTabByRoutePath("增项-选择队伍", "/contractors/addTeamList", val.props, {});
}

const getDwInfo = () => {
	getCbsyjGetTeamInfoByOrgId({orgId: vsAuth.getAuthInfo().permission.orgnaId}).then(res => {
		DWXX.value = res.data || {};
	})
}

const tableLoading = ref(false);
const getDataList = () => {
	tableLoading.value = true;
	listQuery.value.LY = 'CBSZR';
	getCbsyjGetYwxxCg(listQuery.value).then(({data}) => {
		tableData.value = data.list ?? [];
		total.value = data.value;
	}).catch((err) => {
		console.log(err);
	}).finally(() => {
		tableLoading.value = false
	})
};

const indexMethod = (index) => {
  	return index + listQuery.value.size * (listQuery.value.page - 1) + 1;
};

defineExpose({})

</script>

<style scoped>
.title{
    font-size: 14px;
    font-weight: bold;
    line-height: 32px;
}
.connect{
    display: inline-block;
    width: 200px;
    margin-left: 42px;
}
::v-deep .el-card__body {
    padding: 10px 20px;
}
</style>
