<template>
    <div style="height: 670px">

            <el-row class="zhyy-list-searchArea">
                <el-col :span="16" style="display: inline-block; text-align: left">
                    <el-input
                            v-model="queryParams.cxmc"
                            placeholder="搜索车型名称"
                            style="width: 300px"
                            clearable
                    ></el-input>
                    <el-button
                            size="mini"
                            type="primary"
                            style="margin-left: 10px"
                            @click="getTableData()"
                    >查询</el-button
                    >
                </el-col>
            </el-row>
            <el-table
                    highlight-current-row
                    size="default"
                    ref="table"
                    height="540px"
                    fit
                    class="lui-table"
                    :border="false"
                    :data="state.tableData"
            >
                <el-table-column label="序号" width="100px" type="index" align="center" />
                <el-table-column label="车型" min-width="300px" align="center" prop="CX" />
                <el-table-column
                        prop="CZ"
                        label="操作"
                        header-align="center"
                        width="120px"
                        align="center"
                >
                    <template #default="scope">
                        <el-button type="text" @click="confirmData(scope.row,scope.$index)">选择</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="queryParams.page"
                        :page-sizes="[10, 20, 30, 40]"
                        :page-size="queryParams.size"
                        layout="total, sizes, prev, pager, next "
                        :total="state.total"
                ></el-pagination>
            </div>

    </div>
</template>
<script setup>
    import {defineProps, reactive, watch, ref, onMounted, defineEmits} from "vue";
    import {ElMessage, ElMessageBox} from "element-plus";
    import EleProTableColumn from "@src/components/ele-pro-table-column";

    import {v4 as uuidv4} from "uuid";
    import vsfileupload from "@src/views/components/vsfileupload.vue";
    import {getCllxCxList} from "@src/api/sccbsgl";

    const props = defineProps({

    });
    const emits = defineEmits(["close"]);
    const state = reactive({
        tableData: [{}],
        appUser: {},
        total: '',
    });
    onMounted(() => {
        getTableData();
    });

    const queryParams = reactive({
        page: 1,
        total: 0,
        size: 10,
        cxmc: '',
    });

    const getTableData = () => {
        getCllxCxList({ ...queryParams, "pageType": "check" }).then(({ data }) => {
            state.tableData = data.list;
            state.total = data.total;
        });
    };

    // 保存
    const confirmData = (val) => {
        emits("checkClxx", val);
    };

    const handleSizeChange = (val) => {
        queryParams.size = val;
        queryParams.page = 1;
        getTableData()
    };
    const handleCurrentChange = (val) => {
        queryParams.page = val;
        getTableData()
    };

</script>
<style scoped>
    :deep(.el-table-fixed-column--right) {
        background-color: rgba(255, 255, 255, 1) !important;
    }

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-form-item__error) {
        top: 50%;
        transform: translateY(-50%);
        left: 40px;
    }
</style>
