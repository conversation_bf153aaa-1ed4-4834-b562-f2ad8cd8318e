<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <div class="title-text">{{ modelValue.XMMC }}标书查重对比清单</div>
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="YWJMC">
            <el-input ref="input45296" placeholder="请输入原文件名称" v-model="listQuery.YWJMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="DBWJMC">
            <el-input ref="input45296" placeholder="请输入对比文件名称" v-model="listQuery.DBWJMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              过滤
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(500px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="AWJMC" label="原文件（A）" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="BWJMC" label="对比文件（B）" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column label="文本重复" align="center">
                <el-table-column prop="WBCFSL" label="重复数量" align="center"
                                 :show-overflow-tooltip="true" width="100"></el-table-column>
                <el-table-column prop="DACFL" label="对A重复率" align="center"
                                 :show-overflow-tooltip="true" width="100"></el-table-column>
                <el-table-column prop="DBCFL" label="对B重复率" align="center"
                                 :show-overflow-tooltip="true" width="100"></el-table-column>
              </el-table-column>
              <el-table-column prop="GSXXCFSL" label="公司信息重复" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="TPCF" label="图片重复" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="ZXSJ" label="执行时间" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
<!--              <el-table-column prop="CWYZCF" label="错误一致重复" align="center"-->
<!--                               :show-overflow-tooltip="true" width="100"></el-table-column>-->
              <el-table-column prop="CZ" label="操作" align="center" width="120" fixed="right">
                <template #default="scope">
                  <el-button v-if="scope.row.SFCG==='1' && scope.row.JLZT==='1'" size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">结果查看
                  </el-button>
                  <el-tag type="success" v-else-if="!scope.row.SFCG">未查重</el-tag>
                  <el-tag type="warning" v-else-if="scope.row.SFCG==='1' && !scope.row.JLZT">查重中</el-tag>
                  <el-tag type="danger" v-else-if="scope.row.SFCG==='0'">查重失败</el-tag>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="标书查重详情"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <bsccdbxqView v-if="dialogVisible" :params="xqParams" @close="dialogVisible=false"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import bsccdbxqView from "@views/bscc/xmbsccgl/bsccdbxqView";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,bsccdbxqView},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      CCWJID: props.params.id,
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      xqParams: {},
      dialogVisible: false,
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        CCWJID: state.CCWJID
      }
      axiosUtil.get('/backend/bscc/bsccgl/selectBsccdbqdPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const viewRow = (row) => {
      state.xqParams = {editable: false, id: row.CCQDID, operation: 'view'}
      state.dialogVisible = true
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      viewRow

    }
  }

})
</script>

<style scoped>
.title-text {
  font-size: 20px;
  margin-bottom: 40px;
  width: 100%;
  text-align: center;
  color: #980404;
}
</style>
