<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;color: #2A96F9;
      border-bottom: 3px solid #2A96F9;text-align: center;padding-bottom: 2px;padding-left: 20px;padding-right: 20px;">
        招标结果表
      </div>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="getFormData">刷新</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import signImageUpload from "@views/components/signImageUpload";


export default defineComponent({
  name: '',
  components: {signImageUpload},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: true,
      role: props.fromParams.role,
      rules: {

      },
      formData: {}
    })

    const getFormData = () => {
      let params={

      }
      state.loading=true
      axiosUtil.get('/backend/', params).then((res) => {
        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={

      }
      state.loading=true
      axiosUtil.post('/backend',params).then(res=>{
        // ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        // if(state.role==='ZCR'){
        //   emit('saveFromData', {TBRQD_WCSJ: comFun.getNowTime(),TBRQD_WCZT: '1'}, {})
        //   nextTick(() => {
        //     emit('nextStep', `已完成投标人签到`)
        //   })
        // }
        state.editable=false
        state.loading=false
      })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }




    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      submitForm,
      getFormData
    }
  }

})
</script>

<style scoped>

</style>
