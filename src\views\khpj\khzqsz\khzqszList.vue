<template>
  <div class="container">
    <el-form ref="vForm" :model="tableData" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="ZQMC">
            <el-input ref="input45296" placeholder="请输期间名称" v-model="listQuery.ZQMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary"><el-icon><Search/></el-icon>查询</el-button>
            <el-button ref="button9527" type="primary" @click="addData"><el-icon><Plus/></el-icon>新建</el-button>
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="saveData"><el-icon><Check/></el-icon>保存</el-button>

          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="ZQMC" label="期间名称" align="center"
                               :show-overflow-tooltip="true" min-width="200">
                <template #default="{$index,row}">
                  <el-form-item label="" :prop="`${$index}.ZQMC`" :rules="tableRules.ZQMC" v-if="row.edit">
                    <el-input ref="input45296" placeholder="请输入期间名称" v-model="row.ZQMC" type="text" clearable>
                    </el-input>
                  </el-form-item>
                  <div v-else>{{row.ZQMC}}</div>
                </template>
              </el-table-column>

              <el-table-column prop="YXQKS" label="期间开始时间" align="center"
                               :show-overflow-tooltip="true" min-width="200">
                <template #default="{$index,row}">
                  <el-form-item label="" :prop="`${$index}.YXQKS`" :rules="tableRules.YXQKS" v-if="row.edit">
                    <el-date-picker
                        v-model="row.YXQKS"
                        type="date"
                        clearable
                        placeholder="选择期间开始时间"
                        style="width: 100%"
                        value-format="YYYY-MM-DD"
                    ></el-date-picker>
                  </el-form-item>
                  <div v-else>{{row.YXQKS}}</div>
                </template>
              </el-table-column>

              <el-table-column prop="YXQJS" label="期间结束时间" align="center"
                               :show-overflow-tooltip="true" min-width="200">
                <template #default="{$index,row}">
                  <el-form-item label="" :prop="`${$index}.YXQJS`" :rules="tableRules.YXQJS" v-if="row.edit">
                    <el-date-picker
                        v-model="row.YXQJS"
                        type="date"
                        clearable
                        placeholder="选择期间结束时间"
                        style="width: 100%"
                        value-format="YYYY-MM-DD"
                    ></el-date-picker>
                  </el-form-item>
                  <div v-else>{{row.YXQJS}}</div>
                </template>
              </el-table-column>

              <el-table-column prop="SYQK" label="状态" align="center"
                               :show-overflow-tooltip="true" width="110">
              </el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="200">
                <template #default="scope">
                  <el-button v-if="scope.row.SYSL<1 && !scope.row.edit" size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑</el-button>
                  <el-button v-if="scope.row.SYSL<1" size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.row,scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes" class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import { Search, Upload, Plus ,Check} from '@element-plus/icons-vue'
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import vsAuth from "@lib/vsAuth";

export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,Check},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery:{
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      tableRules: {
        ZQMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        YXQKS: [{
          required: true,
          message: '字段值不可为空',
        }],
        YXQJS: [{
          required: true,
          message: '字段值不可为空',
        }],
      }
    })

    const getDataList = () => {
      const requestData = () => {
        const params={
          ...state.listQuery,
        }
        axiosUtil.get('/backend/sckhpj/khzqsz/selectKhzqPage', params).then((res) => {
          state.tableData = res.data.list || []
          state.total = res.data.total
        });
      }

      if(state.tableData.find(item=>item.edit)){
        ElMessageBox.confirm('页面数据未保存，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          requestData()
        }).catch(()=>{})
      }else{
        requestData()
      }
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const submitForm = () => {
      let params={
        ZQXXList: state.tableData.filter(item=> item.edit)
      }
      axiosUtil.post('/backend/sckhpj/khzqsz/saveKhzqList', params).then((res) => {
        ElMessage.success('保存成功')
        state.tableData.forEach(item=>item.edit=false)
        getDataList()
      });
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const addData = () => {
      state.tableData.push({
        PJZQID: comFun.newId(),
        edit: true,
        CJRZH: state.userInfo.userLoginName,
        CJRXM: state.userInfo.userName,
        CJDWID: state.userInfo.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '1'
      })
    }

    const editRow = (row) => {
      row.edit = true
    }

    const deleteRow = (row,index) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.del('/backend/sckhpj/khzqsz/delKhzq?PJZQID='+row.PJZQID,null).then(res=>{
          if(res.data.ZT==='1'){
            ElMessage.success('删除成功!')
            state.tableData.splice(index,1)
          }else {
            ElMessage.error(res.data.msg)
          }
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      addData,
      deleteRow,
      editRow,
      saveData

    }
  }

})
</script>

<style scoped>
:deep(.el-form-item--default ){
  margin-bottom: 0px;
}
</style>
