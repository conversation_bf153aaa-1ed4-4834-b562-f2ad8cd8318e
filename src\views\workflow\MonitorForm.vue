<!-- 流程跟踪页面 -->
<template>
  <el-form :model="formData" ref="vForm" class="lui-page" label-position="left" label-width="0"
           size="default">
    <div class="zhyy-list-main">
    <el-row :gutter="10" style="height: 35px">
      <el-col :span="8">
        <span>发起时间：{{ formData.CJSJ }}</span>
      </el-col>
      <el-col :span="8">
        <span> 发起人：{{ formData.CJR }} </span>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="height: 35px">
      <el-col :span="8">
        <span>流程状态：</span>
        <span v-if="formData.LCZT == '1'">运行中</span>
        <span v-else-if="formData.LCZT == '3'">结束</span>
        <span v-else-if="formData.LCZT == '4'">已终止</span>
        <span v-else>未发起</span>
      </el-col>
      <el-col :span="8">
        <span>当前工作内容：{{ formData.GZNR }}</span>
      </el-col>
      <el-col :span="8">
        <span>完成时间：{{ formData.SINCCPMPLETDATE }}</span>
      </el-col>
    </el-row>
    <hr />
    <el-row :gutter="24" style="height: 50px">
      <el-col :span="24" align="center">
        <H3>业务办理跟踪详情</H3>
      </el-col>
    </el-row>
    <el-table :data="tableData" style="min-height: 300px" :border="true" class="lui-table">
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column prop="BUSINESSNAME" label="业务环节" header-align="center" align="left"></el-table-column>
      <el-table-column prop="CLR" label="办理人" header-align="center" align="left" width="300">
        <template #default="scope">
          <span>{{scope.row.CLDWFULL+ "-" + scope.row.CLDW + "(" + scope.row.CLR + ")" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="STATUS" label="进度状态" width="100" header-align="center" align="center"></el-table-column>
      <el-table-column prop="JSSJ" label="接收时间" header-align="center" align="center" width="200"></el-table-column>
      <el-table-column prop="BLSJ" label="办理时间" header-align="center" align="center" width="200"></el-table-column>
      <el-table-column prop="APPRRESULT" label="审核结果" width="100" header-align="center" align="center"></el-table-column>
      <el-table-column prop="OPINION" label="审查意见" header-align="center" align="left" width="150"></el-table-column>
      <el-table-column v-if="showChange" fixed="right" label="操作" align="center" width="220">
          <template #default="scope">
               <el-button v-if="scope.row.STATUS==='待处理'" plain size="small" class="lui-table-button" type="primary" @click="delBlr(scope.row)">删除</el-button>
              <el-button v-if="scope.row.STATUS==='待处理'" plain size="small" class="lui-table-button" type="primary" @click="changeBlr(scope.row)">变更办理人</el-button> 
              <el-button v-if="scope.row.STATUS==='已完成'&&(scope.$index+1)!=tableData.length&&formData.LCZT == '1'" plain size="small" class="lui-table-button" type="primary" @click="backThisNode(scope.row)">撤到该节点</el-button> 
              <el-button v-if="scope.row.STATUS==='已完成'&&(scope.$index+1)==tableData.length" plain size="small" class="lui-table-button" type="primary" @click="submitForm(scope.row)">撤回</el-button>
          </template>
      </el-table-column>
    </el-table>
  </div>
  </el-form>
  <el-dialog custom-class="lui-dialog" v-if="dialogVisible" v-model="dialogVisible" title="选择人员"  width="80%" append-to-body
             top="1vh">
    <searchUserList v-if="dialogVisible" @closeForm="closeForm" @confirmData="confirmData" :onlyOne="true"/>
  </el-dialog>
</template>
<script>
import api from "../../api/lc";
import axios from "axios";
import { ElLoading,ElMessage,ElMessageBox } from "element-plus";
import axiosUtil from "../../lib/axiosUtil";
import searchUserList from "./searchUserList";
export default {
  name: "MonitorForm",
  props: {
    model:{
      type:String,
      default:'scgl'
    },
    queryParams: Object,
    showChange:{
      type:Boolean,
      default: false
    },
  },
  data() {
    return {
      formData: {},
      tableData: [],
      dialogVisible: false,
      changeRow:{}
    };
  },
  components: {searchUserList},
  computed: {},
  watch: {},
  methods: {
    /**
     * 加载数据
     */
    loadData() {
      let ld = ElLoading.service({
        target: "#monitorDiv",
        text: "正在加载数据，请稍后...",
      });

      axiosUtil.post('/backend/workFlow/monitorTasks',this.queryParams).then((res) => {
          if (res.data) {
            this.formData = res.data;
            if (
              res.data.tasksMonitorList &&
              res.data.tasksMonitorList.length > 0
            ) {
              this.formData.GZNR =
                res.data.tasksMonitorList[
                  res.data.tasksMonitorList.length - 1
                ].BUSINESSNAME;
            }

            this.tableData = res.data.tasksMonitorList;
            for(let i=0;i<this.tableData.length;i++){
                if(this.tableData[i].STATUS=='已取消'){
                    this.tableData[i].STATUS='已撤回'
                }
            }
          }
          ld.close();
        })
        .catch((error) => {
          console.log(error);
          ld.close();
        });
    },
    confirmData(value){
        let params={
          USER_NAME: value.USER_NAME,
          USER_LOGINNAME: value.USER_LOGINNAME,
          ORGNA_ID: value.ORGNA_ID,
          ORGNA_NAME: value.ORGNA_NAME,
          ID: this.changeRow.ID,
          TASKID: this.changeRow.TASKID,
          RECIPIENTNAME: this.changeRow.CLR
        }
        this.dialogVisible=false
        axiosUtil.post('/backend/workFlow/updateBlr',params).then(res=>{
          this.$message.success('修改成功')
          this.loadData()
        })
    },
    delBlr(row){
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        let params={
          ID: row.ID,
          TASKID: row.TASKID,
          RECIPIENTNAME: row.CLR
        }
        axiosUtil.post('/backend/workFlow/deleteBlr',params).then((res) => {
          if(res.data==='Y'){
            ElMessage.success('删除成功')
          }else {
            ElMessage.error('删除失败，最后一位办理人')
          }
          this.loadData()
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })

    },
    changeBlr(row){
      this.changeRow=row
      this.dialogVisible=true
    },
     submitForm(row){
        axiosUtil.post('/backend/workFlow/reForcecall', {taskId:row.TASKID}).then(res => {
          if (res.message === 'success') {
            this.loadData()
            ElMessage({
                message: '撤回成功',
                type: 'success',
              })
          }
        })
    },
    backThisNode(row){
        axiosUtil.post('/backend/workFlow/backThisNode', {taskId:row.TASKID}).then(res => {
          if (res.message === 'success') {
            this.loadData()
            ElMessage({
                message: '撤回成功',
                type: 'success',
              })
          }
        })
    },
    closeForm(){
      this.dialogVisible=false
    },
  },
  created() {},
  mounted() {
    this.loadData();
  },
};
</script>
<style scoped>
</style>
