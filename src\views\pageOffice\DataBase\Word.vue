<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');
const open_params = ref('');

function Save() {
  //使用SaveFilePage属性设置后端保存方法的Controller路由地址，这个地址必须从"/"开始，并且也可以向此路由地址传递json字符串参数，示例如下：
  let saveFileUrl = "/DataBase/save";
  let paramValue = new URLSearchParams(open_params.value);//为了简单起见，这里直接使用打开时的参数。
  pageofficectrl.SaveFilePage = `${saveFileUrl}?${paramValue.toString()}`;
  //在这里写您保存前的代码
  pageofficectrl.WebSave();
  //在这里写您保存后的代码，比如判断保存结果pageofficectrl.CustomSaveResult
}

function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
  pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function AfterDocumentOpened() {
  // PageOffice的文档打开后事件回调函数
}

function openFile() {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/DataBase/Word',
    method: 'get',
    params: open_params.value
  });
}

onMounted(() => {
  //使用pageofficectrl.WindowParams获取获取父页面(此项目中为：IndexView.vue)中POBrowser.openWindow()方法的第三个参数的值,获取到的值为string类型
  open_params.value = JSON.parse(pageofficectrl.WindowParams);
  // 请求后端打开文件
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit, AfterDocumentOpened, Save };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
  <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
</template>