<template>
  <!-- 引进模板 -->
  <el-container>
    <el-header height="auto">
      <!-- Header content -->
      <el-form :model="formData" ref="form" class="lui-page" label-width="150px" size="default" inline>
        <el-row>
          <el-col :span="5">
              <el-input style="width: 200px;height: 100%" v-model="formData.MBMC" placeholder="请输入模板名称"></el-input>
          </el-col>
          <el-col :span="5">
              <el-select
                  style="width: 200px;height: 100%"
                  v-model="formData.MBLX"
                  value-key="value"
                  placeholder="请选择模板类型"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in templateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
              </el-select>
          </el-col>
          <el-col :span="5" style="display:flex;align-items: center;">
            <el-button type="primary" @click="getDataList">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-header>
    <el-main>
      <el-table
          class="lui-table"
        :data="tableData"
        ref="listTable"
        height="calc( 100% - 60px)"
        border
        stripe
        v-loading="loading"
        @select="handleSelectionChange"
      >
        <EleProTableColumn v-for="prop in tableColumn" :col="prop" :key="prop.columnKey">
        </EleProTableColumn>
      </el-table>
      <el-pagination
          @size-change="
            paginationObj.page == 1
              ? getDataList()
              : ((paginationObj.page = 1), getDataList())
          "
          @current-change="getDataList"
          v-model:current-page="paginationObj.page"
          v-model:page-size="paginationObj.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="paginationObj.total"
          background
          :pager-count="7">
      </el-pagination>
    </el-main>
    <el-footer height="auto">
      <!-- Footer content -->
      <div class="bottom-btn" style="margin-bottom: 10px">
        <el-button type="primary" size="default" @click="emits('close')">返回</el-button>
        <el-button type="primary" size="default" @click="confirm">确定</el-button>
      </div>
    </el-footer>
  </el-container>
</template>
<script setup>
import { ref, reactive, onMounted, computed, nextTick, watch } from "vue";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {
  getZrmbglPaging, //准入模板分页查询
  getZrmbglDetail, //模板明细
  getZrmbglDisabled, //模板禁用
  getZrmbglEnabled, //模板启用
  deleteZrmbgl, //模板删除
  postZrmbglCopy, //模板复制
} from "@src/api/sccbsgl.js";
import { ElMessage } from "element-plus";
const props = defineProps({
  defaultData: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(["close", "update"]);
// 上册查询条件
const formData = ref({
  MBMC: "",
  MBLX: "",
  STATUS: "1",
});
// 翻页对象
const paginationObj = ref({
  page: 1,
  size: 10,
  total: 0,
});
/**序号 */
const indexMethod = (index) => {
  return index + paginationObj.value.size * (paginationObj.value.page - 1) + 1;
};
// 模板类型下拉选项
const templateOptions = ref([
  {
    value: "QY",
    label: "企业",
  },
  {
    value: "DW",
    label: "队伍",
  },
]);
const tableColumn = ref([
  { label: "序号", type: "index", width: 55, align: "center", index: indexMethod },
  {
    type: "selection",
    width: 55,
    align: "center",
    selectable: (row) =>
      selectedType.value.includes(row.MBLXBM)
        ? selectedLeftIds.value.includes(row.MBID)
        : true,
  },
  {
    label: "模板类型",
    prop: "MBLXMC",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "模板名称",
    prop: "MBMC",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "描述",
    prop: "MBMS",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作人",
    prop: "CZR",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作时间",
    prop: "CZSJ",
    align: "center",
    showOverflowTooltip: true,
  },
]);
// 表格数据
const tableData = ref([]);
// 已选择的企业/队伍
let selRow = ref([]);
watch(
  () => props.defaultData,
  (val) => {
    if (val) selRow.value = val;
  },
  {
    immediate: true,
  }
);
/**获取数据 */
const loading = ref(false);
const listTable = ref(null);
const getDataList = () => {
  const { page, size } = paginationObj.value;
  loading.value = true;
  getZrmbglPaging({
    ...formData.value,
    page,
    size,
  })
    .then(({ data }) => {
      tableData.value = data.list ?? [];
      paginationObj.value.total = data.total ?? 0;
      nextTick(() => {
        selRow.value.forEach((item) => {
          const row = tableData.value.find((i) => i.MBID == item.MBID);
          if (row) listTable.value.toggleRowSelection(row, true);
        });
      });
    })
    .catch(() => {
      ElMessage({
        message: "查询模板列表失败",
        type: "error",
      });
    })
    .finally(() => {
      loading.value = false;
    });
};
onMounted(getDataList);
/**选择变更 */
const handleSelectionChange = (selection, row) => {
  console.log(selection, row);
  // return;
  // 判断当前元素是否被删除
  const isNoDelItem = selection.find((i) => i.MBID == row.MBID);
  if (!isNoDelItem) {
    const index = selRow.value.findIndex((i) => i.MBID == row.MBID);
    selRow.value.splice(index, 1);
    return;
  }
  // 筛选出新选中的数据存入selRow
  selRow.value.push(...selection.filter((i) => !selectedLeftIds.value.includes(i.MBID)));
};
// 复选框改变时,计算选中的类型（队伍/企业）
const selectedType = computed(() => {
  if (!selRow.value.length) return [];
  return [...new Set(selRow.value.map((item) => item.MBLXBM))];
});
// 复选框改变时,计算选中的模板ID
const selectedLeftIds = computed(() => {
  if (!selRow.value.length) return [];
  return [...new Set(selRow.value.map((item) => item.MBID))];
});

const confirm = () => {
  if (!selectedType.value.includes("QY")) {
    ElMessage.warning("请先选择一个企业");
    return;
  }
  emits("update", selRow.value);
};
</script>
<style lang="scss" scoped>
.el-container {
  height: 450px;
  :deep(.el-form--inline .el-form-item) {
    margin-bottom: 0;
  }
  :deep(.el-table__header-wrapper) {
    .el-table-column--selection {
      .el-checkbox {
        display: none;
      }
    }
  }
  .bottom-btn {
    margin-top: 10px;
    text-align: center;
  }
}
</style>
