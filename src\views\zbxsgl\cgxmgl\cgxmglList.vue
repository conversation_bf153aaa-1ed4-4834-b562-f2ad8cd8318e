<template>
    <div class="container">
        <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
                 size="default">
            <el-row :gutter="20" class="lui-search-form">
                <el-col :span="6" class="grid-cell">
                    <el-form-item label="" prop="XMMC">
                        <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" class="grid-cell">
                    <el-form-item label="" prop="XMBH">
                        <el-input ref="input45296" placeholder="请输入项目编号" v-model="listQuery.XMBH" type="text" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" class="grid-cell">
                    <el-form-item label="" prop="ZT">
                        <el-select v-model="listQuery.ZT" class="full-width-input"
                                   clearable>
                            <el-option v-for="(item, index) in ZTOptions" :key="index" :label="item.DMMC"
                                       placeholder="请选择状态"
                                       :value="item.DMXX"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="4" class="grid-cell">
                    <div class="static-content-item" style="display: flex;">
                        <el-button ref="button91277" @click="getDataList" type="primary">
                            <el-icon>
                                <Search/>
                            </el-icon>
                            查询
                        </el-button>
                        <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData">
                            <el-icon>
                                <Plus/>
                            </el-icon>
                            新建
                        </el-button>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <div class="container-wrapper">
                        <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                                  :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                                             :index="indexMethod"/>
                            <el-table-column prop="XMMC" label="采购项目名称" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="XMBH" label="项目编号" align="center"
                                             :show-overflow-tooltip="true" width="160"></el-table-column>
                            <el-table-column prop="SGSJ" label="施工时间" align="center"
                                             :show-overflow-tooltip="true" min-width="130"></el-table-column>
                            <el-table-column prop="SSDWMC" label="创建部门" align="center"
                                             :show-overflow-tooltip="true" min-width="130"></el-table-column>
                            <el-table-column prop="CJRXM" label="创建人" align="center"
                                             :show-overflow-tooltip="true" width="100"></el-table-column>
                            <el-table-column prop="CJSJ" label="创建时间" align="center"
                                             :show-overflow-tooltip="true" width="180"></el-table-column>
                            <el-table-column prop="ZTMC" label="状态" align="center"
                                             :show-overflow-tooltip="true" width="100"></el-table-column>
                            <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                                <template #default="scope">
                                    <el-button v-if="scope.row.SHZT==='0' && userLoginName === scope.row.CJRZH"
                                               size="small" class="lui-table-button" type="primary"
                                               @click="editRow(scope.row)">编辑
                                    </el-button>
                                  <el-button v-if="scope.row.SHZT==='0' && userLoginName === scope.row.CJRZH"
                                             size="small" class="lui-table-button" type="primary"
                                             @click="deleteRow(scope.row)">删除
                                  </el-button>
                                    <el-button v-else size="small" class="lui-table-button" type="primary"
                                               @click="viewRow(scope.row)">查看
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-pagination background v-model:current-page="listQuery.page"
                                       v-model:page-size="listQuery.size"
                                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                                       class="lui-pagination"
                                       @size-change="getDataList" @current-change="getDataList" :total="total">
                        </el-pagination>
                    </div>
                </el-col>
            </el-row>
        </el-form>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogVisible"
                v-model="dialogVisible"
                title="采购项目"
                @closed="closeForm"
                z-index="1000"
                top="5vh"
                width="1200px">
            <div>
                <cgxmglEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
            </div>
        </el-dialog>
    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import axiosUtil from "@lib/axiosUtil";
    import comFun from "@lib/comFun";
    import {ElMessage, ElMessageBox} from "element-plus";
    import {Plus, Search, Upload} from "@element-plus/icons-vue";
    import cgxmglEdit from "@views/zbxsgl/cgxmgl/cgxmglEdit";
    import vsAuth from "@lib/vsAuth";


    export default defineComponent({
        name: '',
        components: {Search, Upload, Plus, cgxmglEdit},
        props: {},
        setup(props, {emit}) {
            const state = reactive({
                listQuery: {
                    page: 1,
                    size: 10,
                    ZT: '',
                },
                tableData: [],
                total: 0,
                params: {},
                dialogVisible: false,
                userLoginName: vsAuth.getAuthInfo().permission.userLoginName ,

                ZRZYTree: [],
                ZTOptions: [
                    {
                        DMXX: 'WSY',
                        DMMC: '未使用',
                    },
                    {
                        DMXX: 'YSY',
                        DMMC: '已使用',
                    },
                    {
                        DMXX: 'YFQ',
                        DMMC: '已发起招标申请',
                    },
                    {
                        DMXX: 'YWC',
                        DMMC: '已完成招标',
                    }
                ],
            })

            const getDataList = () => {
                const params = {
                    ...state.listQuery,
                    orgnaId: vsAuth.getAuthInfo().permission.orgnaId,
                }
                axiosUtil.get('/backend/xsgl/cgxmgl/selectCgxmpage', params).then((res) => {
                    state.tableData = res.data.list
                    state.total = res.data.total
                });
            }

            // 新增
            const addData = () => {
                state.params = {editable: true, id: comFun.newId(), operation: 'add'}
                state.dialogVisible = true
            }
            // 编辑
            const editRow = (row) => {
                state.params = {editable: true, id: row.XMID, operation: 'edit'}
                state.dialogVisible = true
            }

            const viewRow = (row) => {
                state.params = {editable: false, id: row.XMID, operation: 'view'}
                state.dialogVisible = true
            }

            const deleteRow = (row) => {
                ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axiosUtil.del('/backend/xsgl/cgxmgl/delCgxm?XMID=' + row.XMID, null).then(res => {
                        ElMessage.success({
                            message: '删除成功!'
                        });
                        getDataList()
                    })
                }).catch(() => {
                    ElMessage.info({
                        message: '已取消删除'
                    });
                });
            }

            const getDMBData = async (DMLBID, resList) => {
                let params = {
                    DMLBID
                }
                const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
                state[resList] = res.data
            }

            const indexMethod = (index) => {
                return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
            }
            const closeForm = () => {
                state.dialogVisible = false
                getDataList()
            }

            const getZrzyList = () => {
                let params = {}
                axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
                    state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0')
                });
            }

            const viewGL = (row) => {
                ElMessage.warning("项目已关联选商申请")
            }

            onMounted(() => {
                getZrzyList()
                getDataList()
            })

            return {
                ...toRefs(state),
                getDataList,
                indexMethod,
                editRow,
                addData,
                closeForm,
                deleteRow,
                viewRow,
                viewGL

            }
        }

    })
</script>

<style scoped>

</style>
