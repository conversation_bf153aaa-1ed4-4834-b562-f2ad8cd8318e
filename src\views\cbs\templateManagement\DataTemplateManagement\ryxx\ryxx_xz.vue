<template>
  <div style="height: calc(100% - 40px);">
    <el-form style="height: 100%;" size="default" class="lui-page" v-loading="state.loading">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="userName">
            <el-input ref="input45296" placeholder="人员名称或身份证" @input="getDataList"
                      v-model="state.listQuery.XMSFZ" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="18" style="margin-left: auto">
          <el-button type="primary" @click="exportModel('ryxx')"><el-icon><Download/></el-icon>人员模板</el-button>
          <excelImport bntName="导入人员信息" key="ryxx" url="/backend/importController/importRyxx" :importData="importRyData" @refresh="getDataList"></excelImport>
          <el-button type="primary" @click="exportModel('zsxx')"><el-icon><Download/></el-icon>持证信息模板</el-button>
          <excelImport bntName="导入持证信息" key="zsxx" url="/backend/importController/importRyzsxx" :importData="importRyzsData" @refresh="getDataList"></excelImport>
          <el-button type="primary" class="lui-button-add" @click="addRow"><el-icon><Plus/></el-icon>添加</el-button>
        </el-col>
      </el-row>
      <el-table
          class="lui-table"
        highlight-current-row
        size="default"
        ref="table"
        fit
          height="50vh"
        :border="false"
        :data="state.tableData"
      >
        <EleProTableColumn
          v-for="prop in state.tableColumn"
          :col="prop"
          :key="prop.columnKey"
        >
          <template #DYXL="{ row }">
            <div>{{ryxl.find((i) => i.DMXX == row.DYXL)?.DMMC}}</div>
          </template>
          <template #CZZK="{ row }">
            <!--                    {{ row.ZSXX ? row.ZSXX.map(x => x.ZSMC).toString() : '' }}showZs-->
            <el-button :disabled="false" class="lui-table-button" @click="showZs(row, $index)">查看</el-button>
          </template>

          <template #opration="{ row, $index }">
            <div style="display: flex;gap: 5px;justify-content: center">
              <el-button class="lui-table-button" @click="chooseData(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="editRow(row, $index)">编辑</el-button>
              <el-button class="lui-table-button" @click="delRow(row, $index)">删除</el-button>
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-form>
  </div>
  <el-dialog
      custom-class="lui-dialog"
      title="人员信息编辑"
      v-model="editVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      top="50px"
      width="1200px"
      @close="() => {getDataList()}"
  >
    <ryxxEdit
        :editable="true"
        :editData="editData"
        @updateData="updateData"
        @close="editVisible = false"
    />
  </el-dialog>
  <el-dialog
      custom-class="lui-dialog"
    title="证书信息"
    v-model="zsDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    top="100px"
    width="1200px"
  >
    <el-table
      highlight-current-row
      size="default"
      ref="table"
      fit
      class="lui-table"
      height="300px"
      :border="false"
      :data="currentRow.ZSXX"
    >
      <EleProTableColumn
        v-for="prop in state.fileColumn"
        :col="prop"
        :key="prop.columnKey"
      >
        <template #fileList="{ row, $index }">
          <vsfileupload
              :maxSize="10"
            class="fjsc"
            :editable="false"
            :index="$index"
            :busId="row.ZSZSJID"
            :key="row.ZSZSJID"
            ywlb="DWZTBGFJ"
            busType="dwxx"
            :limit="100"
          ></vsfileupload>
        </template>
        <template #ZSLX="{ row }">
          {{ zslx[row[prop.prop]] }}
        </template>
      </EleProTableColumn>
    </el-table>
  </el-dialog>
</template>
<script setup>
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import { axios } from "@src/assets/core/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import ryxxEdit from "./ryxx_xzEdit.vue";
import { v4 as uuidv4 } from "uuid";
import { getCommonSelectDMB } from "@src/api/cbsxx.js";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import axiosUtil from "../../../../../lib/axiosUtil";
import {auth} from "../../../../../assets/core";
import { Search, Select, Plus, Upload, Download } from '@element-plus/icons-vue'
import comFun from "../../../../../lib/comFun";
import excelImport from "@src/views/components/excelImport.vue";

const props = defineProps({
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  },
  currentData: {
    type: Object,
    default: {},
  }
});
// 导入人员数据
const importRyData = {
  CJRZH: props.TYXYDM,
  CJRXM: auth.getPermission().userName,
  CJDWID: auth.getPermission().orgnaId,
  XGRZH: props.TYXYDM,
  SHZT: '1'
}
// 导入人员证书数据
const importRyzsData = {
  CJRZH: auth.getPermission().userLoginName,
  CJRXM: auth.getPermission().userName,
  CJDWID: auth.getPermission().orgnaId,
  XGRZH: auth.getPermission().userLoginName,
  SHZT: '1',
  ZSCYZLXDM: 'DWCY',
  ZSDLDM: 'RYZS'
}
const zsDialog = ref(false);
const showZs = (row) => {
  currentRow.value = row;
  zsDialog.value = true;
};
const zslx = ref({
  HSE: "HSE",
  HB: "环保",
  AQZS: "安全证书",
});
const rylx = ref({});
const currentRow = ref({});
const getRylx = () => {
  getCommonSelectDMB({ DMLBID: "RYLX" }).then((res) => {
    res.data.forEach((x) => (rylx.value[x.DMXX] = x.DMMC));
  });
};

const ryxl = ref([]);
const getRyxl = () => {
  getCommonSelectDMB({ DMLBID: "XL" }).then((res) => {
    ryxl.value = res.data;
  });
};

const changePro = (row) => {
  row.ZYMC = props.proDetails.filter((x) => x.ZYBM == row.ZYBM)[0].ZYMC;
};
const state = reactive({
  tableData: [],
  listQuery:{},
  loading: false,
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    {
      label: "姓名",
      prop: "RYXM",
      align: "center",
      //width: 150,
    },
    {
      label: "岗位",
      prop: "GWJZW",
      align: "center",
      //width: 150,
    },
    {
      label: "学历",
      prop: "DYXL",
      align: "center",
      //width: 150,
      slot: "DYXL",
    },
    {
      label: "工作年限",
      prop: "BGGZSC",
      align: "center",
      //width: 150,
    },
    {
      label: "持证信息",
      prop: "CZZK",
      align: "center",
      width: 150,
      slot: "CZZK",
    },
    {
      label: "操作",
      align: "center",
      width: 220,
      fixed: "right",
      slot: "opration",
      hide: !props.editable
    },
  ],
  fileColumn: [
    {
      label: "序号",
      type: "index",
      align: "center",
    },
    {
      label: "证书类型",
      prop: "ZSLBDM",
      align: "center",
      showOverflowTooltip: true,
      slot: "ZSLX",
    },
    {
      label: "证书名称",
      prop: "ZSMC",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "证书编号",
      prop: "ZSBH",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "有效开始日期",
      prop: "YXQKS",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "有效结束日期",
      prop: "YXQJS",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "附件",
      prop: "fileList",
      align: "center",
      showOverflowTooltip: true,
      slot: "fileList",
    },
  ],
});


const addRow = () => {
  const userinfo = auth.getPermission();
  let userParams={
    CJRZH: props.TYXYDM,
    CJRXM: userinfo.userName,
    CJDWID: userinfo.orgnaId,
    CJSJ: comFun.getNowTime(),
    SHZT: '1',
  }
  let params={
    edit: true,
    CYZSJID: comFun.newId(),
    ZSXX: [],
    ...userParams,
  }

  editIndex.value = state.tableData.length;
  editData.value = params;
  editVisible.value = true;

}

const editRow = (row,index) => {
  editIndex.value = index;
  editData.value = row;
  editVisible.value = true;
}


const updateData = (val, isAdd) => {
  saveRow(val,isAdd)
};

const saveRow = (params,isAdd) => {
  axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveRynlXxx',{tableData: [params]}).then(r=>{
    ElMessage.success('保存成功')
    if (isAdd) {
      addRow()
    } else {
      editVisible.value = false;
    }
    emit('updateEditData',params)
  })
}

const delRow = (row, index) => {
  ElMessageBox.confirm(
      '确定删除该条数据?',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(() => {
    let params={
      CYZSJID: row.CYZSJID,
      SHZT: '2'
    }
    axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveRynlXxx',{tableData: [params]}).then(r=>{
      state.tableData.splice(index,1)
      ElMessage.success('删除成功')
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除',
    })
  })
}



const emit=defineEmits(['updateChooseData','updateEditData'])
const chooseData = (row, index) => {
  emit('updateChooseData',row)
};
const getDataList = () => {
  const userinfo = auth.getPermission();
  let params = {
    ...state.listQuery,
    orgId: userinfo.orgnaId,
    TYXYDM: props.TYXYDM,
    MBMXID: props.currentData.MBMXID,
  }
  state.loading = true
  axiosUtil.get('/backend/sccbsgl/cbsyj/ytxxx/selectRynlXxx', params).then((res) => {
    state.tableData = res.data || []
    state.loading = false
  })
}

const exportModel = (lx) => {
  const a = document.createElement('a');
  if(lx === 'ryxx'){
    a.href = "../../../../../../static/excel/ryxxModel.xlsx";
    a.download = "人员导入模版";
  }else if(lx === 'zsxx'){
    a.href = "../../../../../../static/excel/ryzsxxModel.xlsx";
    a.download = "人员证书信息模版";
  }
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}


onMounted(()=>{
  getDataList()
})



const info = ref({});
const editVisible = ref(false);
const editIndex = ref(0);
const editData = ref({});



onMounted(() => {
  getRylx();
  getRyxl()
});
</script>
<style scoped>
::v-deep .el-icon{
  display: inline;
}
</style>
