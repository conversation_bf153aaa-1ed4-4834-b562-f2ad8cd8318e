<template>
  <div style="height: 100%">
    <el-table ref="dataTable" :data="tableData" class="lui-table" height="100%"
              :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
              :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
      <el-table-column prop="YWMC" label="类型" fixed="left" align="left" :show-overflow-tooltip="true" width="350">
        <template #default="{row,$index}">
          <span style="color: #F56C6C" v-if="row.REQUIRED==='1'">*</span>{{row.YWMC}}
        </template>
      </el-table-column>
      <el-table-column prop="WJMC" label="文件名称" :fixed="false" align="left">
        <template #default="{row}">
          <el-upload
              name="files"
              class="upload-demo"
              :with-credentials="true"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :disabled="!editable"
              :file-list="row.fileList">
          </el-upload>
        </template>
      </el-table-column>
      <el-table-column fixed="right" :fixed="false" align="center" label="操作"
                       :width="100" v-if="editable">
        <template #default="{row}">
          <el-upload
              name="files"
              class="upload-demo"
              :action="uploadUrl"
              :headers="headers"
              :multiple="row.MULTIPLE ? row.MULTIPLE: true"
              :accept="row.ACCEPT ? row.ACCEPT: '.pdf,.jpj,.jpej,.png,.jpg,.jpeg,.gif,.xlsx,.docx,.doc,.ppt,.txt'"
              :with-credentials="true"
              :data="{busType: 'dwxx',busId: busId,standbyField0: row.YWLB,prefix: prefix+row.YWLB+'/'}"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :on-success="(res)=>{onSuccess(res,row)}"
              :before-upload="(file)=>{return beforeUpload(file,row)}"
              :limit="row.LIMITS ? row.LIMITS: 10"
              :disabled="!editable"
              :on-exceed="handleExceed"
              :show-file-list="false"
              :file-list="row.fileList">
            <el-button size="small" class="lui-table-button" v-if="editable">上传</el-button>
            <div slot="tip" class="el-upload__tip" v-if="editable&&row.SHOWTIP==='true'">
              {{row.TIPS}}
            </div>
          </el-upload>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        z-index="10001"
        top="1vh"
        :title="title"
        width="80%">
      <div>
        <div>
          <pdfView :source="filePath"/>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { CircleCheck } from '@element-plus/icons-vue'
import {defineComponent, onMounted, reactive, toRefs} from "vue";
import { preview } from 'vue3-preview-image' // 使用setup组合式api时引入方法调用
import VueOfficePdf from '@vue-office/pdf'
import axiosUtil from "../../lib/axiosUtil";
import axios from "axios";
import pdfView from "vue-pdf-embed";
import {ElMessage} from "element-plus";
export default defineComponent({
  name: "test",
  components: {CircleCheck,VueOfficePdf,pdfView},
  props: {
    YWLX: {  //业务类型
      type: String,
      required: true
    },
    busId: { //附件业务ID
      type: String,
      required: true,
    },
    editable:{
      type: Boolean,
      default: true
    },
    fileTableData:{
      type: Array,
      default: []
    },
    viewType:{ //预览模式YL；下载模式XZ
      type: String,
      default:'XZ'
    },
    prefix: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}){
    const state=reactive({
      tableData:[],
      uploadUrl:'/backend/minio/upload',
      headers: {Authorization: "Bearer " + 'getToken()'},
      beforeRemoveMessage: true,
      dialogVisible: false,
      title:'',
      filePath:'',
    })
    const loadTableData = () => {
      let params={
        YWLX:props.YWLX
      }
      axiosUtil.get('/backend/cbsxx/common/selectFjb', params).then(res=>{
        state.tableData=res.data
        state.tableData.forEach(item=>{
          loadFileList(item)
        })
      })
    }
    const loadFileList = (item) => {
      if (props.busId) {
        axios.post('/backend/minio/list', {busId: props.busId, standbyField0: item.YWLB}).then((res) => {
          let fileList = [];
          for (let i = 0; i < res.data.data.length; i++) {
            let file = res.data.data[i];
            fileList.push({
              name: file.fileName,
              id: file.id,
              operationId: file.operationId,
              mongoDBId: file.downloadId,
              type: file.fileType,
            });
          }
          item.fileList = fileList
          emit('update:fileTableData',state.tableData)
        })
      }
    }
    const handlePreview = (file) => {
      if(props.viewType==='YL'){
        state.filePath = "/backend/minio/download?id=" + file.id;
        state.fileType = file.type;
        state.title = file.name+'预览';
        if (state.fileType == '.png' || state.fileType == '.jpg' || state.fileType == '.jpeg' || state.fileType == '.gif'){
          preview(state.filePath)
        }else{
          state.dialogVisible = true;
        }
      }else if(props.viewType==='XZ'){
        window.open('/backend/minio/download' + "?id=" + file.id)
      }
    }
    const handleRemove = (file) => {
      if(file.id){
        axios.post('/backend/minio/del', {id: file.id, delFlag: "1"}).then((res) => {
        })
      }

    }
    const beforeRemove = (file, fileList) => {}
    const onSuccess = (res,row) => {
      loadFileList(row)
    }
    const beforeUpload = (file,row) => {
      if(row.MAXSIZE){
        const isLt2M = file.size / 1024 / 1024 < Number(row.MAXSIZE)
        if(!isLt2M){
          ElMessage.error(`文件大小超过限制，请上传小于${row.MAXSIZE}M的文件`)
          return false
        }

      }
      return true
    }
    const handleExceed = (files, fileList) => {}
    const toLog=(e)=>{
    }

    const validateData = () => {
      return new Promise((resolve, reject) => {
        let errFile=[]
        state.tableData.forEach(item=>{
          if(item.REQUIRED==='1' && (!item.fileList || item.fileList.length===0)){
            errFile.push(item.YWMC)
          }
        })
        if (errFile.length===0){
          resolve(true)
        }else {
          reject('请上传'+errFile.join('，')+'等文件')
        }
      })
    }

    onMounted(()=>{
      loadTableData()
    })
    return{
      ...toRefs(state),
      toLog,
      handlePreview,
      handleRemove,
      beforeRemove,
      onSuccess,
      handleExceed,
      beforeUpload,
      validateData
    }
  }
})
</script>

<style scoped>
/deep/.el-upload{
  display: unset;
}
/deep/.el-upload-list {
  margin: 0 0 0;
}
</style>
