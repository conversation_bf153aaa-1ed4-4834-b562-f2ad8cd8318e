<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="width: 100%;text-align: center">
        <h2>{{ formData.XMXX.XMMC }}{{GJC[formData.XMXX.XSFS][0]}}候选人公示</h2>
        <h4>发布日期：{{ formData.CJSJ }}</h4>
      </div>

      <div style="padding: 20px 100px;min-height: 300px;overflow: auto;border-top: rgba(203,207,213,0.62) 1px solid">
        <div style="font-family: 宋体;font-size: 18px;color: black">
          <p style="text-indent: 4ch;">
            {{dataToCh(formData.XMXX.PBRQ)}}开标的（<nobr>{{ formData.XMXX.XMMC }}</nobr>）经依法组建的评标委员会的评审，{{GJC[formData.XMXX.XSFS][0]}}候选人均符合{{GJC[formData.XMXX.XSFS][1]}}文件要求的资格能力条件，结果公示如下：
          </p>
          <div v-for="(item,index) in formData.XSBDList" :key="index">
            <div v-if="formData.XMXX.SFFBD==='1'">{{'标段：'+(item.BDMC || '')}}</div>
            <div v-for="(hh,hi) in head" :key="hi" style="display:flex;">
              <div :class="{'header-column': true,'last-row': hi===head.length-1,'last-col': item.JGMXList.length===0}" style="width: 150px">{{hh.name}}</div>
              <div :class="{'text-column': true,'last-row': hi===head.length-1,'last-col': id===item.JGMXList.length-1}" v-for="(ii,id) in item.JGMXList" :key="id">
                {{ii[hh.prop]}}
              </div>
            </div>
          </div>
          <p style="text-indent: 4ch;">
            公示日期：{{timeToCh(formData.GSSJKS)}}起至{{timeToCh(formData.GSSJJS)}}止。如有异议，请在公示截止日期前向{{GJC[formData.XMXX.XSFS][1]}}人书面提出，异议文件须由法定代表人或委托代理人签字，并加盖法人公章，发送扫描件的同时将原件邮寄{{GJC[formData.XMXX.XSFS][1]}}人处。
          </p>
          <p style="text-indent: 4ch;">
            地&nbsp;址：{{formData.DZ}}
          </p>
          <p style="text-indent: 4ch;">
            邮&nbsp;编：{{formData.YB}}
          </p>
          <p style="text-indent: 4ch;">
            联系人：{{formData.LXR}}
          </p>
          <p style="text-indent: 4ch;">
            电&nbsp;话：{{formData.LXDH}}
          </p>
          <p style="text-indent: 4ch;">
            邮&nbsp;箱：{{formData.YX}}
          </p>

        </div>

      </div>
      <el-row>
        <el-col :span="24" class="grid-cell no-border-bottom" style="display: flex">
          <h3 style="margin-left: 50px">公示文件：</h3>
          <vsfileupload
              style="margin-left: 10px"
              :editable="editable"
              :busId="params.id"
              :key="params.id"
              ywlb="GSWJ"
              busType="GSWJ"
              :limit="100"
          ></vsfileupload>
        </el-col>
      </el-row>



      <div style="width: 100%;margin-top: 10px;justify-content: center;display: flex;margin-bottom: 10px">
        <el-button @click="closeForm">返回</el-button>
      </div>


    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import vsfileupload from "@views/components/vsfileupload";


export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      JGGSID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '0',

        XMXX: {
          XSFS: 'GKZB'
        },

        XSBDList: []
      },
      rules: {},

      GGLXOptions: [],
      TZFSOptions: [],

      openCollapse: ['1'],

      dialogVisible: false,
      hhParams: {},
      title: '',

      head:[
        {name: '排序',prop: 'PX'},
        {name: '投标人名称',prop: 'DWMC'},
        {name: '投标价格',prop: 'BJ'},
        {name: '项目负责人姓名',prop: 'XMJL'},
        {name: '备注',prop: 'ZBJMS'},
      ],

      GJC: {
        JB: ['成交','采购','响应'],
        JJ: ['成交','采购','响应'],
        GKJB: ['成交','采购','响应'],
        GKJJ: ['成交','采购','响应'],
        GKZB: ['中标','招标','投标'],
        YQZB: ['中标','招标','投标'],
      },

    })



    const getFormData = () => {
      let params = {
        JGGSID: state.JGGSID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/zbjggs/selectZbjgById', params).then((res) => {
        state.formData = res.data
        let SZ='一二三四五六七八九'
        state.formData.XSBDList.forEach(item=>{
          item.JGMXList.forEach((ii,ind)=>{
            ii.PX=`第${SZ.charAt(ind)}${state.GJC[state.formData.XMXX.XSFS][0]}候选人`
          })
        })
        state.loading = false
      })
    }


    const dataToCh = (data) => {
      if(!data){
        return ''
      }
      let _data=data.substring(0,10)
      let dataList=_data.split('-')
      return dataList[0]+'年'+dataList[1]+'月'+dataList[2]+'日'
    }
    const timeToCh = (data) => {
      if(!data){
        return ''
      }
      let _data=data.substring(0,10)
      let _time=data.substring(11)
      let dataList=_data.split('-')
      let timeList=_time.split(':')
      return dataList[0]+'年'+dataList[1]+'月'+dataList[2]+'日'+timeList[0]+'时'
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const closeMsg = () => {
      emit('closeMsg')
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()
      getDMBData("GGLX", "GGLXOptions")
      getDMBData("TZFS", "TZFSOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      closeMsg,
      timeToCh,
      dataToCh
    }
  }

})
</script>

<style scoped>
@import '@vueup/vue-quill/dist/vue-quill.snow.css';
/deep/ .el-collapse-item__header {
  background-color: #dcdfef !important;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
  font-size: 16px;
  color: black !important;
}
.header-column{
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  border-left: 1px solid black;
  border-top: 1px solid black;
}
.text-column{
  display: flex;
  align-items: center;
  min-height: 40px;
  border-left: 1px solid black;
  border-top: 1px solid black;
  flex: 1;
  padding-left: 5px;
  padding-right: 5px;
}
.text-center{
  justify-content: center;
}
.last-row{
  border-bottom: 1px solid black;
}
.last-col{
  border-right: 1px solid black;
}
</style>
