<template>
  <div style="padding: 10px">
    <div class="message-item" v-for="(item,index) in tableData" :key="index">
      <div class="user-header">
        <div class="user-icon"><el-icon><UserFilled /></el-icon></div>
        <div>系统提示 {{item.CJSJ}}</div>
      </div>

      <div class="step-info">
        当前阶段: {{item.DQJD}}
      </div>

      <div class="step-message">
        {{item.NR}}
      </div>

      <div class="step-info">
        下一阶段: {{item.XYJD}}
      </div>


    </div>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {UserFilled} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";


export default defineComponent({
  name: '',
  components: {UserFilled},
  props: {
    params: {
      type: Object,
      default: {},
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      tableData: []
    })

    const getDataList = () => {
      if(props.params.KPBYXID){
        const params = {
          KPBYXID:  props.params.KPBYXID
        }
        axiosUtil.get('/backend/kpbyx/xxck/selectXxckList', params).then((res) => {
          state.tableData = res.data || []
        });
      }
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList

    }
  }

})
</script>

<style scoped>
.user-header{
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2A96F9;
  font-weight: bolder;
}
.user-icon{
  background-color: #a6cef6;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 23px;
  color: white;
  text-align: center;

}

.message-item{
  border-bottom: 1px solid #d2d2d2;
  padding: 10px;
  margin-bottom: 20px;
}

.step-info{
  margin-left: 30px;
  color: #fdbc45;
}
.step-message{
  margin-left: 50px;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
