import {axios} from "../assets/core/index";
import {ElMessage} from 'element-plus';

/**
 * 使用axios发送get请求
 * @param {*} url 请求地址
 * @param {*} params 请求参数
 * @returns Promise对象，后面跟then((data)=>{...todo}).catch((Error)=>{...todo})
 */
function get(url,params){
    return new Promise((resolved,rejected)=>{
        axios.get(url,{params:params}).then(function(response){
            if(response && response.data.code && response.data.code===1){
                log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(params),LOG_TYPE:'OPERATION',OPRATE_TYPE:'GET',OPRATE_RESULT:'1'})
                resolved(response.data)
            }else if(response && response.data && response.data.meta && response.data.meta.success){
                log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(params),LOG_TYPE:'OPERATION',OPRATE_TYPE:'GET',OPRATE_RESULT:'1'})
                resolved(response.data)
            }else if(response && response.data && response.data.meta && response.data.meta.status==200){
                log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(params),LOG_TYPE:'OPERATION',OPRATE_TYPE:'GET',OPRATE_RESULT:'1'})
                resolved(response.data)
            }else{
                //log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(params),LOG_TYPE:'OPERATION',OPRATE_TYPE:'GET',OPRATE_RESULT:'0'})
                ElMessage({
                    message: '返回数据错误',
                    type: 'error',
                })
                throw new Error("服务端异常："+JSON.stringify(response.data.message))
            }
        }).catch((err)=>{
            rejected(new Error(err.message));
        });
    })

}

/**
 * 使用axios发送get请求
 * @param {*} url 请求地址
 * @param {*} params 请求参数
 * @returns Promise对象，后面跟then((data)=>{...todo}).catch((Error)=>{...todo})
 */
function post(url,param){
    return new Promise((resolved,rejected)=>{
        axios.post(url,param).then(function(response){
            if(response&& response.data &&  response.data.code && response.data.code===1){
                log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(param),LOG_TYPE:'OPERATION',OPRATE_TYPE:'POST',OPRATE_RESULT:'1'})
                resolved(response.data)
            }else if(response && response.data && response.data.meta && response.data.meta.success){
                log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(param),LOG_TYPE:'OPERATION',OPRATE_TYPE:'POST',OPRATE_RESULT:'1'})
                resolved(response.data)
            }else if(response && response.data && response.data.meta && response.data.meta.status==200){
                log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(param),LOG_TYPE:'OPERATION',OPRATE_TYPE:'POST',OPRATE_RESULT:'1'})
                resolved(response.data)
            }else{
                //log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(param),LOG_TYPE:'OPERATION',OPRATE_TYPE:'POST',OPRATE_RESULT:'0'})
                ElMessage({
                    message: '返回数据错误',
                    type: 'error',
                })
                throw new Error("服务端异常："+JSON.stringify(response.data.message))
            }
        }).catch((err)=>{
            rejected(new Error(err.message));
        });
    })
}

/**
 * 使用axios发送put请求
 * @param {*} url 请求地址
 * @param {*} params 请求参数
 * @returns Promise对象，后面跟then((data)=>{...todo}).catch((Error)=>{...todo})
 */
function put (url, param) {
    return new Promise((resolved,rejected)=>{
        axios.put(url, param).then(function (response) {
            if(response && response.data.code && response.data.code===1){
                log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(param),LOG_TYPE:'OPERATION',OPRATE_TYPE:'PUT',OPRATE_RESULT:'1'})
                resolved(response.data)
            }else{
                ElMessage({
                    message: '返回数据错误',
                    type: 'error',
                })
                throw new Error("服务端异常："+JSON.stringify(response.data.message))
            }
        }).catch((err)=>{
            rejected(new Error(+err.message));
        });
    })
}

/**
 * 使用axios发送del请求
 * @param {*} url 请求地址
 * @param {*} params 请求参数
 * @returns Promise对象，后面跟then((data)=>{...todo}).catch((Error)=>{...todo})
 */
function del (url, param) {
    return new Promise((resolved,rejected)=>{
        axios.delete(url, param).then(function (response) {
            if(response && response.data.code && response.data.code===1){
                log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(param),LOG_TYPE:'OPERATION',OPRATE_TYPE:'DELETE',OPRATE_RESULT:'1'})
                resolved(response.data)
            }else{
                ElMessage({
                    message: '返回数据错误',
                    type: 'error',
                })
                throw new Error("服务端异常："+JSON.stringify(response.data.message))
            }
        }).catch((err)=>{
            rejected(new Error(err.message));
        });
    })
}

function downloadFile(url, param, fileName) {
    return new Promise((resolved,rejected)=>{
        axios.get(url,{params:param, responseType:"blob"}).then(function(response){
            // log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(param),LOG_TYPE:'OPERATION',OPRATE_TYPE:'DOWNLOAD',OPRATE_RESULT:'1'})
           let blob = new Blob([response.data]);
           let url = window.URL.createObjectURL(blob);
           let a = document.createElement('a');
           a.href = url;
           a.download = fileName;
           a.click();
           window.URL.revokeObjectURL(url);
        }).catch((err)=>{
            rejected(new Error("AXIOS调用错误："+err.message));
        });
    })
}
/**
 * 导出Excel
 * @param {*} url 请求地址
 * @param {*} params 请求参数
 */
function exportExcel(url,params) {
    axios({
        method: 'post',
        url,
        data: params,
        responseType: 'blob', //二进制流
    }).then(resp => {
        // log({LOG_CONTENT:url,METHOD:'',PARAMES:JSON.stringify(params),LOG_TYPE:'OPERATION',OPRATE_TYPE:'DOWNLOAD',OPRATE_RESULT:'1'})
        const { headers } = resp;
        const blob = new Blob([resp.data], { type: headers['Content-Type'] });
        const url = window.URL.createObjectURL(blob);
        const aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", params.title + '.xls');
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象 */
    }).catch(error => {
        // this.$notify({
        //     title: "警告",
        //     message: "下载出错" + error,
        //     type: "warning",
        //     duration: 2000,
        //     offset: 80
        // });
    });
}

/**
 * 上传文件
 * @param url
 * @param param
 * @returns {Promise<unknown>}
 */
function uploadFile(url, param) {
    return new Promise((resolved,rejected)=>{
        axios.post(url,param,{headers: { "Content-Type": "multipart/form-data"}}).then(function(response){
            resolved(response.data)
        }).catch((err)=>{
            rejected(new Error(err.message));
        });
    })
}


/**
 * 记录访问日志
 * @param {*} url 请求地址
 * @param {*} params 请求参数
 * @returns Promise对象，后面跟then((data)=>{...todo}).catch((Error)=>{...todo})
 */
 function log(param){
    return new Promise((resolved,rejected)=>{
        return true;
        axios.post('/backend/sys/saveLog',param).then(function(response){
            if(response&& response.data &&  response.data.code && response.data.code===1){
                resolved(response.data)
            }else if(response && response.data && response.data.meta && response.data.meta.success){
                resolved(response.data)
            }else if(response && response.data && response.data.meta && response.data.meta.status==200){
                resolved(response.data)
            }else{
                ElMessage({
                    message: '返回数据错误',
                    type: 'error',
                })
                throw new Error("服务端异常："+JSON.stringify(response.data.message))
            }
        }).catch((err)=>{
            rejected(new Error(err.message));
        });
    })
}

export default {get,post,put,del, downloadFile,exportExcel,uploadFile,log}
