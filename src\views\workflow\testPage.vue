<template>
  <div>
    <el-input style="margin-top: 20px" v-model="shyj" type="textarea" :rows="5" placeholder="请输入审核意见"></el-input>
    <div style="text-align: center; height: 40px; padding: 10px">
      <el-button type="primary" @click="onConfirm">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";


export default defineComponent({
  name: '',
  components: {},
  props: {
    apprValue: String,
    params: Object,
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      shyj: "",
      sffsdx: '1',
      blrArray: [],
      showBlrXz: false,
      nextPerformer: [],
      blfs:'BR'
    })


    const onConfirm = () => {
      emit("confirm", state.shyj, state.sffsdx, state.nextPerformer)
    }

    const onCancel = () => {
      emit("close")
    }

    onMounted(() => {
      if (props.apprValue !== '0') {
        state.shyj = '同意'
      }
      emit('changeTitle','这是一个测试审核页面')
      emit('changeWidth',1000)

    })

    return {
      ...toRefs(state),
      onCancel,
      onConfirm

    }
  }

})
</script>

<style scoped>

</style>
