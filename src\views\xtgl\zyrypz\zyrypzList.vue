<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0" size="default">
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="10" class="grid-cell">
          <div class="static-content-item" style="display: flex;height: 40px">
            <el-row :gutter="20" class="lui-search-form">
              <el-col :span="20" class="grid-cell">
                <el-form-item label="" prop="DWMC" style="width: 100%">
                  <el-input ref="input45296" placeholder="请输入单位名称" v-model="listQuery.DWMC" type="text" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4" class="grid-cell">
                <el-button ref="button91277" @click="getDataList" type="primary"><el-icon><Search/></el-icon>查询</el-button>
              </el-col>
            </el-row>
          </div>
          <div class="container-wrapper" style="margin-top: 20px">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 250px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      @row-click="checkTableRow"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column v-if="true" prop="ORGNA_TWO_NAME" label="组织机构名称" align="left"
                               :show-overflow-tooltip="true" min-width="150"></el-table-column>
              <el-table-column v-if="true" prop="ORGNA_TWO_ID" label="组织机构ID" align="center"
                               :show-overflow-tooltip="true" width="150"></el-table-column>

            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes" class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
        <el-col :span="14" class="grid-cell">
          <div class="card">
            <div class="card-header">
              {{ checkRow?.ORGNA_TWO_NAME ? '组织机构：'+checkRow.ORGNA_TWO_NAME : '请选择组织机构' }}
              <el-button type="primary" style="float: right" @click="addBatch">批量添加</el-button>
            </div>
            <div class="card-body">
              <div style="height: 100%">
                <el-tree
                    ref="myTree"
                    :data="treeData"
                    node-key="ZYBM"
                    :expand-on-click-node="true"
                    show-checkbox
                    :props="{label:'ZYMC',children: 'children'}"
                    @node-click="checkNode">
                  <template #default="{ node, data }">
                    <div style="display:flex;justify-content: space-between;width: 100%;align-items: center">
                      <div>{{data.ZYMC}}
                        <span v-if="data.FZDWMC">({{data.FZDWMC}})</span>
                      </div>
                      <div style="display: flex;justify-content: space-between;gap: 100px">
                        <div style="display: flex;gap: 10px;align-items: center">
                          <div style="width: 200px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">{{data.ZYGLRY}}</div>
                          <el-popover
                              v-if="data.ZYGLRY"
                              title=""
                              trigger="hover"
                              :content="data.ZYGLRY">
                            <template #reference>
                              <el-icon><InfoFilled/></el-icon>
                            </template>
                          </el-popover>
                        </div>
                        <el-button v-if="node.isLeaf" link type="primary" size="small" @click="editRow(data)">添加人员</el-button>
                      </div>
                    </div>
                  </template>
                </el-tree>

              </div>
            </div>
          </div>


        </el-col>
      </el-row>

      <el-dialog
          custom-class="lui-dialog"
          :close-on-click-modal="false"
          v-if="dialogVisible"
          v-model="dialogVisible"
          title="人员选择"
          @closed="getTreeData"
          z-index="1000"
          width="1200px">
        <div>
          <zyrypzEdit v-if="dialogVisible" :params="params" @closeDialog="dialogVisible=false"/>
        </div>
      </el-dialog>


    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import {Search, Upload, Plus, Bottom,InfoFilled} from '@element-plus/icons-vue'
import {ElMessage} from "element-plus";
import zyrypzEdit from "./zyrypzEdit";
import vsAuth from "../../../lib/vsAuth";

export default defineComponent({
  name: '',
  components: {Bottom, Search, Upload, Plus,InfoFilled,zyrypzEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        userId: vsAuth.getAuthInfo().permission.userId,
        page: 1,
        size: 10,
      },
      total: 0,
      tableData: [],
      treeData:[],

      checkRow: null,

      dialogVisible: false,
      params: null,
    })

    const getDataList = () => {
      state.checkRow=null
      state.treeData.splice(0,state.treeData.length)
      axiosUtil.get('/backend/common/zyrypz/selectOrgTPage', state.listQuery).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const getTreeData = () => {
      let params={
        orgId: state.checkRow.ORGNA_TWO_ID
      }
      axiosUtil.get('/backend/common/zyrypz/selectCbsZyList',params).then(res=>{
        let resData=res.data
        state.treeData=treeData(resData,'ZYBM','FZYBM','children','0')
      })
    }


    const instance = getCurrentInstance()
    const addBatch = () => {
      if(!state.checkRow){
        ElMessage.error('请先选择组织机构')
        return
      }
      let checkNode=instance.proxy.$refs['myTree'].getCheckedNodes(true)
      if(checkNode.length===0){
        ElMessage.error('请先选择专业')
        return
      }
      state.params={
        onlyAdd: true,
        checkOrg: state.checkRow,
        ZYList: checkNode
      }
      state.dialogVisible=true
    }

    const editRow = (row) => {
      if(!state.checkRow){
        ElMessage.error('请先选择组织机构')
        return
      }
      state.params={
        onlyAdd: false,
        checkOrg: state.checkRow,
        ZYList: [row]
      }
      state.dialogVisible=true
    }


    const treeData = (data, id, parentId, childName, root) => {
      let cloneData = data
      return cloneData.filter((father) => {
        father[childName] = cloneData.filter((child) => {
          return father[id] === child[parentId]
        })
        return father[parentId] === root
      })
    }

    const checkTableRow = (value) => {
      state.checkRow=value
      getTreeData()
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      state.dialogVisible = false
    }



    onMounted(() => {
      getDataList()
      // getTreeData()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      closeForm,
      checkTableRow,
      addBatch,
      editRow,
      getTreeData
    }
  }

})
</script>

<style scoped>
.card{
  box-shadow: rgba(0, 0, 0, 0.12) 0 0 12px;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
}

.card-header{
  padding: 20px;
  border-bottom: 1px solid #eaeaf1;
}
.card-body{
  flex: 1;
  padding: 10px;
  overflow: auto;
}
</style>
