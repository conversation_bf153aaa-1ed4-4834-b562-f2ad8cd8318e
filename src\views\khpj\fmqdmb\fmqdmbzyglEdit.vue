<template>
  <div>
    <el-form :model="formData" ref="vForm" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 120px;text-align: center;padding-bottom: 2px">
        模板信息
      </div>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="模板名称：" prop="MBMC">
            <div style="margin-left: 10px">{{formData.MBMC}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="模板描述：" prop="MBMS">
            <el-input v-model="formData.MBMS" :rows="3"
                      type="textarea" clearable
                      :disabled="true"/>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 120px;text-align: center;padding-bottom: 2px">
        评价专业信息
      </div>

      <div>
        <div style="text-align: right;padding-bottom: 10px">
          <el-button ref="button91277" @click="addRow" type="primary" v-if="editable">添加</el-button>
        </div>
        <el-table ref="datatable91634" :data="formData.GLZYList" height="calc(300px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>

          <el-table-column prop="ZYMC" label="评价专业名称" align="center"
                           :show-overflow-tooltip="true" min-width="180">
          </el-table-column>
          <el-table-column prop="ZYBM" label="评价专业编码" align="center"
                           :show-overflow-tooltip="true" width="180">
          </el-table-column>
          <el-table-column prop="BZ" label="备注" align="center"
                           :show-overflow-tooltip="true" min-width="180">
          </el-table-column>
          <el-table-column prop="CZ" label="操作" align="center" width="120" v-if="editable">
            <template #default="scope">
              <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>


      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()" v-if="editable">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogPJZYVisible"
        v-model="dialogPJZYVisible"
        title="选择评价专业"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <pjzyChoose v-if="dialogPJZYVisible" @close="dialogPJZYVisible=false" @submit="getKhzyRes"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import pjzyChoose from "@views/khpj/fmqdmb/pjzyChoose";
import {ElMessage, ElMessageBox} from "element-plus";


export default defineComponent({
  name: '',
  components: {pjzyChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      MBID: props.params.id,
      formData: {
        GLZYList: [],
      },
      dialogPJZYVisible: false
    })

    const getFormData = () => {
      let params={
        MBID: state.MBID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/fmqdmbgl/selectFmqdmbById',params).then(res=>{
        state.formData=res.data
        state.loading=false
      })
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const submitForm = () => {
      let params={
        ...state.formData
      }
      state.loading=true
      axiosUtil.post('/backend/sckhpj/fmqdmbgl/saveFmqdzyglgx',params).then(res=>{
        ElMessage.success('保存成功')
        closeForm()
        state.loading=false
      })

    }


    const validateForm = () => {
      return new Promise(resolve => {
        if(state.formData.GLZYList.length===0){
          ElMessage.error('请添加评价专业信息')
          resolve(false)
        }else {
          resolve(true)
        }
      })
    }

    const addRow = () => {
      state.dialogPJZYVisible=true
    }

    const getKhzyRes = (value) => {
      value.forEach(item=>{
        if(!state.formData.GLZYList.find(ii=>ii.PJZYID===item.PJZYID)){
          state.formData.GLZYList.push({
            MBID: state.MBID,
            ...item,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1'
          })
        }
      })
      state.dialogPJZYVisible=false
    }

    const deleteRow = (index) => {
      state.formData.GLZYList.splice(index,1)
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      addRow,
      deleteRow,
      getKhzyRes,
      closeForm,
      saveData

    }
  }

})
</script>

<style scoped>

</style>
