.required .el-form-item__label::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.is-required .el-form-item__label::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
.el-dialog__header{
  border-bottom: 1px solid #EAEEF5;
}
.el-dialog__body {
  padding: 10px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
.zhyy-list-container {
  background-color: #f2f2f2;
}
.zhyy-list-main {
  padding: 8px;
}
.zhyy-list-searchArea {
  padding: 16px;
  background-color: #ffffff;
  margin-bottom: 8px;
  border: 1px solid #EBEEF5;
  border-radius: 5px;
}
.zhyy-list-tableArea {
  background-color: #ffffff;
}
.el-table-fixed-column--right{
  background-color: rgba(255, 255, 255, 1) !important;
}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

[data-change]{
  .el-input__inner{
    color: #f00;
  }
}