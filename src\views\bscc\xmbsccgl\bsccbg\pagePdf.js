// 页面导出为pdf格式 //title表示为下载的标题，html表示document.querySelector('#myPrintHtml')
import html2Canvas from 'html2canvas';
import JsPDF from 'jspdf';

const noTableHeight = 0; //table外的元素高度
function htmlPagePdf(title, htmlList, type, returnPromise, banner, watermark) {// type传有效值pdf则为横版
    return new Promise((resolve, reject) => {
        let defineHeight = 277
        if (banner) {
            defineHeight -= 12
        }
        if (watermark) {
            for (let i = 0; i < htmlList.length; i++) {
                addWatermark(htmlList[i], watermark)
            }
        }

        if (banner) {
            html2Canvas(banner, {
                allowTaint: true,
                taintTest: false,
                logging: false,
                useCORS: true,
                dpi: window.devicePixelRatio * 1,
                scale: 4 // 按比例增加分辨率
            }).then(bannerCanvas => {
                resolve(makeResPdf(htmlList, defineHeight, type, title, returnPromise, bannerCanvas))
            })

        } else {
            resolve(makeResPdf(htmlList, defineHeight, type, title, returnPromise, null))
        }

    })

}


//添加标头
function addBanner(pdf, banner, a4w) {
    pdf.addImage(banner.toDataURL('image/jpeg', 1.0), 'JPEG', 10, 10, a4w, Math.min(12, a4w * banner.height / banner.width)); // 添加Banner 默认
}

//添加页脚
function addPageNum(canvas, text) {
    let ctx = canvas.getContext('2d')
    ctx.font = '25px Arial';
    ctx.fillStyle = '#000000'; // 设置填充颜色
    ctx.fillText(text, canvas.width / 2, canvas.height - 10)
}

function addWatermark(watermarkDiv, watermarkText) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    let binding = {}
    canvas.width = binding.value?.width || 300;
    canvas.height = binding.value?.height || 200;
    ctx.rotate(-10 * Math.PI / 100) // 水印旋转角度
    ctx.font = binding.value?.font || '15px Vedana'  //水印文字大小
    ctx.fillStyle = 'rgba(136,136,136,0.51)' //水印颜色 HEX格式,可使用red 或者rgb格式
    ctx.textAlign = 'center' //水印水平位置
    ctx.textBaseline = 'Middle' //水印垂直位置
    ctx.fillText(watermarkText, canvas.width / 3, canvas.height / 2)

    watermarkDiv.style.background = 'url(' + canvas.toDataURL('image/png') + ') left top repeat' //水印显示(关键代码)


}

function makePagePdf(html, defineHeight, type,pageNum,banner) {
    return new Promise(resolve => {
        html2Canvas(html, {
            allowTaint: false,
            taintTest: false,
            logging: false,
            useCORS: false,
            dpi: window.devicePixelRatio * 1,
            scale: 2 // 按比例增加分辨率
        }).then(canvas => {
            const ctx = canvas.getContext('2d');
            const a4w = type ? defineHeight : 190;
            const a4h = type ? 190 : defineHeight; // A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
            const imgHeight = Math.floor(a4h * canvas.width / a4w); // 按A4显示比例换算一页图像的像素高度
            const page = document.createElement('canvas');
            page.width = canvas.width;
            page.height = imgHeight + 200;// 可能内容不足一页

            // 用getImageData剪裁指定区域，并画到前面创建的canvas对象中
            page.getContext('2d').fillStyle = '#FFFFFF';
            page.getContext('2d').fillRect(0, 0, page.width, page.height)
            page.getContext('2d').putImageData(ctx.getImageData(0, 0, canvas.width, canvas.height), 0, 0);
            addPageNum(page, pageNum)
            resolve(page)
        });
    })
}


function makeResPdf(htmlList, defineHeight, type, title, returnPromise, banner) {
    return new Promise(async resolve => {
        const a4w = type ? defineHeight : 190;
        const a4h = type ? 190 : defineHeight;
        const pdf = new JsPDF('p', 'mm', 'a4'); // A4纸，纵向
        for (let i = 0; i < htmlList.length; i++) {
            let imagePage = await makePagePdf(htmlList[i], defineHeight, type, i + 1, banner)
            if(banner){
                addBanner(pdf,banner,a4w)
            }
            pdf.addImage(imagePage.toDataURL('image/jpeg', 1.0), 'JPEG', 10, banner ? 22 : 10, a4w, Math.min(a4h, a4w * imagePage.height / imagePage.width))
            if(i<htmlList.length-1){
                pdf.addPage()
            }
        }
        if(returnPromise){
            resolve(pdf);
        }else {
            pdf.save(title + '.pdf')
            resolve(true)
        }
    })


}


export default htmlPagePdf;