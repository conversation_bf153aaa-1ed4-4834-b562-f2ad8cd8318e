import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"


// 测试-q01
// @method getTestQ01
// @type get
// @return url
//getTestQ01: `/test/q01`,

// eslint-disable-next-line
export function getTestQ01(params) {
    return axiosUtil.get(`${baseUrl}/test/q01`, params)
}
// 测试-附件表
// @method getTestFjb
// @type get
// @return url
//getTestFjb: `/test/fjb`,

// eslint-disable-next-line
export function getTestFjb(params) {
    return axiosUtil.get(`${baseUrl}/test/fjb`, params)
}




