<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{ formData.XMXX.XMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMXX.XMBH }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="招标单位：" prop="ZGDWID">
            <div style="margin-left: 10px">{{ formData.XMXX.SSDWMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="通知内容：" prop="TZLX">
            <el-select v-model="formData.TZLX" class="full-width-input" clearable disabled>
              <el-option v-for="(item, index) in GGLXOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="公示时间：" prop="GSSJ">
            <div style="display: flex;gap: 10px;align-items: center">
              <el-date-picker
                  v-model="formData.GSSJKS"
                  :disabled="!editable"
                  type="datetime"
                  clearable
                  placeholder="请选择"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
              <div>至</div>
              <el-date-picker
                  v-model="formData.GSSJJS"
                  :disabled="!editable"
                  type="datetime"
                  clearable
                  placeholder="请选择"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系人：" prop="LXR">
            <el-input v-model="formData.LXR" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系电话：" prop="LXDH">
            <el-input v-model="formData.LXDH" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="邮箱：" prop="YX">
            <el-input v-model="formData.YX" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="地址：" prop="DZ">
            <el-input v-model="formData.DZ" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="邮编：" prop="YB">
            <el-input v-model="formData.YB" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <div v-if="formData.XMXX.SFFBD==='1'" style="width: 100%">
          <el-tabs
              v-model="ActiveTab"
              type="border-card">
            <el-tab-pane
                v-for="(item,index) in formData.XSBDList"
                :key="index"
                :label="'标段：'+(item.BDMC || '')"
                :name="index+''">
              <el-row :gutter="0">
                <el-col :span="16" class="grid-cell">
                  <el-form-item label="标段名称：" prop="BDMC">
                    <div style="margin-left: 10px">{{ item.BDMC }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell border-right">
                  <el-form-item label="最高限价（元/%）：" prop="YJJE">
                    <div style="margin-left: 10px">{{ item.YJJE }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell border-right">
                  <el-form-item label="标段内容说明：" prop="BDNRSM">
                    <div style="margin-left: 10px">{{ item.BDNRSM }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell border-right no-border-bottom">
                  <el-form-item label="评标情况：" prop="PBQK">
                    <el-table ref="datatable91634" :data="item.JGMXList" height="200px"
                              :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                      <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                      <el-table-column prop="TJZB" label="推荐中标候选人" align="center"
                                       :show-overflow-tooltip="true" width="120">
                        <template #default="{row,$index}">
                          <div v-if="row.TJZB==='1'">是</div>
                          <div v-if="row.TJZB==='0'">否</div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="TJZBSX" label="推荐中标候选人顺序" align="center"
                                       :show-overflow-tooltip="true" width="120">
                      </el-table-column>
                      <el-table-column prop="DWMC" label="投标人名称" align="center"
                                       :show-overflow-tooltip="true" min-width="160"></el-table-column>
                      <el-table-column prop="BJ" label="投标报价（元/%）" align="center"
                                       :show-overflow-tooltip="true" width="120">
                      </el-table-column>
                      <el-table-column prop="XMJL" label="项目负责人" align="center"
                                       :show-overflow-tooltip="true" width="120">
                      </el-table-column>
                      <el-table-column prop="ZBJMS" label="备注" align="center"
                                       :show-overflow-tooltip="true" width="120">
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </el-col>

<!--                <el-col :span="16" class="grid-cell border-right">-->
<!--                  <el-form-item label="中标单位：" prop="ZBDW">-->
<!--                    <div style="margin-left: 10px">{{ getZbdw(item.JGMXList) }}</div>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->

<!--                <el-col :span="8" class="grid-cell border-right">-->
<!--                  <el-form-item label="中标金额（万元）：" prop="ZBJE">-->
<!--                    <div style="margin-left: 10px">{{ getZbje(item.JGMXList,item) }}</div>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </div>

        <el-row :gutter="0" v-else style="width: 100%">
          <el-col :span="16" class="grid-cell">
            <el-form-item label="标段名称：" prop="BDMC">
              <div style="margin-left: 10px">{{ formData.XSBDList[0]?.BDMC }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="grid-cell border-right">
            <el-form-item label="最高限价（元/%）：" prop="YJJE">
              <div style="margin-left: 10px">{{ formData.XSBDList[0]?.YJJE }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="24" class="grid-cell border-right">
            <el-form-item label="标段内容说明：" prop="BDNRSM">
              <div style="margin-left: 10px">{{ formData.XSBDList[0]?.BDNRSM }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="24" class="grid-cell border-right no-border-bottom">
            <el-form-item label="评标情况：" prop="PBQK">
              <el-table ref="datatable91634" :data="formData.XSBDList[0]?.JGMXList" height="200px"
                        :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                        :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                <el-table-column prop="TJZB" label="推荐中标候选人" align="center"
                                 :show-overflow-tooltip="true" width="120">
                  <template #default="{row,$index}">
                    <div v-if="row.TJZB==='1'">是</div>
                    <div v-if="row.TJZB==='0'">否</div>
                  </template>
                </el-table-column>
                <el-table-column prop="TJZBSX" label="推荐中标候选人顺序" align="center"
                                 :show-overflow-tooltip="true" width="120">
                </el-table-column>
                <el-table-column prop="DWMC" label="投标人名称" align="center"
                                 :show-overflow-tooltip="true" min-width="160"></el-table-column>
                <el-table-column prop="BJ" label="投标报价（元/%）" align="center"
                                 :show-overflow-tooltip="true" width="120">
                </el-table-column>
                <el-table-column prop="XMJL" label="项目负责人" align="center"
                                 :show-overflow-tooltip="true" width="120">
                </el-table-column>
                <el-table-column prop="ZBJMS" label="备注" align="center"
                                 :show-overflow-tooltip="true" width="120">
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>

<!--          <el-col :span="16" class="grid-cell border-right">-->
<!--            <el-form-item label="中标单位:" prop="ZBDW">-->
<!--              <div style="margin-left: 10px">{{ getZbdw(formData.XSBDList[0]?.JGMXList) }}</div>-->
<!--            </el-form-item>-->
<!--          </el-col>-->

<!--          <el-col :span="8" class="grid-cell border-right">-->
<!--            <el-form-item label="中标金额（万元）:" prop="ZBJE">-->
<!--              <div style="margin-left: 10px">{{ getZbje(formData.XSBDList[0]?.JGMXList,formData.XSBDList[0]) }}</div>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>

        <el-col :span="24" class="grid-cell no-border-bottom" v-if="!editable">
          <el-form-item label="公示内容：" prop="GGNR">
            <el-button style="margin-top: 4px;margin-left: 10px" type="primary" @click="dialogZBGSVisible=true">公示预览</el-button>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="公示文件：" prop="GSWJ">
            <vsfileupload
                style="margin-left: 10px"
                :editable="editable"
                :busId="params.id"
                :key="params.id"
                ywlb="GSWJ"
                busType="GSWJ"
                :limit="100"
            ></vsfileupload>
          </el-form-item>
        </el-col>


      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogZBGSVisible"
        v-model="dialogZBGSVisible"
        top="5vh"
        title="公示预览"
        z-index="1000"
        width="1000px">
      <div>
        <zbjggsView :params="params" @close="dialogZBGSVisible=false"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import zbjggsView from "@views/zbxsgl/zbjggs/zbjggsView";

export default defineComponent({
  name: '',
  components: {vsfileupload,zbjggsView},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      JGGSID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',

        XMXX: {},

        TZLX: 'JGGS',

        XSBDList: [],

      },
      rules: {
        LXR: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXDH: [{
          required: true,
          message: '字段值不可为空',
        }],
        YX: [{
          required: true,
          message: '字段值不可为空',
        }],
        GGLX: [{
          required: true,
          message: '字段值不可为空',
        }],
        GSSJ: [{
          required: true,
          validator: (rule, value, callback) => {
            if (!state.formData.GSSJKS) {
              callback(new Error('开始时间不能为空'))
            } else if (!state.formData.GSSJJS) {
              callback(new Error('结束时间不能为空'))
            } else {
              callback()
            }
          }
        }],
      },
      GGLXOptions: [],

      ActiveTab: '0',

      dialogZBGSVisible: false
    })

    const getFormData = () => {
      let params={
        JGGSID: state.JGGSID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/zbjggs/selectZbjgById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        JGGSID: state.JGGSID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      if(type==='submit'){
        params.SHZT='2'
      }
      state.loading=true
      axiosUtil.post('/backend/xsgl/zbjggs/saveZbgsForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getXmxx = () => {
      let params = {
        FAID: state.formData.FAID,
        XSJGID: state.formData.XSJGID,
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/zbjggs/selectXmxxByFaid', params).then(res => {
        state.formData.XMXX = res.data
        state.formData.XSBDList = res.data.XSBDList
        state.loading=false
      })
    }

    const getZbdw = (JGList) => {
      let res=[]
      if(JGList){
        JGList.forEach(item=>{
          if(item.TJZB==='1'){
            res.push(item.DWMC)
          }
        })
      }
      return res.join(',')
    }

    const getZbje = (JGList,row) => {
      let res=0
      if(JGList && row.BJFS){
        JGList.forEach(item=>{
          if(item.TJZB==='1'){
            let BJ=Number(item.BJ)
            if(!isNaN(BJ) && row.BJFS==='JG'){
              res+=BJ
            }else if(!isNaN(BJ) && row.BJFS==='ZHJD' && row.BD){
              res+= Number(row.BD) * (100-BJ) /100
            }
          }
        })
      }
      return res
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      } else {
        state.formData.FAID = props.params.FAID
        state.formData.XSJGID = props.params.XSJGID
        getXmxx()
      }
      getDMBData("GGLX", "GGLXOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      getZbdw,
      getZbje
    }
  }

})
</script>

<style scoped>
:deep(.lui-card-form .border-right .el-form-item__content){
  border-right: 1px solid rgba(227, 234, 248, 1) !important;
}
</style>
