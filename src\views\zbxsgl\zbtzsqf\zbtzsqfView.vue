<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="120px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="招标单位：" prop="ZGDWID">
            <div style="margin-left: 10px">{{ formData.XMXX.SSDWMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{ formData.XMXX.XMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMXX.XMBH }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="评标时间：" prop="KBSJ">
            <div style="margin-left: 10px">{{ formData.XMXX.PBRQ }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="评标地点：" prop="PBDD">
            <div style="margin-left: 10px">{{ formData.XMXX.PBDD }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="中标通知书：" prop="ZBQK">
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-table ref="datatable91634" :data="formData.TZMXList" height="300px"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="BDMC" label="标段名称" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="BDNRSM" label="标段内容说明" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="TJZBSX" label="排序" align="center"
                             :show-overflow-tooltip="true" width="120"></el-table-column>
            <el-table-column prop="DWMC" label="中标单位名称" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="SFZB" label="推荐中标" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <div v-if="row.SFZB==='1'">是</div>
                <div v-if="row.SFZB==='0'">否</div>
              </template>
            </el-table-column>
            <el-table-column prop="BJ" label="中标价" align="center"
                             :show-overflow-tooltip="true" width="100"></el-table-column>
            <el-table-column prop="XMJL" label="项目负责人" align="center"
                             :show-overflow-tooltip="true" width="120"></el-table-column>
            <el-table-column prop="FWQX" label="服务期限" align="center"
                             :show-overflow-tooltip="true" width="120">
            </el-table-column>
            <el-table-column prop="YQHTQDSJ" label="要求合同签订时间" align="center"
                             :show-overflow-tooltip="true" width="120">
            </el-table-column>
            <el-table-column prop="YQHTQDDD" label="要求合同签订地点" align="center"
                             :show-overflow-tooltip="true" width="120">
            </el-table-column>

            <el-table-column prop="BZ" label="备注" align="center"
                             :show-overflow-tooltip="true" width="120">
            </el-table-column>
            <el-table-column prop="CZ" label="操作" align="center" fixed="right"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <el-button size="small" class="lui-table-button" type="primary"
                           @click="viewTzs(row)">查看通知书
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>


      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="closeMsg" v-if="params.operation==='msg'">已阅</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogZBTZVisible"
        v-model="dialogZBTZVisible"
        title="中标通知书预览"
        z-index="1000"
        width="800px">
      <div>
        <zbtzsView :params="ZTParams"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import zbtzsView from "@views/zbxsgl/zbtzsqf/zbtzsView";


export default defineComponent({
  name: '',
  components: {zbtzsView},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      ZBTZID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        SFFBGG: '1',

        XMXX: {},

        TZMXList: [],
      },
      rules: {
        FWQX: [{
          required: true,
          message: '请输入',
        }],
        YQHTQDSJ: [{
          required: true,
          message: '请输入',
        }],
        YQHTQDDD: [{
          required: true,
          message: '请输入',
        }],
      },


      dialogZBTZVisible: false,
      ZTParams:{},

      dialogYLGGVisible: false,
    })

    const getFormData = () => {
      let params={
        ZBTZID: state.ZBTZID,
        CBSCK: '1',
        RYZH: state.userInfo.userLoginName
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/zbtzqf/selectZbtzqfById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }




    const viewTzs = (row) => {
      axiosUtil.get('/backend/common/queryOrgByOrgId', {ORGNA_ID: state.formData.CJDWID}).then(res => {
        state.ZTParams={
          ...row,
          ...state.formData.XMXX,
          SSDW_TWO_NAME: res.data[0].ORGNA_TWO_NAME,
          BDZBR: state.formData.TZMXList.filter(item=>item.SFZB==='1' && item.FABDID===row.FABDID).map(item=>item.DWMC).join('、')
        }
        state.dialogZBTZVisible=true
      })
    }


    const closeForm = () => {
      emit('close')
    }

    const closeMsg = () => {
      emit('closeMsg')
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      closeForm,
      viewTzs,
      closeMsg
    }
  }

})
</script>

<style scoped>
@import '@vueup/vue-quill/dist/vue-quill.snow.css';
</style>
