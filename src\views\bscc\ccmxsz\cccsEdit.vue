<template>
  <div class="context">
    <div class="worming-massage">
      <el-icon :size="25" color="#409EFF">
        <InfoFilled/>
      </el-icon>
      <div>{{ params.DMMC + '设置' }}：{{ params.BYZD1 }}</div>
    </div>

    <div class="form-row" v-if="params.DMXX==='WBCC'">
      <div class="form-label">
        <div class="label">最小重复文本长度阈值</div>
        <el-icon :size="20" color="#409EFF">
          <InfoFilled/>
        </el-icon>
        <div class="label-worming">当两个重复内容的长度超过这里设置的阈值时，在检测报告中显示这两个内容。相反则不显示。</div>
      </div>

      <el-form-item label="" prop="ZXWBCD" size="large" style="margin-top: 10px">
        <el-input v-model="modelValue.ZXWBCD" type="text" style="width: 300px" placeholder="请输入最小重复文本长度阈值" clearable
                  @input="modelValue.ZXWBCD=modelValue.ZXWBCD.replace(/[^\d]/g, '')">
        </el-input>
      </el-form-item>
    </div>

    <div class="form-row" v-if="params.DMXX==='WBCC'">
      <div class="form-label">
        <div class="label">重复文本前后引用长度</div>
        <el-icon :size="20" color="#409EFF">
          <InfoFilled/>
        </el-icon>
        <div class="label-worming">在查重报告显示重复内容时，前后引用的内容长度，方便用户查找内容所在位置。</div>
      </div>

      <el-form-item label="" prop="QHZFCD" size="large" style="margin-top: 10px">
        <el-input v-model="modelValue.QHZFCD" type="text" style="width: 300px" placeholder="请输入重复文本前后引用长度" clearable
                  @input="modelValue.QHZFCD=modelValue.QHZFCD.replace(/[^\d]/g, '')">
        </el-input>
      </el-form-item>
    </div>


<!--    <div class="form-row" v-if="params.DMXX==='TPCC'">-->
<!--      <div class="form-label">-->
<!--        <div class="label">相似度阈值</div>-->
<!--        <el-icon :size="20" color="#409EFF">-->
<!--          <InfoFilled/>-->
<!--        </el-icon>-->
<!--        <div class="label-worming">当两个图片的相似度超过这里设置的阈值时，在检测报告中显示这两个图片重复。相反则不显示。</div>-->
<!--      </div>-->

<!--      <el-form-item label="" prop="TPXSDZZ" size="large" style="margin-top: 10px">-->
<!--        <el-input v-model="modelValue.TPXSDZZ" type="text" style="width: 300px" placeholder="请输入相似度阈值" clearable-->
<!--                  @input="modelValue.TPXSDZZ=modelValue.TPXSDZZ.replace(/[^\d]/g, '')">-->
<!--        </el-input>-->
<!--        <span style="margin-left: 10px">%</span>-->
<!--      </el-form-item>-->
<!--    </div>-->








    <el-table ref="datatable91634" :data="modelValue[params.DMXX+'List']" height="400px"
              v-show="modelValue[params.DMXX+'List'] && modelValue[params.DMXX+'List'].length>0"
              :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
      <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
      <el-table-column prop="CCNR" label="查重内容" align="center"
                       :show-overflow-tooltip="true" width="200"></el-table-column>
      <el-table-column prop="SFQY" label="是否启用" align="center"
                       :show-overflow-tooltip="true" width="160">
        <template #default="{row}">
          <el-switch size="default" v-model="row.SFQY"
                     active-text="启用" inactive-text="禁用"
                     active-value="1" inactive-value="0"/>
        </template>
      </el-table-column>
      <el-table-column prop="ZFCD" label="字符长度" align="center"
                       :show-overflow-tooltip="true" width="150" v-if="params.DMXX==='CWYZXCC'">
        <template #default="{row}">
          <el-input v-model="row.ZFCD" placeholder="请输入" size="default"
                    @input="row.ZFCD=row.ZFCD.replace(/[^\d]/g, '')"/>
        </template>
      </el-table-column>
      <el-table-column prop="CCNRSM" label="查重内容说明" align="left" header-align="center"
                       :show-overflow-tooltip="true" min-width="260"></el-table-column>

    </el-table>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {InfoFilled} from "@element-plus/icons-vue";


export default defineComponent({
  name: '',
  components: {InfoFilled},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({})
    onMounted(() => {

    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>
.context {
  height: calc(100vh - 240px);
  overflow: auto;
}

.worming-massage {
  padding: 20px;
  background-color: rgba(42, 187, 249, 0.2);
  display: flex;
  gap: 20px;
  font-size: 18px;
  align-items: center;
  margin-bottom: 30px;
}

.el-switch {
  --el-color-primary: #409EFF;
}

.form-row{
  margin: 50px 0 20px 40px;
}

.form-label{
  display: flex;
  align-items: center;
}
.form-label .label{
  font-size: 20px;
  width: 250px;
}

.form-label .label-worming{
  margin-left: 20px;
}
</style>
