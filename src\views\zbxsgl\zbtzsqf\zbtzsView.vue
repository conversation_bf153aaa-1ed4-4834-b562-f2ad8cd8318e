<template>
  <div style="font-family: 宋体;font-size: 18px">
    <div style="text-align: right;padding-right: 10px;padding-top: 10px">
      <el-button size="default" type="primary" @click="download">下载</el-button>
    </div>
    <div id="pdf-context">
      <div style="padding: 40px 100px;" v-if="params.SFZB==='1'">
        <h2 style="width: 100%;text-align: center">{{GJC[params.XSFS][0]}}通知书</h2>
        <p>{{params.DWMC}}：</p>
        <p style="text-indent: 4ch;">你方递交的{{params.XMMC}}投标文件已被{{GJC[params.XSFS][1]}}人接受，被确定为{{GJC[params.XSFS][0]}}人。{{GJC[params.XSFS][0]}}价为：
          <span v-if="params.BJFS=='ZHJD'">综合降点率{{params.BJ}}%</span>
          <span v-else>人民币{{comFun.uppercase(params.BJ*10000)}}整（小写￥{{params.BJ * 10000}}元）</span>
          。服务期限：{{params.FWQX}}。请于{{params.YQHTQDSJ}}前到{{params.YQHTQDDD}}与{{GJC[params.XSFS][1]}}人签订合同，在此之前按{{GJC[params.XSFS][1]}}文件规定的数额和形式向{{GJC[params.XSFS][1]}}人提交履约保证金。逾期我方则认为你方自动放弃签约，我方将按有关规定处理。
        </p>
        <p style="display:flex;justify-content: flex-end">{{GJC[params.XSFS][1]}}人：{{params.SSDW_TWO_NAME}}</p>
        <p style="display:flex;justify-content: flex-end">{{dataToCh(params.CJSJ)}}</p>
      </div>
      <div style="padding: 40px;font-family: 宋体" v-else>
        <h2 style="width: 100%;text-align: center">{{GJC[params.XSFS][0]}}结果通知书</h2>
        <p>{{params.DWMC}}：</p>
        <p style="text-indent: 4ch;">我方已接受{{params.BDZBR}}所递交的{{params.XMMC}}投标文件，确定{{params.BDZBR}}为{{GJC[params.XSFS][0]}}人。
        </p>
        <p style="display:flex;justify-content: flex-end">{{GJC[params.XSFS][1]}}人：{{params.SSDW_TWO_NAME}}</p>
        <p style="display:flex;justify-content: flex-end">{{dataToCh(params.CJSJ)}}</p>
      </div>
    </div>
  </div>


</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import comFun from "@lib/comFun";
import htmlPdf from "@lib/pdf";

export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      GJC: {
        JB: ['成交','采购'],
        JJ: ['成交','采购'],
        GKJB: ['成交','采购'],
        GKJJ: ['成交','采购'],
        GKZB: ['中标','招标'],
        YQZB: ['中标','招标'],
      },
    })

    const dataToCh = (data) => {
      if(!data){
        return ''
      }
      let _data=data.substring(0,10)
      let dataList=_data.split('-')
      return dataList[0]+'年'+dataList[1]+'月'+dataList[2]+'日'
    }

    const download = () => {
      const labelList = document.getElementsByClassName('pdf-details');
      htmlPdf(`${state.GJC[props.params.XSFS][0]}结果通知`, document.getElementById('pdf-context'), labelList, null, false, null, '西北油田市场管理信息系统')
    }


    onMounted(() => {

    })

    return {
      ...toRefs(state),
      comFun,
      dataToCh,
      download

    }
  }

})
</script>

<style scoped>

</style>
