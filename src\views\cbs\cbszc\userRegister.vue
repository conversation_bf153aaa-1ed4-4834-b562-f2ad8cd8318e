<template>
<div class="lui-card-form">
  <el-form
      ref="userFormRef"
      size="default"
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="100px"
      :disabled="props.isCheck"
  >
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="统一账号" prop="USER_LDAP">
          <el-input v-model="form.USER_LDAP" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="姓名" prop="USER_NAME">
          <el-input v-model="form.USER_NAME" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="AD账号" prop="USER_AD">
          <el-input v-model="form.USER_AD" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="手机号" prop="MOBILE">
          <el-input v-model="form.MOBILE" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
      <el-form-item label="所属单位" prop="ORGNA_ID">
        <el-tree-select
            v-model="form.ORGNA_ID"
            :data="unitOptions"
            :render-after-expand="false"
            :props="pop"
            :check-strictly="true"
            :default-expand-all="true"
        />
      </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-form-item label="注册岗位" prop="USER_ROLE">
        <el-select v-model="form.USER_ROLE" clearable placeholder="请输入">
          <el-option
              v-for="(item, index) in gwOptions"
              :key="index"
              :label="item.DMMC"
              :value="item.DMXX"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-row>
    <el-row v-if="!props.isCheck">
      <el-col :span="12">
        <el-form-item label="验证码" prop="yzm">
          <el-input v-model="form.yzm" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
          <span style="margin-left: 10px"
          ><el-button type="primary">发送</el-button></span
          >
        <span style="margin-left: 10px">提示：5分钟内不能重新发送。</span>
      </el-col>
    </el-row>

  </el-form>
    <el-row class="btn" justify="space-evenly" v-if="!props.isCheck">
      <el-button type="primary" @click="submit">提交</el-button>
      <el-button @click="cancelHandle">返回</el-button>
    </el-row>
    <el-row class="btn" justify="space-evenly" v-else>
      <template v-if="form.SHZT !== '1'">
        <el-button @click="emits('close')">返回</el-button>
      </template>
      <template v-else>
        <el-button v-if="form.SHZT === '1'" type="primary" @click="approved"
        >通过</el-button
        >
        <el-button v-if="form.SHZT === '1'" type="danger" @click="refuse">退回</el-button>
      </template>
    </el-row>

    <el-dialog
        v-model="refuseDialogVisible"
        title="退回办理"
        width="800px"
        destroy-on-close
    >
      <cbsshRefuseComponent
          :defaultData="defaultData"
          @closeRefuseDialog="closeDialog"
          @finish="emits('finish')"
      ></cbsshRefuseComponent>
    </el-dialog>
</div>
</template>

<script setup>
import { reactive, getCurrentInstance, ref, defineEmits, onMounted, watch } from "vue";
import { vue, auth, runtimeCfg, mixin } from "@src/assets/core/index";
import { ElMessage } from "element-plus";
import cascaderTree from "@src/lib/cascaderTree.js";
import {
  postZcglUserRegister, //用户注册
  getZcglCheckLxrsj, //联系人校验
  getCbsyjGetEntOrganizations, //组织结构
  getExamineView, //场承包商管理-审核-查看
  postExamineApproved, //市场承包商管理-审核-通过审核
} from "@src/api/sccbsgl.js";
// 审核退回弹窗
import cbsshRefuseComponent from "@src/views/cbs/cbsyj/cbsshRefuse.vue";
import { getCommonSelectDMB } from "@src/api/common.js";
import { v4 as uuidv4 } from "uuid";
const { vsuiRouter } = mixin();
const props = defineProps({
  isCheck: {
    type: Boolean,
    default: false,
  },
  defaultData: {
    type: Object,
    default: () => ({
      TYPE: null,
      DATAID: null,
    }),
  },
});

const form = reactive({
  YHZCID: uuidv4().replace(/-/g, ""),
  DWQC: "",
  TYXYDM: "",
  QYYX: "",
  TJDWID: "",
  GLYZH: "",
  LXRXM: "",
  MOBILE: "",
  surePw: "",
  SHZT: "1", //审核状态：未审核：1；审核通过：2；审核不通过：9
  yzm: "",
  BZ: "",
});

const rules = reactive({
  USER_LDAP: [
    {
      required: true,
      message: "请输入统一账号",
    },
  ],
  USER_NAME: [
    {
      required: true,
      message: "请输入姓名",
    },
  ],
  USER_AD: [
    {
      required: true,
      message: "请输入AD账号",
    },
  ],
  MOBILE: [
    {
      required: true,
      message: "请输入手机号",
    },
    {
      trigger: "blur",
      asyncValidator: () => {
        return new Promise((r, j) => {
          const { MOBILE } = form;
          //手机号码格式校验
          validatePhoneNumber(MOBILE)
          .then((data) => {
              if (data) {
                //历史手机号码校验
                getZcglCheckLxrsj({
                    LXRSJ: MOBILE,
                  })
                    .then(({ data }) => {
                      if (data.length) {
                        j("该手机号已注册");
                      } else {
                        r();
                      }
                    })
                    .catch((err) => {
                      j();
                    });
              } else {
                j(data);
              }
            })
            .catch((err) => {
              j(err);
            });
          
        });
      },
    },
  ],
});
const pop = reactive({
  parent: 'PORGID',
  value: 'ORGID',
  label: 'NAME',
  children: 'children',
});
// 获取所属单位
const unitOptions = ref([]);
const getEntOrganizations = () => {
  getCbsyjGetEntOrganizations()
    .then(({ data }) => {
      //if (data) unitOptions.value = data;
      if (data) unitOptions.value = new cascaderTree(data, "ORGID", "PORGID").init();
    })
    .catch((err) => {
      var cacheData = [
        {
          NAME: "中石化工程技术研究院",
          CODE: "1000000000",
          ORGID:"8ebbb5c57aa24e0dad44403ea8c671fd",
          PORGID:"0"
        },
        {
          NAME: "承包商",
          CODE: "SCGL_CBS",
          ORGID:"00450388d2154c2584e9c62b4534a83b",
          PORGID:"8ebbb5c57aa24e0dad44403ea8c671fd"
        },
        {
          NAME: "胜软科技",
          CODE: "1001100",
          ORGID:"4b5336eb67574572b8a2e9c318fa7106",
          PORGID:"00450388d2154c2584e9c62b4534a83b"
        },
      ];
      unitOptions.value = new cascaderTree(cacheData, "ORGID", "PORGID").init();
    });
};
onMounted(() => {
  getEntOrganizations();
});

//查询保存信息
const viewSaveData = () => {
  const { TYPE, DATAID } = props.defaultData;
  if (!TYPE) {
    return;
  }
  getExamineView({
    TYPE,
    DATAID,
  })
    .then(({ data }) => {
      if (data) Object.assign(form, data[0]);
    })
    .catch((err) => {});
};
watch(
  () => props.defaultData,
  () => {
    viewSaveData();
  },
  {
    immediate: true,
  }
);

// 获取岗位
const gwOptions = ref([]);
const getGw = () => {
  getCommonSelectDMB({
    DMLBID: "ZCGW",
  })
    .then(({ data }) => {
      if (data) gwOptions.value = data;
    })
    .catch((err) => {
      ElMessage.error("岗位信息获取失败");
    });
};
onMounted(() => {
  getGw();
});

const userFormRef = ref(null);

const emits = defineEmits(["close", "finish"]);
// 注册保存
const submit = () => {
  userFormRef.value
    .validate()
    .then(() => {
      postZcglUserRegister({ ...form })
        .then((res) => {
          ElMessage.success("保存成功");
          emits("close");
          vsuiRouter.push("/login");
        })
        .catch(() => {
          ElMessage.error("保存失败");
        });
    })
    .catch((err) => {
      console.log(err);
      // ElMessage.warning('校验失败')
    });
};
// 审核通过
const approved = () => {
  const userInfo = auth.getPermission();
  const { DATAID, TYPE } = props.defaultData;
  postExamineApproved({
    TYPE,
    DATAID,
    SHRZH: userInfo.userLoginName,
    SHRXM: userInfo.userName,
  })
    .then((result) => {
      ElMessage.success("审核保存成功！");
      emits("finish");
    })
    .catch((err) => {
      ElMessage.error("审核保存失败！");
    });
};

// 退回
const refuseDialogVisible = ref(false);
const refuse = () => {
  refuseDialogVisible.value = true;
};
const closeDialog = () => {
  refuseDialogVisible.value = false;
};

const cancelHandle = () => {
  emits("close");
  vsuiRouter.push("/login");
};

//手机号码校验
 const validatePhoneNumber = (phoneNumber) => {
  // var reg = /^1[3-9]\d{9}$/;
  // return reg.test(phoneNumber);
  return new Promise((resolve, reject) => {
    var reg = /^1[3-9]\d{9}$/;
    if (reg.test(phoneNumber)) {
      resolve("手机号码格式正确");
    } else {
      reject("手机号码格式错误");
    }
  });
};
</script>
<style scoped>
@import "../style/index.css";
.out-box-content {
  padding: 50px;
}
</style>
