//附件上传API

export default {
     //上传
    uploadUrl(model) {
        let url="/backend/minio/upload"
        return url;
	},

   //下载
   downLoadUrl(model) {
        let url= "/backend/minio/download"   //download  unZipFile
        return url;
	},
    //查询附件列表
    queryListUrl(model) {
        let url= "/backend/minio/list"
        return url;
	},
    
    //删除
    deleteUrl(model) {
        let url= "/backend/minio/del"
        return url;
	},
    //删除
    deleteByBusId(model) {
        let url= "/backend/minio/del/busId"
        return url;
	},
    
    showUrl(model) {
        let url= '/backend/minio/show'
        return url;
	},
}
