<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

//控件中的一些常用方法都在这里调用，比如保存，打印等等
function AfterDocumentOpened() {
	pageofficectrl.word.SetWaterMark("水印文字");
}

function OnPageOfficeCtrlInit() {
	pageofficectrl.CustomToolbar = false;
}

function openFile() {
	return request({
		url: '/JsInsertWaterMark/Word',
		method: 'get',
	})
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit,AfterDocumentOpened};//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
