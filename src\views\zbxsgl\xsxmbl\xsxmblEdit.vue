<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="所属单位：" prop="XMXX.SSDWID" >
            <div style="margin-left: 10px">{{formData.XMXX.SSDWMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目类别：" prop="XMXX.XMLB" >
            <el-cascader v-model="formData.XMXX.XMLB" :options="ZRZYTree" filterable :disabled="!editable"
                         :props="{label:'ZYMC',value:'ZY<PERSON>',emitPath: false}"
                         clearable />
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="是否框架项目：" prop="XMXX.SFKJXM">
            <el-radio-group v-model="formData.XMXX.SFKJXM" :disabled="!editable">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item :label="`项目名称：`" prop="XMXX.XMMC">
            <el-input v-model="formData.XMXX.XMMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item :label="`项目编号：`" prop="XMXX.XMBH" >
            <el-input v-model="formData.XMXX.XMBH" type="text" placeholder="请输入" disabled></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="资金来源：" prop="ZJLY" >
            <el-select v-model="formData.ZJLY" class="full-width-input" clearable :disabled="!editable">
              <el-option v-for="(item, index) in ZJLYOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="选商方式：" prop="XSFS" >
            <el-select v-model="formData.XSFS" class="full-width-input" clearable :disabled="!editable">
              <el-option v-for="(item, index) in XSFSOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="选商组织方式：" prop="ZZFS" >
            <el-select v-model="formData.ZZFS" class="full-width-input" clearable :disabled="!editable">
              <el-option v-for="(item, index) in ZZFSOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="8" class="grid-cell">
          <el-form-item label="选取中标人数量：" prop="XQZBRSL" >
            <el-input v-model="formData.XQZBRSL" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>



        <el-col :span="8" class="grid-cell">
          <el-form-item :label="`开标时间：`" prop="XSWJ.JHKBSJ">
            <el-date-picker
                v-model="formData.XSWJ.JHKBSJ"
                :disabled="!editable"
                type="datetime"
                clearable
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="是否分标段：" prop="SFFBD">
            <el-radio-group v-model="formData.SFFBD" :disabled="!editable" @change="changeSFFBD">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
            <el-button style="margin-top: 4px;margin-left: 20px" type="primary" @click="addBD" v-if="formData.SFFBD==='1' && editable">新增标段</el-button>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell" v-if="formData.BDList.length>0 && formData.SFFBD==='1'">
          <el-tabs
              v-model="ActiveTab"
              type="border-card"
              :closable="editable"
              @tab-remove="delBD"
          >
            <el-tab-pane
                v-for="(item,index) in formData.BDList"
                :key="index"
                :label="'标段：'+(item.BDMC || '')"
                :name="index+''">
              <el-row :gutter="0" class="grid-row" style="padding: 0 20px 20px;">
                <el-col :span="8" class="grid-cell">
                  <el-form-item label="标段名称：" :prop="`BDList.${index}.BDMC`" :rules="rules.BDMC">
                    <el-input v-model="item.BDMC" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell border-right">
                  <el-form-item label="最高限价（元/%）：" :prop="`BDList.${index}.BD`" :rules="rules.BD">
                    <el-input v-model="item.BD" type="text" placeholder="请输入" :disabled="!editable"
                              @input="item.BD=item.BD.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell border-right">
                  <el-form-item label="报价方式：" :prop="`BDList.${index}.BJFS`" :rules="rules.BJFS">
                    <el-select v-model="item.BJFS" class="full-width-input" :disabled="!editable">
                      <el-option v-for="(item, index) in BJFSOptions" :key="index" :label="item.DMMC"
                                 :value="item.DMXX"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell border-right no-border-bottom">
                  <el-form-item label="中标单位：" prop="PBQK">
                    <el-button type="primary" style="margin-bottom: 5px;margin-left: 5px;margin-top: 5px" @click="addPBQK(item)" v-if="editable">选择</el-button>
                    <el-table ref="datatable91634" :data="item.JGMXList" height="200px"
                              :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                      <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                      <el-table-column prop="DWMC" label="中标人名称" align="center"
                                       :show-overflow-tooltip="true" min-width="160">
                      </el-table-column>
                      <el-table-column prop="BJ" label="合同金额（万元）" align="center"
                                       :show-overflow-tooltip="true" width="120">
                        <template #default="{row,$index}">
                          <el-form-item label="" :prop="`BDList.${index}.JGMXList.${$index}.BJ`" label-width="0" :rules="tableRules.BJ">
                            <el-input v-model="row.BJ" type="text" placeholder="请输入" :disabled="!editable"
                                      @input="row.BJ=row.BJ.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>

                      <el-table-column prop="FWQX" label="合同履行期限" align="center"
                                       :show-overflow-tooltip="true" width="120">
                        <template #default="{row,$index}">
                          <el-form-item label="" :prop="`BDList.${index}.JGMXList.${$index}.FWQX`" label-width="0" :rules="tableRules.FWQX">
                            <el-input v-model="row.FWQX" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </el-col>



              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>

        <el-col :span="8" class="grid-cell" v-if="formData.SFFBD==='0' && formData.BDList[0]">
          <el-form-item label="最高限价（元/%）：" :prop="`BDList.0.BD`" :rules="rules.BD">
            <el-input v-model="formData.BDList[0].BD" type="text" placeholder="请输入" :disabled="!editable"
                      @input="formData.BDList[0].BD=formData.BDList[0].BD.replace(/^([0-9-]\d*\.?\d{0,4})?.*$/, '$1')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell border-right" v-if="formData.SFFBD==='0' && formData.BDList[0]">
          <el-form-item label="报价方式：" :prop="`BDList.0.BJFS`" :rules="rules.BJFS">
            <el-select v-model="formData.BDList[0].BJFS" class="full-width-input" :disabled="!editable">
              <el-option v-for="(item, index) in BJFSOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell border-right no-border-bottom" v-if="formData.SFFBD==='0' && formData.BDList[0]">
          <el-form-item label="中标单位：" prop="PBQK">
            <el-button type="primary" style="margin-bottom: 5px;margin-left: 5px;margin-top: 5px" @click="addPBQK(formData.BDList[0])" v-if="editable">选择</el-button>
            <el-table ref="datatable91634" :data="formData.BDList[0]?.JGMXList" height="200px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="DWMC" label="中标人名称" align="center"
                               :show-overflow-tooltip="true" min-width="160">
              </el-table-column>
              <el-table-column prop="BJ" label="合同金额（万元）" align="center"
                               :show-overflow-tooltip="true" width="120">
                <template #default="{row,$index}">
                  <el-form-item label="" :prop="`BDList.0.JGMXList.${$index}.BJ`" label-width="0" :rules="tableRules.BJ">
                    <el-input v-model="row.BJ" type="text" placeholder="请输入" :disabled="!editable"
                              @input="row.BJ=row.BJ.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column prop="FWQX" label="合同履行期限" align="center"
                               :show-overflow-tooltip="true" width="120">
                <template #default="{row,$index}">
                  <el-form-item label="" :prop="`BDList.0.JGMXList.${$index}.FWQX`" label-width="0" :rules="tableRules.FWQX">
                    <el-input v-model="row.FWQX" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>


            </el-table>
          </el-form-item>
        </el-col>

      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogZbdwVisible"
        v-model="dialogZbdwVisible"
        title="中标单位选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <dwChoose @close="dialogZbdwVisible=false" @submit="getZbdwRes" :params="params"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import dwChoose from "@views/zbxsgl/xssqgl/dwChoose";


export default defineComponent({
  name: '',
  components: {dwChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      FAID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',
        SFFBD: '0',

        XMXX: {
          XMID: comFun.newId(),
          SSDWID: vsAuth.getAuthInfo().permission.orgnaId,
          SSDWMC: vsAuth.getAuthInfo().permission.orgnaName,
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
          SHZT: '0',
          YWZT: '0',
          XMLY: 'LS'
        },
        XSWJ: {
          WJID: comFun.newId(),
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
          SHZT: '0',
          YWZT: '1',
          SFWSPS: '0'
        },
        BDList: [{
          FABDID: comFun.newId(),
          FAID: props.params.id,
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
        }],
        XSJGID: comFun.newId(),
        ZBTZID: comFun.newId(),
      },
      rules: {
        XMXX:{
          XMLB: [{
            required: true,
            message: '字段值不可为空',
          }],
          SSDWID: [{
            required: true,
            message: '字段值不可为空',
          }],
          XMMC: [{
            required: true,
            message: '字段值不可为空',
          }],
          SFKJXM:[{
            required: true,
            message: '字段值不可为空',
          }],
        },
        XSFS: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZJLY: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZZFS: [{
          required: true,
          message: '字段值不可为空',
        }],
        SFFBD: [{
          required: true,
          message: '字段值不可为空',
        }],
        XQZBRSL: [{
          required: props.params.XSFS!=='DJTP',
          message: '字段值不可为空',
        }],
        XSWJ:{
          JHKBSJ: [{
            required: true,
            message: '字段值不可为空',
          }],
        },


        BDMC: [{
          required: true,
          message: '字段值不可为空',
        }],

        BJFS: [{
          required: true,
          message: '请填写',
        }],

        BD: [{
          required: true,
          message: '请填写',
        }],

      },

      tableRules:{
        BJ: [{
          required: true,
          message: '待填写',
        }],
        FWQX: [{
          required: true,
          message: '待填写',
        }],
      },

      ActiveTab: '0',

      ZRZYTree: [],
      XSFSOptions: [],
      SSDWOptions: [],
      ZZFSOptions: [],
      ZJLYOptions: [],
      BJFSOptions: [],

      dialogZbdwVisible: false,

      rowParams:{}
    })

    const getFormData = () => {
      let params={
        FAID: state.FAID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/xsxmbl/selectXsxmblxxById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const addBD = () => {
      state.formData.BDList.push({
        FABDID: comFun.newId(),
        FAID: state.FAID,
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
      })
      state.ActiveTab=state.formData.BDList.length-1+''
    }

    const delBD = (index) => {
      state.formData.BDList.splice(Number(index),1)
    }

    const addPBQK = (row) => {
     state.rowParams=row
      state.dialogZbdwVisible=true
    }

    const getZbdwRes = (value) => {
      if(!state.rowParams.JGMXList){
        state.rowParams.JGMXList=[]
      }
      value.forEach(item=>{
        if(!state.rowParams.JGMXList.find(ii=>ii.DWWYBS===item.DWWYBS)){
          state.rowParams.JGMXList.push({
            XSJGMXID: comFun.newId(),
            ZBTZMXID: comFun.newId(),
            XSJGID: state.formData.XSJGID,
            ZBTZID: state.formData.ZBTZID,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '0',
            YWZT: '1',
            FABDID: state.rowParams.FABDID,
            DWMC: item.DWMC,
            DWWYBS: item.DWWYBS,
            SFZB: '1'

          })
        }
      })



      state.dialogZbdwVisible=false
    }

    const changeSFFBD = (value) => {
      if(value==='0'){
        if(state.formData.BDList.length>0){
          state.formData.BDList.splice(1,state.formData.BDList.length-1)
          state.ActiveTab='0'
        }else {
          addBD()
        }
      }
    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        FAID: state.FAID,
        XSWJ: {
          ...state.formData.XSWJ,
          FAID: state.FAID,
          XGRZH: state.userInfo.userLoginName,
          XGSJ: comFun.getNowTime(),
        },
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        XMID: state.formData.XMXX.XMID
      }
      if(type==='submit'){
        params.SHZT='2'
      }

      console.log(params)
      state.loading=true
      axiosUtil.post('/backend/xsgl/xsxmbl/saveXsxmBlForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            if(state.formData.BDList.length===0){
              ElMessage({
                message: '请添加标段',
                type: 'error',
              })
              resolve(false)
              return
            }
            if(state.formData.BDList.find(item=>!item.JGMXList || item.JGMXList.length===0)){
              ElMessage({
                message: '维护中标单位',
                type: 'error',
              })
              resolve(false)
              return
            }

            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const closeForm = () => {
      emit('close')
    }

    const getZrzyList = () => {
      let params={
      }
      axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
        state.ZRZYTree = comFun.treeData(res.data || [],'ZYBM','FZYBM','children','0')
      });
    }

    const createCgxmbh = () => {
      state.loading=true
      axiosUtil.get('/backend/xsgl/cgxmgl/createCgxmbh',null).then(res=>{
        state.formData.XMXX.XMBH=res.data
        state.loading=false
      })
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }else {
        createCgxmbh()
      }
      getZrzyList()
      getDMBData("XSFS", "XSFSOptions")
      getDMBData("ZZFS", "ZZFSOptions")
      getDMBData("ZJLY", "ZJLYOptions")
      getDMBData("BJFS", "BJFSOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      addBD,
      delBD,
      changeSFFBD,
      addPBQK,
      getZbdwRes
    }
  }

})
</script>

<style scoped>

</style>
