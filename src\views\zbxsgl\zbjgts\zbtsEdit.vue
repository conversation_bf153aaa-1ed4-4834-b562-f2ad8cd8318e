<!-- 设计器版本:1.1.4 前端框架版本:VSUI2.1.0 创建时间:2023-07-18 -->
<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="180px"
           size="default" @submit.prevent v-loading="loading">
    <el-row :gutter="0" class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="项目名称：" prop="RW_RWMC">
          <div style="margin-left: 10px">{{formData.RW_RWMC}}</div>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="资金来源：" prop="ZJLY">
          <div style="margin-left: 10px">{{formData.ZJLY}}</div>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="项目编号：" prop="RW_RWBH">
          <div style="margin-left: 10px">{{formData.RW_RWBH}}</div>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
        <el-form-item label="专业分类：" prop="RW_ZYFLMC">
          <div style="margin-left: 10px">{{formData.RW_ZYFLMC}}</div>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="grid-cell" style="height: 30px;line-height: 30px;padding-left: 10px">
        合同相关信息
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="合同经办人：" prop="RW_HTJBRMC">
          <el-input v-model="formData.RW_HTJBRMC" :disabled="true" type="text" clearable>
            <template #append>
              <el-button @click="dialogVisible=true" plain :disabled="!editable">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell no-bottom-border">
        <el-form-item label="统一账号：" prop="RW_HTJBRZH">
          <div style="margin-left: 10px">{{formData.RW_HTJBRZH}}</div>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
        <el-form-item label="经办人所在单位：" prop="RW_HTJBRDWMC">
          <div style="margin-left: 10px">{{formData.RW_HTJBRDWMC}}</div>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="推送时间：" prop="TSSJ">
          <div style="margin-left: 10px">{{formData.TSSJ}}</div>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="推送人：" prop="TSRMC">
          <div style="margin-left: 10px">{{formData.TSRMC}}</div>
        </el-form-item>
      </el-col>

    </el-row>

    <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex;margin-bottom: 10px">
      <el-button type="success" @click="save('submit')" v-if="editable">提交</el-button>
      <el-button @click="closeForm">取消</el-button>
    </div>
  </el-form>
  <el-dialog
      custom-class="lui-dialog"
      v-if="dialogVisible"
      v-model="dialogVisible"
      title="合同经办人选择"
      top="1vh"
      z-index="1000"
      append-to-body
      width="1200px">
    <Htjbrxz v-if="dialogVisible" :params="params" @getRes="getDialogRes"/>
  </el-dialog>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted, nextTick, onUnmounted,
}
  from 'vue'

import vsAuth from "@src/lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "../../../lib/comFun";
import axiosUtil from "../../../lib/axiosUtil";
import Vsfileupload from "../../components/vsfileupload";
import Htjbrxz from "./htjbrxz";
export default defineComponent({
  name: '',
  components: {Vsfileupload,Htjbrxz},
  props: {
    params: {
      type: Object,
      required: true
    },
    commonParams: {
      type: Object,
      required: false
    },
    value: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      GLHTID: props.params.id,
      XSJGMXID: props.params.XSJGMXID,
      formData: {
        GLHTID:null,
        RW_RWMC:null,
        RW_RWBH:null,
        RW_ZYFLDM:null,
        RW_ZYFLMC:null,
        RW_YJFYJE:null,
        RW_FQDWDM:null,
        RW_FQDWMC:null,
        RW_HTJBRMC:null,
        RW_HTJBRZH:null,
        RW_HTJBRDWMC:null,
        RW_CFZHZT:'ZC',
        TSSJ:null,
        TSZT:null,
        TSRMC:null,
        TSRZH:null,
        CJSJ:null,
        CJR:null,
        ENABLE:'1',
      },
      rules: {
        RW_HTJBRMC: [{
          required: true,
          message: '字段值不可为空',
        }]
      },
      editable: props.params.editable,
      dialogVisible: false,
      loading: false

    })

    const getFormData = () => {
      let params = {
        XSJGMXID: state.XSJGMXID
      }
      // state.loading = true
      axiosUtil.get('/backend/xsgl/zbjgts/selectTsFrom', params).then((res) => {
        state.formData = {
          ...state.formData,
          ...res.data
        }
        getHtjbr();
        state.loading = false

      });
    }

    const instance = getCurrentInstance()
    const validateForm = (type) => {
      return new Promise((resolve, reject) => {
        if (type === 'submit') {
          instance.proxy.$refs['vForm'].validate(valid => {
            if (valid) {
              resolve(true)
              //TODO: 提交表单
            } else {
              ElMessage({
                message: '请完善页面信息',
                type: 'error',
              })
              resolve(false)
            }
          })
        } else if (type === 'save') {
          resolve(true)
        }
      })
    }
    const submitForm = (type) => {
      return new Promise((resolve, reject) => {
        console.log(state.userInfo)
        let params = {
          ...state.formData,
          SHZT: type === 'submit' ? '1' : '0'
        }
        if (props.params.operation === 'add') {
          params = {
            ...params,
            RW_FQDWDM: state.userInfo.orgnaId,
            RW_FQDWMC: state.userInfo.orgnaName,
            GLHTID: state.GLHTID,
            XSJGMXID: props.params.XSJGMXID,
            CJR: state.userInfo.userLoginName,
            CJSJ: comFun.getNowTime(),
            TSZT: '2'
          }
        }
        axiosUtil.post('/backend/xsgl/zbjgts/saveTshtxx', params).then(res => {
          if (res.message === 'success' && res.data=='success') {
            resolve(true)
          }else{
            ElMessage({
                message: res.data,
                type: 'error',
              })
          }
        })
      })
    }

    const closeForm = () => {
      emit('closeDialog')
    }
    //正常保存
    const save = (type) => {
      validateForm(type).then(res=>{
        if(res){
          submitForm(type).then(submitRes=>{
            if(submitRes){
              closeForm()
              ElMessage({
                type: 'success',
                customClass: "myMessageClass",
                message: '推送成功',
              })
            }
          })
        }
      })
    }
    const getDialogRes = (res) => {
      if(res){
        state.formData.RW_HTJBRMC= res.USERNAME
        state.formData.RW_HTJBRZH= res.USERID
        state.formData.RW_HTJBRDWMC= res.LONGNAME
      }
      state.dialogVisible=false
    }
    const getHtjbr = () => {
      let params={
        USERID:state.userInfo.userLoginName
      }
      axiosUtil.get('/backend/xsgl/zbjgts/selectHtjbr', params).then((res) => {
        if(res.data.list!=null&&res.data.list.length>0){
            state.formData.RW_HTJBRMC= res.data.list[0].USERNAME
            state.formData.RW_HTJBRZH= res.data.list[0].USERID
            state.formData.RW_HTJBRDWMC= res.data.list[0].LONGNAME
        }
      });
    }
    onMounted(() => {
      getFormData();
      
      state.formData.TSRMC=state.userInfo.userName
      state.formData.TSRZH=state.userInfo.userLoginName
      state.formData.TSSJ=comFun.getNowTime()
    })

    return {
      ...toRefs(state),
      validateForm,
      submitForm,
      closeForm,
      save,
      getDialogRes,
      getHtjbr

    }
  }
})

</script>
<style scoped>
:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: white;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: white;
}

:deep( .el-collapse-item__header ) {
  background-color: #F2F3F5;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
}
:deep(.no-bottom-border .el-form-item__content){
  border-bottom: none;
}
</style>

