<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
           <div style="width:100%;" align="right">
<el-button  class="lui-table-button" v-if="userinfo.userLoginName=='denghui.sripe'" size="small" type="primary" @click="exportData">导出</el-button><!-- v-if="userinfo.userLoginName=='denghui.sripe'" -->
           </div>
           
    <div class="container-wrapper">
      <el-table ref="dataTable"
                class="lui-table"
                :span-method="arraySpanMethod"
                :data="tableData" height="calc(100vh - 140px)"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column label="序号" type="index" width="50" fixed="left"></el-table-column>
        <el-table-column prop="LYMC" label="领域" align="center" :show-overflow-tooltip="true" min-width="150px"/>
        <el-table-column prop="ZYMC" label="专业" align="center" :show-overflow-tooltip="true" min-width="150px"/>
        <el-table-column v-for="(item,index) in ZYZCOptions" :key="index" :prop="item.DMXX" :label="item.DMMC" align="center" width="200px">
          <template #default="{row}">
            <div style="display: flex;">
              <div style="width: 80px;">{{row[item.DMXX] || 0}}</div>
              <el-button class="lui-table-button" size="small" type="primary" @click="editData(row,item.DMXX)">配置</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog v-if="dialogVisible"
               custom-class="lui-dialog"
               append-to-body
               v-model="dialogVisible"
               title="人员配置"
               width="1200px"
               z-index="1000"
               @close="closeDialog">
      <zjzyglEdit :params="params"/>

    </el-dialog>

  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import zjzyglEdit from "./zjzyglEdit";
import {Search, Upload, Plus} from '@element-plus/icons-vue'
import {auth, mixin} from "@src/assets/core/index";
export default defineComponent({
  name: '',
  components: {zjzyglEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userinfo : auth.getPermission(),
      tableData:[{}],
      ZYZCOptions:[],
      arraySpanData:[],
      params:{},
      dialogVisible: false
    })

    const getDataList = () => {
      axiosUtil.get('/backend/zjgl/zjzygl/selectZyxx', {}).then((res) => {
        let treeData = treeToData(res.data, 'ZYBM', 'FZYBM', 'children','0');
        let resData=[]
        let spanData=[]
        treeData.forEach(item1=>{
          let spanNum=0
          if(item1.children){
            spanNum=item1.children.length
            item1.children.forEach(item2=>{
              spanData.push(spanNum)
              spanNum=0
              resData.push({
                ...item2,
                LYMC: item1.ZYMC
              })
            })
          }
        })
        state.arraySpanData=spanData
        state.tableData = resData
      });
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }

    const exportData = () => {
      let column = [[
          { field: 'XM', title: '姓名', width: 280, halign: 'center', align: 'left' },
          { field: 'GZDW', title: '单位', width: 280, halign: 'center', align: 'left' },
          { field: 'JBMC', title: '级别', width: 280, halign: 'center', align: 'left' },
          { field: 'LY', title: '领域', width: 400, halign: 'center', align: 'left' },
          { field: 'ZY', title: '专业', width: 400, halign: 'center', align: 'left' },
          { field: 'RYZH', title: '账号', width: 280, halign: 'center', align: 'left' },
        ]]
      let params={
        title: "专家信息",
        name: "专家信息",
        params: {},
        url: '/excel/zjxxExport',
      }
      params.column=column
      axiosUtil.exportExcel('/backend/commonExport/magicExcel',params)

    }


    const editData = (row,ZJJB) => {
      state.params={
        ZYBM: row.ZYBM,
        ZYMC: row.ZYMC,
        ZJJB: ZJJB
      }
      state.dialogVisible=true

    }

    /**
     * 数据转树型结构
     * @param data
     * @param id id名称
     * @param parentId 父id名称
     * @param childName 生成的子节点名称
     * @param root 根节点的值
     * @returns {*}
     */
    const treeToData = (data, id, parentId, childName, root) => {
      let cloneData = data
      return cloneData.filter((father) => {
        father[childName] = cloneData.filter((child) => {
          return father[id] === child[parentId]
        })
        return father[parentId] === root
      })
    }
    
    const arraySpanMethod = ({row, column, rowIndex, columnIndex}) => {
      if(columnIndex===1){
        if(state.arraySpanData[rowIndex]>0){
          return{
            rowspan: state.arraySpanData[rowIndex],
            colspan: 1
          }
        }else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    }
    const closeDialog = () => {
      getDataList()
    }

    onMounted(() => {
      getDataList()
      getDMBData('ZYZC', 'ZYZCOptions')

    })


    return {
      ...toRefs(state),
      editData,
      arraySpanMethod,
      closeDialog,
      exportData

    }
  }

})
</script>

<style scoped>

</style>
