<template>
  <div v-loading="loading">
    <zbwjTab class="tab-pane" ref="zbwjTab" :params="params" v-model="formData"/>
    <div style="width: 100%;margin-top: 10px;margin-bottom: 10px;justify-content: center;display: flex" v-if="!value.activityId">
      <el-button size="default" type="success" @click="saveForm('save')" v-if="editable">保存</el-button>
      <el-button size="default" type="primary" @click="saveForm('submit')" v-if="editable" :disabled="!FQRLX">提交</el-button>
      <el-button size="default" @click="closeForm" v-if="editable">返回</el-button>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import zbwjTab from "@views/zbxsgl/xssqgl/commonTab/zbwjTab";
import axios from "axios";
import api from "@src/api/lc";
import { log } from "handlebars";


export default defineComponent({
  name: '',
  components: {zbwjTab},
  props: {
    params: {
      type: Object,
      required: true
    },
    value: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      WJID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        SFFBD: '1',

        XMXX: {},

        XSWJ: {
          WJID: comFun.newId(),
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
          SHZT: '0',
          YWZT: '1',
          SFWSPS: '0'
        },
      },

      FQRLX: '',
      processParams:{
        EJDW: {
          ZB:{
            activityId: "new",
            processId: "XSGL_ZBWJ_EJDW",
            processVersion:"1"
          },
          FZB:{
            activityId: "new",
            processId: "XSGL_CGWJ_EJDW",
            processVersion:"1"
          },
        },
        JGBM: {
          ZB:{
            activityId: "new",
            processId: "XSGL_ZBWJ_JGBM",
            processVersion:"1"
          },
          FZB:{
            activityId: "new",
            processId: "XSGL_CGWJ_JGBM",
            processVersion:"1"
          },
        }

      },

    })

    const getFormData = () => {
      let params = {
        WJID: state.WJID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/xssqgl/selectXswjxxById', params).then((res) => {
        state.formData = res.data
        state.FQRLX=res.data.XMXX.FQLX
        getFQRLX()
        state.loading = false
      })
    }

    const saveForm = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(res => {
          submitForm(type)
        }).catch(msg => {
          ElMessage.error(msg)
        })
      }
    }


    const saveData = (value) => {
      let type = value === '1' ? 'submit' : 'save'
      return new Promise((resolve, reject) => {
        if (type === 'save') {
          resolve(submitForm(type))
        } else {
          validateForm().then(res => {
            resolve(submitForm(type))
          }).catch(msg => {
            reject(msg)
          })
        }
      })

    }

    const submitForm = (type) => {
      return new Promise((resolve,reject) => {
        if (props.value.activityId && !['1', 'new'].includes(props.value.activityId)) {
          resolve(true)
          return
        }

        let pbgzParams = {
          WJID: state.WJID,
        }

        if(state.formData.XSWJ.SFWSPS==='1'){
          let pbgzList = {};
          pbgzList.xspsData = instance.proxy.$refs.zbwjTab.xspsData;
          pbgzList.zgpsData = instance.proxy.$refs.zbwjTab.zgpsData;
          pbgzList.xyxpsData = instance.proxy.$refs.zbwjTab.xyxpsData;
          pbgzList.zhpsData = instance.proxy.$refs.zbwjTab.zhpsData;
          pbgzList.jspsData = instance.proxy.$refs.zbwjTab.jspsData;
          pbgzList.bjpsData = instance.proxy.$refs.zbwjTab.bjpsData;
          //权重
          let qzdata = instance.proxy.$refs.zbwjTab.pbgzTableData

          let qzNum = null;
          for (let i in qzdata) {
            if(qzdata[i]['checked'] && qzdata[i]['LX'] === 'XSPS' && pbgzList.xspsData.length == 0){
              ElMessage({message: qzdata[i]['PSLX'] + "已选择，请维护规则配置！", type: 'error',});
              return
            }
            if(qzdata[i]['checked'] && qzdata[i]['LX'] === 'ZGPS' && pbgzList.zgpsData.length == 0){
              ElMessage({message: qzdata[i]['PSLX'] + "已选择，请维护规则配置！", type: 'error',});
              return
            }
            if(qzdata[i]['checked'] && qzdata[i]['LX'] === 'XYXPS' && pbgzList.xyxpsData.length == 0){
              ElMessage({message: qzdata[i]['PSLX'] + "已选择，请维护规则配置！", type: 'error',});
              return
            }
            if(qzdata[i]['checked'] && qzdata[i]['LX'] === 'BJPS' && pbgzList.bjpsData.length == 0){
              ElMessage({message: qzdata[i]['PSLX'] + "已选择，请维护规则配置！", type: 'error',});
              return
            }
            if(qzdata[i]['checked'] && qzdata[i]['LX'] === 'ZHPS' && pbgzList.zhpsData.length == 0){
              ElMessage({message: qzdata[i]['PSLX'] + "已选择，请维护规则配置！", type: 'error',});
              return
            }
            if(qzdata[i]['checked'] && qzdata[i]['LX'] === 'JSPS' && pbgzList.jspsData.length == 0){
              ElMessage({message: qzdata[i]['PSLX'] + "已选择，请维护规则配置！", type: 'error',});
              return
            }
            // 校验 如果选择了 则必须维护权重数据
            if((qzdata[i]['LX'] === 'BJPS' || qzdata[i]['LX'] === 'ZHPS' || qzdata[i]['LX'] === 'JSPS')
                && qzdata[i]['checked'] && ( qzdata[i]['QZ'] == '' || qzdata[i]['QZ'] == null)){
              ElMessage({message: qzdata[i]['PSLX'] + "已选择，请维护权重(%)信息", type: 'error',})
              return
            }
            // 权重求和
            if(qzdata[i]['checked'] && qzdata[i]['QZ'] != '' && qzdata[i]['QZ'] != null){
              qzNum = qzNum + parseInt(qzdata[i]['QZ']);
            }
            if (qzdata[i]['LX'] === 'BJPS') {
              pbgzList.bjqz = qzdata[i]['QZ']
            } else if (qzdata[i]['LX'] === 'ZHPS') {
              pbgzList.zhqz = qzdata[i]['QZ']
            } else if (qzdata[i]['LX'] === 'JSPS') {
              pbgzList.jsqz = qzdata[i]['QZ']
            }
          }
          // 校验权重总和应为100
          if((pbgzList.bjqz || pbgzList.zhqz || pbgzList.jsqz) && qzNum != 100){
            ElMessage({message: "权重(%)总值不为100！", type: 'error',})
            return
          }
          pbgzParams.pbgzList = pbgzList;

          if (props.params.operation === 'add') {
            pbgzParams = {
              ...pbgzParams,
              CJR: state.userInfo.userLoginName,
              CJSJ: comFun.getNowTime(),
            }
          } else if (props.params.operation === 'edit') {
            pbgzParams = {
              ...pbgzParams,
              XGR: state.userInfo.userLoginName,
              XGSJ: comFun.getNowTime(),
            }
          }
        }





        let params = {
          ...state.formData,
          XSWJ: {
            ...state.formData.XSWJ,
            WJID: state.WJID,
            XGRZH: state.userInfo.userLoginName,
            XGSJ: comFun.getNowTime(),
          },
          ...pbgzParams,
        }
        if (type === 'submit') {
          params.XSWJ.SHZT = '1'
        }else {
          params.XSWJ.SHZT = '0'
        }
        state.loading = true

        axiosUtil.post('/backend/xsgl/xssqgl/saveXswjxx', params).then(res => {
          if(params.XSWJ.SHZT==='1' && !props.value.activityId){
            addTask()
          }else if(!props.value.activityId){
            ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
            closeForm()
            state.loading = false
          }else {
            resolve(true)
          }
        })
      })

    }

    const addTask = () => {
      let processName=state.formData.XMXX.XMMC+'项目-招标文件申请'
      let _params = {};
      _params.strSystemCode = 'AUTHM_CENTER';
      _params.processId = state.processParams[state.FQRLX][state.formData.SQLX].processId;
      _params.engineType = 'vs';
      _params.activityId = 'new';
      _params.processInstanceName = processName;
      _params.businessId = state.WJID;
      _params.apprValue = "1";
      _params.apprResult = "提交";
      _params.loginName = state.userInfo.userLoginName;
      _params.userOrgId = state.userInfo.orgnaId;
      axios({
        method: "post",
        url: api.createTask(),
        data: "varJson=" + JSON.stringify(_params),
      }).then((res) => {
        if (res.data && res.data.result == 1) {
          ElMessage({message: '提交成功！', type: 'success',})
          closeForm()
        } else {
          state.loading=false
          ElMessage({message: '提交失败！', type: 'error',})
        }
      }).catch(err=>{
        state.loading=false
        ElMessage({message: '提交失败！', type: 'error',})
      })



    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return Promise.all([
        instance.proxy.$refs['zbwjTab'].validateForm(),
        checkProcess()
      ])
    }

    const checkProcess = () => {
      return new Promise((resolve, reject) => {
        if(state.processParams[state.FQRLX] && state.processParams[state.FQRLX][state.formData.SQLX]){
          resolve(true)
        }else {
          reject('未找到发起流程')
        }
      })

    }

    const closeForm = () => {
      emit('close')
    }

    const getFQRLX = () => {
      let params={
        USER_ID: vsAuth.getAuthInfo().permission.userId
      }
      axiosUtil.get('/backend/common/selectOrganByUserId',params).then(res=>{
        if(res.data && res.data.length>0 && res.data[0].DWLX==='JG'){
          state.FQRLX='JGBM'
        }
      })
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    onMounted(() => {
      getFormData()
      getDMBData("XSFS", "XSFSOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      saveForm,
    }
  }

})
</script>

<style scoped>
.tab-pane {
  height: 65vh;
  overflow: auto;
}
</style>
