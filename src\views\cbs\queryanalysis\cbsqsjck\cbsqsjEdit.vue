<template>
  <div v-loading="loading" class="container">
    <el-tabs v-model="ActiveTab" type="border-card" style="height: calc(100% - 70px)">
      <el-tab-pane :name="item.SJMBBM" :label="item.SJMBMC" v-for="(item,index) in compList">
        <div class="tab-pane-content">
          <component
              v-if="formData[item.SJMBBM]"
              v-model="formData"
              :ref="(el) => setRefMap(el, item.SJMBBM)"
              :is="ComponentDic[item.SJMBBM]"
              :defaultData="formData[item.SJMBBM]"
              :row="formData[item.SJMBBM]"
              :DWYWID="DWYWID"
              :TYXYDM="formData?.JBXX?.TYXYDM"
              YWLXDM="BG"
              :editable="editable"
              :resultTableData="resultFormData?.[item.SJMBBM]"
          ></component>
        </div>
      </el-tab-pane>
    </el-tabs>

    <div style="align-items: center;justify-content: center;display: flex;background-color: white;height: 70px" v-if="editable&&!value">
      <el-button size="default" type="success" @click="saveData('save')">保存</el-button>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw} from "vue";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import jbxxTab from "@views/cbs/cbsbg/commonTab/jbxx/jbxxTab";
import zzxxTab from "@views/cbs/cbsbg/commonTab/zzxx/zzxxTab";
import dwxxTab from "@views/cbs/queryanalysis/cbsqsjck/dwxxTab";
import xkxx from "@views/cbs/templateManagement/DataTemplateManagement/xkxx/xkxx.vue"
import txzs from "@views/cbs/templateManagement/DataTemplateManagement/txzs/txzs.vue"
import ryxxTab from "@views/cbs/cbsbg/commonTab/ryxx/ryxxTab";
import yjxxTab from "@views/cbs/cbsbg/commonTab/yjxx/yjxxTab";
import jcxx from "@views/cbs/templateManagement/DataTemplateManagement/jcxx/jcxx.vue"
import zscq from "@views/cbs/templateManagement/DataTemplateManagement/zscq/zscq.vue"
import fwxx from "@views/cbs/templateManagement/DataTemplateManagement/fwxx/fwxx.vue"
import tdxxTab from "@views/cbs/cbsbg/commonTab/tdxx/tdxxTab";
import sqwtxx from "@views/cbs/templateManagement/DataTemplateManagement/sqwtxx/sqwtxx.vue"
import yrxxTab from "@views/cbs/cbsbg/commonTab/yrxx/yrxxTab";
import tycList from "@views/cbs/tyc/tycList.vue"
import zdxxTab from "@views/cbs/cbsbg/commonTab/zdxx/zdxxTab";
import aqhbTab from "@views/cbs/cbsbg/commonTab/aqhb/aqhbTab";
import {ElLoading, ElMessage} from "element-plus";
import comFun from "@lib/comFun";
import {auth, mixin} from "@core";
import sbxxTab from "@views/cbs/cbsbg/commonTab/sbxx/sbxxTab";
import clxxTab from "@views/cbs/cbsbg/commonTab/clxx/clxxTab";
import tabFun from "@lib/tabFun";
import axios from "axios";
import api from "@src/api/lc";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    value: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params?.editable,
      DWYWID: props.params?.id,
      ActiveTab: 'JBXX',
      formData: {},
      resultFormData: {},
      ComponentDic: {
        JBXX: markRaw(jbxxTab),
        ZZXX: markRaw(zzxxTab),
        XKXX: markRaw(xkxx),
        TXZS: markRaw(txzs),
        RYXX: markRaw(ryxxTab),
        SBXX: markRaw(sbxxTab),
        YJXX: markRaw(yjxxTab),
        JCXX: markRaw(jcxx),
        ZSCQ: markRaw(zscq),
        CLXX: markRaw(clxxTab),
        FWXX: markRaw(fwxx),
        TDXX: markRaw(tdxxTab),
        SQWTXX: markRaw(sqwtxx),
        DWXX: markRaw(dwxxTab),
        YRSQXX: markRaw(yrxxTab),
        QYFXXX: markRaw(tycList),
        ZDXX: markRaw(zdxxTab),
        AQHB: markRaw(aqhbTab),
      },
      refMap: {},

      compList: [],
      MBMXList: [],


    })

    const getFormData = (DWYWID, resForm, isLs) => {
      let params = {
        DWYWID: DWYWID
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/cbsyj/getTeamInfo', params).then((res) => {
        let resData = state.MBMXList.reduce((t, i) => {
          t[i.SJMBBM] ? t[i.SJMBBM].push(i) : (t[i.SJMBBM] = [i]);
          return t;
        }, {})
        Object.entries(res.data || {}).forEach(([key, value]) => {
          if (value) {
            //排除null
            if (Array.isArray(value)) {
              //list的话且不为空数组的时赋值
              if (value.length || isLs) resData[key] = value;
            } else {
              resData[key] = value;
            }
          }
        })

        state[resForm] = resData
        state.loading = false
      })
    }



    const getTabList = () => {
      let params = {}
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/report/cbsqsj/selectAllBqxx', params).then((res) => {
        let tab = res.data.tabList || []
        state.MBMXList = res.data.MBMXList || []
        state.MBMXList = state.MBMXList.map((i) => ({
          ...i,
          ZYMC: i.ZYFLMC,
          ZYBM: i.ZYFLDM,
          SHZT: "0",
        }))
        let MBLX = res.data.MBLX || []
        state.compList = [{SJMBMC: "承包商基本信息", SJMBBM: "JBXX"}]
        state.compList.push({SJMBMC: "队伍信息", SJMBBM: "DWXX"})
        state.compList.push({SJMBMC: "引入申请信息", SJMBBM: "YRSQXX"})
        // if (MBLX.map(i => i.MBLX).includes('DW')) {
        //   state.compList.push({SJMBMC: "队伍信息", SJMBBM: "DWXX"})
        // } else {
        //   state.compList.push({SJMBMC: "引入申请信息", SJMBBM: "YRSQXX"})
        // }
        state.compList.push( ...tab)
        getFormData(state.DWYWID, 'formData')
      })
    }
    const {vsuiEventbus} = mixin()

    const saveData = (type) => {
      if (type === 'save') {
        submitForm(type).then(() => {
          ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
          state.loading = false
        })
      } else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }
    }


    const submitForm = (type) => {
      return new Promise(resolve => {
        state.loading = true

        let params = formatData({...state.formData})

        params.type=type


        console.log('最后的提交', params)
        axiosUtil.post('/backend/sccbsgl/report/cbsqsj/saveCbsQbxx', params).then(res => {
          resolve()
        })
      })


    }



    const formatData = (params) => {
      const userinfo = auth.getPermission();

      const updateUserInfo = {
        XGRZH: userinfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }

      const setDefaultValue = (data) => {
        Object.entries(updateUserInfo).forEach(([key, value]) => {
          if (!data[key]) {
            data[key] = value;
          }
        })
      }

      Object.entries(params).forEach(([key, value]) => {
        if (value) {
          //排除null
          if (Array.isArray(value)) {
            //list的话且不为空数组的时赋值
            value.forEach((x) => {
              x.DWYWID = state.DWYWID;
              setDefaultValue(x);
            });
          } else if (value instanceof Object) {
            setDefaultValue(value);
          }
        }
      })
      params.ZZXX?.forEach((x) => {
        x.ZSDLDM = "ZZZS";
        x.ZSCYZBS = params.YRSQXX.DWYWID
      });
      params.TXZS?.forEach((x) => {
        x.ZSDLDM = "TXZS";
        x.ZSCYZBS = params.YRSQXX.DWYWID
      });
      params.XKXX?.forEach((x) => {
        x.ZSDLDM = "XKZS";
        x.ZSCYZBS = params.YRSQXX.DWYWID
      });
      params.RYXX?.forEach(x => {
        if (x.ZSXX) {
          x.ZSXX.forEach(v => {
            v.DWYWID = state.DWYWID
            setDefaultValue(v);
          })
        }
      })

      let form = {
        cbsjbxx: params.JBXX,
        cbsdwxx: params.YRSQXX,
        cbszs: (params.ZZXX ?? []).concat(params.TXZS ?? []).concat(params.XKXX ?? []),
        cbsdwyj: params.YJXX,

        cbsdwzscq: params.ZSCQ,
        cbsdwclxx: params.CLXX,
        cbsdwfwxx: params.FWXX,
        cbsdwtdxx: params.TDXX,
        cbsdwzdxx: params.ZDXX,
        cbsdwaqhb: params.AQHB,

        cbsdwsb: params.SBXX,
        cbsdwjcqk: params.JCXX,
        cbsdwcy: params.RYXX,
        bczt: '0', //"0保存；1提交；2审核通过；"
        DWYWID: state.DWYWID,
      }
      form.cbszs.forEach(x => x.ZSCYZLXDM = 'CBS')
      return form
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        Promise.all(Object.values(state.refMap).filter(i => i.validateForm).map(i => i.validateForm())).then((result) => {
          ElMessage.success('校验成功')
          resolve(true)
        }).catch((err) => {
          ElMessage.error(Object.values(err)[0]?.[0]?.message)
          resolve(false)
        })
      })
    }

    const setRefMap = (el, name) => {
      state.refMap[name] = el
    }

    onMounted(() => {
      if (!props.params) {
        ElMessage.warning('参数缺失请关闭该页签重新打开！')
        return;
      }
      getTabList()
    })

    return {
      ...toRefs(state),
      setRefMap,
      saveData

    }
  }

})
</script>

<style scoped>
.container {
  height: calc(100vh - 100px);
  background-color: #fff;
}

.container .el-tabs {
  height: 100%;
}

.container .el-tabs ::v-deep .el-tabs__header {
  margin-bottom: 0;
}

.container .el-tabs ::v-deep .el-tabs__content {
  height: calc(100% - 60px);
}

.tab-pane-content {
  height: 100%;
}

:deep(.dataChange) {
  color: #F56C6C;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-table .warning-row) {
  --el-table-tr-bg-color: #f8bcbc;
}

:deep(.el-table .success-row) {
  --el-table-tr-bg-color: #e2fad4;
}


</style>
