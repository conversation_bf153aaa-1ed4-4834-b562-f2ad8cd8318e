<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="200px"
             size="default" v-loading="loading" @submit.prevent>
      <div class="title-text">股权穿透分析结果详情</div>


      <div class="label-text">
        <div style="flex: 1">原公司：{{formData.YGSMC}}</div>
        <div style="flex: 1">穿透公司：{{formData.CTGSMC}}</div>
      </div>



      <el-tabs v-model="ActiveTab" type="border-card">
        <el-tab-pane :name="index+''"
                     v-for="(item,index) in CCFWOptions">
          <template #label>
            {{item.DMMC}}
            <!--（<span >
            {{formData[item.DMXX]}}
          </span>）-->
          </template>
          <dbxqTab :params="item" v-model="formData"/>
        </el-tab-pane>
      </el-tabs>

    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import dbxqTab from "@views/gqct/gqctgl/gqdbxqTab";

export default defineComponent({
  name: '',
  components: {dbxqTab},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GQCTJLID: props.params.id,
      FXQDID: props.params.FXQDID,
      formData: {
        GQCTJLID:props.params.id,
        FXQDID: props.params.FXQDID,
        YGSMC:props.params.YGSMC,
        CTGSMC:props.params.CTGSMC,
        GQGX:props.params.GQGX,
        RZGX:props.params.RZGX,
        GQRZGX:props.params.GQRZGX,
      },
      CCFWOptions: [],
    })

    const getFormData = () => {
      let params={
        GQCTJLID: state.GQCTJLID
      }
      state.loading=true
      /*axiosUtil.get('/backend/bscc/bsccgl/selectCcjgxqById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })*/
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
        state.loading=false
      })
    }

    onMounted(() => {
      getFormData()
      getDMBData('GQCTSZ', 'CCFWOptions')
    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>
.title-text {
  font-size: 20px;
  margin-bottom: 20px;
  width: 100%;
  text-align: center;
  color: #980404;
}
.label-text{
  display: flex;
  font-family: 黑体;
  color: black;
  padding: 20px;

}
</style>
