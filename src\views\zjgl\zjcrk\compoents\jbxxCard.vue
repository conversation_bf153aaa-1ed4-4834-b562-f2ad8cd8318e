<template>
  <el-row :gutter="0" class="grid-row" style="margin-bottom:16px;">
    <el-col :span="8" class="grid-cell">
      <el-form-item label="姓名：" prop="XM">
        <el-input v-model="formData.XM" type="text" placeholder="请输入" :disabled="!editable" clearable></el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="性别：" prop="XB">
        <el-radio-group v-model="formData.XB">
          <el-radio v-for="(item, index) in XBOptions" :disabled="!editable" :key="index" :label="item.DMXX"
                    style="{display: inline}">
            {{ item.DMMC }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="身份证号：" prop="SFZH">
        <el-input v-model="formData.SFZH" type="text" :disabled="!editable" clearable @blur="onInputBlur"></el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="工作单位：" prop="GZDW">
        <el-input readonly v-model="formData.GZDWMC" type="text" :disabled="!editable" clearable>
          <template #append>
            <el-button @click="dialogDWXZVisible=true" plain :disabled="!editable">选择</el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="出生年月：" prop="RKZJBXX.CSNY">
        <el-date-picker v-model="formData.RKZJBXX.CSNY" type="month" class="full-width-input" format="YYYY-MM"
                        value-format="YYYY-MM" :disabled="!editable" clearable :editable="false"></el-date-picker>
      </el-form-item>
    </el-col>
    <!--        <el-col :span="8" class="grid-cell">-->
    <!--          <el-form-item label="手机号：" prop="SJH">-->
    <!--            <el-input v-model="formData.RKZJBXX.SJH" type="text" placeholder="请输入" :disabled="!editable"-->
    <!--                      clearable></el-input>-->
    <!--          </el-form-item>-->
    <!--        </el-col>-->
    <el-col :span="8" class="grid-cell">
      <el-form-item label="学历：" prop="XL">
        <el-select v-model="formData.XL" class="full-width-input" :disabled="!editable" clearable>
          <el-option v-for="(item, index) in XLOptions" :key="index" :label="item.DMMC"
                     :value="item.DMXX"></el-option>
        </el-select>
      </el-form-item>
    </el-col>

  </el-row>
  <el-dialog z-index="1000" custom-class="lui-dialog" v-model="dialogDWXZVisible" title="工作单位选择" width="800px"
             class="dialogClass"
             append-to-body>
    <dwxz v-if="dialogDWXZVisible" @close="dialogDWXZVisible=false" @parentMethod="getDWCheck"></dwxz>
  </el-dialog>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import axiosUtil from "@lib/axiosUtil";
import Dwxz from "@views/zjgl/zjcrk/common/dwxz";


export default defineComponent({
  name: '',
  components: {Dwxz},
  props: {
    formData:{
      required:true,
      type: Object
    },
    params:{
      type: Object
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: props.params.editable,
      XBOptions: [],
      XLOptions: [],
      dialogDWXZVisible: false,
    })


    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }
    const getDWCheck = (value) => {
      props.formData.GZDW = value.ORGNA_ID
      props.formData.GZDWMC = value.ORGNA_NAME
      state.dialogDWXZVisible = false
    }

    const onInputBlur = () => {
      let iden = props.formData.SFZH;
      let sex = null;
      let birth = null;
      let myDate = new Date();
      let month = myDate.getMonth() + 1;
      let day = myDate.getDate();
      let age = 0;

      if (props.formData.SFZH.length === 18) {
        age = myDate.getFullYear() - iden.substring(6, 10) - 1;
        sex = iden.substring(16, 17);
        birth = iden.substring(6, 10) + "-" + iden.substring(10, 12);
        if (iden.substring(10, 12) < month || iden.substring(10, 12) == month && iden.substring(12, 14) <= day) age++;

      }
      if (props.formData.SFZH.length === 15) {
        age = myDate.getFullYear() - iden.substring(6, 8) - 1901;
        sex = iden.substring(13, 14);
        birth = "19" + iden.substring(6, 8) + "-" + iden.substring(8, 10);
        if (iden.substring(8, 10) < month || iden.substring(8, 10) == month && iden.substring(10, 12) <= day) age++;
      }

      if (sex % 2 === 0)
        sex = '0';
      else
        sex = '1';
      //性别  ==> 1:男       0:女
      // this.form.sex = sex;
      props.formData.RKZJBXX.CSNY = birth;
    }

    onMounted(() => {
      getDMBData('XB', 'XBOptions')
      getDMBData('XL', 'XLOptions')
    })

    return {
      ...toRefs(state),
      getDWCheck,
      onInputBlur

    }
  }

})
</script>

<style scoped>

</style>
