{"_args": [["@vsui/vue-multiplex@2.1.0", "F:\\VUEProject\\scgl-product\\scgl-prodect-front-1.0"]], "_from": "@vsui/vue-multiplex@2.1.0", "_id": "@vsui/vue-multiplex@2.1.0", "_inBundle": false, "_integrity": "sha512-JBGPx4tu2tNosTqn354F3nCvAHmOxgrlH3cwD48xlCiXsdFWkZQCnSaEVOhnGa2MkppXFroGmGFkFQrxpVZiPQ==", "_location": "/@vsui/vue-multiplex", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vsui/vue-multiplex@2.1.0", "name": "@vsui/vue-multiplex", "escapedName": "@vsui%2fvue-multiplex", "scope": "@vsui", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/"], "_resolved": "http://***********:7001/@vsui/vue-multiplex/download/@vsui/vue-multiplex-2.1.0.tgz", "_spec": "2.1.0", "_where": "F:\\VUEProject\\scgl-product\\scgl-prodect-front-1.0", "author": {"name": "崔良"}, "company": "VICTORYSOFT CO,LTD", "debuggerDependencies": {"@babel/core": "^7.14.3", "@babel/plugin-proposal-decorators": "7.14.2", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/plugin-transform-runtime": "7.14.3", "@babel/polyfill": "7.12.1", "@babel/preset-env": "^7.14.4", "@babel/runtime": "^7.14.0", "@babel/runtime-corejs3": "^7.14.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^8.2.2", "babel-plugin-transform-vue-jsx": "^3.7.0"}, "dependencies": {"@vsui/lib-jsext": "file:jsext", "axios": "^0.27.2", "color-convert": "^2.0.1", "element-plus": "^2.2.2", "font-awesome": "^4.7.0", "mitt": "^3.0.0", "nprogress": "^0.2.0", "vue": "^3.2.33", "vue-router": "^4.0.15", "vuex": "^4.0.2"}, "description": "@vsui/vue-multiplex@2.1.0", "devDependencies": {"@babel/core": "^7.14.3", "@babel/plugin-proposal-decorators": "^7.14.2", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/plugin-transform-runtime": "^7.14.3", "@babel/preset-env": "^7.14.4", "@babel/runtime": "^7.14.0", "@babel/runtime-corejs3": "^7.14.0", "@vue/compiler-sfc": "^3.2.41", "autoprefixer": "^10.2.5", "babel-loader": "^8.2.2", "copy-webpack-plugin": "9.0.0", "core-js": "^3.23.5", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "3.0.1", "file-loader": "^6.2.0", "mini-css-extract-plugin": "^2.6.0", "node-notifier": "^6.0.0", "postcss": "8.4.20", "postcss-import": "^14.0.0", "postcss-loader": "^7.0.2", "terser-webpack-plugin": "^5.1.3", "url-loader": "^4.1.1", "vue-loader": "^17.0.0", "vue-style-loader": "^4.1.3", "webpack": "5.75.0", "webpack-cli": "^4.10.0"}, "email": "<EMAIL>", "engines": {"node": ">= 14.17.0", "npm": ">= 8.1.0"}, "files": ["dist"], "license": "", "main": "dist/lib/js/vsui.vue.multiplex.umd.min.js", "name": "@vsui/vue-multiplex", "peerDependencies": {"@vsui/lib-jsext": "file:jsext", "axios": "^0.27.2", "element-plus": "^2.2.0", "font-awesome": "^4.7.0", "mitt": "^3.0.0", "vue": "^3.2.33", "vue-router": "^4.0.15", "vuex": "^4.0.2"}, "private": false, "publishConfig": {"registry": "http://***********:7001/", "access": "public"}, "scripts": {"build:core": "webpack --config  build/webpack.conf.js", "eslint": "eslint src", "pack:core": "npm pack ", "postinstall": "node dist/postinstall.js", "pub:core": "npm publish ", "unpub:core": "npm unpublish @vsui/vue-multiplex@2.1.0"}, "version": "2.1.0"}