<!-- 选择专业 -->
<template>
  <div class="container">
    <div style="height: 40px;background-color: white;padding: 20px;">
      <el-steps
          :active="currentStep"
          finish-status="success"
      >
        <el-step title="选择专业" />
        <el-step title="选择区域" />
      </el-steps>
    </div>
    <el-row>
      <el-col :span="24">
        <div class="comp">
          <xzzy v-if="currentStep == 0" :dwlx="dwlx" :zybms="zybms" :NEWDWYWID="NEWDWYWID" :ZRLX="ZRLX" @nextStep="nextStep" @selectUpdate="setSelect"></xzzy>
          <xzqy v-if="currentStep == 1" :dwlx="dwlx" @preStep="preStep" :selectData="selectData"></xzqy>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref,provide, useAttrs } from 'vue';
import xzqy from "./xzqy.vue";
import xzzy from "./xzzy.vue";
let currentStep = ref(0)
const selectData = ref([])
const attrs = useAttrs();
const {dwlx, zybms,uuId,NEWDWYWID,ZRLX} = attrs;
provide('uuId',uuId)
const setSelect = (data) => {
  selectData.value = data
}

const nextStep = () =>{
    currentStep.value = 1
}
const preStep = () =>{
    currentStep.value = 0
}

</script>

<style scoped>
.container{
    height: 100%;
}
.step{
    /*height: 70px;*/
}
/*.step .el-steps{*/
/*    margin-top: 0!important;*/
/*    width: 100%;*/
/*}*/
.comp{
    /*height: calc(100% - 70px);*/
}
</style>
