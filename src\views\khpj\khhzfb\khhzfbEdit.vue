<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px">
        基本信息
      </div>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="考核时间：" prop="PJZQ">
            <el-select v-model="formData.PJZQ" class="full-width-input"
                       :disabled="!editable||operation!=='add'" @change="formData.HZMXList=[]"
                       clearable>
              <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC"
                         :value="item.PJZQID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="考核专业：" prop="PJZYBM">
            <el-select v-model="formData.PJZYBM" class="full-width-input"
                       :disabled="!editable||operation!=='add'" @change="changeKhzy" filterable
                       clearable>
              <el-option v-for="(item, index) in KHZYOptions" :key="index" :label="item.ZYMC"
                         :value="item.PJZYID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="备注：" prop="BZ">
            <el-input v-model="formData.BZ" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px">
        汇总明细
      </div>
      <div style="margin-top: 20px">
        <div style="text-align: right;padding-bottom: 10px">
          <el-button ref="button91277" @click="getHZMXList" type="primary" v-if="editable">汇总计算</el-button>
        </div>
        <el-table ref="datatable91634" :data="formData.HZMXList" height="calc(300px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
          <el-table-column prop="CBSDWQC" label="承包商名称" align="left" header-align="center" :show-overflow-tooltip="true" min-width="160"></el-table-column>
          <el-table-column prop="DWMC" label="队伍名称" align="left" header-align="center" :show-overflow-tooltip="true" min-width="160"></el-table-column>
<!--          <el-table-column prop="PJLX" label="业务主管部门" align="center"-->
<!--                           :show-overflow-tooltip="true" width="160"></el-table-column>-->
          <el-table-column prop="DF1" label="建设单位评价得分（20%）" align="center" :show-overflow-tooltip="true" width="200"></el-table-column>

          <el-table-column prop="DF2" label="专业部门评价得分（40%）" align="center" :show-overflow-tooltip="true" width="200"></el-table-column>
          <el-table-column prop="DF3" label="安全部门评价得分（40%）" align="center" :show-overflow-tooltip="true" width="200"></el-table-column>
          <el-table-column prop="HJDF" label="季度考核得分" align="center" :show-overflow-tooltip="true" width="160"></el-table-column>
          <el-table-column prop="PM" label="季度排名" align="center" :show-overflow-tooltip="true" width="160"></el-table-column>
          <el-table-column prop="PJJG" label="考核结果" align="center" :show-overflow-tooltip="true" width="160"></el-table-column>
          <el-table-column prop="SGXM" label="施工项目" align="left" header-align="center" :show-overflow-tooltip="true" width="260"></el-table-column>
         <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
           <template #default="scope">
             <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看明细
             </el-button>
              <el-button size="small" class="lui-table-button" type="primary" v-if="editable" @click="deleteRow(scope.row)">删除
             </el-button>
           </template>
         </el-table-column>

        </el-table>
      </div>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="考核明细上报"
        z-index="1000"
        top="5vh"
        width="90%">
      <div>
        <khmxView v-if="dialogVisible" :params="params" @close="dialogVisible = false"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import khmxView from "@views/khpj/khhzfb/khmxView";


export default defineComponent({
  name: '',
  components: {khmxView},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      KHHZID: props.params.id,
      operation: props.params.operation,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        PJLX: 'ZQXKHHZ',
        HZMXList: []
      },
      rules: {
        PJZQ: [{
          required: true,
          message: '字段值不可为空',
        }],
        PJZYBM: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      KHSJOptions: [],
      KHZYOptions: [],

      params: {},
      dialogVisible: false,
    })


    const getFormData = () => {
      let params = {
        KHHZID: state.KHHZID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/khhzfb/selectHzfbById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }


    const saveData = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }
    }

    const submitForm = (type) => {
      let params = {
        ...state.formData,
        KHHZID: state.KHHZID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }

      if (type === 'submit') {
        params.SHZT = '1'
      }
      console.log(params)
      state.loading = true
      axiosUtil.post('/backend/sckhpj/khhzfb/saveKhhzfbForm', params).then(res => {
        ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading = false
      })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const viewRow = (row) => {
      console.log(row)
      state.params={
        editable: false, KHHZID: state.KHHZID,DWWYBS:row.DWWYBS, operation: 'view'
      }
      state.dialogVisible = true
    }

    const deleteRow = (row) => {
      ElMessageBox.confirm('你确定删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.del('/backend/sckhpj/khhzfb/delHzmx?KHHZMXID='+row.KHHZMXID, null).then(res => {
          ElMessage.success({
            message: '删除成功!'
          });
          getFormData()
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const getHZMXList = () => {
      if(!state.formData.PJZQ){
        ElMessage.warning('请选择考核时间')
        return
      }
      if(!state.formData.PJZYBM){
        ElMessage.warning('请选择考核专业')
        return
      }
      let params={
        ...state.formData,
        KHHZID: state.KHHZID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      axiosUtil.post('/backend/sckhpj/khhzfb/selectHzmxList',params).then(res=>{
        state.formData.HZMXList = res.data || []
        ElMessage.success('计算成功')
        getFormData()
      })
    }


    const changeKhzy = (value) => {
      if (value) {
        let zyqk = state.KHZYOptions.find(item => item.PJZYID === value)
        state.formData.PJZYMC = zyqk.ZYMC
        getKhhzList();
      } else {
        state.formData.HZMXList = []
      }
    }

    const getKhhzList = () => {
      if (state.formData.PJZQ && state.formData.PJZYBM) {
        //根据专业周期、专业id、部门id查询考核
        let params = {
          KHHZID: state.formData.PJZQ,
          PJZYBM: state.formData.PJZYBM,
        }
        axiosUtil.get('/backend/sckhpj/khmxtb/selectKhhzByParams', params).then((res) => {
          if(res.data.length>0){
              state.formData.PJZYMC='';
              state.formData.PJZYBM='';
              state.formData.PJZQ='';
              ElMessage.error('该考核填报已存在，请重新选择考核周期和专业！'); 
          }
        })
      } else {
        state.formData.KMKHHZList = []
      }
    }

    const getKhsjList = () => {
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhqjList', null).then(res => {
        state.KHSJOptions = res.data || []
      })
    }

    const getKhzyList = () => {
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhzyList', null).then(res => {
        state.KHZYOptions = res.data || []
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      }
      getKhsjList()
      getKhzyList()
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm,
      changeKhzy,
      getHZMXList,
      viewRow,
      deleteRow

    }
  }

})
</script>

<style scoped>

</style>
