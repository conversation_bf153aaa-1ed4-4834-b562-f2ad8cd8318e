<template>
  <div class="Word">
    <div style="height: 800px; width: auto" v-html="poHtmlCode" />
  </div>
</template>

<script>
import axios from "axios";
export default {
  name: "Word",
  data() {
    return {
      poHtmlCode: "",
      ID: "",
    };
  },
  created: function () {
    this.ID = this.$route.query.ID;
    //由于vue中的axios拦截器给请求加token都得是ajax请求，所以这里必须是axios方式去请求后台打开文件的controller
    axios
      .post("/api/WordSalaryBill/Word?ID=" + this.ID)
      .then((response) => {
        this.poHtmlCode = response.data;
      })
      .catch(function (err) {
        console.log(err);
      });
  },
  methods: {
    //控件中的一些常用方法都在这里调用，比如保存，打印等等
    Close() {
      window.external.close();
    },
    OnPageOfficeCtrlInit() {
      // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
      pageofficectrl.AddCustomToolButton("关闭", "Close", 21);
    },
  },
  mounted: function () {
    // 将vue中的方法赋值给window
	 window.Close = this.Close;

    // 以下的为PageOffice事件的回调函数，名称不能改，否则PageOffice控件调用不到
    window.OnPageOfficeCtrlInit = this.OnPageOfficeCtrlInit;
  },
};
</script>

