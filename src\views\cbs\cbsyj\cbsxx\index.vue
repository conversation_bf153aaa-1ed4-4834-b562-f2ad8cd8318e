<!-- 承包商基本信息 -->
<template>
    <div id="auditDiv" class="container" v-loading="loading">
        <el-tabs v-model="activeName" type="card" :before-leave="handleClick" style="height: calc(100% - 50px)">
            <el-tab-pane v-for="(item, index) in compList" :key="index" :label="item.SJMBMC" :name="item.SJMBBM">

                <div class="tab-pane-content">
                    <component 
                        :ref="(el) => setRefMap(el, item.SJMBBM)" 
                        :is="ComponentDic[item.SJMBBM]"
                        :defaultData="saveForm[item.SJMBBM]" 
                        :row="saveForm[item.SJMBBM]" 
                        :DWYWID="uuId || DWYWID"
                        :TYXYDM="saveForm?.JBXX?.TYXYDM || TYXYDM" 
                        :CBSDWQC="saveForm?.JBXX?.CBSDWQC || CBSDWQC"
                        :editable="editable" 
                        :resultTableData="resultTableData?.[item.SJMBBM]" 
                        :params="saveForm">
                    </component>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div style="text-align: center" v-if="editable && !value">
            <el-button type="primary" :disabled="confirmLoading" @click="handleSave('0')">保存</el-button>
            <el-button type="success" v-if="from == 'YWBMBA'" :disabled="confirmLoading"
                @click="chooseBlr('1')">提交</el-button>
            <el-button type="success" v-else :disabled="confirmLoading" @click="submitProcess('1')">提交</el-button>
            <!-- <el-button type="success" @click="testValidate">测试</el-button> -->
        </div>
    </div>

</template>

<script setup>
import {
    reactive,
    ref,
    defineAsyncComponent,
    useAttrs,
    onMounted,
    watch,
    onUnmounted,
} from "vue";
import {
    getCbsyjGetTabShow,
    getCbsyjGetTabData,
    getCbsyjGetCbsjbxx,
    getTeamInfo,
    postCbsyjSaveCbsxx,
    getCbsyjGetTeamResultInfo,//查询队伍结果信息
    getCbsyjGetCbsParams
} from "@src/api/sccbsgl.js";
import { getCommonSelectDMB } from "@src/api/common.js";
import { ElMessage, ElLoading } from "element-plus";
import tabFun from "@src/lib/tabFun";
import { auth, mixin } from "@src/assets/core/index";
import { vsuiapi } from "@vsui/vue-multiplex";
import comFun from "@src/lib/comFun";
import tycList from "../../tyc/tycList.vue";
import api from "@src/api/lc";//流程接口
import axios from "axios";
import axiosUtil from "../../../../lib/axiosUtil";
import vsAuth from '../../../../lib/vsAuth'
import { v4 as uuidv4 } from "uuid";
import TabFun from "@lib/tabFun";


const { vsuiRoute, vsuiEventbus } = mixin();

const props = defineProps({
    YWLXDM: {
        type: String,
        default: "",
    },
});

const emit = defineEmits(['update:value'])

const TYXYDM = ref(vsAuth.getAuthInfo().permission.orgMap.orgnaCode.split("-")[0])
const DWYWID = ref(uuidv4().replace(/-/g, ""))
const CBSDWQC = ref(vsAuth.getAuthInfo().permission.userName)
const attrs = useAttrs();
const value = ref(null);//流程框架页传过来的流程参数
const params = ref(null);//流程框架页传过来的业务参数
const uuId = ref(""); //业务ID
const YWLXDM = ref(props.YWLXDM)
console.log("==============",YWLXDM.value)
const editable = ref(true); // 是否查看
const fxxxsl = ref(0)
const isVIewJgxx = ref(false); // 是否查询正式数据
const from = ref(null);//页面来源哪里
const YTZYBMLDOptions = ref([]);
const showDialog = ref(false);
const nextPerformer = ref([]);
from.value = attrs.from;
value.value = attrs.value;
params.value = attrs.params;
isVIewJgxx.value = attrs.isVIewJgxx; // 是否查询正式数据



// 是否展示历史操作
const ifShowLscz = ref(false);
ifShowLscz.value = attrs.ifShowLscz;
const refresh = (attr) => {
    uuId.value = attr.uuId;
    editable.value = attr.editable;
    console.log(uuId.value, attr);
    getTabsDefaultData();
};
onMounted(() => {
    vsuiEventbus.on("reloadCbsjbxx", refresh);
});
onUnmounted(() => {
    vsuiEventbus.off("reloadCbsjbxx", refresh);
});
// const uuId = '3a99591c9e824909ad544846e84ce8de'
// const MBID = '97CE94A1696C4AC78E680C0DCAFF6E13'
// const MBLX = 'QY'
// const ZYFLDM = 'KJWBXZ01'

let cbsjbxx = defineAsyncComponent(() => import("../../../cbs/cbsyj/cbsxx/cbsjbxx/index.vue"));
let zzxx = defineAsyncComponent(() => import("../../../cbs/cbsyj/cbsxx/zzxx/index.vue"));
let yjxx = defineAsyncComponent(() => import("../../../cbs/cbsyj/cbsxx/yjxx/index.vue"));
let bwtrxx = defineAsyncComponent(() => import("../../../cbs/cbsyj/cbsxx/bwtrxx/bwtrxx.vue"));
let ryxx = defineAsyncComponent(() => import("../../../cbs/cbsyj/cbsxx/ryxx/ryxx.vue"));
let sbxx = defineAsyncComponent(() => import("../../../cbs/cbsyj/cbsxx/sbxx/sbxx.vue"));


const ComponentDic = ref({
    JBXX: cbsjbxx,
    ZZXX: zzxx,
    RYXX: ryxx,
    SBXX: sbxx,
    CBSYJ: yjxx,
    BWTRXX: bwtrxx,
});
const refMap = ref({});
const setRefMap = (el, name) => {
    refMap.value[name] = el
}
// 获取所有子组件的值
const getAllChildValues = () => {
    return Object.entries(refMap.value).reduce((acc, [key, comp]) => {
        acc[key] = comp.form; 
        return acc;
    }, {});
};

const getBlrOptions = (optionName, ROLE) => {
    let params = {
        ROLE: ROLE,
        businessId: value.businessId || uuId.value
    }
    axiosUtil.get('/backend/cbsxx/common/getUserZyglbmByCbsKey', params).then(res => {
        YTZYBMLDOptions.value = res.data || []
    })
}
const chooseBlr = async () => {
    console.log("提交chooseBlr...")
    let saveFlag = await handleSave('1');
    if (!saveFlag) {
        return;
    }
    getBlrOptions('YTZYBMLDOptions', 'CBSGL_YTZYGLBM');
    showDialog.value = true;
}

/* 校验各tab必填项 */
const validateTemplate = async () => {
    return new Promise((resolve, reject) => {
        Promise.all(Object.values(refMap.value).filter(i => i.validateForm).map(i => i.validateForm())).then((result) => {
            ElMessage.success('校验成功')
            resolve(true)
        }).catch((err) => {
            console.log("error======", err);
            console.log(Object.values(err))
            ElMessage.error(Object.values(err)[0]?.[0]?.message)
            resolve(false)
        })
    })

}
let activeName = ref("JBXX");
let compList = ref([
    { SJMBMC: "承包商基本信息", SJMBBM: "JBXX" },
    { SJMBMC: "资质信息", SJMBBM: "ZZXX" },
    { SJMBMC: "业绩信息", SJMBBM: "CBSYJ" },
    { SJMBMC: "被委托人信息", SJMBBM: "BWTRXX" },
    { SJMBMC: "人员信息", SJMBBM: "RYXX" },
    { SJMBMC: "设备信息", SJMBBM: "SBXX" },
    /*
    资质信息
    许可信息
    体系证书
    人员信息
      */
]);

//根据传过来的参数查询系统所需参数
const getParamsData = () => {
    console.log("getParamsData")
    console.log(attrs.uuId)
    if (attrs.uuId) {//直接传值
        uuId.value = attrs.uuId;
        editable.value = attrs.editable;
        getTabsDefaultData();
        if (YWLXDM.value == "BG") {
            getTeamResultInfo()
        }
    }
};
// 查询tab基本信息
const tabDefaultData = ref([]);
// 结果表数据
const resultTableData = ref(null);
const getTeamResultInfo = () => {
    if (!JGDWYWID.value) return;
    getCbsyjGetTeamResultInfo({
        DWYWID: JGDWYWID.value
    }).then(({ data }) => {
        if (!data) return;
        resultTableData.value = data;
    }).catch((err) => {
        ElMessage.warning('查询结果表数据失败')
    });
}
const loading = ref(false);
const getTabsDefaultData = () => {
    console.log("getTabsDefaultData")
    console.log(uuId.value);
    if (/* !ZYFLDM.value ||  !MBLX.value ||*/ !uuId.value /*|| !MBID.value || !YWLXDM.value*/) return;
    loading.value = true;
    Promise.all([
        // 查看时调用结果表数据，准入、变更时调用过程表数据
        getTeamInfo({
            DWYWID: uuId.value,
        }),
    ])
        .then(([{ data: result }]) => {
            console.log("tab基本数据", result);
            const obj = {
                BWTRXX: [],
                RYXX: [],
                JBXX: [],
                CBSYJ: [],
                ZZXX: [],
                SBXX: []
            }
            Object.entries(result || {}).forEach(([key, value]) => {
                if (value) {
                    //排除null
                    if (Array.isArray(value)) {
                        //list的话且不为空数组的时赋值
                        if (value.length || key === 'CBSYJ') obj[key] = value;
                    } else {
                        obj[key] = value;
                    }
                }
            });

            saveForm.value = obj;
            console.log('asdfafafasdfasfasdfasf', saveForm.value)
            if (!saveForm.value.JBXX.CBSDWQC && from.value != 'YWBMBA') {//特殊准入由二级单位发起，不携带组织机构名称和账号信息
                const userinfo = auth.getPermission();
                saveForm.value.JBXX.CBSDWQC = userinfo.orgnaName;
                axiosUtil.get('/backend/common/selectOrganByUserId', { USER_ID: userinfo.userId }).then(res => {
                    saveForm.value.JBXX.TYXYDM = res.data[0]?.ORGNA_CODE.split('-')[0]
                })
            }
            //compList.value.push({SJMBMC: "企业风险信息", SJMBBM: "QYFXXX"})
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            loading.value = false;
        });
};
const statusDic = ref({});
/**查询服务区域 */
const queryStatusDic = () => {
    getCommonSelectDMB({ DMLBID: 'YWLXDM' }).then(({ data }) => {
        console.log('data', data);
        if (data) {
            statusDic.value = data.reduce((t, i) => {
                t[i.DMXX] = i.DMMC;
                return t
            }, {})
        }
    })
}
onMounted(() => {
    queryStatusDic()
})
const saveForm = ref({});
const confirmLoading = ref(false);
const handleSave = async (status) => {
    loading.value = true;
    var flag = false;
    // 提交时添加校验
    if (status == '1') {
        const validResult = await validateTemplate();
        if (!validResult) {
            loading.value = false;
            return false;
        }
    }
    console.log("auth", auth);
    // 设置登记人基本信息
    const userinfo = auth.getPermission();
    const feildList = {
        CJRZH: userinfo.userLoginName,
        CJRXM: userinfo.userName,
        CJDWID: userinfo.orgnaId,
        CJSJ: comFun.getNowTime()
    };
    const updatefeildList = {
        XGRZH: userinfo.userLoginName,
        XGSJ: comFun.getNowTime()
    };
    // 给无默认值数据添加默认数据
    const setDafaultValue = (data) => {
        if (YWLXDM.value == 'CQBA' || YWLXDM.value == 'GKZB' || YWLXDM.value == 'XMBA') {
            Object.entries(feildList).forEach(([key, value]) => {
                if (!data[key]) {
                    data[key] = value;
                }
            });
        } else {
            //其他类型增加更新人信息
            Object.entries(updatefeildList).forEach(([key, value]) => {
                if (!data[key]) {
                    data[key] = value;
                }
            });
        }
    };

    let CBSYWID = DWYWID.value;
    if(YWLXDM.value == 'CQBA' || YWLXDM.value == 'GKZB' || YWLXDM.value == 'XMBA'){
        // 获取子组件传来的数据
        saveForm.value = getAllChildValues()
        saveForm.value.BWTRXX = refMap.value.BWTRXX.formData
        saveForm.value.CBSYJ = refMap.value.CBSYJ.tableData
    }


    console.log("saveForm==",saveForm.value)
    Object.entries(saveForm.value).forEach(([key, value]) => {
        if (value) {
            //排除null
            if (Array.isArray(value)) {
                //list的话且不为空数组的时赋值
                value.forEach((x) => {
                    x.DWYWID = uuId.value || CBSYWID;
                    if(!status){
                        x.SHZT = status
                    }
                    setDafaultValue(x);
                });
            } else if (value instanceof Object) {
                setDafaultValue(value);
            }
        }
    });

        saveForm.value.ZZXX?.forEach((x) => {
            x.ZSDLDM = "ZZZS";
            x.ZSCYZBS = x.ZSYWID
            x.SHZT = status
        });
        saveForm.value.RYXX?.forEach(x => {
            if (x.ZSXX) {
                x.ZSXX.forEach(v => {
                    v.DWYWID = uuId.value || CBSYWID
                    setDafaultValue(v);
                })
            }
            x.SHZT = status
        })
        saveForm.value.CBSYJ?.forEach((x) => {
            x.SHZT = status
        });
        saveForm.value.SBXX?.forEach((x) => {
            x.SHZT = status
        });

        saveForm.value.JBXX.CBSYWID = uuId.value || CBSYWID
        saveForm.value.JBXX.CBSWYBS = uuId.value || CBSYWID //承包商唯一标识
        if(YWLXDM.value) {
            saveForm.value.JBXX.YWLXDM = YWLXDM.value; //给承包商基本信息设置业务类型代码
        }
        
        saveForm.value.JBXX.SHZT = status;

        let form = {
            cbsjbxx: saveForm.value.JBXX,
            cbszs:saveForm.value.ZZXX,
            bwtrxx:saveForm.value.BWTRXX,
            cbsyj:saveForm.value.CBSYJ,
            cbsdwcy:saveForm.value.RYXX,
            cbsdwsb:saveForm.value.SBXX,
            bczt: status || "", //"0保存；1提交；2审核通过；"
            DWYWID: uuId.value || CBSYWID,
        }
        console.log("form :::::",form)
        confirmLoading.value = true;
        let res
        try {
            res = await axiosUtil.post('/backend/sccbsgl/cbsxxgl/savecbsjbxx', form);
        } catch (e) {
            loading.value = false;
            confirmLoading.value = false;
        }
        if (res.code === 1) {
            flag = true;
            if (status == "0") {
            ElMessage.success("保存成功!")
            } else {
            ElMessage.success("提交成功!")
            }
            TabFun.closeNowTab();
            TabFun.addTabByRoutePath(
                `承包商业务导航`,
                "/contractors/cbsywdhPage",
                {},
                {}
            );
        } else {
            flag = false;
            ElMessage({
                type: "error",
                message: res.message,
            });
        }
        confirmLoading.value = false;
        loading.value = false;
        return flag;
};

//提交流程const saveData = (val) => {
const submitProcess = async (status) => {
    await handleSave('1');
}

onMounted(() => {
    getParamsData();
    if (ifShowLscz.value) {
        initCbsLsczList();
    }
    console.log('流程变量', value)
    console.log('业务变量', params)
    // getTabShow();
});
let handleClick = (tab, event) => {
    if (!TYXYDM.value && !saveForm.value?.JBXX?.TYXYDM) {
        ElMessage.warning('请先维护统一信用代码')
        return false
    }
};


// 初始化数据
const tableData = ref([]);
const initCbsLsczList = () => {
    axiosUtil.get('/backend/sccbsgl/report/queryCbsLsczList', { dwywid: uuId.value }).then((res) => {
        tableData.value = res.data.rows
    });
}




</script>

<style scoped>
.container {
    height: calc(100vh - 100px);
    background-color: #fff;
    overflow-y: auto;
}

.container .el-tabs {
    height: 100%;
    margin: 0px 40px 10px 40px;
}

.container .el-tabs ::v-deep .el-tabs__header {
    margin-bottom: 0;
}

.container .el-tabs ::v-deep .el-tabs__content {
    height: calc(100% - 60px);
    border: 2px solid #dfdfdf;
}

.tab-pane-content {
    height: 100%;
}

:deep(.el-table .el-button) {
    padding: 5px;
}

:deep(.el-table .el-button + .el-button) {
    margin-left: 5px;
}

::v-deep .divider .el-divider__text {
    color: #5f80c7;
    font-weight: bold;
}
</style>
