<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
      label-width="160px" size="default" v-loading="loading" @submit.prevent>
      <el-collapse v-model="openCollapse">
        <el-row :gutter="0" class="grid-row">
          <el-col :span="24" class="grid-cell">
            <el-form-item label="会议名称：" prop="HYMC">
              <div style="margin-left: 10px">{{ formData.HYMC }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="会议时间：" prop="KBSJ">
              <div style="margin-left: 10px">{{ formData.KBSJ }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="16" class="grid-cell">
            <el-form-item label="会议地点：" prop="PBDD">
              <div style="margin-left: 10px">{{ formData.PBDD }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell no-border-bottom">
            <el-form-item label="项目信息：" prop="DPSXMList">
              <el-table :data="formData.DPSXMList" height="120px" border :show-summary="false" size="default" stripe
                :highlight-current-row="true" :cell-style="{ padding: '3px 0 ' }" class="lui-table">
                <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                <el-table-column prop="XMMC" label="项目名称" align="center" :show-overflow-tooltip="true"
                  min-width="160"></el-table-column>
                <el-table-column prop="XMBH" label="项目编号" align="center" :show-overflow-tooltip="true"
                  width="160"></el-table-column>
                <el-table-column prop="SSDWMC" label="所属单位" align="center" :show-overflow-tooltip="true"
                  min-width="160"></el-table-column>
                <el-table-column prop="BDSL" label="标段数量" align="center" :show-overflow-tooltip="true"
                  width="100"></el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-collapse-item title="抽取规则" name="2"
          v-if="!value || !value.activityId || ['1', 'new'].includes(value.activityId)">
          <el-row :gutter="0" class="grid-row">
            <el-col :span="24" class="grid-cell no-border-bottom">
              <el-form-item label=" 随机抽取条件：" label-width="120px" prop="SM">
                <div style="display: flex;width: calc(100% - 0px);gap: 10px">
                  <el-table :data="formData.CQTJForm.CQTJMXList" height="210px" :border="true" :show-summary="false"
                    size="default" :stripe="false" :highlight-current-row="true" class="lui-table"
                    :cell-style="{ padding: '0px 0 ' }">
                    <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
                    <el-table-column prop="KEY" label="条件" align="center" min-width="100">
                      <template #default="scope">
                        <el-select v-model="scope.row.KEY" placeholder="请选择" clearable :disabled="!editable"
                          @change="changeCQTJ(scope.row)">
                          <el-option v-for="item in CQTJOptions" :key="item.DMXX" :label="item.DMMC" :value="item.DMXX"
                            :disabled="disabledTj(item.DMXX)">
                          </el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column prop="LOIGC" label="逻辑" align="center" width="80" />
                    <el-table-column prop="VALUE" label="条件值" align="center" min-width="100">
                      <template #default="scope">
                        <el-cascader clearable :disabled="!editable" v-model="scope.row.VALUE" :options="XCSZYOptions"
                          v-if="scope.row.KEY==='JSZYFW'" :show-all-levels="false" collapse-tags
                          :props="{expandTrigger: 'hover',label:'ZYMC',value:'ZYBM',emitPath:false,multiple: true,}" />
                        <el-select v-model="scope.row.VALUE" v-else-if="scope.row.KEY==='ZJQY'" class="full-width-input"
                          :disabled="!editable" clearable multiple collapse-tags>
                          <el-option v-for="(item, index) in ZJQYOptions" :key="index" :label="item.DMMC"
                            :value="item.DMXX" :disabled="item.disabled"></el-option>
                        </el-select>

                        <el-input v-model="scope.row.VALUE" placeholder="请输入" clearable v-else :disabled="!editable"
                          @input="scope.row.VALUE=scope.row.VALUE.replace(/[^\d]/g,'')">
                        </el-input>

                      </template>
                    </el-table-column>
                    <!-- <el-table-column prop="CZ" label="操作" align="center" width="90" v-if="editable">
                      <template #default="scope">
                        <el-button size="small" class="lui-table-button" type="primary"
                                   @click="delTjData(scope.$index)">删除
                        </el-button>
                      </template>
                    </el-table-column> -->
                  </el-table>
                  <!-- <el-button type="primary" :icon="Plus" @click="addCQTJ()"
                             v-if="editable"/> -->
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item title="专家抽取" name="3">
          <el-row :gutter="0" class="grid-row">
            <el-col :span="16" class="grid-cell ">
              <el-form-item label="提示信息：" prop="TSXX" label-width="120px">
                <div style="margin-left: 10px">{{
                  `总人数为${getZRS()}人，其中招标人代表${ZBRDBList.length}人；
                  抽取专家${formData.CQZJList.length}人，
                  指定专家${formData.ZDZJList.length}人、
                  确定出席${formData.ZDZJList.filter(zdjz=> zdjz.SFQRCX == '1').length + formData.CQZJList.filter(zdjz=>
                  zdjz.SFQRCX == '1').length}人。`
                  }}
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
              <div style="margin-left: 10px;display: flex;align-items: center;height: 100%;margin-right: 10px">
                <el-button type="primary" @click="randomZjxx" :loading="randoming" v-if="editable">随机抽取</el-button>
                <el-button type="primary" @click="openZJXZDialog" v-if="editable">指定评委</el-button>
                <el-button type="warning" v-print="'#printMe'">打印</el-button>
                <!--                <el-button type="primary" @click="" style="margin-left: auto">查看回执</el-button>-->
              </div>
            </el-col>

            <el-col :span="8" class="grid-cell" v-if="XMLX==='ZB'">
              <el-form-item label=" 招标人代表：" label-width="120px" prop="ZBRDB">
                <el-input v-model="formData.ZBRDB" type="text" placeholder="请输入" clearable :disabled="true">
                  <template #append v-if="editable">
                    <el-button @click="openCheckRy('ZBRDBList')">选择</el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label=" 监督人：" label-width="100px" prop="JDRY">
                <el-input v-model="formData.JDRY" type="text" placeholder="请输入" clearable :disabled="true">
                  <template #append v-if="editable">
                    <el-button @click="openCheckRy('JDRList')">选择</el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label=" 招标工作人员：" label-width="130px" prop="ZBGZRY">
                <el-input v-model="formData.ZBGZRY" type="text" placeholder="请输入" clearable :disabled="true">
                  <template #append v-if="editable">
                    <el-button @click="openCheckRy('ZBGZRYList')">选择</el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <div style="margin-top: 10px">
            <el-radio-group v-model="ZJLBLX" fill="#409EFF">
              <el-radio-button label="CQ">抽取专家列表</el-radio-button>
              <el-radio-button label="ZD">指定专家列表</el-radio-button>
            </el-radio-group>
          </div>
          <div style="margin-top: 5px">
            <el-table :data="formData.CQZJList" height="260px" :border="true" :show-summary="false" size="default"
              :stripe="false" :highlight-current-row="true" v-show="ZJLBLX==='CQ'" :cell-style="{ padding: '0px 0 ' }"
              class="lui-table">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
              <el-table-column prop="ZJBH" label="专家编号" align="center" width="200"></el-table-column>
              <el-table-column prop="SJH" label="手机号" align="center" min-width="100"></el-table-column>
              <!--              <el-table-column prop="CSZYMC" label="专业" align="center" min-width="100"></el-table-column>-->
              <el-table-column prop="ZJLBMC" label="专家类别" align="center" min-width="100"></el-table-column>
              <el-table-column prop="SFQRCX" label="能否出席" align="center" min-width="100">
                <template #default="{row,$index}">
                  <el-form-item :prop="`CQZJList.${$index}.SFQRCX`" label-width="0" :rules="rules.SFQRCX"
                    style="margin-bottom: 0">
                    <el-select v-model="row.SFQRCX" placeholder="请选择" :disabled="!editable" clearable>
                      <el-option v-for="item in NFCXOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="BCXYY" label="原因说明" align="center" min-width="100">
                <template #default="{row,$index}">
                  <el-form-item :prop="`CQZJList.${$index}.BCXYY`" label-width="0" style="margin-bottom: 0">
                    <el-select v-model="row.BCXYY" class="full-width-input" clearable :disabled="!editable">
                      <el-option v-for="(item, index) in BCXYYOptions" :key="index" :label="item.DMMC"
                        :value="item.DMXX"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>

            <el-table :data="formData.ZDZJList" height="260px" :border="true" :show-summary="false" size="default"
              :stripe="false" :highlight-current-row="true" v-show="ZJLBLX==='ZD'" :cell-style="{ padding: '0px 0 ' }"
              class="lui-table">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
              <el-table-column prop="XM" label="专家姓名" align="center" width="120"></el-table-column>
              <el-table-column prop="SJH" label="手机号" align="center" width="180"></el-table-column>
              <!--              <el-table-column prop="CSZYMC" label="专业" align="center" min-width="100"></el-table-column>-->
              <el-table-column prop="ZJLBMC" label="专家类别" align="center" min-width="100"></el-table-column>
              <el-table-column prop="ZJLXMC" label="专家类型" align="center" min-width="100"></el-table-column>
              <el-table-column prop="SFQRCX" label="能否出席" align="center" min-width="100">
                <template #default="{row,$index}">
                  <el-form-item :prop="`ZDZJList.${$index}.SFQRCX`" label-width="0" :rules="rules.SFQRCX"
                    style="margin-bottom: 0">
                    <el-select v-model="row.SFQRCX" placeholder="请选择" :disabled="!editable" clearable>
                      <el-option v-for="item in NFCXOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="BCXYY" label="原因说明" align="center" min-width="100">
                <template #default="{row,$index}">
                  <el-form-item :prop="`ZDZJList.${$index}.BCXYY`" label-width="0" style="margin-bottom: 0">
                    <el-select v-model="row.BCXYY" class="full-width-input" clearable :disabled="!editable">
                      <el-option v-for="(item, index) in BCXYYOptions" :key="index" :label="item.DMMC"
                        :value="item.DMXX"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" min-width="100" v-if="editable">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary"
                    @click="delZjcqData('ZDZJList',scope.$index)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-row :gutter="0" class="grid-row" v-show="formData.ZDZJList.length>0">
              <el-col :span="16" class="grid-cell ">
                <el-form-item label="指定专家原因：" prop="ZDZJYY">
                  <el-input v-model="formData.ZDZJYY" type="text" placeholder="请输入" clearable :disabled="!editable">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>
      </el-collapse>
      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex" v-if="!value.activityId">
        <el-button type="success" @click="saveForm('save')" v-if="editable">暂存</el-button>
        <el-button type="info" @click="randomZjxx" v-if="editable">继续抽取</el-button>
        <el-button type="primary" @click="saveForm('submit')" v-if="editable">抽取完成</el-button>
        <!-- <el-button type="primary" @click="saveForm('submit')" v-if="editable">提交</el-button> -->
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

    <!-- *****************************  打印区域  *********************************** -->
    <div id="printMe">
      <div class="hidden-for-screen">
        <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
          label-width="160px" size="default" v-loading="loading" @submit.prevent>
          <el-collapse v-model="openPrintCollapse">
            <el-collapse-item title="专家抽取" name="666">
              <el-row :gutter="0" class="grid-row">
                <el-col :span="24" class="grid-cell ">
                  <el-form-item label="提示信息：" prop="TSXX" label-width="120px">
                    <div style="margin-left: 10px">{{
                      `总人数为${getZRS()}人，其中招标人代表${ZBRDBList.length}人；
                      抽取专家${formData.CQZJList.length}人，
                      指定专家${formData.ZDZJList.length}人、
                      确定出席${formData.ZDZJList.filter(zdjz => zdjz.SFQRCX == '1').length + formData.CQZJList.filter(zdjz =>
                        zdjz.SFQRCX == '1').length}人。`
                      }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="grid-cell" v-if="XMLX==='ZB'">
                  <el-form-item label=" 招标人代表：" label-width="120px">
                    <el-input v-model="formData.ZBRDB" type="text">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="grid-cell">
                  <el-form-item label=" 监督人：" label-width="100px">
                    <el-input v-model="formData.JDRY" type="text">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="grid-cell">
                  <el-form-item label=" 招标工作人员：" label-width="130px">
                    <el-input v-model="formData.ZBGZRY" type="text">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <div style="margin-top: 5px">
                <div>抽取专家列表</div>
                <el-table :data="formData.CQZJList" :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{ padding: '0px 0 ' }" class="lui-table">
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="ZJBH" label="专家编号" align="center" width="200"></el-table-column>
                  <el-table-column prop="SJH" label="手机号" align="center" width="200"></el-table-column>
                  <el-table-column prop="ZJLBMC" label="专家类别" align="center" width="100"></el-table-column>
                  <el-table-column prop="SFQRCX" label="能否出席" align="center" width="100">
                    <template #default="{ row }">
                      <span>{{ row.SFQRCX == '1' ? '是' : '否' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="BCXYY" label="原因说明" align="left" header-align="center" min-width="400">
                  </el-table-column>
                </el-table>
                <div>指定专家列表</div>
                <el-table :data="formData.ZDZJList" :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{ padding: '0px 0 ' }" class="lui-table">
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="XM" label="专家姓名" align="center" width="120"></el-table-column>
                  <el-table-column prop="SJH" label="手机号" align="center" width="180"></el-table-column>
                  <el-table-column prop="ZJLBMC" label="专家类别" align="center" width="100"></el-table-column>
                  <el-table-column prop="ZJLXMC" label="专家类型" align="center" width="100"></el-table-column>
                  <el-table-column prop="SFQRCX" label="能否出席" align="center" width="100">
                    <template #default="{ row }">
                      <span>{{ row.SFQRCX == '1' ? '是' : '否' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="BCXYY" label="原因说明" align="left" header-align="center"
                    min-width="400"></el-table-column>
                </el-table>

                <el-row :gutter="0" class="grid-row" v-show="formData.ZDZJList.length > 0">
                  <el-col :span="16" class="grid-cell ">
                    <el-form-item label="指定专家原因：" prop="ZDZJYY">
                      <el-input v-model="formData.ZDZJYY" type="text" placeholder="">
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </div>
    </div>


    <el-dialog v-if="dialogRYVisible" v-model="dialogRYVisible" title="人员选择" top="2vh" width="900px">
      <div>
        <ryxz v-if="dialogRYVisible" :queryParams="{userOrgToTwo: userInfo.orgnaId}" @getData="getRyRes"></ryxz>
      </div>
    </el-dialog>

    <el-dialog v-if="dialogZJVisible" v-model="dialogZJVisible" title="专家选择" top="2vh" width="1200px">
      <div>
        <zdzjList @getData="getZjRes" :fitterList="fitterList"></zdzjList>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, getCurrentInstance, onMounted, reactive, toRefs, watch} from "vue";
import vsAuth from "@lib/vsAuth";
import {Plus} from '@element-plus/icons-vue'
import {ElMessage} from "element-plus";
import cgxmxz from "@views/zjgl/pwcq/cgxmxz";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import ryxz from "@views/zjgl/zjcrk/common/ryxz";
import zdzjList from "@views/zjgl/pwcq/zdzjList";
import axios from "axios";
import api from "@src/api/lc";
import tabFun from "@lib/tabFun";

export default defineComponent({
  name: '',
  components: {cgxmxz, ryxz, zdzjList},
  props: {
    params: {
      type: Object,
      required: true
    },
    value: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      PBHYBS: props.params.id,
      openCollapse: ['2', '3'],
      openPrintCollapse: ['666'],
      formData: {
        DPSXMList: [],
        CQTJForm: {
          CQGZBS: comFun.newId(),
          PBHYBS: props.params.id,
          CQTJMXList: [],
        },
        CQZJList: [],
        ZDZJList: [],
        CJR: vsAuth.getAuthInfo().permission.userLoginName,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        HYLX: 'PBHY'

      },
      rules: {
        HYMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        KBSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        PBDD: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZBRDB: [{
          required: true,
          message: '字段值不可为空',
        }],
        JDRY: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZBGZRY: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      ZJLBLX: 'CQ',
      CQTJOptions: [],
      XCSZYOptions: [],
      ZJQYOptions: [],
      BCXYYOptions: [],
      NFCXOptions: [{value: '1', label: '是'}, {value: '0', label: '否'}],//能否出席下拉框

      listName: '',

      ZBRDBList: [],
      JDRList: [],
      ZBGZRYList: [],
      fitterList: [],


      dialogXMVisible: false,
      dialogRYVisible: false,
      dialogZJVisible: false,

      randoming: false,

      XMLX: 'ZB'


    })

    watch(() => state.ZBRDBList, (newVal) => {
      let res = []
      if (newVal) {
        newVal.forEach(item => {
          res.push(item.XM)
        })
      }
      state.formData.ZBRDB = res.join(',')
    });

    watch(() => state.JDRList, (newVal) => {
      let res = []
      if (newVal) {
        newVal.forEach(item => {
          res.push(item.XM)
        })
      }
      state.formData.JDRY = res.join(',')
    });

    watch(() => state.ZBGZRYList, (newVal) => {
      let res = []
      if (newVal) {
        newVal.forEach(item => {
          res.push(item.XM)
        })
      }
      state.formData.ZBGZRY = res.join(',')
    });


    const getFormData = () => {
      let params = {
        PBHYBS: state.PBHYBS
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/zjpbpw/selectPbpwzjById', params).then(res => {
        state.formData = res.data
        if(state.formData.CQZJList.length==0&&state.formData.ZDZJList.length!=0){
             state.ZJLBLX = 'ZD'
        }
        state.formData.CQTJForm.CQTJMXList.forEach(item => {
          if (['JSZYFW','ZJQY'].includes(item.KEY)) {
            item.VALUE = item.VALUE?.split(',') || []
          }
        })

        let ZBRDBList = [];
        let JDRList = [];
        let ZBGZRYList = [];
        state.formData.QTRYList.forEach(item => {
          if (item.SFZBRDB === '1') {
            ZBRDBList.push(item)
          }if (item.SFJDRY === '1') {
            JDRList.push(item)
          }if (item.SFZBGZRY === '1') {
            ZBGZRYList.push(item)
          }
        })

        state.XMLX= state.formData.DPSXMList.find(item=>item.XMLX==='ZB') ? 'ZB' : 'FZB';

        state.ZBRDBList = ZBRDBList;
        state.JDRList = JDRList;
        state.ZBGZRYList = ZBGZRYList;


        state.loading = false
        loadCqtj()
      })
    }

    const submitForm = (type) => {
      return new Promise((resolve,reject) => {
        if (props.value.activityId && !['1', 'new'].includes(props.value.activityId)) {
          resolve(true)
          return
        }


        let params = {
          ...state.formData,
          PBHYBS: state.PBHYBS,
          FSR: vsAuth.getAuthInfo().permission.userName,
          FSRZH: vsAuth.getAuthInfo().permission.userLoginName,
        };

        params.CQTJForm.CQTJMXList = params.CQTJForm.CQTJMXList.map(item => {
          let res = {...item}
          if (res.VALUE instanceof Array) {
            res.VALUE = res.VALUE.join(',')
          }
          return res
        })
        params.QTRYList = [...state.ZBRDBList, ...state.JDRList, ...state.ZBGZRYList];

        if (type === 'submit') {
          /*if (state.formData.ZDZJList.length > 0 && state.XMLX==='ZB') {
            params.SHZT = '1'
          } else {
            params.SHZT = '2'
          }*/
          params.SHZT = '2'
        }else {
          params.SHZT = '0'
        }

        console.log(params)
        state.loading = true
        axiosUtil.post('/backend/xsgl/zjpbpw/savePbpwzjForm', params).then(res => {
          /*if(params.SHZT==='1'){
            if(!props.value.activityId){
              addTask()
            }else{
              resolve(true)
            }
            resolve(true)
          }else {
            ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
            closeForm()
            state.loading = false
          }*/
          ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`);
          closeForm();
          state.loading = false;

        })
      })
    }

    const saveForm = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(res => {
          submitForm(type)
        }).catch(msg => {
          ElMessage.error(msg)
        })
      }
    }

    const saveData = (value) => {
      let type = value === '1' ? 'submit' : 'save'
      return new Promise((resolve, reject) => {
        if (type === 'save') {
          resolve(submitForm(type))
        } else {
          validateForm().then(res => {
            resolve(submitForm(type))
          }).catch(msg => {
            reject(msg)
          })
        }
      })



    }


    const addTask = () => {
      let processName=state.formData.DPSXMList.map(item=>item.XMMC).join(',')+'项目组会-评委抽取审核'
      let _params = {};
      _params.strSystemCode = 'AUTHM_CENTER';
      _params.processId = 'XSGL_PWCQ';
      _params.engineType = 'vs';
      _params.activityId = 'new';
      _params.processInstanceName = processName;
      _params.businessId = state.PBHYBS;
      _params.apprValue = "1";
      _params.apprResult = "提交";
      _params.loginName = state.userInfo.userLoginName;
      _params.userOrgId = state.userInfo.orgnaId;
      axios({
        method: "post",
        url: api.createTask(),
        data: "varJson=" + JSON.stringify(_params),
      }).then((res) => {
        if (res.data && res.data.result == 1) {
          ElMessage({message: '提交成功！', type: 'success',})
          closeForm()
        } else {
          state.loading=false
          ElMessage({message: '提交失败！', type: 'error',})
        }
      }).catch(err=>{
        state.loading=false
        ElMessage({message: '提交失败！', type: 'error',})
      })



    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise((resolve, reject) => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            if(state.formData.ZDZJList.length>0 && !state.formData.ZDZJYY){
              reject('请填写指定专家原因')
            }else {
              resolve(true)
            }
          } else {
            reject('请完善页面信息')
          }
        })
      })
    }

    const validateJbxx = () => {
      return new Promise((resolve, reject) => {
        instance.proxy.$refs['vForm'].validateField(['HYMC', 'KBSJ', 'PBDD'], valid => {
          if (valid) {
            resolve(true)
          } else {
            reject('请完善基础信息')
          }
        })
      })
    }

    const chooseXm = () => {
      state.dialogXMVisible = true
    }


    const delTjData = (index) => {
      state.formData.CQTJForm.CQTJMXList.splice(index, 1)
    }

    const delZjcqData = (listName, index) => {
      state.formData[listName].splice(index, 1)
    }

    const addCQTJ = () => {
      state.formData.CQTJForm.CQTJMXList.push({
        CQGZMXBS: comFun.newId(),
        CQGZBS: state.formData.CQTJForm.CQGZBS,
        KEY: '',
        LOIGC: '',
        VALUE: ''
      })
    }
    // 新增时初始化抽取条件
    const loadCqtj = () => {
      if (state.formData.CQTJForm.CQTJMXList.length == 0) {
        state.CQTJOptions.forEach(val => {
          state.formData.CQTJForm.CQTJMXList.push(
            { "CQGZMXBS": comFun.newId(), "CQGZBS": state.formData.CQTJForm.CQGZBS, "KEY": val.DMXX, "LOIGC": val.BYZD1, "VALUE": null }
          )
        })
      }


    }
    const changeCQTJ = (row) => {
      state.CQTJOptions.forEach(item => {
        if (item.DMXX === row.KEY) {
          row.LOIGC = item.BYZD1
        }
      })
      row.VALUE = null
    }

    const disabledTj = (value) => {
      let res = state.formData.CQTJForm.CQTJMXList.find(item => item.KEY === value)
      if (res) {
        return true
      } else {
        return false
      }
    }


    const getZjRes = (value) => {
      let res = []
      if (value) {
        value.forEach(item => {
          res.push({
            XM: item.XM,
            PBPWBS: comFun.newId(),
            PBHYBS: props.params.id,
            RYBS: item.ZJBS,
            ZJCQFS: 'ZD',
            SJH: item.RKZJBXX.LXDH,
            SZDWMC: item.GZDW,
            ZJLB: item.ZJLB,
            ZJLBMC: item.ZJLBMC,
            ZJLX: item.ZJLX,
            ZJLXMC: item.ZJLX === 'NB' ? '内部' : '外部',
            CSZYMC: item.XCSZYMC,
            SFQRCX: '1',
            CJR: state.userInfo.userLoginName,
            CJSJ: comFun.getNowTime()
          })
        })
      }
      state.formData.ZDZJList.push(...res)
      state.dialogZJVisible = false
      state.ZJLBLX = 'ZD'
    }

    const getRyRes = (value) => {
      let zd = {}
      if (state.listName === 'ZBRDBList') {
        zd = {SFZBRDB: '1'}
      } else if (state.listName === 'JDRList') {
        zd = {SFJDRY: '1'}
      } else if (state.listName === 'ZBGZRYList') {
        zd = {SFZBGZRY: '1'}
      }
      let res = []
      if (value) {
        value.forEach(item => {
          res.push({
            XM: item.USER_NAME,
            PBPWBS: comFun.newId(),
            PBHYBS: props.params.id,
            RYBS: item.USER_ID,
            ZJCQFS: 'ZD',
            SJH: item.USER_MOBILE,
            SZDWMC: item.ORGNA_NAME,
            CJR: state.userInfo.userLoginName,
            CJSJ: comFun.getNowTime(),
            ...zd
          })
        })
      }
      state[state.listName] = res
      state.dialogRYVisible = false
    }

    const openCheckRy = (listName) => {
      state.listName = listName
      state.dialogRYVisible = true
    }

    const openZJXZDialog = () => {
      state.fitterList = [
        ...state.formData.CQZJList.map(item => item.RYBS),
        ...state.formData.ZDZJList.map(item => item.RYBS)
      ]
      state.dialogZJVisible = true
    }

    const randomZjxx = () => {
      Promise.all([
        validateJbxx(),
        validateCqtj(),
      ]).then(res => {
        state.randoming = true
        let params = {
          ...state.formData,
          PBHYBS: state.PBHYBS,
          YTJZJ: [
            ...state.formData.CQZJList.map(item => item.RYBS),
            ...state.formData.ZDZJList.map(item => item.RYBS)
          ],
          JSYCQRS: state.formData.CQZJList.filter(item => item.SFQRCX !== '0' && item.ZJLB==='JS').length,
          JJYCQRS: state.formData.CQZJList.filter(item => item.SFQRCX !== '0' && item.ZJLB==='JJ').length,
          CQTJXX: state.formData.CQTJForm.CQTJMXList.reduce((obj, item) => {
            obj[item.KEY] = item.VALUE
            return obj
          }, {})
        }
        axiosUtil.post('/backend/zjgl/pwcq/randomZjxx', params).then(res => {
          if (res.data && res.data.ZT === '1') {
            state.formData.CQZJList = res.data.YCQZJList || []
            ElMessage.success('抽取成功：' + res.data.msg)
            state.ZJLBLX = 'CQ'
          } else {
            ElMessage.error('抽取失败')
          }
          state.randoming = false
        })


        console.log(params)

      }).catch(msg => {
        ElMessage.error(msg)
      })


    }

    const validateCqtj = () => {
      return new Promise((resolve, reject) => {
        if (state.formData.CQTJForm.CQTJMXList.length === 0) {
          reject('请先添加抽取规则')
          return
        }

        if (!state.formData.CQTJForm.CQTJMXList.some((val) => ['JSLRS','JJLRS'].includes(val.KEY))) {
          reject('必须包含技术类人数或经济类人数')
          return
        }

        if (state.formData.CQTJForm.CQTJMXList.some((val) =>
            !val.KEY || !val.VALUE || (val.VALUE instanceof Array && val.VALUE.length === 0))) {
          reject('条件、逻辑、条件值不能为空！')
          return
        }
        resolve()
      })
    }


    const closeForm = () => {
      emit('close')
    }

    const getZYData = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy', {}).then((res) => {
        state.XCSZYOptions = comFun.treeData(res.data, 'ZYBM', 'FZYBM', 'children', '0')
      });
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const getZRS = () => {
      return state.ZBRDBList.length + state.formData.CQZJList.length + state.formData.ZDZJList.length
              + state.JDRList.length + state.ZBGZRYList.length;
    }

    onMounted(() => {
      getDMBData("CQTJ", "CQTJOptions").then(()=>{
        if (props.params.operation !== 'add') {
          getFormData()
        }
        getZYData()
        getDMBData("ZJQY", "ZJQYOptions")
        getDMBData("BCXYY", "BCXYYOptions")
      })
    })

    return {
      ...toRefs(state),
      Plus,
      closeForm,
      saveData,
      chooseXm,
      addCQTJ,
      changeCQTJ,
      disabledTj,
      delTjData,
      openCheckRy,
      getRyRes,
      randomZjxx,
      openZJXZDialog,
      getZjRes,
      delZjcqData,
      getZRS,
      saveForm,
      loadCqtj
    }
  }

})
</script>

<style scoped>
/deep/ .el-collapse-item__header {
  background-color: #dcdfef !important;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
  font-size: 16px;
  color: black !important;
}

/* 屏幕显示的样式 */
.hidden-for-screen {
  display: none; /* 屏幕上不显示 */
}

/* 打印时的样式 */
@media print {
  .hidden-for-screen {
    display: block; /* 打印时显示 */
  }
}
</style>
