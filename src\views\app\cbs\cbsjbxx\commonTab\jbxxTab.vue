<template>
  <div style="font-size: 14px;padding-top: 10px;">
    <appRow label="企业名称：" label-width="130px">{{defaultData['CBSDWQC']}}</appRow>
    <appRow label="统一社会信用代码：" label-width="130px">{{defaultData['TYXYDM']}}</appRow>
<!--    <appRow label="企业类型：" label-width="130px">{{defaultData['QYLXDM']}}</appRow>-->
    <appRow label="法人：" label-width="130px">{{defaultData['FDDBRXM']}}</appRow>
    <appRow label="法人联系方式：" label-width="130px">{{defaultData['FDDBRLXFS']}}</appRow>
    <appRow label="注册资金：" label-width="130px">{{defaultData['ZCZBJE']}}</appRow>
    <appRow label="引进类型：" label-width="130px">{{defaultData['QYLXDM']}}</appRow>
    <appRow label="联系人：" label-width="130px">{{defaultData['LXRXM']}}</appRow>
    <appRow label="联系人联系方式：" label-width="130px">{{defaultData['LXRSJH']}}</appRow>
    <appRow label="发证时间：" label-width="130px">{{defaultData['FZSJ']}}</appRow>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import appRow from "@views/app/common/appRow";

export default defineComponent({
  name: '',
  components: {appRow},
  props: {
    defaultData: {
      type: Object,
      defaultData: () => {},
    },
  },
  setup(props, {emit}) {
    const state = reactive({})
    onMounted(() => {

    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>

</style>
