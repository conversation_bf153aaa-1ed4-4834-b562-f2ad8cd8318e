<template>
  <div v-loading="loading">
    <div style="height: calc(100vh - 150px)">
      <component ref="busiCom" :is="componentChild[busiUrl]" v-model:processParams="params" @closeAudit="closeForm"
                 :params="businessParams"></component>
    </div>
    <div style="width: 100%;height: 50px;display: flex;align-items: center;justify-content: center">
      <el-button size="default" type="success" @click="saveData('save')"
                 v-if="processParams.status === '1' && ['new','1'].includes(processParams.activityId)">
        保存
      </el-button>
      <el-button size="default" type="primary" @click="saveData('submit')"
                 v-if="processParams.status === '1' && ['new','1'].includes(processParams.activityId)">
        提交
      </el-button>
      <el-button size="default" type="danger" @click="terminatedTask"
                 v-if="processParams.status === '1' && ['new','1'].includes(processParams.activityId) && params.TASKID">
        终止
      </el-button>



      <el-button size="default" type="primary" @click="onOperate('1')"
                 v-if="processParams.status === '1' && !['new','1'].includes(processParams.activityId)">
        同意
      </el-button>
      <el-button size="default" type="danger" @click="onOperate('0')"
                 v-if="processParams.status === '1' && !['new','1'].includes(processParams.activityId)">
        不同意
      </el-button>


      <el-button size="default" @click="closeForm">关闭</el-button>
    </div>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="title"
        @closed="dialogVisible=false"
        z-index="1000"
        append-to-body
        width="600px">
      <div>
        <component v-if="examUrl" ref="opinCom" :is="componentChild[examUrl]"
                   @getFinishRes="getFinishRes" :processParams="processParams" :operateParams="operateParams"
                   @closeForm="dialogVisible=false"/>
        <opinionForm v-else @getFinishRes="getFinishRes" :processParams="processParams" :operateParams="operateParams"
                     @closeForm="dialogVisible=false"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw} from "vue";
import testForm from "@views/workflow/test/testForm";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import newWork from "@views/workflow/newWork/index";
import opinionForm from "@views/workflow/newWork/opinionForm";


// import dwhbEdit from "@views/business/dwhb/dwhbEdit";
// import cbsxxEdit from "@views/business/cbszr/cbsxxEdit.vue";

export default defineComponent({
  name: '',
  components: {opinionForm},
  props: {
    processParams: {
      type: Object,
      required: true
    },
    businessParams: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      businessParams: props.businessParams,
      params: props.processParams,
      busiUrl: props.processParams.busiUrl,
      examUrl: props.processParams.examUrl,
      componentChild:{
        test: markRaw(testForm),
        // dwhb: markRaw(dwhbEdit),
        // // 承包商正式准入
        // TBZGSQ: markRaw(cbsxxEdit)
      },
      loading: false,

      dialogVisible: false,
      title: '',

      operateParams:{}

    })

    const instance = getCurrentInstance()
    const saveData = (value) => {
      state.loading=true
      instance.proxy.$refs['busiCom'].saveData(value).then(res=>{
        if(value==='submit'){
          if(state.params.TASKID){
            getFinishRes({
              suggestflg: '1',
            })
            return
          }


          let params={
            ...state.params,
            founder: state.userInfo.userLoginName,
            processInstanceId: state.businessParams.id,
            tzbl: state.params.tzbl
          }
          if(!params.nextPerformers){
            axiosUtil.get('/backend/workFlow/wf5/getLcblr',state.params).then(res=>{
              params.nextPerformers=res.data
              if(params.nextPerformers){
                createTask(params)
              }else {
                ElMessage.error('获取办理人失败')
                rollBack(state.params)
              }
            }).catch(res=>{
              ElMessage.error('获取办理人失败')
              rollBack(state.params)
            })
          }else {
            createTask(params)
          }
        }else {
          state.loading=false
        }
      }).catch(msg=>{
        ElMessage.error(msg)
        state.loading=false
      })
    }

    const createTask = async (params) => {
      let {processId, processName, founder, processInstanceId, nextPerformers, toActivityId, tzbl} = params
      let res = await newWork.createTask(processId, processName, founder, processInstanceId, nextPerformers, toActivityId, tzbl)
      if(res.success){
        ElMessage.success("已发起流程")
        closeForm()
      }else {
        ElMessage.error("流程发起失败")
        rollBack(state.params)
      }
    }

    const getFinishRes = (res) => {
      state.dialogVisible=false
      state.loading=true
      let params={
        ...res,
        performer: state.userInfo.userLoginName,
        tzbl: res.tzbl || state.params.tzbl,
        sfcxzlc: res.sfcxzlc,
        taskId: state.params.TASKID
      }
      if(!res.nextPerformers){
        axiosUtil.get('/backend/workFlow/wf5/getLcblr',state.params).then(res=>{
          params.nextPerformers=res.data
          if(params.nextPerformers){
            finishTask(params)
          }else {
            ElMessage.error('获取办理人失败')
          }
        }).catch(res=>{
          ElMessage.error('获取办理人失败')
        })
      }else {
        finishTask(params)
      }
    }

    const finishTask = async (params) => {
      let {taskId, performer, suggestflg, result, nextPerformers, toActivityId, tzbl, dbid,sfcxzlc} = params
      let res = await newWork.finishTask(taskId, performer, suggestflg, result, nextPerformers, toActivityId, tzbl, dbid,sfcxzlc)
      if (res.success) {
        ElMessage.success("提交成功")
        closeForm()
      } else {
        ElMessage.error("提交失败")
      }

    }

    const terminatedTask = () => {
      ElMessageBox.confirm('确定终止该流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let processInstanceId = state.params.PROCESSINSTANCEID
        let userId = state.userInfo.userId
        let userName = state.userInfo.userName
        state.loading=true
        let res = await newWork.terminatedTask(processInstanceId, userId, userName)
        if (res.success) {
          ElMessage.success("终止成功")
          closeForm()
        } else {
          state.loading=false
          ElMessage.error("终止失败")
        }
      }).catch(() => {});
    }

    const onOperate = (value) => {
      state.operateParams={
        suggestflg: value,
        result: value==='1' ? '同意' : '退回'
      }
      state.title=value==='1' ? '通过意见' : '驳回意见'
      instance.proxy.$refs['busiCom'].saveData('submit').then(res=>{
        state.dialogVisible=true
      })
    }

    const rollBack=(params)=>{
      state.loading=false
      axiosUtil.get('/backend/workFlow/wf5/rollBack',params)
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      onOperate,
      getFinishRes,
      terminatedTask

    }
  }

})
</script>

<style scoped>

</style>
