#工作流5版本

##一.流程服务
####流程服务版本 5.3.2（项目包含建表sql）
###1.变量配置（/varConfig）
shbl: 判断下节点走向（仅多分支有用）
sfcxzlc： 判断流程退回时是否需要重新走流程（默认 ```1``` 重新走流程）
###2.函数配置（/funConfig）
节点结束回调： 发起节点与审核节点调用（离开节点触发）
流程运行结束： 结束节点调用（进入节点前触发）
###3.配置流程（/wfconfig）
####节点设置（基础设置）
节点名称：根据实际情况命名。   
节点描述：发起节点设置为 ```1```或```new```，审批节点从```2```开始依次设置，结束节点设置为```0```。   
审核链接：不填。   
发起链接：不填。   
函数路径：发起节点与审核节点选择节点结束回调，结束节点选择流程运行结束。   
####节点设置（审核设置）
退回结点：根据实际情况选择。   
超期期限：根据实际情况选择。   
排除节假日：根据实际情况选择。   
办理人：不填。   
审核方式：根据实际情况选择。   
####连线设置
审批意见：不填。
流程变量：当有多个分支的时候，选择shbl，否则可不设置。

##二.框架页
###1.index.js配置
需要向```processData```对象中添加流程相关信息
```
TEST:{
    processId:'17362239868489278',  //processId查询twfprocesses表获得
    busiUrl: 'test',    //流程审核业务页面编码
    examUrl: {
        'new': 'sh1'   //流程审核办理页面（若未配置该值将使用默认页面）
        ...
    },      
},
```
需要向```processMap```对象中添加流程映射信息
```
    '17362239868489278' : 'TEST',    //processId:流程映射名称
```
###2.auditFrame.vue配置
需要在```componentChild```中引入业务页面
```
componentChild:{
    test: markRaw(testForm),
},
```

发起页面引入auditFrame.vue组件时，需传入props```businessParams```与```processParams```。   
businessParams(业务参数)格式为
```
{
  editable: true,
  id: comFun.newId(), //业务id
  operation: 'add'
}
```
processParams(流程参数)格式为
```
{
  ...newWork.processData.TEST,  //可通过配置文件直接引入基础信息
  activityId: 'new',  //与初始节点备注配置一致
  status: '1'       //状态1
}
```
###3.业务页面配置
业务页面接收参数```processParams```，在提交与保存时默认调用saveData```(type)```方法，type为submit时为提交，type为save时为保存。函数需要
返回一个```Promise```函数，若是校验失败请返回```reject(msg)```，msg为校验失败提示信息。    
流程若是发起时，需要通过```emit("update:processParams", data)```更新processParams中processInstanceId（业务id）与processName（业务发起名称），如果需要指定流程走向请添加tzbl属性。

###4.审核页面
可接收props```operateParams```和```processParams```，
若自定义审核页面提交数据请执行```emit("getFinishRes",state.formData)```。
state.formData格式
```
{
    ...props.operateParams,
    tzbl: '1',  //需要选择分支的时候使用，会覆盖掉业务页面的tzbl
    sfcxzlc: '1',//是否重新走流程
    suggestflg: '1',//是否通过
    result: '同意' //审核意见
}
```

##三.流程相关接口
###1.获取流程办理人
url：/getLcblr```GET```   

| 参数                | 描述              | 是否必须 |
|-------------------|-----------------|------|
| processId         | 流程id            | 是    |
| processInstanceId | 业务id            | 是    |
| activityId        | 节点id（与描述配置的值一致） | 是    |

返回值为用户登录账号字符串，多个办理人用分号隔开。

###2.流程节点结束
url：/updateBusinessByPorcessInstance```POST```  

| 参数（部分常用参数，已转换）    | 描述              | 是否必须 |
|-------------------|-----------------|------|
| processId         | 流程id            | 是    |
| processInstanceId | 业务id            | 是    |
| activityId        | 节点id（与描述配置的值一致） | 是    |
| shbl              | 审核变量            | 是    |
| isPass            | 是否通过            | 是    |

###3.流程运行结束
url：/finallBusinessByPorcessInstance```POST```   
参数同```流程节点结束```接口

###4.回滚业务状态
url：/rollBack```GET```   

| 参数                | 描述              | 是否必须 |
|-------------------|-----------------|------|
| processId         | 流程id            | 是    |
| processInstanceId | 业务id            | 是    |

当流程发起失败时框架页自动执行该操作。
##四.数据迁移
单个流程迁移
```
select * from twfactivities where processid='17367383939694445';
select * from twfperformers where processid='17367383939694445';
select * from twfprocesses where processid='17367383939694445';
select * from twfprocesstoblob where processid='17367383939694445';
select * from twftransitions where processid='17367383939694445';
select * from twfvariable WHERE processid='17367383939694445';
```