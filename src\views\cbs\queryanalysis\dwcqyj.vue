<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.yjlx" placeholder="请选择预警类型" clearable>
            <el-option label="已超期" value="0"/>
            <el-option label="一个月内将超期" value="30"/>
            <el-option label="三个月内将超期" value="90"/>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="data.queryForm.dwmc" placeholder="请输入名称"></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.dwzt" placeholder="请选择状态" clearable>
            <el-option label="正常" value="ZC"/>
            <el-option label="暂停" value="ZT"/>
            <el-option label="延长期限" value="YQ"/>
            <el-option label="取消" value="QX"/>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="10" class="grid-cell">
        <div style="display: flex">
          <el-button @click="resetQuery"><el-icon><RefreshRight/></el-icon>重置</el-button>
          <el-button type="primary" @click="query"><el-icon><Search/></el-icon>查询</el-button>
          <el-button type="primary" @click="exportData"><el-icon><Upload/></el-icon>导出</el-button>
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          highlight-current-row
          size="default"
          height="calc(100vh - 250px)"
          ref="table"
          fit
          border
          :data="data.tableData"
          v-loading="tableLoading"
      >
        <EleProTableColumn
            v-for="prop in data.tableColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #opration="{row,$index}">
            <div>
              <!--todo-->
              <el-button class="lui-table-button" @click="goWrite(row)">发起变更</el-button>
            </div>
          </template>
          <template #status="{row,$index}">
            <span v-if="row.DWZT=='ZC'">正常</span>
            <span v-if="row.DWZT=='QX'">取消</span>
            <span v-if="row.DWZT=='ZT'">暂停</span>
            <span v-if="row.DWZT=='YQ'">延长期限</span>
          </template>
        </EleProTableColumn>
      </el-table>
      <el-pagination
          background
          v-model:current-page="data.queryForm.page"
          v-model:page-size="data.queryForm.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="data.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
    </div>
  </el-form>
</template>
<script setup>
import {
    ref,
    reactive,
    onMounted, onUnmounted,
} from "vue";
import {ElMessage, ElMessageBox} from 'element-plus';
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getReportDwcqyj,postDwrcglDwyqqx,getBgGetChangedTeamById,getBgCbsqyxx, getTeamreslutGetProDetails, getReportDwcqyjExport} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam,getCommonSelectDMB} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import {v4 as uuidv4} from "uuid";
import comFun from "@src/lib/comFun";
import tabFun from "@src/lib/tabFun";
import {runtimeCfg,eventBus, mixin} from '@src/assets/core/index';
import { Search, Upload, Plus,RefreshRight} from '@element-plus/icons-vue'
import axiosUtil from "@lib/axiosUtil";
const {vsuiEventbus} = mixin()
const data = reactive({
    total: 0,
    shortcuts: [
        {
            text: '三个月',
            value: () => {
                const end = new Date()
                const start = data.saveForm.DWYXQJS?new Date(data.saveForm.DWYXQJS): new Date()
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90)
                return [start, end]
            },
        },{
            text: '半年',
            value: () => {
                const end = new Date()
                const start = data.saveForm.DWYXQJS?new Date(data.saveForm.DWYXQJS): new Date()
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180)
                return [start, end]
            },
        },{
            text: '一年',
            value: () => {
                const end = new Date()
                const start = data.saveForm.DWYXQJS?new Date(data.saveForm.DWYXQJS): new Date()
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365)
                return [start, end]
            },
        },
    ],
    queryForm: {
        yjlx: null,
        dwmc: null,
        dwzt: null,
        page: 1,
        size: 10
    },
    changeReason:[],
    saveForm: {
        date: ''
    },
    teamStatus:[
        {
            value: 'ZC',
            name: '正常'
        },
        {
            value: 'ZT',
            name: '暂停'
        },
        {
            value: 'QX',
            name: '取消'
        },
        {
            value: 'YQ',
            name: '延长期限'
        },
    ],
    currentUser: {},
    saveVisible: false,
    tableData: [],
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 60,
            align: "center"
        },
        {
            label: "企业名称",
            prop: "CBSDWQC",
            align: "left",
            showOverflowTooltip: true,
        },
        {
            label: "队伍名称",
            prop: "DWMC",
            align: "left",
            showOverflowTooltip: true,
        },
        {
            label: "当前状态",
            prop: "DWZT",
            align: "center",
            slot: 'status'
        },
        {
            label: "状态有效期",
            prop: "DWYXQJS",
            align: "center",
            width: 150,
        },
        {
            label: "预警说明",
            prop: "YJYY",
            align: "left",
            showOverflowTooltip: true,
            width: 400,
        },
        {
            label: "操作",
            align: "center",
            width: 150,
            fixed: "right",
            slot: "opration",
        },
    ],
})

const resetQuery = () => {
    data.queryForm = {
        yjlx: null,
        dwmc: null,
        dwzt: null,
        page: 1,
        size: 10
    }
}


const exportData = ()=>{
  let column = [[
    { field: 'CBSDWQC', title: '企业名称'},
    { field: 'DWMC', title: '队伍名称'},
    { field: 'DWZT', title: '当前状态'},
    { field: 'DWYXQJS', title: '状态有效期'},
    { field: 'YJYY', title: '预警说明'},
  ]]
  let params = {
    title: "承包商超期预警",
    name: "承包商超期预警",
    params: data.queryForm,
    url: '/excel/cbscqyjExport',
  }
  params.column = column
  axiosUtil.exportExcel('/backend/excelExport/magicExcel', params)
}

const goWrite = (row) => {
    let param = {
        DWYWID: row.DWYWID,
        XNDWYWID: uuidv4().replace(/-/g, ''),
        NEWTYPE: 'BG'
    }
    getBgCbsqyxx(param).then(res=>{
        if(row.DWLX == 'CBS'){
            getTeamreslutGetProDetails({dwid:row.DWYWID}).then(res=>{
                tabFun.addTabByRoutePath(
                  "承包商基本信息",
                  "/contractors/cbsjbxxIndex",
                  {
                      uuId : param.XNDWYWID, //队伍业务ID
                      MBID: res.data.MBID, //模板ID
                      MBLX: "QY", //模板类型、
                      ZYFLDM: res.data.ZYFLDM, //专业分类代码
                      YWLXDM: "BG", //业务类型代码
                  },
                  {}
                );
            })
        }else if(row.DWLX == 'DW'){
            getBgGetChangedTeamById({dwid:param.XNDWYWID}).then(r=>{
                tabFun.addTabByRoutePath('队伍信息变更', '/contractors/yrsqxxIndex', {DWYWID: r.data.DWYWID,YWLXDM: 'BG',editable:true,backPath: '/query-analysis/dwcqyj'}, );
            })

        }
    })
}
const tableLoading = ref(false);
const query = () => {
    tableLoading.value = true;
    getReportDwcqyj({
        ...data.queryForm,
        orgid: data.currentUser.ORGNA_ID
    }).then(res => {
        console.log(res)
        data.tableData = res.data.list
        data.total = res.data.total
    }).catch((err) => {
        console.log(err);
    }).finally(() => {
        tableLoading.value = false
    })

}
const edit = (row, index) => {
    console.log(row)
    data.saveForm = {
        RCGLID: uuidv4().replace(/-/g,''),
        date:[
            row.DWYXQJS || comFun.getNowDate(),
            comFun.refDate(row.DWYXQJS || comFun.getNowDate(),30)
        ],
        ...row,
        BGHZT: 'YQ'
    }
    console.log(data.saveForm)
    data.saveVisible = true;

}
const getChangeReason = ()=>{
    getCommonSelectDMB({DMLBID: 'DWZTBGYY'}).then(res=>{
        data.changeReason = res.data
    })
}
const saveFormRef = ref(null)
const save = () => {
    data.saveForm.YXQKS = data.saveForm.date[0]
    data.saveForm.YXQJS = data.saveForm.date[1]
    let bgh = {
        ZTKSSJ:  data.saveForm.YXQKS ,
        ZTJSRQ: data.saveForm.YXQJS,
        ZT: data.saveForm.BGHZT,
    }
    let bgq = {
        ZTKSSJ:  data.saveForm.DWYXQKS,
        ZTJSRQ: data.saveForm.DWYXQJS,
        ZT: data.saveForm.DWZT,
    }
    data.saveForm.CJSJ = comFun.getNowTime();
    data.saveForm.CJRXM = data.currentUser.USER_NAME;
    data.saveForm.CJRZH = data.currentUser.USER_LOGINNAME;
    data.saveForm.CJDWID = data.currentUser.ORGNA_ID
    data.saveForm.BGH = JSON.stringify(bgh)
    data.saveForm.BGQ = JSON.stringify(bgq)
    data.saveForm.DWZT = data.saveForm.BGHZT
    console.log(data.saveForm);
    postDwrcglDwyqqx(data.saveForm).then(res => {
        ElMessage({
            type: 'success',
            message: '保存成功!'
        });
        query();
        data.saveVisible = false;
    }).catch(e => {
        ElMessage({
            type: 'error',
            message: '保存失败!'
        });
    })
}

const getUserInfo = () => {
    let user = VSAuth.getAuthInfo().permission
    getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
        data.currentUser = res.data;
        query();
    })
}

const handleSizeChange = (val) => {
    data.queryForm.size = val;
    query();
}
const handleCurrentChange = (val) => {
    data.queryForm.page = val;
    query();
}


onMounted(() => {
    getChangeReason();
    getUserInfo();
    vsuiEventbus.on('reloadTableData',getUserInfo);
})
onMounted(() => {

})
onUnmounted(()=>{
    // timer.value = null;
    vsuiEventbus.off('reloadTableData',getUserInfo);
})
</script>

<style scoped>
.header {
    padding: 10px 10px 0;
    height: 50px;
    line-height: 50px;
}

.main {
    height: calc(100% - 155px);
    padding: 10px;
}
.footer {
    height: 100px;
    line-height: 100px;
}
</style>
