<script setup>
import request from '@/utils/request'
import { ref, onMounted } from 'vue'
import { POBrowser } from "js-pageoffice"

const titleText = ref('');

onMounted(async () => {
	try {
		const response = await request({
			url: '/index',
			method: 'get',
		});
		titleText.value = response;
	} catch (error) {
		console.error('Failed to fetch title:', error);
	}
});

function open_pageoffice(vue_page_url, param) {
	POBrowser.openWindow(vue_page_url, 'width=1200px;height=800px;', param);
}
</script>

<template>
	<div class="Word">
		<div style="text-align:left;margin-top:30px;">
			<span>1、演示给Excel模板中定义了名称的一块区域赋值，保存时可获取此区域内各单元格的数据：</span><br /><br />
			<a href="#" @click.prevent="open_pageoffice('Excel', '')">openTableByDefinedName方法的简单使用</a>
		</div>
		<div style="text-align:left; margin-top:30px;">
			<span>2、演示如何在不修改代码的情况下，填充数据到用户自定义的两个不同的Excel模板，显示出不同的效果：</span><br /><br />
			<a href="#" @click.prevent="open_pageoffice('Excel2', 'temp1.xlsx')">自定义Excel模板一显示效果</a><br /><br />
			<a href="#" @click.prevent="open_pageoffice('Excel2', 'temp2.xlsx')">自定义Excel模板二显示效果</a>
		</div>
		<div style="text-align:left; margin-top:30px;">
			<span>3、演示openTable与openTableByDefinedName方法填充数据到相同Excel模板，在动态填充的数据按实际数据行数自动扩展的情况下，生成的文件显示出不同的效果：</span><br /><br />
			<a href="#" @click.prevent="open_pageoffice('Excel6', '')">打开模板（test4.xls）</a><br /><br />
			<a href="#"
				@click.prevent="open_pageoffice('Excel4', '')">用openTable填充数据到模板（test4.xls）后的效果――出现表格重叠的问题</a><br /><br />
			<a href="#"
				@click.prevent="open_pageoffice('Excel5', '')">用openTableByDefinedName填充数据到模板（test4.xls）后的效果――表格互不影响</a>
		</div>
	</div>
</template>
