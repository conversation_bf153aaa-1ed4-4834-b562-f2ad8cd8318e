<!-- 新增队伍 -->
<template>
  <div>
<!--    <el-card class="box-card">-->
<!--      <template #header>-->
<!--        <div>-->
<!--          <div class="card-header">-->
<!--            <div class="ra-icon"></div>-->
<!--            <span style="font-size: 18px;font-weight: 400">业务办理</span>-->
<!--          </div>-->
<!--        </div>-->
<!--      </template>-->
<!--      <div class="el-row-class">-->
<!--        <BellFilled class="bell-icon"/>-->
<!--        <div>正式投标资格申请：</div>-->
<!--        <div>办理某个专业长期准入，请从这里-->
<!--          <el-button @click="openDialog('ZR')" class="row-button" type="text">发起申请>>></el-button>-->
<!--        </div>-->
<!--      </div>-->

<!--      <div class="el-row-class">-->
<!--        <BellFilled class="bell-icon"/>-->
<!--        <div>企业信息变更：</div>-->
<!--        <div>营业执照、企业资质信息等资料到期或发生变化后，请从这里-->
<!--          <el-button type="text" class="row-button" @click="goCbsBg">发起申请>>></el-button>-->
<!--        </div>-->
<!--      </div>-->

<!--      &lt;!&ndash;-->
<!--            <div class="el-row-class">-->
<!--              <BellFilled class="bell-icon"/>-->
<!--              <div>队伍信息变更申请：</div>-->
<!--              <div>办理了长期准入的队伍，队伍资质、人员、设备等信息到期或发生变化后，请从这里-->
<!--                <el-button type="text" class="row-button" @click="bgqy">发起申请>>></el-button>-->
<!--              </div>-->
<!--            </div> &ndash;&gt;-->

<!--      <div class="el-row-class">-->
<!--        <BellFilled class="bell-icon"/>-->
<!--        <div>承包商增项申请：</div>-->
<!--        <div>已经办理了长期准入的队伍，申请增加新的投标专业，请从这里-->
<!--          <el-button type="text" class="row-button" @click="zx()">发起申请>>></el-button>-->
<!--        </div>-->
<!--      </div>-->
<!--      &lt;!&ndash; <div class="el-row-class">-->
<!--        <BellFilled class="bell-icon"/>-->
<!--        <div>承包商复审申请：</div>-->
<!--        <div>按照年度复审管理要求，接到复审通知后，请从这里-->
<!--          <el-button type="text" class="row-button" @click="fs()">发起申请>>></el-button>-->
<!--        </div>-->
<!--      </div> &ndash;&gt;-->
<!--    </el-card>-->

    <el-card class="box-card" style="margin-top: 10px;padding-bottom: 20px">
      <template #header>
        <div style="display: flex;align-items: center;gap: 10px">
          <!-- <div class="card-header">
            <div class="ra-icon"></div>
            <span style="font-size: 18px;font-weight: 400">草稿</span>
          </div> -->

            <el-input ref="input45296" placeholder="请输入业务标题" v-model="params.YWBT" type="text" clearable size="default" style="width: 200px;margin-left: auto">
            </el-input>
          <el-button type="primary" @click="getDataList" size="default">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
        </div>
      </template>
      <el-table
          class="lui-table"
          :data="tableData"
          width="100%"
          size="default"
          :stripe="true"
          height="calc(100vh - 320px)"
          v-loading="tableLoading"
      >
        <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            align="center"
            width="50"
        ></el-table-column>
        <el-table-column
            prop="YWLXDMMC"
            label="业务类型"
            header-align="center"
            align="center"
            show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
            prop="YWBT"
            label="业务标题"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="CJRXM"
            label="办理人"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="CJSJ"
            label="办理时间"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            label="操作"
            header-align="center"
            align="center"
            width="150"
        >
          <template #default="{ row, $index }">
            <div class="table-col-btn">
              <el-button v-if="row.SHZT=='0'" class="lui-table-button" @click="handleEdit(row, $index)"
              >编辑
              </el-button
              >
              <el-button v-if="row.SHZT=='0'" class="lui-table-button" @click="handleDelete(row, $index)"
              >删除
              </el-button
              >
              <el-button v-if="row.SHZT!='0'" class="lui-table-button" @click="handleEdit(row, $index)"
              >查看
              </el-button
              >
              <el-button v-if="row.SHZT!='0'" class="lui-table-button" @click="handleMonitor(row, $index)"
              >跟踪
              </el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="params.page"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="params.size"
            layout="total, prev, pager, next, sizes"
            class="lui-pagination"
            :total="params.total"
        ></el-pagination>
      </div>

    </el-card>

    <el-dialog z-index="1000" title="业务流程跟踪" v-model="showDialog" width="80%" top="35px" :close-on-click-modal="false">
      <monitorForm2 v-if="showDialog" :queryParams="queryParams"/>
    </el-dialog>
  </div>
</template>

<script setup>
import {ElMessage, ElMessageBox} from "element-plus";
import {computed, nextTick, onMounted, reactive, ref, onUnmounted} from "vue";
import TabFun from "@src/lib/tabFun";
import axiosUtil from "../../../lib/axiosUtil";
import vsAuth from "@src/lib/vsAuth";
import zxzy from "@views/cbs/cbsyj/xzzy";
import zxzyzx from "@views/cbs/cbsyj/xzzy-zx/index";

import {mixin} from "@src/assets/core/index";
import {v4 as uuidv4} from "uuid";
import monitorForm2 from "../../workflow/MonitorForm2";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
const {vsuiRoute, vsuiEventbus} = mixin();
import {
  getCbsyjGetYwxxCg,
  deleteCbsyjTeamdelete,
  deleteCbsyjDeleteCbsyj,
  getCbsyjGetTeamInfoByOrgId,
  getTeamreslutGetProDetails,
  getBgCbsqyxx,
  getCbszxCopyAddInfo
} from "@src/api/sccbsgl.js";
import VSAuth from "@src/lib/vsAuth";
import {BellFilled} from '@element-plus/icons-vue'

let tableData = ref([]);

let currentTeam = ref({});

let showDialog = ref(false)
let params = reactive({
  page: 1,
  // 每页的数据条数
  size: 10,
  total: 0,
  orgnaId: "",
  LY: 'CBSZR'
});
let queryParams = reactive({
  id: ''
})

const deleteRow = (index, row) => {
  axiosUtil.get("/sldwgl/dwkh/deleteDwkh", row).then((res) => {
    if (res.data.meta.success) {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      getDataList();
    } else {
      ElMessage({
        message: res.data.meta.message,
        type: "error",
      });
    }
  });
};
/**
 * 序号
 */
const indexMethod = (index) => {
  return index + params.size * (params.page - 1) + 1;
};
/**
 * 页面数据条数改变时
 */
const handleSizeChange = (val) => {
  params.page = 1;
  params.size = val;
  getDataList();
};
/**
 * 翻页
 */
const handleCurrentChange = (val) => {
  params.page = val;
  getDataList();
};
/**
 * @Params: {{Params}}
 * @Description: 获取数据
 */


const getDwInfo = () => {
  getCbsyjGetTeamInfoByOrgId({orgId: vsAuth.getAuthInfo().permission.orgnaId}).then(res => {
    currentTeam.value = res.data || {};
  })
}
// 承包商变更
const goCbsBg = () => {
  if (!currentTeam.value.DWYWID) {
    ElMessage({
      message: "请先进行正式投标资格申请",
      type: "warning",
    });
    return false
  }
  let ex = JSON.parse(currentTeam.value.EXTENSION)
  let param = {
    DWYWID: currentTeam.value.DWYWID,
    XNDWYWID: uuidv4().replace(/-/g, ''),
    NEWTYPE: 'BG'
  }
  getBgCbsqyxx(param).then(r => {
    getTeamreslutGetProDetails({dwid: currentTeam.value.DWYWID, dwlx: currentTeam.value.DWLX}).then(res => {
      TabFun.addTabByRoutePath(
          "承包商基本信息",
          "/contractors/cbsjbxxIndex",
          {
            uuId: param.XNDWYWID, //队伍业务ID
            MBID: ex.MBID, //模板ID
            MBLX: "QY", //模板类型、
            ZYFLDM: res.data.ZYFLDM, //专业分类代码
            YWLXDM: "BG", //业务类型代码
            JGDWYWID: param.DWYWID,
            editable: true
          },
          {}
      );
      vsuiEventbus.emit("reloadCbsjbxx", {
        uuId: param.XNDWYWID, //队伍业务ID
        MBID: ex.MBID, //模板ID
        MBLX: "QY", //模板类型、
        ZYFLDM: res.data.ZYFLDM, //专业分类代码
        YWLXDM: "BG", //业务类型代码
        JGDWYWID: param.DWYWID,
        editable: true
      });
    })
  })
}


/**查询表格数据 */
const tableLoading = ref(false);
const getDataList = () => {
  params.orgnaId = vsAuth.getAuthInfo().permission.orgnaId;
  tableLoading.value = true;
  getCbsyjGetYwxxCg(params).then(({data}) => {
    tableData.value = data.list ?? [];
    params.total = data.total;
  }).catch((err) => {
    console.log(err);
  }).finally(() => {
    tableLoading.value = false
  })
};

/**准入申请 */
const openDialog = (ywlxdm) => {
  if (ywlxdm == "ZR") {
    TabFun.addTabByCustomName("专业选择", "zxzy", zxzy, {}, {});
  }
};
const zx = () => {
  if (!currentTeam.value.DWYWID) {
    ElMessage({
      message: "请先进行正式投标资格申请",
      type: "warning",
    });
    return false
  }
  if (currentTeam.value.ADWLX.includes("DW")) {
    TabFun.addTabByRoutePath("增项-选择队伍", "/contractors/addTeamList", {}, {});
  } else {
    let zybms = currentTeam.value.ZYBM.split(',')
    let newId = uuidv4().replace(/-/g, '');
    getCbszxCopyAddInfo({
      dwywidNew: newId,
      dwywidResut: currentTeam.value.DWYWID,
      newType: 'ZX'
    }).then(res => {
      if (currentTeam.value.DWLX === 'CBS') {
        TabFun.addTabByCustomName("增项-专业选择", "zxzyzx", zxzyzx, {
          uuId: newId,
          dwlx: 'QY',
          zybms: zybms
        }, {});
      }
    })
  }
}
const fs = () => {
  if (!currentTeam.value.DWYWID) {
    ElMessage({
      message: "请先进行正式投标资格申请",
      type: "warning",
    });
    return false
  }
  if (currentTeam.value.ADWLX.includes("DW")) {
    TabFun.addTabByRoutePath("复审-选择队伍", "/contractors/teamReviewList", {}, {});
  } else {
    // let zybms = currentTeam.value.ZYBM.split(',')
    let newId = uuidv4().replace(/-/g, '');
    let param = {
      DWYWID: currentTeam.value.DWYWID,
      XNDWYWID: newId,
      NEWTYPE: 'BG'
    }
    getBgCbsqyxx(param).then(res => {
      if (currentTeam.value.DWLX === 'CBS') {
        getTeamreslutGetProDetails({dwid: currentTeam.value.DWYWID, dwlx: currentTeam.value.DWLX}).then(res => {
          let ex = JSON.parse(currentTeam.value.EXTENSION)
          TabFun.addTabByRoutePath(
              "承包商基本信息",
              "/contractors/cbsjbxxIndex",
              {
                uuId: newId, //队伍业务ID
                MBID: ex.MBID, //模板ID
                MBLX: "QY", //模板类型、
                ZYFLDM: res.data.ZYFLDM, //专业分类代码
                YWLXDM: "FS", //业务类型代码
                JGDWYWID: currentTeam.value.DWYWID,
                editable: true
              },
              {}
          );
          vsuiEventbus.emit("reloadCbsjbxx", {
            uuId: newId, //队伍业务ID
            MBID: ex.MBID, //模板ID
            MBLX: "QY", //模板类型、
            ZYFLDM: res.data.ZYFLDM, //专业分类代码
            YWLXDM: "FS", //业务类型代码
            JGDWYWID: currentTeam.value.DWYWID,
            editable: true
          });
        })
      }
    })
  }
}

// 变更信息
const bgqy = () => {
  TabFun.addTabByRoutePath("变更列表", "/contractors/teamList", {}, {});
}
// 编辑跳转
const handleEdit = (row) => {
  var editable = false;
  if (row.SHZT == '0') {
    editable = true;
  }
  // if (row.DWLX === 'DW') {
  //   TabFun.addTabByRoutePath(`队伍信息${row.YWLXDMMC}`, '/contractors/yrsqxxIndex', {
  //     DWYWID: row.DWYWID,
  //     YWLXDM: 'BG',
  //     backPath: '/contractors/zrfqIndex',
  //     JGDWYWID: row.JGDWYWID,
  //     editable: editable
  //   },);
  //   return false;
  // }
  const {DWYWID: uuId, MBID, MBLX, ZYFLDM, YWLXDM, EXTENSION} = row;
  TabFun.addTabByRoutePath(
      `承包商信息${row.YWLXDMMC}`,
      "/contractors/cbsjbxxIndex",
      {
        uuId,
        MBID,
        MBLX,
        ZYFLDM,
        YWLXDM: YWLXDM ?? 'ZR',
        JGDWYWID: row.JGDWYWID,
        EXTENSION,
        editable: editable
      },
      {}
  );
  vsuiEventbus.emit("reloadCbsjbxx", {
    uuId,
    MBID,
    MBLX,
    ZYFLDM,
    YWLXDM: YWLXDM ?? 'ZR',
    JGDWYWID: row.JGDWYWID,
    EXTENSION,
    editable: editable
  });
};
const handleMonitor = (value) => {
  queryParams.id = value.DWYWID
  showDialog.value = true
}
const handleDelete = ({DWYWID}) => {
  ElMessageBox.confirm("确定删除此数据?", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  })
      .then(() => {
        deleteCbsyjDeleteCbsyj({
          params: {
            DWYWID,
          },
        })
            .then((result) => {
              ElMessage.success("删除成功");
              getDataList();
            })
            .catch((err) => {
              ElMessage.error("删除失败");
            });
      })
      .catch(() => {
        // catch error
      });
};
onMounted(() => {
  vsuiEventbus.on("reloadCaoGao", getDataList);
});
onUnmounted(() => {
  vsuiEventbus.off("reloadCaoGao", getDataList);
});
onMounted(() => {
  getDwInfo();
  getDataList();
});
</script>

<style scoped>

.el-row-class {
  display: flex;
  align-items: center;
  font-size: 18px;
  height: 50px;
  padding-left: 30px;
}
.el-row-class:hover{
  background-color: #edf4ff;
}
.bell-icon{
  width: 1em;
  height: 1em;
  color: #3c85fb;
  background-color: rgba(198, 226, 255, 0.74);
  padding: 2px;
  border-radius: 50%;
  margin-right: 20px;
}
.row-button{
  font-size: 18px;
  color: #409EFF;
}
.ra-icon{
  width: 8px;
  height: 16px;
  border-radius: 4px;
  background: #3c85fb;
}
.card-header{
  display: flex;
  align-items: center;
  gap: 20px;

}
</style>
