
/************************************************************************************************************************************
 *
 *
 *                                                          FBI WARNING
 *
 *      本文件引入内容为全局使用，为提升首屏加载速度，个别页面、组件使用到的组件，库，样式等在各自文件中引入，不要在此文件中引入
 *
 *
 *
 *
 *
 ***************************************************************************************************************************************/

import { app, router, store , elementPlus } from './assets/core/index';
import locale from "element-plus/lib/locale/lang/zh-cn";

//import vue3PreviewImage from 'vue3-preview-image'
const debounce = (fn, delay) => {
 let timer = null;
 return function () {
  let context = this;
  let args = arguments;
  clearTimeout(timer);
  timer = setTimeout(function () {
   fn.apply(context, args);
  }, delay);
 }
}

const _ResizeObserver = window.ResizeObserver;
window.ResizeObserver = class ResizeObserver extends _ResizeObserver{
 constructor(callback) {
  callback = debounce(callback, 16);
  super(callback);
 }
}

 app
 .use(router)
 .use(store)
  //.use(vue3PreviewImage)
 .use(elementPlus, { size: 'small', zIndex: 3000 ,locale})
 .mount('#app');

