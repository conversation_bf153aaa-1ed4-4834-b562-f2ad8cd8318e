<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{ formData.XMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMBH }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标单位：" prop="SSDWMC">
            <div style="margin-left: 10px">{{ formData.SSDWMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="计划开标地点：" prop="JHKBDD">
            <div style="margin-left: 10px">{{ formData.JHKBDD }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="投标文件递交时间：" prop="TBWJDJSJ">
            <div style="margin-left: 10px;display: flex;gap: 10px">
              <div>{{ formData.QRSFCJJZSJ }}</div>
              <div>至</div>
              <div>{{ formData.TBWJDJJZSJ }}</div>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="开标时间：" prop="KBSJ">
            <div style="margin-left: 10px">{{ formData.KBSJ }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="投标文件递交：" prop="TBWJDJ">
            <el-table ref="datatable91634" :data="formData.TBDJList" height="300px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="BDMC" label="标段名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="DWMC" label="投递单位" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="SFCY" label="是否参与" align="center"
                               :show-overflow-tooltip="true" width="80">
                <template #default="{row}">
                  <div v-if="row.SFCY==='1'">参与</div>
                  <div v-if="row.SFCY==='0'">不参与</div>
                </template>
              </el-table-column>
              <el-table-column prop="HHZT" label="审核状态" align="center"
                               :show-overflow-tooltip="true" width="100">
                <template #default="{row}">
                  <div v-if="row.HHZT==='2'">已通过</div>
                  <div v-if="row.HHZT==='3'">未通过</div>
                </template>
              </el-table-column>
              <el-table-column prop="SFGMBS" label="购买标书缴费" align="center"
                               :show-overflow-tooltip="true" width="100">
                <template #default="{row}">
                  <div v-if="row.SFGMBS==='1'">已缴费</div>
                  <div v-if="row.SFGMBS==='0'">未缴费</div>
                </template>
              </el-table-column>

              <el-table-column prop="TBWJ" label="投标文件" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="200">
                <template #default="{row}">
                  <vsfileupload
                      :editable="editable"
                      v-model:files="row.fileList"
                      :busId="row.TBBSID"
                      :key="row.TBBSID"
                      ywlb="TBWJ"
                      busType="TBWJ"
                      :limit="1"
                      accept=".pdf,.PDF"
                  ></vsfileupload>
                </template>
              </el-table-column>

            </el-table>
          </el-form-item>
        </el-col>


      </el-row>

      <div style="color: red;font-weight: bold;margin-top: 10px">提示：投标文件原始文档格式要求为PDF格式，请将投标文件压缩为ZIP格式并设置密码后上传。</div>


      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";


export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      QRHHID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',

        TBDJList: []
      },
      rules: {},
      DWXX: {}
    })

    const getFormData = () => {
      let params = {
        QRHHID: state.QRHHID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/tbwjdj/selectTbwjXmById', params).then((res) => {
        state.formData = res.data
        state.formData.TBDJList.forEach(item=>{
          if(!item.TBBSID){
            Object.assign(item,{
              TBBSID: comFun.newId(),
              CJRZH: state.userInfo.userLoginName,
              CJRXM: state.userInfo.userName,
              CJDWID: state.userInfo.orgnaId,
              CJSJ: comFun.getNowTime(),
              SHZT: '0',
              YWZT: '1',
            })
          }
        })
        state.loading = false
      })
    }

    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        TBDJList: state.formData.TBDJList.map(item=>{
          return{
            ...item,
            TBWJDJSJ: comFun.getNowTime(),
            SHZT: type==='submit' ? '1' : '0'
          }
        }),
        TJZT: type==='submit' ? '1' : '0'
      }
      state.loading=true
      axiosUtil.post('/backend/xsgl/tbwjdj/saveTbwjForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        let WSCBD=state.formData.TBDJList.find(item=>!item.fileList || item.fileList.length===0)
        if(WSCBD){
          ElMessage.error(`请上传标段：${WSCBD.BDMC}的投标文件`)
          resolve(false)
        }else {
          resolve(true)
        }
      })
    }


    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData
    }
  }

})
</script>

<style scoped>

</style>
