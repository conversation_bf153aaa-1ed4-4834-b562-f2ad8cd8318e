<template>
  <div>
    <component ref="YXCard" :onlyView="true" :is="cardComponent[params.XSFS]" @workEdit="chooseLx"
               v-if="cardComponent[params.XSFS]" :params="params"/>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="dialogTitle"
        @closed="closeForm"
        z-index="1200"
        top="5vh"
        :width="dialogWidth">
      <div>
        <component :is="pageComponent[busType]" v-if="pageComponent[busType] && dialogVisible" :params="busParams"
                   @close="closeForm"/>
      </div>
    </el-dialog>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw} from "vue";
import zbxmdxCard from "@views/zbxsgl/xmyx/dxCard/zbxmdxCard";
import fzbxmdxCard from "@views/zbxsgl/xmyx/dxCard/fzbxmdxCard";
import djtpdhCard from "@views/zbxsgl/xmyx/dxCard/djtpdhCard";



import xssqglEdit from "@views/zbxsgl/xssqgl/xssqglEdit";
import zbfaEdit from "@views/zbxsgl/xssqgl/zbfaEdit";
import xswjEdit from "@views/zbxsgl/xssqgl/xswjEdit";
import xstzfbEdit from "@views/zbxsgl/xstzfb/xstzfbEdit";
import xstzfbView from "@views/zbxsgl/xstzfb/xstzfbView";
import hhqkqrEdit from "@views/zbxsgl/hhqkqr/hhqkqrEdit";
import zbwjlqqkView from "@views/zbxsgl/zbwjlq/zbwjlqqkView";
import tbwjqrEdit from "@views/zbxsgl/tbwjdj/tbwjqrEdit";
import ydhyEdit from "@views/zbxsgl/ydhy/ydhyEdit";
import pwcqglEdit from "@views/zbxsgl/pwcq/pwcqglEdit";
import dbsqglEdit from "@views/zbxsgl/dbsqgl/dbsqglEdit";
import zbjggsEdit from "@views/zbxsgl/zbjggs/zbjggsEdit";
import zbjggsView from "@views/zbxsgl/zbjggs/zbjggsView";
import zbtzsqfEdit from "@views/zbxsgl/zbtzsqf/zbtzsqfEdit";
import zbtzggView from "@views/zbxsgl/zbtzsqf/zbtzggView";
import comFun from "@lib/comFun";
import djtpgsEdit from "@views/zbxsgl/djtpgs/djtpgsEdit";
import tpjlEdit from "@views/zbxsgl/tpjl/tpjlEdit";
import djtpgsView from "@views/zbxsgl/djtpgs/djtpgsView";
import zbjgqrEdit from "@views/zbxsgl/zbjgqr/zbjgqrEdit";




export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      cardComponent:{
        GKZB: markRaw(zbxmdxCard),
        YQZB: markRaw(zbxmdxCard),
        JB: markRaw(fzbxmdxCard),
        JJ: markRaw(fzbxmdxCard),
        DJTP: markRaw(djtpdhCard),
        GKJB: markRaw(fzbxmdxCard),
        GKJJ: markRaw(fzbxmdxCard),
      },

      busType: '',
      dialogVisible: false,
      dialogTitle: '',//业务名称
      dialogWidth: '1200px',
      busParams:{},
      pageComponent: {
        XSSQ: markRaw(xssqglEdit),
        XSFA: markRaw(zbfaEdit),
        XSWJ: markRaw(xswjEdit),
        XSGG: markRaw(xstzfbEdit),
        XSBM: markRaw(xstzfbView),
        XSHH: markRaw(hhqkqrEdit),
        WJLQ: markRaw(zbwjlqqkView),
        TBWJ: markRaw(tbwjqrEdit),
        YDHY: markRaw(ydhyEdit),
        PWCQ: markRaw(pwcqglEdit),
        ZBJG: markRaw(dbsqglEdit),
        ZBGS: markRaw(zbjggsEdit),
        GSYL: markRaw(zbjggsView),
        ZBQR: markRaw(zbjgqrEdit),
        ZBTZ: markRaw(zbtzsqfEdit),
        TGYL: markRaw(zbtzggView),
        TPGS: markRaw(djtpgsEdit),
        TPJL: markRaw(tpjlEdit),
        TPYL: markRaw(djtpgsView),
      },

    })


    const instance = getCurrentInstance()
    const closeForm = () => {
      state.dialogVisible = false
      // if(instance.proxy.$refs['YXCard']){
      //   instance.proxy.$refs['YXCard'].getFormData()
      // }
    }

    const chooseLx = (row, busType, editable, operation) => {
      if(['GKZB','YQZB'].includes(row.XSFS)){
        workEditZb(row, busType, editable, operation)
      }else if(['JB','JJ','GKJJ','GKJB'].includes(row.XSFS)){
        workEditFzb(row, busType, editable, operation)
      }else if(row.XSFS==='DJTP'){
        workEditDjtp(row, busType, editable, operation)
      }
    }

    const workEditZb = (row, busType, editable, operation) => {
      state.busType = busType;
      state.busParams = {editable: false, id: '', operation: 'view'}
      if (busType === 'XSSQ') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.FAID
        state.busParams.XMID=row.XMID
        state.busParams.XMMC=row.XMMC
        state.busParams.activeTab = 'ZBFA'
        state.dialogTitle = row.XMMC + '-选商申请'
      }


      if (busType === 'XSFA') {
        state.busParams.id = row.FAID
        state.dialogTitle = row.XMMC + '-选商申请方案'
      }

      if (busType === 'XSWJ') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.WJID
        state.dialogTitle = row.XMMC + '-选商文件'
      }

      if (busType === 'XSGG') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.GGID
        state.busParams.WJID = row.WJID
        state.busParams.XSFS = row.XSFS
        let title = ''
        if (row.XSFS === 'GKZB') {
          title = '-招标公告';
        } else if (row.XSFS === 'YQZB') {
          title = '-投标邀请函';
        } else if (row.XSFS === 'GKJB' || row.XSFS === 'GKJJ') {
          title = '-采购公告';
        } else if (row.XSFS === 'JB' || row.XSFS === 'JJ') {
          title = '-采购邀请函';
        }
        state.dialogTitle = row.XMMC + title
      }

      if (busType === 'XSBM') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-报名参与'
      }

      if (busType === 'XSHH') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-回函确认'
      }

      if (busType === 'WJLQ') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-招标文件领取情况'
      }

      if (busType === 'TBWJ') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-投标文件上传情况'
      }

      if (busType === 'YDHY') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.PBHYBS
        state.dialogTitle = row.XMMC + '-评标会议预定'
      }

      if (busType === 'PWCQ') {
        state.busParams.id = row.PBHYBS
        state.dialogTitle = row.XMMC + '-评委抽取'
      }

      if (busType === 'ZBJG') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.XSJGID
        state.busParams.FAID = row.FAID
        state.busParams.GGID = row.GGID
        state.dialogTitle = row.XMMC + '-评审资料及评标报告上传'
      }

      if (busType === 'ZBGS') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.JGGSID
        state.busParams.FAID = row.FAID
        state.busParams.XSJGID = row.XSJGID
        state.dialogTitle = row.XMMC + '-中标候选人公示'
      }

      if (busType === 'GSYL') {
        state.busParams.id = row.JGGSID
        state.dialogTitle = row.XMMC + '-中标候选人公示预览'
      }

      if(busType === 'ZBQR'){
        state.busParams.id = row.XSJGID || comFun.newId()
        state.busParams.tableEdit= !row.JGGSID
        if(operation!=='view'){
          state.busParams.operation= row.XSJGID ? 'edit' : 'add'
        }
        state.busParams.FAID = row.FAID
        state.busParams.GGID = row.GGID
        state.dialogTitle = row.XMMC + '-中标结果'
      }

      if (busType === 'ZBTZ') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.ZBTZID
        state.busParams.FAID = row.FAID
        state.busParams.XSJGID = row.XSJGID
        state.dialogTitle = row.XMMC + '-中标通知书签发'
      }

      if (busType === 'TGYL') {
        state.busParams.id = row.ZBJGGSID
        state.dialogTitle = row.XMMC + '-中标结果公告预览'
      }

      state.dialogVisible = true;
    }

    const workEditFzb = (row, busType, editable, operation) => {
      state.busType = busType;
      state.busParams = {editable: false, id: '', operation: 'view'}
      if (busType === 'XSSQ') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.FAID
        state.busParams.XMID=row.XMID
        state.busParams.XMMC=row.XMMC
        state.busParams.activeTab = 'ZBFA'
        state.dialogTitle = row.XMMC + '-选商申请'
      }


      if (busType === 'XSFA') {
        state.busParams.id = row.FAID
        state.dialogTitle = row.XMMC + '-选商申请方案'
      }

      if (busType === 'XSWJ') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.WJID
        state.dialogTitle = row.XMMC + '-选商文件'
      }

      if (busType === 'XSGG') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.GGID
        state.busParams.WJID = row.WJID
        state.dialogTitle = row.XMMC + '-选商通知'
      }

      if (busType === 'XSBM') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-报名参与'
      }

      if (busType === 'XSHH') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-回函确认'
      }

      if (busType === 'WJLQ') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-招标文件领取情况'
      }

      if (busType === 'TBWJ') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-响应文件上传情况'
      }

      if (busType === 'YDHY') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.PBHYBS
        state.dialogTitle = row.XMMC + '-评标会议预定'
      }

      if (busType === 'PWCQ') {
        state.busParams.id = row.PBHYBS
        state.dialogTitle = row.XMMC + '-评委抽取'
      }

      if (busType === 'ZBJG') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.XSJGID
        state.busParams.FAID = row.FAID
        state.busParams.GGID = row.GGID
        state.dialogTitle = row.XMMC + '-评审资料及评标报告上传'
      }

      if (busType === 'ZBGS') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.JGGSID
        state.busParams.FAID = row.FAID
        state.busParams.XSJGID = row.XSJGID
        state.dialogTitle = row.XMMC + '-成交候选人公示'
      }

      if (busType === 'GSYL') {
        state.busParams.id = row.JGGSID
        state.dialogTitle = row.XMMC + '-成交候选人公示预览'
      }

      if(busType === 'ZBQR'){
        state.busParams.id = row.XSJGID || comFun.newId()
        state.busParams.tableEdit= !row.JGGSID
        if(operation!=='view'){
          state.busParams.operation= row.XSJGID ? 'edit' : 'add'
        }
        state.busParams.FAID = row.FAID
        state.busParams.GGID = row.GGID
        state.dialogTitle = row.XMMC + '-成交结果'
      }

      if (busType === 'ZBTZ') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.ZBTZID
        state.busParams.FAID = row.FAID
        state.busParams.XSJGID = row.XSJGID
        state.dialogTitle = row.XMMC + '-成交通知书签发'
      }

      if (busType === 'TGYL') {
        state.busParams.id = row.ZBJGGSID
        state.dialogTitle = row.XMMC + '-成交结果公告预览'
      }

      state.dialogVisible = true;
    }

    const workEditDjtp = (row, busType, editable, operation) => {
      console.error(row)
      state.busType = busType;
      state.busParams = {editable: false, id: '', operation: 'view'}
      if (busType === 'XSSQ') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.FAID
        state.busParams.XMID=row.XMID
        state.busParams.XMMC=row.XMMC
        state.busParams.activeTab = 'ZBFA'
        state.dialogTitle = row.XMMC + '-选商申请'
      }


      if (busType === 'XSFA') {
        state.busParams.id = row.FAID
        state.dialogTitle = row.XMMC + '-选商申请方案'
      }


      if (busType === 'TPGS') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.GGID
        state.busParams.WJID = row.WJID
        console.log(state.busParams)
        state.dialogTitle = row.XMMC + '-独家谈判公示'
      }

      if (busType === 'TPYL') {
        state.busParams.id = row.GGID
        state.dialogTitle = row.XMMC + '-独家谈判公示预览'
      }

      if (busType === 'TPJL') {
        state.busParams.id = operation === 'add' ? comFun.newId() : row.XSJGID
        state.busParams.FAID = row.FAID
        state.dialogTitle = row.XMMC + '-谈判记录'
      }

      state.dialogVisible = true;
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      closeForm,
      chooseLx

    }
  }

})
</script>

<style scoped>

</style>
