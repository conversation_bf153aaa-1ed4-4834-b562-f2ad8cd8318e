try{

var env = process.env;

var COLOR = env.npm_config_color?true:false;


var BANNER = String.raw`


                   #####                               #                                                                               #                                                
                  #     # #    #  ####  #    # #      #  #      # #####        #    #  ####  #    # #         ##   #    # ##### #    # #    #        #    #  ####  ######   ##   ###### 
                  # ### # #    # #      #    # #     #   #      # #    #       #    # #      #    # #        #  #  #    #   #   #    # #    #        #    # #      #       #  #  #      
                  # ### # #    #  ####  #    # #    #    #      # #####  ##### #    #  ####  #    # # ##### #    # #    #   #   ###### #    #  ##### #    #  ####  #####  #    # #####  
                  # ####  #    #      # #    # #   #     #      # #    #       #    #      # #    # #       ###### #    #   #   #    # #######       #    #      # #      ###### #      
                  #        #  #  #    # #    # #  #      #      # #    #        #  #  #    # #    # #       #    # #    #   #   #    #      #         #  #  #    # #      #    # #      
                   #####    ##    ####   ####  # #       ###### # #####          ##    ####   ####  #       #    #  ####    #   #    #      #          ##    ####  ###### #    # #      
                                                                                                                                                                      

  
 `
BANNER="\u001B[96m"+BANNER+"\u001B[0m";

BANNER+=    "     \u001B[96m欢迎使用@vsui/lib-vsui-auth4-vseaf 鉴权组件(\u001B[94m http://10.68.7.155:7002/package/@vsui/lib-vsui-auth4-vseaf \u001B[0m)，\n\n" +

            "     \u001B[96m该组件仅在胜软NPM(VSNPM)中发布，更多胜软组件请访问：http://10.68.7.155:7002/\u001B[0m\n\n" +

            "     \u001B[96m版本：1.0.0   本组件适配vsui.vue@2.1.0等使用vue3的框架版本 \u001B[0m\n\n" +
  
            "     \u001B[96m开发部门：济南技术研发中心--框架团队 \u001B[0m\n\n" +
    
            "     \u001B[96m开发人员：崔良 (<EMAIL>)  \u001B[0m\n\n\n\n";


(function showBanner() {
  console.log(COLOR ? BANNER : BANNER.replace(/\u001B\[\d+m/g, ''));
})()
}catch(e){return true}



