/**
 * 因为前后端的数据接口与数据结构已经约定好，但后端框架无法同步升级，因此，新的默认过程不能使用，只能使用自定义过程代替，待后端升级后另行通知，请关注@vsui/lib-vsui-auth4-vseaf鉴权库
 * 
 * 这是自定义处理获取用户信息的示例代码，此示例用于自定义@vsui/lib-vsui-auth4-vseaf鉴权库中获取用户信息的过程()
 * 
 * 自定义过程为开发者考虑到了系统接入的复杂情况，因此允许开发者按实际情况自行实现此过程，切记：应对的是整个过程的自行处理，具体细节实现需要用户自行考虑实现
 * 
 * 此代码通过自定义方式，模拟了鉴权库中获取用户信息的过程与数据转换过程，如在项目中，有需要自定义获取用户信息的过程，请按照以下示例过程完成代码
 * 
 * getUserInfo为用户获取用户信息的函数约定，返回Promise类型，resolve过程请返回如下数据结构：
 
    {
        userName:"登录账号字符串,即用户名,不可留空",
        realName:"用户真实名称字符串，如：张三"
        passwd:"用户密码字符串,为安全考虑,可留空",
        token:"无状态模式下的凭据信息字符串,可留空",
        permission:保存后端返回的完整用户信息对象,不可留空,数据结构见下方,
        modulesTree:用户菜单资源数组,请转换后按照固定结构传入,不可留空,数据结构见下方
    }

    permission数据结构如下：

    {
		"resList":[
			{
				"resId": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
				"resName": "Dashboard",
				"resPid": "0",
				"iconClass": "fa fa-tachometer",
				"resPath": "/dashboard",
				...资源其他属性
			},
			{
				"resId": "9B45ADRVGSD45GDED4FTTWSSW128JHFA",
				"resPid": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
				"resName": "控制台",
				"iconClass": "fa fa-podcast",
				"resPath": "/dashboard/workplace",
				...资源其他属性
			},
			...其他资源
		]
		...资源其他属性
	},


    modulesTree的数据结构如下：

	{
		"resId": "9B45ADRVGSD45GDED4FTTWSSW128JHFA",
		"resPid": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
		"resName": "控制台",
		"iconClass": "fa fa-podcast",
		"resPath": "/dashboard",
		"children":[
			{
				"resId": "D54FFDFC3791GTBSE45FDWEF7SFA",
				"resName": "Dashboard",
				"iconClass": "fa fa-bus",
				"resPath": "/dashboard",
				"resPid": "9B45ADRVGSD45GDED4FTTWSSW128JHFA",
				...其他属性
			},
			...其他菜单
		]
		...其他属性
	},
 * 
 * 
 * 
 */
    import {axios,runtimeCfg,store} from '../assets/core/index'
    const vsAuthConfig=runtimeCfg.vs_auth_config;
    
    /**
     * 此函数用于处理用户菜单列表转换为树形结构
     * @param {Array} list 权限中心接口返回数据的resList节点数据，是平级菜单数组
     * @returns 将平级数组中存在的父子关系进行转换，返回一个树形数组
     */
    function convertDataToModuleTree(list){
        if (!list || !list.length) {
            return []
            }
            list=JSON.parse(JSON.stringify(list));
            let treeListMap = {};
            for (let item of list) {
            treeListMap[item.resId] = item
            }
            for (let i = 0; i < list.length; i++) {
            if (list[i].resPid && treeListMap[list[i].resPid]) {
                if (!treeListMap[list[i].resPid].children) {
                treeListMap[list[i].resPid].children = []
                }
                treeListMap[list[i].resPid].children.push(list[i]);
                list.splice(i, 1);
                i--
            }
            }
            return list
    }
    /**
     * 转换资源列表数据为vsui.vue@2.1.0-rc4版本可用的数据结构
     * @param {*} resList 
     * @returns 
     */
    function convertResList(resList){
        if (!resList || !resList.length) {
            return []
        }
        for (let res of resList) {
            //v5版权限资源使用resIcon作为图标
            if(res.resIcon){
                res.iconClass = res.resIcon;
                delete res.resIcon;
            }
            res.resPath = res.resPvalue
            delete res.resPvalue;
        }
        return resList;
    }
    
    export default {
        getUserInfo:function(){
          return new Promise((resolve,reject)=>
          {
            axios.get(`${vsAuthConfig.whereVSEAF}/authm/api/rest/getUserInfo`).then(resp1=>
                {
                    let data1=resp1.data;
                    if(data1 && data1.meta && data1.meta.success)
                    {
                        
                            let userName = data1.data.userLoginName;
                            let realName = data1.data.entUserName;
                            let permission = data1.data;
                            axios.post(`${vsAuthConfig.whereVSEAF}/authm/api/rest/getAppResByAreaUser`,{}
                    ).then(resp2=>{
                                let data2=resp2.data;
                                if (data2 && data2.meta && data2.meta.success)
                                {
                                    let passwd="",token="";
                                    let resList= convertResList(data2.data);
                                    let modulesTree = convertDataToModuleTree(resList);
                                    permission.resList = resList;
                                    permission.loginType= 'WW'
                                    let loginData = { userName, realName, passwd, token, permission, modulesTree};
                                    resolve(loginData);
                                }
                                else{
                                    throw new Error(data2.meta.message);
                                }
                            }).catch(err=>{
                                reject(new Error("获取用户菜单数据或转换数据过程失败:"+err.message))
                            })                   
                    }
                    else{
                        throw new Error(data1.meta.message);
                    }
                }).catch(err=>
                {
                    reject(new Error("获取用户信息失败:"+err.message))
                })
            
          }) 
      }
    }