<template>
  <el-form ref="vForm" class="lui-card-form" label-position="left" label-width="150px"
           size="default">
    <el-row>
      <el-col :span="12">
        <div class="top">
          <el-button type="primary" size="default" @click="yjmbDialogVisible = true">新增</el-button>
        </div>
        <div class="wrap">
          <el-table :data="yjmbTableData" height="400px"
                    class="lui-table"
                    border stripe>
            <EleProTableColumn
                v-for="prop in yjmbTableColumn"
                :col="prop"
                :key="prop.columnKey">
              <template #opration="{ row }">
                <el-button class="lui-table-button" @click="delTemplate(row)">删除</el-button>
              </template>
            </EleProTableColumn>
          </el-table>
        </div>
      </el-col>

      <el-col :span="12">
        <div class="top">
          <el-button type="primary" size="default" @click="zylbDialogVisible = true">新增</el-button>
        </div>
        <div class="wrap">
          <el-table :data="zylbTableData" height="400px"
                    class="lui-table"
                    border stripe>
            <EleProTableColumn
                v-for="prop in zylbTableColumn"
                :col="prop"
                :key="prop.columnKey">
              <template #opration="{ row }">
                <el-button class="lui-table-button" @click="delZylb(row)">删除</el-button>
              </template>
            </EleProTableColumn>
          </el-table>
        </div>
      </el-col>

    </el-row>
    <div style="width: 100%;text-align: center">
      <el-button type="primary" size="default" @click="emits('close')">返回</el-button>
      <el-button type="primary" size="default" @click="saveZyflZrmbGx">保存</el-button>
    </div>
    <el-dialog
        custom-class="lui-dialog"
        title="选择企业/队伍"
        v-model="yjmbDialogVisible"
        width="85%"
        top="3vh"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        destroy-on-close
        @close="onYjmbClose"
    >
      <yjmb :defaultData="yjmbTableData" @close="onClose" @update="updateYjmb" />
    </el-dialog>
    <el-dialog
        custom-class="lui-dialog"
        title="选择专业"
        v-model="zylbDialogVisible"
        width="85%"
        top="3vh"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        destroy-on-close
        @close="onZylbClose"
    >
      <zylb :defaultData="zylbTableData" @close="onZylbClose" @update="updateZylb" />
    </el-dialog>
  </el-form>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import { auth } from "@src/assets/core/index";
import util from "@lib/comFun.js";
import yjmb from "./yjmb.vue";
import zylb from "./zylb.vue";
import {
  getZrmbglPaging, //准入模板分页查询
  getZrmbglDetail, //模板明细
  getZrmbglDisabled, //模板禁用
  getZrmbglEnabled, //模板启用
  deleteZrmbgl, //模板删除
  postZrmbglCopy, //模板复制
  postZrmbglSaveZyflZrmbGx, //保存模板与专业关系
} from "@src/api/sccbsgl.js";
const emits = defineEmits(["close", "update"]);
const yjmbTableColumn = ref([
  { label: "序号", type: "index", width: 55, align: "center" },
  {
    label: "模板类型",
    prop: "MBLXMC",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "模板名称",
    prop: "MBMC",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "描述",
    prop: "MBMS",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作人",
    prop: "CZR",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作时间",
    prop: "CZSJ",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作",
    align: "center",
    showOverflowTooltip: true,
    width: 100,
    slot: "opration",
  },
]);
//专业类别
const zylbTableColumn = ref([
  { label: "序号", type: "index", width: 55, align: "center" },
  {
    label: "专业类别",
    align: "center",
    children: [
      {
        label: "一级",
        prop: "DLZYMC",
        headerAlign: "center",
        align: "left",
        showOverflowTooltip: true,
      },
      {
        label: "二级",
        prop: "ZLZYMC",
        headerAlign: "center",
        align: "left",
        showOverflowTooltip: true,
      },
      {
        label: "三级",
        prop: "XLZYMC",
        headerAlign: "center",
        align: "left",
        showOverflowTooltip: true,
      },
    ],
  },
  {
    label: "操作",
    align: "center",
    showOverflowTooltip: true,
    slot: "opration",
  },
]);
// 引进模板table
const yjmbTableData = ref([]);
// 选择队伍/企业弹窗相关
const yjmbDialogVisible = ref(false);
// 关闭引进模板弹窗
const onYjmbClose = () => {
  yjmbDialogVisible.value = false;
};
// 更新弹窗选择的引进模板数据
const updateYjmb = (val) => {
  yjmbTableData.value = val;
  onYjmbClose();
};
// 删除模板
const delTemplate = (row, index) => {
  yjmbTableData.value.splice(index, 1);
};

// 专业类别table
const zylbTableData = ref([]);
// 选择队伍/企业弹窗相关
const zylbDialogVisible = ref(false);
// 关闭引进模板弹窗
const onZylbClose = () => {
  zylbDialogVisible.value = false;
};
// 更新弹窗选择的引进模板数据
const updateZylb = (val) => {
  zylbTableData.value = val;
  onZylbClose();
};
// 删除模板
const delZylb = (row, index) => {
  zylbTableData.value.splice(index, 1);
};
//当前用户信息
const userinfo = auth.getPermission();
// 保存模板与专业关系
const saveZyflZrmbGx = () => {
  if (!yjmbTableData.value.length) {
    ElMessage.warning("请先选择模板");
    return;
  }
  if (!yjmbTableData.value.some((i) => i.MBLXBM == "QY")) {
    ElMessage.warning("请先选择一个企业模板");
    return;
  }
  if (!zylbTableData.value.length) {
    ElMessage.warning("请先选择专业");
    return;
  }
  postZrmbglSaveZyflZrmbGx({
    MB: yjmbTableData.value.map(({ MBID, MBLXBM }) => ({
      MBID,
      MBLX: MBLXBM,
    })),
    ZY: zylbTableData.value.map(({ ZYBM, ZYBMC }) => ({
      ZYFLDM: ZYBM,
      ZYFLMC: ZYBMC,
    })),
    CZR: userinfo.userName, //"操作人",
    CZRZH: userinfo.userLoginName, //"操作人账号",
    CZSJ: util.getNowTime(), //"操作时间",
  })
    .then((result) => {
      ElMessage.success("保存成功");
      emits("update");
    })
    .catch((err) => {
      ElMessage.error("保存失败");
    });
};
</script>
<style lang="scss" scoped>
// 结构样式
.container {
  display: flex;
  flex-direction: column;
  height: 500px;
  overflow: hidden;
  .main {
    flex: 1;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    .box {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
    }
  }
  .footer {
    height: auto;
    // padding: 10px;
    text-align: center;
  }
}
.top {
  padding: 0 10px;
  text-align: right;
}
.wrap {
  flex: 1;
  padding: 10px;
  overflow: hidden;
}
</style>
