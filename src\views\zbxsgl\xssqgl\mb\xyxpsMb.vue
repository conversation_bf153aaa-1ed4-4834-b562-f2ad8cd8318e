<template>
  <el-container>
    <el-main class="vs-workbench-main-scoped">
      <!-- 查询条件区域 -->
      <el-row :gutter="15" style="padding-bottom:12px">
        <el-col :span="24" align="right">

          <el-button size="default" style="margin-left:10px" @click="downloadFile('初步评审模板.xlsx')" v-if="editable">下载模板</el-button>
          <excleImport url="/backend/commonImport/excelImport"
                       :params="importParams" @getExcelData="getExcelData" buttonType="warning"
                       style="margin-left: 10px" v-if="editable" buttonText="导入模板"></excleImport>

          <el-button size="default" type="success" style="margin-left:10px" @click="saveData" v-if="editable">保存</el-button>
          <el-button size="default" type="primary" style="margin-left:10px" @click="addData" v-if="editable">新增</el-button>
          <!-- <el-button type="primary" style="margin-left:10px" @click="selectModel" v-if="editable">选择模板</el-button> -->
          <el-button size="default" style="margin-left:10px" @click="goBack">返回</el-button>
        </el-col>
      </el-row>
      <!--数据表格-->
      <el-row>
        <el-table ref="datatable91634" :data="tableData" height="520px" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" label="序号" width="50" align="center" :index="indexMethod"></el-table-column>
          <el-table-column prop="PJYS" label="评审因素" width="250" header-align="center" align="left">
            <template #default="scope">
              <el-input v-model="scope.row.PJYS" v-if="editable"></el-input>
              <span v-text="scope.row.PJYS" v-else-if="!editable"></span>
            </template>
          </el-table-column>
          <el-table-column prop="PJBZ" label="评审标准" header-align="center" align="left">
            <template #default="scope">
              <el-input type="textarea" :rows="5" v-model="scope.row.PJBZ" v-if="editable"></el-input>
              <span v-text="scope.row.PJBZ" v-else-if="!editable"></span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120" v-if="editable">
            <template #default="scope">
              <!-- <el-button @click="editRow(scope.row)" type="text" size="mini">编辑</el-button> -->
              <el-button @click="deleteRow(scope.row,scope.$index)" class="lui-table-button" size="mini">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </el-main>
    <!-- 弹出选择模板页面 -->
    <!-- <el-dialog v-model="dialogFormVisible" :title="diagName" top="5vh" width="1000px" :append-to-body="true"
               @close="[dialogFormVisible=false]" :close-on-click-modal="false">
      <selectModel :model="model" :key="{ sqid }" @handleClose="handleClose" :sqid="sqid" :mbl="PSLXDM"/>
    </el-dialog> -->
  </el-container>
</template>
<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
} from 'vue'
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "../../../../lib/axiosUtil";
import comFun from "../../../../lib/comFun";
import excleImport from "@views/components/excleImport";
// import selectModel from "./selectModel";

export default defineComponent({
  name: '',
  components: {excleImport},
  props: {
    model: String,
    XMZBSBS: String,
    saveDataList: Array,
    editable: {
      type: Boolean,
      default: true
    },
    PSLXDM: String,
  },
  setup(props, {emit}) {
    const state = reactive({
      // 当前页码
      currentPage: 1,
      // 每页的数据条数
      pageSize: 10,
      total: 0,
      //弹出框
      diagName: '',
      //弹出框
      // dialogFormVisible: false,
      //表数据
      tableData: [],
      form: {},
      avg: {},
      importParams:{
        url: '/excelI/importPsmb',
        params: {columns: ['PJYS', 'PJBZ']}
      }
    })
    const saveData = () => {
      if(state.tableData.find(item=>!item.PJYS || !item.PJBZ)){
        ElMessage({
          message: "请填写完整！",
          type: "warning"
        });
        return
      }

      ElMessage({
        message: "保存成功！",
        type: "success"
      });
      emit('handleClose', state.tableData, 'XYXPS')

    }
    //序号
    const indexMethod = (index) => {
      return index + state.pageSize * (state.currentPage - 1) + 1;
    }
    //页面数据条数改变时
    const handleSizeChange = (val) => {
      state.currentPage = 1;
      state.pageSize = val;
      queryData();
    }
    //翻页
    const handleCurrentChange = (val) => {
      state.currentPage = val;
      queryData();
    }
    //每页多少条数据
    const handleSelect = (item) => {
      state.activeIndex = item;
      if (item === 2) {
        state.pageSize = 10;
        state.currentPage = 1;
      }
      //state.queryData();
    }
    const queryData = () => {
      if (props.saveDataList.length > 0) {
        state.tableData = props.saveDataList;
      }
    }
    //选择模板
    // const selectModel = (row, index) => {
    //   state.diagName = '选择模板';
    //   state.dialogFormVisible = true;
    //   state.id = row.id;
    // }
    //新增
    const addData = () => {
      let id = comFun.newId()
      state.tableData.push({ZBPJMXBS: id, PJYS: '', PJBZ: ''});
    }
    //关闭模板选择框
    // const handleClose = (avg) => {
    //   state.avg = avg;
    //   //{id: 'xxx', psys: '投标人名称', psbz: '与营业执照、资质证书一致（见扫描件）'},
    //   state.dialogFormVisible = false;
    //   getMbDataList();
    // }
    const getMbDataList = () => {
      state.tableData = [];
      let params = {
        zbpjmbbs: state.avg.ZBPJMBBS,
      }
      axiosUtil.get('/backend/gcyztb/cgwjgl/selectZbpjmbMx', params).then(res => {
        let pageData = res.data
        for (let i in pageData) {
          state.tableData.push(pageData[i])
        }
      })
    }
    //关闭
    const goBack = () => {
      emit('handleClose2')
    }
    //删除
    const deleteRow = (row, index) => {
      console.log(row.id)
      ElMessageBox.confirm("确定要删除选择的数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        //删除逻辑...
        let data = state.tableData;
        data.splice(index, 1);
        state.tableData = data;
        ElMessage({
          message: "删除成功",
          type: "success"
        });
      }).catch(() => {
        ElMessage({
          type: "info",
          message: "已取消删除"
        });
      });
    }

    const downloadFile = (name) => {
      comFun.downLoadTemplate(name)
    }

    const getExcelData = (value) => {
      let importList=value.data || []
      importList.forEach(item=>{
        state.tableData.push({
          ZBPJMXBS: comFun.newId(),
          ...item
        })
      })

    }

    onMounted(() => {
      queryData()
    })
    return {
      ...toRefs(state),
      saveData,
      addData,
      // selectModel,
      goBack,
      indexMethod,
      deleteRow,
      downloadFile,
      getExcelData
      // handleClose,

    }
  }

})
</script>
<style>

.el-dialog__header {
  padding-top: 12px;
}

.el-dialog__body {
  padding-top: 4px;
}

* >>> .el-form-item__content .el-input__inner .el-textarea__inner {
  width: 98%;
}

.bigTitle {
  font-size: 24px;

}
</style>
