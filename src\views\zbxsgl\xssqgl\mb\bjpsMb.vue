<template>
  <el-container>
    <el-main class="vs-workbench-main-scoped">
      <!-- 查询条件区域 -->
      <el-row :gutter="15" style="padding-bottom:12px">
        <el-col :span="24" align="right">
          <el-button size="default" type="success" style="margin-left:10px" @click="saveData" v-if="editable">保存</el-button>
          <el-button size="default" style="margin-left:10px" @click="goBack">返回</el-button>
        </el-col>
      </el-row>
      <!--数据表格-->
      <el-form :model="formData">
        <el-row>
          <!-- 表格 -->
          <el-col :span="24">
            <el-form-item label="报价方式：" label-width="160px" prop="JHFSRQ">
              <el-radio-group v-model="formData.BJFS" size="medium">
                <el-radio v-for="(item, index) in pffsArray" :key="index" :label="item.DMXX" :disabled="!editable">
                  {{ item.DMMC }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="formData.BJFS=='01'">
            <p>1.评标基准价：</p>
            <p>N为有效投标报价的个数，</p>
            <p>（1）当N＜&nbsp;<el-input v-model="formData.GS_N1" style="width:60px"
                                     v-on:input="formData.GS_N1=formData.GS_N1.replace(/[^\d.]/g,'')"
                                     :disabled="!editable"></el-input>
              时，A=所有有效报价的算数平均值；
            </p>
            <p>（2）当
              <el-input v-model="formData.GS_N1" style="width:60px"
                        v-on:input="formData.GS_N1=formData.GS_N1.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              ≤N＜
              <el-input v-model="formData.GS_N2" style="width:60px"
                        v-on:input="formData.GS_N2=formData.GS_N2.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              时，A=所有有效报价去掉一个最高价和一个最低价后的算数平均值；
            </p>
            <p>（3）当N≥
              <el-input v-model="formData.GS_N2" style="width:60px"
                        v-on:input="formData.GS_N2=formData.GS_N2.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              时，A=所有有效报价去掉二个最高价和二个最低价后的算术平均值。
            </p>
            <p>2.投标报价得分：</p>
            <p>(1)与评标基准价相同得100分；</p>
            <p>(2在评标基准价以上，每提高
              <el-input v-model="formData.JZJ_TOP_BFD" style="width:60px"
                        v-on:input="formData.JZJ_TOP_BFD=formData.JZJ_TOP_BFD.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              个百分点，扣
              <el-input v-model="formData.JZJ_TOP_KF" style="width:60px"
                        v-on:input="formData.JZJ_TOP_KF=formData.JZJ_TOP_KF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分，得分为 100 -（P-A）/A/百分点×100×每百分点分值，扣至
              <el-input v-model="formData.JZJ_TOP_JZDF" style="width:60px"
                        v-on:input="formData.JZJ_TOP_JZDF=formData.JZJ_TOP_JZDF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分为止；
            </p>
            <p>(3)在评标基准价以下，每减少
              <el-input v-model="formData.JZJ_BUT_BFD" style="width:60px"
                        v-on:input="formData.JZJ_BUT_BFD=formData.JZJ_BUT_BFD.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              个百分点，扣
              <el-input v-model="formData.JZJ_BUT_KF2" style="width:60px"
                        v-on:input="formData.JZJ_BUT_KF2=formData.JZJ_BUT_KF2.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分，得分为 100 -（A-P）/A/百分点×100×每百分点分值，扣至
              <el-input v-model="formData.JZJ_BUT_JZDF" style="width:60px"
                        v-on:input="formData.JZJ_BUT_JZDF=formData.JZJ_BUT_JZDF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分为止。得分保留小数点后2位；
            </p>
            <p>(4)分值按内插法计算，结果保留小数点后二位。</p>
            <p>(5)投标报价超过最高投标限价的，得0分。</p>
          </el-col>
          <el-col :span="24" v-if="formData.BJFS=='02'">
            <p>1.评标基准价：</p>
            <p>所有有效投标人报价（P）的最低值作为评标基准价（A）。</p>
            <p>2.投标报价得分：</p>
            <p>(1)与评标基准价相同得100分；</p>
            <p>(2在评标基准价以上，每提高
              <el-input v-model="formData.JZJ_TOP_BFD" style="width:60px"
                        v-on:input="formData.JZJ_TOP_BFD=formData.JZJ_TOP_BFD.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              个百分点，扣
              <el-input v-model="formData.JZJ_TOP_KF" style="width:60px"
                        v-on:input="formData.JZJ_TOP_KF=formData.JZJ_TOP_KF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分，得分为 100 -（P-A）/A/百分点×100×每百分点分值，扣至
              <el-input v-model="formData.JZJ_TOP_JZDF" style="width:60px"
                        v-on:input="formData.JZJ_TOP_JZDF=formData.JZJ_TOP_JZDF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分为止；
            </p>
            <p>(4)分值按内插法计算，结果保留小数点后二位。</p>
            <p>(5)投标报价超过最高投标限价的，得0分。</p>
          </el-col>
          <el-col :span="24" v-if="formData.BJFS=='03'">
            <p>1.评标基准价：</p>
            <p>所有有效投标人下浮费率（P）的最高值，作为评标基准价（A）。</p>
            <p>2.投标报价得分：</p>
            <p>(1)与评标基准价相同得100分；</p>
            <p>(3)在评标基准价以下，每减少
              <el-input v-model="formData.JZJ_BUT_BFD" style="width:60px"
                        v-on:input="formData.JZJ_BUT_BFD=formData.JZJ_BUT_BFD.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              个百分点，扣
              <el-input v-model="formData.JZJ_BUT_KF2" style="width:60px"
                        v-on:input="formData.JZJ_BUT_KF2=formData.JZJ_BUT_KF2.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分，得分为 100 -（A-P）/百分点×100×每百分点分值，扣至
              <el-input v-model="formData.JZJ_BUT_JZDF" style="width:60px"
                        v-on:input="formData.JZJ_BUT_JZDF=formData.JZJ_BUT_JZDF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分为止。得分保留小数点后2位；
            </p>
            <p>(4)分值按内插法计算，结果保留小数点后二位。</p>
            <p>(5)投标报价超过最高投标限价的，得0分。</p>
          </el-col>
          <el-col :span="24" v-if="formData.BJFS=='04'">
            <p>1.评标基准下浮率：</p>
            <p>N为有效投标下浮率的个数，</p>
            <p>（1）当N＜&nbsp;<el-input v-model="formData.GS_N1" style="width:60px"
                                     v-on:input="formData.GS_N1=formData.GS_N1.replace(/[^\d.]/g,'')"
                                     :disabled="!editable"></el-input>
              时，A=所有有效下浮率的算数平均值；
            </p>
            <p>（2）当
              <el-input v-model="formData.GS_N1" style="width:60px"
                        v-on:input="formData.GS_N1=formData.GS_N1.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              ≤N＜
              <el-input v-model="formData.GS_N2" style="width:60px"
                        v-on:input="formData.GS_N2=formData.GS_N2.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              时，A=所有有效下浮率去掉一个最高下浮率和一个最低下浮率后的算数平均值；
            </p>
            <p>（3）当N≥
              <el-input v-model="formData.GS_N2" style="width:60px"
                        v-on:input="formData.GS_N2=formData.GS_N2.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              时，A=所有有效报价去掉二个最高下浮率和二个最低下浮率后的算术平均值。
            </p>
            <p>2.投标报价得分：</p>
            <p>(1)与评标基准下浮率相同得100分；</p>
            <p>(2在评标基准下浮率以上，每提高
              <el-input v-model="formData.JZJ_TOP_BFD" style="width:60px"
                        v-on:input="formData.JZJ_TOP_BFD=formData.JZJ_TOP_BFD.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              个百分点，扣
              <el-input v-model="formData.JZJ_TOP_KF" style="width:60px"
                        v-on:input="formData.JZJ_TOP_KF=formData.JZJ_TOP_KF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分，得分为 100 -（P-A）/A/百分点×100×每百分点分值，扣至
              <el-input v-model="formData.JZJ_TOP_JZDF" style="width:60px"
                        v-on:input="formData.JZJ_TOP_JZDF=formData.JZJ_TOP_JZDF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分为止；
            </p>
            <p>(3)在评标基准下浮率以下，每减少
              <el-input v-model="formData.JZJ_BUT_BFD" style="width:60px"
                        v-on:input="formData.JZJ_BUT_BFD=formData.JZJ_BUT_BFD.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              个百分点，扣
              <el-input v-model="formData.JZJ_BUT_KF2" style="width:60px"
                        v-on:input="formData.JZJ_BUT_KF2=formData.JZJ_BUT_KF2.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分，得分为 100 -（A-P）/A/百分点×100×每百分点分值，扣至
              <el-input v-model="formData.JZJ_BUT_JZDF" style="width:60px"
                        v-on:input="formData.JZJ_BUT_JZDF=formData.JZJ_BUT_JZDF.replace(/[^\d.]/g,'')"
                        :disabled="!editable"></el-input>
              分为止。得分保留小数点后2位；
            </p>
            <p>(4)分值按内插法计算，结果保留小数点后二位。</p>
            <!-- <p>(5)投标报价超过最高投标限价的，得0分。</p> -->
          </el-col>
        </el-row>
      </el-form>
    </el-main>
  </el-container>
</template>
<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
} from 'vue'
import {ElMessage} from "element-plus";
import axiosUtil from "../../../../lib/axiosUtil";
import comFun from "../../../../lib/comFun";

export default defineComponent({
  name: '',
  components: {},
  props: {
    model: String,
    bjpsData: Object,
    id: String,
    editable: {
      type: Boolean,
      default: true
    },
    pageFlag: String,
  },
  setup(props, {emit}) {
    const state = reactive({
      ifReadonly: "javascript:return true",
      choose: false,

      formData: {
        BJFS: '01',
        GS_N1: 5,
        GS_N2: 8,
        JZJ_TOP_BFD: 1,
        JZJ_TOP_KF: 0.7,
        JZJ_TOP_JZDF: 75,
        JZJ_BUT_BFD: 1,
        JZJ_BUT_KF2: 0.5,
        JZJ_BUT_JZDF: 75,
      },
      pffsArray: [],
    })
    const saveData = () => {
      let g = /^[1-9]*[1-9][0-9]*$/;
      if (
          comFun.isNumber(state.formData.GS_N1) && state.formData.GS_N1 < 10000 && g.test(state.formData.GS_N1)
          && comFun.isNumber(state.formData.GS_N2) && state.formData.GS_N2 < 10000 && g.test(state.formData.GS_N2)
      ) {
        ElMessage({
          message: "保存成功！",
          type: "success"
        });
        emit('handleClose', state.formData, 'BJPS')
      } else {
        ElMessage({type: 'warning', message: '请输入正确的数字！'});
        return false;
      }


    }
    //查询类别
    const getDmData = () => {
      let param = {DMLBID: 'PFFS'};
      axiosUtil.get('/backend/cbsxx/common/selectDMB', param).then(res => {
        let dmData = res.data

        //评分方式
        state.pffsArray = dmData;
      })

    }

    const goBack = () => {
      emit('handleClose2')
    }
    onMounted(() => {
      state.formData = props.bjpsData
      getDmData();
    })
    return {
      ...toRefs(state),
      saveData,
      goBack,

    }
  }
})

</script>
<style>

.el-dialog__header {
  padding-top: 12px;
}

.el-dialog__body {
  padding-top: 4px;
}

* >>> .el-form-item__content .el-input__inner .el-textarea__inner {
  width: 98%;
}

.bigTitle {
  font-size: 24px;

}
</style>
