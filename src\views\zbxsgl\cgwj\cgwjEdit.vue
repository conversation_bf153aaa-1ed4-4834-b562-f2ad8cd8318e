<template>
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0" size="default">
        <el-row style="height: 40px;">
            <el-col :span="24" class="title" style="text-align: center;">
                {{props.params.XMMC}}
            </el-col>
        </el-row>
        <el-row style="height: 40px;">
            <el-col :span="24" style="text-align: right;">
                <el-button type="primary" @click="changeModel"> 切换模板类型 </el-button>
                <el-button type="primary" @click="recopy"> 重新复制 </el-button>
                <el-button type="success" @click="submit"> 提交 </el-button>
                <el-button @click="close"> 关闭 </el-button>
            </el-col>
        </el-row>
        
        <el-table 
            ref="dataTable" 
            :data="tableData" 
            height="540px" 
            class="lui-table"
            border
            stripe
            size="default" 
            :span-method="arraySpanMethod"
            highlight-current-row>
            <el-table-column prop="title" align="center" min-width="200"></el-table-column>
            <el-table-column prop="text" label="章节" align="center" min-width="200"></el-table-column>
            <el-table-column label="操作" align="center" width="120">
                <template #default="scope">
                    <el-button size="small" type="primary" class="lui-table-button"  @click="editData(scope.row)">编辑
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-row style="line-height: 50px;">
            <el-col :span="24" style="text-align: center;">
                <el-button size="default" type="primary" @click="addData"> 生成招标文件 </el-button>
                <el-button size="default" type="primary" @click="addData"> 查看/不能修改 </el-button>
            </el-col>
        </el-row>
    </el-form>
    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="showModel"
        v-model="showModel"
        title="选择模版类型"
        width="600px">
        <el-radio-group v-model="form.ZBWJLX" size="large" style="padding: 15px 20px;">
            <el-radio v-for="item in modelList" :key="item.DMXX" :label="item.DMXX">{{item.DMMC}}</el-radio>
        </el-radio-group>
        <el-row style="line-height: 50px;">
            <el-col :span="24" style="text-align: center;">
                <el-button size="default" type="primary" @click="comfirmModel"> 确定 </el-button>
                <el-button size="default" @click="showModel = false"> 关闭 </el-button>
            </el-col>
        </el-row>
    </el-dialog>
    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle"
        fullscreen>
        <ggyqhEdit v-if="dialogLx === 'GGYQH'" :data="form" @reload="queryXswjForm" @closeDialog="closeDialog"></ggyqhEdit>
        <cbsxzEdit v-if="dialogLx === 'ONE'" :data="form" @reload="queryXswjForm" @closeDialog="closeDialog"></cbsxzEdit>
        <psbfEdit v-if="dialogLx === 'TWO_PSBF'" :data="form" @reload="queryXswjForm" @closeDialog="closeDialog"></psbfEdit>
        <zgcsEdit v-if="dialogLx === 'TWO_ZZCS'" :data="form" @reload="queryXswjForm" @closeDialog="closeDialog"></zgcsEdit>
        <jszhdfEdit v-if="dialogLx === 'TWO_JSPF' || dialogLx === 'TWO_ZHPF'" :data="form" :type="dialogLx === 'TWO_JSPF'?'JSPF':'ZHPF'" @closeDialog="closeDialog"></jszhdfEdit>
    </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import ggyqhEdit from "./ggyqhEdit.vue";
import cbsxzEdit from "./cbsxzEdit.vue";
import psbfEdit from "./psbfEdit.vue";
import zgcsEdit from "./zgcsEdit.vue";
import jszhdfEdit from "./jszhdfEdit.vue";
const props = defineProps({
    params: {
        type: Object,
        default: () => ({
            FAID:'19EE5A4D88764254A1EF8249528F7F6C',
            XMMC: '测试测试111',
            id: '1111'
        })
    }
});
const emits = defineEmits(["closeCgwj"]);
const form = ref({});
// flag_为合并单元格标记
const tableData = ref([
    { id:'GGYQH', flag_title: '1', flag_text: '1', title: '公告/邀请函信息', text: '' },
    { id:'ONE', flag_title: '2', flag_text: '2', title: '第一章、承包商须知', text: '' },
    { id:'TWO_PSBF', flag_title: '3', flag_text: '3', title: '第二章、评审办法', text: '评审办法' },
    { id:'TWO_PSCX', flag_title: '3', flag_text: '4', title: '第二章、评审办法', text: '评审程序' },
    { id:'TWO_ZZCS', flag_title: '3', flag_text: '5', title: '第二章、评审办法', text: '资格初审' },
    { id:'TWO_JSPF', flag_title: '3', flag_text: '6', title: '第二章、评审办法', text: '技术评分表' },
    { id:'TWO_ZHPF', flag_title: '3', flag_text: '7', title: '第二章、评审办法', text: '综合评分表' },
    { id:'HTTK', flag_title: '4', flag_text: '8', title: '第三章、合同条款', text: '' },
    { id:'FOUR', flag_title: '5', flag_text: '9', title: '第四章', text: '' },
    { id:'FIVE', flag_title: '6', flag_text: '9', title: '第五章', text: '' },
    { id:'SIX', flag_title: '7', flag_text: '9', title: '第六章', text: '' },
    { id:'SEVEN', flag_title: '8', flag_text: '10', title: '第七章、响应文件格式', text: '' },
    
])
onMounted(() => {
    queryXswjForm();
    queryModelList();
});

// 查询选商文件数据
const queryXswjForm = () => {
    axiosUtil.get('/backend/xsgl/xswj/queryXswjForm', {
        FAID: props.params.FAID
    }).then(res=>{
        form.value = res.data.form;
    })
}

// 模版类型
const modelList = ref([]);
const queryModelList = () => {
    axiosUtil.get('/backend/cbsxx/common/selectDMB', {
        DMLBID: "ZBWJLX"
    }).then(res=>{
        modelList.value = res.data;
    })
}

// 编辑
const showDialog = ref(false);
const dialogTitle = ref('');
const dialogLx = ref('');
const editData = (row) => {
    if(!form.value.ZBWJLX){
        ElMessage.warning('请先选择模板类型!');
        return;
    }
    if(row.text){
      dialogTitle.value = row.title + '-' + row.text;
    }else {
      dialogTitle.value = row.title;
    }

    dialogLx.value = row.id;
    showDialog.value = true;
}

const closeDialog = () => {
    showDialog.value = false;
}

// 选择模版类型
const showModel = ref(false);
const changeModel = () => {
    showModel.value = true;
}

// 选择模版确定
const comfirmModel = () => {
    if(!form.value.ZBWJLX){
        ElMessage.warning('请先选择模板类型!');
        return;
    }
    ElMessageBox.confirm('更换模版会清除数据，确定要选择该模板吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        axiosUtil.post('/backend/xsgl/xswj/changeModel', {
            WJID: props.params.id,
            FAID: props.params.FAID,
            ZBWJLX: form.value.ZBWJLX,
            CJRZH: VSAuth.getAuthInfo().permission.userLoginName,
            CJRXM: VSAuth.getAuthInfo().permission.userName,
            CJDWID: VSAuth.getAuthInfo().permission.orgnaId
        }).then(res=>{
            if(res.data.success){
                queryXswjForm();
                showModel.value = false;
            }else{
                ElMessage.error('保存失败！');
            }
        })
        
    }).catch(() => {
        
    });
    
}

const recopy = () => {
    ElMessageBox.confirm('你确定要重新复制吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        ElMessage.success('复制成功!');
    }).catch(() => {
    
    });
}

const submit = () => {
    ElMessageBox.confirm('你确定要提交吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        axiosUtil.post('/backend/xsgl/xswj/saveXswj', {
            WJID: props.params.id,
            FAID: props.params.FAID,
        }).then(res=>{
            if(res.data.success){
                ElMessage.success('提交成功!');
            }else{
                ElMessage.error('保存失败！');
            }
        })
        
    }).catch(() => {
    
    });
}

const close = () => {
    emits('closeCgwj');
}

const arraySpanMethod = ({row,column,rowIndex,columnIndex}) => {
    if(columnIndex === 0){
        let nameSpan = getSpanNumber(tableData.value,"flag_title");
        return {
            rowspan: nameSpan[rowIndex],
            colspan: 1,
        };
    }else if(columnIndex === 1 || columnIndex === 2){
        let nameSpan = getSpanNumber(tableData.value,"flag_text");
        return {
            rowspan: nameSpan[rowIndex],
            colspan: 1,
        };
    }
    
}

// 合并单元格
const getSpanNumber = (data, prop) => {
    //data要处理的数组，prop要合并的属性，比如name
    //数组的长度，有时候后台可能返回个null而不是[]
    let length = Array.isArray(data) ? data.length : 0;
    if (length > 0) {
        //用于标识位置
        let position = 0;
        //用于对比的数据
        let temp = data[0][prop];
        //要返回的结果
        let result = [1];
        //假设数据是AABCC，我们的目标就是返回20120
        for (let i = 1; i < length; i++) {
            if (data[i][prop] == temp) {
                //标识位置的数据加一
                result[position] += 1;
                //当前位置添0
                result[i] = 0;
            } else {
                //不相同时，修改标识位置，该位置设为1，修改对比值
                position = i;
                result[i] = 1;
                temp = data[i][prop];
            }
        }
        //返回结果
        return result;
    } else {
        return [0];
    }
}

defineExpose({});
</script>

<style scoped>
.title{
    font-size: 22px;
    color: #CC0000;
    font-weight: bold;
}
</style>