<template>
    <div>
        <el-form :model="modelValue" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
                 label-width="241px"
                 size="default" @submit.prevent>
            <el-row :gutter="0" class="grid-row">
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="所属单位：" prop="SSDWID">
                        <div style="margin-left: 10px">{{modelValue.XMXX.SSDWMC}}</div>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="项目类别：" prop="XMLB" label-width="180px">
                        <el-cascader v-model="modelValue.XMXX.XMLB" :options="ZRZYTree" filterable :disabled="!editable"
                                     :props="{label:'ZYMC',value:'ZY<PERSON>',emitPath: false}"
                                     clearable/>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="是否框架项目：" prop="XMXX.SFKJXM">
                        <el-radio-group v-model="modelValue.XMXX.SFKJXM" :disabled="!editable">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="16" class="grid-cell">
                    <el-form-item :label="`${modelValue.SQLX==='FZB' ? '采购': ''}项目名称：`" prop="XMID">
                        <el-input v-model="modelValue.XMXX.XMMC" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable && !params.XMID">
                                <el-button @click="dialogCGXMVisible=true">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item :label="`${modelValue.SQLX==='FZB' ? '采购': ''}项目编号：`" prop="XMBH">
                        <el-input v-model="modelValue.XMXX.XMBH" type="text" placeholder="请输入" disabled></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="资金来源：" prop="ZJLY">
                        <el-select v-model="modelValue.ZJLY" class="full-width-input" clearable :disabled="!editable">
                            <el-option v-for="(item, index) in ZJLYOptions" :key="index" :label="item.DMMC"
                                       :value="item.DMXX"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="选商方式：" prop="XSFS" label-width="180px">
                        <el-select v-model="modelValue.XSFS" class="full-width-input" clearable disabled>
                            <el-option v-for="(item, index) in XSFSOptions" :key="index" :label="item.DMMC"
                                       :value="item.DMXX"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="选商组织方式：" prop="ZZFS">
                        <el-select v-model="modelValue.ZZFS" class="full-width-input" clearable :disabled="!editable">
                            <el-option v-for="(item, index) in ZZFSOptions" :key="index" :label="item.DMMC"
                                       :value="item.DMXX"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="16" class="grid-cell">
                    <el-form-item label="项目批复文件或立项说明：" prop="PFHLXSM">
                        <el-input v-model="modelValue.PFHLXSM" type="text" placeholder="请输入"
                                  :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="服务期限：" prop="FWQX">
                        <el-input v-model="modelValue.FWQX" type="text" placeholder="请输入"
                                  :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="选取中标人数量：" prop="XQZBRSL">
                        <el-input v-model="modelValue.XQZBRSL" type="text" placeholder="请输入"
                                  :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item :label="`计划发标时间：`" prop="XSWJ.JHFBSJ" label-width="180px">
                        <el-date-picker
                                v-model="modelValue.XSWJ.JHFBSJ"
                                :disabled="!editable"
                                type="datetime"
                                clearable
                                style="width: 100%"
                                placeholder="请选择"
                                format="YYYY-MM-DD HH:mm"
                                value-format="YYYY-MM-DD HH:mm:ss"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item :label="`计划${modelValue.SQLX==='FZB' ? '评审（谈判）': '开标'}时间：`" prop="XSWJ.JHKBSJ">
                        <el-date-picker
                                v-model="modelValue.XSWJ.JHKBSJ"
                                :disabled="!editable"
                                type="datetime"
                                clearable
                                style="width: 100%"
                                placeholder="请选择"
                                format="YYYY-MM-DD HH:mm"
                                value-format="YYYY-MM-DD HH:mm:ss"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="申报意见（选商方式选用说明）：" prop="SBYJ" :rules="rules.SBYJ">
                        <el-input v-model="modelValue.SBYJ" type="text" placeholder="请输入"
                                  :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8" class="grid-cell" v-if="modelValue.XSFS!=='DJTP'">
                    <el-form-item label="是否分标段：" prop="SFFBD">
                        <el-radio-group v-model="modelValue.SFFBD" :disabled="!editable" @change="changeSFFBD">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell" v-if="modelValue.XSFS!=='DJTP'">
                    <el-button style="margin-top: 4px;margin-left: 10px" type="primary" @click="addBD"
                               v-if="modelValue.SFFBD==='1' && editable">新增标段
                    </el-button>
                </el-col>

                <el-col :span="8" class="grid-cell" v-if="modelValue.SFFBD==='0' && modelValue.BDList[0]">
                    <el-form-item label="预计金额（万元）：" :prop="`BDList.0.YJJE`" :rules="rules.YJJE">
                        <el-input v-model="modelValue.BDList[0].YJJE" type="text" placeholder="请输入"
                                  :disabled="!editable"
                                  @input="modelValue.BDList[0].YJJE=modelValue.BDList[0].YJJE.replace(/^([0-9-]\d*\.?\d{0,4})?.*$/, '$1')"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell"
                        v-if="modelValue.XSFS!=='GKZB' && modelValue.SFFBD==='0' && modelValue.BDList[0]">
                    <el-form-item label="拟邀请单位:" prop="NYQDW">
                        <el-button style="margin-left: 10px" type="primary" @click="openDWXZ(modelValue.BDList[0])"
                                   v-if="editable">增加
                        </el-button>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell"
                        v-if="modelValue.XSFS!=='GKZB' && modelValue.SFFBD==='0' && modelValue.BDList[0]">
                    <el-table ref="datatable91634" :data="modelValue.BDList[0].DWList" height="250px"
                              :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                        <el-table-column prop="CBSDWQC" label="单位名称" align="center"
                                         :show-overflow-tooltip="true" min-width="160"></el-table-column>
                        <!--            <el-table-column prop="DWMC" label="队伍名称" align="center"-->
                        <!--                             :show-overflow-tooltip="true" min-width="160"></el-table-column>-->
                        <el-table-column prop="LXR" label="联系人" align="center"
                                         :show-overflow-tooltip="true" width="100">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`BDList.0.DWList.${$index}.LXR`" label-width="0"
                                              :rules="tableRules.LXR" v-if="editable">
                                    <el-input v-model="row.LXR" type="text" placeholder="请输入" clearable
                                              :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="LXDH" label="联系电话" align="center"
                                         :show-overflow-tooltip="true" width="160">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`BDList.0.DWList.${$index}.LXDH`" label-width="0"
                                              :rules="tableRules.LXDH" v-if="editable">
                                    <el-input v-model="row.LXDH" type="text" placeholder="请输入" clearable
                                              :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="EMAIL" label="邮箱地址" align="center"
                                         :show-overflow-tooltip="true" width="160">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`BDList.0.DWList.${$index}.EMAIL`" label-width="0"
                                              :rules="tableRules.EMAIL" v-if="editable">
                                    <el-input v-model="row.EMAIL" type="text" placeholder="请输入" clearable
                                              :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="CZ" label="操作" align="center" width="100" fixed="right" v-if="editable">
                            <template #default="scope">
                                <el-button size="small" class="lui-table-button" type="primary"
                                           @click="delDW(modelValue.BDList[0].DWList,scope.$index)">删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>


                <el-col :span="24" class="grid-cell" v-if="modelValue.BDList.length>0 && modelValue.SFFBD==='1'">
                    <el-tabs
                            v-model="ActiveTab"
                            type="border-card"
                            :closable="editable"
                            @tab-remove="delBD"
                    >
                        <el-tab-pane
                                v-for="(item,index) in modelValue.BDList"
                                :key="index"
                                :label="'标段：'+(item.BDMC || '')"
                                :name="index+''">
                            <el-row :gutter="0" class="grid-row" style="padding: 0 20px 20px;">
                                <el-col :span="16" class="grid-cell">
                                    <el-form-item label="标段名称：" :prop="`BDList.${index}.BDMC`" :rules="rules.BDMC">
                                        <el-input v-model="item.BDMC" type="text" placeholder="请输入"
                                                  :disabled="!editable"></el-input>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="8" class="grid-cell border-right">
                                    <el-form-item label="预计金额（万元）：" :prop="`BDList.${index}.YJJE`" :rules="rules.YJJE">
                                        <el-input v-model="item.YJJE" type="text" placeholder="请输入"
                                                  :disabled="!editable"
                                                  @input="item.YJJE=item.YJJE.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')"></el-input>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24" class="grid-cell border-right no-border-bottom">
                                    <el-form-item label="标段内容说明：" :prop="`BDList.${index}.BDNRSM`"
                                                  :rules="rules.BDNRSM">
                                        <el-input v-model="item.BDNRSM"
                                                  type="textarea"
                                                  :rows="3" placeholder="请输入" :disabled="!editable"></el-input>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24" class="grid-cell border-right no-border-bottom">
                                    <el-form-item label="资质要求：" :prop="`BDList.${index}.ZZYQ`" :rules="rules.ZZYQ">
                                        <el-input v-model="item.ZZYQ" type="textarea"
                                                  :rows="3" placeholder="请输入" :disabled="!editable"></el-input>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24" class="grid-cell" v-if="modelValue.XSFS!=='GKZB'">
                                    <el-form-item label="拟邀请单位:" prop="NYQDW">
                                        <el-button style="margin-left: 10px" type="primary" @click="openDWXZ(item)"
                                                   v-if="editable">增加
                                        </el-button>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24" class="grid-cell" v-if="modelValue.XSFS!=='GKZB'">
                                    <el-table ref="datatable91634" :data="item.DWList" height="250px"
                                              :border="true" :show-summary="false" size="default" :stripe="false"
                                              class="lui-table"
                                              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                                        <el-table-column type="index" width="60" fixed="left" label="序号"
                                                         align="center"/>
                                        <el-table-column prop="CBSDWQC" label="单位名称" align="center"
                                                         :show-overflow-tooltip="true"
                                                         min-width="160"></el-table-column>
                                        <!--            <el-table-column prop="DWMC" label="队伍名称" align="center"-->
                                        <!--                             :show-overflow-tooltip="true" min-width="160"></el-table-column>-->
                                        <el-table-column prop="LXR" label="联系人" align="center"
                                                         :show-overflow-tooltip="true" width="100">
                                            <template #default="{row,$index}">
                                                <el-form-item label="" :prop="`BDList.${index}.DWList.${$index}.LXR`"
                                                              label-width="0" :rules="tableRules.LXR" v-if="editable">
                                                    <el-input v-model="row.LXR" type="text" placeholder="请输入" clearable
                                                              :disabled="!editable">
                                                    </el-input>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="LXDH" label="联系电话" align="center"
                                                         :show-overflow-tooltip="true" width="160">
                                            <template #default="{row,$index}">
                                                <el-form-item label="" :prop="`BDList.${index}.DWList.${$index}.LXDH`"
                                                              label-width="0" :rules="tableRules.LXDH" v-if="editable">
                                                    <el-input v-model="row.LXDH" type="text" placeholder="请输入" clearable
                                                              :disabled="!editable">
                                                    </el-input>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="EMAIL" label="邮箱地址" align="center"
                                                         :show-overflow-tooltip="true" width="160">
                                            <template #default="{row,$index}">
                                                <el-form-item label="" :prop="`BDList.${index}.DWList.${$index}.EMAIL`"
                                                              label-width="0" :rules="tableRules.EMAIL" v-if="editable">
                                                    <el-input v-model="row.EMAIL" type="text" placeholder="请输入"
                                                              clearable :disabled="!editable">
                                                    </el-input>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="CZ" label="操作" align="center" width="100" fixed="right"
                                                         v-if="editable">
                                            <template #default="scope">
                                                <el-button size="small" class="lui-table-button" type="primary"
                                                           @click="delDW(item.DWList,scope.$index)">删除
                                                </el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-col>


                            </el-row>
                        </el-tab-pane>
                    </el-tabs>
                </el-col>

                <el-col :span="24" class="grid-cell no-border-bottom"
                        v-if="modelValue.SFFBD==='0' && modelValue.BDList[0]">
                    <el-form-item label="项目内容说明：" :prop="`BDList.0.BDNRSM`" :rules="rules.BDNRSM">
                        <el-input v-model="modelValue.BDList[0].BDNRSM" type="textarea"
                                  :rows="3" placeholder="请输入" :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell no-border-bottom"
                        v-if="modelValue.SFFBD==='0' && modelValue.BDList[0]">
                    <el-form-item label="资质要求：" :prop="`BDList.0.ZZYQ`" :rules="rules.ZZYQ">
                        <el-input v-model="modelValue.BDList[0].ZZYQ" type="textarea"
                                  :rows="3" placeholder="请输入" :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="modelValue.SQLX==='FZB'">
                    <el-form-item label="非招标原因说明：" prop="FZBYYSM">
                        <el-input v-model="modelValue.FZBYYSM" type="textarea"
                                  :rows="3" placeholder="请输入" clearable :disabled="!editable">
                        </el-input>
                    </el-form-item>
                </el-col>


                <el-col :span="24" class="grid-cell">
                    <el-form-item label="附件资料:" prop="FJZL">
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="grid-cell">
                    <vsFileUploadTable style="width: 100%;height: 150px" YWLX="CGSQ" :key="params.id"
                                       :busId="params.id" v-model:fileTableData="fileTableData" :editable="editable"/>
                </el-col>
            </el-row>
        </el-form>
        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogCGXMVisible"
                v-model="dialogCGXMVisible"
                title="采购项目选择"
                z-index="1000"
                top="6vh"
                width="1100px">
            <div>
                <xmChoose @close="dialogCGXMVisible=false" @submit="getCgxmRes" :params="params"/>
            </div>
        </el-dialog>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogYQDWVisible"
                v-model="dialogYQDWVisible"
                title="邀请单位选择"
                z-index="1000"
                top="6vh"
                width="1100px">
            <div>
                <dwChoose @close="dialogYQDWVisible=false" @submit="getYqdwRes" :params="params"/>
            </div>
        </el-dialog>
    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import comFun from "@lib/comFun";
    import vsFileUploadTable from "@views/components/vsFileUploadTable";
    import axiosUtil from "@lib/axiosUtil";
    import xmChoose from "@views/zbxsgl/xssqgl/xmChoose";
    import dwChoose from "@views/zbxsgl/xssqgl/dwChoose";
    import vsAuth from "@lib/vsAuth";

    export default defineComponent({
        name: '',
        components: {vsFileUploadTable, xmChoose, dwChoose},
        props: {
            params: {
                type: Object,
                required: true
            },
            modelValue: {
                type: Object,
                default: {}
            }
        },
        setup(props, {emit}) {
            const state = reactive({
                editable: props.params.editable,
                FAID: props.params.id,
                rules: {
                    'XMXX.SFKJXM': [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    XMID: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    ZJLY: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    ZZFS: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    SBYJ: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    SFFBD: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    YJJE: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    BDMC: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    BDNRSM: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    ZZYQ: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    FZBQX: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    FZBYYSM: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    FWQX: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    XQZBRSL: [{
                        required: props.params.XSFS !== 'DJTP',
                        message: '字段值不可为空',
                    }],

                    XSWJ: {
                        JHFBSJ: [{
                            required: props.params.XSFS !== 'DJTP',
                            message: '字段值不可为空',
                        }],
                        JHKBSJ: [{
                            required: true,
                            message: '字段值不可为空',
                        }],
                    }
                },
                tableRules: {
                    TJLY: [{
                        required: true,
                        message: '待填写',
                    }],
                    LXR: [{
                        required: true,
                        message: '待填写',
                    }],
                    EMAIL: [{
                        required: true,
                        message: '待填写',
                    }],
                    LXDH: [{
                        required: true,
                        message: '待填写',
                    }],
                },
                ActiveTab: '0',
                fileTableData: [],

                ZRZYTree: [],
                XSFSOptions: [],
                SSDWOptions: [],
                ZZFSOptions: [],
                ZJLYOptions: [],

                dialogCGXMVisible: false,
                dialogYQDWVisible: false,

                BDRow: []
            })


            const addBD = () => {
                props.modelValue.BDList.push({
                    FABDID: comFun.newId(),
                    FAID: state.FAID,
                    CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
                    CJRXM: vsAuth.getAuthInfo().permission.userName,
                    CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
                    CJSJ: comFun.getNowTime(),
                    DWList: [],
                })
                state.ActiveTab = props.modelValue.BDList.length - 1 + ''
            }

            const delBD = (index) => {
                props.modelValue.BDList.splice(Number(index), 1)
            }

            const delDW = (list, index) => {
                list.splice(index, 1)
            }

            const openDWXZ = (item) => {
                state.BDRow = item
                state.dialogYQDWVisible = true
            }

            const changeSFFBD = (value) => {
                if (value === '0') {
                    if (props.modelValue.BDList.length > 0) {
                        props.modelValue.BDList.splice(1, props.modelValue.BDList.length - 1)
                        state.ActiveTab = '0'
                    } else {
                        addBD()
                    }
                }
            }

            const getCgxmRes = (value) => {
                props.modelValue.XMXX = value
                props.modelValue.XMID = value.XMID
                state.dialogCGXMVisible = false
            }

            const getYqdwRes = (value) => {
                value.forEach(item => {
                    if (!state.BDRow.DWList.find(ii => ii.DWWYBS === item.DWWYBS)) {
                        state.BDRow.DWList.push({
                            ...item,
                            YQDWID: comFun.newId(),
                            FAID: state.FAID,
                            FABDID: state.BDRow.FABDID,
                            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
                            CJRXM: vsAuth.getAuthInfo().permission.userName,
                            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
                            CJSJ: comFun.getNowTime(),
                        })
                    }
                })
                state.dialogYQDWVisible = false
            }

            const getDMBData = async (DMLBID, resList) => {
                let params = {
                    DMLBID
                }
                const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
                state[resList] = res.data
            }

            const instance = getCurrentInstance()
            const validateForm = () => {
                return new Promise((resolve, reject) => {
                    instance.proxy.$refs['vForm'].validate(valid => {
                        if (valid) {
                            if (props.modelValue.BDList.length === 0) {
                                reject('请添加标段')
                                return
                            }
                            if (props.modelValue.XSFS !== 'GKZB' && props.modelValue.XSFS !== 'GKJJ' && props.modelValue.XSFS !== 'GKJB' && props.modelValue.BDList.find(item => item.DWList.length === 0)) {
                                reject('请添加邀请单位')
                                return
                            }

                            if (props.modelValue.XSFS === 'DJTP' && props.modelValue.BDList.find(item => item.DWList.length > 1)) {
                                reject('独家谈判只能邀请一家单位')
                                return
                            }


                            resolve(true)
                        } else {
                            reject(`请完善${props.modelValue.SQLX === 'FZB' ? '采购方案' : '招标方案'}信息`)
                        }
                    })
                })
            }

            const getZrzyList = () => {
                let params = {}
                axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
                    state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0')
                });
            }

            onMounted(() => {
                getZrzyList()
                getDMBData("XSFS", "XSFSOptions")
                // getDMBData("SSDW", "SSDWOptions")
                getDMBData("ZZFS", "ZZFSOptions")
                getDMBData("ZJLY", "ZJLYOptions")
                if (props.params.operation === 'add' && props.params.XSFS === 'DJTP') {
                    props.modelValue.SFFBD = '0'
                    changeSFFBD('0')
                }
            })

            return {
                ...toRefs(state),
                addBD,
                getCgxmRes,
                changeSFFBD,
                delBD,
                delDW,
                getYqdwRes,
                validateForm,
                openDWXZ

            }
        }

    })
</script>

<style scoped>
    :deep(.lui-card-form .border-right .el-form-item__content) {
        border-right: 1px solid rgba(227, 234, 248, 1) !important;
    }
</style>
