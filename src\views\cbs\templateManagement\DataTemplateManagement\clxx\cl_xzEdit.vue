<template>
    <el-form
            :model="form"
            ref="vForm"
            :rules="rules"
            label-position="right"
            label-width="120px"
            size="default"
            class="lui-card-form">
        <el-row class="grid-row">
            <el-col :span="8" class="grid-cell">
                <el-form-item label="车牌号" prop="CPH">
                    <el-input v-model="form.CPH" :disabled="!editable" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="车辆名称" prop="CLMC">
                    <el-input v-model="form.CLMC" :disabled="!editable" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="生产厂家" prop="SCCJ">
                    <el-input v-model="form.SCCJ" :disabled="!editable" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row class="grid-row">
            <el-col :span="8" class="grid-cell">
                <el-form-item label="品牌型号" prop="PPXH">
                    <el-input v-model="form.PPXH" :disabled="!editable" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="车辆类型" prop="CLLXMC">
                    <el-input v-model="form.CLLXMC" disabled style="width: 75%" placeholder="请选择"></el-input>
                    <el-button type="success" size="small" @click="checkCllx" v-if="editable">选择</el-button>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="吨（座）位" prop="DZW">
                    <el-input v-if="state.dzwFlag === 'input'" :disabled="!editable" v-model="form.DZW" clearable placeholder="请输入"></el-input>
                    <el-select v-else v-model="form.DZW" :disabled="!editable" placeholder="请选择">
                        <el-option
                                v-for="item in state.dzwArray"
                                :key="item.DW"
                                :label="item.DW"
                                :value="item.DW">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row class="grid-row">
            <el-col :span="8" class="grid-cell">
                <el-form-item label="规格型号" prop="GGXH">
                    <el-input v-model="form.GGXH" :disabled="!editable" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="大架号" prop="DJH">
                    <el-input v-model="form.DJH" :disabled="!editable" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="发动机编号" prop="FDJBH">
                    <el-input v-model="form.FDJBH" :disabled="!editable" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row class="grid-row">
            <el-col :span="8" class="grid-cell">
                <el-form-item label="年检日期" prop="NJRQ">
                    <el-date-picker
                        :disabled="!editable"
                            v-model="form.NJRQ"
                            type="date"
                            placeholder="请选择日期"
                            value-format="YYYY-MM-DD"
                            style="width: 100%"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="有效结束日期" prop="YXQJSRQ">
                    <el-date-picker
                        :disabled="!editable"
                            v-model="form.YXQJSRQ"
                            type="date"
                            placeholder="请选择日期"
                            value-format="YYYY-MM-DD"
                            style="width: 100%"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="年检机构" prop="NJJG">
                    <el-input :disabled="!editable" v-model="form.NJJG" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row class="grid-row">
            <el-col :span="8" class="grid-cell">
                <el-form-item label="规定使用年限" prop="GDSYNX">
                    <el-input :disabled="!editable" v-model="form.GDSYNX" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="购置时间" prop="GZSJ">
                    <el-date-picker
                        :disabled="!editable"
                            v-model="form.GZSJ"
                            type="date"
                            placeholder="请选择日期"
                            value-format="YYYY-MM-DD"
                            style="width: 100%"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="保单编号" prop="BDBH">
                    <el-input :disabled="!editable" v-model="form.BDBH" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row class="grid-row">
            <el-col :span="8" class="grid-cell">
                <el-form-item label="保单机构" prop="BDJG">
                    <el-input :disabled="!editable" v-model="form.BDJG" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="备注" prop="BZ">
                    <el-input :disabled="!editable" v-model="form.BZ" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

      <el-row class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="附件11" prop="FJ">
            <vsfileupload
                :maxSize="10"
                :editable="editable"
                :busId="form.CLZSJID"
                :key="form.CLZSJID"
                ywlb="CLFJ"
                busType="dwxx"
                :limit="100"
            ></vsfileupload>
          </el-form-item>
        </el-col>
      </el-row>

        <el-row class="btn" align="center" justify="center" v-if="editable">
            <el-button type="primary" @click="confirm">保存</el-button>
            <el-button @click="handleReturn">返回</el-button>
        </el-row>
    </el-form>

    <el-dialog
            custom-class="lui-dialog"
            title="选择车辆类型"
            v-model="editVisible"
            width="700px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            destroy-on-close
            @close="
              () => {}
            "
    >
        <checkClxxList @close="editVisible = false" @checkClxx="checkClxx"
        />
    </el-dialog>
</template>

<script setup>
    import {
        ref,
        reactive,
        onMounted,
        defineProps,
        defineEmits,
        watch,
    } from "vue";
    import axiosUtil from "@lib/axiosUtil";
    import {v4 as uuidv4} from "uuid";
    import {ElMessage, ElMessageBox} from "element-plus";
    import checkClxxList from "./checkClxxList";
    import {getCllxList} from "@src/api/sccbsgl";
    import vsfileupload from "@src/views/components/vsfileupload.vue";

    const activeName = ref("zyglqx");
    const props = defineProps({
        editData: {
            type: Object,
            default: () => {
            },
        },
      // 是否查看模式
      editable: {
        type: Boolean,
        default: true
      }
    });

    const emits = defineEmits(["close"]);
    const defaultData = reactive({
        CPH: '',
        CLMC: '',
        SCCJ: '',
        PPXH: '',
        CLLXMC: '',
        DZW: '',
        GGXH: '',
        DJH: '',
        FDJBH: '',
        NJRQ: '',
        YXQJSRQ: '',
        NJJG: '',
        GDSYNX: '',
        GZSJ: '',
        BDBH: '',
        BDJG: '',
        BZ: '',
    });
    const form = ref({
        CPH: '',
        CLMC: '',
        SCCJ: '',
        PPXH: '',
        CLLXMC: '',
        DZW: '',
        GGXH: '',
        DJH: '',
        FDJBH: '',
        NJRQ: '',
        YXQJSRQ: '',
        NJJG: '',
        GDSYNX: '',
        GZSJ: '',
        BDBH: '',
        BDJG: '',
        BZ: '',
    });
    watch(
        () => props.editData,
        (val) => {
            form.value = val ? Object.assign(form.value, val) : defaultData;
        },
        {
            immediate: true,
        }
    );
    onMounted(() => {

    });

    const state = reactive({
        // 弹窗
        editVisible: false,
        // 吨（座）位 下拉数据
        dzwArray: [],
        // 吨（座）位 判断展示， input输入框， 其余的下拉框
        dzwFlag: '',
    })

    const rules = ref({
        CPH: [{required: true, message: "请输入车牌号", trigger: "blur,change"}],
        CLMC: [{required: true, message: "请输入车辆名称", trigger: "blur,change"}],
        SCCJ: [{required: true, message: "请输入生产厂家", trigger: "blur,change"}],
        PPXH: [{required: true, message: "请输入品牌型号", trigger: "blur,change"}],
        CLLXMC: [{required: true, message: "请选择车辆类型", trigger: "blur,change"}],
        DZW: [{required: true, message: "请输入吨（座）位", trigger: "blur,change"}],
        GGXH: [{required: true, message: "请输入规格型号", trigger: "blur,change"}],
        DJH: [{required: true, message: "请输入大架号", trigger: "blur,change"}],
        FDJBH: [{required: true, message: "请输入发动机编号", trigger: "blur,change"}],
        NJRQ: [{required: true, message: "请选择年检日期", trigger: "blur,change"}],
        YXQJSRQ: [{required: true, message: "请选择有效结束日期", trigger: "blur,change"}],
        NJJG: [{required: true, message: "请输入年检机构", trigger: "blur,change"}],
        GDSYNX: [{required: true, message: "请输入规定使用年限", trigger: "blur,change"}],
        GZSJ: [{required: true, message: "请选择购置时间", trigger: "blur,change"}],
        BDBH: [{required: true, message: "请输入保单编号", trigger: "blur,change"}],
        BDJG: [{required: true, message: "请输入保单机构", trigger: "blur,change"}],
        BZ: [{required: true, message: "请输入备注", trigger: "blur,change"}]
    })

    // 根据选择的车辆类型查询吨（座）位
    const getDzwList = () => {
        getCllxList({ page: 1, size: 9999, cllxmc: form.value.CLLXMC }).then(({ data }) => {
            state.dzwArray = data.list;
            if(data.list && data.list.length > 0) {
                for(let i = 0; i < state.dzwArray.length; i++) {
                    if(state.dzwArray[i].DW == '' || state.dzwArray[i].DW == null) {
                        state.dzwFlag = 'input';
                        break;
                    }
                }
            }else {
                state.dzwFlag = 'input';
            }
        });
    };

    // 选择车辆类型的回调
    const checkClxx = (val) => {
        console.log(val);
        form.value.CLLXDM = val.CLBM;
        form.value.CLLXMC = val.CX;
        editVisible.value = false;
        // 清空原数据
        state.dzwFlag = '';
        form.value.DZW = '';
        getDzwList();
    };

    //保存，把数据传回父列表页
    const confirm = () => {
        validateForm()
            .then((result) => {
                emits(
                    "updateData",
                    {
                        ...form.value,
                    }
                );
            })
            .catch((err) => {
                console.log(err);
            });
    };

    const editVisible = ref(false);
    const checkCllx = () => {
        editVisible.value = true;
    };

    const vForm = ref(null);
    const validateForm = () => {
        return vForm.value.validate();
    };

    /**返回 */
    const handleReturn = () => {
        emits("close");
        // TODO 返回
    };
    onMounted(() => {
    });
</script>

<style scoped src="../../../style/index.css"></style>
<style scoped>
    .el-collapse {
        width: 100%;
    }

    .el-collapse ::v-deep .el-collapse-item__header {
        background-color: #e3e6f6;
        padding-left: 15px;
    }

    .out-box-content {
        padding: 20px;
    }
</style>
