/*
 *  @date：20211228
 *  @version:V2.0.0
 *  @description: 自动注册对框架的界面切换事件进行默认处理
 *  此文件无需显式引入，
 *  export default{
 *      mixins:[mixin],
 * 
 * 
 * 
 */
import {eventDefine} from "../../vsui.vue/index.js";
import router from "../../router/index.js"
import auth from "../../auth/index.js"
import tabFun from "@lib/tabFun";
import {ElMessage} from "element-plus";



(function(){

     /*********************************UI界面触发事件***************************************************************** */
     window.addEventListener("logout",(eventArgs)=>{
         auth.logout().then(()=>{

         })
    })

    window.addEventListener("changePasswd",(eventArgs)=>{
        tabFun.addTabByRoutePath('密码修改','/workflow/mmxg',null,null)
    })

    window.addEventListener("changeOrg",(eventArgs)=>{
        tabFun.addTabByRoutePath('单位切换','/workflow/orgnaChange',null,null)
    })



    


    window.addEventListener("changeEvm",(eventArgs)=>{
        if(process.env.NODE_ENV === 'production'){
            ElMessage.error('操作不允许')
            return
        }
        if(sessionStorage.getItem('TestEnv')==='prod'){
            sessionStorage.setItem('TestEnv','dev')
        }else {
            sessionStorage.setItem('TestEnv','prod')
        }
        location.reload()
    })

})()

export default {}