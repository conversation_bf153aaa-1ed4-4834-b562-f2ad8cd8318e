<template>
  <div style="height: 250px">
    <el-form
      ref="userFormRef"
      size="normal"
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="100px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="统一账号" prop="USER_LDAP">
            <el-input
              v-model="form.USER_LDAP"
              clearable
              readonly
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="USER_NAME">
            <el-input
              v-model="form.USER_NAME"
              clearable
              readonly
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="AD账号" prop="USER_AD">
            <el-input
              v-model="form.USER_AD"
              clearable
              readonly
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="MOBILE">
            <el-input
              v-model="form.MOBILE"
              clearable
              readonly
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="所属单位" prop="ORGNA_ID">
          <el-select
            v-model="form.ORGNA_ID"
            readonly
            disabled
            clearable
            placeholder="请输入"
          >
            <el-option
              v-for="(item, index) in unitOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="注册岗位" prop="USER_ROLE">
          <el-select
            v-model="form.USER_ROLE"
            readonly
            disabled
            clearable
            placeholder="请输入"
          >
            <el-option
              v-for="(item, index) in gwOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
    </el-form>
    <el-row class="btn" style="justify-content: center">
      <el-button type="primary" @click="submit">通过</el-button>
      <el-button type="danger" @click="refuseDialog">退回</el-button>
    </el-row>
    <el-dialog
      v-model="refuseDialogVisible"
      title="退回办理"
      width="800px"
      :before-close="closeDialog"
    >
      <cbsshRefuseComponent @closeRefuseDialog="closeDialog"></cbsshRefuseComponent>
    </el-dialog>
  </div>
</template>
<script setup>
import outerBox from "@src/components/common/outerBox.vue";
import { reactive, getCurrentInstance, ref, watch } from "vue";
import cbsshRefuseComponent from "@src/views/cbs/cbsyj/cbsshRefuse.vue";
import axiosUtil from "@lib/axiosUtil";
import { ElMessage } from "element-plus";
const props = defineProps({
  defaultData: {
    type: Object,
    default: () => {},
  },
});

const form = ref({
  DWQC: "",
  TYXYDM: "",
  QYYX: "",
  TJDWID: "",
  GLYZH: "",
  LXRXM: "",
  MOBILE: "",
  surePw: "",
  yzm: "",
  BZ: "",
});
watch(
  () => props.defaultData,
  (val) => {
    form.value = val;
  },
  {
    immediate: true,
  }
);
let refuseDialogVisible = ref(false);
const refuseDialog = () => {
  refuseDialogVisible.value = true;
};
const closeDialog = () => {
  refuseDialogVisible.value = false;
};
const submit = () => {};
</script>
<style src="../style/index.css" scoped></style>
<style scoped></style>
