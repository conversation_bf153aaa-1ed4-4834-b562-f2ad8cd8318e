<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        class="lui-page"
        :model="state"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        v-loading="state.loading"
    >
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="userName">
            <el-input ref="input45296" placeholder="车牌号或车辆名称" @input="getDataList"
                      v-model="state.listQuery.CPHCLMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="1" class="grid-cell" style="margin-left: auto">
          <el-button type="primary" class="lui-button-add" @click="addRow"><el-icon><Plus/></el-icon>添加</el-button>
        </el-col>
      </el-row>
      <el-table
          highlight-current-row
          size="default"
          ref="table"
          height="50vh"
          fit
          class="lui-table"
          :border="false"
          :data="state.tableData"
      >
        <EleProTableColumn
            v-for="prop in state.tableColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #fileList="{ row, $index }">
            <vsfileupload
                :maxSize="10"
                @getFileList="getFileList"
                :index="$index"
                :editable="false"
                :busId="row.CLZSJID"
                :key="row.CLZSJID"
                ywlb="CLFJ"
                busType="dwxx"
                :limit="100"
            ></vsfileupload>
          </template>
          <template #opration="{ row, $index }">
            <div>
              <el-button class="lui-table-button" @click="chooseData(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="editRow(row, $index)">编辑</el-button>
              <el-button class="lui-table-button" @click="delRow(row, $index)">删除</el-button>
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-form>
  </div>

  <el-dialog
      custom-class="lui-dialog"
      title="编辑车辆信息"
      v-model="editVisible"
      width="1200px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      @close="() => {getDataList()}"
  >
    <clEdit :editData="editData" @close="editVisible = false" :editable="true"
            @updateData="updateData"
    />
  </el-dialog>
</template>
<script setup>
import {defineProps, reactive, watch, ref,defineEmits, onMounted} from "vue";
import {ElMessage, ElMessageBox} from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";

import {v4 as uuidv4} from "uuid";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import clEdit from "./cl_xzEdit";
import axiosUtil from "../../../../../lib/axiosUtil";
import {auth} from "../../../../../assets/core";
import comFun from "../../../../../lib/comFun";
import { Search, Select, Plus} from '@element-plus/icons-vue'

const getFileList = (res) => {
  state.tableData[res.index].fileList = res.fileList;
};
const props = defineProps({
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  }
});
const state = reactive({
  tableData: [],
  listQuery:{},
  loading: false,
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    {
      label: "车辆名称",
      prop: "CLMC",
      align: "center",
      width: 150,
    },
    {
      label: "车型",
      prop: "CLLXMC",
      align: "center",
      width: 120,
    },
    {
      label: "吨(座)位",
      prop: "DZW",
      align: "center",
      width: 120,
    },
    {
      label: "车牌号",
      prop: "CPH",
      align: "center",
      width: 120,
    },
    {
      label: "品牌型号",
      prop: "PPXH",
      align: "center",
      width: 120,
    },
    {
      label: "规格型号",
      prop: "GGXH",
      align: "center",
      width: 120,
    },
    {
      label: "购置时间",
      prop: "GZSJ",
      align: "center",
      width: 150,
    },
    {
      label: "备注",
      prop: "BZ",
      align: "left",
      headerAlign: "center",
      width: 150,
    },
    {
      label: "附件",
      prop: "fileList",
      headerAlign: "center",
      align: "left",
      slot: "fileList",
      width: 200,
    },
    {
      label: "操作",
      align: "center",
      width: 150,
      fixed: "right",
      slot: "opration",
      hide: !props.editable
    },
  ],
  // 弹窗
  editVisible: false,
  editData: {},
});


const addRow = () => {
  const userinfo = auth.getPermission();
  let userParams={
    CJRZH: props.TYXYDM,
    CJRXM: userinfo.userName,
    CJDWID: userinfo.orgnaId,
    CJSJ: comFun.getNowTime(),
    SHZT: '1',
  }
  let params={
    edit: true,
    CLZSJID: comFun.newId(),
    ...userParams,
  }

  editIndex.value = state.tableData.length;
  editData.value = params;
  editVisible.value = true;

}


const editRow = (row,index) => {
  editIndex.value = index;
  editData.value = row;
  editVisible.value = true;
}

const updateData = (val, isAdd) => {
  saveRow(val,isAdd)
};

const saveRow = (params,isAdd) => {
  axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveClxxXxx',{tableData: [params]}).then(r=>{
    ElMessage.success('保存成功')
    editVisible.value = false;
    emit('updateEditData',params)
  })
}

const delRow = (row, index) => {
  ElMessageBox.confirm(
      '确定删除该条数据?',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(() => {
    let params={
      CLZSJID: row.CLZSJID,
      SHZT: '2'
    }
    axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveClxxXxx',{tableData: [params]}).then(r=>{
      state.tableData.splice(index,1)
      ElMessage.success('删除成功')
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除',
    })
  })
}

const emit=defineEmits(['updateChooseData','updateEditData'])
const chooseData = (row, index) => {
  emit('updateChooseData',row)
};

const getDataList = () => {
  const userinfo = auth.getPermission();
  let params = {
    ...state.listQuery,
    orgId: userinfo.orgnaId,
    TYXYDM: props.TYXYDM,
  }
  state.loading = true
  axiosUtil.get('/backend/sccbsgl/cbsyj/ytxxx/selectClxxXxx', params).then((res) => {
    state.tableData = res.data || []
    state.loading = false
  })
}

onMounted(()=>{
  getDataList()
})

const vForm = ref(null);
const validateForm = () => {
  return vForm.value.validate();
};
defineExpose({
  validateForm,
});

const editVisible = ref(false);
const editIndex = ref(0);
const editData = ref({});
</script>
<style scoped>
:deep(.el-table-fixed-column--right) {
  background-color: rgba(255, 255, 255, 1) !important;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__error) {
  top: 50%;
  transform: translateY(-50%);
  left: 40px;
}
</style>
