<!-- 评委抽取管理列表 -->
<template>
  <el-form ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="hymc">
          <el-input placeholder="请输入会议名称" v-model="listQuery.HYMC" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell" style="margin-right: 100px">
        <el-form-item label="" prop="hysj">
          <div style="display: flex">
            <el-date-picker style="width: 130px;" v-model="listQuery.KBSJ1" type="date" placeholder="会议开始时间" :size="size"
                            value-format="YYYY-MM-DD"/>
            <span style="margin-left: 5px;margin-right: 5px">至</span>
            <el-date-picker style="width: 130px;" v-model="listQuery.KBSJ2" type="date" placeholder="会议结束时间" :size="size"
                            value-format="YYYY-MM-DD"/>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="hydd">
          <el-input placeholder="请输入会议地点" v-model="listQuery.PBDD" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell" style="display: flex;gap: 10px">
        <div class="static-content-item" v-show="true" style="display: block;">
          <el-button ref="button91277" @click="getDataList" type="primary"> <el-icon>
            <Search/>
          </el-icon>查询</el-button>
        </div>
        <!-- <div class="static-content-item" v-show="true" style="display: block;">
          <el-button ref="button91277" @click="" type="primary">高级查询</el-button>
        </div> -->
        <div class="static-content-item" v-show="true" style="display: block;">
          <el-button ref="button91277" @click="" type="primary">  <el-icon>
            <Upload/>
          </el-icon>导出</el-button>
        </div>

<!--        <div class="static-content-item" v-show="true" style="display: block;">-->
<!--          <el-button ref="button91277" @click="add('ZB')" type="success">新增招标</el-button>-->
<!--        </div>-->
        <div class="static-content-item" v-show="true" style="display: block;">
          <el-button ref="button91277" @click="add('FZB')" type="success">
            <el-icon>
              <Plus/>
            </el-icon>新增
          </el-button>
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper" v-show="true">
      <el-table ref="dataTable" :data="tableData" height="calc(100vh - 250px)"  class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                         :index="indexMethod"></el-table-column>
        <el-table-column prop="HYMC" label="会议名称" align="center"
                         min-width="160"></el-table-column>
        <el-table-column prop="HYLX" label="会议类型" align="center"
                         min-width="160">
          <template #default="{row}">
            <div>招标</div>
          </template>
        </el-table-column>
        <el-table-column prop="KBSJ" label="会议时间" align="center"
                         min-width="100"></el-table-column>
        <el-table-column prop="PBDD" label="会议地点" align="center"
                         min-width="100"></el-table-column>
        <el-table-column prop="CJRMC" label="抽取人" align="center"
                         min-width="100"></el-table-column>
        <el-table-column prop="CJSJ" label="抽取时间" align="center"
                         min-width="150"></el-table-column>
        <el-table-column prop="CZ" label="操作" align="center" width="150">
          <template #default="scope">
            <div v-if="scope.row['SHZT'] === '0' ">
              <el-button size="small" class="lui-table-button" type="primary"
                         @click="editData(scope.row)">编辑
              </el-button>
              <el-button size="small" class="lui-table-button" type="primary"
                         @click="delRowData(scope.row.PBHYBS)">删除
              </el-button>
            </div>
            <div v-if="scope.row['SHZT'] === '1' ">
              <el-button size="small" class="lui-table-button" type="primary"
                         @click="viewData(scope.row)">查看
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background
                     class="lui-pagination"
                     v-model:current-page="listQuery.page"
                     v-model:page-size="listQuery.size" :page-sizes="[10,20,50,100]"
                     layout="total, prev, pager, next, sizes"
                     @size-change="pageOrSizeChange"
                     @current-change="pageOrSizeChange"
                     :total="total">
      </el-pagination>
    </div>
    <!-- 评委抽取 非招标弹出页 -->

  </el-form>

  <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="评委抽取" z-index="1000" width="80%" top="1vh"
             @closed="closeFzbForm">
    <pwcqglEdit :key="PWHYBS" :params="params" @close="closeFzbForm"/>
  </el-dialog>

  <!-- 评委抽取 评标弹出页 -->
  <el-dialog v-if="dialogPbVisible" v-model="dialogPbVisible" title="评委抽取-评标" z-index="1000" width="80%" top="1vh"
             @closed="closePbForm">
    <pwcqPbEdit :key="PWHYBS" :params="params" @close="closePbForm"/>
  </el-dialog>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted
}
  from 'vue'
// 请求方法 get,post,put,del,downloadFile
import vsAuth from "../../../lib/vsAuth";
import pwcqFzbEdit from "./pwcqFzbEdit.vue";//非招标
import pwcqPbEdit from "./pwcqPbEdit.vue";//评标
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "../../../lib/comFun";
import axiosUtil from "../../../lib/axiosUtil";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import pwcqglEdit from "@views/zjgl/pwcq/pwcqglEdit";

export default defineComponent({
  name: '',
  components: {pwcqFzbEdit, pwcqPbEdit,Search, Plus, Upload,pwcqglEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      PWHYBS: '',
      listQuery: {
        HYMC: '',
        HYKSSJ: '',
        HYJSSJ: '',
        HYDD: '',
        page: 1,
        size: 10,
      },
      tableData: [{}],
      total: 0,
      rules: {},
      xmlbArray: [],
      dialogVisible: false,
      dialogPbVisible: false,
      params: {},
    })

    const getDataList = () => {
      axiosUtil.get('/backend/zjgl/pwcqgl/queryPwcqList', state.listQuery).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });

    }
    const delRowData = (PBHYBS) => {
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        axiosUtil.get('/backend/zjgl/pwcqgl/deletePwcqgl', {PBHYBS}).then((res) => {
          getDataList()
          ElMessage({
            message: '删除成功',
            customClass: "myMessageClass",
            type: 'success',
          })
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    // 点击编辑
    const editData = (value) => {
      state.params = {editable: true, id: value.PBHYBS, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewData = (value) => {
      state.params = {editable: false, id: value.PBHYBS, operation: 'view'}
      state.dialogVisible = true
    }

    // 新增
    const add = (type) => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }


    const closeFzbForm = () => {
      state.dialogVisible = false
      getDataList()
    }
    const closePbForm = () => {
      state.dialogPbVisible = false
      getDataList()
    }
    onMounted(async () => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      closeFzbForm,
      delRowData,
      viewData,
      editData,
      add,
      closePbForm,
      indexMethod
    }
  }
})

</script>
<style scoped>
.grid-cell .el-input {
  max-width: 250px;
}
</style>

