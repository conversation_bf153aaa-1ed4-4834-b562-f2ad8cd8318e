<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="10">
      <el-col :span="11">
        <el-row :gutter="20" class="lui-search-form">
          <el-col :span="8" class="grid-cell">
            <el-form-item label="" v-show="true">
              <el-input style="width:100%;" placeholder="请输入姓名" v-model="data1.listQuery.XM" type="text" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="" v-show="true">
              <el-input style="width:100%;" placeholder="请输入部门名称" v-model="data1.listQuery.BM" type="text" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <div class="static-content-item" v-show="true" style="display: flex;padding-left: 10px;text-align: right;">
              <el-button ref="button91277" @click="getDataList" type="primary">
                <el-icon>
                  <Search/>
                </el-icon>
                查询
              </el-button>
            </div>
          </el-col>
        </el-row>
        <el-table ref="table1" :data="data1.tableData" height="calc(500px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  @selection-change="(val)=>handleSelectionChange(val,'data1')"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="selection" width="55" :selectable="selectable"/>
          <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                           :index="(index)=>indexMethod(index,'data1')"/>
          <el-table-column prop="USER_NAME" label="姓名" align="center" width="120"/>
          <el-table-column prop="ORGNA_NAME" label="部门" align="center" :show-overflow-tooltip="true"/>
        </el-table>
        <el-pagination background v-model:current-page="data1.listQuery.page" v-model:page-size="data1.listQuery.size"
                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                       class="lui-pagination"
                       @size-change="getDataList" @current-change="getDataList" :total="data1.total">
        </el-pagination>
      </el-col>
      <el-col :span="2">
        <div style="width: 100px;display: flex;align-items: center;height: 100%">
          <el-button type="primary" @click="toLeft">&lt;</el-button>
          <el-button type="primary" @click="toRight">></el-button>
        </div>
      </el-col>
      <el-col :span="11">
        <div style="height: 50px;text-align: right">
          <el-button ref="button91277" @click="submit" type="success">确定</el-button>
        </div>
        <el-table ref="datatable91634" :data="pageTableData('data2')" height="calc(500px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  @selection-change="(val)=>handleSelectionChange(val,'data2')"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="selection" width="55"/>
          <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                           :index="(index)=>indexMethod(index,'data2')"/>
          <el-table-column prop="USER_NAME" label="姓名" align="center" width="120"/>
          <el-table-column prop="ORGNA_NAME" label="部门" align="center" :show-overflow-tooltip="true"/>
        </el-table>
        <el-pagination background v-model:current-page="data2.listQuery.page" v-model:page-size="data2.listQuery.size"
                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                       class="lui-pagination"
                       @size-change="" @current-change="" :total="data2.total">
        </el-pagination>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {Search, Upload, Plus} from '@element-plus/icons-vue'
import axiosUtil from "../../lib/axiosUtil";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {Search, Upload, Plus},
  props: {
    params: {
      type: Object,
      required: true
    },


  },
  setup(props, {emit}) {
    const state = reactive({
      data1: {
        tableData: [],
        listQuery: {
          page: 1,
          size: 10,
        },
        total: 0,
        checkList: [],
      },
      data2: {
        tableData: [],
        listQuery: {
          page: 1,
          size: 10,
        },
        total: 0,
        checkList: [],
      },
    })
    const getDataList = () => {
      let params = {
        ...state.data1.listQuery
      }
      axiosUtil.get('/backend/common/selectUserList', params).then((res) => {
        state.data1.tableData = res.data.list
        state.data1.total = res.data.total
      });
    }
    const instance = getCurrentInstance()

    const toLeft = () => {
      state.data2.tableData = state.data2.tableData.filter(item => {
        return !state.data2.checkList.find(i => item.USER_ID === i.USER_ID)
      })
      state.data2.total=state.data2.tableData.length
      state.data2.checkList = []
    }

    const toRight = () => {
      state.data1.checkList.forEach(item => {
        if (!state.data2.tableData.find(i => item.USER_ID === i.USER_ID)) {
          state.data2.tableData.push(item)
        }
      })
      instance.proxy.$refs['table1'].clearSelection()
      state.data2.total=state.data2.tableData.length
    }

    const handleSelectionChange = (val, name) => {
      state[name].checkList = val
    }
    const selectable = (row, index) => {
      return !state.data2.tableData.find(item => item.USER_ID === row.USER_ID)
    }

    const indexMethod = (index, name) => {
      return (state[name].listQuery.page - 1) * state[name].listQuery.size + index + 1;
    }

    const pageTableData = (name) => {
      return state[name].tableData.slice((state[name].listQuery.page - 1) * state[name].listQuery.size, state[name].listQuery.page * state[name].listQuery.size)
    }
    const submit = () => {
      if(state.data2.tableData.length<1){
        ElMessage.warning('请至少选择一条数据')
        return
      }
      emit('getRes',state.data2.tableData)
      closeForm()
    }


    const closeForm = () => {
      emit('closeDialog')
    }
    onMounted(() => {
      getDataList()
    })
    return {
      ...toRefs(state),
      closeForm,
      getDataList,
      handleSelectionChange,
      selectable,
      toLeft,
      toRight,
      indexMethod,
      pageTableData,
      submit
    }
  }

})
</script>

<style scoped>

</style>
