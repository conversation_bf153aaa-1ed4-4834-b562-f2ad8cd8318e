<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px">
        基本信息
      </div>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目所属单位：" prop="JSDWID">
            <el-select v-model="formData.JSDWID" class="full-width-input" filterable
                       :disabled="readonly ? true : !editable" @change="(value)=>formData.JSDWMC=EJDWOptions.find(item=>item.ORGNA_ID===value)?.ORGNA_NAME"
                       clearable>
              <el-option v-for="(item, index) in EJDWOptions" :key="index" :label="item.ORGNA_NAME"
                         :value="item.ORGNA_ID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="16" class="grid-cell">
          <el-form-item label="项目名称：" prop="XMMC">
            <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="true">
              <template #append v-if="editable">
                <el-button @click="chooseXM">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="承包商名称：" prop="PJNR">
            <div style="margin-left: 10px">{{formData.CBSDWQC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="队伍名称：" prop="DWMC">
            <div style="margin-left: 10px">{{formData.DWMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="子项目名称：" prop="ZXMMC">
            <div style="margin-left: 10px">{{formData.ZXMMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="问题发现时间：" prop="WTFXSJ">
            <el-date-picker
                v-model="formData.WTFXSJ"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="评分标准：" prop="PFBZID">

            <el-input v-model="formData.PFBZ" type="text" placeholder="请输入" clearable :disabled="true">
              <template #append v-if="editable">
                <el-button @click="choosePFBZ">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="问题描述：" prop="WTMS">
            <el-input v-model="formData.WTMS" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="记分标准：" prop="JFBZ">
            <div style="margin-left: 10px">{{formData.JFBZ}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="记分分值：" prop="JFFZ">
            <template #label>
              <span>记分分值：</span>
              <el-tooltip class="item" effect="dark" placement="top" content="正数为加分、负数为扣分">
                <QuestionFilled style="height: 15px;margin-top: 13px"></QuestionFilled>
              </el-tooltip>
            </template>
            <el-input v-model="formData.JFFZ" type="text" placeholder="请输入" clearable :disabled="!editable"
                      @input="formData.JFFZ=formData.JFFZ.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="整改截止日期：" prop="ZGJZSJ">
            <el-date-picker
                v-model="formData.ZGJZSJ"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>


        <el-col :span="24" class="grid-cell">
          <el-form-item label="附件资料：" prop="ZL">
            <vsfileupload style="margin-left: 10px" v-model:files="FJZLFiles" :busId="params.id"
                          :key="params.id"
                          :editable="editable" ywlb="wtsb"/>
          </el-form-item>
        </el-col>
      </el-row>


      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px">
        已上报问题
      </div>

      <div>
        <div style="display: flex;gap: 10px;margin-bottom: 10px">
          <el-input style="width: 180px" v-model="listQuery.WTMS" type="text"
                    placeholder="请输入问题描述" clearable>
          </el-input>
          <el-button ref="button91277" @click="getDataList" type="primary" >查询</el-button>
        </div>
        <el-table ref="datatable91634" :data="tableData" height="calc(200px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
          <el-table-column prop="WTFXSJ" label="问题发现时间" align="center"
                           :show-overflow-tooltip="true" width="160"></el-table-column>
          <el-table-column prop="WTMS" label="问题描述" align="center"
                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
          <el-table-column prop="PFBZ" label="评分标准" align="center"
                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
          <el-table-column prop="JFBZ" label="记分标准" align="center"
                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
          <el-table-column prop="JFFZ" label="记分值" align="center"
                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
          <el-table-column prop="ZL" label="附件资料" align="center"
                           :show-overflow-tooltip="true" min-width="160">
            <template #default="{row}">
              <vsfileupload :busId="row.XMWTID"
                            :key="row.XMWTID"
                            :editable="false" ywlb="wtsb"/>
            </template>
          </el-table-column>
          <el-table-column prop="CJDWMC" label="上报单位" align="center"
                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
          <el-table-column prop="CJRXM" label="上报人" align="center"
                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
        </el-table>
        <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next"
                       class="lui-pagination"
                       @size-change="getDataList" @current-change="getDataList" :total="total">
        </el-pagination>
      </div>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable && formData.SHZT == '0'">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogXMXZVisible"
        v-model="dialogXMXZVisible"
        title="项目选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <xmChoose v-if="dialogXMXZVisible" @close="dialogXMXZVisible=false" :JSDWID="formData.JSDWID" :key="formData.JSDWID"  @submit="getXMXZRes"/>
      </div>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogBZXZVisible"
        v-model="dialogBZXZVisible"
        title="评分标准选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <bzChoose v-if="dialogBZXZVisible" @close="dialogBZXZVisible=false" :XMID="formData.XMID" :key="formData.XMID"  @submit="setPFBZ"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import vsAuth from "@lib/vsAuth";
import vsfileupload from "@views/components/vsfileupload";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import xmChoose from "@views/khpj/xmwtsb/xmChoose";
import bzChoose from "@views/khpj/xmwtsb/bzChoose";
import {QuestionFilled} from '@element-plus/icons-vue'
export default defineComponent({
  name: '',
  components: {vsfileupload,xmChoose,bzChoose,QuestionFilled},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      readonly: false,
      XMWTID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        JSDWID:'',
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        PJZT:props.params.PJZT
      },
      rules: {
        JSDWID: [{
          required: true,
          message: '字段值不可为空',
        }],
        XMMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        WTFXSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        PFBZID: [{
          required: true,
          message: '字段值不可为空',
        }],
        WTMS: [{
          required: true,
          message: '字段值不可为空',
        }],
        JFFZ: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZGJZSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      PJLBOptions: [],
      EJDWOptions: [],
      PFBZOptions: [],

      FJZLFiles: [],


      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,

      dialogXMXZVisible: false,
      dialogBZXZVisible: false,
      XZZBParams: false,

    })

    watch(()=>state.formData.XMID,(value)=>{
      if(value){
      }
    })

    const chooseXM = () => {
      state.dialogXMXZVisible = true
    }

    const choosePFBZ = () => {
      if(!state.formData.XMID){
        ElMessage.warning('请先选择项目')
        return
      }
      state.dialogBZXZVisible = true
    }

    const getFormData = () => {
      let params={
        XMWTID: state.XMWTID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/xmwtsb/selectWtsbById', params).then((res) => {
        state.formData=res.data
        getDataList()
        state.loading=false
      })
    }

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        XMID: state.formData.XMID,
        XMWTID: state.XMWTID
      }
      axiosUtil.get('/backend/sckhpj/xmwtsb/selectYsbwtPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      if (state.formData.JFBZ&&Math.abs(state.formData.JFBZ) < Math.abs(state.formData.JFFZ)) {
        ElMessage.warning("记分分值不能超过评分标准");
        return;
      }
      let params={
        ...state.formData,
        XMWTID: state.XMWTID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      if(type==='submit'){
        params.SHZT='1'
      }
      state.loading=true
      axiosUtil.post('/backend/sckhpj/xmwtsb/saveXmwtForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const getXMXZRes = (value) => {
      state.formData.DWMC=value.DWMC
      state.formData.CBSDWQC=value.CBSDWQC
      state.formData.DWWYBS=value.DWWYBS
      state.formData.CBSWYBS=value.CBSWYBS
      state.formData.JSDWMC=value.JSDWMC
      state.formData.JSDWID=value.JSDWID
      state.formData.XMID=value.XMID
      state.formData.XMMC=value.XMMC
      state.formData.ZXMMC=value.ZXMMC
      state.formData.ZXMID=value.ZXMID
      setPFBZ(null)
      state.dialogXMXZVisible=false
      getDataList()

    }



    const setPFBZ = (selectOne) => {
      state.formData.PFBZID=selectOne?.PFBZID
      state.formData.PJMBID=selectOne?.PJMBID
      state.formData.PJZYBM=selectOne?.PJZYID
      state.formData.PFBZ=selectOne?.PFBZXQ
      state.formData.JFBZ=selectOne?.BZJF
      state.formData.JFFZ=selectOne?.BZJF
      state.formData.JFFZ=selectOne?.BZJF
      state.dialogBZXZVisible=false
    }

    const getEjdwList = () => {
      axiosUtil.get('/backend/common/selectEjdwList',null).then(res=>{
        state.EJDWOptions=res.data || []
      })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const getUserTwoOrg = () =>{
      axiosUtil.get('/backend/common/selectOrganByUserId',{USER_ID: vsAuth.getAuthInfo().permission.userId}).then(res=>{
        console.log(res,'resresresrser');
        if(res.data && !state.formData.JSDWID){
          state.formData.JSDWID = res.data[0].ORGNA_TWO_ID
        }
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      if(props.params.operation == 'add' && props.params.PJZT == 'JSDW'){
        state.readonly = true
        getUserTwoOrg()
      }
      getEjdwList()

    })

    return {
      ...toRefs(state),
      getDataList,
      saveData,
      closeForm,
      indexMethod,
      getXMXZRes,
      setPFBZ,
      chooseXM,
      choosePFBZ


    }
  }

})
</script>

<style scoped>

</style>
