<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-page" label-position="right"
             label-width="200px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="4" class="grid-cell" style="margin-left: auto">
          <div class="static-content-item" style="display: flex;" v-if="editable && !value">
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="saveData('save')">
              <el-icon>
                <Document/>
              </el-icon>
              保存
            </el-button>
            <el-button ref="button91277" @click="saveData('submit')" type="primary">
              <el-icon>
                <Check/>
              </el-icon>
              提交
            </el-button>
          </div>
        </el-col>
      </el-row>
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="formData.BGMXList" height="calc(100vh - 180px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="CBSDWQC" label="企业名称" align="center"
                               :show-overflow-tooltip="true" min-width="150"></el-table-column>
              <el-table-column prop="DWMC" label="队伍名称" align="center"
                               :show-overflow-tooltip="true" min-width="150"></el-table-column>
              <el-table-column prop="ZYMC" label="专业名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="BGQQYMC" label="已选择服务区域" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="BGHFWQY" label="变更后服务区域" align="center" min-width="420">
                <template #default="{row,$index}">
                  <el-checkbox size="small"
                               v-model="row.checkAll"
                               :disabled="!editable"
                               :indeterminate="row.isIndeterminate"
                               @change="(value)=>handleCheckAllChange(value,row)">
                    全选
                  </el-checkbox>
                  <el-checkbox-group v-model="row.FWQY" size="small" :disabled="!editable" @change="(value)=>handleCheckedCitiesChange(value,row)">
                    <el-checkbox v-for="(item, index) in FWQYOptions"
                                 :key="index"
                                 :label="item.ORGNA_ID">{{ item.ORGNA_NAME }}
                    </el-checkbox>
<!--                    <el-checkbox label="QT">-->
<!--                      <el-input size="small" v-if="row.FWQY&&row.FWQY.includes('QT')" v-model="row.QTFWQYMC"-->
<!--                                placeholder="请输入内容" :disabled="!editable"></el-input>-->
<!--                      <div v-else>其它</div>-->
<!--                    </el-checkbox>-->
                  </el-checkbox-group>
                </template>


              </el-table-column>

            </el-table>
          </div>
        </el-col>
      </el-row>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import {ElLoading, ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import {Document, Check} from "@element-plus/icons-vue";
import comFun from "@lib/comFun";
import tabFun from "@lib/tabFun";
import axios from "axios";
import api from "@src/api/lc";
import {mixin} from "@core";


export default defineComponent({
  name: '',
  components: {Document, Check},
  props: {
    params: {
      type: Object,
      required: true
    },
    value: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params?.editable,
      QYBGID: props.params?.id,
      formData: {
        BGMXList: []
      },

      FWQYOptions: []
    })

    const getFormData = () => {
      let params = {
        QYBGID: state.QYBGID
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/qyxxbg/selectQybgForm', params).then(res => {
        state.formData = res.data
        state.formData.BGMXList.forEach(item => {
          item.FWQY = item.BGXQList.map(ii => {
            if (ii.FWQYBM === 'QT') {
              item.QTFWQYMC=ii.FWQYMC
            }
            return ii.FWQYBM
          })
        })
        state.loading = false
      })
    }

    const submitForm = (type) => {
      return new Promise(resolve => {
        state.loading = true
        let params = {
          ...state.formData,
          XGRZH: state.userInfo.userLoginName,
          XGSJ: comFun.getNowTime(),
        }
        if (type === 'submit') {
          params.SHZT = '1'
        }
        params.BGMXList.forEach(item => {
          item.FWQY.forEach(ii => {
            let FWQYMC = state.FWQYOptions.find(iii => iii.ORGNA_ID === ii).ORGNA_NAME

            let FWQYRow = item.BGXQList.find(iii => iii.FWQYBM === ii)
            if (!FWQYRow) {
              item.BGXQList.push({
                BGXQID: comFun.newId(),
                QYBGMXID: item.QYBGMXID,
                FWQYWYBS: comFun.newId(),
                FWQYBM: ii,
                ZYMXID: item.ZYMXID,
                FWQYMC: FWQYMC
              })
            }
          })

          item.BGXQList = item.BGXQList.filter(iii => item.FWQY.includes(iii.FWQYBM))
        })
        axiosUtil.post('/backend/sccbsgl/qyxxbg/saveQybgForm', params).then(res => {
          resolve(true)
        })
      })
    }

    const saveData = (type) => {
      return new Promise((resolve, reject)=> {
        //工作流提交
        if (props.value) {
          if (type === '1') {
            type = 'submit'
          } else {
            type = 'save'
          }
          if (['1', 'new'].includes(props.value.activityId)) {
            if (type === 'save') {
              resolve(submitForm(type))
            } else {
              validateForm().then(res => {
                if (res) {
                  resolve(submitForm(type))
                } else {
                  reject()
                }
              })
            }
          } else {
            resolve(true)
          }
          //普通提交
        } else {
          if (type === 'save') {
            submitForm(type).then(() => {
              ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
              state.loading = false
            })
          } else {
            validateForm().then(res => {
              if (res) {
                submitForm(type).then(() => {
                  //发起流程
                  submitProcess()
                })
              }
            })
          }
        }
      })
    }

    const {vsuiEventbus} = mixin()
    const submitProcess = () => {
      let processName = state.formData.YWBT
      let processId = 'CBS_QYBG'

      let _params = {};
      _params.strSystemCode = 'AUTHM_CENTER';
      _params.processId = processId;
      _params.engineType = 'vs';
      _params.activityId = 'new';
      _params.processInstanceName = processName;
      _params.businessId = state.QYBGID;
      _params.apprValue = "1";
      _params.apprResult = "提交";
      _params.loginName = state.userInfo.userLoginName;
      _params.userOrgId = state.userInfo.orgnaId;
      let ld = ElLoading.service({target: "#auditDiv", text: "正在提交数据，请稍后...",});
      axios({
        method: "post",
        url: api.createTask(),
        data: "varJson=" + JSON.stringify(_params),
      }).then((res) => {
        ld.close();
        if (res.data && res.data.result == 1) {
          ElMessage({message: '提交成功！', type: 'success',})
          tabFun.closeNowTab();
          vsuiEventbus.emit("reloadBGTableData", {});
        } else {
          state.loading=false
          ElMessage({message: '提交失败！', type: 'error',})
        }
      }).catch((error) => {
        console.error(error)
        state.loading=false
        ld.close();
        ElMessage({message: '提交失败！', type: 'error',})
      });
    }

    const validateForm = () => {
      return new Promise((resolve, reject) => {
        resolve(true)
      })
    }

    const getArea = (DMLBID, resList) => {
      axiosUtil.get('/backend/common/selectTwoOrgAndJgbmList', {}).then((res) => {
          state.FWQYOptions = res.data;
      });
    }

    const handleCheckAllChange = (val,row) => {
      row.FWQY = val ? state.FWQYOptions.map(item=>item.ORGNA_ID) : []
      row.isIndeterminate = false
    }

    const handleCheckedCitiesChange = (value,row) => {
      const checkedCount = value.length
      row.checkAll = checkedCount === state.FWQYOptions.length
      row.isIndeterminate = checkedCount > 0 && checkedCount < state.FWQYOptions.length
    }

    onMounted(() => {
      if (!props.params) {
        ElMessage.warning('参数缺失请关闭该页签重新打开！')
        return;
      }
      getFormData()
      getArea('FWQY', 'FWQYOptions')
    })

    return {
      ...toRefs(state),
      saveData,
      handleCheckAllChange,
      handleCheckedCitiesChange

    }
  }

})
</script>

<style scoped>

</style>
