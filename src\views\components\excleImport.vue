<template>
  <div style="display: inline-block">
    <el-upload
        v-model:file-list="fileList"
        class="upload-demo"
        :action="url"
        :on-success="onSuccess"
        :before-upload="beforeUpload"
        accept=".xls,.xlsx"
        :data="{paramsJson:JSON.stringify(params)}"
        :show-file-list="false">
      <el-button size="default" :type="buttonType">
        <el-icon v-if="showIcon">
          <TopRight/>
        </el-icon>
        {{buttonText}}
      </el-button>
    </el-upload>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {ElMessage} from "element-plus";
import {TopRight} from '@element-plus/icons-vue'

export default defineComponent({
  name: '',
  components: {TopRight},
  props: {
    url: {
      type: String,
      required: true
    },
    params: {
      type: Object,
      required: true
    },
    showIcon: {
      type: Object,
      default: false
    },
    beforeImport: {
      type: Function,
    },
    buttonText: {
      type: String,
      default: '导入'
    },
    buttonType:  {
      type: String,
      default: 'primary'
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      fileList: []
    })
    const onSuccess = (res) => {
      if (res.message === 'success') {
        ElMessage.success('导入成功')
      }
      emit('getExcelData', res)
    }
    const beforeUpload = () => {
      return new Promise(resolve => {
        if (props.beforeImport) {
          resolve(props.beforeImport.call(this))
        } else {
          resolve(true)
        }
      })
    }
    onMounted(() => {

    })

    return {
      ...toRefs(state),
      onSuccess,
      beforeUpload
    }
  }

})
</script>

<style scoped>

</style>
