<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

//控件中的一些常用方法都在这里调用，比如保存，打印等等
function saveAsHTML() {
  pageofficectrl.SaveFilePage = "/SaveAsHTML/save";
  pageofficectrl.WebSaveAsHTML();
  alert("HTML格式的文件已经保存到后端项目static/doc/SaveAsHTML 目录下。");
}
function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮    
  pageofficectrl.AddCustomToolButton("另存HTML", "saveAsHTML", 1);
}

function openFile() {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/SaveAsHTML/Word',
    method: 'get',
  });
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit,saveAsHTML };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <div class="Word">
    <div id="div1">
      <a href='/dev-api/doc/SaveAsHTML/test.htm'> 查看另存的html文件</a><br>
    </div>
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
  </div>
</template>
