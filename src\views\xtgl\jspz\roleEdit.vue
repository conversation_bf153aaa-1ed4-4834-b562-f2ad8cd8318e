<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="140px"
           size="default" v-loading="loading" @submit.prevent>
    <el-row :gutter="0" class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="角色名称：" prop="ROLE_NAME">
          <el-input v-model="formData.ROLE_NAME" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="应用角色编号：" prop="ROLE_CODE">
          <el-input v-model="formData.ROLE_CODE" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
        <el-form-item label="角色描述：" prop="ROLE_DESC">
          <el-input v-model="formData.ROLE_DESC"  :rows="3"
                    type="textarea" clearable show-word-limit :maxlength="100"
                    :disabled="!editable"/>
        </el-form-item>
      </el-col>
    </el-row>

    <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
      <el-button type="success" @click="saveData()" v-if="editable">保存</el-button>
      <el-button @click="closeForm">返回</el-button>
    </div>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import {ElMessage} from "element-plus";
import comFun from "../../../lib/comFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      roleId: props.params.id,
      formData:{
        ROLE_NAME: null,
        ROLE_CODE:null,
        ROLE_DESC:null,
      },
      rules: {
        ROLE_NAME: [{
          required: true,
          message: '字段值不可为空',
        }],
        ROLE_CODE: [{
          required: true,
          validator: async (rule, value, callback) => {
            if (!value) {
              callback(new Error('字段值不可为空'))
            } else {
              let isCF = await checkProp(value,'ROLE_CODE')
              if(Boolean(isCF.data)){
                callback()
              }else {
                callback(new Error('角色编号不能重复'))
              }
            }
          },
        }],
      },
      editable: props.params.editable,
      loading: false,
    })
    const getFormData = () => {
      let params = {
        roleId: state.roleId
      }
      state.loading = true
      axiosUtil.get('/backend/common/jspz/selectRoleInfoById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      });
    }

    const checkProp = (value,prop) => {
      let params={
        roleId: state.roleId,
        value: value,
        prop: prop
      }
      return axiosUtil.get('/backend/common/jspz/checkProp', params)
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const submitForm = () => {
      let params
      if(props.params.operation === 'add'){
        params={
          ROLE_ID: props.params.id,
          ...state.formData,
          CREATOR: state.userInfo.userId,
          CREATE_TIME: comFun.getNowTime(),
          ZT: 'add'
        }
      }else {
        params={
          ...state.formData
        }
      }
      console.error(params)
      axiosUtil.post('/backend/common/jspz/saveRoleInfo', params).then((res) => {
        ElMessage.success('保存成功')
      });
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }
    

    const closeForm = () => {
      emit('close')
    }


    onMounted(() => {

      if (props.params.operation !== 'add') {
        getFormData()
      }
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm

    }
  }

})
</script>

<style scoped>

</style>
