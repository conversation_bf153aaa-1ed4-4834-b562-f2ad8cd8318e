<h1 align="center">@vsui/lib-jsext</h1>

### **胜软vsui.vue前端javascript扩展** 

##### 作者：崔良

##### @vsui/lib-jsext 是对原生javascript基础对象的扩展与常用函数局的封装，与JS基础与运行环境有关，与三方库无依赖关系。



### **当前版本:** 0.1.3 

### 支持 ES6，ES5，浏览器环境，Nodejs环境，使用UMD规范编译，支持import、require引入



[ **[在线演示](http://***********/UE/vsui.lib/jsext/examples/01-basic.html)  |
[源码](http://***********/UE/vsui.lib/jsext)** ]





*您还知悉其他项目? 请反馈济南研发中心框架组核心团队，谢谢!*

## 特性

* 对String对象进行扩展，支持format，trim，ltrim，rtrim
* 对Date对象进行扩展，支持format，addDays
* 增加命名空间（namespace）的支持，getNameSpace，setNameSpace
* 增加对cookie的访问库addCookie，getCookie，delCookie，clearCookie，allCookie，以及系统开发中需要用到的默认cookie键名
* 增加对浏览器正常/全屏的切换，默认为正常模式，screenSwitch
* 增加对uri的处理，getJsonFromUrlParams，setJsonToUrlParams，getParameter，setParameter
* 增加对dom元素class属性的设置功能，addClass，removeClass

## 升级内容

* 升级命名空间注册函数setNameSpace的处理过程，修复在ES6规范下，window使用proxy对象后注册命名空间不正常的问题


## 入门指南

### 支持性

本组件采用umd规范，提供了尽可能多的使用支持性，umd规范支持性请自行百度。

可在浏览器中使用script标签引入，对javascript基础类型的扩展会自动执行，无需单独调用函数，其他对象均可访问，入口点为：window.VsuiLibJSExt；
```javascript
//暂无测试代码
```

可在nodejs环境下使用require("@vsui/lib-jsext")引入，可用对String与Date类型的扩展（自动扩展）以及Uri对象函数，其他类型不可见，
```javascript
const Uri= require("@vsui/lib-jsext").Uri;

Uri.setJsonToUrlParams({'aa':1,"bb":2},"http://localhost/")
```

可在ES6下（es2015）完整使用
```javascript
import {Uri,Class,Cookie,NameSpace,Screen} from "@vsui/lib-jsext";
```

### 安装

#### npm

    # 使用 npm    
	npm install @vsui/lib-jsext --save
    



#### 引入

对于基础类型如 String,Date无需声明式引入，即无需这样（引入后会导致编译出现重复声明的错误，）：
```javascript
import {Date,String} from "@vsui/lib-jsext";
```
对于使用到Class,Cooike,NameSpace,Screen,Uri的需要如下引入
```javascript
import {Uri,Class,Cookie,NameSpace,Screen} from "@vsui/lib-jsext";
```



### 文档

#### Class

/***
 *  @author:cuiliang
 *  @email: <EMAIL>
 *  @date：20191024
 *  @version:V1.1.0
 *  @description:
 *  该模块开放函数在需要的地方引入即可，禁止全局引入，因为全局引入后您仍需要在每个使用的模块中单独引入
 *  软件中如有对该模块方法调用时，请将该模块在main.js中引入，即可在程序运行时就完成了对基础类型的扩展，
 *  如对此机制不明白的请查阅javascript相关文档
 *  1:addClass 添加dom元素中的class样式
 *  2:removeClass 移除dom元素中的class样式
 *
 *  @example:
 *  let dom=document.getElementById("app")||jQuery("#app")[0];
 *  addClass("classA",dom)
 *  removeClass("classA",dom)
 *
 *  @interface:
 *  addClass(string,HTMLElement)
 *  removeClass(string,HTMLElement)
 *
 */
 
##### 库函数
```javascript
//  添加className
function addClass(className, dom) 

// 删除className
function removeClass(className, dom)
```



#### Cookie

/***
 *  @author:cuiliang
 *  @email: <EMAIL>
 *  @date：20180529
 *  @version:V1.1.0
 *  @description:
 *  封装对cookie操作的类库,并定义统一的cookie定义
 *  1:addCookie 添加cookie，名称不重名的前提下添加的cookie不会影响原有cookie，请参考cookie特性
 *  2:getCookie 通过cookie名称获取cookie值
 *  3:delCookie 通过cookie名称删除cookie值
 *  4:clearCookie 清除Cookie
 *  5:allCookie 读取所有保存的cookie字符串
 *  以上函数请注意参数
 *
 */
##### 库函数

```javascript
  /**
	 *添加cookie，名称不重名的前提下添加的cookie不会影响原有cookie，请参考cookie特性
	 * @param {string} name cookie名称
	 * @param {string} value cookie值
	 * @param {Int32Array} hours cookie过期时间（小时），为0时不设定过期时间
	 * @param {string} path cookie作用路径，可为空，默认不设置
	 * @param {string} domain cookie作用域，可为空，默认不设置
	 * @returns {Cookie} this 为支持链式写法，返回自身
	 */
  addCookie: function(name, value, hours, path, domain)
```

```javascript
  /**
	 * 通过cookie名称获取cookie值
	 * @param {string} name 要获取cookie的名称
	 * @returns {string} 获取的cookie值
	 */
  getCookie: function(name, domain = "", path)
```
```javascript
  /**
	 * 通过cookie名称删除cookie值
	 * @param {string} name cookie名称
	 * @returns {Cookie} this 为支持链式写法，返回自身
	 */
  delCookie: function(name, path, domain = "")
```
```javascript
  /**
	 * 清除Cookie
	 */
  clearCookie: function(path, domain)
```
```javascript
  /**
	 * 读取所有保存的cookie字符串
	 * @returns {string} 返回cookie字符串
	 */
  allCookie: function()
```

##### 公开对象

```javascript

/***
 * 定义cookie键名信息
 */
const CookieDef = {
  /**
	 * 用户名键名
	 */
  userName: "username",

  /**
	 * 密码键名
	 */
  passwd: "password",

  /**
	 * token授权
	 */
  token: "token",
  /**
	 * 默认有效时间
	 */
  hours: 12,
  /**
	 * 默认作用路径
	 */
  path: "/",

  /**
	 * 默认作用域
	 */
  domain: "",
};

```
```javascript
```
```javascript
```
```javascript
```

### NameSpace

/***
 *  @author:cuiliang
 *  @email: <EMAIL>
 *  @date：20191024
 *  @version:V1.1.0
 *  @description:
 *  为了规范化前端开发，并解决越来越多的前端模块带来的自由组合、嵌入，多版本共存等问题，避免变量的全局污染带来的可访问性问题，
 *  @example:
 *  如vsui.vue框架采用：
 *  victorysoft.software.frame.vsui.vue.v1_1_0
 *  如报表设计器采用：
 *  victorysoft.component.reportTools.designer
 *
 *  @interface:
 *  const namespace="victorysoft.software.frame.vsui.vue.v1_1_0";
 *  setNameSpace(string,obj);
 *  getNameSpace(string);
 */
##### 库函数

```javascript
/**
 * 使用一个字符串创建一个命名空间（如："victorysoft.component.vsui.vue.v1_2_0"），并可指定该命名空间最后一层名称空间指向的对象（如："victorysoft.component.vsui.vue.v1_1_0=obj）
 * @param {String} namespace 要设置的 命名空间 字符串，一般在系统内可以使用本文件声明中的常量namespace作为基础地址，以避免拼写错误或便于升级维护,如`${namespace}.你的其他名称空间`
 * @param {Object} obj 命名空间最后一层的空间名称指向的对象（指向的是obj对象，而非他的结构复制），该参数允许为空，若空则默认为{}对象,
 */
function setNameSpace(namespace, obj)
```

 ```javascript
 /**
 * 根据一个命名空间字符串获取该命名空间的对象，如不存在则返回null，如果存在则返回到该空间的指针引用
 * @param {String} namespace 要获取的命名空间字符串，一般在系统内可以使用本文件声明中的常量namespace作为基础地址，以避免拼写错误或便于升级维护,如`${namespace}.你的其他名称空间`
 * @param {bool} create 如果该命名空间不存在的话是否创建
 */
function getNameSpace(namespace, create)
```

### Screen

/***
 *  @author:cuiliang
 *  @email: <EMAIL>
 *  @date：20191024
 *  @version:V1.1.0
 *  @description:
 *  该模块开放函数在需要的地方引入即可，禁止全局引入，因为全局引入后您仍需要在每个使用的模块中单独引入
 *  该模块为浏览器全屏模式与正常模式开关函数，内置状态机制，初始为正常模式，有如下功能：
 *  1：screenSwitch 根据当前状态（如全屏）切换为另一种状态（如正常）
 *
 *  @example:
 *  无
 *
 *  @interface:
 *  screenSwitch()
 */
 
 ```javascript
 function screenSwitch()
 ```
 
 ### Uri
 
 /***
 *  @author:cuiliang
 *  @email: <EMAIL>
 *  @date：20191024
 *  @version:V1.1.0
 *  @description:
 *  该模块开放函数在需要的地方引入即可，禁止全局引入，因为全局引入后您仍需要在每个使用的模块中单独引入
 *  该模块处理string类型的url(非windox.location对象)，有如下功能：
 *  1：getJsonFromUrlParams 将入参url的参数序列化为json，
 *  2：setJsonToUrlParams 将入参的json对象拼入入参的url之后
 *  3：getParameter 从入参的url中获取名称为入参name的参数值
 *  4：setParameter 将入参的url中设置名称为入参arg的参数值为入参arg_val，此函数一次只能设置一个参数
 *
 *  modify by cuiliang 20200428
 *  修改setJsonToUrlParams函数对传入已存在参数，未在url中替换，而是追加的bug
 *
 *
 *  @example:
 *  无
 *
 *  @interface:
 *  getJsonFromUrlParams(string),
 *  setJsonToUrlParams(json,string),
 *  getParameter(string,string),
 *  setParameter(string,string,string),
 *
 * /
 
 #### 库函数
 
  ```javascript
/**
 * 从url中提取参数，并构造json对象
 * @param {*} url
 */
function getJsonFromUrlParams(url)
 ```
  
  ```javascript
/**
 * 将json的对象作为参数放入url，返回url；如：http://localhost/index?a=1&b=2
 * @param {*} jsonParams
 * @param {*} url
 */
function setJsonToUrlParams(jsonParams, url)
 ```
  
  ```javascript
/**
 * 从url中获取指定的参数值
 * @param {*} name
 * @param {*} url
 */
function getParameter(name, url)
 ```
  
  ```javascript
/**
 * 向url中设置指定的参数名与参数值
 * @param {*} arg 要设置的参数名
 * @param {*} arg_val 要设置的参数值
 * @param {*} url 指定的URL
 */
function setParameter(arg, arg_val, url)
 ```
 
 
 
## 感谢使用该组件，如有问题请反馈至[ 济南研发中心框架组核心团队-崔良 ]

