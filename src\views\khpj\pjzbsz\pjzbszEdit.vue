<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="评价类别：" prop="ZBLB">
            <el-select v-model="formData.ZBLB" class="full-width-input"
                       :disabled="!editable"
                       clearable>
              <el-option v-for="(item, index) in PJLBOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="评价内容：" prop="PJNR">
            <el-input v-model="formData.PJNR" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="指标名称：" prop="ZBMC">
            <el-input v-model="formData.ZBMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="指标记分：" prop="ZBJF">
            <el-input v-model="formData.ZBJF" type="text" placeholder="请输入" clearable :disabled="!editable"
                      @input="formData.ZBJF=formData.ZBJF.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')">
            </el-input>
          </el-form-item>
        </el-col>



        <el-col :span="8" class="grid-cell">
          <el-form-item label="记分层级：" prop="JFCJ">
            <el-select v-model="formData.JFCJ" class="full-width-input"
                       :disabled="!editable" @change="formData.BZMXList=[]"
                       clearable>
              <el-option v-for="(item, index) in JFCJOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="记分方式：" prop="JFFS">
            <el-select v-model="formData.JFFS" class="full-width-input"
                       :disabled="!editable"
                       clearable>
              <el-option v-for="(item, index) in JFFSOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="备注：" prop="BZ">
            <el-input v-model="formData.BZ" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

      </el-row>

      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px" v-if="formData.JFCJ==='BZ'">
        标准明细
      </div>
      <div v-if="formData.JFCJ==='BZ'">
        <div style="text-align: right;padding-bottom: 10px">
          <el-button ref="button91277" @click="addRow" type="primary" v-if="editable">添加</el-button>
        </div>
        <el-table ref="datatable91634" :data="formData.BZMXList" height="calc(300px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
          <el-table-column prop="PFBZXQ" label="评分标准" align="center"
                           :show-overflow-tooltip="true" min-width="300">
            <template #default="{$index,row}">
              <el-form-item label="" label-width="0" :prop="`BZMXList.${$index}.PFBZXQ`" :rules="tableRules.PFBZXQ" v-if="editable">
                <el-input ref="input45296" placeholder="请输入评分标准" v-model="row.PFBZXQ" type="text" clearable>
                </el-input>
              </el-form-item>
              <div v-else>{{row.PFBZXQ}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="BZJF" label="标准记分" align="center"
                           :show-overflow-tooltip="true" min-width="150">
            <template #default="{$index,row}">
              <el-form-item label="" label-width="0" :prop="`BZMXList.${$index}.BZJF`" :rules="tableRules.BZJF" v-if="editable">
                <el-input ref="input45296" placeholder="请输入标准记分" v-model="row.BZJF" type="text" clearable
                          @input="row.BZJF=row.BZJF.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')">
                </el-input>
              </el-form-item>
              <div v-else>{{row.BZJF}}</div>
            </template>
          </el-table-column>

          <el-table-column prop="CZ" label="操作" align="center" width="120">
            <template #default="scope">
              <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

      </div>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()" v-if="editable">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      PJZBID: props.params.id,
      formData: {
        BZMXList: [],
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime()
      },
      rules: {
        ZBLB: [{
          required: true,
          message: '字段值不可为空',
        }],
        PJNR: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZBMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZBJF: [{
          required: true,
          message: '字段值不可为空',
        }],
         ZBJF: [{
          required: true,
          message: '字段值不可为空',
        }],
         JFCJ: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      tableRules:{
        PFBZXQ: [{
          required: true,
          message: '字段值不可为空',
        }],
        BZJF: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      PJLBOptions: [],
      JFCJOptions: [],
      JFFSOptions: [],

    })

    const getFormData = () => {
      let params={
        PJZBID: state.PJZBID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/pjzbsz/selectPjzbById',params).then(res=>{
        state.formData=res.data
        state.loading=false
      })

    }


    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const submitForm = () => {
      let params={
        ...state.formData,
        PJZBID: state.PJZBID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        SHZT: '1',
      }

      if(params.BZMXList.length===0){
        params.BZMXList=[{
          BZBZID: comFun.newId(),
          PJZBID: state.PJZBID,
          PFBZXQ: state.formData.PJNR,
          BZLX: 'ZB',
          PXH: 1,
          BZJF: params.ZBJF
        }]
      }
      state.loading=true
      axiosUtil.post('/backend/sckhpj/pjzbsz/savePjzbFrom',params).then(res=>{
        ElMessage.success('保存成功')
        closeForm()
        state.loading=false
      })


    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            if(state.formData.BZMXList.length>0){
              let ZBJF=0
              state.formData.BZMXList.forEach(item=>ZBJF+=Number(item.BZJF))
              if(ZBJF===Number(state.formData.ZBJF)){
                resolve(true)
              }else {
                ElMessage.error('标准记分合计应等于指标记分')
                resolve(false)
              }
            }else {
              resolve(true)
            }
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const addRow = () => {
      state.formData.BZMXList.push({
        BZBZID: comFun.newId(),
        PJZBID: state.PJZBID,
        BZLX: 'ZB',
        PXH: state.formData.BZMXList.length+1
      })
    }

    const deleteRow = (index) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.formData.BZMXList.splice(index,1)
        ElMessage.success({
          message: '删除成功!'
        });
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }


    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      getDMBData('PJLB', 'PJLBOptions')
      getDMBData('JFCJ', 'JFCJOptions')
      getDMBData('JFFS', 'JFFSOptions')
    })

    return {
      ...toRefs(state),
      getFormData,
      saveData,
      closeForm,
      addRow,
      deleteRow

    }
  }

})
</script>

<style scoped>

</style>
