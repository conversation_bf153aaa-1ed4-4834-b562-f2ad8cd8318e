<template>
  <div>

  </div>
</template>


<script>
  import VSAuth from "../lib/vsAuth";
  export default {
    name: "jumpPage",
    data(){
      return {
        VSAuth
      }
    },
    methods: {

    },
    mounted() {
      console.log(VSAuth.getAuthInfo())
      if(VSAuth.getAuthInfo().isLogined){
        sessionStorage.setItem("userName",VSAuth.getAuthInfo().realName);
        sessionStorage.setItem("permission",JSON.stringify(VSAuth.getAuthInfo().permission));
        console.log(VSAuth.getAuthInfo().permission)
        sessionStorage.setItem("isLogin",true)
        this.$router.push("/dashboard");
      }
      else{
        console.log( this.$route.query,' this.$router.param')
        this.$router.push("/logins");
      }

    },
  };
</script>
    
