<template>
  <div style="font-size: 14px;" v-loading="loading">
    <div style="margin-bottom: 10px;border: 1px solid #cbcbcb;padding: 5px"
         v-for="(item,index) in tableData" :key="index">
      <div style="font-weight: bold;color: #409EFF;cursor: pointer;width: 100%;text-align: right"
           @click="viewRow(item)">查看队伍完整信息》
      </div>
      <appRow label="队伍名称：" label-width="90px">{{ item['unitName'] }}</appRow>
      <appRow label="队伍编码：" label-width="90px">{{ item['dwbm'] }}</appRow>
      <appRow label="服务专业：" label-width="90px">{{ item['FWZY'] }}</appRow>
      <appRow label="服务区域：" label-width="90px">{{ item['FWQY'] }}</appRow>

      <appRow v-for="(iitem,iindex) in templates" :key="iindex"
              :label="iitem.SJMBMC.substring(0,2)+'：'" label-width="90px">
        {{ item.templates.filter(x=>x.SJMBBM == iitem.SJMBBM).length }}
      </appRow>

    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import appRow from "@views/app/common/appRow";
import vsfileupload from "@views/components/vsfileupload";
import {ArrowLeftBold, User, Postcard} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import {router} from "@core";

export default defineComponent({
  name: '',
  components: {appRow, vsfileupload, ArrowLeftBold, User, Postcard},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    DWYWID: {
      type: String,
      defaultData: () => '',
    },
  },


  setup(props, {emit}) {
    const state = reactive({
      tableData: [],
      templates: [],
      loading: false
    })

    const getDataList = () => {
      const params = {
        DWYWID: props.DWYWID
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/report/selectDwjgxxList', params).then((res) => {
        let resData = res.data || []
        resData.forEach(x => {
          x.unitName = x.DWMC;
          x.specialName = x.SQFWFW;
          x.dwbm = x.DWBM
        })
        state.tableData = resData
        state.loading = false
      });
    }

    const getMbxxList = () => {
      const params = {
        DWYWID: props.DWYWID,
        MBLX: 'DW',
      }
      axiosUtil.get('/backend/sccbsgl/report/selecBqListByJgId', params).then(res => {
        state.templates = res.data.tabList || []
      })
    }

    const viewRow = (row) => {
      router.push({path:'/app/dwjbxxView',query:{id:row.DWYWID}});
    }


    onMounted(() => {
      getDataList()
      getMbxxList()

    })

    return {
      ...toRefs(state),
      viewRow

    }
  }

})
</script>

<style scoped>
</style>
