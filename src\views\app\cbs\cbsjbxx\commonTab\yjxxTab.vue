<template>
  <div style="font-size: 14px;">
    <div style="margin-bottom: 10px;border: 1px solid #cbcbcb;padding: 5px"
         v-for="(item,index) in tableData" :key="index">
      <div style="font-weight: bold;color: #409EFF">{{item['ZYMC']}}</div>
      <div v-for="(iitem,iindex) in item.SJXXList" :key="iindex">
        <appRow label="信息项：" label-width="110px">{{iitem['XXXMC']}}</appRow>
        <appRow label="项目名称：" label-width="110px">{{iitem['XMMC']}}</appRow>
        <appRow label="甲方单位：" label-width="110px">{{iitem['JSDW']}}</appRow>
        <appRow label="合同金额(万元)：" label-width="110px">{{iitem['HTJE']}}</appRow>
        <appRow label="合同开始日期：" label-width="110px">{{iitem['HTRQKS']}}</appRow>
        <appRow label="合同结束日期：" label-width="110px">{{iitem['HTRQJS']}}</appRow>
        <appRow label="业绩简介：" label-width="110px">{{iitem['GCFW']}}</appRow>

        <appRow label="相关附件：" label-width="110px">
          <vsfileupload :busId="iitem.DWYJID" :editable="false"
                        :key="iitem.DWYJID" ywlb="DWYJFJ"
                        busType="dwyj"
                        :limit="100"></vsfileupload>
        </appRow>
        <div style="width: 100%;border-bottom: 1px dashed #c4c4c4;margin-top: 10px;margin-bottom: 10px"
             v-if="iindex+1!==item.SJXXList.length"></div>
      </div>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import appRow from "@views/app/common/appRow";
import vsfileupload from "@views/components/vsfileupload";

export default defineComponent({
  name: '',
  components: {appRow,vsfileupload},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
  },



  setup(props, {emit}) {
    const state = reactive({
      tableData:[]
    })
    watch(()=>props.defaultData,()=>{
      if(props.defaultData){
        let res=[]
        props.defaultData.forEach(item=>{
          let ZYXX=res.find(ii=>item.ZYBM===ii.ZYBM)
          if(ZYXX){
            ZYXX.SJXXList.push(item)
          }else {
            res.push({
              ZYBM: item.ZYBM,
              ZYMC: item.ZYMC,
              SJXXList: [item]
            })
          }
        })
        console.log(res)
        state.tableData=res
      }
    },{immediate: true,deep: true})


    onMounted(() => {

    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>

</style>
