{"_args": [["@vsui/lib-j<PERSON>t@0.1.3", "F:\\VUEProject\\scgl-product\\scgl-prodect-front-1.0"]], "_from": "@vsui/lib-j<PERSON>t@0.1.3", "_id": "@vsui/lib-j<PERSON>t@0.1.3", "_inBundle": false, "_integrity": "sha512-V5u1YinVmd2w+ybs+Y5VzOuLCuD89fZLqWVwbzRjP8cjQUpyQwaFR2j1CX15g7XPzIoPxT/1cdjzlGuaJ4N/8A==", "_location": "/@vsui/lib-j<PERSON>t", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vsui/lib-j<PERSON>t@0.1.3", "name": "@vsui/lib-jsext", "escapedName": "@vsui%2flib-jsext", "scope": "@vsui", "rawSpec": "0.1.3", "saveSpec": null, "fetchSpec": "0.1.3"}, "_requiredBy": ["/@vsui/vue-multiplex"], "_resolved": "http://***********:7001/@vsui/lib-jsext/download/@vsui/lib-jsext-0.1.3.tgz", "_spec": "0.1.3", "_where": "F:\\VUEProject\\scgl-product\\scgl-prodect-front-1.0", "author": {"name": "<EMAIL>|<EMAIL>"}, "bakdependencies": {"core-js": "^3.4.4", "@babel/runtime": "^7.8.7", "babel-preset-stage-0": "^6.24.1"}, "bakdevDependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "bugs": {"url": "http://***********/UE/vsui.lib/jsext/issues"}, "dependencies": {"@ahcui/lib-jsext": "^0.1.2"}, "description": "胜软前端JS扩展库", "devDependencies": {"@babel/core": "^7.8.7", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-react": "^7.7.4", "@babel/runtime-corejs3": "^7.16.3", "babel-eslint": "^10.0.3", "babel-loader": "^8.0.6", "babel-preset-es2015": "^6.24.1", "eslint": "^5.16.0", "uglify-js-plugin": "0.0.6", "webpack": "4.41.5", "webpack-cli": "^3.3.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "files": ["dist"], "homepage": "http://***********/UE/vsui.lib/jsext.git#readme", "keywords": ["@vsui", "vsui", "victory", "victorysoft", "lib", "lib-j<PERSON>t"], "license": "MIT", "main": "dist/lib-jsext.umd.min.js", "name": "@vsui/lib-jsext", "private": false, "repository": {"type": "git", "url": "http://***********/UE/vsui.lib/jsext.git"}, "scripts": {"build": "webpack --config webpack.conf.js", "pub:lib-jsext-0.1.3": "npm publish", "unpub:lib-jsext-0.1.3": "npm unpublish @vsui/lib-jsext@0.1.3"}, "version": "0.1.3"}