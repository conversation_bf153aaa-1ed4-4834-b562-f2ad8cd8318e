<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="XM">
          <el-input v-model="listQuery.XM" type="text" placeholder="请输入姓名" style="width:100%;" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="GZDW">
          <el-input v-model="listQuery.GZDW" type="text" placeholder="请输入工作单位" style="width:100%;" clearable></el-input>
        </el-form-item>
      </el-col>
      <!-- <el-col :span="6" class="grid-cell">
        <el-form-item label="申报专业" prop="major">
          <el-input v-model="listQuery.major" type="text" placeholder="请选择" clearable suffix-icon="More">
          </el-input>
        </el-form-item>
      </el-col> -->
      <el-col :span="6" class="grid-cell" style="display: flex;gap: 10px;text-align: right;">
        <el-button type="primary" @click="getDataList">
          <el-icon>
            <Search/>
          </el-icon>
          查询
        </el-button>
        <el-button type="success" @click="confirm">确认</el-button>
        <el-button type="default" @click="closeForm">关闭</el-button>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table ref="multipleTable" :data="tableData" height="500px" row-key="ZJBH"
                @selection-change="handleSelectionChange" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column type="selection" width="55" :selectable="selectEnable" fixed="left" :reserve-selection="true"/>
        <el-table-column type="index" width="60" fixed="left" label="序号" :index="indexMethod"></el-table-column>
        <el-table-column v-if="true" prop="XM" label="姓名" align="center"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column v-if="true" prop="GZDWMC" label="工作单位" align="center"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column v-if="true" prop="ZCMC" label="职称" align="center"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column v-if="true" prop="XCSZYMC" label="现从事专业" align="center"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <!--  <el-table-column v-if="true" prop="SBZY" label="申报专业"  align="center"
            :show-overflow-tooltip="true"><template
             #default="scope"><span>{{scope.row.RKZJBXX['SBZY']}}</span></template></el-table-column> -->
<!--        <el-table-column v-if="true" prop="SJH" label="手机号" align="center"-->
<!--                         :show-overflow-tooltip="true">-->
<!--          <template-->
<!--              #default="scope"><span>{{ scope.row.RKZJBXX['BGDH'] }}</span></template>-->
<!--        </el-table-column>-->
      </el-table>
      <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                     :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                     class="lui-pagination"
                     @size-change="getDataList" @current-change="getDataList" :total="total">
      </el-pagination>
    </div>
  </el-form>

</template>

<script>
import {
  defineComponent,
  ref,
  shallowRef,
  toRefs,
  reactive,
  getCurrentInstance,
  watchEffect,
  onMounted
}
  from 'vue'
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import {Search, Upload, Plus} from '@element-plus/icons-vue'

export default defineComponent({
  components: {Search, Plus, Upload},
  props: {
    zjParams: {
      type: Object,
      required: true
    },
  },
  setup(props, context) {
    const multipleTable = ref(null);
    const state = reactive({
      listQuery: {
        XM: null,
        GZDW: null,
        page: 1,
        size: 10
      },
      yxTableData: props.zjParams.tableData,
      tableData: null,
      total: 0,
      rules: {},
      currentRow: [],
    })
    const methods = {}
    const instance = getCurrentInstance()
    const submitForm = () => {
      instance.proxy.$refs['vForm'].validate(valid => {
        if (!valid) return
        //TODO: 提交表单
      })
    }
    const resetForm = () => {
      instance.proxy.$refs['vForm'].resetFields()
    }
    const getDataList = () => {
      axiosUtil.get('/backend/zjgl/zjckgl/queryYrkZjList', state.listQuery).then((res) => {
        state.tableData = res.data.list ? res.data.list : []
        state.total = res.data.total
        state.yxTableData.forEach(row => {
          multipleTable.value.toggleRowSelection(row);
        })
      });
    }
    const confirm = () => {
      if (state.currentRow.length > 0) {
        context.emit('parentMethod', state.currentRow)
      } else {
        ElMessage({
          message: `请选择数据`,
          type: 'warning',
        })
      }
    }
    const handleSelectionChange = (val) => {
      state.currentRow = val
    }
    const closeForm = () => {
      context.emit("closeZjForm")
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const selectEnable = (row) => {
      return true
      /*  if(state.yxTableData.some(item=>item.ZJBH==row.ZJBH)){//禁止勾选
          return false
       }else{
         return true
       } */
    }
    onMounted(async () => {
      getDataList()
    })

    return {
      ...toRefs(state),
      ...methods,
      submitForm,
      resetForm,
      getDataList,
      confirm,
      closeForm,
      handleSelectionChange,
      selectEnable,
      multipleTable, indexMethod
    }
  }
})

</script>

<style scoped>

</style>
