<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="PJLB">
            <el-input ref="input45296" placeholder="请输入建设单位" v-model="listQuery.JSDW" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell" style="margin-right: 150px">
          <el-form-item label="" prop="PJLB">
            <div style="display: flex;gap: 10px">
              <el-date-picker
                  v-model="listQuery.KSSJ"
                  type="date"
                  clearable
                  style="width: 150px"
                  placeholder="发现时间开始"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
              <div>至</div>
              <el-date-picker
                  v-model="listQuery.JSSJ"
                  type="date"
                  clearable
                  style="width: 150px"
                  placeholder="发现时间结束"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 180px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="JSDWMC" label="建设单位" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="XMSL" label="项目总量" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="WTSL" label="问题总量" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="LJFZ" label="累计分值" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {Plus, Search, Upload} from "@element-plus/icons-vue";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {

      },
      tableData: [],
      params: {},
      dialogVisible: false,
    })
    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/sckhpj/cxtj/selectWttjAdwList', params).then((res) => {
        state.tableData = res.data
      });
    }
    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList

    }
  }

})
</script>

<style scoped>

</style>
