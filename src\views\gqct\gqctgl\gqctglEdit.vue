<template>
  <div v-loading="loading">
    <firstPane ref="firstPane" :params="params" v-model="formData" v-if="step==='1'"/>
    <secondPane ref="secondPane" :params="params" v-model="formData" v-if="step==='2'"/>


    <div class="bottom-button" v-if="step==='1'">
      <el-button size="default" type="success" @click="saveData('save')" v-if="editable">保存</el-button>
      <el-button size="default" type="primary" @click="jyData('submit','TYC')" v-if="editable">天眼查校验</el-button>
      <el-button size="default" type="primary" @click="jyData('submit','QCC')" v-if="editable">企查查校验</el-button>
      <el-button size="default" type="primary" @click="saveData('submit')">下一步</el-button>
      <el-button size="default" @click="closeForm">返回</el-button>
    </div>

    <div class="bottom-button" v-if="step==='2'">
      <el-button size="default" type="warning" @click="step='1'">上一步</el-button>
      <el-button size="default" type="primary" @click="allAnalysis('TYC')" v-if="editable">天眼查分析</el-button>
      <el-button size="default" type="primary" @click="allAnalysis('QCC')" v-if="editable">企查查分析</el-button>
      <el-button size="default" type="success" @click="downloadCCBG" :loading="reporting" v-if="params.hasXm">股权穿透报告下载</el-button>
    </div>
  </div>

<!--  <gqctbgHtml ref="gqctbgHtml" style="visibility: unset;position: absolute" :params="params" @close="closeForm"/>-->

  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="true"
      v-if="dialogBGVisible"
      v-model="dialogBGVisible"
      title="股权穿透报告预览"
      z-index="1000"
      top="1vh"
      width="1000px">
    <div>
      <gqctbgHtml v-if="dialogBGVisible" ref="bsccbgHtml" :params="params" @close="closeForm"/>
    </div>
  </el-dialog>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import firstPane from "@views/gqct/gqctgl/firstPane";
import secondPane from "@views/gqct/gqctgl/secondPane";
import comFun from "@lib/comFun";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import gqctbgHtml from "@views/gqct/gqctgl/gqctbg/gqctbgHtml";

export default defineComponent({
  name: '',
  components: {firstPane,secondPane,gqctbgHtml},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GQCTJLID: props.params.id,
      hasXm: props.params.hasXm,
      FXQDID: props.params.fxqdid,
      step: '1',
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJDWMC: vsAuth.getAuthInfo().permission.orgnaName,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        qyList:[],
      },
      reporting: false,

      dialogBGVisible: false
    })

    const getFormData = () => {
      let params={
        GQCTJLID: state.GQCTJLID,
        FXQDID: state.FXQDID,
      }
      state.loading=true
      axiosUtil.get('/backend/gqct/gqctgl/getGqctglFirstForm', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }


    const saveData = (type) => {
      var xmid =instance.proxy.$refs['firstPane'].getXmid();
      props.params.xmid = xmid;
      if (!props.params.editable){
        state.step='2'
        return
      }
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }
    }

    const jyData = (sub,type) => {
      console.log("jyData")
      var qdata = instance.proxy.$refs['firstPane'].getCCQDList();
      let params={
        GSMC: qdata,
        type: type
      };
      console.log(params)
      state.loading=true
      axiosUtil.get('/backend/gqct/gqctgl/checkGqctGsmc', params).then((res) => {
        state.loading=false
        ElMessage.success(`校验完成`)
            instance.proxy.$refs['firstPane'].setCCQDList(res.data);
      })
    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        GQCTJLID: state.GQCTJLID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        qyList: instance.proxy.$refs['firstPane'].getCCQDDataList()
      }
      if(type==='submit'){
        params.SHZT='1'
      }
      state.loading=true
      axiosUtil.post('/backend/gqct/gqctgl/saveGqctglFirst',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        if(type==='submit'){
          ////props.params.editable=false
          props.params.operation='view'
          ////state.editable=false
          state.step='2'
        }else {
          closeForm()
        }
        state.loading=false
      })
      console.log(params)
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['firstPane'].validateForm().then(res=>{
          resolve(true)
        }).catch(msg=>{
          ElMessage.error(msg)
          resolve(false)
        })
      })
    }

    const downloadCCBG = () => {
      state.dialogBGVisible=true

      // state.reporting=true
      // instance.proxy.$refs['gqctbgHtml'].pdfFunc().then(res=>{
      //   ElMessage.success('已下载报告')
      //   state.reporting=false
      // }).catch(msg=>{
      //   state.reporting=false
      //   ElMessage.warning(msg)
      // })
    }
    const allAnalysis = (type) => {
      state.loading=true
      let params={
        ...state.formData,
        GQCTJLID: state.GQCTJLID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        type: type
      }
      axiosUtil.post('/backend/gqct/gqctgl/doAllAnalysis',params).then(res=>{
        ElMessage.success(`恭喜，穿透分析已完成`)
        //页面2 列表查询
        instance.proxy.$refs['secondPane'].getDataList();
        state.loading=false
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      jyData,
      allAnalysis,
      downloadCCBG

    }
  }

})
</script>

<style scoped>
.bottom-button{
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
