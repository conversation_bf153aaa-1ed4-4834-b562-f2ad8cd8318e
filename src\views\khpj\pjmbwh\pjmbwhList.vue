<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="MBMC">
            <el-input ref="input45296" placeholder="请输入模板名称" v-model="listQuery.MBMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData">
              <el-icon>
                <Plus/>
              </el-icon>
              新建
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="MBMC" label="模板名称" align="center"
                               :show-overflow-tooltip="true" min-width="230"></el-table-column>
              <el-table-column prop="MBMS" label="模板描述" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="BBH" label="版本号" align="center"
                               :show-overflow-tooltip="true" width="150"></el-table-column>
              <el-table-column prop="CJSJ" label="创建时间" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="XGSJ" label="更新时间" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="300">
                <template #default="scope">
                  <el-button v-if="scope.row.SYSL<1" size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑
                  </el-button>
                  <el-button v-else size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                  </el-button>
                  <el-button v-if="scope.row.SYSL<1" size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.row)">删除
                  </el-button>
                  <el-button size="small" class="lui-table-button" type="primary" @click="changeZt(scope.row)">{{scope.row.SFQY==='1' ? '禁用' : '启用'}}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="评价模板"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <pjmbwhEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Search, Upload, Plus} from '@element-plus/icons-vue'
import pjmbwhEdit from "@views/khpj/pjmbwh/pjmbwhEdit";
import axiosUtil from "@lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,pjmbwhEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/sckhpj/pjmbwh/selectPjmbPage', params).then((res) => {
        state.tableData = res.data.list || []
        state.total = res.data.total
      });
    }

    // 新增
    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.params = {editable: true, id: row.PJMBID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.params = {editable: false, id: row.PJMBID, operation: 'view'}
      state.dialogVisible = true
    }

    const deleteRow = (row) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.del('/backend/sckhpj/pjmbwh/delPjmb?PJMBID=' + row.PJMBID, null).then(res => {
          if(res.data.ZT==='1'){
            ElMessage.success('删除成功!')
            getDataList()
          }else {
            ElMessage.error(res.data.msg)
          }
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const changeZt = (row) => {
      let params={
        PJMBID: row.PJMBID,
        SFQY: row.SFQY==='1' ? '0' : '1'
      }
      axiosUtil.post('/backend/sckhpj/pjmbwh/updatePjmbxx',params).then(res=>{
        ElMessage.success(`${row.SFQY==='1' ? '禁用' : '启用'}成功`)
        getDataList()
      })
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      addData,
      closeForm,
      deleteRow,
      changeZt,
      viewRow

    }
  }

})
</script>

<style scoped>

</style>
