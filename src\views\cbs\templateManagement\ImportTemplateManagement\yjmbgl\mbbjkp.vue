<template>
  <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
           size="default">
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input
              v-model="formData.MBMC"
              :readonly="isNew ? false : !edit"
              placeholder="请输入模板名称"
              style="width: 200px"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input
              v-model="formData.MBMS"
              :readonly="isNew ? false : !edit"
              placeholder="请输入模板描述"
              style="width: 200px"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select
              v-model="formData.MBLXBM"
              value-key="value"
              placeholder="请选择模板类型"
              clearable
              filterable
              :disabled="isNew ? false : !edit"
              style="width: 100%"
          >
            <el-option
                v-for="item in templateOptions"
                :key="item.DMXX"
                :label="item.DMMC"
                :value="item.DMXX"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <div style="display: flex">
          <el-button type="primary" v-if="!isNew" :disabled="!edit" @click="getTemplate">
            <el-icon><Search/></el-icon>查询
          </el-button>
          <el-button type="primary" class="lui-button-add" :disabled="!edit" @click="addTemplate">
            <el-icon><Plus/></el-icon>新增
          </el-button>
          <el-button type="primary" :disabled="!edit" @click="saveTemplate">保存</el-button>
          <el-button type="primary" @click="exportData">
            导出
          </el-button>
          <!-- <el-button type="primary" @click="exportData">导出</el-button> -->
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          :data="tableData"
          height="calc(100vh - 300px)"
          @row-click="onSelectOp"
          border
          stripe
          v-loading="tableLoading"
      >
        <EleProTableColumn v-for="col in tableColumn" :col="col" :key="col.columnKey">
          <!-- 输入框通用 -->
          <template #input="{ row }">
            <el-input v-model="row[col.prop]" v-if="row.show"></el-input>
            <span v-else>{{ row[col.prop] }}</span>
          </template>
          <!-- 是否必填 -->
          <template #SFBT="{ row }">
            <el-select v-model="row.SFBT" placeholder="请选择" v-if="row.show">
              <el-option
                  v-for="item in SfbtArry"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              >
              </el-option>
            </el-select>
            <span v-else>{{ ["否", "是"][Number(row.SFBT)] }}</span>
          </template>
          <!-- 数据模板 -->
          <template #SJMB="{ row }">
            <el-select v-model="row.SJMBBM" placeholder="请选择" v-if="row.show">
              <el-option
                  v-for="item in SjmbArry"
                  :key="item.DMXX"
                  :label="item.DMMC"
                  :value="item.DMXX"
              >
              </el-option>
            </el-select>
            <span v-else>{{ mbDic[row.SJMBBM] }}</span>
          </template>
          <template #opration="{ row, $index }">
            <div>
              <!-- <el-button type="text" @click="">详情</el-button> -->
              <el-button class="lui-table-button" @click="delRow(row, $index)" v-if="edit">删除</el-button>
              <!-- <el-button type="text" @click="">编辑</el-button> -->
              <!-- <el-button type="text" @click="">禁用</el-button> -->
              <!-- <el-button type="text" @click="">启用</el-button> -->
              <!-- <el-button type="text" @click="">复制</el-button> -->
            </div>
          </template>
          <!-- 最低人数 -->
          <template #ZDRS="{ row }">
            <el-input v-model="row.ZDRS" type="number" v-if="row.show"></el-input>
            <span v-else>{{ row.ZDRS }}</span>
          </template>
          <!-- 最低学历 -->
          <template #ZDXL="{ row }">
            <el-select v-model="row.ZDXL" placeholder="请选择" v-if="row.show">
              <el-option
                  v-for="item in ryxlList"
                  :key="item.DMXX"
                  :label="item.DMMC"
                  :value="item.DMXX"
              >
              </el-option>
            </el-select>
            <span v-else>{{ xlDic[row.ZDXL] }}</span>
          </template>
          <!-- 工作年限 -->
          <template #GZNX="{ row }">
            <el-input v-model="row.GZNX" type="number" v-if="row.show"></el-input>
            <span v-else>{{ row.GZNX }}</span>
          </template>
          <!-- 设备数量 -->
          <template #SBSL="{ row }">
            <el-input v-model="row.SBSL" type="number" v-if="row.show"></el-input>
            <span v-else>{{ row.SBSL }}</span>
          </template>
          <!-- 持证信息 -->
          <template #CZXX="{ row }">
            <el-select v-model="row.CZXXARR" 
                multiple
                collapse-tags
                placeholder="请选择" 
                v-if="row.show">
              <el-option
                  v-for="item in zslxList"
                  :key="item.DMXX"
                  :label="item.DMMC"
                  :value="item.DMXX"
              >
              </el-option>
            </el-select>
            <span v-else>{{ dealCzxx(row.CZXXARR) }}</span>
          </template>
        </EleProTableColumn>
      </el-table>
    </div>
  </el-form>
</template>
<script setup>
import {defineEmits, onMounted, ref, watch} from "vue";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import util from "@lib/comFun.js";
import {
  getZrmbglPaging, //准入模板分页查询
  getZrmbglDetail, //模板明细
  getZrmbglDisabled, //模板禁用
  getZrmbglEnabled, //模板启用
  deleteZrmbgl, //模板删除
  postZrmbglSave, //模板保存
    getZrmbglDetailExport
} from "@src/api/sccbsgl.js";
import { getCommonSelectDMB } from "@src/api/common.js";
import { auth } from "@src/assets/core/index";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
//
const userinfo = auth.getPermission();
const emits = defineEmits(["close"]);
const props = defineProps({
  appUser: Object,
  edit: Boolean,
  MBID: String,
  isNew: Boolean,
});
// 上侧查询条件
const formData = ref({
  MBMC: "", //"模板名称",
  MBMS: "", //"描述",
  MBLXBM: "", //"模板类型编码",
});
const SfbtArry = [
  { value: "1", label: "是" },
  { value: "0", label: "否" },
];
const tableColumn = ref([
  { label: "序号", type: "index", width: 55, align: "center" },
  {
    label: "信息类别",
    prop: "XXLBMC",
    align: "center",
    showOverflowTooltip: true,
    slot: "input",
  },
  {
    label: "信息项",
    prop: "XXXMC",
    align: "center",
    showOverflowTooltip: true,
    slot: "input",
  },
  {
    label: "业务要求",
    prop: "YWYQ",
    align: "center",
    showOverflowTooltip: true,
    slot: "input",
  },
  {
    label: "录入资料说明",
    prop: "LRZLSM",
    align: "center",
    showOverflowTooltip: true,
    slot: "input",
  },
  {
    label: "必填",
    prop: "BT",
    align: "center",
    slot: "SFBT",
  },
  {
    label: "操作",
    align: "center",
    width: 80,
    slot: "opration",
  },
  {
    label: "数据模板",
    prop: "SJMB",
    align: "center",
    slot: "SJMB",
  },
  {
    label: "最低人数",
    prop: "ZDRS",
    align: "center",
    slot: "ZDRS",
  },
  {
    label: "最低学历",
    prop: "ZDXL",
    align: "center",
    slot: "ZDXL",
  },
  {
    label: "工作年限",
    prop: "GZNX",
    align: "center",
    slot: "GZNX",
  },
  {
    label: "设备数量",
    prop: "SBSL",
    align: "center",
    slot: "SBSL",
  },
  {
    label: "持证信息",
    prop: "CZXX",
    align: "center",
    slot: "CZXX",
  },
]);
// 表格数据
const tableData = ref([]);
// 数据模板字典
const mbDic = ref({
  /* ZZXX: "资质信息",
  RYXX: "人员能力",
  SBQK: "装备能力",
  YJQK: "服务业绩",
  CDQK: "场地配备",
  QHSEGL: "制度建设",
  AQHB: "安全环保",
  CLXX: "车辆信息", */
});
// 模板类型下拉选项
const templateOptions = ref([]);
const getTemplateOptions = () => {
  getCommonSelectDMB({
    DMLBID: "MBLX",
  })
      .then(({ data }) => {
        if (data) templateOptions.value = data;
      })
      .catch((err) => {
        ElMessage.error("模板类型获取失败");
      });
};
onMounted(() => getTemplateOptions());

// 查询数据模板
const SjmbArry = ref([]);
const getSjmbArry = () => {
  getCommonSelectDMB({
    DMLBID: "SJMB",
  })
      .then(({ data }) => {
        // console.log("data数据模板获取", data);
        if (!data) return; 
        SjmbArry.value = data;
        mbDic.value = data.reduce((t,i) => {
          t[i.DMXX] = i.DMMC;
          return t
        },{})
      })
      .catch((err) => {
        console.log(err);
        // ElMessage.error("数据模板获取失败");
      });
};
onMounted(() => {
  getSjmbArry();
  getRyxl();
  getZslx();
});

//

// 获取默认表格信息
const tableLoading = ref(false);
const getTemplate = () => {
  if (!props.MBID) return;
  tableLoading.value = true;
  getZrmbglDetail({ id: props.MBID })
      .then(({ data }) => {
        if (!data) return;
        const { header, detail } = data;
        tableData.value = detail;
        console.log(tableData.value);
        for (let i = 0; i < tableData.value.length; i++) {
          if(tableData.value[i].CZXX){
            console.log(tableData.value[i].CZXX.split(","));
            tableData.value[i].CZXXARR = tableData.value[i].CZXX.split(",");
          }
        }
        console.log("header", header);
        formData.value = header?.[0];
      })
      .catch((err) => {}).finally(() => {
        tableLoading.value = false
      })
};
onMounted(getTemplate);
// 排序方法
const sortId = (a, b) => {
  return a["XXLBMC"].localeCompare(b["XXLBMC"]);
};
// 新增时创建模板ID
const NEWUUID = util.newId();


const exportData = ()=>{
    getZrmbglDetailExport({id: props.MBID},`${formData.value.MBMC}信息${new Date().getTime()}.xlsx`);
}

// 增加模板
const addTemplate = () => {
  for (let i = 0; i < tableData.value.length; i++) {
    tableData.value[i].show = false;
  }
  for (let i = 0; i < tableData.value.length; i++) {
    if (
        tableData.value[i].XXLBMC == "" ||
        tableData.value[i].XXXMC == "" ||
        tableData.value[i].SJMBBM == ""
    ) {
      ElMessage({
        message: `请将第${i + 1}行中的信息填写完整后再新增行`,
        type: "warning",
      });
      return;
    }
  }
  //新增行后对数据进行排序
  tableData.value.sort(sortId);
  let list = {
    isNew: "1",
    show: false,
    LRZLSM: "", //"证件名称、编号及有效期",
    MBID: props.MBID || NEWUUID,
    MBMXID: util.newId(),
    MKZJDM: "", //
    PXH: 2, //
    SFBT: "1", //是否必填
    SJMBBM: "", //数据模板编码
    SJMBMC: "", //数据模板名称
    XXLBBM: "", //息项编码
    XXLBMC: "", //信息项名称
    XXXBM: "",
    XXXMC: "", //咨询资信证书
    YWYQ: "", //工程设计咨询单位乙级及以上资信证书
  };
  tableData.value.push(list);
};
// 删除行
const delRow = (row, index) => {
  tableData.value.splice(index, 1);
};

//

// 给无默认值数据添加默认数据
const setDafaultValue = (data, orgData) => {
  Object.entries(orgData).forEach(([key, value]) => {
    if (!data[key]) {
      data[key] = value;
    }
  });
};
// 保存模板
const saveTemplate = () => {
  if (!tableData.value.length) {
    ElMessage.warning("请先创建一条数据");
    return;
  }
  const checkArr = [
    { feild: "MBMC", label: "模板名称" },
    { feild: "MBMS", label: "描述" },
    { feild: "MBLXBM", label: "模板类型" },
  ];
  for (let i = 0; i < checkArr.length; i++) {
    if (!formData.value[checkArr[i].feild]) {
      ElMessage.warning(`请先填写${checkArr[i].label}`);
      return;
    }
  }

  for (let i = 0; i < tableData.value.length; i++) {
    console.log(tableData.value[i].CZXXARR);
    if(tableData.value[i].CZXXARR){
      tableData.value[i].CZXX = tableData.value[i].CZXXARR.join(",");
    }
    if (
        tableData.value[i].XXLBMC == "" ||
        tableData.value[i].XXXMC == "" ||
        tableData.value[i].SJMBBM == ""
    ) {
      console.log(tableData.value[i]);

      ElMessage({
        message: `请将第 ${i + 1} 行中的信息填写完整`,
        type: "warning",
      });
      return;
    }
  }
  // 初始化需要填充的信息
  const feildList = {
    MBID: props.MBID || NEWUUID, //"模板id",
    MBLXMC: templateOptions.value.find((i) => i.DMXX == formData.value.MBLXBM)?.DMMC, //"模板类型名称",
    CZR: userinfo.userName, //"创建人",
    CZRZH: userinfo.userLoginName, //"创建人账号",
    CZSJ: util.getNowTime(), //"创建时间",
    STATUS: "0", //"状态",
  };
  console.log(feildList);
  setDafaultValue(formData.value, feildList);
  // return
  postZrmbglSave({
    ...formData.value,
    ITEMS: tableData.value,
  })
      .then((result) => {
        ElMessage.success("保存成功");
        emits('close')
      })
      .catch((err) => {
        ElMessage.error("保存失败");
      });
};
// 行点击事件
const onSelectOp = (row, column, event) => {
  console.log(row, column, event);
  if (props.edit) {
    for (let i = 0; i < tableData.length; i++) {
      tableData.value[i].show = false;
    }
    row.show = true;
  }
};
// 查询学历列表
const ryxlList = ref([]);
const xlDic = ref({});
const getRyxl = () => {
  getCommonSelectDMB({ DMLBID: "XL" }).then((res) => {
    ryxlList.value = res.data;
    xlDic.value = res.data.reduce((t,i) => {
      t[i.DMXX] = i.DMMC;
      return t
    },{})
  });
};

// 查询证书类型
const zslxList = ref([]);
const getZslx = () => {
  getCommonSelectDMB({ DMLBID: "RYZSLX" }).then((res) => {
    zslxList.value = res.data;
  });
};
const dealCzxx = (arr) => {
  if(!arr){
    return;
  }
  let mcarr = '';
  for(let i=0;i<arr.length;i++){
    for(let k=0;k<zslxList.value.length;k++){
      if(arr[i] === zslxList.value[k].DMXX){
        mcarr += i===0 ? zslxList.value[k].DMMC : ',' + zslxList.value[k].DMMC
      }
    }
  }
  return mcarr;
}
</script>
<style lang="scss" scoped>
.el-container.custom {
  padding: 0;
}
</style>
