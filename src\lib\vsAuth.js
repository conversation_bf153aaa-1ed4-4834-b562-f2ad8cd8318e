
import { router, store, axios, NameSpace, namespace, runtimeCfg} from '../assets/core/index'
import customOpt from "./vsAuth.customOpt";
import VSAuth from '@vsui/lib-vsui-auth4-vseaf';

const vsAuth = NameSpace.getNameSpace(`${namespace}.vsAuth`)

const vsAuthConfig=runtimeCfg.vs_auth_config

if (!vsAuth) {
  // 模板工程默认使用胜软鉴权库
  VSAuth.init({
    router, 
    store, 
    axios, 
    ...vsAuthConfig,
    /**
     * 配置自定义过程配置项名称，避免配置项名称冲突
     */
     customCfg:"vsuiauth-detail",
     /**
      * 对自定义配置项进行过程定义
      */
     "vsuiauth-detail":customOpt
  })
  NameSpace.setNameSpace(`${namespace}.vsAuth`, VSAuth)
}

export default VSAuth