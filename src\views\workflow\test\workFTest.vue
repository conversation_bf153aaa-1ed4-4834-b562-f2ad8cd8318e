<template>
  <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
           size="default" @submit.prevent>
    <el-row ref="grid70953" :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="input45296">
          <el-select v-model="processParams.processId" class="full-width-input" placeholder="请选择流程">
            <el-option v-for="(item, index) in LCJKOptions" :key="index" :label="item.DMMC"
                       :value="item.DMXX" :disabled="item.disabled"/>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="6" class="grid-cell">
        <el-button ref="button91277" @click="getDataList" type="primary">
          刷新
        </el-button>
        <el-button ref="button112780" type="primary" class="lui-button-add" @click="addRow">
          新增
        </el-button>
      </el-col>
    </el-row>

    <el-row ref="grid71868" :gutter="12">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper">
          <el-table ref="datatable91634" :data="tableData" height="200px" v-loading="loading"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
            <el-table-column prop="PROCESS_NAME" label="业务类型" header-align="center" align="center" width="150">
            </el-table-column>
            <el-table-column prop="PROCESSINSTANCENAME" label="任务名称" header-align="center" align="left">
              <template #default="scope">
                <el-button type="text" @click="onAudit(scope.row)">
                  {{ scope.row.PROCESSINSTANCENAME }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="业务环节" prop="TASKNAME" header-align="center" align="center" width="200">
              <template #default="scope">
                {{scope.row.TASKNAME || '已结束'}}
              </template>
            </el-table-column>
            <el-table-column label="发送人" prop="OWNERNAME" header-align="center" width="100" align="center"></el-table-column>
            <el-table-column label="接收时间" prop="CREATEDTIME" header-align="center" align="center" width="180"></el-table-column>
            <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
              <template #default="scope">
                <el-button size="small" class="lui-table-button" type="primary" @click="monitorRow(scope.row)">跟踪
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>

  <!-- 文件上传示例-->
    <vsfileupload
        style="margin-left: 10px"
        :editable="true"
        :busId="busId"
        :key="busId"
        ywlb="ZBWJ"
        busType="ZBWJ"
        :prefix="`${cbsid}/${comFun.getNowMonth('/')}/${busId}/ZBWJ/`"
        :limit="100"
    ></vsfileupload>

    <!-- 文件表上传示例-->
    <vsFileUploadTable
        style="width: 100%;height: 200px"
        YWLX="CBSJBXX"
        :key="busId"
        :busId="busId"
        :prefix="`${cbsid}/${comFun.getNowMonth('/')}/${busId}/`"
        :editable="true"
    />

    <fileEdit
        :busId="busId"
        :key="busId"
        ywlb="ZBWJ"
        ref="fileEdit"
        busType="ZBWJ"/>

    <el-button ref="button112780" type="primary" class="lui-button-add" @click="generationWord">
      生成模板文件
    </el-button>
  </el-form>


  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="false"
      v-if="dialogVisible"
      v-model="dialogVisible"
      title="业务办理"
      @closed="dialogVisible=false"
      z-index="1000"
      top="5vh"
      append-to-body
      width="1200px">
    <div>
      <auditFrame v-if="dialogVisible" :businessParams="params" :processParams="processParams" @close="closeForm"/>
    </div>
  </el-dialog>

  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="false"
      v-if="dialogGZVisible"
      v-model="dialogGZVisible"
      title="流程跟踪"
      @closed="dialogGZVisible=false"
      z-index="1000"
      top="5vh"
      append-to-body
      width="1200px">
    <div>
      <monitorForm v-if="dialogGZVisible" :monitorParams="monitorParams" @close="closeForm"/>
    </div>
  </el-dialog>








</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
// import auditFrame from "@views/workflow/newWork/auditFrameNew";
import {ElMessage} from "element-plus";
import newWork from "@views/workflow/newWork";
import auditFrame from "@views/workflow/newWork/auditFrame";
import vsAuth from "@lib/vsAuth";
import monitorForm from "@views/workflow/newWork/monitorForm";
import vsfileupload from "@views/components/vsfileupload";
import vsFileUploadTable from "@views/components/vsFileUploadTable";
import fileEdit from "@views/components/fileEdit";

export default defineComponent({
  name: '',
  components: {auditFrame,monitorForm,vsfileupload,vsFileUploadTable,fileEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery:{
        page: 1,
        size: 10,

      },

      params: {},
      processParams: {
        ...newWork.processData.test,
        activityId: 'new',
        status: '1'
      },
      LCJKOptions: [],

      dialogVisible: false,
      showDialog: false,
      queryParams: {},

      tableData: [],
      total: 0,
      loading: false,

      dialogGZVisible: false,
      monitorParams: {},


      cbsid: '00000000001',
      busId: '00000000000000000000'

    })

    const getDataList = () => {
      let params = {
        ...state.listQuery,
        loginName: state.userInfo.userLoginName,
        STATUS: '1'


      }
      state.loading=true
      axiosUtil.get('/backend/workFlow/wf5/selectTaskList', params).then(res => {
        state.tableData = res.data.list  || []
        state.total = res.data.total
        state.loading=false
      })
    }

    const addRow = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible=true
    }


    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      getDataList()
      state.dialogVisible=false
    }

    const monitorRow = (row) => {
      state.monitorParams = row
      state.dialogGZVisible=true
    }

    const instance = getCurrentInstance()
    const generationWord = () => {
      let params={
        fileParams:{
          busType: 'dwxx',
          busId: state.busId,
          standbyField0: 'ZBWJ',
          prefix: `${state.cbsid}/${comFun.getNowMonth('/')}/${state.busId}/`,
        },
        DWID: '0EF57644BE353278E0631011100AE0EB',
        YWSQID: 'CA95009B39300001EBEF6CD4EEC1165B',
        SCNR: '审查内容',
        TJDWLDMC: '推荐单位领导名称',
        TJDWLDYJ: '推荐单位领导意见',
        ZYZGLDMC: '专业主管领导名称',
        ZYZGLDYJ: '专业主管领导意见',
        BTMC: '标题名称',
      }
      axiosUtil.post('/backend/test/generationCbsyqh',params).then(res=>{
        ElMessage.success('推荐函生成成功')
        instance.proxy.$refs['fileEdit'].loadFileList()
      })
    }







    onMounted(() => {
      getDataList()
      // getDMBData("LCJK", "LCJKOptions")

    })

    return {
      ...toRefs(state),
      addRow,
      indexMethod,
      getDataList,
      closeForm,
      monitorRow,
      comFun,
      generationWord


    }
  }

})
</script>

<style scoped>

</style>
