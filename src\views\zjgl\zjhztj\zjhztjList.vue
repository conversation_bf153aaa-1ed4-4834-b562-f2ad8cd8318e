<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <div style="margin-bottom: 10px">
        <el-radio-group v-model="TJLX" fill="#409EFF">
          <el-radio-button label="ZY">按专业</el-radio-button>
          <el-radio-button label="DW">按单位</el-radio-button>
        </el-radio-group>
      </div>

      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell" v-if="TJLX==='ZY'">
          <el-form-item label="" prop="ZYMC">
            <el-input ref="input45296" placeholder="请输入专业名称" v-model="listQuery.ZYMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell" v-if="TJLX==='DW'">
          <el-form-item label="" prop="DWMC">
            <el-input ref="input45296" placeholder="请输入单位名称" v-model="listQuery.DWMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <div class="static-content-item" style="display: flex;">
          <el-button ref="button91277" @click="getDataList" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
          <el-button ref="button9527" type="primary" @click="exportData">
            <el-icon>
              <Upload/>
            </el-icon>
            导出
          </el-button>
        </div>
      </el-row>


      <el-row ref="grid71868" :gutter="12" v-show="TJLX==='ZY'">
        <el-col :span="24" class="grid-cell">
          <zyTab ref="ZYTab" :listQuery="listQuery" />
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12" v-show="TJLX==='DW'">
        <el-col :span="24" class="grid-cell">
          <dwTab ref="DWTab" :listQuery="listQuery" />
        </el-col>
      </el-row>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import axiosUtil from "@lib/axiosUtil";
import zyTab from "@views/zjgl/zjhztj/zyTab";
import dwTab from "@views/zjgl/zjhztj/dwTab";

export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,zyTab,dwTab},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        // page: 1,
        // size: 10,
      },

      params: {},
      dialogVisible: false,

      TJLX: 'ZY'
    })

    const instance = getCurrentInstance()
    const getDataList = () => {
      instance.proxy.$refs[state.TJLX+'Tab'].getDataList()
    }

    const exportData = () => {
      instance.proxy.$refs[state.TJLX+'Tab'].exportData()
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      exportData

    }
  }

})
</script>

<style scoped>

</style>
