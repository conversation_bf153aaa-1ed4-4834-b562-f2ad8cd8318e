<!-- 采购项目管理列表 -->
<template>
  <el-form class="lui-page" ref="vForm" :rules="rules" label-position="right" label-width="0" size="default"
           @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="xmmc">
          <el-input placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="xmbh">
          <el-input placeholder="请输入项目编号" v-model="listQuery.XMBM" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="xmlb">
          <el-select ref="select14540" placeholder="请选择项目类别" v-model="listQuery.XMLB" class="full-width-input"
                     clearable>
            <el-option v-for="(item, index) in XMLBOptions" :key="index" :label="item.DMMC" :value="item.DMXX"
                       :disabled="item.disabled"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell" style="display: flex;gap: 10px;text-align: right;">
        <div class="static-content-item" v-show="true" style="display: block;">
          <el-button ref="button91277" @click="getDataList" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
        </div>
        <div class="static-content-item" v-show="true" style="display: block;">
          <el-button ref="button91277" @click="add" type="primary">
            <el-icon>
              <Plus/>
            </el-icon>
            新增
          </el-button>
        </div>
        <div class="static-content-item" v-show="true" style="display: block;">
          <el-button ref="button91277" @click="exportExcel" type="primary">
            <el-icon>
              <Upload/>
            </el-icon>
            导出
          </el-button>
        </div>
      </el-col>
    </el-row>

    <div class="container-wrapper" v-show="true">
      <el-table ref="dataTable" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                         :index="indexMethod"></el-table-column>
        <el-table-column prop="XMMC" label="项目名称" align="center" min-width="160"></el-table-column>
        <el-table-column prop="XMBM" label="项目编号" align="center" min-width="100"></el-table-column>
        <el-table-column prop="XMLB" label="项目类别" align="center" min-width="100"></el-table-column>
        <el-table-column prop="SSDW" label="所属单位" align="center" min-width="100"></el-table-column>
        <el-table-column prop="XMED" label="项目额度" align="center" min-width="100"></el-table-column>
        <el-table-column prop="CJRMC" label="创建人" align="center" min-width="100"></el-table-column>
        <el-table-column prop="CJSJ" label="创建时间" align="center" min-width="200"></el-table-column>
        <el-table-column prop="CZ" label="操作" align="center" min-width="200">
          <template #default="scope">

            <div v-if="scope.row['SHZT'] == 0 ">
              <el-button size="small" type="primary" class="lui-table-button"
                         @click="editData(scope.row)">编辑
              </el-button>
              <el-button size="small" plain type="primary" class="lui-table-button"
                         @click="delRowData(scope.row.CGXMBS)">删除
              </el-button>
            </div>
            <div v-if="scope.row['SHZT'] == 1 ">
              <el-button size="small" type="primary" class="lui-table-button"
                         @click="viewData(scope.row)">查看
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background
                     class="lui-pagination"
                     v-model:current-page="listQuery.page"
                     v-model:page-size="listQuery.size" :page-sizes="[10,20,50,100]"
                     layout="total, prev, pager, next, sizes"
                     @size-change="pageOrSizeChange"
                     @current-change="pageOrSizeChange"
                     :total="total">
      </el-pagination>
    </div>

  </el-form>
  <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="采购项目管理" z-index="1000" @closed="closeForm"
             width="80%">
    <div>
      <cgxmglEdit :key="CGXMBS" :params="params" @closeForm="closeForm"/>
    </div>
  </el-dialog>

</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted
}
  from 'vue'
// 请求方法 get,post,put,del,downloadFile
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import cgxmglEdit from "./cgxmglEdit.vue";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "../../../lib/comFun";
import {Plus, Search, Upload} from "@element-plus/icons-vue";

export default defineComponent({
  name: '',
  components: {cgxmglEdit,Search, Plus, Upload},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      CGXMBS: '',
      listQuery: {
        XMMC: '',
        XMBH: '',
        XMLB: '',
        page: 1,
        size: 10,
      },
      tableData: [{}],
      total: 0,
      rules: {},
      XMLBOptions: [],
      dialogVisible: false,
      params: null,
    })

    const getDataList = () => {
      axiosUtil.get('/backend/zjgl/cgxmgl/queryCgxmList', state.listQuery).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }
    const delRowData = (CGXMBS) => {
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        axiosUtil.get('/backend/zjgl/cgxmgl/deleteCgxmgl', {CGXMBS}).then((res) => {
          getDataList()
          ElMessage({
            message: '删除成功',
            customClass: "myMessageClass",
            type: 'success',
          })
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }
    const editData = (value) => {
      if (value) {
        state.params = {editable: true, id: value.CGXMBS, operation: 'edit'}
      }
      state.dialogVisible = true
    }

    // 点击编辑
    const viewData = (value) => {
      state.params = {editable: false, id: value.CGXMBS, operation: 'view'}
      state.dialogVisible = true
    }

    // 新增
    const add = () => {
      state.dialogVisible = true
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
    }


    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      var res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }


    onMounted(() => {
      getDataList()
      getDMBData("XMLB", "XMLBOptions")
    })

    return {
      ...toRefs(state),
      getDataList,
      closeForm,
      delRowData,
      viewData,
      editData,
      add, getDMBData, indexMethod
    }
  }
})

</script>
<style>
.containner {
  padding: 10px 8px;
}
</style>

