/**
 * 流程API
 */
 export default {
	 //获取任务列表
	 getTasksList(model) {
		 let url= "/hdscglpt/workflow/rest/wfClient/getTasksList"
		 return url;
	 },
 
	 //获取监控信息
	 getTasksMonitor(model) {
		 let url= "/hdscglpt/workflow/rest/wfClient/monitorTasks"
		 return url;
	 },
	 //获取审核按钮信息
	 getApproveValue(model) {
		 let url= "/hdscglpt/workflow/rest/wfClient/getApproveValue"
		 return url;
	 },
	 //获取流程审核页面
	 getTaskInfo(model) {
		 let url= "/hdscglpt/workflow/rest/wfClient/getTaskInfo"
		 return url;
	 },
	 //获取已办查看页面
	 selectProcessForms(model) {
		 let url= "/hdscglpt/workflow/rest/wfClient/selectProcessForms"
		 return url;
	 },
	 //创建任务
	 createTask(model) {
		 let url= "/hdscglpt/workflow/rest/wfClient/createTask"
		 return url;
	 },
	 //结束任务
	 finishTask(model) {
		 let url= "/hdscglpt/workflow/rest/wfClient/finishTask"
		 return url;
	 },
	 //撤回流程
	 repealTask(model) {
		let url= "/hdscglpt/workflow/rest/wfClient/repealedTask"
		return url;
	},

	//指派任务
	reassignTask(model) {
		let url= "/hdscglpt/workflow/rest/wfClient/reassignTask"
		return url;
	},
 }