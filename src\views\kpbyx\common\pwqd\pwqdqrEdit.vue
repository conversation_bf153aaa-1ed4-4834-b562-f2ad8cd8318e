<template>
  <div style="width: 100%" v-loading="loading">
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 160px;text-align: center;padding-bottom: 2px">
        评委签到确认
      </div>
      <el-row ref="grid71868" :gutter="12" style="width: 100%">
        <el-col :span="24">
          <el-table ref="datatable91634" :data="formData.PWQDList" height="300px"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="ZJLBMC" label="人员类型" align="center"
                             :show-overflow-tooltip="true" width="160">
              <template #default="{row}">
                <div v-if="row.SFZBRDB==='1'">
                  需求单位代表
                </div>
                <div v-else>
                  {{row.ZJLBMC}}专家
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="XM" label="姓名" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="PWJS" label="评委角色" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row}">
                评委
              </template>
            </el-table-column>
            <el-table-column prop="QZTP" label="签章图片" align="center"
                             :show-overflow-tooltip="true" width="200">
              <template #default="{row}">
                <el-image v-if="row.SIGNIMAGE" style="width: 150px;height: 60px"
                          :src="'/backend/minio/download?id='+row.SIGNIMAGE"></el-image>
              </template>
            </el-table-column>
            <el-table-column prop="QDZT" label="签到状态" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row}">
                <div style="color: #2A96F9" v-if="row.SFQD==='1'">已签到</div>
                <div style="color: red" v-else>未签到</div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;"
           v-if="parentForm.QD_WCZT!=='1'">
        <el-button size="default" type="primary" @click="saveData('submit')">确认</el-button>
        <el-button size="default" @click="getFormData">刷新</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "@lib/comFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: true,
      formData: {
        PWQDList: []
      },
      rules: {},
      loading: false
    })

    const getFormData = () => {
      let params={
        PBHYBS: props.params.PBHYBS
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/pwqd/selectQdpwxx', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const saveData = () => {
      let QDQK=state.formData.PWQDList.find(item=>item.SFQD!=='1')
      // if(QDQK){
      //   ElMessage.warning('请等待所有评委完成签字')
      //   return
      // }
      ElMessageBox.confirm('是否确认签到信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        emit('saveFromData', {QD_WCZT: '1',QD_WCSJ: comFun.getNowTime(),QD_SFGS: '1'},{})
        nextTick(()=>{
          emit('nextStep','人员签到已确认，请开始投票选择评委主任')
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      getFormData,
      saveData


    }
  }

})
</script>

<style scoped>

</style>
