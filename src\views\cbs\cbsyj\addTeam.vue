<!-- 新增队伍 -->
<template>
  <el-form style="height: 100%;width: 100%" size="default" class="lui-page">
    <el-table :data="props.teams" height="calc(100% - 70px)" border class="lui-table">
      <el-table-column label="队伍名称" prop="unitName" show-overflow-tooltip
                       header-align="center" align="left"
                       width="300"></el-table-column>
      <el-table-column label="申请服务范围" prop="sqfwfw" header-align="center"
                       align="left">
        <template v-slot="scope">
          <div>
            {{
              scope.row.specialName
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" header-align="center" align="center" width="100">
        <template v-slot="scope">
          <div>
            <el-button class="lui-table-button" @click="handleSelectProfession(scope.row, scope.$index)">
              选择专业
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row justify="space-evenly" align="center" style="margin-top: 10px;">
      <el-button type="primary" @click="handleSure">确定</el-button>
      <el-button @click="handleReturn">返回</el-button>
    </el-row>
  </el-form>
</template>

<script setup>
import outerBox from '@components/common/outerBox'
import {reactive, defineProps, defineEmits, onMounted} from 'vue'
import {ElMessage} from "element-plus";

let props = defineProps({
    teams: {
        type: Array,
        default: []
    },
  dialogTitle:{
    type: String,
  }
})
const emits = defineEmits(['return', 'sure', 'chooseSpeciality','goChildrenBack'])
let tableData = reactive([])

/**确定 */
const handleSure = () => {
    let list = props.teams.filter(x=>x.specials.length == 0)
    if(list.length > 0){
        ElMessage({
            message: '请选择申请服务范围！',
            type: 'warning'
        })
        return false;
    }
    emits('sure',props.teams)
}
/**返回 */
const handleReturn = () => {
    emits('goChildrenBack')
}
/**选择专业 */
const handleSelectProfession = (row, index) => {
    emits('chooseSpeciality', {
        current: row,
        step: 3
    })
}
onMounted(()=>{
  emits('update:dialogTitle','新增队伍')
})
</script>

<style scoped>
.el-container {
    height: 100%;
}

.out-box-content {
    height: calc(100% - 60px);
    padding-top: 20px;
}
</style>