<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CBSMC">
            <el-input ref="input45296" placeholder="请输入企业名称" v-model="listQuery.CBSMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="CBSDWQC" label="企业名称" align="center"
                               :show-overflow-tooltip="true" min-width="160">
              </el-table-column>
              <el-table-column prop="DWMC" label="队伍名称" align="center"
                               :show-overflow-tooltip="true" min-width="160">
                <template #default="{row}">
                  <div @click="viewCBS(row)" style="cursor: pointer; color: #5F80C7;">
                    {{ row['DWMC'] }}
                  </div>
                </template>
              </el-table-column>
<!--              <el-table-column prop="ZQMC" label="准入证书号" align="center"-->
<!--                               :show-overflow-tooltip="true" width="160"></el-table-column>-->
              <el-table-column prop="DLZYMC" label="专业名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="ZYFL" label="专业分类" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
<!--              <el-table-column prop="QYLXMC" label="企业类型" align="center"-->
<!--                               :show-overflow-tooltip="true" width="130"></el-table-column>-->
              <el-table-column prop="TJDWMC" label="引进单位" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="CJSJ" label="引进时间" align="center"
                               :show-overflow-tooltip="true" width="180"></el-table-column>
              <el-table-column prop="DWZTMC" label="队伍状态" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="100" fixed="right">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="企业登记证书"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1400px">
      <div>
        <qydjzsPrint v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import qydjzsPrint from "@views/cbs/queryanalysis/qydjzs/qydjzsPrint";
import {getTeamreslutGetProDetails} from "@src/api/sccbsgl";
import TabFun from "@lib/tabFun";
import {mixin} from "@core";
import tabFun from "@lib/tabFun";
import cbsjbxxIndex from "@views/cbs/cbsyj";
import dwjbxx from "@views/cbs/cbsyj/yrsqxxIndex";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,qydjzsPrint},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/sccbsgl/report/qydjzs/selectQydjzsPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }


    const viewRow = (row) => {
      state.params = {editable: false, id: row.DWYWID, operation: 'view',DLZYBM: row.DLZYBM}
      state.dialogVisible = true
    }

    const {vsuiEventbus} = mixin();
    const viewCBS = ({DWYWID, DWLX, DWEXTENSION,CBSDWQC,DWMC}) => {
      if(DWLX === 'CBS'){
        let ex = JSON.parse(DWEXTENSION)
        getTeamreslutGetProDetails({dwid:DWYWID}).then(res=>{
          tabFun.addTabByCustomName(
              CBSDWQC,
              'cbsjbxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
              cbsjbxxIndex,
              {
                uuId : DWYWID, //队伍业务ID
                MBID: ex.MBID, //模板ID
                MBLX: "QY", //模板类型、
                ZYFLDM: res.data.ZYFLDM, //专业分类代码
                YWLXDM: "BG", //业务类型代码
                editable: false,//是否查看
                isVIewJgxx: true
              },
              {}
          );
          // vsuiEventbus.emit("reloadCbsjbxx", {
          //     uuId : DWYWID, //队伍业务ID
          //     MBID: ex.MBID, //模板ID
          //     MBLX: "QY", //模板类型、
          //     ZYFLDM: res.data.ZYFLDM, //专业分类代码
          //     YWLXDM: "BG", //业务类型代码
          //     editable: false,//是否查看
          //   isVIewJgxx: true
          // });
        })
      }else if(DWLX === 'DW'){
        tabFun.addTabByCustomName(DWMC,
            'dwxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
            dwjbxx, {DWYWID: DWYWID,YWLXDM: 'BG',backPath: '/query-analysis/dwmx',
              JGDWYWID: DWYWID,//结果表队伍业务ID
              editable: false,//是否查看
              isVIewJgxx: true
            }, {});

      }

    }


    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      closeForm,
      viewRow,
      viewCBS

    }
  }

})
</script>

<style scoped>

</style>
