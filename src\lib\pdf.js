// 页面导出为pdf格式 //title表示为下载的标题，html表示document.querySelector('#myPrintHtml')
import html2Canvas from 'html2canvas';
import JsPDF from 'jspdf';

const noTableHeight = 0; //table外的元素高度
function htmlPdf(title, html, lableList, type,returnPromise,banner,watermark) {// type传有效值pdf则为横版
    return new Promise((resolve, reject) => {
        let defineHeight=277
        if(banner){
            defineHeight-=12
        }

        if (lableList) {
            const startHeight=html.offsetTop
            const pageHeight = Math.floor(defineHeight * html.scrollWidth / 190); //计算pdf高度

            let pageNum=1
            for (let i = 0; i < lableList.length;) { //循环获取的元素
                let domItem=lableList[i]
                // const multiple = Math.ceil((domItem.offsetTop + domItem.offsetHeight) / pageHeight); //元素的高度
                if (domItem.offsetTop + domItem.offsetHeight -startHeight>pageNum*pageHeight) { //计算是否超出一页
                    let _H = ''; //向pdf插入空白块的内容高度

                    //高度差加上修正
                    _H = pageNum * pageHeight - (domItem.offsetTop + noTableHeight -startHeight) + pageNum*3;
                    // console.error('_H',_H,{
                    //     pageNum,pageHeight,
                    //     offsetTop:domItem.offsetTop,
                    //     offsetHeight: domItem.offsetHeight,
                    //     noTableHeight,
                    //     domItem
                    // })
                    const newNode = getFooterElement(_H);  //向pdf插入空白块的内容
                    const divParent = domItem.parentNode; // 获取该div的父节点
                    if(_H!==0){
                        // 判断兄弟节点是否存在
                        if(domItem.classList.contains('table-row')){
                            domItem.style.borderTop = '1px solid #e0e3e8'
                        }
                        divParent.insertBefore(newNode, domItem);
                    }

                    pageNum++
                }else {
                    i++
                }
            }

        }
        if(watermark){
            addWatermark(html,watermark)
        }

        if(banner){
            html2Canvas(banner,{
                allowTaint: true,
                taintTest: false,
                logging: false,
                useCORS: true,
                dpi: window.devicePixelRatio * 1,
                scale: 1 // 按比例增加分辨率
            }).then(bannerCanvas=>{
                resolve(makeResPdf(html,defineHeight,type,title,returnPromise,bannerCanvas))
            })

        }else {
            resolve(makeResPdf(html,defineHeight,type,title,returnPromise,null))
        }

    })

}


//添加标头
function addBanner(pdf,banner,a4w){
    pdf.addImage(banner.toDataURL('image/jpeg', 1.0), 'JPEG', 10, 10, a4w, Math.min(12, a4w * banner.height / banner.width)); // 添加Banner 默认
}

//添加页脚
function addPageNum(canvas,text){
    let ctx=canvas.getContext('2d')
    ctx.font = '50px Arial';
    ctx.fillStyle = '#000000'; // 设置填充颜色
    ctx.fillText(text, canvas.width/2, canvas.height-10)
}

function addWatermark(watermarkDiv,watermarkText){
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    let binding={}
    canvas.width = binding.value?.width || 300;
    canvas.height = binding.value?.height || 200;
    ctx.rotate(-10 * Math.PI / 100) // 水印旋转角度
    ctx.font =binding.value?.font || '15px Vedana'  //水印文字大小
    ctx.fillStyle = 'rgba(136,136,136,0.51)' //水印颜色 HEX格式,可使用red 或者rgb格式
    ctx.textAlign = 'center' //水印水平位置
    ctx.textBaseline = 'Middle' //水印垂直位置
    ctx.fillText(watermarkText, canvas.width / 3, canvas.height / 2)

    watermarkDiv.style.background = 'url(' + canvas.toDataURL('image/png') + ') left top repeat' //水印显示(关键代码)


}

//删除处理的元素
function deleteFooterElement(html){
    for (const dom of html.getElementsByClassName('divRemove')) {
        dom.parentNode.removeChild(dom);
    }
}

function makeResPdf(html,defineHeight,type,title,returnPromise,banner){
    return new Promise(resolve => {
        html2Canvas(html, {
            allowTaint: false,
            taintTest: false,
            logging: false,
            useCORS: true,
            dpi: window.devicePixelRatio * 1,
            scale: 4 // 按比例增加分辨率
        }).then(canvas => {
            const pdf = new JsPDF('p', 'mm', 'a4'); // A4纸，纵向
            const ctx = canvas.getContext('2d');
            const a4w = type ? defineHeight : 190;
            const a4h = type ? 190 : defineHeight; // A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
            const imgHeight = Math.floor(a4h * canvas.width / a4w); // 按A4显示比例换算一页图像的像素高度
            let renderedHeight = 0;
            let pageNum=0


            testCanvas(canvas,a4w,a4h)

            while (renderedHeight < canvas.height) {
                pageNum++

                const page = document.createElement('canvas');
                page.width = canvas.width;
                page.height = imgHeight+200;// 可能内容不足一页


                // 用getImageData剪裁指定区域，并画到前面创建的canvas对象中
                page.getContext('2d').fillStyle = '#FFFFFF';
                page.getContext('2d').fillRect(0, 0, page.width, page.height)
                page.getContext('2d').putImageData(ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight)), 0, 0);
                addPageNum(page,pageNum)
                if(banner){
                    addBanner(pdf,banner,a4w)
                }
                pdf.addImage(page.toDataURL('image/jpeg', 1.0), 'JPEG', 10, banner ? 22 : 10, a4w, Math.min(a4h, a4w * page.height / page.width)); // 添加图像到页面，保留10mm边距

                renderedHeight += imgHeight;
                if (renderedHeight < canvas.height) {
                    pdf.addPage();// 如果后面还有内容，添加一个空页
                }


                // delete page;
            }


            deleteFooterElement(html)

            if(returnPromise){
                resolve(pdf);
            }else {
                pdf.save(title + '.pdf')
                resolve(true)
            }
        });
    })


}

//测试使用
function testCanvas(copyCanvas,a4w,a4h){
    const myCanvas=document.getElementById('myCanvas')
    if(!myCanvas){
        return
    }
    myCanvas.width=copyCanvas.width
    myCanvas.height=copyCanvas.height
    let ctx=myCanvas.getContext('2d')
    ctx.drawImage(copyCanvas, 0, 0)
    const imgHeight = Math.floor(a4h * copyCanvas.width / a4w);
    let renderedHeight = 0;

    while (renderedHeight < myCanvas.height) {
        ctx.beginPath();
        ctx.moveTo(0, renderedHeight);
        ctx.lineTo(myCanvas.width, renderedHeight);
        ctx.stroke();

        renderedHeight += imgHeight;
    }

}


// pdf截断需要一个空白位置来补充
function getFooterElement(remainingHeight, fillingHeight = 0) {
    const newNode = document.createElement('div');
    newNode.style.background = '#FFFFFF';
    newNode.style.width = 'calc(100% + 8px)';
    newNode.style.borderTop = '1px solid #d5d0d0'
    newNode.style.borderBottom = '1px solid #d5d0d0'
    newNode.style.marginLeft = '-4px';
    newNode.style.marginTop = '1px';
    newNode.style.marginBottom = '1px';
    newNode.classList.add('divRemove');
    newNode.style.height = (remainingHeight + fillingHeight) + 'px';
    return newNode;
}
function isSplit(nodes, index, pageHeight) {
    return nodes[index].offsetTop + nodes[index].offsetHeight + noTableHeight < pageHeight && nodes[index + 1] && nodes[index + 1].offsetTop + nodes[index + 1].offsetHeight + noTableHeight > pageHeight;
}
export default htmlPdf;