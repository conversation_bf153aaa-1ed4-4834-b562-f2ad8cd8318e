<template>
  <div class="context" style="height: 70vh;overflow: auto" v-loading="loading">
    <!--    <el-button size="default" type="success" @click="pdfFunc">查重报告下载</el-button>-->

    <el-button size="default" type="success" style="float: right" @click="makeBg" :loading="loading || making">生成报告</el-button>
    <div class="A4-context" id="pdf-context">
      <div class="title-text pdf-details">标书查重对比报告</div>
      <div class="describe-text pdf-details">说明：本报告仅为基于提供标书文件和查重参数设置进行的技术查重结果，仅供参考，不作为任何法律依据。</div>
      <div class="title1-text pdf-details">摘要：</div>

      <div class="label-row pdf-details">
        <div class="label-col">
          {{ formData.XMMC }}查重
        </div>
        <div class="label-col">
          标书数量：{{formData.BSSL}}
        </div>
        <div class="label-col">
          查重人：{{ formData.CJRXM }}
        </div>
      </div>

      <div class="label-row pdf-details">
        <div class="label-col">
          查重时间：{{ formData.CJSJ }}
        </div>
        <div class="label-col">
          报告生成时间：{{ nowDate }}
        </div>
      </div>

      <div class="title1-text pdf-details">标书查重清单：</div>

      <div class="table-head pdf-details">
        <div class="table-column" style="width: 6mm">序号</div>
        <div class="table-column" style="flex: 1">原文件（A）</div>
        <div class="table-column" style="flex: 1">对比文件（B）</div>

        <div class="table-column-span">
          <div class="table-column">文本重复</div>
          <div class="table-column-head">
            <div class="table-column" style="width: 14mm;">重复数量</div>
            <div class="table-column" style="width: 15mm;">对A重复率</div>
            <div class="table-column" style="width: 15mm;">对B重复率</div>
          </div>
        </div>

        <div class="table-column" style="width: 18mm;">公司信息重复</div>
        <div class="table-column" style="width: 14mm;">图片重复</div>
<!--        <div class="table-column" style="width: 18mm;">错误一致重复</div>-->
      </div>

      <div class="table-row pdf-details" v-for="(item,index) in formData.CCQDList" :key="index">
        <div class="table-column" style="width: 6mm">{{ index + 1 }}</div>
        <div class="table-column" style="flex: 1">{{ item.AWJMC }}</div>
        <div class="table-column" style="flex: 1">{{ item.BWJMC }}</div>
        <div class="table-column" style="width: 14mm;">{{ item.WBCFSL }}</div>
        <div class="table-column" style="width: 15mm;">{{ item.DACFL }}</div>
        <div class="table-column" style="width: 15mm;">{{ item.DBCFL }}</div>

        <div class="table-column" style="width: 18mm;">{{ item.GSXXCFSL }}</div>
        <div class="table-column" style="width: 14mm;">{{ item.TPCF }}</div>
<!--        <div class="table-column" style="width: 18mm;">{{ item.CWYZCF }}</div>-->
      </div>


      <div class="title1-text pdf-details" style="margin-top: 5mm">详情：</div>

      <div style="margin-bottom: 4mm" v-for="(item,index) in formData.CCQDList" :key="index">
        <div class="title2-text pdf-details">
          {{ index + 1 }}.源文件（A）：{{ item.AWJMC }}&nbsp;&nbsp;VS&nbsp;&nbsp;对比文件（B）：{{ item.BWJMC }}
        </div>
        <div v-for="(ii,ind) in CCFWOptions" :key="ind">
          <div class="title3-text pdf-details">
            <div>
              >&nbsp;{{ ii.DMMC }}
            </div>
            <div class="label-row" style="color: black;gap: 4mm;margin-bottom: 0">
              <div class="label-col">
                重复数量：{{ item[ii.DMXX + 'List']?.length || 0 }}
              </div>
              <div class="label-col" v-if="ii.DMXX==='WBCC'">
                重复字数：{{ item.WBCFSL }}
              </div>
              <div class="label-col" v-if="ii.DMXX==='WBCC'">
                对A重复率：{{ item.DACFL }}
              </div>
              <div class="label-col" v-if="ii.DMXX==='WBCC'">
                对B重复率：{{ item.DBCFL }}
              </div>
            </div>
          </div>

          <div class="table-head pdf-details" style="margin-left: 17mm">
            <div class="table-column" style="width: 6mm">序号</div>

            <div class="table-column-span" style="flex: 1">
              <div class="table-column">原文件（A）</div>
              <div class="table-column-head">

                <div class="table-column" style="flex: 1">原始内容</div>
                <div class="table-column" style="width: 14mm;">页码</div>
              </div>
            </div>

            <div class="table-column-span" style="flex: 1">
              <div class="table-column">对比文件（B）</div>
              <div class="table-column-head">

                <div class="table-column" style="flex: 1">原始内容</div>
                <div class="table-column" style="width: 14mm;">页码</div>
              </div>
            </div>

            <div class="table-column" style="width: 15mm;" v-if="ii.DMXX==='WBCC'">重复字数</div>
            <div class="table-column" style="width: 15mm;" v-if="['CWYZXCC','GSXXCC'].includes(ii.DMXX)">备注</div>

          </div>


          <div class="table-row pdf-details" style="margin-left: 17mm" v-for="(iii,iin) in item[ii.DMXX+'List']"
               :key="iin">
            <div class="table-column" style="width: 6mm">{{ iin + 1 }}</div>
            <div class="table-column-span" style="flex: 1;height: auto;">
              <div class="table-column-head" style="height: 100%">
                <div class="table-column" style="flex: 1">
                  <div v-if="['WBCC','GSXXCC','CWYZXCC'].includes(ii.DMXX)" v-html="getHtml(iii,'A')">
                  </div>
                  <img class="preview-pictures" :src="iii.ATPDZ" v-if="ii.DMXX==='TPCC'"/>
                </div>
                <div class="table-column" style="width: 14mm;">{{ iii.AWJYM }}</div>
              </div>
            </div>
            <div class="table-column-span" style="flex: 1;height: auto;">
              <div class="table-column-head" style="height: 100%">
                <div class="table-column" style="flex: 1">
                  <div v-if="['WBCC','GSXXCC','CWYZXCC'].includes(ii.DMXX)" v-html="getHtml(iii,'B')">
                  </div>
                  <img class="preview-pictures" :src="iii.BTPDZ" v-if="ii.DMXX==='TPCC'"/>
                </div>
                <div class="table-column" style="width: 14mm;">{{ iii.BWJYM }}</div>
              </div>
            </div>

            <div class="table-column" style="width: 15mm;" v-if="ii.DMXX==='WBCC'">{{Number(iii.AWBJS || 0) - Number(iii.AWBKS || 0)}}</div>
            <div class="table-column" style="width: 15mm;" v-if="['CWYZXCC','GSXXCC'].includes(ii.DMXX)">{{ iii.BZ }}
            </div>
          </div>
        </div>
      </div>


    </div>
    <banner id="bgBanner"/>


    <div id="res-context">

    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import banner from "@views/bscc/xmbsccgl/bsccbg/banner";
import axiosUtil from "@lib/axiosUtil";
import htmlPdf from "@lib/pdf";
import htmlPagePdf from "@views/bscc/xmbsccgl/bsccbg/pagePdf";
import comFun from "@lib/comFun";
import {axios} from "@core";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {banner},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: true,
      uuid: comFun.newId(),
      CCWJID: props.params.id,
      formData: {
        CCQDList: []
      },
      CCFWOptions: [],
      nowDate: comFun.getNowTime(),
      making: false
    })

    const getFormData = () => {
      let params = {
        CCWJID: state.CCWJID
      }
      state.loading=true
      axiosUtil.get('/backend/bscc/bsccgl/selectCcwjQbccjg', params).then(res => {
        if (res.data) {
          let imgPList = []
          state.formData = res.data
          state.formData.CCQDList.forEach(item => {
            item.TPCCList.forEach(ii => {
              imgPList.push(...[
                getImageBase64(ii, `/backend/minio/downloadPreviewImage?path=${ii.AWJNR}`, 'ATPDZ'),
                getImageBase64(ii, `/backend/minio/downloadPreviewImage?path=${ii.BWJNR}`, 'BTPDZ')
              ])
            })
          })
          Promise.all(imgPList).then(res=>{
            console.log('加载完了k')
            initPage()
          }).catch(e=>{
            console.error(e)
            state.loading=false
          })
        }
      }).catch(res=>{
        ElMessage.error('数据加载失败')
      })
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res => {
        state[resList] = res.data || []
      })
    }
    // const pdfFunc = () => {
    //   if (state.loading) {
    //     return Promise.reject('数据加载中,请稍后尝试')
    //   }
    //   return new Promise(resolve => {
    //     nextTick(() => {
    //       const labelList = document.getElementsByClassName('pdf-details');
    //       const banner = document.getElementById('bgBanner');
    //       htmlPdf('标书查重对比报告', document.getElementById('pdf-context'), labelList, null, false, banner, '江汉油田市场化运行管理系统').then(res => {
    //         state.uuid = comFun.newId()
    //         resolve(res)
    //       })
    //     })
    //   })
    // }


    const pdfFunc = () => {
      if (state.loading) {
        return Promise.reject('数据加载中,请稍后尝试')
      }
      return new Promise(resolve => {
        nextTick(() => {
          const htmlList = document.getElementsByClassName('page-context');
          const banner = document.getElementById('bgBanner');
          htmlPagePdf('标书查重对比报告', htmlList, null, false, banner, '西北油田市场管理信息系统').then(res => {
            // state.uuid = comFun.newId()
            resolve(res)
          })
        })
      })


    }

    const makeBg = () => {
      state.making=true
      pdfFunc().then(res=>{
        ElMessage.success('已下载报告')
        state.making=false
      })
    }

    const initPage = () => {
      nextTick(() => {
        spiltPageDom()
        state.loading=false
      })
    }

    const spiltPageDom = () => {
      const html = document.getElementById('pdf-context')
      const labelList = document.getElementsByClassName('pdf-details')
      const banner = document.getElementById('bgBanner')
      const resDom = document.getElementById('res-context')
      let defineHeight = 277
      if (banner) {
        defineHeight -= 12
      }

      const addContainer = () => {//生成新的容器
        const newContainer = document.createElement('div')
        newContainer.classList.add('A4-context')
        newContainer.classList.add('page-context')
        newContainer.style.width = '210mm'
        newContainer.style.border = '1px solid #d5d0d0'
        newContainer.style.fontSize = '3mm'
        newContainer.style.color = 'black'
        return newContainer
      }

      const copyRootContainer = (dom) => {
        const findRootContainer = (pdom) => {
          if (pdom.id === 'pdf-context') {
            return null
          } else {
            let ppdom = findRootContainer(pdom.parentNode)
            if (ppdom) {
              let cpdom = pdom.cloneNode(false)
              ppdom.appendChild(cpdom)
              return cpdom
            } else {
              return pdom.cloneNode(false)
            }
          }
        }
        const divParent = dom.parentNode;
        let copyDom = findRootContainer(divParent)
        // console.log('copyDom',dom,copyDom)
        return copyDom
      }

      const pageHeight = Math.floor(defineHeight * html.scrollWidth / 190) //计算pdf高度
      const startHeight = html.offsetTop

      let pageNum = 1

      let pageContainer

      let lastDivParent = null //上一个父元素
      let lastCopyParent = null //上一个复制的父元素
      let pageDom = []
      let allDomPage = []
      for (let i = 0; i < labelList.length && i < 5000;) { //循环获取的元素，上限5000防止死循环
        let domItem = labelList[i]
        if (domItem.offsetTop + domItem.offsetHeight - startHeight > pageNum * pageHeight) { //计算是否超出一页
          allDomPage.push(pageDom)
          pageDom = []
          pageNum++
        } else {
          pageDom.push(domItem)
          i++
        }
      }
      allDomPage.push(pageDom)

      allDomPage.forEach(list => {
        pageContainer = addContainer()
        lastDivParent = null
        lastCopyParent = null
        list.forEach(domItem => {
          const divParent = domItem.parentNode
          if (divParent.id === 'pdf-context') {
            pageContainer.appendChild(domItem)
          } else {
            if (lastDivParent === divParent) {  //如果父元素和上一个相同，直接添加到复制的父元素
              lastCopyParent.appendChild(domItem)
            } else {   //否则复制一个新的父元素并添加
              lastCopyParent = copyRootContainer(domItem)
              lastCopyParent.appendChild(domItem)
              pageContainer.appendChild(lastCopyParent)
            }
          }
          lastDivParent = divParent
        })
        resDom.appendChild(pageContainer)
      })
    }


    const getHtml = (row, p) => {
      let text = row[p + 'WJNR']
      let start = row[p + 'WBKS']
      let end = row[p + 'WBJS']
      if (start !== null && end !== null) {
        let part1 = text.slice(0, Number(start))
        let part2 = text.slice(Number(start), Number(end) + 1)
        let part3 = text.slice(Number(end) + 1)
        text = `${part1}<span style="color: red">${part2}</span>${part3}`
      }
      return text
    }

    const blobToBase64 = (blob) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }

    const getImageBase64 = async (row, url, prop) => {
      let response = await axios.get(url, {params: null, responseType: "blob"})
      let blob = new Blob([response.data], {type: 'image/jpeg'});


      let base64String = await blobToBase64(blob)
      row[prop] = base64String
      return ''


    }

    onMounted(() => {
      getDMBData('CCFW', 'CCFWOptions')
      getFormData()
    })

    return {
      ...toRefs(state),
      pdfFunc,
      getHtml,
      initPage,
      getImageBase64,
      makeBg

    }
  }

})
</script>

<style scoped>
.context {
  padding: 20px;
}

.A4-context {
  /*height: 297mm;*/
  width: 210mm;
  /*border: 1px solid #d5d0d0;*/
  font-size: 3mm;
  color: black;
}

.title-text {
  width: 100%;
  font-size: 6mm;
  text-align: center;
  margin-top: 1mm;
  margin-bottom: 2mm;
}

.describe-text {
  width: 100%;
  text-align: center;
  margin-bottom: 5mm;
}

.title1-text {
  margin-left: 5mm;
  font-weight: bolder;
  margin-bottom: 5mm;
  font-size: 3.5mm;
}

.label-row {
  display: flex;
  padding: 0 10mm;
  justify-content: space-between;
  margin-bottom: 4mm;
}

.table-head {
  display: flex;
  border-left: 1px solid #e0e3e8;
  border-top: 1px solid #e0e3e8;
  margin: 0 10mm;
  font-weight: bold;
}

.table-column {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 4mm;
  border-bottom: 1px solid #e0e3e8;
  border-right: 1px solid #e0e3e8;
  padding: 1mm;
  background-color: white;
}

.table-column-span {
  flex-direction: column;
}

.table-column-head {
  display: flex;
}

.table-row {
  display: flex;
  border-left: 1px solid #e0e3e8;
  margin: 0 10mm;
}

.title2-text {
  margin-left: 10mm;
  margin-top: 10px;
  font-weight: bolder;
}

.title3-text {
  margin-top: 5mm;
  display: flex;
  color: #2A96F9;
  margin-left: 15mm;
  margin-bottom: 4mm;
}

.preview-pictures {
  height: 50px;
}
</style>
