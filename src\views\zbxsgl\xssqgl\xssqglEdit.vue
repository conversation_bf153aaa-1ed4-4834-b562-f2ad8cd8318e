<template>
  <div v-loading="loading">
    <zbfaTab class="tab-pane" ref="zbfaTab" :params="params" v-model="formData" />
    <el-form :model="formData" ref="vForm" class="lui-card-form" label-position="right"
             label-width="250px"
             size="default" @submit.prevent>
      <el-row :gutter="0" class="grid-row" v-if="['1', 'new'].includes(value.activityId) && value.processId==='XSGL_XXSQ_NEW'">


        <el-col :span="24" class="grid-cell" v-if="formData.BLRList[0] && !formData.BLRList[0].hide">
          <el-form-item label="二级单位专业管理部门科室长：" prop="BLRList.0.VALUE">
            <el-cascader v-model="formData.BLRList[0].VALUE" :options="ZYGLBMKSTree" filterable :disabled="!editable"
                    :show-all-levels="false"     :props="{label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false,multiple: true}"
                         clearable/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell" v-if="formData.BLRList[1] && !formData.BLRList[1].hide">
          <el-form-item label="二级单位业务分管领导：" prop="BLRList.1.VALUE">
            <el-select
                v-model="formData.BLRList[1].VALUE"
                :disabled="!editable"
                multiple
                filterable
                placeholder="请选择"
                style="width: 100%">
              <el-option
                  v-for="item in YWFGLDOptions"
                  :key="item.USER_LOGINNAME"
                  :label="`${item.USER_NAME}（${item.ORGNA_NAME}）`"
                  :value="item.USER_LOGINNAME"/>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell" v-if="formData.BLRList[3] && !formData.BLRList[3].hide">
          <el-form-item label="油田专业部门部室：" prop="BLRList.3.VALUE">
            <el-cascader v-model="formData.BLRList[3].VALUE" :options="YTZYBMBSTree" filterable :disabled="!editable"
                   :show-all-levels="false"      :props="{label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false,multiple: true}"
                         clearable/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell" v-if="formData.BLRList[2] && !formData.BLRList[2].hide">
          <el-form-item label="二级单位主要领导：" prop="BLRList.2.VALUE">
            <el-select
                v-model="formData.BLRList[2].VALUE"
                :disabled="!editable"
                multiple
                filterable
                placeholder="请选择"
                style="width: 100%">
              <el-option
                  v-for="item in ZYLDOptions"
                  :key="item.USER_LOGINNAME"
                  :label="`${item.USER_NAME}（${item.ORGNA_NAME}）`"
                  :value="item.USER_LOGINNAME"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="0" class="grid-row" v-if="['1', 'new'].includes(value.activityId) && value.processId==='XSGL_XXSQ_JGBM'">
        <el-col :span="24" class="grid-cell" v-if="formData.BLRList[0] && !formData.BLRList[0].hide">
          <el-form-item label="机关部门分管领导：" prop="BLRList.0.VALUE">
            <el-select
                v-model="formData.BLRList[0].VALUE"
                :disabled="!editable"
                multiple
                filterable
                placeholder="请选择"
                style="width: 100%">
              <el-option
                  v-for="item in JGBMFGLDOptions"
                  :key="item.USER_LOGINNAME"
                  :label="`${item.USER_NAME}（${item.ORGNA_NAME}）`"
                  :value="item.USER_LOGINNAME"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell" v-if="formData.BLRList[1] && !formData.BLRList[1].hide">
          <el-form-item label="油田专业部门部室：" prop="BLRList.1.VALUE">
            <el-cascader v-model="formData.BLRList[1].VALUE" :options="YTZYBMBSTree" filterable :disabled="!editable"
                      :show-all-levels="false"   :props="{label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false,multiple: true}"
                         clearable/>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>

        <div style="width: 100%;margin-top: 10px;margin-bottom: 10px;justify-content: center;display: flex">
          <el-button size="default" type="success" @click="saveData('save')" v-if="editable">保存</el-button>
          <el-button size="default" type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
          <el-button size="default" @click="closeForm">返回</el-button>
        </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, computed} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import zbfaTab from "@views/zbxsgl/xssqgl/commonTab/zbfaTab";
import zbwjTab from "@views/zbxsgl/xssqgl/commonTab/zbwjTab";


export default defineComponent({
  name: '',
  components: {zbfaTab, zbwjTab},
  props: {
    params: {
      type: Object,
      required: true
    },
    value: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      FAID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        SFFBD: '0',

        XMXX: {},
        XSWJ: {
          WJID: comFun.newId(),
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
          SHZT: '0',
          YWZT: '1',
          SFWSPS: '0'
        },
        BDList: [{
          FABDID: comFun.newId(),
          FAID: props.params.id,
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
          DWList: [],
        }],
        // DWList: [],
        BLRList: []
      },

      BLRModel: {
        'XSGL_XXSQ_NEW':[
          {VALUE: [], TYPE: 'GR', ACTIVITYID: '3', LABEL: '二级单位专业管理部门科室长',required: true},
          {VALUE: [], TYPE: 'GR', ACTIVITYID: '6', LABEL: '二级单位业务分管领导',required: true},
          {VALUE: [], TYPE: 'GR', ACTIVITYID: '7', LABEL: '二级单位主要领导',hide: true,required: true},
          {VALUE: [], TYPE: 'BM', ACTIVITYID: '8', LABEL: '油田专业部门部室',hide: true,required: true},
        ],
        'XSGL_XXSQ_JGBM':[
          {VALUE: [], TYPE: 'GR', ACTIVITYID: '4', LABEL: '机关部门分管领导',required: true},
          {VALUE: [], TYPE: 'BM', ACTIVITYID: '6', LABEL: '油田专业部门部室',required: false},
        ],
      },

      ZYGLBMKSZOptions: [],
      YWFGLDOptions: [],
      JGBMFGLDOptions: [],//机关部门分管领导
      ZYLDOptions:[],//二级单位主要领导

      ZYGLBMKSList: [],
      ZYGLBMKSTree: [],
      YTZYBMBSList: [],
      YTZYBMBSTree: [],

      ActiveTab: 'ZBFA'
    })

    watch(()=>state.formData.XMXX.XMID,()=>{
      changeBlrList()
    },{deep: true})


    watch(()=>state.formData.BDList,()=>{
      console.error('BDFSBH')
      changeBlrList()
    },{deep: true})

    watch(()=>state.formData.ZJLY,()=>{
      changeBlrList()
    },{deep: true})


    const getFormData = () => {
      let params = {
        FAID: state.FAID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/xssqgl/selectXssqById', params).then((res) => {
        state.formData = res.data
        if (state.formData.BLRList.length === 0) {
          state.formData.BLRList = state.BLRModel[props.value.processId]
        } else {
          state.formData.BLRList.forEach((item, index) => {
            item.VALUE = item.VALUE ? item.VALUE.split(',') : []
            item.LABEL = state.BLRModel[props.value.processId][index]?.LABEL
            item.required = state.BLRModel[props.value.processId][index]?.required

          })
        }
        state.loading = false
      })
    }


    const saveData = (value) => {
      let type = value === '1' ? 'submit' : 'save'
      return new Promise((resolve, reject) => {
        if (type === 'save') {
          resolve(submitForm(type))
        } else {
          validateForm().then(() => {
            resolve(submitForm(type))
          }).catch(msg => {
            ElMessage.error(msg)
            reject(msg)
          })
        }
      })


    }

    const submitForm = (type) => {
      return new Promise((resolve,reject) => {
        let data = {
          ...props.value,
          businessId: props.value.businessId || props.params.id,
          processInstanceName: state.formData.XMXX.XMMC + '-选商申请',
          conditionStr: "lczx=1",
        }
        emit("update:value", data)

        if (!['1', 'new'].includes(props.value.activityId)) {
          resolve(true)
          return
        }


        let params = {
          ...state.formData,
          FAID: state.FAID,
          XSWJ: {
            ...state.formData.XSWJ,
            FAID: state.FAID,
            XGRZH: state.userInfo.userLoginName,
            XGSJ: comFun.getNowTime(),
          },
          XGRZH: state.userInfo.userLoginName,
          XGSJ: comFun.getNowTime(),

          BLRList: state.formData.BLRList.map((item, index) => {
            return {
              ...item,
              BUSINESSID: props.params.id,
              PROCESSID: props.value.processId,
              CJRZH: state.userInfo.userLoginName,
              CJRXM: state.userInfo.userName,
              CJDWID: state.userInfo.orgnaId,
              CJSJ: comFun.getNowTime(),
              SHZT: '1',
              XGRZH: state.userInfo.userLoginName,
              XGSJ: comFun.getNowTime(),
              NAME: getBlrName(item.VALUE,index),
              VALUE: item.VALUE.join(',')
            }
          })
        }
        params.BDList.forEach(item => item.XMID = params.XMID)
        if (params.SFFBD === '0') {
          params.BDList[0].BDMC = params.XMXX.XMMC
        }
        if (type === 'submit') {
          params.SHZT = '1'
        }
        state.loading = true
        axiosUtil.post('/backend/xsgl/xssqgl/saveXssqForm', params).then(res => {
          state.loading = false
          if(state.formData.ZJLY==='TZ' && state.formData.XMXX.ZYFL==='ADMGC'){
            ElMessage.success('提交成功')
            emit("closeAudit")
            reject()
          }else {
            resolve(true)
          }
        })
      })

    }

    const getBlrName = (values,index) => {
      if(props.value.processId==='XSGL_XXSQ_NEW'){
        return values.map(ii=>{
          if (index === 0) {
            return state.ZYGLBMKSList.find(iii => iii.ORGNA_ID === ii)?.ORGNA_NAME
          } else if (index === 1) {
            return state.YWFGLDOptions.find(iii => iii.USER_LOGINNAME === ii)?.USER_NAME
          } else if (index === 2) {
            return state.ZYLDOptions.find(iii => iii.USER_LOGINNAME === ii)?.USER_NAME
          }else if (index === 3) {
            return state.YTZYBMBSList.find(iii => iii.ORGNA_ID === ii)?.ORGNA_NAME
          }
        }).join(',')
      }else if(props.value.processId==='XSGL_XXSQ_JGBM'){
        return values.map(ii=>{
          if (index === 0) {
            return state.JGBMFGLDOptions.find(iii => iii.USER_LOGINNAME === ii)?.USER_NAME
          } else if (index === 1) {
            return state.YTZYBMBSList.find(iii => iii.ORGNA_ID === ii)?.ORGNA_NAME
          }
        }).join(',')
      }


    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return Promise.all([
        instance.proxy.$refs['zbfaTab'].validateForm(),
        validateBlr()
      ])
    }

    const validateBlr = () => {
      return new Promise((resolve, reject) => {
        let nullRow = state.formData.BLRList.find(item => item.required &&!item.hide && (!item.VALUE || item.VALUE.length === 0))
        if (nullRow) {
          reject('请选择' + nullRow.LABEL)
        } else {
          resolve()
        }
      })

    }

    const changeBlrList = () => {
      if(state.formData.BLRList.length>0){
        if(state.formData.XMXX.XMID){
          let ZYFL=state.formData.XMXX.ZYFL
          if(ZYFL==='ADMGC' && state.formData.ZJLY==='TZ'){
            state.formData.BLRList.forEach(item=>item.hide=true)
          }else {
            let YQDWList=[]
            state.formData.BDList.forEach(item=>{
              YQDWList.push(...item.DWList)
            })
            if(props.value.processId==='XSGL_XXSQ_NEW'){
              state.formData.BLRList[0].hide=false
              state.formData.BLRList[1].hide=false

              if(state.formData.XMXX.SFTSDW==='1'){
                state.formData.BLRList[2].hide=false
                state.formData.BLRList[3].hide=true
              }else {
                state.formData.BLRList[2].hide=true
                state.formData.BLRList[3].hide=false
                let sum=0
                state.formData.BDList.forEach(item=>{
                  if(item.YJJE){
                    sum+=Number(item.YJJE)
                  }
                })


                let SFSHN= YQDWList.length>0 && !YQDWList.find(item=> item.DWXTGSDM!=='SHN')

                if((['ASYGC','B'].includes(ZYFL) && SFSHN) || ZYFL==='B_ZXSP'){
                  state.formData.BLRList[3].hide=true
                }

                if(['ASYGC','ADMGC'].includes(ZYFL) && sum<200){
                  state.formData.BLRList[3].hide=true
                }

                if(['B'].includes(ZYFL) && sum<50){
                  state.formData.BLRList[3].hide=true
                }
              }
            }else if(props.value.processId==='XSGL_XXSQ_JGBM'){
              state.formData.BLRList[0].hide=false
              state.formData.BLRList[1].hide=false
              let sum=0
              state.formData.BDList.forEach(item=>{
                if(item.YJJE){
                  sum+=Number(item.YJJE)
                }
              })


              let SFSHN= YQDWList.length>0 && !YQDWList.find(item=> item.DWXTGSDM!=='SHN')

              if((['ASYGC','B'].includes(ZYFL) && SFSHN) || ZYFL==='B_ZXSP'){
                state.formData.BLRList[1].hide=true
              }

              if(['ASYGC','ADMGC'].includes(ZYFL) && sum<200){
                state.formData.BLRList[1].hide=true
              }
              if(['B'].includes(ZYFL) && sum<50){
                state.formData.BLRList[1].hide=true
              }
            }

          }
        }else {
          state.formData.BDList.forEach(item=>item.hide=true)
        }
      }

    }

    const getXmxx = (XMID) => {
      let params = {
        XMID: XMID
      }
      axiosUtil.get('/backend/xsgl/xssqgl/selectXmxxByXmid', params).then(res => {
        state.formData.XMXX = res.data
        state.formData.XMID = XMID
      })
    }

    const closeForm = () => {
      emit('close')
    }

    const getBlrOptions = (optionName, ROLE) => {
      let params = {
        ROLE: ROLE,
        ORGNA_TWO_ID: state.userInfo.orgnaId
      }
      axiosUtil.get('/backend/common/selectUserByRole', params).then(res => {
        state[optionName] = res.data || []
      })
    }

    const getOrgList = (optionName, ROLE, DWID) => {
      let params = {
        ROLE: ROLE,
        DWID: DWID
      }
      axiosUtil.get('/backend/common/selectJyjsdwTree', params).then((res) => {
        state[optionName + 'List'] = res.data || []
        state[optionName + 'Tree'] = comFun.treeData(state[optionName + 'List'], 'ORGNA_ID', 'PORGNA_ID', 'children', '0')
      });
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      } else {
        state.formData.BLRList=state.BLRModel[props.value.processId]

        state.formData.XSFS = props.params.XSFS
        state.formData.SQLX = props.params.SQLX
        if (props.params.XMID) {
          getXmxx(props.params.XMID)
        }
      }
      if (props.params.activeTab) {
        state.ActiveTab = props.params.activeTab
      }
      getDMBData("XSFS", "XSFSOptions")

      if(props.value.processId==='XSGL_XXSQ_NEW'){
        getOrgList('ZYGLBMKS', 'XSGL_EJDW_ZYGLBMKSZ', state.userInfo.orgnaId)
        getOrgList('YTZYBMBS', 'XSGL_YTZYBMBS', null)
        getBlrOptions('YWFGLDOptions', 'XSGL_EJDW_YWFGLD')
        getBlrOptions('ZYLDOptions', 'XSGL_EJDW_ZYLD')
      }else if(props.value.processId==='XSGL_XXSQ_JGBM'){
        getOrgList('YTZYBMBS', 'XSGL_YTZYBMBS', null)
        getBlrOptions('JGBMFGLDOptions', 'XSGL_YTJGBMKSZ')
      }


    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
    }
  }

})
</script>

<style scoped>
.tab-pane {
  overflow: auto;
}
</style>
