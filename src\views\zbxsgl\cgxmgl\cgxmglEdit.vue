<template>
    <div>
        <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
                 label-width="160px"
                 size="default" v-loading="loading" @submit.prevent>
            <el-row :gutter="0" class="grid-row">

                <el-col :span="14" class="grid-cell">
                    <el-form-item label="采购项目名称：" prop="XMMC">
                        <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="!editable">
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="10" class="grid-cell">
                    <el-form-item label="项目编号：" prop="XMBH">
                        <el-input v-model="formData.XMBH" type="text" placeholder="请输入" disabled></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="14" class="grid-cell">
                    <el-form-item label="施工时间：" prop="SGSJ">
                        <el-date-picker
                                v-model="formData.SGKSSJ"
                                :disabled="!editable"
                                type="date"
                                clearable
                                style="width: 40%"
                                placeholder="施工时间开始"
                                value-format="YYYY-MM-DD"
                        ></el-date-picker>
                        <div>至</div>
                        <el-date-picker
                                v-model="formData.SGJSSJ"
                                :disabled="!editable"
                                type="date"
                                clearable
                                style="width: 40%"
                                placeholder="施工时间结束"
                                value-format="YYYY-MM-DD"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>

            </el-row>

            <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
                <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
                <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
                <el-button @click="closeForm">返回</el-button>
            </div>

        </el-form>
    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import vsAuth from "@lib/vsAuth";
    import comFun from "@lib/comFun";
    import axiosUtil from "@lib/axiosUtil";
    import {ElMessage} from "element-plus";


    export default defineComponent({
        name: '',
        components: {},
        props: {
            params: {
                type: Object,
                required: true
            },
        },
        setup(props, {emit}) {
            const state = reactive({
                userInfo: vsAuth.getAuthInfo().permission,
                loading: false,
                editable: props.params.editable,
                XMID: props.params.id,
                formData: {
                    SSDWID: vsAuth.getAuthInfo().permission.orgnaId,
                    SSDWMC: vsAuth.getAuthInfo().permission.orgnaName,
                    CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
                    CJRXM: vsAuth.getAuthInfo().permission.userName,
                    CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
                    CJSJ: comFun.getNowTime(),
                    SHZT: '0',
                    YWZT: '0'
                },
                rules: {
                    XMLB: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    SSDWID: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    XMMC: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    SGSJ: [
                        {
                            trigger: "blur",
                            validator: (rule, value, callback) => {
                                if (!state.formData.SGKSSJ || !state.formData.SGJSSJ) {
                                    callback(new Error('字段值不可为空'));
                                } if (state.formData.SGKSSJ > state.formData.SGJSSJ) {
                                    callback(new Error('施工结束时间不得早于施工开始时间'));
                                } else {
                                    callback();
                                }
                            },
                        },
                    ],
                },

                ZRZYTree: [],
                SSDWOptions: [],
            });

            const getFormData = () => {
                let params = {
                    XMID: state.XMID
                };
                state.loading = true;
                axiosUtil.get('/backend/xsgl/cgxmgl/selectCgxmById', params).then((res) => {
                    state.formData = res.data;
                    state.loading = false;
                })
            };

            const saveData = (type) => {
                if (type === 'save') {
                    submitForm(type);
                } else {
                    validateForm().then(res => {
                        if (res) {
                            submitForm(type);
                        }
                    })
                }
            };

            const submitForm = (type) => {
                let params = {
                    ...state.formData,
                    XMID: state.XMID,
                    XGRZH: state.userInfo.userLoginName,
                    XGSJ: comFun.getNowTime(),
                };
                if (type === 'submit') {
                    params.SHZT = '1';
                    params.YWZT = '1';
                }
                state.loading = true
                axiosUtil.post('/backend/xsgl/cgxmgl/saveCgxmForm', params).then(res => {
                    if (res.data && res.data.ZT === '1') {
                        ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`);
                        closeForm();
                    } else {
                        ElMessage.warning(res.data?.msg);
                        createCgxmbh()
                    }
                    state.loading = false;
                })
            };

            const instance = getCurrentInstance();
            const validateForm = () => {
                return new Promise(resolve => {
                    instance.proxy.$refs['vForm'].validate(valid => {
                        if (valid) {
                            resolve(true);
                        } else {
                            ElMessage({
                                message: '请完善页面信息',
                                type: 'error',
                            });
                            resolve(false);
                        }
                    })
                })
            };

            const createCgxmbh = () => {
                axiosUtil.get('/backend/xsgl/cgxmgl/createCgxmbh', null).then(res => {
                    state.formData.XMBH = res.data;
                })
            };

            const getDMBData = async (DMLBID, resList) => {
                let params = {
                    DMLBID
                };
                const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
                state[resList] = res.data
            };

            const closeForm = () => {
                emit('close')
            };

            const getZrzyList = () => {
                let params = {};
                axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
                    state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0')
                });
            };

            onMounted(() => {
                //getZrzyList();
                if (props.params.operation !== 'add') {
                    getFormData();
                } else {
                    createCgxmbh();
                }
                // getDMBData("SSDW", "SSDWOptions")
            });

            return {
                ...toRefs(state),
                closeForm,
                saveData
            }
        }

    })
</script>

<style scoped>
    :deep .el-form-item__error {
        margin-top: 26px;
    }
</style>
