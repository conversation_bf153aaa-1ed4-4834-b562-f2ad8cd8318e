<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目名称：" prop="XMMC">
            <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="建设单位：" prop="JSDW">
            <el-input v-model="formData.JSDW" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="合同名称：" prop="HTMC">
            <el-input v-model="formData.HTMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="合同金额（万元）：" prop="HTJE">
            <el-input v-model="formData.HTJE" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="合同日期开始：" prop="HTRQKS">
            <el-date-picker
                v-model="formData.HTRQKS"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="合同日期结束：" prop="HTRQJS">
            <el-date-picker
                v-model="formData.HTRQJS"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="工程范围：" prop="GCFW">
            <el-input v-model="formData.GCFW" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="承担工作内容：" prop="CDGZNR">
            <el-input v-model="formData.CDGZNR" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

      </el-row>

      <el-collapse v-model="activeName" accordion>
        <el-collapse-item title="相关附件" name="xgfj">
          <vsFileUploadTable
              ref="fileTable"
              style="width: 100%;height: 200px"
              YWLX="CBSYJXX"
              :key="CBSYJID"
              :busId="CBSYJID"
              v-model:fileTableData="fileTableData"
              :editable="editable"
          />
        </el-collapse-item>
      </el-collapse>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData(true)" v-if="editable">继续添加</el-button>
        <el-button type="primary" @click="saveData(false)" v-if="editable">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsFileUploadTable from "@views/components/vsFileUploadTable";


export default defineComponent({
  name: '',
  components: {vsFileUploadTable},
  props: {
    currentRow: {
      type: Object,
      required: true,
      default: {}
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.currentRow.editable,
      CBSYJID: props.currentRow.id,
      formData: {
        ...props.currentRow
      },
      rules: {
        XMMC: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      fileTableData: [],
      activeName: 'xgfj'
    })


    watch(()=>props.currentRow,(val)=>{
      state.formData={
        ...props.currentRow
      }
      state.editable=props.currentRow.editable
      state.CBSYJID=props.currentRow.id
    },{deep: true})


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (isContinue) => {
      let params={
        ...state.formData,
        CBSYJID: state.formData.id
      }
      emit('submit', params,isContinue)


    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }



    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      closeForm,
      saveData
    }
  }

})
</script>

<style scoped>
.el-collapse .el-collapse-item ::v-deep .el-collapse-item__header {
  background-color: #e3e6f6;
  padding-left: 10px;
}
</style>
