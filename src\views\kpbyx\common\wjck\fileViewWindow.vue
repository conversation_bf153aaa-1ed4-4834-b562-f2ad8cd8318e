<template>
  <div>
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default" style="height: 100vh; width: 100%">
      <el-row style="width: 100%; height: 100%;">
        <el-col :span="6" style="border: 1px solid #BEBEBE; height: 100%;">
          <div style="width: calc(100% - 20px); height: calc(100% - 20px); padding: 10px">
            <span slot="label"><i class="el-icon-date"></i> 请选择要打开的文件</span>
            <el-menu default-active="1" class="el-menu-vertical-demo" @open="handleOpen"
                     @close="handleClose" ref="menu">
              <el-sub-menu v-for="(item, index) in contentList" :key="item.MLBS"
                           :index="index">
                <template #title><div style="color: #09d7ff">{{ item.MLMC }}</div></template>
                <el-menu-item-group>
                  <el-menu-item v-for="(itemEj) in item.fileList" :key="item.ID"
                                :index="itemEj.ID" style="cursor:pointer;"
                                @click="fileClick(item, itemEj)">
                    <div style="width: 230px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                         :title="itemEj.FILENAME"><el-icon @click.stop="downloadFile(itemEj)"><Download /></el-icon>{{ itemEj.FILENAME }}</div>
                  </el-menu-item>
                </el-menu-item-group>
              </el-sub-menu>
            </el-menu>
          </div>

        </el-col>

        <el-col :span="18" style="background-color: #2A2A2E">
          <el-tabs
              class="fileClass"
              v-model="activeTab"
              style="width: 100%; height: 93%"
              type="card"
              editable @edit="handleTabsEdit">
            <el-tab-pane
                :key="item.name + index"
                v-for="(item, index) in editableTabs"
                :label="item.title"
                :name="item.name + index"
                style="width: 100%; height: 100%"
            >
              <!--<iframe style="width:100%;height:100%;margin-top: 10px;"
                      :src="item.content" frameborder=0 scrolling="no"></iframe>-->
              <!--<pdfView :source="{url: item.content}" style="width: 100%; height: 800px; overflow: auto" />-->
              <div class="pdf-container">
                <pdfPreView :pdfUrl="item.content" style="width: 100%; height: 94vh; overflow: auto"></pdfPreView>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </el-form>

  </div>
</template>

<script>
import {Download} from "@element-plus/icons-vue";
import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import pdfView from 'vue-pdf-embed'
import pdfPreView from './pdfPreview'

export default defineComponent({
  name: '',
  components: {pdfView, pdfPreView,Download},
  props: {
    params: {
      type: Object,
      default: {},
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      // 待删除
      JLID: props.params.JLID,
      activeTab: '',
      contentList: [
        {
          MLMC: '招标文件',
          MLBS: 'zbwj',
          fileList: []
        },
      ],
      editableTabs: [],
    });

    onMounted(() => {
      getFileList();
    });

    const fileClick = (itemFj, itemZj) => {
      let param = {
        title: itemFj.MLMC + itemZj.FILENAME,
        name: itemZj.ID,
        content: '/backend/minio/download?id=' + itemZj.DOWNLOADID,
      };
      openTabPane(param);
    };


    const getFileList = () => {
      let params = {
        JLID: state.JLID,
      };
      state.loading = true;
      axiosUtil.get('/backend/kpbyx/bsjm/selectZtbwjList', params).then((res) => {
        if (res && res.data) {
          // 默认第一个文件夹是招标文件
          state.contentList[0].fileList = res.data.ZBWJList;
          // 后面的遍历 投标单位
          state.contentList = [...state.contentList, ...res.data.TBDWList];
        }
        state.loading = false;
      });
    };

    const openTabPane = (row) => {
      if (state.editableTabs.length > 4) {
        ElMessage({
          message: "最多同时打开5个文件！",
          type: "warning",
        });
        return false
      }
      state.editableTabs.push({
        title: row.title,
        name: row.name,
        content: row.content,
      });
      state.activeTab = row.name + (state.editableTabs.length - 1);
    };

    const handleTabsEdit = (targetName, action) => {
      if (action === 'remove') {
        let tabs = state.editableTabs;
        let activeName = state.activeTab;
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if ((tab.name + index) === targetName) {
              let nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                activeName = nextTab.name;
              }
            }
          });
        }

        state.activeTab = activeName;
        state.editableTabs = tabs.filter((tab, index) => (tab.name + index) !== targetName);
      }
    };

    const downloadFile = (value) => {
      window.open('/backend/minio/download?id=' + value.DOWNLOADID);
    }

    return {
      ...toRefs(state),
      fileClick,
      openTabPane,
      handleTabsEdit,
      downloadFile
    }
  }

})
</script>

<style scoped>
.fileClass >>> .el-tabs__content {
  height: 100%;
  width: 100%;
}

/* 针对WebKit和Blink引擎的浏览器 */
::-webkit-scrollbar-thumb {
  background-color: darkgrey; /* 滑块颜色 */
  border-radius: 10px; /* 滑块圆角 */
  border: 2px solid orange; /* 滑块边框 */
}

/* 针对WebKit和Blink引擎的浏览器 */
::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道颜色 */
}

/deep/ .el-tabs__item {
  color: white !important;
}

/deep/ .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  color: #3c85fb !important;
}

/deep/ .el-tabs__new-tab {
  display: none;
}
</style>
