<template>
  <div style="font-size: 14px;">
    <div style="margin-bottom: 10px;border: 1px solid #cbcbcb;padding: 5px"
         v-for="(item,index) in tableData" :key="index">
      <div style="font-weight: bold;color: #409EFF">{{item['ZYMC']}}</div>
      <div v-for="(iitem,iindex) in item.SJXXList" :key="iindex">
        <div style="font-weight: bold;color: #409EFF;cursor: pointer;width: 100%;text-align: right" @click="viewRow(iitem)">查看车辆完整信息》</div>
        <appRow label="信息项：" label-width="90px">{{iitem['XXXMC']}}</appRow>
        <appRow label="车辆名称：" label-width="90px">{{iitem['CLMC']}}</appRow>
        <appRow label="车型：" label-width="90px">{{iitem['CLLXMC']}}</appRow>
        <appRow label="吨(座)位：" label-width="90px">{{iitem['DZW']}}</appRow>
        <appRow label="车牌号：" label-width="90px">{{iitem['CPH']}}</appRow>
        <appRow label="品牌型号：" label-width="90px">{{iitem['PPXH']}}</appRow>
        <appRow label="规格型号：" label-width="90px">{{iitem['GGXH']}}</appRow>
        <appRow label="购置时间：" label-width="90px">{{iitem['GZSJ']}}</appRow>
        <appRow label="相关附件：" label-width="90px">
          <vsfileupload
              :editable="false"
              :busId="iitem.CLID"
              :key="iitem.CLID"
              ywlb="CLFJ"
              busType="dwxx"
              :limit="100"
          ></vsfileupload>
        </appRow>

        <div style="width: 100%;border-bottom: 1px dashed #c4c4c4;margin-top: 10px;margin-bottom: 10px"
             v-if="iindex+1!==item.SJXXList.length"></div>
      </div>
    </div>

    <div class="info-view" v-if="showView">
      <div class="banner">
        <el-icon :size="25" @click="showView=false"><ArrowLeftBold /></el-icon>
        <div style="font-weight: bold;font-size: 16px;color: #409EFF">车辆信息查看</div>
        <div></div>
      </div>

      <div style="padding: 10px;display: flex;align-items: center;gap: 10px;color: #409EFF;font-weight: bold;font-size: 16px">
        <el-icon :size="25"><Bicycle /></el-icon>
        车辆信息
      </div>

      <div style="padding: 10px;margin: 10px;border: 1px solid #8c939d;background-color: white">
        <appRow label="车辆名称：" label-width="120px">{{checkRow['CLMC']}}</appRow>
        <appRow label="车辆类型：" label-width="120px">{{checkRow['CLLXMC']}}</appRow>
        <appRow label="吨（座）位：" label-width="120px">{{checkRow['DZW']}}</appRow>
        <appRow label="车牌号：" label-width="120px">{{checkRow['CPH']}}</appRow>
        <appRow label="生产厂家：" label-width="120px">{{checkRow['SCCJ']}}</appRow>
        <appRow label="品牌型号：" label-width="120px">{{checkRow['PPXH']}}</appRow>
        <appRow label="规格型号：" label-width="120px">{{checkRow['GGXH']}}</appRow>
        <appRow label="大架号：" label-width="120px">{{checkRow['DJH']}}</appRow>
        <appRow label="发动机编号：" label-width="120px">{{checkRow['FDJBH']}}</appRow>
        <appRow label="年检日期：" label-width="120px">{{checkRow['NJRQ']}}</appRow>
        <appRow label="有效结束日期：" label-width="120px">{{checkRow['YXQJSRQ']}}</appRow>
        <appRow label="年检机构：" label-width="120px">{{checkRow['NJJG']}}</appRow>
        <appRow label="规定使用年限：" label-width="120px">{{checkRow['GDSYNX']}}</appRow>
        <appRow label="购置时间：" label-width="120px">{{checkRow['GZSJ']}}</appRow>
        <appRow label="保单编号：" label-width="120px">{{checkRow['BDBH']}}</appRow>
        <appRow label="保单机构：" label-width="120px">{{checkRow['BDJG']}}</appRow>
        <appRow label="相关附件：" label-width="120px">
          <vsfileupload
              :editable="false"
              :busId="checkRow.CLID"
              :key="checkRow.CLID"
              ywlb="CLFJ"
              busType="dwxx"
              :limit="100"
          ></vsfileupload>
        </appRow>

      </div>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import appRow from "@views/app/common/appRow";
import vsfileupload from "@views/components/vsfileupload";
import {ArrowLeftBold,User,Bicycle} from "@element-plus/icons-vue";

export default defineComponent({
  name: '',
  components: {appRow,vsfileupload,ArrowLeftBold,User,Bicycle},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
  },



  setup(props, {emit}) {
    const state = reactive({
      tableData:[],
      checkRow: {},
      showView: false
    })
    watch(()=>props.defaultData,()=>{
      if(props.defaultData){
        let res=[]
        props.defaultData.forEach(item=>{
          let ZYXX=res.find(ii=>item.ZYBM===ii.ZYBM)
          if(ZYXX){
            ZYXX.SJXXList.push(item)
          }else {
            res.push({
              ZYBM: item.ZYBM,
              ZYMC: item.ZYMC,
              SJXXList: [item]
            })
          }
        })
        console.log(res)
        state.tableData=res
      }
    },{immediate: true,deep: true})

    const viewRow = (row) => {
      state.checkRow=row
      state.showView=true
    }


    onMounted(() => {

    })

    return {
      ...toRefs(state),
      viewRow

    }
  }

})
</script>

<style scoped>
.info-view{
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 666;
  background-color: #e1e1e1;
  font-size: 16px;
}
.banner{
  height: 40px;
  background-color: white;
  align-items: center;
  justify-content: space-between;
  display: flex;
}
</style>
