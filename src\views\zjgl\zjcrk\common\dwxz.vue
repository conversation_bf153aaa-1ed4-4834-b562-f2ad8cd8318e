<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-page" label-position="left" label-width="0"
           size="default">
    <el-row :span="24" style="text-align: center;margin-top: 15px;display: flex;align-items: center;gap: 10px">
      <el-input type="text" placeholder="请输入单位名称" v-model="listQuery.DWMC" clearable style="width: 200px;"></el-input>
      <el-button type="success" @click="getDataList"><el-icon><Search/></el-icon>查询</el-button>
    </el-row>
    <div class="container-wrapper" style="margin-top: 10px">
      <el-table
          class="lui-table"
          :data="tableData"
          style="width: 100%; margin-bottom: 20px"
          row-key="ORGNA_ID"
          border
          height="calc(80vh - 250px)"
          ref="singleTableRef"
          highlight-current-row
          :expand-row-keys="expandRow"
          @current-change="handleSelectionChange"
      >
        <el-table-column type="index" width="100"/>
        <el-table-column prop="ORGNA_NAME" label="单位名称"/>
      </el-table>
    </div>
    <el-row :span="24" style="text-align: center">
            <span style="width: 100%;text-align: center">
                <el-button type="success" @click="confirmZy">确定</el-button>
                <el-button type="primary" @click="cancel">取消</el-button>
            </span>

    </el-row>
  </el-form>
</template>
<script>
import {
  defineComponent,
  toRefs,
  reactive,
  defineEmits,
  ref,
  getCurrentInstance,
  onMounted, computed
}
  from 'vue'
// 请求方法 get,post,put,del,downloadFile
import axiosUtil from "@lib/axiosUtil";
import {mixin} from '@src/assets/core';
import {ElMessage, ElMessageBox} from "element-plus";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
export default defineComponent({
  name: '',
  components: {Search,Plus,Upload},
  props: {
    CBSBS: {
      type: String
    },
    showZZY: {
      type: Boolean,
      default: true
    },
  },

  setup(props, {emit}) {
    const ZZYEditable = computed(() => (ZYBM) => {
      let res = false
      state.currentRow.forEach(item => {
        if (item.ZYBM === ZYBM) {
          res = true
        }
      })
      if (state.ZZY === ZYBM && !res) {
        state.ZZY = null
      }
      return res
    })
    const state = reactive({
      formData: {},
      listQuery: {
        ZYMC: '',
        ZT: '',
        page: 1,
        size: 10,
      },
      ZZY: null,
      currentRow: [],
      tableData: [],
      total: 0,
      rules: {},

      expandRow: []

    })
    // 深度合并对象
    const instance = getCurrentInstance()
    const getDataList = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectGzdw', state.listQuery).then((res) => {
        let treeData = transData(res.data, 'ORGNA_ID', 'PORGNA_ID', 'children');
        state.tableData = treeData
        state.expandRow=treeData.map(item=>item.ORGNA_ID)
        state.total = res.data.total
      });
    }
    let singleTableRef = ref(null)
    const resetListQuery = () => {
      state.listQuery.CBSMC = ''
      state.listQuery.ZT = ''
    }
    const handleSelectionChange = (val) => {
      state.currentRow = val
    }
    const selectEnable = (row) => {
      return row.SFMJ == '1'
    }


    const confirmZy = () => {
      if (state.currentRow) {
        emit('parentMethod', state.currentRow)
      } else {
        ElMessage({
          message: `请选择一条数据`,
          type: 'warning',
        })
      }
    }
    /**
     * json格式转树状结构
     * @param   {json}      json数据
     * @param   {String}    id的字符串
     * @param   {String}    父id的字符串
     * @param   {String}    children的字符串
     * @return  {Array}     数组
     */
    const transData = (a, idStr, pidStr, childrenStr) => {
      let r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
      for (; i < len; i++) {
        a[i]['isparent'] = false;
        hash[a[i][id]] = a[i];
      }
      for (; j < len; j++) {
        var aVal = a[j], hashVP = hash[aVal[pid]];
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = []);
          hashVP[children].push(aVal);
        } else {
          r.push(aVal);
        }
      }
      //this.setIsParent(r);
      return r;
    }

    const setIsParent = (arr) => {
      for (var j = 0; j < arr.length; j++) {
        if (arr[j].children && arr[j].children.length > 0) {
          arr[j].isparent = true;
          this.setIsParent(arr[j].children);
        }
      }
    }
    const openDialog = (value) => {
      state.dialogData = value
      state.dialogVisible = true
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }
    onMounted(async () => {
      getDataList()

    })

    const cancel = () => {
      emit("close")
    }
    return {
      ...toRefs(state),
      getDataList,
      singleTableRef,
      confirmZy,
      resetListQuery,
      handleSelectionChange,
      selectEnable,
      transData,
      setIsParent,
      openDialog,
      closeForm,
      cancel,
      ZZYEditable
    }
  }
})
</script>
<style scoped>
/deep/ .el-table .el-table__cell {
  padding: 0;
}

.el-table {
  --el-table-current-row-bg-color: rgb(97, 163, 241, 0.5);
}
</style>
