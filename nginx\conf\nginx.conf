############################################################################powered by Nginx####################################################################################
###########################################################################supported vsui.vue###################################################################################

#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;



events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #proxy_cache_path ../proxy_cache_dir/tmp-test levels=1:2 keys_zone=tmp-test:100m inactive=7d max_size=1000g;  

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    
	####################################开启GZIP################################
	#开启和关闭gzip模式
	gzip on;
	#gizp压缩起点，文件大于1k才进行压缩
	gzip_min_length 1k;

	# gzip 压缩级别，1-9，数字越大压缩的越好，也越占用CPU时间
	gzip_comp_level 6;

	# 进行压缩的文件类型。
	gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/xml text/javascript application/json image/png image/gif image/jpeg;

	#nginx对于静态文件的处理模块，开启后会寻找以.gz结尾的文件，直接返回，不会占用cpu进行压缩，如果找不到则不进行压缩
	# gzip_static on|off

	# 是否在http header中添加Vary: Accept-Encoding，建议开启
	gzip_vary on;

	# 设置压缩所需要的缓冲区大小，以4k为单位，如果文件为7k则申请2*4k的缓冲区 
	gzip_buffers 4 16k;

	# 设置gzip压缩针对的HTTP协议版本
	# gzip_http_version 1.1;

	charset utf-8;
	
	#全局设置nginx的站点根目录，root值不存在时指向nginx目录下的html子目录
	#为保持良好的习惯，应该在server.location中定义每个应用路径的root，而不是修改这个配置
	#root ../dist/;
	
	#全局设置nginx首页文件名
	index index.html;

    server {
		#服务器端口
        listen       9000;
		
		#服务器IP/主机名
        server_name  localhost;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;
		
		
		# by cuiliang on 20210902 请仔细阅读如下信息：
		#请将本节点放在最后，本配置用于处理非页面的资源类文件，如有资源类文件返回html页面，请将文件后缀追加到本项内
		location ~.*\.(gif|jpg|jpeg|png|svg|js|css|json|xml|ico|woff|ttf|pdf|md)$ {
			#root E:\Work-Space\vsui\vue\vsui.vue\vsui.vue.v2.0.0\dist;
			#index index.html index.htm;
			expires 1s;
			add_header Cache-Control no-cache,no-store;
		}
		
		
		
		#本节点配置web应用的路径，如域名为：http://www.vsui.com
		location ^~ /vsui/vue/ {
			
			# root 与 alias只能配置一个，以下两种方式均保证前端虚拟路径为/vsui/vue/的情况下正常访问
			
			#root：此处的root可以单独设置/vsui/vue/指向的主机目录，localtion所指的路径必须是真实存在的物理路径,root后面带不带"/"，都不会影响访问
			#以下root配置下，访问http://www.vsui.com/vsui/vue/a.html实际指定的是E:/Work-Space/vsui/vue/vsui.vue/vsui.vue.v1.2.0/dist/vsui/vue/vsui/vue/a.html。
			#root   ../;
			
			#alias：lias指定的目录是准确的，即/vsui/vue/地址下请求的文件直接是在alias目录下查找的，alias指定的目录后面必须要加上"/"符号.
			#以下虚拟目录配置下，访问http://www.vsui.com/vsui/vue/a.html实际指定的是E:/Work-Space/vsui/vue/vsui.vue/vsui.vue.v1.2.0/dist/vsui/vue/a.html。
			alias  ../dist/;
			
			#此处的 @router 实际上是引用下面的转发，否则在 Vue 路由刷新时可能会抛出 404
			try_files $uri $uri/ /index.html last;
			
			rewrite /vsui/vue/static/js/runtime/config.js /vsui/vue/static/js/runtime/config_9000.js last;
			
			index  index.html index.htm;
		}
		
		# by cuiliang on 20210902 请仔细阅读如下信息：
		#本节点为前端应用对后端vseaf框架的反向代理，请求发出的默认基地址为/vseaf-service，如在开发中变更了，请修改此处
		#proxy_pass为指向的后端vseaf框架真实的地址
		location ^~ /vseaf-service {
			proxy_set_header Host $server_name;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Nginx-Proxy true;
			proxy_pass http://**********:31230;
		}
		location ^~ /apidata/ {
		 alias  ../dist/apidata/;
		}
		
		location /restful/api {
		 proxy_pass http://**********:30666/apidata;
		}
					
    }
	server {
		#服务器端口
        listen       9001;
		
		#服务器IP/主机名
        server_name  localhost;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;
		
		#设定用于存储客户端请求报文的body部分的临时存储路径及子目录结构和数量,通常情况下用户请求报文是不带body的，除开用户往服务器上提交内容或文件。
		#示例
		#	client_body_temp_path /dd/	2 1 1;
        #	    1：表示用一位16进制数字表示一级子目录；0-f;
        #	    2：表示用2位16进程数字表示二级子目录：00-ff;
        #	    3：表示用2位16进程数字表示三级子目录：00-ff;
		#client_body_temp_path path [level1 [level2 [level3]]];
		
		# by cuiliang on 20210902 请仔细阅读如下信息： 
		#本节点为应用示例，参考使用，禁止修改、加工此节点内容，正式环境请将此节点删除
		#本节点配置web应用的路径，如域名为：http://www.vsui.com
		location ^~ / {
			
			# root 与 alias只能配置一个，以下两种方式均保证前端虚拟路径为/vsui/vue/的情况下正常访问
			
			#root：此处的root可以单独设置/vsui/vue/指向的主机目录，localtion所指的路径必须是真实存在的物理路径,root后面带不带"/"，都不会影响访问
			#以下root配置下，访问http://www.vsui.com/vsui/vue/a.html实际指定的是E:/Work-Space/vsui/vue/vsui.vue/vsui.vue.v1.2.0/dist/vsui/vue/vsui/vue/a.html。
			#root   ../;
			
			#alias：lias指定的目录是准确的，即/vsui/vue/地址下请求的文件直接是在alias目录下查找的，alias指定的目录后面必须要加上"/"符号.
			#以下虚拟目录配置下，访问http://www.vsui.com/vsui/vue/a.html实际指定的是E:/Work-Space/vsui/vue/vsui.vue/vsui.vue.v1.2.0/dist/vsui/vue/a.html。
			alias  ../dist/;
			
			#此处的 @router 实际上是引用下面的转发，否则在 Vue 路由刷新时可能会抛出 404
			try_files $uri $uri/ /index.html last;
			
			#rewrite /static/js/runtime/config.js /static/js/runtime/config_9001.js last;
			
			index  index.html index.htm;
		}
		
		
		
		# by cuiliang on 20210902 请仔细阅读如下信息：
		#本节点为前端应用对后端vseaf框架的反向代理，请求发出的默认基地址为/vseaf-service，如在开发中变更了，请修改此处
		#proxy_pass为指向的后端vseaf框架真实的地址
		location ^~ /vseaf-service {
			proxy_set_header Host $server_name;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Nginx-Proxy true;
			proxy_pass http://**********:31230;
		}
		
		location ^~ /apidata/ {
			alias  ../dist/apidata/;
		}
		
		location /restful/api {
			proxy_pass http://**********:30666/apidata;
		}
		
		
		
		# by cuiliang on 20210902 请仔细阅读如下信息：
		#请将本节点放在最后，本配置用于处理非页面的资源类文件，如有资源类文件返回html页面，请将文件后缀追加到本项内
		location ~ .*\.(gif|jpg|jpeg|png|svg|html|js|css|json|xml|ico|woff|ttf|gz|mp4|ogg|ogv|webm|htc|pdf|md)$ {
			index index.html index.htm;
			expires 1s;
			add_header Cache-Control no-cache,no-store;
		}
	
    }

}
