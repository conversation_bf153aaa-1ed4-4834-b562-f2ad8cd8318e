
<template>
  <div class="doc">
    <a  href="#" @click.prevent="open_pageoffice()">打开文件</a>
  </div>
</template>


<script>
import { POBrowser } from "js-pageoffice";

 export default {
   name: 'pageOffice',
   methods: {
     open_pageoffice() {
       let paramJson={};
       //文件ID(可做入参)
       paramJson.file_id=1;
       //文件名称(可做入参)
       paramJson.file_name="1.docx";
      //文件类型(可做入参)
      paramJson.file_type="word";
      let paramString=JSON.stringify(paramJson);

       //openWindow()第三个参数用来向弹出的PageOffice浏览器（POBrowser）窗口传递参数(参数长度不限)，支持json格式字符串。
       //此处为了方便演示，我们传递了file_id和file_name两个参数，具体以您实际开发为准。
      POBrowser.setProxyBaseAPI('/backend');

      //word编辑保存
      if("word" == paramJson.file_type){
        POBrowser.openWindow('/SimpleWord/Word', 'width=1200px;height=700px;',paramString);
      }else if("excel" == paramJson.file_type){

      }else if("ppt" == paramJson.file_type){
        
      }
      
      
     },
    

   },
   mounted(){
    window.POPageMounted = this;//此行必须
   }
 }
</script>