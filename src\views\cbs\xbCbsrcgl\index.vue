<!-- 承包商日常管理 -->
<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px" size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-select v-model="formData.zt" placeholder="选择队伍状态" clearable>
          <el-option v-for="item in dwztOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item>
          <el-input v-model="formData.dwmc" placeholder="输入承包商名称" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item>
          <el-input v-model="formData.sqtbfw" placeholder="输入申请投标范围" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell" style="max-width: 100%;">
        <el-button type="primary" @click="resetQuery"><el-icon>
            <RefreshRight />
          </el-icon>重置</el-button>
        <el-button type="primary" @click="queryCbsxx"><el-icon>
            <Search />
          </el-icon>查询</el-button>
        <el-button type="primary" @click="exportXlsx"><el-icon>
            <Upload />
          </el-icon>导出</el-button>
      </el-col>
    </el-row>

    <div class="container-wrapper">
      <el-table :data="tableData" class="lui-table" highlight-current-row ref="table" fit size="default"
        height="calc(100vh - 250px)" :border="false" v-loading="tableLoading" row-key="dwywid" lazy :load="load"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
        <el-table-column type="index" width="50">
          <template #default="scope">
            {{ scope.row.index }}
          </template>
        </el-table-column>
        <el-table-column prop="dwmc" label="承包商/队伍名称" width="260" header-align="center"></el-table-column>
        <el-table-column prop="dwlx" label="队伍类型" width="80" header-align="center" align="center">
          <template #default="scope">
            <span v-if="scope.row.dwlx == 'XN'">承包商</span>
            <span v-if="scope.row.dwlx == 'DW'">队伍</span>
          </template>
        </el-table-column>
        <el-table-column prop="sqtbfw" label="申请投标范围" min-width="200" header-align="center"
          align="left"></el-table-column>
        <el-table-column prop="zrzh" label="准入证编号" width="100" header-align="center" align="center"></el-table-column>
        <el-table-column label="有效期" width="220" header-align="center" align="center">
          <template #default="scope">
            <span>{{ scope.row.yxqks.split(' ')[0] }} 到 {{ scope.row.yxqjs.split(' ')[0] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="dwzt" label="队伍状态" width="110" header-align="center" align="center">
          <template #default="scope">
            <span v-if="scope.row.dwzt == '1'">正常</span>
            <span v-if="scope.row.dwzt == '2'">暂停投标</span>
            <span v-if="scope.row.dwzt == '3'">停工整顿</span>
            <span v-if="scope.row.dwzt == '4'">取消投标资格</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400" header-align="center" align="left">
          <template #default="scope">
            <div v-if="scope.row.dwlx == 'DW'">
              <el-button type="text" @click="handleztbg(scope.row)">队伍状态变更</el-button>
              <el-button type="text">队伍专业变更</el-button>
              <el-button type="text" @click="handlezyztbg(scope.row)">队伍专业状态变更</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination v-model:current-page="formData.page" v-model:page-size="formData.size"
        layout="total,prev, pager, next" :total="total" />

      <el-dialog v-model="ztbgDialogVisible" v-if="ztbgDialogVisible" title="队伍状态变更" width="50%">
        <ztbgEdit @handleClose="handleClose" :params="toChild" />
      </el-dialog>

      <el-dialog v-model="zyztbgDialogVisible" v-if="zyztbgDialogVisible" title="队伍专业状态变更" width="50%">
        <zyztbgEdit @handleClose="handleClose" :params="toChild" />
      </el-dialog>

      <el-dialog v-model="zybgDialogVisible" v-if="zybgDialogVisible" title="队伍专业变更" width="50%">
      </el-dialog>
    </div>
  </el-form>
</template>
<script setup>
import { onMounted, reactive, ref, toRefs, nextTick } from "vue";
import { Search, Upload, Plus, RefreshRight } from '@element-plus/icons-vue'
import { queryCbsxxList, queryDwxxList, exportToXlsx } from "@/api/xbcbsrcgl.js";
// 队伍状态变更
import ztbgEdit from "./ztbg/ztbgEdit.vue";
import zyztbgEdit from "./zyztbg/zyztbgEdit.vue";

const state = reactive({
  tableLoading: false,
  formData: {
    zt: "",   // 队伍状态
    dwmc: "",   // 承包商/队伍名称
    sqtbfw: "", // 队伍名称
    page: 1,
    size: 10
  },
  tableData: [],
  total: 0,
  dwztOptions: [
    { value: "1", label: "正常" },
    { value: "2", label: "暂停投标" },
    { value: "3", label: "停工整顿" },
    { value: "4", label: "取消投标资格" },
  ],
  ztbgDialogVisible: false, // 状态变更dialog
  zyztbgDialogVisible: false, // 专业状态变更dialog
  zybgDialogVisible: false, // 专业变更 dialog
});

const {
  tableLoading,
  formData,
  tableData,
  total,
  dwztOptions,
  ztbgDialogVisible,
  zyztbgDialogVisible,
  zybgDialogVisible,
} = toRefs(state);
const table = ref(null);

// 传递给子组件的数据
const toChild = reactive({
  dwywid: null, // 队伍业务id
  cbsywid: null, // 承包商业务id
  dwlx: null, // 队伍类型
});

// 查询承包商信息
const queryCbsxx = () => {
  tableLoading.value = true;
  queryCbsxxList(formData.value).then((res) => {
    tableData.value = res.data.rows;
    total.value = res.data.total;
    for (var i = 0; i < tableData.value.length; i++) {
      tableData.value[i].index = i + 1;
      tableData.value[i].children = [];
      tableData.value[i].hasChildren = tableData.value[i].dwlx == 'XN' ? true : false;
    }
  }).finally(() => {
    tableLoading.value = false;
  });
};

// 导出
const exportXlsx = () => {
  exportToXlsx({});
}

// 重置
const resetQuery = () => {
  formData.value = {
    zt: "",
    dwmc: "",
    sqtbfw: "",
    page: 1,
    size: 10
  }
  queryCbsxx();
}

// 子级数据懒加载
const load = async (row, treeNode, resolve) => {
  try {
    const params = {
      cbsywid: row.cbsywid,
      zt: formData.value.zt,
      sqtbfw: formData.value.sqtbfw
    };
    const res = await queryDwxxList(params);
    for (var i = 0; i < res.data.length; i++) {
      res.data[i].index = row.index + '.' + (i + 1);
    }
    resolve(res.data);
  } catch (error) {
    resolve([]);
  }
}

// 处理状态变更
const handleztbg = (row) => {
  toChild.dwywid = row.dwywid; // 队伍业务id
  ztbgDialogVisible.value = true;
}

// 处理专业状态变更
const handlezyztbg = (row) => {
  toChild.dwywid = row.dwywid;
  zyztbgDialogVisible.value = true;
}


// 处理 dialog 关闭
const handleClose = () => {
  ztbgDialogVisible.value = false;
  zyztbgDialogVisible.value = false;
  zybgDialogVisible.value = false;
}

onMounted(() => {
  // 查询承包商信息
  queryCbsxx();
});

</script>
<style scoped></style>