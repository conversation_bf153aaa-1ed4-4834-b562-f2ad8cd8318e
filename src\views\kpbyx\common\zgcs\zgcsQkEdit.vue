<template>
  <div style="width: 100%" v-loading="loading">
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;
      border-bottom: 3px solid #2A96F9;padding-left: 10px;padding-right: 10px;text-align: center;padding-bottom: 2px">
        资格初审情况统计确认
      </div>

      <el-table :data="formData.PSXXList" border class="lui-table" :highlight-current-row="true" size="default"
                :cell-style="{ padding: '10px 0 ' }" height="calc(100vh - 580px)">
        <el-table-column type="index" width="50" fixed="left" label="" align="center"/>
        <el-table-column property="PJBZ" header-align="center" align="left"
                         label="初审内容"></el-table-column>
        <el-table-column v-for="item in formData.TBRList" :key="item" :property="item.TBBSID"
                         header-align="center" align="center" :label="item.DWMC" width="220">
          <template #default="scope">
            <div class="greenStyle" v-if="scope.row[item.TBBSID] === '1' && scope.row.type === 'SJ'">合格
            </div>
            <div class="redStyle" v-if="scope.row[item.TBBSID] === '2' && scope.row.type === 'SJ'">不合格
            </div>
            <div class="greenStyle" v-if="scope.row[item.TBBSID] === '1' && scope.row.type === 'HJ'">通过
            </div>
            <div class="redStyle" v-if="scope.row[item.TBBSID] === '2' && scope.row.type === 'HJ'">不通过
            </div>
          </template>
        </el-table-column>
      </el-table>


      <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;"
           v-if="editable">
        <el-button size="default" type="primary" @click="saveData('submit')">确认</el-button>
        <el-button size="default" @click="getFormData">刷新</el-button>
      </div>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import comFun from "@lib/comFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: false,
      editable: props.parentForm.ZGSC_WCZT !== '1',
      formData: {
        PSXXList: [],
        TBRList: [],
      },
      rules: {}
    })

    const getFormData = () => {

    }

    const saveData = (type) => {
      emit('saveFromData', {ZGSC_WCZT: '1',ZGSC_WCSJ: comFun.getNowTime(),ZGSC_SFGS: '1'},{})
      state.editable=false
      nextTick(() => {
        emit('nextStep', `已完成资格初审`)
      })
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      getFormData,
      saveData

    }
  }

})
</script>

<style scoped>

</style>
