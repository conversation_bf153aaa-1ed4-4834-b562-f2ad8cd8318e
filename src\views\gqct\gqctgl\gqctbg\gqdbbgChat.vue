<template>
  <div class="table-row"
      v-if="params.DMXX=='GQGX'"
       :ref="'GQGX'+params.INDEX"
       :id="'GQGX'+params.INDEX"
  ></div>
  <div class="table-row"
      v-if="params.DMXX=='RZGX'"
       :ref="'RZGX'+params.INDEX"
       :id="'RZGX'+params.INDEX"
  ></div>
  <div class="table-row"
      v-if="params.DMXX=='GQRZGX'"
       :ref="'GQRZGX'+params.INDEX"
       :id="'GQRZGX'+params.INDEX"
  ></div>
</template>

<script>
import { getCurrentInstance,onBeforeMount,ref, onMounted, onUnmounted, nextTick, defineComponent, reactive, toRefs} from 'vue';
import * as echarts from 'echarts';
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({})
    onMounted(() => {
        // alert("Chat")
        setEcharts();
        
    })

    const setEcharts = () => {
      var qParams = {DMXX:props.params.DMXX,GQCTJLID:props.params.GQCTJLID,FXQDID:props.params.FXQDID};
      //查询数据
      // axiosUtil.get('/backend/gqct/gqctgl/getGqData', qParams).then((res) => {  //显示所有对象和关系
      axiosUtil.get('/backend/gqct/gqctgl/getNodeAndLink', qParams).then((res) => {//仅显示有连接的对象和关系

        if(res.message=='error'){
          ElMessage({
            message: `查询数据错误`,
            type: 'error',
          })
        }

        if(res.data.data.length==0){
          ElMessage({
            message: `没有数据`,
            type: 'error',
          })
        }
        var data = [];
        res.data.data.forEach((d)=>{
          data.push({
            name: d.DXMC,
            // x: d.WZ_X||getRandomNumber(500),
            // y: d.WZ_Y||getRandomNumber(500),
            x: d.WZ_X ? Number(d.WZ_X) + 500 : d.WZ_X,
            y: d.WZ_Y ? Number(d.WZ_Y) + 200 : d.WZ_Y ,
            fixed: d.WZ_X ? true : false,
            symbolSize: d.SIZE||50,
            //symbol:d.SYMBOL,
            id:d.DXID,
            itemStyle: {
              color: d.COLOUR
            },
            normal: {
              label: {
                rotate: 45,

              }
            },
          });
        })

        var link = [];
        res.data.link.forEach(d=>{
          link.push({
            source: d.SOURCE,
            target: d.TARGET,
            label: {
              normal: {
                fontSize: 8,
                show : true,//显示
                //position: 'right',//相对于节点标签的位置，默认在节点中间
                //回调函数，你期望节点标签上显示什么
                formatter: function(){
                  return d.LABEL;
                }
              }
            },
            lineStyle: {
              curveness: 0.01,
              color:d.COLOR
            }
          });
        })
        setChartsData(data,link);

      })
    };

    const setChartsData = (data,link) => {
      var id =props.params.DMXX+props.params.INDEX;//'myChart';
      const dom = document.getElementById(id);
      const myChart = echarts.init(dom); // 初始化echarts实例
      const option = {
        title: {
          text: ''
        },

        tooltip: {},
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [
          {
            type: 'graph',
            layout: 'force',
            force: {
              repulsion: 1000,
              edgeLength: 220,
              gravity: 0.1,
              layoutAnimation: true,
            },
            zoom: 0.6,
            symbolSize: 50,
            roam: true,
            // draggable:true,
            label: {
              show: true,
              formatter: function (params) {
                // 定义每行最大字符数，根据实际需求调整
                var maxCharsPerLine = 8;
                var name = params.name;
                var lines = [];
                for (var i = 0; i < name.length; i += maxCharsPerLine) {
                  lines.push(name.substring(i, i + maxCharsPerLine));
                }
                return lines.join('\n');
              }
            },
            edgeSymbol: ['circle', 'arrow'],
            edgeSymbolSize: [4, 10],
            preventOverlap: true,
            edgeLabel: {
              fontSize: 20
            },
            data: data,
            // links: [],
            links: link,
            lineStyle: {
              opacity: 0.9,
              width: 2,
              curveness: 0
            }
          }
        ]
      };
      // 设置实例参数
      myChart.setOption(option);
    };

    return {
      ...toRefs(state),
      setEcharts,

    }
  }

})


</script>

<style scoped>
/* 添加一些CSS样式来美化图表容器（可选） */
.table-row{
  width: 210mm;
  height: 600px
}
</style>
