<template>
  <div>
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CBSDWQC">
            <el-input ref="input45296" placeholder="企业名称" v-model="listQuery.CBSDWQC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="DWMC">
            <el-input ref="input45296" placeholder="请输入队伍标名称" v-model="listQuery.DWMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="400px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="CBSDWQC" label="承包商名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="DWMC" label="队伍名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="saveData(scope.row)">选择</el-button>
                </template>
              </el-table-column>

            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>


    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      listQuery:{
        page: 1,
        size: 10,
        CBSDWQC:'',
      },
      tableData: [],
      total: 0,
      checkList: [],

      PJLBOptions: [],
    })

    const getDataList = () => {
      let params={
        ...state.listQuery
      }
      axiosUtil.get('/backend/sckhpj/xmwtsb/selectSbdwxxPage',params).then(res=>{
        state.tableData = res.data.list || []
        state.total = res.data.total
      })

    }

    const saveData = (row) => {
      emit('submit',row)
    }




    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getDataList()

    })

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      closeForm,
      saveData,

    }
  }

})
</script>

<style scoped>

</style>
