<!-- 选择专业 -->
<template>
  <el-form :model="queryParams" ref="form" class="lui-page" size="default" label-width="0" inline>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="queryParams.ZYJB" clearable placeholder="请选择专业级别">
            <el-option
                v-for="(item, index) in zyjbOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input
              v-model="queryParams.FJZYMC"
              placeholder="请输入父专业名称"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input
              v-model="queryParams.ZYMC"
              placeholder="请输入专业名称"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input
              v-model="queryParams.ZYBM"
              placeholder="请输入专业编码"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item>
          <el-button type="primary" @click="queryTableData" style="margin-left: 10px">查询
          </el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="table">
      <div class="table-content">
        <el-table
            class="lui-table"
            :data="tableData"
            @selection-change="handleSelectionChange"
            row-key="ZYID"
            default-expand-all
            ref="multipleTableRef"
            height="calc(100vh - 320px)"
            border
            v-loading="tableLoading"
        >
          <!-- SFMJ 是否末级 -->
          <el-table-column
              type="selection"
              header-align="center"
              align="center"
              width="40"
              :selectable="
                    (row) =>
                      row.SFMJ == '1' && (selectedType ? row.MBLX == selectedType : true)
                  "
          ></el-table-column>
          <el-table-column
              label="序号"
              type="index"
              header-align="center"
              align="center"
              width="50"
          ></el-table-column>
          <el-table-column
              label="专业名称"
              prop="ZYMC"
              header-align="center"
              align="left"
          ></el-table-column>
          <el-table-column
              label="专业级别"
              prop="ZYJB"
              header-align="center"
              align="center"
              width="100"
          ></el-table-column>
          <el-table-column
              label="专业编码"
              prop="ZYBM"
              header-align="center"
              align="left"
          ></el-table-column>
          <el-table-column
              label="专业分类"
              prop="ZYFL"
              header-align="center"
              align="center"
              width="100"
          >
            <template #default="{ row }">
              <div v-if="row.ZYFL">{{ row.ZYFL }} 类</div>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="是否自营"
            prop="SFZY"
            header-align="center"
            align="center"
            width="100"
          >
            <template #default="{ row }">
              <div>
                {{ row.SFZY ? ["非自营", "自营"][Number(row.SFZY)] : "--" }}
              </div>
            </template>
          </el-table-column> -->
          <el-table-column
              label="范围描述"
              prop="ZYMS"
              header-align="center"
              align="left"
          ></el-table-column>
          <el-table-column
              label="引入方式"
              prop="YRFS"
              header-align="center"
              align="center"
              width="100"
          >
            <template #default="{ row }">
              <div>
                {{
                  row.MBLX
                      ? row.MBLX.includes("DW")
                          ? "按队伍引入"
                          : "按企业引入"
                      : "--"
                }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <el-row class="pagenation">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 200]"
          :small="small"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row> -->
    </div>
    <el-row class="btn" justify="end" align="center">
      <el-button type="primary" @click="nextStep">下一步</el-button>
    </el-row>
  </el-form>
</template>

<script setup>
import outerBox from "@components/common/outerBox";
import comFun from "@src/lib/comFun";
import {
  ref,
  reactive,
  getCurrentInstance,
  onMounted,
  defineProps,
  computed,
  inject,
} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import {
  getZyglZyglList,
  getCbsyjCxmjzyjb,
  getCbsyjXzzyList,
  postCbsyjSaveZy,
  postCbsyjGenerateCbsAndDwxx,
  getCbsyjCheckOnlyId
} from "@src/api/sccbsgl.js";
import cascaderTree from "@src/lib/cascaderTree.js";
import VSAuth from "@src/lib/vsAuth";
import tabFun from "@src/lib/tabFun";
import {v4 as uuidv4} from "uuid";
import {mixin} from "@src/assets/core/index";

const {vsuiRoute, vsuiEventbus} = mixin();

const props = defineProps({
  change: {
    type: String,
    default: "",
  },
});

const uuId = inject("uuId");
const nt = defineEmits(["nextStep", "selectUpdate"]);
const userInfo = VSAuth.getAuthInfo().permission;
const {userLoginName, realName} = userInfo;
const queryParams = reactive({
  ZYJB: "",
  FJZYMC: "",
  ZYMC: "",
  ZYBM: "",
  page: 1,
  total: 0,
  pageSize: 10,
});
let tableData = ref([]);
let selRow = ref([]);
let zyjbOptions = reactive([
  {label: "全部", value: ""},
  {label: "大类", value: "1"},
  {label: "中类", value: "2"},
]);

const handleSizeChange = (val) => {
  console.log(`${val} items per page`);
  queryParams.pageSize = val;
  queryParams.page = 1;
  // queryTableData()
};
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`);
  queryParams.page = val;
};
/**查询专业级别 */
const queryZyjb = () => {
};
/**查询表格数据 */
const tableLoading = ref(false);
const queryTableData = () => {
  tableLoading.value = true;
  getCbsyjXzzyList({...queryParams}).then(({data}) => {
    tableData.value = new cascaderTree(data, "ZYBM", "FZYBM").init();
  }).catch((err) => {
    console.log(err);
  }).finally(() => {
    tableLoading.value = false
  })
};
const lastLevel = ref(null);
onMounted(() => {
  getCbsyjCxmjzyjb()
      .then((result) => {
        // console.log(result);
        lastLevel.value = result.data;
      })
      .catch((err) => {
      })
      .finally(() => {
        queryTableData();
      });
});
/**选择变更 */
const handleSelectionChange = (val) => {
  selRow.value = val;
};
const selectedType = computed(() => {
  if (!selRow.value.length) return "";
  return selRow.value.map((item) => item.MBLX).some((i) => i.includes("DW"))
      ? "DW,QY"
      : "QY";
});
/**下一步 */
const nextStep = () => {
  if (!selRow.value.length) {
    ElMessage.warning("请先选择专业");
    return;
  }
  if (props.change) {
    nt("selectUpdate", selRow.value);
    nt("nextStep");
    return;
  }
  // 保存已选专业信息
  const saveData = selRow.value.map((item) => {
    const SXZYMXID_ID = uuidv4().replace(/-/g, ""); //主键id
    return {
      ...item,
      ZYMXID: SXZYMXID_ID,
      SXZYMXID: SXZYMXID_ID,
      DWYWID: uuId, //队伍业务id
      ZYWYBS: SXZYMXID_ID,
      ZRMBID: item.MBIDS.split(",")[0],
      ZYMC: item.ZYMC,
      ZYBM: item.ZYBM,
      CJRZH: userLoginName,
      CJRXM: realName,
      CJDWID: userInfo.orgnaId,
      CJSJ: comFun.getNowTime(),
      SHZT: 0,
      SFYFP: 0,
    };
  });
  console.error('??',saveData)
  postCbsyjSaveZy({
    dataList: saveData,
    ZYLX: selectedType.value.includes("DW") ? "DW" : "QY", //"队伍:DW 企业:QY"
  })
      .then(({data}) => {
        ElMessage.success("保存专业成功");
        // 生成承包商信息和虚拟队伍信息
        const CBSYWID = uuidv4().replace(/-/g, "");

        new Promise(resolve => resolve(true))
        // getCbsyjCheckOnlyId({
        //   dwid: uuId,
        //   orgId: userInfo.orgnaId,
        //   newType: selectedType.value.includes("DW") ? "XN" : "CBS"
        // })
            .then(res => {
          console.log(res)
          if (res.data) {
            if (selectedType.value.includes("DW")) {
              tabFun.openNewTabClose(
                  "承包商基本信息",
                  "/contractors/cbsjbxxAdminIndex",
                  {
                    uuId, //队伍业务ID
                    MBID: saveData.map((i) => i.ZRMBID).join(","), //模板ID
                    MBLX: selectedType.value.includes("DW") ? "DW" : "QY", //模板类型、
                    ZYFLDM: saveData.map((i) => i.ZYBM).join(","), //专业分类代码
                    YWLXDM: "ZR", //业务类型代码
                    EXTENSION: {
                      // 队伍的时候保存企业模板id，用于承包商基本信息页的模板查询
                      MBID: saveData.map((i) => selectedType.value.includes("DW") ? i.MBIDS.split(",")?.[1] : i.MBIDS).join(","),
                    },
                    editable:true,
                    from:'YWBMBA'
                  },
                  {}
              );
              // vsuiEventbus.emit("reloadCbsjbxx", {});
              vsuiEventbus.emit("reloadCbsjbxx", {
                uuId, //队伍业务ID
                MBID: saveData.map((i) => i.ZRMBID).join(","), //模板ID
                MBLX: selectedType.value.includes("DW") ? "DW" : "QY", //模板类型、
                ZYFLDM: saveData.map((i) => i.ZYBM).join(","), //专业分类代码
                YWLXDM: "ZR", //业务类型代码
                EXTENSION: {
                  // 队伍的时候保存企业模板id，用于承包商基本信息页的模板查询
                  MBID: saveData.map((i) => selectedType.value.includes("DW") ? i.MBIDS.split(",")?.[1] : i.MBIDS).join(","),
                }
              });
              // tabFun.closeTabByPath('/contractors/zrfqIndex')
            } else {
              nt("selectUpdate", saveData);
              nt("nextStep");
            }
            // tabFun.closeTabByPath("/contractors/zrfqIndex");
          } else {
            postCbsyjGenerateCbsAndDwxx({
              CJR: userInfo.userLoginName,
              LY: 'GLYZR',
              CBSYWID, //"承包商业务id",
              DWYWID: uuId, //"队伍业务ID",
              DLRORGID: userInfo.orgnaId, //"登录人组织机构ID",
              DWLX: selectedType.value.includes("DW") ? "XN" : "CBS",//队伍类型
              EXTENSION: {
                // 队伍的时候保存企业模板id，用于承包商基本信息页的模板查询
                MBID: saveData.map((i) => selectedType.value.includes("DW") ? i.MBIDS.split(",")?.[1] : i.MBIDS).join(","),
              }
            })
                .then((result) => {
                  if (selectedType.value.includes("DW")) {
                    tabFun.openNewTabClose(
                        "承包商基本信息",
                        "/contractors/cbsjbxxAdminIndex",
                        {
                          uuId, //队伍业务ID
                          MBID: saveData.map((i) => i.ZRMBID).join(","), //模板ID
                          MBLX: selectedType.value.includes("DW") ? "DW" : "QY", //模板类型、
                          ZYFLDM: saveData.map((i) => i.ZYBM).join(","), //专业分类代码
                          YWLXDM: "ZR", //业务类型代码
                          EXTENSION: {
                            // 队伍的时候保存企业模板id，用于承包商基本信息页的模板查询
                            MBID: saveData.map((i) => selectedType.value.includes("DW") ? i.MBIDS.split(",")?.[1] : i.MBIDS).join(","),
                          },
                          from:'YWBMBA'
                        },
                        {}
                    );
                    // vsuiEventbus.emit("reloadCbsjbxx", {});
                    vsuiEventbus.emit("reloadCbsjbxx", {
                      uuId, //队伍业务ID
                      MBID: saveData.map((i) => i.ZRMBID).join(","), //模板ID
                      MBLX: selectedType.value.includes("DW") ? "DW" : "QY", //模板类型、
                      ZYFLDM: saveData.map((i) => i.ZYBM).join(","), //专业分类代码
                      YWLXDM: "ZR", //业务类型代码
                      EXTENSION: {
                        // 队伍的时候保存企业模板id，用于承包商基本信息页的模板查询
                        MBID: saveData.map((i) => selectedType.value.includes("DW") ? i.MBIDS.split(",")?.[1] : i.MBIDS).join(","),
                      }
                    });
                    // tabFun.closeTabByPath('/contractors/zrfqIndex')
                  } else {
                    nt("selectUpdate", saveData);
                    nt("nextStep");
                  }
                  // tabFun.closeTabByPath("/contractors/zrfqIndex");
                })
                .catch((err) => {
                });
          }
        })
      })
      .catch((err) => {
      });
};
</script>

<style scoped src="../../style/index.css"></style>
<style scoped>
.container {
  overflow: hidden;
  width: calc(100% - 20px);
  height: 100%;
}

.out-box-content {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  height: calc(100% - 80px);
  padding: 10px;
}

.search {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 30px;
  margin-bottom: 10px;
}

.search > span {
  margin: 0 10px;
}

.search > span .el-input,
.el-select {
  width: 200px;
}

.table {
  /* height: calc(100% - 140px); */
  flex: 1;
  overflow: hidden;
}

.table-content {
  height: 100%;
}

.pagenation {
  margin-top: 10px;
}

.btn {
}
</style>
