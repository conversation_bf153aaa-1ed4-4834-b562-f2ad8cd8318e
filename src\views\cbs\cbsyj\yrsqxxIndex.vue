<!-- 承包商基本信息 -->
<template>
    <div class="container">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" style="height:calc(100% - 50px)">
            <el-tab-pane
                    v-for="(item, index) in compList"
                    :key="index"
                    :label="item.SJMBMC"
                    :name="item.SJMBBM"
            >
                <div class="tab-pane-content">
                    <!-- <component v-if="index == 0" :is="cbsjbxx"></component>
                    <component v-if="index == 4" :is="zjdw"></component> -->
                    <!-- <span v-else>{{ item.label }}</span> -->
                    <component
                    :ref="(el) => setRefMap(el, item.SJMBBM)"
                    :is="ComponentDic[item.SJMBBM]" :row="row" :proDetails="proDetails"
                               :editData="saveForm[item.SJMBBM]"
                    :DWYWID="DWYWID"
                               :defaultData="saveForm[item.SJMBBM]"
                               :resultTableData="resultTableData?.[item.SJMBBM]"
                    :TYXYDM="saveForm?.JBXX.TYXYDM"
                               :editable="editable"
                               :LSZRLX="attrs.LSZRLX"></component>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div style="height:320px;margin: 0px 40px 20px 40px" v-if="ifShowLscz">
            <el-divider content-position="left" class="divider">历史操作</el-divider>
            <el-table
                class="lui-table"
                highlight-current-row
                size="default"
                height="300px"
                border
                :data="tableData"
            >
                <el-table-column type="index" width="60" label="序号" align="center"/>
                <el-table-column label="业务类型" min-width="150" prop="YWLXMC" header-align="center" align="left">
                </el-table-column>
                <el-table-column label="发起时间" min-width="150" prop="CJSJ" header-align="center" align="center">
                </el-table-column>
                <el-table-column label="结束时间" min-width="100" header-align="center" align="center">
                </el-table-column>
                <el-table-column label="操作" min-width="60" header-align="center" align="center">
                    <template #default="{ row }">
                        <el-button class="lui-table-button" @click="viewLsjl(row)">
                            查看
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div style="text-align: center;">
            <el-button type="primary" @click="save('0')" v-if="editable">保存</el-button>
            <el-button type="success" @click="save('0','submit')" v-if="editable">提交队伍信息</el-button>
            <el-button type="default" @click="cancelPage" v-if="editable">返回</el-button>
<!--            <el-button type="success" v-if="YWLXDM != 'ZR'" @click="save('1')">提交</el-button>-->
            <!-- <el-button type="success" @click="validateTemplate">测试</el-button> -->
        </div>
    </div>
</template>

<script setup>
import {defineAsyncComponent, onMounted, ref, useAttrs, watch} from "vue";
import {
    getCbsyjGetTabShow,
    getCbsyjGetTabData,
    getCbsyjGetProDetails,
    postCbsyjSaveCbsxx,
    getCbsyjTeambyid,
    getCbsyjGetTeamResultInfo,//查询队伍结果信息
    getCbsyjTeamresultbyid
} from "@src/api/sccbsgl.js";
import {getCbsyjGetTeamInfo} from "../../../api/sccbsgl";
import {ElMessage} from "element-plus";
import tabFun from "@src/lib/tabFun";
import {auth, mixin} from '@src/assets/core/index';
import comFun from "@src/lib/comFun";
import {getCommonSelectDMB} from "@src/api/common.js";
import axiosUtil from "@lib/axiosUtil";
const attrs = useAttrs();
const {DWYWID, type, YWLXDM, backPath, JGDWYWID, editable, LSZRLX} = attrs;
// const DWYWID = 'b88d70fdf0634bbd9171479a7d4c82bf'
// const YWLXDM = 'ZR'
// const type = 'cqyj'
const isVIewJgxx = ref(false); // 是否查询正式数据
isVIewJgxx.value=attrs.isVIewJgxx;

// 是否展示历史操作
const ifShowLscz = ref(false);
ifShowLscz.value=attrs.ifShowLscz;

const row = ref({});
watch(
    () => DWYWID,
    (val) => {

    },
    {
        immediate: true,
    }
);
const {vsuiEventbus} = mixin()
// 引入申请信息
let yrsqxxComp = defineAsyncComponent(() => import("@views/cbs/cbsyj/yrsqxx.vue"));
// 队伍信息
let dwxx = defineAsyncComponent(() => import("@views/cbs/cbsyj/dwxx.vue"));
// 选择专业及区域
let zjdw = defineAsyncComponent(() => import("@views/cbs/cbsyj/xzzyjqy.vue"));
// 模板页
// 资质信息
const zzxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/zzxx/zzxx.vue")
);
// 许可信息
const xkxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/xkxx/xkxx.vue")
);
// 体系证书
const txzsComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/txzs/txzs.vue")
);
// 人员信息
const ryxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/ryxx/ryxx.vue")
);
// 设备信息
const sbxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/sbxx/sbxx.vue")
);
// 业绩信息
const yjxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/yjxx/yjxx.vue")
);
// 奖惩信息
const jcxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/jcxx/jcxx.vue")
);
// 知识产权
const zscqComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/zscq/zscq.vue")
);
// 车辆信息
const clxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/clxx/clxx.vue")
);
// 房屋信息
const fwxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/fwxx/fwxx.vue")
);
// 土地信息
const tdxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/tdxx/tdxx.vue")
);

// 制度信息
const zdxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/zdxx/zdxx.vue")
);

// 安全环保
const aqhbComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/aqhb/aqhb.vue")
);
// 授权委托信息
const sqwtxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/sqwtxx/sqwtxx.vue")
);
const ComponentDic = ref({
    YRSQXX: yrsqxxComp,
    ZZXX: zzxxComp,
    XKXX: xkxxComp,
    TXZS: txzsComp,
    RYXX: ryxxComp,
    SBXX: sbxxComp,
    YJXX: yjxxComp,
    JCXX: jcxxComp,
    ZSCQ: zscqComp,
    CLXX: clxxComp,
    FWXX: fwxxComp,
    TDXX: tdxxComp,
    SQWTXX: sqwtxxComp,
    ZDXX: zdxxComp,
    AQHB: aqhbComp,
});
const refMap = ref({});
const setRefMap = (el,name) =>{
    refMap.value[name] = el;
}
/* 校验各tab必填项 */
const validateTemplate = async() => {
    return new Promise((resolve,reject) => {
        Promise.all(Object.values(refMap.value).filter(i => i.validateForm).map(i => i.validateForm())).then((result) => {
            ElMessage.success('校验成功');
            resolve(true);
        }).catch((err) => {
            console.log(err);
            ElMessage.error(Object.values(err)[0]?.[0]?.message)
            resolve(false)
        })
    })

}

const saveForm = ref({})

let activeName = ref("YRSQXX");
let compList = ref([
    /*  { SJMBMC: "承包商基本信息", SJMBBM: "CBSJBXX" },
     { SJMBMC: "资质信息", SJMBBM: "ZZXX" },
     { SJMBMC: "许可信息", SJMBBM: "XKXX" },
     { SJMBMC: "体系证书", SJMBBM: "TXZS" },
     { SJMBMC: "人员信息", SJMBBM: "RYXX" },
     { SJMBMC: "设备信息", SJMBBM: "SBXX" },
     { SJMBMC: "业绩信息", SJMBBM: "YJXX" },
     { SJMBMC: "授权委托信息", SJMBBM: "SQWTXX" }, */
    /*
  资质信息
  许可信息
  体系证书
  人员信息
    */
]);

// 查询tab页数量

const getTabShow = () => {
    // compList.value = [{ SJMBMC: "引入申请信息", SJMBBM: "YRSQXX" },...row.templates]
    // return;
    getCbsyjGetTabShow({
        mbids: row.value.mbids
    }).then(({data}) => {
        refMap.value = {}
        compList.value = [{SJMBMC: "队伍基本信息", SJMBBM: "YRSQXX"}, ...data]
        // [{SJMBMC: "引入申请信息", SJMBBM: "YRSQXX"}, ...data].forEach(x=>saveForm.value[x.SJMBBM] = [])
    }).catch((err) => {

    });
}
const proDetails = ref([])
const getProDetails = () => {
    getCbsyjGetProDetails({dwid: row.value.DWYWID}).then(res => {
        proDetails.value = res.data;
    })
}
// 查询tab基本信息
const tabDefaultData = ref([]);
// 结果表数据
const resultTableData = ref(null);
const getTeamResultInfo = () => {
    if(!JGDWYWID) return;
    getCbsyjGetTeamResultInfo({
        DWYWID: JGDWYWID
    }).then(({data}) => {
        if(!data) return;
        resultTableData.value = data;
    }).catch((err) => {
        ElMessage.warning('查询结果表数据失败')
    });
}
onMounted(() => {
    // 如果是变更状态，查询结果表数据
    if(YWLXDM == "BG"){
        getTeamResultInfo()
    }
})
const getTabsDefaultData = () => {
    Promise.all([
        getCbsyjGetTabData({
            ZYFLDM: row.value.ZYFLDMS, MBLX: 'DW'
        }),
        (isVIewJgxx.value ? getCbsyjGetTeamResultInfo : getCbsyjGetTeamInfo)({
            DWYWID: row.value.DWYWID
        }),
        getCbsyjGetTabShow({
            mbids: row.value.mbids
        })
    ])
        .then(([{data}, {data: result}, {data: tab}]) => {
            console.log("tab基本数据", data, result);
            tabDefaultData.value = data.map((i) => ({
                ...i,
                ZYMC: i.ZYFLMC,
                ZYBM: i.ZYFLDM,
                SHZT: 1,
                EXTENSION:{
                    SJLY: 'MBSC'
                  }
            }));
            const obj = tabDefaultData.value.reduce((t, i) => {
                t[i.SJMBBM] ? t[i.SJMBBM].push(i) : (t[i.SJMBBM] = [i]);
                return t;
            }, {});
            Object.entries(result).forEach(([key, value]) => {
                if (value) {
                    //排除null
                    if (Array.isArray(value)) {
                        //list的话且不为空数组的时赋值
                        if (value.length || !obj[key]) obj[key] = value;
                    } else {
                        obj[key] = value;
                    }
                }
            });
            saveForm.value = obj;
            compList.value = [{SJMBMC: "队伍基本信息", SJMBBM: "YRSQXX"}, ...tab]
        })
        .catch((err) => {
        });
};

const statusDic = ref({});
const queryStatusDic = () => {
    getCommonSelectDMB({DMLBID: 'YWLXDM'}).then(({data}) => {
        console.log('data', data);
        if (data) {
            statusDic.value = data.reduce((t, i) => {
                t[i.DMXX] = i.DMMC;
                return t
            }, {})
        }
    })
}

const save = async(status,btn) => {
    // 提交时添加校验
    // if(status == '1' || YWLXDM === 'ZR'){
    //     const validResult = await validateTemplate();
    //     if(!validResult) return
    // }
    // 保存去掉检验，增加提交校验
    if(attrs.LSZRLX){
        saveForm.value.YRSQXX.EXTENSION.LSZRLX = attrs.LSZRLX;
    }
    if(btn === 'submit'){
        saveForm.value.YRSQXX.EXTENSION.SFWCJY = "1";
        const validResult = await validateTemplate();
        if(!validResult) return
    }else{
        saveForm.value.YRSQXX.EXTENSION.SFWCJY = "0";
    }
    // 设置登记人基本信息
    const userinfo = auth.getPermission();
    const feildList = {
        CJRZH: userinfo.userLoginName,
        CJRXM: userinfo.userName,
        CJDWID: userinfo.orgnaId,
        CJSJ: comFun.getNowTime()
    };
    const updatefeildList = {
        XGRZH: userinfo.userLoginName,
        XGSJ: comFun.getNowTime()
    };
    // 给无默认值数据添加默认数据
    const setDafaultValue = (data) => {
        if (YWLXDM === 'ZR') {
            //准入增加创建人信息
            Object.entries(feildList).forEach(([key, value]) => {
                if (!data[key]) {
                    data[key] = value;
                }
            });
        } else {
            //其他类型增加更新人信息
            Object.entries(updatefeildList).forEach(([key, value]) => {
                if (!data[key]) {
                    data[key] = value;
                }
            });
        }

    };
    Object.entries(saveForm.value).forEach(([key, value]) => {
        if (value) {
            //排除null
            if (Array.isArray(value)) {
                //list的话且不为空数组的时赋值
                value.forEach(x => {
                    x.DWYWID = row.value.DWYWID
                    setDafaultValue(x);
                })
            }
        }
    });


    // postCbsyjSaveCbsxx
    saveForm.value.ZZXX.forEach(x => {
        x.ZSDLDM = 'ZZZS'
        x.ZSCYZBS = saveForm.value.YRSQXX.DWYWID
    })
    saveForm.value.TXZS.forEach(x => {
        x.ZSDLDM = 'TXZS'
        x.ZSCYZBS = saveForm.value.YRSQXX.DWYWID
    })
    saveForm.value.XKXX.forEach(x => {
        x.ZSDLDM = 'XKZS'
        x.ZSCYZBS = saveForm.value.YRSQXX.DWYWID
    })
    saveForm.value.RYXX.forEach(x => {
        if (x.ZSXX) {

            x.ZSXX.forEach(v => {
                v.DWYWID = row.value.DWYWID
                setDafaultValue(v);
            })
        }
    })
    saveForm.value.YRSQXX.YWBT = saveForm.value.JBXX.CBSDWQC + `-队伍${statusDic.value[YWLXDM]}`;
    saveForm.value.YRSQXX.YWLXDM = YWLXDM;
    saveForm.value.JBXX.SHZT = status; //审核状态
    saveForm.value.YRSQXX.SHZT = status; //审核状态
    let form = {
        cbsdwxx: saveForm.value.YRSQXX,
        cbszs: saveForm.value.ZZXX.concat(saveForm.value.TXZS).concat(saveForm.value.XKXX),
        cbsdwyj: saveForm.value.YJXX,

        cbsdwzscq: saveForm.value.ZSCQ,
        cbsdwclxx: saveForm.value.CLXX,
        cbsdwfwxx: saveForm.value.FWXX,
        cbsdwtdxx: saveForm.value.TDXX,
      cbsdwzdxx: saveForm.value.ZDXX,
      cbsdwaqhb: saveForm.value.AQHB,

        cbsdwsb: saveForm.value.SBXX,
        cbsdwjcqk: saveForm.value.JCXX,
        cbsdwcy: saveForm.value.RYXX,
        cbsjbxx: null,
        bczt: status, //"0保存；1提交；2审核通过；"
        DWYWID: DWYWID,
    }

    if (YWLXDM != 'ZR') {
        form.cbsjbxx = saveForm.value.JBXX
    }
    //统一添加持有者类型为队伍
    form.cbszs.forEach(x => x.ZSCYZLXDM = 'DW')
    console.log(JSON.stringify(form))
    postCbsyjSaveCbsxx(form).then(res => {

        // 根据队伍的承包商业务Id更新虚拟队伍信息的 YWBT
        updateXnDwxx();

        ElMessage({
            type: "success",
            message: btn === "submit" ? "提交成功!" : "保存成功!",
        });
        if (YWLXDM == 'ZR' || YWLXDM == 'ZX' || YWLXDM == 'LS') {
            // 保存的时候 不返回父页面
            if (status === '0' && btn !== 'submit') {

            } else if (type == 'cqyj') {
                vsuiEventbus.emit('reloadTableData', {})
                tabFun.setCurrentTabByPath('/query-analysis/dwcqyj')
                tabFun.closeTabByPath('/contractors/yrsqxxIndex')
            } else {
                vsuiEventbus.emit('reloadTableData', {})
                tabFun.setCurrentTabByPath('/contractors/cbsjbxxIndex')
                tabFun.closeTabByPath('/contractors/yrsqxxIndex')
            }
        }else{
            if(status == '1' && backPath){
                tabFun.setCurrentTabByPath(backPath)
                tabFun.closeTabByPath('/contractors/yrsqxxIndex')
            }
        }
    })
}
const getTeamById = () => {
    (isVIewJgxx.value ? getCbsyjTeamresultbyid : getCbsyjTeambyid)({
        DWYWID: DWYWID
    }).then(({data}) => {
        row.value = data;
        getProDetails();
        getTabsDefaultData();
    }).catch((err) => {

    });
};
const cancelPage = () => {
    vsuiEventbus.emit('reloadTableData', {})
    tabFun.setCurrentTabByPath('/contractors/cbsjbxxIndex')
    tabFun.closeTabByPath('/contractors/yrsqxxIndex')
};
onMounted(() => {
    getTeamById();
    queryStatusDic();
    if(ifShowLscz.value){
        initCbsLsczList();
    }
});
let handleClick = (tab, event) => {
    console.log(tab, event);
};
// 初始化数据
const tableData = ref([]);
const initCbsLsczList = () => {
    axiosUtil.get('/backend/sccbsgl/report/queryDwLsczList', {dwywid:DWYWID}).then((res) => {
        tableData.value = res.data.rows
    });
}
// 查询历史记录
const viewLsjl = (row) => {
    if(row.YWLXDM === 'ZR' || row.YWLXDM === 'ZX'){
        const {DWYWID: uuId, MBID, MBLX, ZYFLDM, YWLXDM, EXTENSION} = row;
        tabFun.addTabByRoutePath(
        `承包商信息`,
        "/contractors/cbsjbxxIndex",
        {
            uuId,
            MBID,
            MBLX,
            ZYFLDM,
            YWLXDM: YWLXDM ?? 'ZR',
            JGDWYWID: row.JGDWYWID,
            EXTENSION,
            editable: false
        },
        {}
        );
    }else if(YWLXDM === 'BG'){
        let params={
            editable: false,
            id: row.DWYWID,
            operation: 'view'
        }
        tabFun.addTabByRoutePath("队伍变更信息", "/contractors/dwbgEdit", {params}, {});
    }
}
// 根据队伍的承包商业务Id更新虚拟队伍信息的 YWBT
const updateXnDwxx = () => {
    let param = {
        DWYWID: DWYWID,
        YWBT: saveForm.value.JBXX.CBSDWQC + `-${statusDic.value[YWLXDM]}`,
    };
    axiosUtil.post('/backend/sccbsgl/cbsyj/updateXnDwxx', param).then((res) => {

    });
}

</script>

<style scoped>
.container {
    height: calc(100vh - 100px);
    background-color: #fff;
    overflow-y: auto;
}

.container .el-tabs {
    height: 100%;
    margin: 0px 40px 10px 40px;
}

.container .el-tabs ::v-deep .el-tabs__header {
    margin-bottom: 0;
}

.container .el-tabs ::v-deep .el-tabs__content {
    height: calc(100% - 60px);
    border: 2px solid #dfdfdf;
}

.tab-pane-content {
    height: 100%;
}
:deep(.el-table .el-button) {
    padding: 5px;
}

:deep(.el-table .el-button + .el-button) {
    margin-left: 5px;
}

::v-deep .divider .el-divider__text{
    color:#5f80c7;
    font-weight: bold;
}
</style>
