<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="120px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell no-border-bottom" v-if="formData.XMXX.SFFBD==='1'">
          <el-form-item label="选择参与标段：" prop="CYBD" >
            <el-table ref="datatable91634" :data="formData.XMXX.BDList" height="150px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="BDMC" label="标段名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="YJJE" label="预计金额（万元）" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="CY" label="参与" align="center"
                               :show-overflow-tooltip="true" width="160">
                <template #default="{row}">
                  <el-checkbox :disabled="!editable" v-model="row.SFCY" label="" size="default" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>


        <el-col :span="24" class="grid-cell">
          <el-form-item label="回函时间：" prop="HHSJ">
            <div style="margin-left: 10px">{{ formData.CJSJ }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="确认函：" prop="QRH">
            <vsfileupload
                style="margin-left: 10px"
                :editable="editable"
                :busId="params.id"
                :key="params.id"
                ywlb="QRH"
                busType="QRH"
                :limit="100"
            ></vsfileupload>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="备注：" prop="BZ">
            <el-input v-model="formData.BZ" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系人" prop="LXR" >
            <el-input v-model="formData.LXR" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系电话" prop="LXDH" >
            <el-input v-model="formData.LXDH" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系邮箱" prop="LXYX" >
            <el-input v-model="formData.LXYX" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>

      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import component from "@views/component";

export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      QRHHID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        SFCY: '1',

        XMXX:{}
      },
      rules: {
        SFCY: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXR: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXDH: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXYX: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      HHLX: ''
    })

    const getFormData = () => {
      let params={
        QRHHID: state.QRHHID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/xstzfb/selectHhbmxxById', params).then((res) => {
        state.formData=res.data
        state.formData.XMXX.BDList.forEach(item=>{
          item.SFCY=item.SFCY === '1'
        })
        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        QRHHID: state.QRHHID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        BMSJ: comFun.getNowTime(),
        YWZT: '1',
        SHZT: '1',
        LY: state.HHLX,
        ZBHHList: []
      }
      state.formData.XMXX.BDList.forEach(item=>{
        if(params.SFCY==='1' && (item.SFCY || state.formData.XMXX.SFFBD==='0')){
          params.ZBHHList.push({
            HHID: comFun.newId(),
            GGID: state.formData.GGID,
            FABDID: item.FABDID,
            QRHHID: state.QRHHID,
            DWWYBS: state.formData.DWWYBS,
            DWMC: state.formData.DWMC,
            BMSJ: params.BMSJ,
            LXR: state.formData.LXR,
            LXDH: state.formData.LXDH,
            LXYX: state.formData.LXYX,
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
            YWZT: '1',
          })
        }
      })

      console.log(params)
      state.loading=true
      axiosUtil.post('/backend/xsgl/xstzfb/saveHhbmForm',params).then(res=>{
        if(res.data.ZT==='1'){
          ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
          closeForm()
          emit('submit')
        }else {
          ElMessage.error(res.data.msg)
        }

        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            if(state.formData.XMXX.SFFBD==='1'&& !state.formData.XMXX.BDList.find(item=>item.SFCY)){
              ElMessage({
                message: '请选择参与标段',
                type: 'error',
              })
              resolve(false)
            }else {
              resolve(true)
            }
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }


    const getXmxx = () => {
      let params={
        GGID: state.formData.GGID,
        RYZH: state.userInfo.userLoginName,
      }
      axiosUtil.get('/backend/xsgl/xstzfb/selectXmxxByGgid',params).then(res=>{
        state.formData.XMXX=res.data
      })
    }

    const getDwxx = () => {
      state.loading=true
      let params={
        GGID: state.formData.GGID,
        RYZH: state.userInfo.userLoginName,
        RYXM: state.userInfo.userName,
      }

      axiosUtil.get('/backend/xsgl/xstzfb/selectDwxxByGgid',params).then(res=>{
        if(res.data){
          state.formData.DWMC=res.data.DWMC
          state.formData.DWWYBS=res.data.DWWYBS
          state.formData.LXR=res.data.LXR
          state.formData.LXDH=res.data.LXDH
          state.formData.LXYX=res.data.EMAIL
          state.loading=false
        }else {
          ElMessage.error('当前用户不符合报名要求')
        }
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }else {
        state.formData.GGID=props.params.GGID
        getDwxx()
        getXmxx()
      }
      state.HHLX=props.params.HHLX
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData
    }
  }

})
</script>

<style scoped>

</style>
