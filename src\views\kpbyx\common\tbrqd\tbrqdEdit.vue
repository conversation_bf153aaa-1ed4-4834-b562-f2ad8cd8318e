<template>
  <div>
    <el-form :model="tableData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;color: #2A96F9;
      border-bottom: 3px solid #2A96F9;text-align: center;padding-bottom: 2px;padding-left: 20px;padding-right: 20px;">
        投标人签到
      </div>

      <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 520px)"
                :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column prop="DWMC" label="投标单位名称" align="center"
                         :show-overflow-tooltip="true" min-width="160"></el-table-column>
        <el-table-column prop="QZXX" label="法定代表人或授权委托人" align="center" width="160">
          <template #default="{row}">
            {{row.QD_SFQD==='1' ? '' : '未签字'}}
            <signImageUpload v-if="row.JLID" :editable="editable && role==='TBR'" :busId="row.JLID"
                             busType="TBRQZ" ywlb="QZTP" v-model:signFile="row.signFile" :key="row.JLID"/>
          </template>


        </el-table-column>
        <el-table-column prop="BZ" label="备注" align="center" width="160">
        </el-table-column>
      </el-table>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData('submit')" v-if="editable && role==='ZCR'">提交</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable && role==='TBR'">提交</el-button>
        <el-button @click="getTableData">刷新</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import signImageUpload from "@views/components/signImageUpload";


export default defineComponent({
  name: '',
  components: {signImageUpload},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.parentForm.TBRQD_WCZT!=='1',
      role: props.fromParams.role,
      KPBYXID: props.params.KPBYXID,
      JLID: props.params.JLID,
      tableData: [],
      HHID: props.parentForm.RYXX.HHID,
      rules: {

      }
    })

    const getTableData = () => {
      let params={
        JLID: state.JLID,
        HHID: state.HHID,
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/tbrqd/selectTbrqdList', params).then((res) => {
        state.tableData=res.data || []
        state.tableData.forEach(item=>{
          if(!item.JLID){
            Object.assign(item,{
              JLID:comFun.newId(),
              KPBYXID: state.KPBYXID,
              QZZTBS: item.HHID,
              QZSJ: comFun.getNowTime(),
              YWLX: 'TBRQZ',
              CJRZH: state.userInfo.userLoginName,
              CJRXM: state.userInfo.userName,
              CJDWID: state.userInfo.orgnaId,
              CJSJ: comFun.getNowTime(),
              SHZT: '0',
              YWZT: '1',
            })
          }
        })

        if(state.role==='TBR' && state.tableData.find(item=>item.QD_SFQD!=='1')){
          state.editable=true
        }else if(state.role==='TBR'){
          state.editable=false
        }

        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        TJRLX: state.role,
        HHID: state.HHID,
        QZXXList: state.tableData.map(item=>{
          return{
            ...item,
            SHZT: '1'

          }
        })
      }
      state.loading=true
      axiosUtil.post('/backend/kpbyx/tbrqd/saveTbrqzxx',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        if(state.role==='ZCR'){
          emit('saveFromData', {TBRQD_WCSJ: comFun.getNowTime(),TBRQD_WCZT: '1'}, {})
          nextTick(() => {
            emit('nextStep', `已完成投标人签到`)
          })
        }
        state.editable=false
        state.loading=false
      })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            if(state.role==='TBR' && state.tableData.find(item=>!item.signFile)){
              ElMessage({
                message: '请完成签字',
                type: 'error',
              })
              return
            }


            if(state.role==='ZCR' && state.tableData.find(item=>item.QD_SFQD!=='1')){
              ElMessage({
                message: '请等待投标人完成签字',
                type: 'error',
              })
              return
            }

            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }




    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getTableData()
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      getTableData,
      submitForm
    }
  }

})
</script>

<style scoped>

</style>
