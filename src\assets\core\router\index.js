
import {router,version} from "../vsui.vue/index.js";
import {routesUnUseVsuiVue,routesUseVsuiVue} from "./router.define.js";
import { routerInterceptorFirst, routerInterceptorLast } from './router.interceptor'

const vsui=version.name;
router.removeRoute("err_403");
//以下代码添加路由到根路由
routesUnUseVsuiVue.forEach(route=>{
  router.addRoute(route);
})


//以下代码添加路由到vsui.vue的home层级路由，业务代码应该添加到home路由下，才会加载vsui.vue框架
routesUseVsuiVue.forEach(route=>{
  router.addRoute(vsui,route)
})



/**
  * 
  * 
  * 如下图例展示了多个路由拦截器的工作机制
  *
  *
  *                ↗-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-↘
  *                ↑                  后加入                                                                         |
  *     -----------|----------  routerInterceptor3  beforeEach                                                       ↓
  *     -----------|----------  routerInterceptor2  beforeEach                                                       ↓
  *     -----------↑----------  routerInterceptor1  beforeEach                                                       |
  *     -----------|----------  routerInterceptor0  beforeEach                                                       ↓
  *                ↑                   先加入                                                                         |
  *             路由跳转                                                                                              ↓
  *                ↑                   后加入                                                                         |
  *     -----------|----------  routerInterceptor3  afterEach                                           
  *     -----------|----------  routerInterceptor2  afterEach                                                        ↓
  *     -----------↑----------  routerInterceptor1  afterEach                                                        |
  *     -----------|----------  routerInterceptor0  afterEach                                                        ↓
  *                ↑                   先加入                                                                         |
  *                ↖-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-↙
  *
  *
*/


router.beforeEach(routerInterceptorFirst.beforeEach)
router.afterEach(routerInterceptorFirst.afterEach)



window.setTimeout(function () {
  router.beforeEach(routerInterceptorLast.beforeEach)
  router.afterEach(routerInterceptorLast.afterEach)
}, 10)



export default router;  
