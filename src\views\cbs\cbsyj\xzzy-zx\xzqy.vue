<!-- 选择区域 -->
<template>
  <el-form ref="form" class="lui-page" size="default" label-width="0" inline>
    <div class="table">
      <el-table :data="tableData" class="lui-table" height="calc(100vh - 260px)" border>
        <el-table-column
            label="专业名称"
            prop="ZYMC"
            header-align="center"
            align="center"
        ></el-table-column>
        <el-table-column
            label="服务区域"
            prop="FWQY"
            header-align="center"
            align="center"
        >
          <template v-slot="scope">
            <!-- <div>
              <el-select v-model="scope.row.FWQY" clearable placeholder="请选择">
                <el-option
                    v-for="(item, index) in fwqyOptions"
                    :key="index"
                    :label="item.DMMC"
                    :value="item.DMXX"
                ></el-option>
              </el-select>
            </div> -->
            <el-checkbox size="small" v-if="scope.row.ZYFL === 'C'"
                         v-model="scope.row.checkAll"
                         :indeterminate="scope.row.isIndeterminate"
                         @change="(value)=>handleCheckAllChange(value,scope.row)">
              全选
            </el-checkbox>
            <el-checkbox-group v-if="scope.row.ZYFL === 'C'" v-model="scope.row.fwid" size="small" @change="(value)=>handleCheckedCitiesChange(value,scope.row)">
              <el-checkbox v-for="(item, index) in fwqyOptions"
                           :key="index"
                           :label="item.ORGNA_ID">{{item.ORGNA_NAME}}</el-checkbox>
            </el-checkbox-group>
            <span v-else>全局</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
            label="操作"
            header-align="center"
            align="center"
            width="100"
        >
          <template v-slot="scope">
            <div>
              <el-button type="text" @click="handleDelete(scope.row, scope.$index)"
              >删除
              </el-button>
            </div>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <el-row class="btn" justify="end" align="center">
      <el-button type="primary" @click="preStep">上一步</el-button>
      <el-button type="primary" v-if="props.change == '1'" @click="handleChange">确定</el-button>
      <el-button type="primary" v-else @click="handleSave">确定</el-button>
    </el-row>
  </el-form>
</template>

<script setup>
import outerBox from "@components/common/outerBox";
import {
    ref,
    reactive,
    getCurrentInstance,
    onMounted,
    defineProps,
    watch,
    inject,
} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import tabFun from "@src/lib/tabFun";
import {getCommonsSelectDMB} from "@src/api/gcyztb.js";

postCbsyjSaveXzqy;
import {postCbsyjSaveXzqy, getBgGetChangedTeamById} from "@src/api/sccbsgl.js";
import {v4 as uuidv4} from "uuid";
import {mixin} from "@src/assets/core/index";

const {vsuiRoute, vsuiEventbus} = mixin();

const emits = defineEmits(["preStep", "handleChange"]);
let tableData = ref([]);
let fwqyOptions = ref([]);
const uuId = inject("uuId");
const props = defineProps({
    selectData: {
        type: Object,
        default: () => {
        },
    },
    change: {
        type: String,
        default: "",
    },
    dwlx: {
        type: String,
        default: "",
    },
});
watch(
    () => props.selectData,
    (val) => {
        for(let i=0;i<val.length;i++){
            if(val[i].ZYFL === 'A' || val[i].ZYFL === 'B'){
                val[i].fwid = ['all'];
            }else{
                val[i].fwid = [];
            }
        }
        tableData.value = val;
    },
    {
        immediate: true,
    }
);
const getArea = () => {
    axiosUtil.get('/backend/common/selectTwoOrgAndJgbmList', {}).then((res) => {
        fwqyOptions.value = res.data;
    });
};
/**查询表格数据 */
const queryTableData = () => {
    // TODO 查询
    /* tableData.value.push(
        ...[
          { ZYMC: "信息系统技术服务", FWQY: "sl" },
          { ZYMC: "数据技术服务", FWQY: "xc" },
          { ZYMC: "工控技术服务", FWQY: "sl" },
        ]
      ); */
};

const handleChange = () => {
    let flag = false;
    tableData.value.forEach((x) => {
        if (!x.FWQY) {
            flag = true;
        }
    });
    if (flag) {
        ElMessage({
            message: "请选择服务区域",
            type: "warning",
        });
        return;
    }
    emits("handleChange", tableData);
};

/**删除 */
const handleDelete = (val, index) => {
    ElMessageBox.confirm("是否删除此条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(() => {
            // TODO 删除
            ElMessage({
                message: "已删除",
                type: "success",
            });
            queryTableData();
        })
        .catch(() => {
            ElMessage({
                message: "已取消删除",
                type: "info",
            });
        });
};
/**上一步 */
const preStep = () => {
    emits("preStep");
};
/**确定 */
const handleSave = () => {
  if(tableData.value.some(i => !i.fwid||i.fwid.length===0)){
    ElMessage.warning('请选择服务区域')
    return
  }
    let fwqyParams=[];
    tableData.value.forEach(item=>{
        item.fwqy='';
        item.fwid.forEach(ii=>{
            if(ii === 'all'){
                item.fwqy='全局';
                fwqyParams.push({
                    FWQYID: uuidv4().replace(/-/g, ""), //"主键id",
                    ZYMXID: item.ZYMXID, //"专业明细ID",
                    FWQYWYBS: uuidv4().replace(/-/g, ""), //"服务区域唯一标识",
                    FWQYBM: ii, //"服务区域编码",
                    FWQYMC: '全局', //"服务区域名称",
                })
            }else{
                let _FWQYMC = fwqyOptions.value.find((i) => i.ORGNA_ID === ii).ORGNA_NAME
                fwqyParams.push({
                    FWQYID: uuidv4().replace(/-/g, ""), //"主键id",
                    ZYMXID: item.ZYMXID, //"专业明细ID",
                    FWQYWYBS: uuidv4().replace(/-/g, ""), //"服务区域唯一标识",
                    FWQYBM: ii, //"服务区域编码",
                    FWQYMC: _FWQYMC, //"服务区域名称",
                })
                if(_FWQYMC){
                    item.fwqy=item.fwqy+_FWQYMC+';'
                }
            }
        })
    })
    postCbsyjSaveXzqy(fwqyParams)
        .then(({data}) => {
            ElMessage.success("保存区域成功");
            // if(props.dwlx == 'DW'){
            //     getBgGetChangedTeamById({dwid: uuId}).then(r => {
            //         tabFun.openNewTabClose('队伍信息增项', '/contractors/yrsqxxIndex', {
            //             DWYWID: r.data.DWYWID,
            //             YWLXDM: 'ZX',
            //             editable:true,
            //             backPath: '/contractors/addTeamList'
            //         },{});
            //     })
            //     // vsuiEventbus.emit("reloadCbsjbxx", {});
            //     vsuiEventbus.emit("reloadTableData", {});
            // }else{
                tabFun.openNewTabClose(
                  "承包商信息增项",
                  "/contractors/cbsjbxxIndex",
                  {
                      uuId, //队伍业务ID
                      MBID: tableData.value.map((i) => i.QYMBID).join(","), //模板ID
                      MBLX: props.dwlx, //模板类型
                      ZYFLDM: tableData.value.map((i) => i.ZYBM).join(","), //专业分类代码
                      YWLXDM: "ZX", //业务类型代码
                      editable:true,
                  },
                  {}
                );
                // vsuiEventbus.emit("reloadCbsjbxx", {});
                vsuiEventbus.emit("reloadCbsjbxx", {
                    uuId, //队伍业务ID
                    MBID: tableData.value.map((i) => i.QYMBID).join(","), //模板ID
                    MBLX: props.dwlx, //模板类型
                    ZYFLDM: tableData.value.map((i) => i.ZYBM).join(","), //专业分类代码
                    YWLXDM: "ZX", //业务类型代码
                });
            // }

            // tabFun.closeTabByPath('/contractors/zrfqIndex')
        })
        .catch((err) => {
        });
};
const handleCheckAllChange = (val,row) => {
  row.fwid = val ? fwqyOptions.value.map(item=>item.ORGNA_ID) : []
  row.isIndeterminate = false
}

const handleCheckedCitiesChange = (value,row) => {
  const checkedCount = value.length
  row.checkAll = checkedCount === fwqyOptions.value.length
  row.isIndeterminate = checkedCount > 0 && checkedCount < fwqyOptions.value.length
}
onMounted(() => {
    queryTableData();
    getArea();
});
</script>

<style scoped src="../../style/index.css"></style>
<style scoped>
.container {
    overflow: hidden;
    width: calc(100% - 20px);
    height: 100%;
}

.out-box-content {
    height: calc(100% - 140px);
}

.table {
    height: calc(100% - 40px);
    overflow: hidden;
}

.btn {
    margin-top: 10px;
}
</style>
