<template>
    <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
            size="default" @submit.prevent>
        <el-row :gutter="20" class="lui-search-form" style="margin-bottom: 20px;">
            <el-col :span="6">
                <el-input v-model="listQuery.cbsmc" placeholder="请输入承包商名称"></el-input>
            </el-col>
            <el-col :span="18">
                <el-button type="primary" @click="initListData">查询</el-button>
            </el-col>
        </el-row>
        <div class="container-wrapper">
            <el-table
                class="lui-table"
                highlight-current-row
                ref="table"
                size="default"
                height="500px"
                border
                :data="tableData"
                v-loading="listLoading"
            >
                <el-table-column type="index" width="60" label="序号" align="center"/>
                <el-table-column label="承包商名称" min-width="300" prop="CBSDWQC" header-align="center" align="left">
                </el-table-column>
                <el-table-column label="操作" min-width="60" header-align="center" align="center">
                    <template #default="{ row }">
                        <el-button class="lui-table-button" @click="callback(row)">
                            选择
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </el-form>
</template>
<script>
import {defineComponent, onMounted, reactive, toRefs} from "vue";
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import { v4 as uuidv4 } from "uuid";
export default defineComponent({
  components: {},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery:{
        deptId: VSAuth.getAuthInfo().permission.orgnaId,
        cbsmc: ''
      },
      listLoading: false,
      tableData: [],
    })
    onMounted(() => {
      initListData();
    })

    // 初始化数据
    const initListData = () => {
      const params = {
        ...state.listQuery,
      }
      state.listLoading=true
      axiosUtil.get('/backend/sccbsgl/dwrcgl/dwhb/queryDxcbs', params).then((res) => {
        state.tableData = res.data;
        state.listLoading=false
      });
    }
    
    const callback = (row) => {
      emit('callBackCbsxx',row);
    }
    return {
      ...toRefs(state),
      initListData,
      callback,
    }
  }

})
</script>

<style scoped>

</style>
