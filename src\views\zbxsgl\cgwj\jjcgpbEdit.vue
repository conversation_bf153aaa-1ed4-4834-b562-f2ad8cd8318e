<template>
  <div>
    <div style="text-align: center; color: red; font-size: 32px; margin-bottom: 30px;">评分算法设置</div>
    <el-form :model="formData" ref="vForm" label-position="top" class="lui-card-form">
      <!-- 一、竞价采购评标办法 -->
      <el-card shadow="never" style="margin-bottom: 20px;">
        <div style="font-weight: bold; font-size: 18px; margin-bottom: 10px;">一、竞价采购评标办法</div>
        <el-form-item prop="PFSF_PBBF">
          <el-input style="font-size: 16px;"
            type="textarea"
            v-model="formData.PFSF_PBBF"
            :rows="5"
            placeholder="请输入竞价采购评标办法内容"
          />
        </el-form-item>
      </el-card>
      <!-- 二、定标办法 -->
      <el-card shadow="never" style="margin-bottom: 20px;">
        <div style="font-weight: bold; font-size: 18px; margin-bottom: 10px;">二、定标办法</div>
        <el-form-item prop="PFSF_GDJJDBBF">
          <el-input style="font-size: 14px;"
            type="textarea"
            v-model="formData.PFSF_GDJJDBBF"
            :rows="3"
            placeholder="请输入定标办法内容"
          />
        </el-form-item>
      </el-card>
      <!-- 三、流标及招标结束 -->
      <el-card shadow="never">
        <div style="font-weight: bold; font-size: 18px; margin-bottom: 10px;">三、流标及招标结束</div>
        <el-form-item prop="PFSF_GDJJLBZBJS">
          <el-input style="font-size: 16px;"
            type="textarea"
            v-model="formData.PFSF_GDJJLBZBJS"
            :rows="2"
            placeholder="请输入流标及招标结束内容"
          />
        </el-form-item>
      </el-card>
    </el-form>
    <el-row style="line-height: 50px;">
      <el-col :span="24" style="text-align: center; position: fixed; bottom: 9px; left: 0; right: 0; z-index: 100;background-color: white;">
        <el-button size="default" type="primary" @click="confirmData">确认</el-button>
        <el-button size="default" @click="close">关闭</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
});
const emit = defineEmits(["closeDialogpsbf","reload"]);
const formData = ref({});

const rules = ref({});
const disabledDate = (time) => {
    return time.getTime() < Date.now() - 1000*60*60*24;
};

onMounted(() => {
  formData.value = Object.assign({}, props.data);

  if(formData.value.PFSF_PBBF == null){
    formData.value.PFSF_PBBF = '1、本次采购采用低价中标的方法，质量和服务均能满足采购文件实质性响应要求的承包商，报价最低的为第一中标顺序人。\n2、《技术标》评分满分100分，评审分结果为评审小组各成员有效评分结果的平均值。当评审小组成员人数大于等于11人时，去掉1个最高分值和1个最低分值后计算平均值。';
  }
  if(formData.value.PFSF_GDJJDBBF == null){
    formData.value.PFSF_GDJJDBBF = '1、报价最低的为第一中标顺序人。\n2、报价相同情况下，由评委会小组根据投标文件内容，确定中标人顺序。';
  }
  if(formData.value.PFSF_GDJJLBZBJS == null){
    formData.value.PFSF_GDJJLBZBJS = '1、如一个标段的所有投标文件均为无效标，则宣布流标。';
  }
});

// 确认
const vForm = ref(null);
const confirmData = () => {
  if(!formData.value.PFSF_PBBF || !formData.value.PFSF_GDJJDBBF || !formData.value.PFSF_GDJJLBZBJS){
    ElMessage.warning('还有未填写项!');
    return;
  }

  emit('confirmDialogData', formData.value);
}

const close = () => {
  emit('closeDialogpsbf');
}

defineExpose({});

</script>

<style scoped>
.lui-card-form {
  height: calc(100vh - 160px);
  overflow-y: auto;
  padding-bottom: 80px; /* 新增，避免底部fixed按钮遮挡内容 */
}

::v-deep .el-input__inner{
  border: 1px solid gray;
  text-align: center;
}

</style>