<template>
  <el-form :model="formData" ref="vForm" class="lui-card-form" :rules="vFormRules" :label-width="110" size="default">
    <el-row class="grid-row">
      <el-col :span="8" class="grid-cell">
        <el-form-item label="项目名称" prop="XMMC">
          <el-input v-model.trim="formData.XMMC" placeholder="请输入" :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="甲方单位" prop="JSDW">
          <el-input v-model.trim="formData.JSDW" placeholder="请输入" :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="合同金额(万元)" prop="HTJE" label-width="150">
          <el-input type="number" v-model.number="formData.HTJE" placeholder="请输入" :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="合同开始日期" prop="HTRQKS">
          <el-date-picker
              :disabled="!editable"
              v-model="formData.HTRQKS"
              type="date"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              placeholder="合同开始日期"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="合同结束日期" prop="HTRQJS">
          <el-date-picker
              :disabled="!editable"
              v-model="formData.HTRQJS"
              type="date"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              placeholder="合同结束日期"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell"></el-col>

      <el-col :span="24" class="grid-cell">
        <el-form-item label="业绩简介" prop="GCFW">
          <el-input
              :disabled="!editable"
              type="textarea"
              maxlength="500"
              show-word-limit
              v-model.trim="formData.GCFW"
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-collapse-item title="相关证明附件" name="1">
        <vsfileupload :busId="editData.YJZSJID" :editable="editable" :maxSize="10"
                      :key="editData.YJZSJID" ywlb="DWYJFJ"
                      busType="dwyj"
                      :limit="100"></vsfileupload>
      </el-collapse-item>
    </el-collapse>
    <div v-if="editable">
      <el-button type="primary" size="default" @click="continueAdd">继续添加</el-button>
      <el-button type="primary" size="default" @click="confirm()">确定</el-button>
      <el-button type="primary" size="default" @click="cancel">取消</el-button>
    </div>
  </el-form>
</template>
<script setup>
import {defineEmits, defineProps, ref, watch,} from "vue";
import vsfileupload from "@src/views/components/vsfileupload.vue";

const dafultData = ref({
  XMMC: null,
  JSDW: null,
  HTJE: null,
  HTRQKS: null,
  HTRQJS: null,
  GCFW: null,
});
const formData = ref({
  XMMC: null,
  JSDW: null,
  HTJE: null,
  HTRQKS: null,
  HTRQJS: null,
  GCFW: null,
});
const activeNames = ref("1");
const certificateTypeOptions = ref([
    {
        label: "HSE",
        value: "HSE",
    },
]);
const tableColumn = ref([
  {
    label: "类型",
    prop: "LX",
    align: "center",
    showOverflowTooltip: true,
      slot: "LX",
  },
  {
    label: "文件名称",
    prop: "WJMC",
    align: "center",
    showOverflowTooltip: true,
    slot: "WJMC",
  },
  {
    label: "操作",
    align: "center",
    width: 150,
    fixed: "right",
    slot: "opration",
  },
]);
const tableData = ref([{}]);
const insertRow = (row, index) => {};
const props = defineProps({
    editData: {
        type: Object,
        default: () => {},
    },
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  }
});
watch(
    () => props.editData,
    (val) => {
        console.log('1111111111111111');
        formData.value = Object.assign(formData.value, {...dafultData.value}, {...val});
    },
    {
        immediate: true,
        deep: false
    }
);

const emits = defineEmits(["updateData", "close"]);
// 继续添加
const continueAdd = () => {
    confirm(true);
};
//确认
const confirm = (isAdd = false) => {
    validateForm().then((result) => {
        emits(
            "updateData",
            {
                ...formData.value,
            },
            isAdd
        );
    }).catch((err) => {
        console.log(err);
    });
};
// 取消
const cancel = () => {
    emits("close");
};

const updateFile = (row, index) => {};
const deleteRow = (row, index) => {};
const vForm = ref(null);
const vFormRules = ref({
    XMMC:[
        { required: true, message: '请输入项目名称', trigger: 'blur' },
        { max: 256, message: '最多输入256个字符', trigger: 'blur' },
    ],
    JSDW:[
        { required: true, message: '请输入建设单位', trigger: 'blur' },
        { max: 256, message: '最多输入256个字符', trigger: 'blur' },
    ],
    HTJE:[
        { required: true, message: '请输入合同金额', trigger: 'blur' },
        { pattern: /^[0-9]{1,16}(.[0-9]{1,6})?$/, message: '请输入正常范围', trigger: 'change' },
        { type: 'number', message: '请输入数字' },
    ],
    HTRQKS:[
        { required: false, message: '请选择合同日期开始', trigger: 'change' },
    ],
    HTRQJS:[
        { required: false, message: '请选择合同日期结束', trigger: 'change' },
         {validator: (rule,value,callback) => {//参加工作日期日期格式校验
            if (value && formData.value.HTRQKS) {
                  if (value < formData.value.HTRQKS) {
                    callback(new Error('合同结束日期必须大于合同开始日期'));
                  } else {
                    callback();
                  }
                } else {
                  callback();
                }
          }, trigger: 'blur' }
    ],
    GCFW:[
        { required: false, message: '请输入主要工作量', trigger: 'blur' },
        { max: 4000, message: '最多输入4000个字符', trigger: 'blur' },
    ],
    

})
const validateForm = () => {
  return vForm.value.validate()
}
defineExpose({
  validateForm
})
</script>
