<!-- 新增队伍 -->
<template>
  <div>
    <el-card class="box-card" style="margin-top: 10px;padding-bottom: 20px">
      <template #header>
        <div style="display: flex;align-items: center;gap: 10px">
          <!-- <div class="card-header">
            <div class="ra-icon"></div>
            <span style="font-size: 18px;font-weight: 400">变更草稿</span>
          </div> -->
          <el-input ref="input45296" placeholder="请输入业务标题" v-model="listQuery.YWBT" type="text" clearable size="default" style="width: 200px;margin-left: auto">
          </el-input>
          <el-button type="primary" @click="getDataList" size="default">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
        </div>
      </template>
      <el-table
          class="lui-table"
          :data="tableData"
          width="100%"
          size="default"
          :stripe="true"
          height="calc(100vh - 320px)"
      >
        <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            align="center"
            width="50"
        ></el-table-column>
        <el-table-column
            prop="YWLXDMMC"
            label="业务类型"
            header-align="center"
            align="center"
            show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
            prop="YWBTMC"
            label="业务标题"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="CJRXM"
            label="办理人"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="CJSJ"
            label="办理时间"
            header-align="center"
            align="center"
        >
        </el-table-column>
        <el-table-column
            label="操作"
            header-align="center"
            align="center"
            width="200"
        >
          <template #default="{ row, $index }">
            <div class="table-col-btn">
              <el-button v-if="!row.SHZT || row.SHZT==='0'" class="lui-table-button" @click="handleEdit(row, $index)">编辑
              </el-button>
              <el-button :loading="deling.has(row.YWID)" v-if="!row.SHZT || row.SHZT==='0'" class="lui-table-button" @click="handleDelete(row, $index)">
                删除
              </el-button>
              <el-button v-if="row.SHZT && row.SHZT!=='0'" class="lui-table-button" @click="handleView(row, $index)">查看
              </el-button>
              <el-button v-if="row.SHZT && row.SHZT!=='0'" class="lui-table-button" @click="handleMonitor(row, $index)">
                跟踪
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                     :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                     class="lui-pagination"
                     @size-change="getDataList" @current-change="getDataList" :total="total">
      </el-pagination>
    </el-card>

    <el-dialog z-index="1000" title="业务流程跟踪" v-model="showDialog" width="80%" top="35px" :close-on-click-modal="false">
      <monitorForm2 v-if="showDialog" :queryParams="queryParams"/>
    </el-dialog>
  </div>
</template>

<script>
import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, onUnmounted} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import hmdglEdit from "@views/khpj/hmdgl/hmdglEdit";
import vsfileupload from "@views/components/vsfileupload";
import TabFun from "@lib/tabFun";
import vsAuth from "@lib/vsAuth";
import {mixin} from "@core";
import monitorForm2 from "../../workflow/MonitorForm2";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus, hmdglEdit, vsfileupload,monitorForm2},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,

      CLDXLXOptions: [],
      HMDLYOptions: [],
      showDialog: false,
      queryParams: {},
      deling: new Set()

    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        orgnaId: vsAuth.getAuthInfo().permission.orgnaId
      }
      axiosUtil.get('/backend/sccbsgl/dwxxbg/selectCbsbgcgPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const handleView = (row) => {
      let params={
        editable: false,
        id: row.YWID,
        operation: 'view'
      }

      if(row.YWLX==='DWBG'){
        TabFun.addTabByRoutePath("队伍变更信息", "/contractors/dwbgEdit", {params}, {});
      }else if(row.YWLX==='CBSBG'){
        TabFun.addTabByRoutePath("承包商变更信息", "/contractors/cbsbgEdit", {params}, {});
      }else if(row.YWLX==='QYBG'){
        TabFun.addTabByRoutePath("区域变更信息", "/contractors/qybgEdit", {params}, {});
      }


    }

    const handleEdit = (row) => {
      let params={
        editable: true,
        id: row.YWID,
        operation: 'edit'
      }

      if(row.YWLX==='DWBG'){
        TabFun.addTabByRoutePath("队伍变更信息", "/contractors/dwbgEdit", {params}, {});
      }else if(row.YWLX==='CBSBG'){
        TabFun.addTabByRoutePath("承包商变更信息", "/contractors/cbsbgEdit", {params}, {});
      }if(row.YWLX==='DWFS'){
        TabFun.addTabByRoutePath("队伍复审信息", "/contractors/dwbgEdit", {params}, {});
      }
      // else if(row.YWLX==='ZYSC'){
      //   TabFun.addTabByRoutePath("队伍专业删除", "/contractors/dwzyscEdit", {params}, {});
      // }
      else if(row.YWLX==='QYBG'){
        TabFun.addTabByRoutePath("区域变更信息", "/contractors/qybgEdit", {params}, {});
      }

    }

    const handleMonitor = (row) => {
      state.queryParams.id = row.YWID
      state.showDialog = true
    }

    const handleDelete = (row) => {
      ElMessageBox.confirm("确定删除此数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        state.deling.add(row.YWID)

        if(row.YWLX==='DWBG' || row.YWLX==='CBSBG' || row.YWLX==='ZYSC'){
          axiosUtil.del('/backend/sccbsgl/dwxxbg/delBgsj?BGJLID='+row.BGJLID,null).then(res=>{
            ElMessage.success('删除成功')
            state.deling.delete(row.YWID)
            getDataList()
          })
        }else if(row.YWLX==='QYBG'){
          axiosUtil.del('/backend/sccbsgl/qyxxbg/delQybgxx?QYBGID='+row.YWID,null).then(res=>{
            ElMessage.success('删除成功')
            state.deling.delete(row.YWID)
            getDataList()
          })
        }




      }).catch(() => {
        // catch error
      });
    }


    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res => {
        state[resList] = res.data || []
      })
    }

    const {vsuiEventbus} = mixin()

    onMounted(() => {
      vsuiEventbus.on('reloadBGTableData',getDataList);
      getDataList()
      getDMBData('CLDXLX', 'CLDXLXOptions')
      getDMBData('HMDLY', 'HMDLYOptions')
    })

    onUnmounted(()=>{
      vsuiEventbus.off('reloadBGTableData',getDataList);
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      handleEdit,
      handleDelete,
      handleView,
      handleMonitor


    }

  }

})
</script>


<style scoped>

.el-row-class {
  display: flex;
  align-items: center;
  font-size: 18px;
  height: 50px;
  padding-left: 30px;
}

.el-row-class:hover {
  background-color: #edf4ff;
}

.bell-icon {
  width: 1em;
  height: 1em;
  color: #3c85fb;
  background-color: rgba(198, 226, 255, 0.74);
  padding: 2px;
  border-radius: 50%;
  margin-right: 20px;
}

.row-button {
  font-size: 18px;
  color: #409EFF;
}

.ra-icon {
  width: 8px;
  height: 16px;
  border-radius: 4px;
  background: #3c85fb;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 20px;

}
</style>
