const pkgInfo = require('../package.json');
try{

  var env = process.env;

  var COLOR = env.npm_config_color?true:false;


  var BANNER = `

           ██████╗ ██╗   ██╗███████╗██╗   ██╗██╗    ██╗██╗   ██╗██╗   ██╗███████╗    ███╗   ███╗██╗   ██╗██╗  ████████╗██╗██████╗ ██╗     ███████╗██╗  ██╗
          ██╔═══██╗██║   ██║██╔════╝██║   ██║██║   ██╔╝██║   ██║██║   ██║██╔════╝    ████╗ ████║██║   ██║██║  ╚══██╔══╝██║██╔══██╗██║     ██╔════╝╚██╗██╔╝
          ██║██╗██║██║   ██║███████╗██║   ██║██║  ██╔╝ ██║   ██║██║   ██║█████╗█████╗██╔████╔██║██║   ██║██║     ██║   ██║██████╔╝██║     █████╗   ╚███╔╝ 
          ██║██║██║╚██╗ ██╔╝╚════██║██║   ██║██║ ██╔╝  ╚██╗ ██╔╝██║   ██║██╔══╝╚════╝██║╚██╔╝██║██║   ██║██║     ██║   ██║██╔═══╝ ██║     ██╔══╝   ██╔██╗ 
          ╚█║████╔╝ ╚████╔╝ ███████║╚██████╔╝██║██╔╝    ╚████╔╝ ╚██████╔╝███████╗    ██║ ╚═╝ ██║╚██████╔╝███████╗██║   ██║██║     ███████╗███████╗██╔╝ ██╗
           ╚╝╚═══╝   ╚═══╝  ╚══════╝ ╚═════╝ ╚═╝╚═╝      ╚═══╝   ╚═════╝ ╚══════╝    ╚═╝     ╚═╝ ╚═════╝ ╚══════╝╚═╝   ╚═╝╚═╝     ╚══════╝╚══════╝╚═╝  ╚═╝
                                                                                                                                                  

                                                                          
                                                                                                ██████╗ ██████╗    ██╗    ██████╗ 
                                                                                              ██╔═══██╗╚════██╗  ███║   ██╔═████╗ 
                                                                                              ██║██╗██║ █████╔╝  ╚██║   ██║██╔██║
                                                                                              ██║██║██║██╔═══╝    ██║   ████╔╝██║
                                                                                              ╚█║████╔╝███████╗██╗██║██╗╚██████╔╝
                                                                                                ╚╝╚═══╝ ╚══════╝╚═╝╚═╝╚═╝ ╚═════╝ 
                                                                                                                                                                                  

  `
  BANNER="\u001B[96m"+BANNER+"\u001B[0m";

  BANNER+=    "     \u001B[96m欢迎使用"+pkgInfo.name+" (\u001B[94m http://10.68.7.155:7002/package/"+pkgInfo.name+"/v/"+pkgInfo.version+" \u001B[0m)，\n\n" +

              "     \u001B[96m该组件仅在胜软NPM(VSNPM)中发布，更多胜软组件请访问：http://10.68.7.155:7002/\u001B[0m\n\n" +

              "     \u001B[96m版本："+pkgInfo.version+"\u001B[0m\n\n" +
    
              "     \u001B[96m公司："+pkgInfo.company+" \u001B[0m\n\n" +
      
              "     \u001B[96m人员者："+pkgInfo.author+" ("+pkgInfo.email+")  \u001B[0m\n\n\n\n";


  (function showBanner() {
    console.log(COLOR ? BANNER : BANNER.replace(/\u001B\[\d+m/g, ''));
  })()
}
catch(e){}



