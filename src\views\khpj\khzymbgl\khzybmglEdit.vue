<template>
  <div v-loading="loading">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="10">
        <el-col :span="11" style="padding: 10px;border: 1px solid #cbcfd5">
          <div style="margin-bottom: 10px;border-bottom: 1px solid #cbcfd5;padding-bottom: 5px;font-size: 16px;font-weight: bold">
            选择模板
          </div>
          <div style="display: flex;padding-bottom: 10px;justify-content: right;gap: 10px">
            <el-button ref="button91277" @click="dialogPJMBVisible=true" type="primary">
              增加
            </el-button>
          </div>
          <el-table ref="table1" :data="formData.MBList" height="calc(400px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="MBMC" label="模板名称" align="center" min-width="120" :show-overflow-tooltip="true"/>
            <el-table-column prop="MBMS" label="模板描述" align="center" min-width="120" :show-overflow-tooltip="true"/>
            <el-table-column prop="CZ" label="操作" align="center" width="120">
              <template #default="scope">
                <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.$index,formData.MBList)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="2">
        </el-col>
        <el-col :span="11" style="padding: 10px;border: 1px solid #cbcfd5">
          <div style="margin-bottom: 10px;border-bottom: 1px solid #cbcfd5;padding-bottom: 5px;font-size: 16px;font-weight: bold">
            选择专业
          </div>
          <div style="display: flex;padding-bottom: 10px;justify-content: right;gap: 10px">
            <el-button ref="button91277" @click="dialogKHZYVisible=true" type="primary">
              增加
            </el-button>
          </div>
          <el-table ref="datatable91634" :data="formData.ZYList" height="calc(400px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="ZYMC" label="考核专业" align="center" min-width="120" :show-overflow-tooltip="true"/>
            <el-table-column prop="BZ" label="备注" align="center" min-width="120" :show-overflow-tooltip="true"/>
            <el-table-column prop="CZ" label="操作" align="center" width="120">
              <template #default="scope">
                <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.$index,formData.ZYList)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>


      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()" v-if="editable">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogKHZYVisible"
        v-model="dialogKHZYVisible"
        title="选择考核专业"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <khzyChoose v-if="dialogKHZYVisible" :params="KHZYParams" @close="dialogKHZYVisible=false" @submit="getKhzyRes"/>
      </div>
    </el-dialog>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogPJMBVisible"
        v-model="dialogPJMBVisible"
        title="选择评价模板"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <pjmbChoose v-if="dialogPJMBVisible" :params="PJMBParams" @close="dialogPJMBVisible=false" @submit="getPjmbRes"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import {ElMessage} from "element-plus";
import khzyChoose from "@views/khpj/khzymbgl/khzyChoose";
import pjmbChoose from "@views/khpj/khzymbgl/pjmbChoose";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";


export default defineComponent({
  name: '',
  components: {khzyChoose,pjmbChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      formData: {
        MBList: [],
        ZYList: []
      },


      dialogKHZYVisible: false,
      KHZYParams: {},


      dialogPJMBVisible: false,
      PJMBParams: {},
    })


    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const submitForm = () => {
      let params={
        GLGXList: []
      }
      state.formData.MBList.forEach(item=>{
        state.formData.ZYList.forEach(ii=>{
          params.GLGXList.push({
            PJMBID: item.PJMBID,
            PJZYID: ii.PJZYID,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
            SYDWID: 'SZSY'
          })
        })
      })
      axiosUtil.post('/backend/sckhpj/zymbgl/saveZymbglgx',params).then(res=>{
        ElMessage.success('保存成功')
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        if(state.formData.MBList.length===0){
          ElMessage.error('请选择模板')
          resolve(false)
          return
        }
       if(state.formData.ZYList.length===0){
         ElMessage.error('请选择专业')
         resolve(false)
         return
       }
       // if(state.formData.MBList.length>1){
       //   ElMessage.error('只能选择一个模板')
       //   resolve(false)
       //   return
       // }
        resolve(true)
      })
    }

    const deleteRow = (index,table) => {
      table.splice(index,1)
    }

    const closeForm = () => {
      emit('close')
    }

    const getKhzyRes = (value) => {
      value.forEach(item=>{
        if(!state.formData.ZYList.find(ii=>ii.PJZYID===item.PJZYID)){
          state.formData.ZYList.push(item)
        }
      })
      state.dialogKHZYVisible=false
    }

    const getPjmbRes = (value) => {
      value.forEach(item=>{
        if(!state.formData.MBList.find(ii=>ii.PJMBID===item.PJMBID)){
          state.formData.MBList.push(item)
        }
      })
      state.dialogPJMBVisible=false
    }

    onMounted(() => {
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm,
      getKhzyRes,
      getPjmbRes,
      deleteRow

    }
  }

})
</script>

<style scoped>

</style>
