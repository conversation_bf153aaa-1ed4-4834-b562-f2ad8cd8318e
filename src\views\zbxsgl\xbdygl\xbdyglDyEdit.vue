<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="回复标题：" prop="HFBT">
            <el-input v-model="formData.HFBT" type="text" placeholder="请输入" clearable :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="回复内容：" prop="HFNR">
            <el-input v-model="formData.HFNR" :rows="12"
                      type="textarea" clearable
                      show-word-limit :maxlength="2000"
                      placeholder="请输入"/>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item label="附件上传：" prop="PBDD">
            <vsfileupload v-if="params.type === 'edit'" ref="vsfileupload" style="margin-left: 10px;min-width: 200px" :busId="params.dyid"
                :key="params.dyid" v-model:files="fileList"
                :editable="params.editable" ywlb="xbdyglfj" busType="xbdyglfj"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
     <div style="width: 100%;margin-bottom: 10px;justify-content: center;display: flex">
        <el-button v-if="params.type === 'edit'" type="primary" @click="saveData">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import comFun from "@lib/comFun";
import vsAuth from "@lib/vsAuth";
export default defineComponent({
  name: '',
  components: {axiosUtil,vsfileupload},
  props: {
      params: {
          type: Object,
          default: {},
      },
  },
  setup(props, {emit}) {
    const state = reactive({
        editable: props.params.editable,
        formData: {
            HFBT: '',
            HFNR: '',
            FAID: props.params.faid,
            DYID: props.params.dyid,
            WTID: props.params.wtid,
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        },
        fileList: [],
    })

    const queryDygd = (() =>{
        axiosUtil.get('/backend/xsgl/xbdygl/queryXbDylbList', props.params).then((res) => {
            if(!!res.data.list){
              state.formData = res.data.list[0];
            }
        });
    })
   
   const closeForm = (() =>{
       emit('closeDyForm',false);
   })

   //修改状态
   const saveData = (() =>{
        axiosUtil.post('/backend/xsgl/xbdygl/saveDyxxData', state.formData).then((res) => {
            if (res.code === 1) {
                emit('closeDyForm',true);
                ElMessage.success("保存成功");
            }
        }).catch((err) =>{
            ElMessage.error("保存失败");
        });
   })

    onMounted(() => {
        queryDygd();
    })

    return {
        ...toRefs(state),
        queryDygd,
        closeForm,
        saveData,
    }
  }

})
</script>

<style scoped>
</style>
