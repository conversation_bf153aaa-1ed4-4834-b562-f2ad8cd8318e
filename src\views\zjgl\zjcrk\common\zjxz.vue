<template>
  <el-form :model="listQuery" ref="vForm" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="" prop="name">
          <el-input v-model="listQuery.XM" type="text" placeholder="请输入姓名" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell" style="display: flex;gap: 10px">
        <div class="ml10 static-content-item">
          <el-button type="primary" @click="getDataList"><el-icon><Search/></el-icon>查询</el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="success" @click="submit">确定</el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="warning" @click="clear">清空</el-button>
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table ref="dataTable" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column type="index" width="60" fixed="left">
          <template #default="{$index,row}">
            <el-checkbox-group v-model="checkList">
              <el-checkbox :label="row" :disabled="fitterList.includes(row.ZJBS)">{{(listQuery.page-1)*listQuery.size+$index+1}}</el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>
        <el-table-column v-if="true" prop="XM" label="姓名" :fixed="false" align="center" width="100"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.XM }}</span></template>
        </el-table-column>
        <el-table-column v-if="true" prop="GZDW" label="工作单位" :fixed="false" align="left"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.GZDW }}</span></template>
        </el-table-column>
        <el-table-column v-if="true" prop="ZJLBMC" label="专家类别" :fixed="false" align="center"
                         :show-overflow-tooltip="true" width="100">
        </el-table-column>
        <el-table-column v-if="true" prop="ZJZY" label="专业" :fixed="false" align="left"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column v-if="true" prop="RKZJBXX.BGDH" label="手机号" :fixed="false" align="center" width="180"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.RKZJBXX?.BGDH }}</span></template>
        </el-table-column>
      </el-table>
      <el-pagination background
                     v-model:current-page="listQuery.page"
                     v-model:page-size="listQuery.size" :page-sizes="[10,20,50,100]"
                     layout="total, prev, pager, next, sizes"
                     class="lui-pagination"
                     @size-change="pageOrSizeChange"
                     @current-change="pageOrSizeChange"
                     :total="total">
      </el-pagination>
    </div>
  </el-form>

</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
}
  from 'vue'

import axiosUtil from "../../../../lib/axiosUtil";
import { Search, Upload, Plus} from '@element-plus/icons-vue'

export default defineComponent({
  components: {Search,Plus,Upload},
  props: {
    fitterList:{
      type:Array,
      default: []
    }
  },
  setup(props,{emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      checkList:[],
      rules: {},
      total:0,
    })
    const getDataList = () => {
      let params={...state.listQuery}
      axiosUtil.get(`/backend/zjgl/pwcqgl/queryZj?page=${state.listQuery.page}&size=${state.listQuery.size}`, params).then((res) => {
        state.tableData=res.data.list
        state.total = res.data.total
      });
    }
    const pageOrSizeChange = () => {
      getDataList()
    }
    const submit = () => {
      console.log(state.checkList)
      emit('getData',state.checkList)
    }

    const clear = () => {
      emit('clearData')
    }



    onMounted(()=>{
      getDataList()
    })
    return {
      ...toRefs(state),
      getDataList,
      pageOrSizeChange,
      submit,
      clear
    }
  }
})

</script>

<style scoped>
.grid-cell .el-input{
  max-width: 250px;
}
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: white;
}
</style>
