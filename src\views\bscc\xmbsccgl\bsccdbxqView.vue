<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="200px"
             size="default" v-loading="loading" @submit.prevent>
      <div class="title-text">标书查重对比详情</div>


      <div class="label-text">
        <div style="flex: 1">原文件（A）：{{formData.AWJMC}}</div>
        <div style="flex: 1">对比文件（B）：{{formData.BWJMC}}</div>
      </div>



      <el-tabs v-model="ActiveTab" type="border-card">
        <el-tab-pane :name="index+''"
                     v-for="(item,index) in CCFWOptions">
          <template #label>
            {{item.DMMC}}（<span :style="`${formData[item.DMXX+'List']?.length>0 ? 'color: red' : ''}`">
            {{formData[item.DMXX+'List']?.length || 0}}
          </span>）
          </template>
          <dbxqTab :params="item" v-model="formData"/>
        </el-tab-pane>
      </el-tabs>

    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import dbxqTab from "@views/bscc/xmbsccgl/dbxqTab";

export default defineComponent({
  name: '',
  components: {dbxqTab},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      CCQDID: props.params.id,
      formData: {

      },
      CCFWOptions: [],
    })

    const getFormData = () => {
      let params={
        CCQDID: state.CCQDID
      }
      state.loading=true
      axiosUtil.get('/backend/bscc/bsccgl/selectCcjgxqById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
        state.loading=false
      })
    }

    onMounted(() => {
      getFormData()
      getDMBData('CCFW', 'CCFWOptions')
    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>
.title-text {
  font-size: 20px;
  margin-bottom: 20px;
  width: 100%;
  text-align: center;
  color: #980404;
}
.label-text{
  display: flex;
  font-family: 黑体;
  color: black;
  padding: 20px;

}
</style>
