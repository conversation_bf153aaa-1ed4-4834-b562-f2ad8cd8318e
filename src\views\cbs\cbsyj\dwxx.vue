<!-- 队伍信息 -->
<template>
  <div style="height: calc(100% - 40px);">
    <el-form style="height: 100%;" size="default" class="lui-page" >
     <div style="display: flex;justify-content: space-between;margin-bottom: 20px" v-if="editable">
          <span>提示：请录入本次新申请队伍信息</span>
          <el-button class="lui-button-add" type="primary" @click="handleAdd">
            <el-icon><Plus/></el-icon>增加
          </el-button>
     </div>
      <el-table :data="data.tableData" height="calc(100% - 80px)" border size="default" class="lui-table">
        <el-table-column label="队伍名称" prop="unitName" width="200" show-overflow-tooltip header-align="center"
                         align=""></el-table-column>
        <el-table-column label="队伍编码" prop="dwbm" width="200" header-align="center"
                         align=""></el-table-column>
        <el-table-column label="申请服务范围" show-overflow-tooltip prop="specialName"
                         header-align="center" align=""></el-table-column>
        <el-table-column v-for="(item,index) in data.templates" :key="index" :label="item.SJMBMC"
                         :prop="item.SJMBBM" header-align="center" align="center" width="100">
          <template v-slot="scope">
            <div v-if="scope.row.templates.filter(x=>x.SJMBBM == item.SJMBBM).length > 0">
                <el-button type="success" v-if="scope.row.EXTENSION && JSON.parse(scope.row.EXTENSION).SFWCJY === '1' && checkRyList(scope.row.checkRyList,item.SJMBBM).checked" :icon="Check" circle
                        style="width: 30px;height: 30px"/>
                <el-button type="danger" v-else :icon="Close" circle
                        style="width: 30px;height: 30px"/>
            </div>
            <div v-else></div>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" align="center" fixed="right" width="150">
          <template v-slot="scope">
            <div>
              <el-button v-if="editable" class="lui-table-button" @click="goWrite(scope.row)">录入信息</el-button>
              <el-button v-if="editable" class="lui-table-button" @click="handleDeleteRow(scope.row, scope.$index)">删除</el-button>
              <el-button v-if="!editable" class="lui-table-button" @click="handleViewRow(scope.row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <el-dialog title="添加队伍" z-index="99" v-model="chooseChildren" top="10vh"
               width="1200px" append-to-body destroy-on-close
              :close-on-click-modal="false" @close="handleCloseDialog"
               custom-class="lui-dialog" v-if="chooseChildren">
      <chooseZjdw :zyOptions="data.zyOptions" @sure="saveTeam"/>
    </el-dialog>



<!--    <el-dialog :title="dialogTitle" z-index="99" v-model="chooseChildren" top="10vh" width="1200px" append-to-body destroy-on-close-->
<!--               :close-on-click-modal="false" @close="handleCloseDialog" custom-class="lui-dialog" v-if="chooseChildren">-->
<!--      <el-row style="height: 70vh;" v-if="data.step == 1">-->
<!--        <childrenUnit-->
<!--            v-model:dialogTitle="dialogTitle"-->
<!--            @goEdit="goEdit"-->
<!--            @sure="handleSure"-->
<!--            :tableData="data.childrenCompanys"-->
<!--            :selected="data.chlidrens"-->
<!--            @return="handleReturn">-->
<!--        </childrenUnit>-->
<!--      </el-row>-->
<!--      <el-row style="height: 70vh;" v-if="data.step == 1.1">-->
<!--        <zjdwgl-->
<!--            v-model:dialogTitle="dialogTitle"-->
<!--            @goChildrenBack="goChildrenBack"-->
<!--                @queryChildrenCompanys="queryChildrenCompanys"-->
<!--                :selected="data.chlidrens"-->
<!--                returnFlag="1"-->
<!--        >-->
<!--        </zjdwgl>-->
<!--      </el-row>-->
<!--      <el-row style="height: 70vh;" v-if="data.step == 2">-->
<!--        <addTeam-->
<!--            v-model:dialogTitle="dialogTitle"-->
<!--            @sure="saveTeam"-->
<!--                 @goChildrenBack="goChildrenBack"-->
<!--                 :teams="data.chlidrens"-->
<!--                 @chooseSpeciality="chooseSpeciality">-->
<!--        </addTeam>-->
<!--      </el-row>-->
<!--      <el-row style="height: 70vh;" v-else-if="data.step == 3">-->
<!--        <xzzyjqy-->
<!--            v-model:dialogTitle="dialogTitle"-->
<!--            :specialbk="data.currentTeam.specialbk"-->
<!--                 :specialities="data.currentTeam.specials"-->
<!--                 :zyOptions="data.zyOptions"-->
<!--                 @chooseSpeciality="chooseSpeciality"-->
<!--                 @goTeamBack="goTeamBack"-->
<!--                 @submitSpeciality="submitSpeciality"></xzzyjqy>-->
<!--      </el-row>-->
<!--    </el-dialog>-->
    <el-dialog z-index="99" v-model="editDwxxDialog" fullscreen  append-to-body destroy-on-close
               :close-on-click-modal="false" @close="handleCloseDialog" custom-class="lui-dialog">
      <yrsqxxIndex :key="data.chooseDWYWID" :DWYWID="data.chooseDWYWID" :YWLXDM="YWLXDM"></yrsqxxIndex>
    </el-dialog>
  </div>
</template>

<script setup>
import {defineProps, onMounted, onUnmounted, reactive, ref, watch} from "vue"
import {ElMessage, ElMessageBox} from 'element-plus'
import addTeam from './addTeam.vue'
import childrenUnit from './childrenUnit.vue'
import xzzyjqy from './xzzyjqy.vue'
import zjdwgl from './zjdwgl.vue'
import tabFun from "@src/lib/tabFun";
import comFun from "@src/lib/comFun";
import yrsqxxIndex from './yrsqxxIndex.vue'//队伍信息编辑页面
import chooseZjdw from "@views/cbs/cbsyj/chooseZjdw";
import {useRouter} from 'vue-router'
import {runtimeCfg,eventBus, mixin} from '@src/assets/core/index';
import {
    getCbsyjGetTeamSpecials,
    getCbsyjTeamlist,
    getZjdwglQuery,
    postCbsyjSaveTeam,
    deleteCbsyjTeamdelete,
    getCbsyjTempletequerys,
    getCbsyjGetCbsTeamById,
    getCheckIsFirstZr
} from '@src/api/sccbsgl.js'
import {Check, Close} from '@element-plus/icons-vue'
import {getCommonGetUserByParam} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import {v4 as uuidv4} from "uuid";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
const props = defineProps({
    DWYWID: {
        type: String,
        defaultData: () => '',
    },
    TYXYDM: {//统一信用代码
        type: String,
    },
    YWLXDM: {
        type: String,
        defaultData: () => '',
    },
    row: {
        type: Object,
        defaultData: () => {},
    },
  editable: {
    type: String,
    defaultData: () => false,
  },
  LSZRLX: {
    type: String,
    default: "",
  },
});
watch(
    () => props.DWYWID,
    (val) => {
        queryTableData()
    },
);

const {vsuiEventbus} = mixin()
const getUserInfo = () => {
    let user = VSAuth.getAuthInfo().permission;
    console.log('user',user);
    getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
        data.currentUser = res.data;
    })
}

const getCbsTeamById = () => {
    getCbsyjGetCbsTeamById({DWYWID: props.DWYWID}).then(res => {
        data.cbsdwxx = res.data;
    })
}
const dialogTitle = ref('123')
//==============================================================================================================================================================================
//childrenUnit  子级单位选择组件
const goEdit = () => {
    // chooseChildren.value = false;
    data.step = 1.1

    // tabFun.addTabByRoutePath('子级单位管理','/mainData/zjdwgl',{},{});
}

/**确定 */
const handleSure = (res) => {
    console.log(res)
    data.chlidrens = res;
    data.step = 2;
    // chooseChildren.value = false
}

/**返回 */
const handleReturn = () => {
    chooseChildren.value = false
}
//==============================================================================================================================================================================
//子级单位维护
const queryChildrenCompanys = () => {
    let dwmcs = data.tableData.map(x => x.DWMC).toString()
    getZjdwglQuery({dwmcs: dwmcs,orgid: data.currentUser.ORGNA_ID}).then(res => {
        data.childrenCompanys = res.data.map(x => {
            return {
                unitName: x.DWMC,
                dwbm: x.DWBM,
                id: x.DWWYBS,
                zsjid: x.DWWYBS,
                specials: [],
                specialbk: [],
                ...x
            }
        })
    })
}

const goChildrenBack = (res) => {
    data.step = 1;
}
//==============================================================================================================================================================================
// 新增队伍
const chooseSpeciality = (res) => {
    data.currentTeam = res.current;
    data.step = res.step;
}

const saveTeam = (res) => {
    chooseChildren.value = false;
    res.forEach(x => {
        x.CJRXM = data.currentUser.USER_NAME;
        x.CJRZH = data.currentUser.USER_LOGINNAME;
        x.CJDWID = data.currentUser.ORGNA_ID
        x.CJSJ = comFun.getNowTime();
        x.CBSYWID = data.cbsdwxx.CBSYWID;
    })
    data.saveTeam = res.map(x => {
        x.specials.forEach(v => {
            v.CJRXM = data.currentUser.USER_NAME;
            v.CJRZH = data.currentUser.USER_LOGINNAME;
            v.CJDWID = data.currentUser.ORGNA_ID;
            v.CJSJ = comFun.getNowTime();
            v.STATUS = 'ZC'
            v.SHZT = '0'
            v.FWQYID = uuidv4().replace(/-/g, ''),
                v.SHZT = '0'
            v.SHZT = '0'
            v.FWQYBM = v.fwid
            v.FWQYMC = v.fwqy
        });
        // 如果是C类队伍，把选中的区域赋值到 推荐单位
        let tjdw = '';
        if(data.cbsdwxx.DWLB === 'C') {
            tjdw = x.specials[0].fwid
        }
        return {
            DWYWID: uuidv4().replace(/-/g, ''),
            DWWYBS: x.zsjid,
            CJRXM: data.currentUser.USER_NAME,
            CJRZH: data.currentUser.USER_LOGINNAME,
            CJDWID: data.currentUser.ORGNA_ID,
            DWMC: x.DWMC,
            DWBM: x.DWBM,
            ZYLB: x.specials,
            DWLX: 'DW',
            DWZT: '1',
            SHZT: '0',
            YWLXDM: props.YWLXDM,
            zsjid: x.zsjid,
            CBSYWID: x.CBSYWID,
            TYXYDM: props.TYXYDM,
          DWLB: data.cbsdwxx.DWLB,
            TJDW: tjdw,
        }
    })
    postCbsyjSaveTeam(data.saveTeam).then(r => {
        if (r.message == 'success') {
            queryTableData();
            data.step = 1;
        }
    })
}

//==============================================================================================================================================================================
//选择专业及区域
const goTeamBack = () => {
    data.step = 2;
    data.currentTeam.specials = data.currentTeam.specialbk;
}

const getTeamSpeciality = () => {
    getCbsyjGetTeamSpecials({dwid: data.DWYWID}).then(r => {
        data.zyOptions = r.data.map(x => {
            return {
                label: x.ZYMC,
                value: x.ZYBM,
                ...x,
            }
        });
        getCbsyjTempletequerys({mbids: r.data.map(x => x.ZRMBID).toString()}).then(res => {
            data.templates = res.data
        })
    })
}

const submitSpeciality = (res) => {
    //	[ { "zymc": "信息系统技术服务", "fwqy": "区域1" }, { "zymc": "数据技术服务", "fwqy": "区域2" } ]
    let r = JSON.parse(JSON.stringify(res));
    console.log('res', r)
    let strs = r.map(x => `${x.zymc}，${x.fwqy}`)
    let re = ''
    strs.forEach(x => re += `${x}；`)
    data.currentTeam.specials = r;
    data.currentTeam.specialbk = r;
    data.currentTeam.specialName = re;
    data.step = 2;
}


const router = useRouter()

let data = reactive({
    chooseDWYWID:'',//所选择的队伍业务ID
    // todo
    DWYWID: props.DWYWID,
    cbsdwxx: {},
    childrenCompanys: [],
    step: 1,
    chlidrens: [],
    currentTeam: {},
    saveTeam: {},
    tableData: [],
    zyOptions: [],
    currentUser: {},
    templates: []
})


let chooseChildren = ref(false)
//编辑队伍信息
let editDwxxDialog = ref(false)

const goWrite = (row) => {
    tabFun.addTabByRoutePath('队伍基本信息', '/contractors/yrsqxxIndex', {DWYWID: row.DWYWID,YWLXDM: props.YWLXDM,editable:true,LSZRLX:props.LSZRLX}, );
    //data.chooseDWYWID=row.DWYWID
    //editDwxxDialog.value=true;
    // router.push("/contractors/yrsqxx")
}

const handleViewRow = (row) => {
  tabFun.addTabByRoutePath('队伍基本信息', '/contractors/yrsqxxIndex', {DWYWID: row.DWYWID,YWLXDM: props.YWLXDM,editable:false}, );
  //data.chooseDWYWID=row.DWYWID
  //editDwxxDialog.value=true;
  // router.push("/contractors/yrsqxx")
}

/**增加 */
const handleAdd = () => {
    chooseChildren.value = true
}
/**关闭弹框 */
const handleCloseDialog = () => {
    data.step = 1;
    chooseChildren.value = false
}
/**查询表格数据 */
const queryTableData = () => {
    getCbsyjTeamlist({DWYWID: props.DWYWID}).then(res => {
        res.data.forEach(x => {
            x.unitName = x.DWMC;
            x.specialName = x.SQFWFW;
            x.dwbm = x.DWBM
        })
        data.tableData = res.data
        console.log('4654654', data.tableData)
        queryChildrenCompanys();
    })

    // // TODO 查询表格数据
    // data.tableData.length = 0
    // data.tableData.push({})
}
/**录入信息 */

/**删除 */
const handleDeleteRow = (val, index) => {
    ElMessageBox.confirm('是否删除此条数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        deleteCbsyjTeamdelete({dwid: val.DWYWID}).then(res => {
            ElMessage({
                message: '删除成功!',
                type: 'success'
            })
            queryTableData();
        })

    }).catch(() => {

    })
}
const timer = ref(null);

const validateForm = () => {
  if(!data.tableData || data.tableData.length===0){
    return Promise.reject({mgs:[{message:'至少添加一支队伍'}]})
  }
  if(data.tableData.find(item=>item.templates.find(i=>!item[i.SJMBBM]))){
    return Promise.reject({mgs:[{message:'请完成队伍信息'}]})
  }else {
    for(let i=0;i<data.tableData.length;i++){
        if(JSON.parse(data.tableData[i].EXTENSION).SFWCJY === '0'){
            return Promise.reject({mgs:[{message:data.tableData[i].DWMC + '数据未提交，请先提交队伍信息'}]})
        }
        let map = checkRyList(data.tableData[i].checkRyList,'RYXX');
        if(!map.checked){
            return Promise.reject({mgs:[{message:data.tableData[i].DWMC + map.zymc  +'专业人员数不得少于最低定员数' + map.zddys}]})
        }
    }
    return Promise.resolve(true)
  }
}

// 校验每个专业的人员数量是否满足最低定员数要求
const checkRyList = (list,SJMBBM) => {
    if(SJMBBM === 'RYXX'){
        for(let i=0;i<list.length;i++){
            if(list[i].ZDDYS && Number(list[i].ZDDYS) > Number(list[i].SJSL)){
                return { checked: false, zymc: list[i].ZYMC, zddys: list[i].ZDDYS};
            }
        }
    }
    return { checked: true };
}


defineExpose({
  validateForm,
});

// 判断是否为首次准入
let isFirstZr = ref(true);
const checkIsFirstZr = () => {
    getCheckIsFirstZr({
        tyxydm: props.TYXYDM,
    })
        .then(({ data }) => {
            isFirstZr.value = data.rows.length == 0;
        })
        .catch((err) => {
            ElMessage.error("查询失败");
        });
};

onMounted(() => {
    // 判断是否为首次准入
    checkIsFirstZr();

    getCbsTeamById();
    let user = VSAuth.getAuthInfo().permission;
    console.log('user',user);
    getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
        data.currentUser = res.data;
        queryTableData()
    })
    getTeamSpeciality();
    vsuiEventbus.on('reloadTableData',queryTableData);
    // queryChildrenCompanys();

})
onUnmounted(()=>{
    // timer.value = null;
    vsuiEventbus.off('reloadTableData',queryTableData);
})

</script>

<style scoped>
.el-container{
    height: 100%;
}
.el-header {
    display: flex;
    align-items: center;
    color: #81838f;
    justify-content: space-between;
}

>>> .el-dialog__header {
    height: 25px !important;
}

</style>
