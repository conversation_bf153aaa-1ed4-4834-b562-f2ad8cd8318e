<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="140px"
           size="default" @submit.prevent>
    <el-row :gutter="0" class="grid-row">
<!--      <el-col :span="24" class="grid-cell">-->
<!--        <el-form-item label="审核结果" prop="SHJG">-->
<!--          <el-radio-group v-model="formData.SHJG">-->
<!--            <el-radio label="1">通过</el-radio>-->
<!--            <el-radio label="0">退回</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
<!--      </el-col>-->
      <el-col :span="24" class="grid-cell">
        <el-form-item label="审核意见" prop="SHYJ">
          <el-input v-model="formData.SHYJ" :rows="4"
                    type="textarea" clearable
                    show-word-limit :maxlength="100"
                    placeholder="请输入"/>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0">
      <div style="width: 100%;text-align: center;margin-top: 10px">
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button @click="closeSH">取消</el-button>
      </div>
    </el-row>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    apprValue:{
      type: String,
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      formData:{
        SHJG:'',
        SHYJ:'',
      },
      rules: {
        SHYJ: [{
          required: true,
          message: '审核意见不可为空',
        }],
        SHJG: [{
          required: true,
          message: '字段值不可为空',
        }],
      }
    })

    const closeSH = () => {
      emit('closeSH')
    }
    
    const validateData = () => {
      return new Promise((resolve, reject) => {
        instance.proxy.$refs.vForm.validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            reject('请完善页面信息')
          }
        })
      })
    }

    const instance = getCurrentInstance()
    const submit = () => {
      validateData().then(res=>{
        emit('confirm',state.formData,null)
        closeSH()
      }).catch(msg=>{
        ElMessage.error(msg)
      })
    }
    onMounted(() => {
      state.formData.SHJG=props.apprValue
      if(props.apprValue==='1'){
        state.formData.SHYJ='同意'
      }
      emit('changeTitle',props.apprValue==='1' ? '通过意见' : '驳回意见')
    })

    return {
      ...toRefs(state),
      submit,
      closeSH

    }
  }

})
</script>

<style scoped>

</style>
