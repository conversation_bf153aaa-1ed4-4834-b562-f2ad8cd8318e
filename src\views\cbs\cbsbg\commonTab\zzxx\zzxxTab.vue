<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        :model="tableData"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        class="lui-page"
        :disabled="!editable">
<!--      <div style="color: red">-->
<!--        {{ BGXX }}-->
<!--      </div>-->
      <el-table ref="dataTable" :data="[...tableData,...getDelRow()]" height="500px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column v-for="(item,index) in tableColumn" :key="index"
                         :prop="item.prop" :label="item.label" :align="item.align" :width="item.width"
                         :min-width="item.minWidth"
                         :show-overflow-tooltip="item.showOverflowTooltip">
          <template #default="{row,$index}" v-if="item.slot==='fileList'">
            <vsfileupload
                :maxSize="10"
                :index="$index"
                :ref="addRefs($index)"
                :editable="false"
                :busId="row.ZSYWID"
                :key="row.ZSYWID"
                ywlb="DWZTBGFJ"
                busType="dwxx"
                :limit="100"
            ></vsfileupload>
          </template>
          <template #default="{row,$index}" v-else-if="item.slot==='format'">
            {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='change'">
            <div :class="{dataChange : isChangeT(row.ZSWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.ZSWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.ZSWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span>{{ row[item.prop] }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='formatChange'">
            <div :class="{dataChange : isChangeT(row.ZSWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.ZSWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.ZSWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span> {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='info'">
            <el-tooltip :content="row[item.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="CZ" label="操作" align="center" width="250" fixed="right" v-if="editable">
          <template #default="{row,$index}">
            <div v-if="tableRowClassName({row})!=='warning-row'">
              <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button class="lui-table-button" @click="deleteRow(row, $index)">删除</el-button>
            </div>
            <div v-else>
              已删除
            </div>
          </template>
        </el-table-column>


      </el-table>

    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        title="信息项选择"
        v-model="chooseVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        top="50px"
        width="1200px">
      <zzxxXz
          :key="editIndex"
          :currentRow="currentRow"
          @updateChooseData="updateChooseData"
          @updateEditData="updateEditData"
          @close="chooseVisible = false"
          :TYXYDM="TYXYDM"
      />
    </el-dialog>


  </div>
</template>

<script>

import {defineComponent, onMounted, reactive, ref, toRefs, watch} from "vue";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import zzxxXz from "@views/cbs/templateManagement/DataTemplateManagement/zzxx/zzxx_xz.vue"
import vsfileupload from "@views/components/vsfileupload";
import {InfoFilled} from "@element-plus/icons-vue";


export default defineComponent({
  name: '',
  components: {zzxxXz, vsfileupload, InfoFilled},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    TYXYDM: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      chooseVisible: false,
      tableData: [],
      tableColumn: [
        {
          label: "专业名称",
          prop: "ZYMC",
          align: "center",
          showOverflowTooltip: true,
          width: 100,
          // slot: "select",
        },
        {
          label: "业务要求",
          prop: "YWYQ",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "录入资料说明",
          prop: "LRZLSM",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "信息项",
          prop: "XXXMC",
          align: "center",
          showOverflowTooltip: true,
          width: 150,
        },
        {
          label: "资质信息名称",
          prop: "ZSMC",
          align: "center",
          minWidth: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
          required: true,
          maxlength: 128,
        },
        {
          label: "证书编号",
          prop: "ZSBH",
          align: "center",
          minWidth: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
          // slot: "input",
          required: true,
          maxlength: 64,
        },
        {
          label: "资质等级",
          prop: "ZSDJ",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
          // slot: "input",
          maxlength: 40,
        },
        {
          label: "证书到期日期",
          prop: "YXQJS",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'formatChange' : 'format',
        },
        {
          label: "附件",
          prop: "fileList",
          headerAlign: "center",
          align: "left",
          showOverflowTooltip: true,
          width: 150,
          slot: "fileList",
        },
      ],
    })


    watch(() => props.defaultData, (val) => {
      if (val) {
        val.forEach((x) => {
          const UUID = comFun.newId()
          x.ZSYWID = x.ZSYWID || UUID
          x.ZSWYBS = x.ZSWYBS || UUID
        })
      }
      state.tableData = val
    }, {immediate: true})


    const copyRow = (row, index) => {
      const UUID = comFun.newId()
      state.tableData.splice(index, 0, {...row, ZSYWID: UUID, ZSWYBS: UUID, SHZT: ''})
    }

    const insertRow = (row, index) => {
      const UUID = comFun.newId()
      state.tableData.splice(index + 1, 0, {
        ZSYWID: UUID,
        ZSWYBS: UUID,
        ZYMC: row.ZYMC,
        ZYBM: row.ZYBM,
        XXXMC: row.XXXMC,
        MBMXID: row.MBMXID,
        YWYQ: row.YWYQ,
        LRZLSM: row.LRZLSM,
        XXX: "",
        ZSMC: "",
        ZSDJ: "",
        YXQKS: null,
        YXQJS: null,
        FZBM: "",
        FJ: "",
      })
    }

    const currentRow = ref({})
    const editIndex = ref(0)


    const updateChooseData = (val) => {
      changeData(currentRow.value, val, editIndex.value, false)
    }
    const changeData = (oldRow, newRow, index, visible) => {
      let params = {
        newId: oldRow.ZSYWID,
        oldId: newRow.ZSZSJID,
        cover: true
      }

      axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
        oldRow.ZSZSJID = newRow.ZSZSJID
        oldRow.ZSMC = newRow.ZSMC
        oldRow.ZSBH = newRow.ZSBH
        oldRow.ZSDJ = newRow.ZSDJ
        oldRow.YXQJS = newRow.YXQJS
        refs.value[index].loadFileList()
        state.chooseVisible = visible
      })
    }

    watch(() => state.tableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])
    const initBgInfo = () => {
      if (state.tableData && props.resultTableData) {
        let res = []
        let BGQBS = props.resultTableData.map(i => i.ZSWYBS)
        let BGHBS = state.tableData.map(i => i.ZSWYBS)
        let allBS = new Set([...BGQBS, ...BGHBS])
        allBS.forEach(item => {
          if (BGQBS.includes(item) && BGHBS.includes(item)) {
            let BGQXX = props.resultTableData.find(i => i.ZSWYBS === item)
            let BGHXX = state.tableData.find(i => i.ZSWYBS === item)
            let isBg = false
            let dbsj = []
            let checkProp = ['ZSMC', 'ZSBH', 'ZSDJ', 'YXQJS']
            checkProp.forEach(ii => {
              if ((BGQXX[ii] || '') !== (BGHXX[ii] || '')) {
                dbsj.push({
                  BGQ: BGQXX[ii] || '',
                  BGH: BGHXX[ii] || '',
                  ZDMC: ii
                })
                isBg = true

              }
            })
            if (isBg) {
              res.push({
                YWLX: 'ZZXX',
                BGZT: 'BG',
                WYBS: item,
                BGXQ: dbsj
              })
            }

          } else if (BGQBS.includes(item) && !BGHBS.includes(item)) {
            res.push({
              YWLX: 'ZZXX',
              BGZT: 'SC',
              WYBS: item,
            })
          } else if (!BGQBS.includes(item) && BGHBS.includes(item)) {
            res.push({
              YWLX: 'ZZXX',
              BGZT: 'XZ',
              WYBS: item,
            })
          }
        })
        BGXX.value = res
      }

    }

    const getDelRow = () => {
      if(props.resultTableData && state.tableData){
        return props.resultTableData.filter(item => !state.tableData.find(ii => ii.ZSWYBS === item.ZSWYBS))
      }else {
        return []
      }
    }

    const tableRowClassName = ({row,index}) => {
      let info=BGXX.value.find(ii=>ii.WYBS===row.ZSWYBS) || {}
      if (info.BGZT==='XZ'){
        return "success-row"
      }else if(info.BGZT==='SC'){
        return "warning-row"
      }

    }

    const isChangeT = (ZSWYBS, prop) => {
      let info = BGXX.value.find(item => item.WYBS === ZSWYBS && item.BGZT === 'BG')
      if (info) {
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if(res){
          res.BGMS = `${res.BGQ} 变更为 ${res.BGH}`
          return res
        }
      }
      return null
    }

    const updateEditData = (row) => {
      state.tableData.forEach((item, index) => {
        if (item.ZSZSJID === row.ZSZSJID) {
          changeData(item, row, index, true)
        }
      })
    }

    const chooseRow = (row, index) => {
      currentRow.value = row
      editIndex.value = index
      state.chooseVisible = true
    }

    const deleteRow = (row, index) => {
      ElMessageBox.confirm("是否删除此条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        state.tableData.splice(index, 1);
        ElMessage({
          message: "删除成功!",
          type: "success",
        });
      }).catch(() => {
      })
    }

    const validateForm = () => {
      return new Promise((resolve, reject) => {
        if (state.tableData.find(item => !item.ZSZSJID)) {
          reject({mgs: [{message: '请完成资质信息'}]})
        } else {
          resolve(true)
        }
      })
    }

    const refs = ref([]);
    const addRefs = (id) => {
      return (el) => {
        refs.value[id] = el;
      }
    }



    onMounted(() => {

    })

    return {
      ...toRefs(state),
      validateForm,
      editIndex,
      currentRow,
      updateChooseData,
      updateEditData,
      addRefs,
      chooseRow,
      insertRow,
      deleteRow,
      BGXX,
      isChangeT,
      getDelRow,
      tableRowClassName
    }
  }

})
</script>

<style scoped>

</style>
