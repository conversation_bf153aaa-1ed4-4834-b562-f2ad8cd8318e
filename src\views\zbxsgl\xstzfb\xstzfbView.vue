<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="width: 100%;text-align: center">
        <h2>{{ formData.GGMC }}</h2>
        <h4>发布日期：{{ formData.CJSJ }}</h4>
      </div>

      <el-row :gutter="0" class="grid-row">
        <el-col :span="14" class="grid-cell" :offset="2">
          <h3 style="margin-left: 10px">招标项目名称：{{ formData.XMXX.XMMC }}</h3>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <h3 style="margin-left: 10px">招标项目编号：{{ formData.XMXX.XMBH }}</h3>
        </el-col>

      </el-row>
      <div style="padding: 20px 100px;min-height: 300px;overflow: auto">
        <div class="editor-content-view" v-html="formData.GGNR"></div>
      </div>

      <div style="padding: 20px">
        <el-collapse v-model="openCollapse">
          <el-collapse-item title="其它信息" name="1">
            <el-row>
              <el-col :span="24" class="grid-cell no-border-bottom" style="display: flex">
                <h3 style="margin-left: 10px">公告文件：</h3>
                <vsfileupload
                    style="margin-left: 10px"
                    :editable="editable"
                    :busId="params.id"
                    :key="params.id"
                    ywlb="GGWJ"
                    busType="GGWJ"
                    :limit="100"
                ></vsfileupload>
              </el-col>

              <el-col :span="12" class="grid-cell no-border-bottom">
                <h3 style="margin-left: 10px">确认是否参加截止时间：{{ formData.QRSFCJJZSJ }}</h3>
              </el-col>

              <el-col :span="12" class="grid-cell no-border-bottom">
                <h3 style="margin-left: 10px">招标文件获取时间：{{ formData.ZBWJHQSJKS }} 至 {{ formData.ZBWJHQSJJS }}</h3>
              </el-col>

              <el-col :span="12" class="grid-cell no-border-bottom">
                <h3 style="margin-left: 10px">投标文件递交截止时间：{{ formData.TBWJDJJZSJ }}</h3>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>



      <div style="width: 100%;margin-top: 10px;justify-content: center;display: flex;margin-bottom: 10px">
        <el-button type="danger" @click="" disabled v-if="canBm && (new Date(formData.QRSFCJJZSJ)<new Date() || formData.HHSHZT==='2')">已截止</el-button>
        <el-button type="primary" @click="viewBmhh(formData.TZFS==='DXYQ' ? 'YQHH' : 'BMCY')" v-else-if="canBm && BMXX">已参与</el-button>
        <el-button type="success" @click="openBmhh('YQHH')" v-else-if="canBm && formData.TZFS==='DXYQ'">邀请回函</el-button>
<!--        <el-button type="success" @click="openBmhh('BMCY')" v-else-if="canBm">报名参与</el-button>-->
        <el-button @click="closeForm">返回</el-button>
      </div>


    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="title"
        @closed="dialogVisible=false"
        z-index="1000"
        top="5vh"
        width="1000px">
      <div>
        <bmhhEdit v-if="dialogVisible" :params="hhParams" @close="closeHH" @submit="closeMsg"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import bmhhEdit from "@views/zbxsgl/xstzfb/bmhhEdit";


export default defineComponent({
  name: '',
  components: { vsfileupload,bmhhEdit},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GGID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '0',

        XMXX: {},
      },
      rules: {},

      GGLXOptions: [],
      TZFSOptions: [],

      openCollapse: ['1'],

      dialogVisible: false,
      hhParams: {},
      title: '',

      canBm: false,
      BMXX: null

    })

    const getFormData = () => {
      let params = {
        GGID: state.GGID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/xstzfb/selectXstzById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      })
    }

    const openBmhh = (type) => {
      state.hhParams = {editable: true, id: comFun.newId(), operation: 'add',GGID: state.GGID, HHLX: type}
      state.title=type==='YQHH' ? '邀请回函' : '报名参与'
      state.dialogVisible = true
    }

    const viewBmhh = (type) => {
      state.hhParams = {editable: false, id: state.BMXX.QRHHID, operation: 'view', HHLX: type}
      state.title=type==='YQHH' ? '回函查看' : '报名查看'
      state.dialogVisible = true
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const closeMsg = () => {
      emit('closeMsg')
    }

    const closeForm = () => {
      emit('close')
    }

    const getDwxx = () => {
      let params={
        GGID: state.GGID,
        RYZH: state.userInfo.userLoginName,
        RYXM: state.userInfo.userName,
      }

      axiosUtil.get('/backend/xsgl/xstzfb/selectDwxxByGgid',params).then(res=>{
        if(res.data && props.params.operation==='msg'){
          state.canBm=true
          state.BMXX=res.data.BMXX
        }
      })
    }

    const closeHH = () => {
      state.dialogVisible=false
      getDwxx()
    }

    onMounted(() => {
      getFormData()
      getDwxx()
      getDMBData("GGLX", "GGLXOptions")
      getDMBData("TZFS", "TZFSOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      openBmhh,
      closeMsg,
      viewBmhh,
      getFormData,
      closeHH
    }
  }

})
</script>

<style scoped>
/deep/ .el-collapse-item__header {
  background-color: #dcdfef !important;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
  font-size: 16px;
  color: black !important;
}



.editor-content-view p,
.editor-content-view li {
  white-space: pre-wrap; /* 保留空格 */
}

.editor-content-view blockquote {
  border-left: 8px solid #d0e5f2;
  padding: 10px 10px;
  margin: 10px 0;
  background-color: #f1f1f1;
}

.editor-content-view code {
  font-family: monospace;
  background-color: #eee;
  padding: 3px;
  border-radius: 3px;
}
.editor-content-view pre>code {
  display: block;
  padding: 10px;
}

:deep(.editor-content-view table) {
  border-collapse: collapse;
}
:deep(.editor-content-view td,
.editor-content-view th) {
  border: 1px solid #ccc;
  min-width: 50px;
  height: 20px;
}
:deep(.editor-content-view th) {
  background-color: #f1f1f1;
}

.editor-content-view ul,
.editor-content-view ol {
  padding-left: 20px;
}

.editor-content-view input[type="checkbox"] {
  margin-right: 5px;
}
</style>
