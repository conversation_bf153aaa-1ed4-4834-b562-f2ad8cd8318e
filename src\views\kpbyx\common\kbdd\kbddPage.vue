<template>
  <div style="padding: 20px">
    <div class="page-context">
      <div>距离开标还有</div>
      <div>{{formatTimeDiff(KBSJOffset)}}</div>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      KBSJOffset: props.params.KBSJOffset
    })

    const formatTimeDiff = (ms) => {
      if(!ms){
        return ''
      }
      const totalSeconds = Math.floor(ms / 1000)
      const hours = Math.floor(totalSeconds / 3600)
      const minutes = Math.floor((totalSeconds % 3600) / 60)
      const seconds = totalSeconds % 60

      // 补零函数
      const pad = (n) => n.toString().padStart(2, '0');
      return `${pad(hours)}时${pad(minutes)}分${pad(seconds)}秒`
    }

    const startCountdown = () => {
      setInterval(() => {
        state.KBSJOffset-=1000
      },1000)
    }

    onMounted(() => {
      startCountdown()
    })

    return {
      ...toRefs(state),
      formatTimeDiff

    }
  }

})
</script>

<style scoped>
.page-context {
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 20px;
  height: calc(100vh - 460px);
  padding: 20px;
  background-color: #bdb5dc;
}
.page-context>div:last-child{
  color: #ce0101;
}

</style>
