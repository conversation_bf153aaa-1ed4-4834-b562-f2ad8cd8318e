<template>
  <div>
    <div>{{params}}</div>
    <div>{{processParams}}</div>
    1231231
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    processParams: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({})

    const saveData = (type) => {
      return new Promise((resolve, reject) => {
        if (type === 'submit') {

          let data = {
            ...props.processParams,
            processInstanceId: props.processParams.businessId || props.params.id,
            processName: '承包商准入流程测试AA'+props.params.id,
            tzbl: '2'
          }
          emit("update:processParams", data)

          resolve(true)
        } else {
          ElMessage.info('直接提交吧')
          resolve(true)
        }
      })
    }


    onMounted(() => {

    })

    return {
      ...toRefs(state),
      saveData

    }
  }

})
</script>

<style scoped>

</style>
