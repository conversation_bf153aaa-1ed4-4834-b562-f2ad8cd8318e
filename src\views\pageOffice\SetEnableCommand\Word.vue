<template>
<div class="Word">
	禁止保存、打印、另存。<br/><br/>
  <div style="width:auto; height:700px;" v-html="poHtmlCode" >
  </div>
</div>
</template>

<script>
  import axios from 'axios';
  export default{
    name: 'Word',
    data(){
      return {
        poHtmlCode: '',
      }
    },
    created: function(){
      //由于vue中的axios拦截器给请求加token都得是ajax请求，所以这里必须是axios方式去请求后台打开文件的controller
      axios.post("/api/SetEnableCommand/Word").then((response) => {
        this.poHtmlCode = response.data;
      }).catch(function (err) {
        console.log(err)
      })
    },
    methods:{
			AfterDocumentOpened(){
			  // PageOffice的文档打开后事件回调函数
        pageofficectrl.DisableSave=true;  //禁止保存
        pageofficectrl.DisableSaveAs=true; //禁止另存
        pageofficectrl.DisablePrint=true; //禁止打印
			}
			
    },
    mounted: function(){
      // 以下的为PageOffice事件的回调函数，名称不能改，否则PageOffice控件调用不到
		  window.AfterDocumentOpened = this.AfterDocumentOpened;		  
    }
}
</script>
