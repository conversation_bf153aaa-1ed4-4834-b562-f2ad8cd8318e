<!-- 承包商注册 -->
<template>
    <div class="lui-card-form">
        <el-form
                size="default"
                :model="form"
                ref="vForm"
                label-position="right"
                label-width="120px"
                :disabled="props.isCheck"
                v-loading="form.loading"
        >
            <div v-if="form.FXXXSL>0" style="text-align: right;cursor: pointer;color: #F56C6C"
                 @click="fxxxDialogVisible=true">风险信息查阅
            </div>
            <el-row :gutter="0" class="grid-row" style="margin-bottom:16px;">
                <el-col :span="24" :class="{'grid-cell': true, 'back-red':form.FXXXSL>0}">
                    <el-form-item label="单位全称" prop="DWQC">
                        <el-input v-model.trim="form.DWQC" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="grid-cell">
                    <el-form-item label="统一信用代码" prop="TYXYDM">
                        <el-input v-model.trim="form.TYXYDM" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="grid-cell">
                    <el-form-item label="企业邮箱" prop="QYYX">
                        <el-input v-model.trim="form.QYYX" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12" class="grid-cell" style="padding-left: 10px;color:red;padding-top:5px">
                    <div>提示：企业邮箱将作为系统登录账号，请确认无误后再进行提交！</div>
                </el-col>
                <!--      <el-col :span="24" class="grid-cell">-->
                <!--        <el-form-item label="推荐单位" prop="TJDWID">-->
                <!--&lt;!&ndash;          <el-tree-select&ndash;&gt;-->
                <!--&lt;!&ndash;              v-model="form.TJDWID"&ndash;&gt;-->
                <!--&lt;!&ndash;              :data="unitOptions"&ndash;&gt;-->
                <!--&lt;!&ndash;              :render-after-expand="false"&ndash;&gt;-->
                <!--&lt;!&ndash;              :props="pop"&ndash;&gt;-->
                <!--&lt;!&ndash;              :check-strictly="true"&ndash;&gt;-->
                <!--&lt;!&ndash;              :default-expand-all="true"&ndash;&gt;-->
                <!--&lt;!&ndash;          />&ndash;&gt;-->
                <!--          &lt;!&ndash; <el-select-->
                <!--              v-model="form.TJDWID"-->
                <!--              placeholder="请选择推荐单位"-->
                <!--              clearable>-->
                <!--            <el-option-->
                <!--                v-for="item in tjdw"-->
                <!--                :key="item.DMXX"-->
                <!--                :label="item.DMMC"-->
                <!--                :value="item.DMXX"-->
                <!--            >-->
                <!--            </el-option>-->
                <!--          </el-select> &ndash;&gt;-->
                <!--          <el-cascader v-model="form.TJDWID" :options="tjdw" filterable placeholder="请选择推荐单位"-->
                <!--                         :props="{label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false}"-->
                <!--                         clearable />-->
                <!--        </el-form-item>-->
                <!--      </el-col>-->
                <!--      <el-col v-if="!props.isCheck" :span="8" class="grid-cell">-->
                <!--        <el-form-item label="管理员账户名" prop="GLYZH">-->
                <!--          <el-input-->
                <!--              v-model.trim="form.GLYZH"-->
                <!--              maxlength="30"-->
                <!--              minlength="8"-->
                <!--              clearable-->
                <!--              placeholder="请输入"-->
                <!--          ></el-input>-->
                <!--        </el-form-item>-->
                <!--      </el-col>-->
                <!-- <el-col v-if="!props.isCheck" :span="16" class="grid-cell" style="padding-left: 10px">
                  <div >提示：账户名由英文或字母组成，字母开头，不得少于8位，不得超过30位</div>
                </el-col> -->
                <!--      <el-col v-if="props.isCheck" :span="24" class="grid-cell">-->
                <!--        <el-form-item label="管理员账户名" prop="GLYZH">-->
                <!--          <el-input-->
                <!--              v-model.trim="form.GLYZH"-->
                <!--              maxlength="30"-->
                <!--              minlength="8"-->
                <!--              clearable-->
                <!--              placeholder="请输入"-->
                <!--          ></el-input>-->
                <!--        </el-form-item>-->
                <!--      </el-col>-->
                <el-col :span="12" class="grid-cell">
                    <el-form-item label="联系人姓名" prop="LXRXM">
                        <el-input v-model.trim="form.LXRXM" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12" class="grid-cell">
                    <el-form-item label="联系人手机" prop="LXRSJ">
                        <el-input v-model="form.LXRSJ" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <template v-if="!props.isCheck">
                    <el-col :span="12" class="grid-cell">
                        <el-form-item label="密码" prop="MM">
                            <el-input
                                    v-model.trim="form.MM"
                                    type="password"
                                    clearable
                                    placeholder="请输入"
                                    show-password
                                    minlength="8"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" class="grid-cell" style="padding-left: 10px;color:red;"
                    >提示：密码由英文+字母+字符组成，不得少于8位,并且不能连续出现3个大小连续或相同的数字&nbsp;(如：456、654、888)
                    </el-col>
                    <el-col :span="12" class="grid-cell">
                        <el-form-item label="确认密码" prop="surePw">
                            <el-input
                                    v-model="form.surePw"
                                    type="password"
                                    clearable
                                    placeholder="请输入"
                                    show-password
                                    minlength="8"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" class="grid-cell" style="padding-left: 10px">
                        <!-- (如：456、654、888) -->
                        <span></span>
                    </el-col>
                    <!--      <el-col :span="8" class="grid-cell">-->
                    <!--        <el-form-item label="验证码" prop="yzm">-->
                    <!--          <el-input v-model="form.yzm" clearable placeholder="请输入"></el-input>-->
                    <!--        </el-form-item>-->
                    <!--      </el-col>-->
                    <!--      <el-col :span="16" class="grid-cell" style="padding-left: 10px">-->
                    <!--        <div style="display: flex;align-items: center;height: 100%">-->
                    <!--          <el-button type="primary"  @click="getYzm">{{buttonName}}</el-button>-->
                    <!--          <span style="padding-left: 10px">提示：5分钟内不能重新发送。</span>-->
                    <!--        </div>-->
                    <!--      </el-col>-->
                </template>
                <el-col :span="24" class="grid-cell">
                    <el-form-item label="备注" prop="BZ">
                        <el-input
                                v-model="form.BZ"
                                type="textarea"
                                placeholder="请输入"
                                :rows="3"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <!--<el-row class="table">
                <vsFileUploadTable style="width: 100%;height: 150px" YWLX="CBSZC" :key="form.CBSZCID"
                                   :busId="form.CBSZCID" v-model:fileTableData="fileTableData"
                                   :editable="!props.isCheck"/>
            </el-row>-->
        </el-form>

        <el-row class="btn" justify="space-evenly" v-if="!props.isCheck">
            <el-button type="primary" @click="submit" size="default">提交</el-button>
            <el-button @click="cancelHandle" size="default">返回</el-button>
        </el-row>
        <el-row class="btn" justify="space-evenly" v-else>
            <template v-if="form.SHZT !== '1'">
                <el-button @click="emits('close')" size="default">返回</el-button>
            </template>
            <template v-else>
                <el-button v-if="form.SHZT === '1'" type="primary" @click="approved"
                           size="default">通过
                </el-button>
                <el-button v-if="form.SHZT === '1'" type="danger" @click="refuse" size="default">退回</el-button>
            </template>
        </el-row>
        <el-dialog
                custom-class="lui-dialog"
                v-model="refuseDialogVisible"
                title="退回办理"
                width="800px"
                destroy-on-close
        >
            <cbsshRefuseComponent
                    :defaultData="defaultData"
                    @closeRefuseDialog="closeDialog"
                    @finish="emits('finish')"
            ></cbsshRefuseComponent>
        </el-dialog>
        <el-dialog
                custom-class="lui-dialog"
                v-model="fxxxDialogVisible"
                title="风险信息"
                top="1vh"
                width="800px">
            <tycList style="height: 600px" :TYXYDM="form.TYXYDM" v-if="fxxxDialogVisible"/>
        </el-dialog>
    </div>
</template>

<script setup>
    import outerBox from "../../../components/common/outerBox.vue";
    import {reactive, getCurrentInstance, ref, defineEmits, onMounted, watch} from "vue";
    import {vue, auth, runtimeCfg, mixin} from "@src/assets/core/index";
    import axiosUtil from "@lib/axiosUtil";
    import {ElMessage} from "element-plus";
    import cascaderTree from "@src/lib/cascaderTree.js";
    // 审核退回弹窗
    import cbsshRefuseComponent from "@src/views/cbs/cbsyj/cbsshRefuse.vue";
    import vsFileUploadTable from "@src/views/components/vsFileUploadTable.vue";
    import {
        postZcglContractorRegister, //承包商注册
        getZcglCheckDwqc, //单位全称校验
        getZcglCheckLxrsj, //联系人校验
        getZcglCheckTyxydm, //统一信用代码
        getCbsyjGetEntOrganizations, //组织结构
        getExamineView, //场承包商管理-审核-查看
        postExamineApproved, //市场承包商管理-审核-通过审核
    } from "@src/api/sccbsgl.js";

    const {vsuiRouter} = mixin();
    const instance = getCurrentInstance();
    import {v4 as uuidv4} from "uuid";
    import comFun from "../../../lib/comFun";
    import tycList from "../tyc/tycList";
    import {getCommonSelectDMB} from "../../../api/cbsxx";
    import component from "@views/component";

    const props = defineProps({
        isCheck: {
            type: Boolean,
            default: false,
        },
        defaultData: {
            type: Object,
            default: () => ({
                TYPE: null,
                DATAID: null,
            }),
        },
    });

    const form = reactive({
        CBSZCID: uuidv4().replace(/-/g, ""),
        DWQC: "",
        TYXYDM: "",
        QYYX: "",
        TJDWID: "",
        GLYZH: "",
        LXRXM: "",
        SHZT: "1", //审核状态：未审核：1；审核通过：2；审核不通过：9
        LXRSJ: "",
        MM: "",
        surePw: "",
        yzm: "",
        BZ: "",
        tableData: [
            {lx: "营业执照照片", fileName: "可以上传多个文件", file: []},
            {lx: "法人身份证照片", fileName: "可以上传多个文件", file: []},
        ],
        loading: false
    });
    const fileTableData = vue.ref([]);
    const rules = reactive({
        // 单位全称校验
        DWQC: [
            {
                required: true,
                message: "请输入单位全称",
            },
            {
                trigger: "blur",
                asyncValidator: () => {
                    return new Promise((r, j) => {
                        const {DWQC} = form;
                        getZcglCheckDwqc({
                            DWQC,
                        })
                            .then(({data}) => {
                                if (data.length) {
                                    j("单位全称已存在");
                                } else {
                                    r();
                                }
                            })
                            .catch((err) => {
                                j();
                            });
                    });
                },
            },
        ],
        TJDWID: [{
            required: true,
            message: "请选择推荐单位",
        }],
        LXRXM: [{
            required: true,
            message: "请输入联系人姓名",
        }],
        // 统一信用代码
        TYXYDM: [
            {
                required: true,
                message: "请输入统一信用代码",
            },
            {
                trigger: "blur",
                asyncValidator: () => {
                    return new Promise((r, j) => {
                        const {TYXYDM} = form;
                        getZcglCheckTyxydm({
                            TYXYDM,
                        })
                            .then(({data}) => {
                                if (data.length) {
                                    ElMessage.warning('统一信用代码已存在')
                                    r();
                                } else {
                                    r();
                                }
                            })
                            .catch((err) => {
                                j();
                            });
                    });
                },
            },
        ],

        QYYX: [
            {
                required: true,
                message: "请输入企业邮箱",
            },
            {
                trigger: "blur",
                asyncValidator: () => {
                    return new Promise((r, j) => {
                        axiosUtil.get('/backend/sccbsgl/zcgl/checkQyyx', {QYYX: form.QYYX}).then(res => {
                            if (res.data.length > 0) {
                                j("企业邮箱已存在")
                            } else {
                                r()
                            }
                        }).catch(err => {
                            j(err)
                        })
                    })
                },
            },
        ],

        // 管理员账户
        GLYZH: [
            {
                required: true,
                message: "请输入管理员账户",
            },
            {pattern: /^[a-zA-Z][a-z0-9]{7,29}$/i, message: "请输入正确的管理员账户", trigger: "blur"},
            {
                min: 8,
                max: 30,
                message: "不得少于8位，不得超过30位",
                trigger: "change",
            },
        ],
        // 联系人手机
        LXRSJ: [
            {required: true, trigger: blur, message: "请输联系人手机"},
            // {
            //   validator: /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/,
            //   trigger: blur,
            // },
            {
                trigger: "blur",
                asyncValidator: () => {
                    return new Promise((r, j) => {
                        const {LXRSJ} = form;
                        validatePhoneNumber(LXRSJ)
                            .then((data) => {
                                if (data) {
                                    r();
                                } else {
                                    j(data);
                                }
                            })
                            .catch((err) => {
                                j(err);
                            });
                    });
                },
            }
        ],
        // 密码
        MM: [
            {
                required: true,
                message: "请输入密码",
            },
            {
                min: 8,
                max: 16,
                message: "不得少于8位，不得超过16位",
                trigger: "change",
            },
            {
                pattern: /^(?=.*\d)(?!.*(\d)\1{2})(?!.*(012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210))(?=.*[a-zA-Z])(?=.*[^\da-zA-Z\s]).{8,16}$/,
                message: "至少包含字母、数字、特殊字符,并且不能连续出现3个大小连续或相同的数字(如：456、654、888)",
                trigger: "blur"
            },
            //
        ],
        //确认密码
        surePw: [
            {require: true, trigger: blur, message: "请确认密码"},
            {
                trigger: "blur",
                validator: (rule, value, callback) => {
                    if (value == form.MM) callback();
                    else callback(new Error("两次密码输入不一致"));
                },
            },
        ],
    });
    const mmPattern = /^(?=.*\d)(?!.*(\d)\1{2})(?!.*(012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210))(?=.*[a-zA-Z])(?=.*[^\da-zA-Z\s]).{8,16}$/;
    const lxrsjPattern = /^1[3-9]\d{9}$/;

    const pop = reactive({
        parent: 'PORGID',
        value: 'ORGID',
        label: 'NAME',
        children: 'children',
    });
    // 获取所属单位
    const unitOptions = ref([]);
    const getEntOrganizations = () => {
        getCbsyjGetEntOrganizations()
            .then(({data}) => {
                //if (data) unitOptions.value = data;
                if (data) unitOptions.value = new cascaderTree(data, "ORGID", "PORGID").init();
            })
            .catch((err) => {
                var cacheData = [
                    {
                        NAME: "中石化工程技术研究院",
                        CODE: "1000000000",
                        ORGID: "8ebbb5c57aa24e0dad44403ea8c671fd",
                        PORGID: "0"
                    },
                    {
                        NAME: "承包商",
                        CODE: "SCGL_CBS",
                        ORGID: "00450388d2154c2584e9c62b4534a83b",
                        PORGID: "8ebbb5c57aa24e0dad44403ea8c671fd"
                    },
                    {
                        NAME: "胜软科技",
                        CODE: "1001100",
                        ORGID: "4b5336eb67574572b8a2e9c318fa7106",
                        PORGID: "00450388d2154c2584e9c62b4534a83b"
                    },
                ];
                unitOptions.value = new cascaderTree(cacheData, "ORGID", "PORGID").init();
            });
    };
    //手机号码校验
    const validatePhoneNumber = (phoneNumber) => {
        // var reg = /^1[3-9]\d{9}$/;
        // return reg.test(phoneNumber);
        return new Promise((resolve, reject) => {
            var reg = /^1[3-9]\d{9}$/;
            if (reg.test(phoneNumber)) {
                resolve("手机号码格式正确");
            } else {
                reject("手机号码格式错误");
            }
        });
    };

    onMounted(() => {
        console.log(1)
        getTjdw()
        getEntOrganizations();
    });
    const vForm = ref(null);
    const emits = defineEmits(["close", "finish"]);

    //查询保存信息
    const viewSaveData = () => {
        const {TYPE, DATAID} = props.defaultData;
        if (!TYPE) {
            return;
        }
        getExamineView({
            TYPE,
            DATAID,
        })
            .then(({data}) => {
                if (data) Object.assign(form, data[0]);
            })
            .catch((err) => {
            });
    };
    watch(
        () => props.defaultData,
        () => {
            viewSaveData();
        },
        {
            immediate: true,
        }
    );
    // 注册保存
    const submit = () => {
        vForm.value.validate().then(() => {}).catch((err) => {console.log(err);});
        if (!form.DWQC) {
            ElMessage.error("请输入单位全称！");
            return;
        }
        if (!form.LXRXM) {
            ElMessage.error("请输入联系人姓名！");
            return;
        }
        if (!form.TYXYDM) {
            ElMessage.error("请输入统一信用代码！");
            return;
        }
        if (!form.QYYX) {
            ElMessage.error("请输入企业邮箱！");
            return;
        }
        if (!form.MM) {
            ElMessage.error("请输入密码！");
            return;
        }
        if (!form.surePw) {
            ElMessage.error("请确认密码！");
            return;
        }
        if (!form.LXRSJ) {
            ElMessage.error("请输入联系人手机号！");
            return;
        }
        if (form.surePw !== form.MM) {
            ElMessage.error("两次密码输入不一致！");
            return;
        }
        if (form.MM.length < 8 || form.MM.length > 16) {
            ElMessage.error("密码不得少于8位，不得超过16位！");
            return;
        }
        if (!mmPattern.test(form.MM)) {
            ElMessage.error("密码至少包含字母、数字、特殊字符,并且不能连续出现3个大小连续或相同的数字(如：456、654、888)");
            return;
        }
        if (!lxrsjPattern.test(form.LXRSJ)) {
            ElMessage.error("手机号码格式错误");
            return;
        }
        axiosUtil.get('/backend/sccbsgl/zcgl/checkQyyx', {QYYX: form.QYYX}).then(res => {
            if (res.data.length > 0) {
                ElMessage.error('企业邮箱已存在');
            } else {
                getZcglCheckTyxydm({
                    TYXYDM: form.TYXYDM,
                })
                .then(({data}) => {
                    if (data.length) {
                        ElMessage.error('统一信用代码已存在');
                    } else {
                        getZcglCheckDwqc({
                            DWQC: form.DWQC,
                        })
                        .then(({data}) => {
                            if (data.length) {
                                ElMessage.error('单位全称已存在');
                            } else {
                                saveData();
                            }
                        })
                    }
                })
            }
        });

    };

    const saveData = () => {
        form.loading = true;
        let param = {
            YWLX: 'CBSZX',
            YZM: form.yzm,
            PHONE: form.LXRSJ
        }
        postZcglContractorRegister({...form})
            .then((res) => {
                ElMessage.success({
                    message: "注册成功，请重新登录",
                    type: 'success',
                    duration: 10000
                });

                let params = {
                    CBSWYBS: form.CBSZCID,
                    CBSDWQC: form.DWQC,
                    TYXYDM: form.TYXYDM,
                    CJSJ: comFun.getNowTime(),
                    name: form.DWQC,
                    humanName: form.LXRXM
                }
                // axiosUtil.get('/backend/sccbsgl/zcgl/getTycData',params)
                emits("close");
                form.loading = false
                vsuiRouter.push("/login");

            })
            .catch(() => {
                ElMessage.error("保存失败");
            });
    };

    // 审核通过
    const approved = () => {
        const userInfo = auth.getPermission();
        const {DATAID, TYPE} = props.defaultData;
        console.log(
            JSON.stringify({
                TYPE,
                DATAID,
                SHRZH: userInfo.userLoginName,
                SHRXM: userInfo.userName,
            })
        );
        // return false;
        postExamineApproved({
            TYPE,
            DATAID,
            SHRZH: userInfo.userLoginName,
            SHRXM: userInfo.userName,
        })
            .then((result) => {
                ElMessage.success("审核保存成功！");
                emits("finish");
            })
            .catch((err) => {
                ElMessage.error("审核保存失败！");
            });
    };

    // 退回
    const refuseDialogVisible = ref(false);
    const refuse = () => {
        refuseDialogVisible.value = true;
    };
    const closeDialog = () => {
        refuseDialogVisible.value = false;
    };
    //获取验证码
    const time = vue.ref(60);
    const buttonName = vue.ref('发送短信');
    const getYzm = async () => {
        const {LXRSJ} = form;
        var reg = /^1[3-9]\d{9}$/;
        if (reg.test(LXRSJ)) {
            let interval = window.setInterval(function () {
                buttonName.value = time.value + '后重新发送';
                --time.value;
                if (time.value < 0) {
                    buttonName.value = "重新发送";
                    time.value = 60;
                    window.clearInterval(interval);
                }
            }, 1000);
            let result = await axiosUtil.post('/backend/SysMessageLog/saveYzm', {PHONE: LXRSJ, YWLX: 'CBSZX'});
            if (result.data.meta.message == 'ok') {

            } else {
                this.$message.error("短信发送失败！")
            }
        } else {
            reject("手机号码格式错误");
        }

    };

    const fxxxDialogVisible = ref(false)


    const cancelHandle = () => {
        emits("close");
        vsuiRouter.push("/login");
    };
    const tjdw = ref([]);
    const getTjdw = () => {
        axiosUtil.get('/backend/common/selectTwoOrgAndJgbmList', {}).then((res) => {
            tjdw.value = comFun.treeData(res.data || [], 'ORGNA_ID', 'PORGNA_ID', 'children', '1458257119443951634')
        });
    };
</script>

<style scoped>
    @import "../style/index.css";

    .out-box-content {
        padding: 50px;
    }

    .el-form > .el-row:nth-child(2),
    .el-row:nth-child(5) {
        display: flex;
        justify-content: space-between;
    }

    .btn > .el-button:last-child {
        /* margin-left: 20px; */
    }

    :deep(.back-red .el-form-item__content .el-input.is-disabled .el-input__wrapper) {
        /*background-color: #F56C6C;*/
        --el-disabled-text-color: #F56C6C;
    }
</style>
