<template>
  <div style="height: calc(100% - 40px);">
    <el-form style="height: 100%;" class="lui-page" size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="userName">
            <el-input ref="input45296" placeholder="设备名称" @input="getDataList"
                      v-model="state.listQuery.SBMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="18" style="margin-left: auto">
          <el-button type="primary" @click="exportModel()"><el-icon><Download/></el-icon>设备模板</el-button>
          <excelImport bntName="导入设备信息" key="sbxx" url="/backend/importController/importSbxx" :importData="importSbData" @refresh="getDataList"></excelImport>
          <el-button type="primary" class="lui-button-add" @click="addRow"><el-icon><Plus/></el-icon>添加</el-button>
          <el-button type="primary" @click="batchChoose" :disabled="selectedRows.length === 0"><el-icon><Select/></el-icon>选择</el-button>
        </el-col>
      </el-row>
      <el-table
        highlight-current-row
        size="default"
        ref="table"
        class="lui-table"
        fit
        height="50vh"
        :border="false"
        :data="state.tableData"
        @selection-change="handleSelectionChange" 
      >
      <el-table-column
          type="selection"
          width="55"
          align="center"
      />
        <EleProTableColumn
          v-for="prop in state.tableColumn"
          :col="prop"
          :key="prop.columnKey"
        >
            <template #format="{ row }">
                {{ row[prop.prop]?row[prop.prop].replace(" 00:00:00", ""): '' }}
            </template>
          <template #opration="{ row, $index }">
            <div>
              <!-- <el-button class="lui-table-button" @click="chooseData(row, $index)">选择</el-button> -->
              <el-button class="lui-table-button" @click="editRow(row, $index)">编辑</el-button>
              <el-button class="lui-table-button" @click="delRow(row, $index)">删除</el-button>
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-form>
  </div>

  <el-dialog
      custom-class="lui-dialog"
      title="设备信息编辑"
      v-model="editVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      top="100px"
      width="1200px"
      @close="()=>{getDataList()}">
    <sbxxEdit :editData="editData" :editable="true" @updateData="updateData" @close="editVisible = false" :resultTableData="editDataWYBS" />
  </el-dialog>
</template>
<script setup>
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
  provide
} from "vue";
import { axios ,auth} from "@src/assets/core/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {v4 as uuidv4} from "uuid";
import axiosUtil from "../../../../../lib/axiosUtil";
import sbxxEdit from './sbxx_xzEdit.vue'
import { Search, Select, Plus, Upload, Download } from '@element-plus/icons-vue'
import comFun from "../../../../../lib/comFun";
import excelImport from "@src/views/components/excelImport.vue";

const state = reactive({
  tableData: [],
  listQuery:{},
  loading: false,
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    {
      label: "设备名称",
      prop: "SBMC",
      align: "center",
      // minWidth: 150,
    },
    {
      label: "规格型号",
      prop: "GGXH",
      align: "center",
      width: 150,
    },
    {
      label: "生产厂家",
      prop: "SCCJ",
      align: "center",
      // width: 150,
    },
    {
      label: "出厂日期",
      prop: "TCRQ",
      align: "center",
      width: 150,
        slot: 'format'
    },
    {
      label: "数量",
      prop: "SL",
      align: "center",
      width: 150,
    },
    {
      label: "操作",
      align: "center",
      width: 200,
      fixed: "right",
      slot: "opration",
      // hide: !props.editable
    },
  ],
});

const selectedRows = ref([])
const tableRef = ref(null);
// 批量选择按钮
const handleSelectionChange = (val) => {
  selectedRows.value = val;
};
const batchChoose = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一条数据');
    return;
  }
  
  // 将选中的所有行数据通过事件发送给父组件
  emit('updateChooseData', selectedRows.value);
};

// 导入设备数据
const importSbData = {
  CJRZH: props.TYXYDM,
  CJRXM: auth.getPermission().userName,
  CJDWID: auth.getPermission().orgnaId,
  XGRZH: props.TYXYDM,
  SHZT: '1'
}

const addRow = () => {
  const userinfo = auth.getPermission();
  let userParams={
    CJRZH: props.TYXYDM,
    CJRXM: userinfo.userName,
    CJDWID: userinfo.orgnaId,
    CJSJ: comFun.getNowTime(),
    SHZT: '1',
  }
  let params={
    edit: true,
    EXTENSION:{},
    SBZSJID: comFun.newId(),
    ...userParams,
  }

  editIndex.value = state.tableData.length;
  editData.value = params;
  editVisible.value = true;

}


const editRow = (row,index) => {
  editIndex.value = index;
  editData.value = row;
  editVisible.value = true;
}

const updateData = (val, isAdd) => {
  saveRow(val,isAdd)
};

const saveRow = (params,isAdd) => {
  axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveZbnlXxx',{tableData: [params]}).then(r=>{
    ElMessage.success('保存成功')
    if (isAdd) {
      addRow()
    } else {
      editVisible.value = false;
    }
    emit('updateEditData',params)
  })
}

const delRow = (row, index) => {
  ElMessageBox.confirm(
      '确定删除该条数据?',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(() => {
    let params={
      SBZSJID: row.SBZSJID,
      SHZT: '2'
    }
    axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveZbnlXxx',{tableData: [params]}).then(r=>{
      state.tableData.splice(index,1)
      ElMessage.success('删除成功')
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除',
    })
  })
}

const props = defineProps({
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
  TYXYDM: {
    type: String,
    default: ''
  }
});
const emit=defineEmits(['updateChooseData','updateEditData'])
const chooseData = (row, index) => {
  emit('updateChooseData',row)
};

const getDataList = () => {
  const userinfo = auth.getPermission();
  let params = {
    ...state.listQuery,
    orgId: userinfo.orgnaId,
    TYXYDM: props.TYXYDM,
  }
  state.loading = true
  axiosUtil.get('/backend/sccbsgl/cbsyj/ytxxx/selectZbnlXxx', params).then((res) => {
    state.tableData = res.data || []
    state.loading = false
  })
}

const exportModel = (lx) => {
  const a = document.createElement('a');
  a.href = "../../../../../../static/excel/sbxxModel.xlsx";
  a.download = "设备导入模版";
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

onMounted(()=>{
  getDataList()
})

const editVisible = ref(false);
const editIndex = ref(0);
const editData = ref({});

const vForm = ref(null);
const validateForm = () => {
  return vForm.value.validate();
};
defineExpose({
  validateForm,
});

</script>
<style scoped>
>>> .el-table-fixed-column--right{
    background-color: rgba(255, 255, 255, 1) !important;
}
::v-deep .el-icon{
  display: inline;
}
</style>