<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="KHZQ">
            <el-select v-model="listQuery.KHZQ" class="full-width-input"
                       placeholder="请选择考核周期"
                       clearable>
              <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC"
                         :value="item.PJZQID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="KHZY">
            <el-select v-model="listQuery.KHZY" class="full-width-input"
                       placeholder="请选择考核专业" filterable
                       clearable>
              <el-option v-for="(item, index) in KHZYOptions" :key="index" :label="item.ZYMC"
                         :value="item.PJZYID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CBSMC">
            <el-input ref="input45296" placeholder="请输入承包商名称" v-model="listQuery.CBSMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="DWMC">
            <el-input ref="input45296" placeholder="请输入队伍名称" v-model="listQuery.DWMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMMC">
            <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData">
              <el-icon>
                <Plus/>
              </el-icon>
              新建
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="ZQMC" label="考核周期" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="PJZYMC" label="考核专业" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="CBSDWQC" label="承包商名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="DWMC" label="队伍名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="XMMC" label="项目名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="PJDXMC" label="单项工程名称" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="HJDF" label="考核得分" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="CJSJ" label="评价时间" align="center"
                               :show-overflow-tooltip="true" width="120"></el-table-column>


              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
              <span v-if="scope.row.SHZT==='0'">
                    <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑
                    </el-button>
                    <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.row)">删除
                    </el-button>
                  </span>
                  <span v-else>
                    <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                    </el-button>
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="考核明细上报"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="90%">
      <div>
        <khmxtbEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import khmxtbEdit from "@views/khpj/khmxtb/khmxtbEdit";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "@lib/axiosUtil";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,khmxtbEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,

      KHSJOptions: [],
      KHZYOptions: [],
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhmxsbPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    // 新增
    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.params = {editable: true, id: row.KHPJID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.params = {editable: false, id: row.KHPJID, operation: 'view'}
      state.dialogVisible = true
    }

    const deleteRow = (row) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.del('/backend/sckhpj/khmxtb/delKhmxtb?KHPJID=' + row.KHPJID, null).then(res => {
          ElMessage.success({
            message: '删除成功!'
          });
          getDataList()
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const getKhsjList = () => {
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhqjList', null).then(res => {
        state.KHSJOptions = res.data || []
      })
    }

    const getKhzyList = () => {
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhzyList', null).then(res => {
        state.KHZYOptions = res.data || []
      })
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getDataList()
      getKhsjList()
      getKhzyList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      addData,
      closeForm,
      deleteRow,
      viewRow

    }
  }

})
</script>

<style scoped>

</style>
