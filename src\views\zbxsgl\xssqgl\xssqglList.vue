<template>
    <div class="container">
        <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
                 size="default">
            <el-row :gutter="20" class="lui-search-form">
                <el-col :span="6" class="grid-cell">
                    <el-form-item label="" prop="XMMC">
                        <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" class="grid-cell" style="margin-right: 150px">
                    <el-form-item label="" prop="XMMC">
                        <div style="display: flex;align-items: center;gap: 10px">
                            <el-date-picker
                                    v-model="listQuery.KSSJ"
                                    type="date"
                                    clearable
                                    style="width: 150px"
                                    placeholder="请选择开始时间"
                                    value-format="YYYY-MM-DD"
                            ></el-date-picker>
                            <div>-</div>
                            <el-date-picker
                                    v-model="listQuery.JSSJ"
                                    type="date"
                                    clearable
                                    style="width: 150px"
                                    placeholder="请选择结束时间"
                                    value-format="YYYY-MM-DD"
                            ></el-date-picker>
                        </div>
                    </el-form-item>
                </el-col>

                <el-col :span="6" class="grid-cell">
                    <el-form-item label="" prop="CJR">
                        <el-input ref="input45296" placeholder="请输入创建人" v-model="listQuery.CJR" type="text" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="4" class="grid-cell">
                    <div class="static-content-item" style="display: flex;">
                        <el-button ref="button91277" @click="getDataList" type="primary">
                            <el-icon>
                                <Search/>
                            </el-icon>
                            查询
                        </el-button>
                        <el-button ref="button9527" type="primary" class="lui-button-add" @click="checkXsfs">
                            <el-icon>
                                <Plus/>
                            </el-icon>
                            新建
                        </el-button>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <div class="container-wrapper">
                        <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                                  :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                                             :index="indexMethod"/>
                            <el-table-column prop="XMMC" label="项目名称" align="left" header-align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="ZJLYMC" label="资金来源" align="center"
                                             :show-overflow-tooltip="true" width="160"></el-table-column>
                            <el-table-column prop="SSDWMC" label="项目主管部门" align="center"
                                             :show-overflow-tooltip="true" min-width="130"></el-table-column>
                            <el-table-column prop="XSFSMC" label="采购方式" align="center"
                                             :show-overflow-tooltip="true" width="120"></el-table-column>
                            <el-table-column prop="ZZFSMC" label="招标组织方式" align="center"
                                             :show-overflow-tooltip="true" width="120"></el-table-column>
                            <el-table-column prop="CJSJ" label="创建时间" align="center"
                                             :show-overflow-tooltip="true" width="180"></el-table-column>
                            <el-table-column prop="CJRXM" label="创建人" align="center"
                                             :show-overflow-tooltip="true" width="100"></el-table-column>

                            <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                                <template #default="scope">
                                    <span v-if="scope.row.SHZT==='0'">
                                      <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑
                                      </el-button>
                                      <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.row)">删除
                                      </el-button>
                                    </span>
                                    <span v-else>
                                      <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                                      </el-button>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-pagination background v-model:current-page="listQuery.page"
                                       v-model:page-size="listQuery.size"
                                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                                       class="lui-pagination"
                                       @size-change="getDataList" @current-change="getDataList" :total="total">
                        </el-pagination>
                    </div>
                </el-col>
            </el-row>
        </el-form>

        <!--<el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogVisible"
                v-model="dialogVisible"
                title="选商申请"
                @closed="closeForm"
                z-index="1000"
                top="5vh"
                width="1400px">
            <div>
                <auditFrame v-if="dialogVisible" :business-params="params"
                            :process-params="processParams[FQRLX]" @close="closeForm"/>
            </div>
        </el-dialog>-->

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogVisible"
                v-model="dialogVisible"
                title="选商申请"
                @closed="closeForm"
                z-index="1000"
                top="5vh"
                width="1400px">
            <div>
                <xssqglEditNew v-if="dialogVisible" :params="params" v-model:value="processParams[FQRLX]" @close="closeForm"/>
            </div>
        </el-dialog>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogXSFSVisible"
                v-model="dialogXSFSVisible"
                title="选择选商方式"
                z-index="1000"
                width="500px">
            <div style="padding: 20px">
                <el-radio-group v-model="XSFS" size="default" style="display: flex;flex-direction: column;gap: 20px">
                    <el-radio style="width: 150px;margin-right: 0" :label="item.DMXX"
                              v-for="(item,index) in XSFSOptions" :key="index">{{item.DMMC}}
                    </el-radio>
                </el-radio-group>

                <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
                    <el-button size="default" type="primary" @click="addData">确定</el-button>
                    <el-button size="default" @click="dialogXSFSVisible=false">返回</el-button>
                </div>
            </div>
        </el-dialog>

        <el-dialog z-index="1000" title="业务流程跟踪" v-model="dialogYWGZVisible" width="1400px" top="5vh"
                   :close-on-click-modal="false">
            <monitorForm2 v-if="dialogYWGZVisible" :queryParams="params"/>
        </el-dialog>
    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import axiosUtil from "@lib/axiosUtil";
    import comFun from "@lib/comFun";
    import {ElMessage, ElMessageBox} from "element-plus";
    import {Plus, Search, Upload} from "@element-plus/icons-vue";
    import xssqglEdit from "@views/zbxsgl/xssqgl/xssqglEdit";
    import auditFrame from "@views/workflow/AuditFrame";
    import monitorForm2 from "@views/workflow/MonitorForm2";
    import vsAuth from "@lib/vsAuth";
    import xssqglEditNew from "@views/zbxsgl/xssqgl/xssqglEditNew";


    export default defineComponent({
        name: '',
        components: {Search, Upload, Plus, xssqglEdit, auditFrame, monitorForm2, xssqglEditNew},
        props: {},
        setup(props, {emit}) {
            const state = reactive({
                listQuery: {
                    page: 1,
                    size: 10,
                    KSSJ: '',
                },
                tableData: [],
                total: 0,
                params: {},
                dialogVisible: false,

                XSFSOptions: [],

                XSFS: '',
                dialogXSFSVisible: false,
                dialogYWGZVisible: false,

                FQRLX: '',
                processParams: {
                    EJDW: {
                        activityId: "new",
                        processId: "XSGL_XXSQ_NEW",
                        processVersion: "1"
                    },
                    JGBM: {
                        activityId: "new",
                        processId: "XSGL_XXSQ_JGBM",
                        processVersion: "1"
                    }

                },
            })


            const getDataList = () => {
                const params = {
                    ...state.listQuery,
                    orgnaId: vsAuth.getAuthInfo().permission.orgnaId,
                }
                axiosUtil.get('/backend/xsgl/xssqgl/selectXssqPage', params).then((res) => {
                    state.tableData = res.data.list
                    state.total = res.data.total
                });
            }

            // 新增
            const addData = () => {
                if (!state.XSFS) {
                    ElMessage.warning('请选择选商方式')
                    return
                }
                state.dialogXSFSVisible = false
                let SQLX = state.XSFSOptions.find(item => item.DMXX === state.XSFS).BYZD1
                state.params = {editable: true, id: comFun.newId(), operation: 'add', XSFS: state.XSFS, SQLX: SQLX, FQRLX: state.FQRLX}
                state.dialogVisible = true
            }
            // 编辑
            const editRow = (row) => {
                state.params = {editable: true, id: row.FAID, operation: 'edit', FQRLX: state.FQRLX, XSFS: state.XSFS}
                state.dialogVisible = true
            }

            const viewRow = (row) => {
                state.params = {editable: false, id: row.FAID, operation: 'view', FQRLX: state.FQRLX, XSFS: state.XSFS}
                state.dialogVisible = true
            }

            const deleteRow = (row) => {
                ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axiosUtil.del('/backend/xsgl/xssqgl/delXssq?FAID=' + row.FAID, null).then(res => {
                        ElMessage.success({
                            message: '删除成功!'
                        });
                        getDataList()
                    })
                }).catch(() => {
                    ElMessage.info({
                        message: '已取消删除'
                    });
                });
            }

            const checkXsfs = () => {
                state.XSFS = 'GKZB'
                state.dialogXSFSVisible = true

            }

            const indexMethod = (index) => {
                return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
            }
            const closeForm = () => {
                state.dialogVisible = false
                getDataList()
            }

            const timeAddMonth = (dateString, monthNum) => {
                const [year, month, day] = dateString.split('-').map(Number)

                let date = new Date(year, month - 1, day);

                date.setMonth(date.getMonth() + monthNum);

                const newYear = date.getFullYear();
                const newMonth = String(date.getMonth() + 1).padStart(2, '0');
                const newDay = String(date.getDate()).padStart(2, '0');

                return `${newYear}-${newMonth}-${newDay}`;
            };

            const getDMBData = async (DMLBID, resList) => {
                let params = {
                    DMLBID
                };
                const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
                state[resList] = res.data;
            };

            const viewMonitor = (row) => {
                state.params = {id: row.FAID};
                state.dialogYWGZVisible = true;
            };

            const getFQRLX = () => {
                let params={
                    USER_ID: vsAuth.getAuthInfo().permission.userId
                }
                axiosUtil.get('/backend/common/selectOrganByUserId',params).then(res=>{
                    if(res.data && res.data.length>0 && res.data[0].DWLX==='JG'){
                        state.FQRLX='JGBM'
                    }else {
                        state.FQRLX='EJDW'
                    }
                })
            }

            onMounted(() => {
                state.listQuery.KSSJ = timeAddMonth(comFun.getNowDate(), -1);
                getFQRLX();
                getDataList();
                getDMBData("CGFS", "XSFSOptions");
            });

            return {
                ...toRefs(state),
                getDataList,
                indexMethod,
                editRow,
                addData,
                closeForm,
                deleteRow,
                viewRow,
                checkXsfs,
                timeAddMonth,
                viewMonitor

            }
        }

    })
</script>

<style scoped>

</style>
