<template>
  <div>
    <QuillEditor ref="myQuillEditor"
                 theme="snow"
                 :read-only="!editable"
                 v-model:content="content"
                 :options="editorOption"
                 contentType="html"
                 @update:content="setValue"
                 @change="onEditorChange"
                 style="min-height: 180px"
    />
    <!-- 使用自定义图片上传 -->
    <input type="file" hidden accept=".jpg,.png" ref="fileBtn" @change="handleUpload" />
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, toRaw} from "vue";
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import {v4 as getUUID} from "uuid";
import axiosUtil from "@lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {QuillEditor},
  props: {
    modelValue:{
      type: String,
    },
    urlPrefix:{
      type: String,
      default: '/backend'
    },
    editable: {
      type: String,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      content: '',
      editorOption: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            [{ 'indent': '-1' }, { 'indent': '+1' }],
            [{ 'header': 1 }, { 'header': 2 }],
            ['image'],
            [{ 'direction': 'rtl' }],
            [{ 'color': [] }, { 'background': [] }]
          ]
        },
        placeholder: props.editable ?  '请输入内容...' : ''
      }
    })
    const instance = getCurrentInstance()
    const imgHandler = (state) => {
      if (state) {
        instance.proxy.$refs['fileBtn'].click()
      }
    }
    const setValue = () => {
      let myQuillEditor=instance.proxy.$refs['myQuillEditor']
      const text = toRaw(myQuillEditor).getHTML()
      emit('update:modelValue', text);

    }
    const onEditorChange = (val)=>{
    }


    const handleUpload = (e) => {
      let myQuillEditor=instance.proxy.$refs['myQuillEditor']
      const files = Array.prototype.slice.call(e.target.files)
      if (!files) {
        return
      }
      const formdata = new FormData()
      formdata.append('files', files[0])
      formdata.append('busType', 'hbycjxgl')
      formdata.append('busId', newId())
      formdata.append('standbyField0', 'ggfj')
      axiosUtil.uploadFile(props.urlPrefix+'/minio/upload',formdata).then(res=>{
        console.error(res)
        if(res.data && res.data.DOWNLOADID){
          console.error(res.data)
          const quill = toRaw(myQuillEditor).getQuill()
          const length = quill.getSelection().index
          // 插入图片，res为服务器返回的图片链接地址
          quill.insertEmbed(length, 'image', props.urlPrefix+'/minio/download?id='+res.data.DOWNLOADID)
          // 调整光标到最后
          quill.setSelection(length + 1)
        }
      })
    }

    const newId = () => {
      return getUUID().replace(/-/g, '').toUpperCase()
    }

    onMounted(() => {
      let myQuillEditor=instance.proxy.$refs['myQuillEditor']
      const quill = toRaw(myQuillEditor).getQuill()
      if (myQuillEditor) {
        quill.getModule('toolbar').addHandler('image', imgHandler)
      }
      if(props.modelValue){
        let val=props.modelValue
        toRaw(myQuillEditor).setHTML(val)
        const quill = toRaw(myQuillEditor).getQuill()
        const length = quill.getLength();
        quill.setSelection(length,length + 1)
        state.updateFirst=true
      }
    })

    return {
      ...toRefs(state),
      handleUpload,
      onEditorChange,
      setValue

    }
  }

})
</script>

<style scoped>
/deep/.ql-clipboard {
  position: fixed;
  display: none;
  left: 50%;
  top: 50%;
}
</style>

