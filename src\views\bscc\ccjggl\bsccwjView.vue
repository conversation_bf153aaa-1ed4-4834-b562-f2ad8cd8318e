<template>
  <div>
    <ccwjPane ref="cczxPane" :params="params" v-model="formData"/>
    <div class="bottom-button" >
      <el-button size="default" @click="closeForm">返回</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import ccwjPane from "@views/bscc/xmbsccgl/ccwjPane";

export default defineComponent({
  name: '',
  components: {ccwjPane},
  props: {
    params: {
      type: Object,
      required: true
    },
    formData:{
      type: Object,
      required: true
    }
  },
  setup(props, {emit}) {
    const state = reactive({})

    const closeForm = () => {
      emit('close')
    }
    onMounted(() => {

    })

    return {
      ...toRefs(state),
      closeForm

    }
  }

})
</script>

<style scoped>
.bottom-button{
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
