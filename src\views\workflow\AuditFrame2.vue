<!-- 审核框架页 -->
<template>
  <div id="auditDiv" :style="{ height: height }">
    <el-container style="height: 100%">
      <el-main style="padding: 20px">
        <div style="width: 100%;text-align: center;font-weight: bold;font-size: 30px;margin-bottom: 20px">
          当前审批事项：{{lcTitle[processParams.processId]}}
        </div>
        <div style="display: flex;justify-content: center;align-items: center;">
          <processDetails style="overflow:auto;width: auto;" :processParams="processParams"/>
<!--          <div style="width: 200px; height: 220px">-->
<!--            <div align="center" style="color:red">当前业务流程图（点击放大）</div>-->
<!--            <el-image-->
<!--                style="width: 200px; height: 200px"-->
<!--                :key="lcsmxx.pic"-->
<!--                :hide-on-click-modal="true"-->
<!--                :src="lcsmxx.pic"-->
<!--                :preview-src-list="[lcsmxx.pic]"-->
<!--                fit="cover"-->
<!--            />-->
<!--          </div>-->

        </div>

        <div class="row-row">
          <div class="row-title">1.项目名称：</div>
          <div class="row-context">
            {{processParams.processInstanceName?.substring(0, processParams.processInstanceName?.lastIndexOf('-'))}}
          </div>
        </div>

        <div class="row-row">
          <div class="row-title">2.审批内容：</div>
          <div class="row-context">
            是否同意{{lcTitle[processParams.processId]}}各项内容。
          </div>
        </div>
        <div style="width: 100%;text-align: center;font-size: 18px;margin-bottom: 20px;margin-top: 20px">
          {{lcTitle[processParams.processId]}}主要信息
        </div>
        <div style="box-shadow: rgba(0, 0, 0, 0.12) 0 0 12px;">
          <component ref="busiCom" :is="componentChild[busiUrl]"
                     v-model:value="params" @closeAudit="close"
                     @confirm="finishTask" @addTask="addTask" :params="businessParams"></component>
        </div>

        <div class="row-row" v-if="isYld">
          <div class="row-title" style="color: #409EFF">3.审批依据：</div>
          <div class="row-context">
            <div style="white-space: pre-line;line-height: 40px">{{lcsmxx.BLYJ}}</div>
          </div>
        </div>

        <div class="row-row" v-else>
          <div class="row-title" style="width: 250px">3.审批历史记录：</div>
          <div class="row-context">
          </div>
        </div>
        <div style="width: 100%;text-align: center;font-size: 18px;margin-bottom: 10px;margin-top: 20px" v-if="isYld">
          审批历史记录
        </div>
        <el-table :data="tableData" style="min-height: 300px;margin-top: 10px" :border="true" class="lui-table">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <el-table-column prop="BUSINESSNAME" label="审批节点" header-align="center" align="left" width="150"></el-table-column>
          <el-table-column prop="CLR" label="审批人" header-align="center" align="center" width="200">
            <template #default="scope">
              <span>{{ scope.row.CLDW + "(" + scope.row.CLR + ")" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="OPINION" label="审批意见" header-align="center" align="left" min-width="250"></el-table-column>

          <!--          <el-table-column prop="APPRRESULT" label="审批情况" width="100" header-align="center" align="center"></el-table-column>-->
          <el-table-column prop="JSSJ" label="接收时间" header-align="center" align="center" width="200"></el-table-column>
          <el-table-column prop="BLSJ" label="审批时间" header-align="center" align="center" width="200"></el-table-column>
        </el-table>


      </el-main>
      <el-footer style="height: 40px; margin: 4px auto;">
        <el-button v-for="item in buttons" :type="item.BYZD1||'primary'" v-bind:key="item.ACTIVITYID"
                   @click="onOperate(item)">{{ item.APPROVENAME }}
        </el-button>
        <el-button type="success" v-if="processParams.processId != 'YJPJ_CBSPJ' && processParams.flag != 'view' &&processParams.status != '3' &&
            processParams.activityId != undefined && (processParams.activityId == 'new' ||processParams.activityId == '1')"
                   @click="onSave()">保存
        </el-button>
        <el-button type="primary"
                   v-if="processParams.processId != 'YJPJ_CBSPJ' && processParams.flag != 'view' && processParams.status != '3'&&buttons.length==0"
                   @click="onSubmit">提交
        </el-button>
        <!--        <el-button @click="close" v-if="processParams.processId != 'YJPJ_CBSPJ' ">关闭</el-button>-->
      </el-footer>
    </el-container>
    <el-dialog v-model="showDialog" :title="shTItle" :append-to-body="true" width="35%">
      <opinion-form v-if="showDialog" :apprValue="apprValue" :params="params" @close="closeOpinion" @confirm="finishTask"></opinion-form>
    </el-dialog>
  </div>
</template>
<script>

import {
  defineComponent,
  defineAsyncComponent,
  toRefs,
  reactive,
  markRaw,
  nextTick,
  ref,
  getCurrentInstance,
  onMounted
}
  from 'vue'
import {ElLoading, ElMessage, ElMessageBox} from "element-plus";
import api from "../../api/lc";
import OpinionForm from "./OpinionForm.vue";
import axios from "axios";
import vsAuth from "@src/lib/vsAuth";
import axiosUtil from "../../lib/axiosUtil";
import processDetails from "./processDetails";


//承包商准入
import cbszr from "../cbs/cbsyj/index";



// //測試
// import testDialog from "../test/workflowTest/testDialog";

export default defineComponent({
  name: 'AuditFrame',
  components: {OpinionForm, cbszr,processDetails},
  props: {
    processParams: {
      type: Object,
      default: {},
    },
    businessParams: {
      type: Object,
      default: {},
    },
  },
  emits: ['close'],
  setup(props, context) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      busiUrl: null,
      busiCom: null,
      params: props.processParams,
      businessParams: props.businessParams,
      strSystemCode: "",
      buttons: [],
      showDialog: false,
      apprValue: null,
      apprResult: null,
      height: "calc(100vh - 150px)!important",
      nextPerformer: "",
      shTItle: '审核意见',
      componentChild: {'cbszr': markRaw(cbszr)},


      lcsmxx:[],
      tableData:[],
      lcTitle:{
        'ZTB_XSSQLC': '选商方案',
        'ZTB_ZBQKSP': '选商情况',
      },
      isYld: false
    })

    const instance = getCurrentInstance()


    /**
     * 加载审核页面
     */
    const loadAuditPage = async () => {
      let ld = ElLoading.service({target: "#auditDiv", text: "正在加载页面，请稍后...",});
      if (props.processParams.status == "3") {
        //已办查看页面
        try {
          let res = await axios({
            url: api.getTaskInfo(),
            data: "varJson=" + JSON.stringify(state.params),
            method: "post",
          });
          if (res.data) {
            state.busiUrl = res.data.businessUrl;


          }
        } catch (error) {
          ld.close();
        }
      } else {
        //待办审核页面
        try {
          let resInfo = await axios({
            url: api.getTaskInfo(),
            data: "varJson=" + JSON.stringify(state.params),
            method: "post",
          });
          if (resInfo.data) {
            state.busiUrl = resInfo.data.businessUrl;
          }
        } catch (error) {
          ld.close();
        }
      }
      //componentChild = defineAsyncComponent(() => import(`@views/${state.busiUrl}.vue`));
      ld.close();
    }
    /**
     * 获取审核按钮
     */
    const getApproveValue = async () => {
      if (state.params.status == "3") {
        return;
      }
      let ld = ElLoading.service({target: "#auditDiv", text: "正在加载审核按钮，请稍后...",});
      if (!state.params.Processversion) {
        state.params.Processversion = '1'
      }
      try {
        let res = await axios({
          method: "post",
          url: api.getApproveValue(),
          data: "varJson=" + JSON.stringify(state.params),
        });
        if (res.data && res.data.appValueList) {
          state.buttons = res.data.appValueList;
          ld.close();
        }
      } catch (error) {
        ld.close();
      }
    }

    const close = () => {
      context.emit("close");
    }

    const closeOpinion = () => {
      state.showDialog = false;
    }
    /**
     * 动态按钮事件
     */
    const onOperate = (item) => {
      //业务变量
      if (item.APPROVEVAR == "ywbl") {
        let f = instance.proxy.$refs["busiCom"][item.APPROVEVALUE];
        if (f && f instanceof Function) {
          let valName = item.APPROVENAME;
          state.apprValue = 1;
          state.apprResult = valName;
          f();
        }
        return;
      } else if (item.APPROVENAME === '指派') {
        reassignTask()
      } else {
        let val = item.APPROVEVALUE;
        let valName = item.APPROVENAME;
        console.log('执行了嘛？',val)
        instance.proxy.$refs["busiCom"].saveData(val)
            .then(() => {
              if (state.params.activityId == "new") {
                addTask();
              } else {
                state.apprValue = val;
                state.apprResult = valName;
                if (state.params.activityId == "1" || item.isSubmit) {
                  finishTask();
                } else {
                  state.showDialog = false;
                  nextTick(() => {
                    if (state.apprValue == 0) {
                      state.shTItle = '驳回意见'
                    } else {
                      state.shTItle = '审核意见'
                    }
                    state.showDialog = true;
                  });
                }
              }
            })
            .catch((error) => {
              console.log(error);
            });
      }
    }
    /**
     * 保存
     */
    const onSave = () => {
      instance.proxy.$refs["busiCom"]
          .saveData(null)
          .then(() => {
            ElMessage({
              message: '保存成功！',
              customClass: "myMessageClass",
              type: 'success',
            })
          })
          .catch((error) => {
            console.log(error);
            ElMessage({
              message: '保存失败！',
              customClass: "myMessageClass",
              type: 'error',
            })
          });
    }
    /**
     * 提交
     */
    const onSubmit = () => {
      onOperate({
        APPROVEVALUE: "1",
        APPROVENAME: "同意",
        isSubmit: true,
      });
    }
    /**
     * 创建流程
     */
    const addTask = (sffsdx) => {
      console.log("222222222222222222222222222", state.params)
      let _params = {};
      if (state.params.nextPerformer) { //如果参数里有下节点办理人
        _params.nextPerformer = state.params.nextPerformer;
      }
      if (sffsdx) {
        _params.conditionStr = 'sffsdx=' + sffsdx;
      } else {
        _params.conditionStr = 'sffsdx=0';
      }
      if (state.params.conditionStr) {
        _params.conditionStr = _params.conditionStr + '@' + state.params.conditionStr;
      } else {
        _params.conditionStr = _params.conditionStr + '@lczx=1';
      }
      // _params.nextPerformer = 'admin';

      _params.strSystemCode = state.strSystemCode;
      _params.processId = state.params.processId;
      _params.engineType = state.params.engineType;
      _params.activityId = state.params.activityId;
      _params.processInstanceName = state.params.processInstanceName;
      _params.businessId = state.params.businessId;
      _params.apprValue = "1";
      _params.apprResult = "提交";

      _params.loginName = state.userInfo.userLoginName;
      _params.userOrgId = state.userInfo.orgnaId;

      let ld = ElLoading.service({
        target: "#auditDiv",
        text: "正在提交数据，请稍后...",
      });
      axios({
        method: "post",
        url: api.createTask(),
        data: "varJson=" + JSON.stringify(_params),
      })
          .then((res) => {
            ld.close();
            if (res.data && res.data.result == 1) {
              ElMessage({message: '提交成功！', type: 'success',})
              context.emit("close");
            } else {
              axiosUtil.get('/backend/workFlow/backBusinessStatus', {processId:_params.processId,businessId:_params.businessId})
              ElMessage({message: res.data.error,type: 'error',})
            }
          })
          .catch((error) => {
            axiosUtil.get('/backend/workFlow/backBusinessStatus', {processId:_params.processId,businessId:_params.businessId})
            console.log(error);
            ld.close();
            ElMessage({message: '提交失败！', type: 'error',})
          });
    }
    /**
     * 办理待办
     */
    const finishTask = (shyj, sffsdx ,nextPerformer) => {
      if (!sffsdx) {
        sffsdx = '1';
      }
      state.showDialog = false;
      let _params = {};
      if (state.params.nextPerformer) { //如果参数里有下节点办理人
        _params.nextPerformer = state.params.nextPerformer;
      }
      _params.strSystemCode = state.strSystemCode;
      if (state.apprValue != null) {
        _params.apprValue = state.apprValue;
      } else {
        _params.apprValue = state.params.apprValue;
      }
      _params.apprResult = state.apprResult;
      if (_params.apprResult == '同意') {
        _params.apprResult = '审核通过'
      } else if (_params.apprResult == '不同意') {
        _params.apprResult = '审核驳回'
      }
      // _params.nextPerformer = 'admin';
      _params.opinion = shyj;
      _params.taskId = state.params.taskId;
      _params.processId = state.params.processId;
      _params.activityId = state.params.activityId;
      _params.processInstanceName = state.params.processInstanceName;
      _params.processInstanceId = state.params.processInstanceId;
      _params.engineType = state.params.engineType;
      _params.doType = "send";
      _params.businessId = state.params.businessId;

      _params.loginName = state.userInfo.userLoginName;
      _params.userOrgId = state.userInfo.orgnaId;
      _params.conditionStr = 'sffsdx=' + sffsdx;
      if (state.params.conditionStr) {
        _params.conditionStr = _params.conditionStr + '@' + state.params.conditionStr;
      } else {
        _params.conditionStr = _params.conditionStr + '@lczx=1';
      }
      if(nextPerformer){
        if(_params.nextPerformer){
          nextPerformer.push(_params.nextPerformer)
        }
        _params.nextPerformer=nextPerformer.join(',')
      }
      let ld = ElLoading.service({
        target: "#auditDiv",
        text: "正在提交数据，请稍后...",
      });
      axios({
        method: "post",
        url: api.finishTask(),
        data: "varJson=" + JSON.stringify(_params),
      })
          .then((res) => {
            ld.close();
            if (res.data && res.data.result == 1) {
              ElMessage({message: '提交成功！', type: 'success',})
              context.emit("close");
            }else{
              ElMessage({message: res.data.error,type: 'error',})
            }
          })
          .catch((error) => {
            console.log(error);
            ElMessage({message: '提交失败！', type: 'error',})
            ld.close();
          });
    }
    /**
     * 获取下一节点办理人
     */
    const getNextPerformer = async (varJson) => {
      let ld = ElLoading.service({
        target: "#auditDiv",
        text: "正在获取下一节点办理人，请稍后...",
      });
      try {
        let res = await axios({
          method: "post",
          url: api.getNextPerformer(),
          data: "varJson=" + JSON.stringify(varJson),
        });
        if (res.data && res.data.nextPerformer) {
          ld.close();
          return res.data.nextPerformer;
        }
      } catch (error) {
        console.log(error);
        ld.close();
        return "";
      }
    }

    /**
     * 指派任务
     * @param shyj
     * @param sffsdx
     * @returns {Promise<void>}
     */
    const reassignTask = async (shyj, sffsdx) => {
      if (!sffsdx) {
        sffsdx = '1';
      }
      let _params = {};
      _params.toUser = state.params.nextPerformer;
      _params.toUser = 'dongyingzhao'
      _params.fromUser = state.userInfo.userLoginName;
      _params.taskId = state.params.taskId;
      // _params.engineType = 'h3'
      _params.opinion = shyj;
      _params.taskId = state.params.taskId;
      _params.processId = state.params.processId;
      _params.activityId = state.params.activityId;
      _params.processInstanceName = state.params.processInstanceName;
      _params.processInstanceId = state.params.processInstanceId;
      _params.engineType = state.params.engineType;
      _params.doType = "send";
      _params.businessId = state.params.businessId;
      _params.loginName = state.userInfo.username;
      _params.userOrgId = state.userInfo.orgId;
      _params.sffsdx = sffsdx;
      let ld = ElLoading.service({
        target: "#auditDiv",
        text: "正在提交数据，请稍后...",
      });
      axios({
        method: "post",
        url: api.reassignTask(),
        data: "varJson=" + JSON.stringify(_params),
      }).then((res) => {
        if (res.data && res.data.result == 1) {
          ElMessage({message: '委派成功！', type: 'success',})
          context.emit("close");
          ld.close();
        } else {
          ElMessage({message: '委派失败！', type: 'error',})
          ld.close();
        }
      }).catch((error) => {
        console.log(error);
        ld.close();
      });
    }
    const loadMonitorData = () => {
      let params={
        processId: props.processParams.processId,
        processInstanceId: props.processParams.processInstanceId,
        id: props.processParams.id,
      }
      let ld = ElLoading.service({
        target: "#monitorDiv",
        text: "正在加载数据，请稍后...",
      });
      axiosUtil.post('/backend/workFlow/monitorTasks',params).then((res) => {
        if (res.data) {
          state.tableData = res.data.tasksMonitorList;
          for(let i=0;i<state.tableData.length;i++){
            if(state.tableData[i].STATUS==='已取消'){
              state.tableData[i].STATUS='已撤回'
            }
          }
        }
        ld.close();
      }).catch((error) => {
        console.log(error);
        ld.close();
      });
    }

    const loadPicAndTextData = () => {
      let params={
        processId: props.processParams.processId,
        businessId: props.processParams.businessId,
      }
      axiosUtil.get('/backend/workFlow/getLcsmxx',params).then((res) => {
        state.lcsmxx=res.data
      })
    }

    onMounted(async () => {
      var roleList = state.userInfo.roleList;
      if(roleList){
        for(var i=0;i<roleList.length;i++){
          if(roleList[i].roleCode=='LC_XS_YWFGLD'||roleList[i].roleCode=='LC_XS_ZJL'||roleList[i].roleCode=='LC_XS_SCFGLD'){
            state.isYld=true;
            break;
          }
        }
      }
      loadMonitorData()
      loadPicAndTextData()
      await loadAuditPage();
      await getApproveValue();
    })

    return {
      ...toRefs(state),
      loadAuditPage,
      getApproveValue,
      close,
      closeOpinion,
      onOperate,
      onSave,
      onSubmit,
      addTask,
      finishTask,
      getNextPerformer
    }
  }
})
</script>
<style scoped>
.row-row{
  margin-top: 20px;
  display: flex;
}
.row-title{
  width: 120px;
  font-size: 18px;
  font-weight: bold;
}
.row-context{
  width: calc(100% - 100px);
  font-size: 18px;
}
</style>
