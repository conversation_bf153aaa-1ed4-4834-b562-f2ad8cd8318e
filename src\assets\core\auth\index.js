import {auth} from "../vsui.vue/index.js";
import VSAuth from '../../../lib/vsAuth.js'
import { Cookie } from '@vsui/lib-jsext'



auth.createAuth({

  

  /**
     * 必须实现
     * 当前登录用户名字符串
     * 数据示例："admin",
     */
  getUserName () {
    return VSAuth.getAuthInfo().userName;
  },
    /**
     * 必须实现
     * 当前登录用户名字符串
     * 数据示例："崔良",
     */
   getRealName () {
    return VSAuth.getAuthInfo().realName;
  },
  /**
     * 必须实现
     * 用户的权限完整信息，JSON对象,结构自定义,框架中仅做保存，未使用，以备业务需要
     * 数据示例:{权限JSON对象,结构自定义,框架中仅做保存，未使用，以备业务需要}
     */
  getPermission () {
    return VSAuth.getAuthInfo().permission;
  },
  /**
     *
     * 必须实现
     * 用户资源转换为树状菜单后的对象
     * 结构如下：
     *      [{
    *           resId:"菜单唯一识别符,必选",
    *           iconColor:"#000,代表菜单图标字体颜色，可选",
    *           iconClass:"fa fa-bus 字体字库名称，代表菜单图标，可选",
    *           resPath:"菜单url路径，第一级非必选，二级及以下菜单必选",
    *           resName:"菜单名称文字",
    *           children:[{
                    resId:"菜单唯一识别符,必选",
    *               iconColor:"#000,代表菜单图标字体颜色，可选",
    *               iconClass:"fa fa-bus 字体字库名称，代表菜单图标，可选",
    *               resPath:"菜单url路径，第一级非必选，二级及以下菜单必选",
    *               resName:"菜单名称文字"},
    *               {...},
    *               {...}]
 *          }],
     */
  getModulesTree () {
    
    let tree=VSAuth.getAuthInfo().modulesTree;
    return tree;
  },

  /**
     * 非必须
     * 为使用jwt等模式的登陆时预留
     * 在REST无会话模式下使用的票据信息
     */
  getToken () {
    return ""
  },

})

/**
* 登录函数指向，框架自带了权限中心组件，默认如下：
* 返回为一个Promise对象
* login:(参数按需定义即可)=>{
*  return new Promise((resolve,reject)=>{
*          //这里是登录过程
*      });
* }
* @param {string} username 用户名
* @param {string} password 密码
* @returns {Promise} 返回一个异步对象
*/
auth.login = (username, password) => {
    return new Promise((resolve, reject) => {
    let authInfo=VSAuth.getAuthInfo();
        authInfo.auth.login(username, password).then(() => {
          if (// VSAuth.getConfig().authMode=="SECURITY"||
            VSAuth.getConfig().authMode == 'DEV') {
            Cookie.Cookie.addCookie(Cookie.CookieDef.userName, username, Cookie.CookieDef.hours, Cookie.CookieDef.path, Cookie.CookieDef.domain)
            Cookie.Cookie.addCookie(Cookie.CookieDef.passwd, password, Cookie.CookieDef.hours, Cookie.CookieDef.path, Cookie.CookieDef.domain)
          }
          resolve()
        }).catch(error => {
          reject(error)
        })
    })
},

/**
* 退出函数指向，框架自带了权限中心组件，默认如下：
* 返回为一个Promise对象
* logout:(参数按需定义即可)=>{
*  return new Promise((resolve,reject)=>{
*          //这里是退出过程
*      });
* }
* @returns {Promise} 返回一个异步对象
*
*/
auth.logout = () => {
    return new Promise((resolve, reject) => {
        /* //谐云退出
        let token;
        const tokenReg = /^K[a-zA-Z0-9]*$/;
        const paramTokenReg = new RegExp('(^|&)token=([^&]*)(&|$)', 'i');
        // 从当前域URL地址或localstorage中获取token，URL中token是最新的
        let regExpMatchArray = window.location.search.substr(1).match(paramTokenReg);
        token = regExpMatchArray != null ? decodeURI(regExpMatchArray[2]) : localStorage.getItem('token');
        token = tokenReg.test(token) ? token : '';
        let request = new XMLHttpRequest();
        request.open('DELETE', 'http://gateway.jhof.sinopec.com/kepler/auth/logout', true);

        // 请求头设置请求头key=Authorization,value=${"Bearer " + token}
        request.setRequestHeader('Authorization', 'Bearer ' + token);
        request.send(null);
        request.onreadystatechange = function () {};
 */
        //胜软退出
        VSAuth.getAuthInfo().auth.logout().then(() => {
          if (// VSAuth.getConfig().authMode=="SECURITY"||
            VSAuth.getConfig().authMode == 'DEV') {
            Cookie.Cookie.delCookie(Cookie.CookieDef.userName, Cookie.CookieDef.path, Cookie.CookieDef.domain)
            Cookie.Cookie.delCookie(Cookie.CookieDef.passwd, Cookie.CookieDef.path, Cookie.CookieDef.domain)
            VSAuth.getAuthInfo().auth.toLoginPage()
          }
          else if(VSAuth.getConfig().authMode == 'SIAM'){
          }
          else if(VSAuth.getConfig().authMode == 'CAS'){
          }
          else if(VSAuth.getConfig().authMode == 'SECURITY'){
            VSAuth.getAuthInfo().auth.toLoginPage()
          }
          resolve()
        }).catch(error => {
          reject(error)
        })
    })
}

export default auth;
