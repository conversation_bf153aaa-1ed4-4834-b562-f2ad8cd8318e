<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <div style="display: flex;align-items: center;border-bottom: 1px solid #d5d9e0;margin-bottom: 10px">
        <div style="width: 5px;height: 5px;background-color: #2A96F9;border-radius: 5px"></div>
        <div style="color: black;font-weight: bold;margin-left: 10px">基本信息</div>
      </div>

      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <el-input v-model="formData.XMBH" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16" class="grid-cell">
          <el-form-item label="资金来源：" prop="ZJLYMC">
            <el-input v-model="formData.ZJLYMC" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="主办部门：" prop="SSDWMC">
            <el-input v-model="formData.SSDWMC" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16" class="grid-cell">
          <el-form-item label="项目类别：" prop="XMLB">
            <el-cascader v-model="formData.XMLB" :options="ZRZYTree" filterable :disabled="true"
                         :props="{label:'ZYMC',value:'ZYBM',emitPath: false}"
                         clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标时间：" prop="KBSJ">
            <el-date-picker
                v-model="formData.KBSJ"
                :disabled="true"
                type="datetime"
                clearable
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>

        </el-col>
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标地点：" prop="PBDD">
            <el-input v-model="formData.PBDD" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <div v-for="(item,index) in stepTypes" :key="index" style="margin-top: 20px">
        <div style="display: flex;align-items: center;border-bottom: 1px solid #d5d9e0;margin-bottom: 20px">
          <div style="width: 5px;height: 5px;background-color: #2A96F9;border-radius: 5px"></div>
          <div style="color: black;font-weight: bold;margin-left: 10px">{{ item.type }}</div>
        </div>
        <el-row :gutter="0" class="grid-row">
          <el-col :span="12" class="grid-cell" v-for="(ii,dex) in stepTypes[index].steps" :key="dex">
            <el-form-item :label="`${ii.label}：`" prop="XMMC">
              <el-button size="small" link type="primary" @click="workEdit(formData,item.key,ii.key)">
                【{{ getButtonLabel(ii, index) }}】
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import tabFun from "@lib/tabFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      rules: {},
      ZRZYTree: [],
      stepTypes: [
        {
          type: '评审会准备',
          key: 'PSHZB',
          steps: [
            {label: '签到表', key: 'QDB', index: 0},
            {label: '响应文件解密', key: 'XYWJJM', index: 1},
            {label: '评标或最高限价', key: 'PBHZGXJ', index: 2},
          ]
        },
        {
          type: '评审',
          key: 'PS',
          steps: [
            {label: '设置评标会密码', key: 'SZPBHMM', index: 3},
            {label: '评标小组签到', key: 'PBXZQD', index: 4},
          ]
        },
        {
          type: '成交',
          key: 'CJ',
          steps: [
            {label: '商务最终报价确认单', key: 'SWZZBJQRD', index: 5},
            {label: '商务洽谈备忘录', key: 'SWQTBWL', index: 6},
          ]
        }
      ]
    })


    const getZrzyList = () => {
      let params = {}
      axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
        state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0')
      });
    }

    const getButtonLabel = (item,index) => {
      return '未完成'
    }

    const workEdit = (row, stepType,busType) => {
      console.log('workEdit',row, stepType,busType)
      let params={
        id: row.KPBYXID || comFun.newId(),
        JLID: row.JLID,
        operation: row.KPBYXID ? 'edit' : 'add',
        role: 'ZCR',
        busType: busType,
      }
      params.type='DJTP'
      tabFun.addTabByRoutePath('现场评审','/zbxs/kpbdtPage',{params: params},{})

    }

    onMounted(() => {
      getZrzyList()
    })

    return {
      ...toRefs(state),
      workEdit,
      getButtonLabel

    }
  }

})
</script>

<style scoped>

</style>
