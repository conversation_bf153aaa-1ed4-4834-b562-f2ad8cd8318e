import {v4 as getUUID} from 'uuid'
import {axios} from "../assets/core";
const CryptoJS =require("crypto-js")

/**
 * 工具类
 */
export default {
    isNumber(val) {
        if (val === "" || val == null) {
            return false;
        }
        if (!isNaN(val)) {
            //对于空数组和只有一个数值成员的数组或全是数字组成的字符串，isNaN返回false，例如：'123'、[]、[2]、['123'],isNaN返回false,
            //所以如果不需要val包含这些特殊情况，则这个判断改写为if(!isNaN(val) && typeof val === 'number' )
            return true;
        } else {
            return false;
        }
    },
    checkRepeat(objArray, field, excludeField, excludeValue) {
        var result = {flag: false, type: ''};
        var existMap = {};
        for (var i = 0; i < objArray.length; i++) {
            if (excludeField) {
                if (objArray[i][excludeField] != excludeValue) {
                    if (objArray[i][field]) {
                        if (existMap[objArray[i][field]] == undefined) {
                            existMap[objArray[i][field]] = '1';
                        } else {
                            result.flag = true;
                            result.type = '1';//证书代码重复
                            break;
                        }
                        if (objArray[i]["YXQQSRQ"] && objArray[i]["YXQZZRQ"]) {
                            if (objArray[i]["YXQQSRQ"] > objArray[i]["YXQZZRQ"]) {
                                result.flag = true;
                                result.type = '3';//有效期起始日期不能大于有效期终止日期
                                break;
                            }
                        }
                    } else {
                        result.flag = true;
                        result.type = '2'; //证书类别代码为空
                        break;
                    }
                }
            } else {
                if (existMap[objArray[i][field]] == undefined) {
                    existMap[objArray[i][field]] = '1';
                } else {
                    result.flag = true;
                    break;
                }
            }
        }
        return result;
    },
    /**
     * 获取当前日期
     */
    getNowDate() { //获取当前日期
        const now = new Date()
        const year = now.getFullYear()  //年
        const month = now.getMonth() + 1 //月
        const day = now.getDate()    //日
        const hours = now.getHours()  //小时数
        const minutes = now.getMinutes()  //分钟数
        const seconds = now.getSeconds()  //秒数
        const week = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][now.getDay()]  //星期
        //想展示什么  对应的展示即可
        return year + '-' + this.formatTime(month) + '-' + this.formatTime(day);
    },

    /**
     * 获取当前年月
     * @param split 分隔符号，默认-
     * @returns {string}
     */
    getNowMonth(split='-'){
        const now = new Date()
        const year = now.getFullYear()  //年
        const month = now.getMonth() + 1 //月
        return year + split + this.formatTime(month);
    },

    addDate(num) { //当前日期计算
        var now = new Date();
        now = now.valueOf();
        now = now + num * 24 * 60 * 60 * 1000; //备注 如果是往前计算日期则为减号 否则为加号
        now = new Date(now);
        const year = now.getFullYear()  //年
        const month = now.getMonth() + 1 //月
        const day = now.getDate()    //日
        const hours = now.getHours()  //小时数
        const minutes = now.getMinutes()  //分钟数
        const seconds = now.getSeconds()  //秒数
        const week = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][now.getDay()]  //星期
        //想展示什么  对应的展示即可
        return year + '-' + this.formatTime(month) + '-' + this.formatTime(day);
    },
    refDate(date, num) { //指定日期计算
        var now = new Date(date);
        now = now.valueOf();
        now = now + num * 24 * 60 * 60 * 1000; //备注 如果是往前计算日期则为减号 否则为加号
        now = new Date(now);
        const year = now.getFullYear()  //年
        const month = now.getMonth() + 1 //月
        const day = now.getDate()    //日
        const hours = now.getHours()  //小时数
        const minutes = now.getMinutes()  //分钟数
        const seconds = now.getSeconds()  //秒数
        const week = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][now.getDay()]  //星期
        //想展示什么  对应的展示即可
        return year + '-' + this.formatTime(month) + '-' + this.formatTime(day);
    },

    formatTime(time) {
        return time < 10 ? '0' + time : time
    },
    /**
     * 获取当前时间
     */
    getNowTime() { //获取当前实践
        const now = new Date()
        const year = now.getFullYear()  //年
        const month = now.getMonth() + 1 //月
        const day = now.getDate()    //日
        const hours = now.getHours()  //小时数
        const minutes = now.getMinutes()  //分钟数
        const seconds = now.getSeconds()  //秒数
        const week = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][now.getDay()]  //星期
        //想展示什么  对应的展示即可
        return year + '-' + this.formatTime(month) + '-' + this.formatTime(day) + ' ' + this.formatTime(hours) + ':' + this.formatTime(minutes) + ':' + this.formatTime(seconds);
    },


    getStringDay(date){
        const now = date
        const year = now.getFullYear()  //年
        const month = now.getMonth() + 1 //月
        const day = now.getDate()    //日
        //想展示什么  对应的展示即可
        return year + '-' + this.formatTime(month) + '-' + this.formatTime(day);
    },



    /**
     * 获取差几天时间
     */
    getDateAddTime(num) { //获取差几天当时间(返回值是DATE)
        var curDate = new Date();
        curDate = curDate.valueOf();
        curDate = curDate + num * 24 * 60 * 60 * 1000; //备注 如果是往前计算日期则为减号 否则为加号
        curDate = new Date(curDate);
        return curDate;
    },

    getDateStr(date) {
        var date = new Date(date);
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        if (month.length == 1) {
            month = "0" + month;
        }
        if (day.length == 1) {
            day = "0" + day;
        }
        var d = year + "-" + month + "-" + day;
        return d;
    },
    /**
     * 根据时间戳，生成ID
     */
    newId() { //生成ID
        return getUUID().replace(/-/g, '').toUpperCase()
    },

    // 判断身份证号码
    validateIdNo(rule, value, callback) {
        const reg = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[X])$)$/;
        if (value == '' || value == undefined || value == null) {
            callback();
        } else {
            if ((!reg.test(value)) && value != '') {
                callback(new Error('请输入正确的身份证号码'));
            } else {
                callback();
            }
        }
    },

    /* 判断手机号码*/
    validatePhone(rule, value, callback) {
        const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        if (value == '' || value == undefined || value == null) {
            callback();
        } else {
            if ((!reg.test(value)) && value != '') {
                callback(new Error('请输入正确的电话号码'));
            } else {
                callback();
            }
        }
    },

	/* 判断邮箱*/
	validateEMail(rule, value, callback) {
		const reg = /^([a-zA-Z0-9]+[-_\.]?)+@[a-zA-Z0-9]+\.[a-z]+$/;
		if (value == '' || value == undefined || value == null) {
			callback();
		} else {
			if (!reg.test(value)) {
				callback(new Error('请输入正确的邮箱地址'));
			} else {
				callback();
			}
		}
	},
	/*  返回树*/
	transData(a, idStr, pidStr, childrenStr) {
		var r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
		for (; i < len; i++) {
			a[i]['isparent'] = false;
			hash[a[i][id]] = a[i];
		}
		for (; j < len; j++) {
			var aVal = a[j], hashVP = hash[aVal[pid]];
			if (hashVP) {
				!hashVP[children] && (hashVP[children] = []);
				hashVP[children].push(aVal);
			} else {
				r.push(aVal);
			}
		}
		// this.setIsParent(r);
		return r;
	},
	//校验数字（正负整数0，以及正负小数）
	// isNumber(val){
	// 	var regPos= /^[0-9]+.?[0-9]*/
	// 	if(regPos.test(val)){
	// 		return true;
	// 	}else{
	// 		return false;
	// 	}
	// },
	downLoadTemplate(fileName) {
		axios({
		  method: 'POST',
		  url: '/backend/basecommon/downLoadModel',
		  data: {fileName:fileName},
		  responseType: 'arraybuffer'
		}).then(response => {
		  let url = window.URL.createObjectURL(new Blob([response.data]))
		  let a = document.createElement('a')
		  a.setAttribute("download",fileName);
		  a.href = url
		  a.click();
		});
	  },
      /**
     * 数据转树型结构
     * @param data
     * @param id id名称
     * @param parentId 父id名称
     * @param childName 生成的子节点名称
     * @param root 根节点的值
     * @returns {*}
     */
    treeData(data, id, parentId, childName, root) {
        let cloneData = data
        return cloneData.filter((father) => {
            father[childName] = cloneData.filter((child) => {
                return father[id] === child[parentId]
            })
            return father[parentId] === root
        })
    },
    getParentIdArr(id,data,idKey = 'id',pidKey="pid"){
        function foo(data) {
          for(var k in data) {
            line[data[k][idKey]] = data[k];
            if(data[k].children) {
               foo(data[k].children);
            }
          }
        }
        function getParents(id) {
            var res = [];
            if(!line[id]) return res;
            res.push(line[id])
            return res.concat( getParents(line[id][pidKey]) );
        }
        var line = {};
        foo(data)
        // console.log(line); //穿线
        var r = getParents(id);
        // console.log(r);
        // console.log(r.reverse(),r);
        return r.reverse()
    },
    /**
     * 数字转大写
     * @param num
     * @returns {string}
     */
    uppercase(num) {
        let minus = "";
        let m_str = "";
        const text = num.toString();
        const zh = '零壹贰叁肆伍陆柒捌玖';
        if (text.indexOf("-") === 0) {
            num = text.replace("-", "");
            minus = "负"
        }
        let money_num = Number(num);
        let money = Math.round(money_num * 100).toString(10);
        const len = money.length;
        for (let i = 0; i < len; i++) {
            m_str = m_str + zh.charAt(parseInt(money.charAt(i))) + this.sitToUnit(len - i - 1)
        }
        m_str = m_str.replace("零分", "");
        m_str = m_str.replace("零角", "零");
        let yy = 0;
        while (true) {
            let len = m_str.length;
            m_str = m_str.replace("零元", "元");
            m_str = m_str.replace("零万", "万");
            m_str = m_str.replace("零亿", "亿");
            m_str = m_str.replace("零仟", "零");
            m_str = m_str.replace("零佰", "零");
            m_str = m_str.replace("零零", "零");
            m_str = m_str.replace("零拾", "零");
            m_str = m_str.replace("亿万", "亿零");
            m_str = m_str.replace("万仟", "万零");
            m_str = m_str.replace("仟佰", "仟零");
            yy = m_str.length;
            if (yy === len) {
                break
            }
        }
        yy = m_str.length;
        if (m_str.charAt(yy - 1) === "零") {
            m_str = m_str.substring(0, yy - 1)
        }
        yy = m_str.length;
        if (m_str.charAt(yy - 1) === "元") {
            // m_str = m_str + "整"
        }
        return minus + m_str
    },
    sitToUnit(a) {
        if (a > 10) {
            a = a - 8;
            return (this.sitToUnit(a))
        }
        const zh = '分角元拾佰仟万拾佰仟亿';
        return zh.charAt(a)
    },
    desEncode(str, key){
        var keyHex = CryptoJS.enc.Utf8.parse(key);
        var encrypted = CryptoJS.DES.encrypt(str, keyHex, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });
        return encrypted.toString();
    },

    // 两个集合合并，并去重
    mergeObjectsAndDeduplicateById(arr1, arr2, id) {
        const map = new Map();
        [...arr1, ...arr2].forEach(item => {
            if (!map.has(item[id])) {
                map.set(item[id], item);
            }
        });
        return Array.from(map.values());
    },

}
