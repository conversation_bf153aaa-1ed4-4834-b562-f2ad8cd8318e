<template>
  <div class="context">
    <el-button size="default" type="success" style="float: right" @click="makeBg" :loading="loading || making">生成报告</el-button>
    <banner id="bgBanner"/>
    <div class="A4-context" id="pdf-context" :key="uuid">
      <div class="title-text">股权穿透分析报告</div>
      <div class="describe-text">说明：本报告仅为基于提供公司名称和穿透参数设置进行的技术分析结果，仅供参考，不作为任何法律依据。</div>
      <div class="title1-text">摘要：</div>

      <div class="label-row">
        <div class="label-col">
          标题：{{formData.JCXX.BT}}
        </div>
        <div class="label-col">
          公司数量：{{formData.JCXX.QYSL}}
        </div>
        <div class="label-col">
          穿透分析人：{{formData.JCXX.CJRXM}}
        </div>
      </div>


      <div class="label-row">
        <div class="label-col">
          穿透分析时间：{{formData.JCXX.XGSJ}}
        </div>
        <div class="label-col">
          报告生成时间：{{formData.JCXX.CJSJ}}
        </div>
      </div>

      <div class="title1-text">股权穿透分析清单：</div>

      <div class="table-head">
        <div class="table-column" style="width: 6mm">序号</div>
        <div class="table-column" style="flex: 1">原公司名称</div>
        <div class="table-column" style="flex: 1">穿透公司名称</div>

        <div class="table-column-span">
          <div class="table-column">股权关系</div>
          <div class="table-column-head">
            <div class="table-column" style="width: 14mm;">路径</div>
            <div class="table-column" style="width: 15mm;">控股</div>
          </div>
        </div>

        <div class="table-column" style="width: 18mm;">任职关系</div>
        <div class="table-column" style="width: 14mm;">股权任职关系</div>
      </div>

      <div class="table-row pdf-details" v-for="(item,index) in formData.CCQDList" :key="index">
        <div class="table-column" style="width: 6mm">{{ index + 1 }}</div>
        <div class="table-column" style="flex: 1">{{ item.YGSMC }}</div>
        <div class="table-column" style="flex: 1">{{ item.CTGSMC }}</div>
        <div class="table-column" style="width: 14mm;">{{ item.LJ }}</div>
        <div class="table-column" style="width: 15mm;">{{ item.KG }}</div>

        <div class="table-column" style="width: 18mm;">{{ item.RZGX }}</div>
        <div class="table-column" style="width: 14mm;">{{ item.GQRZGX }}</div>
      </div>


      <div class="title1-text pdf-details" style="margin-top: 5mm">详情：</div>

      <div style="margin-bottom: 4mm" v-for="(item,index) in formData.CCQDList" :key="index">
        <div class="title2-text pdf-details">
          {{ index + 1 }}.原公司名称：{{ item.YGSMC }}&nbsp;&nbsp;VS&nbsp;&nbsp;穿透公司名称：{{ item.CTGSMC }}
        </div>
        <div v-for="(ii,ind) in GQCTSZOptions" :key="ind">
          <div class="table-row pdf-details">
            <div v-if="ii.DMXX=='GQGX'" >
              >&nbsp;&nbsp; <span style="color: #1B5FBC;">{{ ii.DMMC }}</span> &nbsp;&nbsp;关系路径：{{item.LJ}} &nbsp;控股路径：{{item.KG}}
            </div>
            <div v-if="ii.DMXX=='RZGX'" >
              >&nbsp;&nbsp;<span style="color: #1B5FBC;">{{ ii.DMMC }}</span> &nbsp;&nbsp;&nbsp;关系路径：{{item.RZGX}}
            </div>
            <div v-if="ii.DMXX=='GQRZGX'"  >
              >&nbsp;&nbsp; <span style="color: #1B5FBC;">{{ ii.DMMC }}</span> &nbsp;&nbsp;&nbsp;关系路径：{{item.GQRZGX}}
            </div>
          </div>
          <div class="table-row pdf-details" >
            <dbxqTab :params="{GQCTJLID:item.GQCTJLID,FXQDID:item.FXQDID,DMXX:ii.DMXX,INDEX:index}" :v-model="{GQCTJLID:item.GQCTJLID,FXQDID:item.FXQDID,DMXX:ii.DMXX,INDEX:index}"/>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import banner from "@views/gqct/gqctgl/gqctbg/banner";
import axiosUtil from "@lib/axiosUtil";
import htmlPdf from "@lib/pdf";
import comFun from "@lib/comFun";
import dbxqTab from "@views/gqct/gqctgl/gqctbg/gqdbbgChat";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {banner,dbxqTab},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: true,
      GQCTJLID: props.params.GQCTJLID,
      uuid: comFun.newId(),
      formData: {
        CCQDList: [],
        JCXX:{

        },
      },
      GQCTSZOptions: [],

      making: false
    })

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res => {
        state[resList] = res.data || []
        state.loading = false
      })
    }
    const getGqctBgData = () => {
      let params = {
        GQCTJLID:props.params.id
      }
      axiosUtil.get('/backend/gqct/gqctgl/getGqctBgData', params).then(res => {
        state.formData.CCQDList = res.data.list || []
        state.formData.JCXX = res.data || []
        state.loading = false
      })
    }
    const pdfFunc = () => {
      if (state.loading) {
        return Promise.reject('数据加载中,请稍后尝试')
      }
      const labelList = document.getElementsByClassName('pdf-details');
      const banner = document.getElementById('bgBanner');
      return new Promise(resolve => {
        htmlPdf('股权穿透分析报告', document.getElementById('pdf-context'), labelList, null, false, banner, '西北油田市场管理信息系统').then(res => {
          state.uuid = comFun.newId()
          resolve(res)
        })
      })
    }

    const makeBg = () => {
      state.making=true
      pdfFunc().then(res=>{
        ElMessage.success('已下载报告')
        state.making=false
      })
    }

    onMounted(() => {
      getDMBData('GQCTSZ', 'GQCTSZOptions')
      getGqctBgData()

    })

    return {
      ...toRefs(state),
      pdfFunc,
      makeBg

    }
  }

})
</script>

<style scoped>
.context {
  padding: 20px;
}

.A4-context {
  /*height: 297mm;*/
  width: 210mm;
  border: 1px solid #d5d0d0;
  font-size: 3mm;
  color: black;
}

.title-text {
  width: 100%;
  font-size: 6mm;
  text-align: center;
  margin-top: 1mm;
  margin-bottom: 2mm;
}

.describe-text {
  width: 100%;
  text-align: center;
  margin-bottom: 5mm;
}

.title1-text {
  margin-left: 5mm;
  font-weight: bolder;
  margin-bottom: 5mm;
  font-size: 3.5mm;
}

.label-row {
  display: flex;
  padding: 0 10mm;
  justify-content: space-between;
  margin-bottom: 4mm;
}

.table-head {
  display: flex;
  border-left: 1px solid #e0e3e8;
  border-top: 1px solid #e0e3e8;
  margin: 0 10mm;
  font-weight: bold;
}

.table-column {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 4mm;
  border-bottom: 1px solid #e0e3e8;
  border-right: 1px solid #e0e3e8;
  padding: 1mm;
  background-color: white;
}

.table-column-span {
  flex-direction: column;
}

.table-column-head {
  display: flex;
}

.table-row {
  display: flex;
  border-left: 1px solid #e0e3e8;
  margin: 0 10mm;
}

.title2-text {
  margin-left: 10mm;
  font-weight: bolder;
}

.title3-text {
  margin-top: 5mm;
  display: flex;
  color: #2A96F9;
  margin-left: 15mm;
  margin-bottom: 4mm;
}
</style>
