import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"


// 市场承包商管理-队伍日常管理-队伍状态变更列表
// @method getDwrcglDwztbgPage
// @type get
// @return url
//getDwrcglDwztbgPage: `/sccbsgl/dwrcgl/dwztbg/page`,

// eslint-disable-next-line
export function getDwrcglDwztbgPage(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/dwrcgl/dwztbg/page`, params)
}
// 市场承包商管理-队伍日常管理-队伍专业变更保存
// @method postDwrcglDwzybg
// @type post
// @return url
//postDwrcglDwzybg: `/sccbsgl/dwrcgl/dwzybg`,

// eslint-disable-next-line
export function postDwrcglDwzybg(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/dwrcgl/dwzybg`, params)
}
// 市场承包商管理-队伍日常管理-队伍超期预警列表
// @method getDwrcglDwcqyj
// @type get
// @return url
//getDwrcglDwcqyj: `/sccbsgl/dwrcgl/dwcqyj`,

// eslint-disable-next-line
export function getDwrcglDwcqyj(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/dwrcgl/dwcqyj`, params)
}
// 市场承包商管理-队伍日常管理-队伍延期期限
// @method postDwrcglDwyqqx
// @type post
// @return url
//postDwrcglDwyqqx: `/sccbsgl/dwrcgl/dwyqqx`,

// eslint-disable-next-line
export function postDwrcglDwyqqx(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/dwrcgl/dwyqqx`, params)
}
// 市场承包商管理-队伍日常管理-队伍专业变更列表
// @method getDwrcglDwzybglb
// @type get
// @return url
//getDwrcglDwzybglb: `/sccbsgl/dwrcgl/dwzybglb`,

// eslint-disable-next-line
export function getDwrcglDwzybglb(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/dwrcgl/dwzybglb`, params)
}
// 市场承包商管理-队伍日常管理-队伍状态变更保存
// @method postDwrcglDwztbg
// @type post
// @return url
//postDwrcglDwztbg: `/sccbsgl/dwrcgl/dwztbg`,

// eslint-disable-next-line
export function postDwrcglDwztbg(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/dwrcgl/dwztbg`, params)
}
// 市场承包商管理-审核-退回
// @method postExamineSendBack
// @type post
// @return url
//postExamineSendBack: `/sccbsgl/examine/sendBack`,

// eslint-disable-next-line
export function postExamineSendBack(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/examine/sendBack`, params)
}
// 市场承包商管理-审核-查看
// @method getExamineView
// @type get
// @return url
//getExamineView: `/sccbsgl/examine/view`,

// eslint-disable-next-line
export function getExamineView(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/examine/view`, params)
}
// 市场承包商管理-审核-通过审核
// @method postExamineApproved
// @type post
// @return url
//postExamineApproved: `/sccbsgl/examine/approved`,

// eslint-disable-next-line
export function postExamineApproved(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/examine/approved`, params)
}
// 市场承包商管理-审核-注册审核列表
// @method getExamineRegisterListPage
// @type get
// @return url
//getExamineRegisterListPage: `/sccbsgl/examine/registerListPage`,

// eslint-disable-next-line
export function getExamineRegisterListPage(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/examine/registerListPage`, params)
}
// 市场承包商管理-专业管理-列表查询
// @method getZyglZyglList
// @type get
// @return url
//getZyglZyglList: `/sccbsgl/zygl/zyglList`,

// eslint-disable-next-line
export function getZyglZyglList(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zygl/zyglList`, params)
}
// 市场承包商管理-专业管理-根据角色查询人员
// @method getZyglGetUserByRole
// @type get
// @return url
//getZyglGetUserByRole: `/sccbsgl/zygl/getUserByRole`,

// eslint-disable-next-line
export function getZyglGetUserByRole(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zygl/getUserByRole`, params)
}
// 市场承包商管理-专业管理-专业导出
// @method getZyglExport
// @type downloadFile
// @return url
//getZyglExport: `/sccbsgl/zygl/export`,

// eslint-disable-next-line
export function getZyglExport(params,fileName) {
    return axiosUtil.downloadFile(`${baseUrl}/sccbsgl/zygl/export`, params,fileName)
}
// 市场承包商管理-专业管理-新增
// @method postZyglSaveZygl
// @type post
// @return url
//postZyglSaveZygl: `/sccbsgl/zygl/saveZygl`,

// eslint-disable-next-line
export function postZyglSaveZygl(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/zygl/saveZygl`, params)
}
// 市场承包商管理-主数据管理-子级单位管理-子级单位保存(新增和编辑(编辑时DWWYBS不为空))
// @method postZjdwglSave
// @type post
// @return url
//postZjdwglSave: `/sccbsgl/zsjgl/zjdwgl/save`,

// eslint-disable-next-line
export function postZjdwglSave(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/zsjgl/zjdwgl/save`, params)
}
// 市场承包商管理-主数据管理-子级单位管理-子级单位查询
// @method getZjdwglQuery
// @type get
// @return url
//getZjdwglQuery: `/sccbsgl/zsjgl/zjdwgl/query`,

// eslint-disable-next-line
export function getZjdwglQuery(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zsjgl/zjdwgl/query`, params)
}
// 市场承包商管理-主数据管理-子级单位管理-子级单位导出
// @method getZjdwglExport
// @type downloadFile
// @return url
//getZjdwglExport: `/sccbsgl/zsjgl/zjdwgl/export`,

// eslint-disable-next-line
export function getZjdwglExport(params,fileName) {
    return axiosUtil.downloadFile(`${baseUrl}/sccbsgl/zsjgl/zjdwgl/export`, params,fileName)
}
// 市场承包商管理-主数据管理-子级单位管理-根据id删除
// @method deleteZjdwglRemove
// @type del
// @return url
//deleteZjdwglRemove: `/sccbsgl/zsjgl/zjdwgl/remove/${params.id}/${params.status}`,

// eslint-disable-next-line
export function deleteZjdwglRemove(params) {
    return axiosUtil.del(`${baseUrl}/sccbsgl/zsjgl/zjdwgl/remove/${params.id}/${params.status}`, params)
}
// 市场承包商管理-承包商变更-企业信息变更保存
// @method postBgCbsjbxx
// @type post
// @return url
//postBgCbsjbxx: `/sccbsgl/bg/cbsjbxx`,

// eslint-disable-next-line
export function postBgCbsjbxx(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/bg/cbsjbxx`, params)
}
// 市场承包商管理-承包商变更-队伍发起变更后获取当前过程队伍信息
// @method getBgGetChangedTeamById
// @type get
// @return url
//getBgGetChangedTeamById: `/sccbsgl/bg/getChangedTeamById`,

// eslint-disable-next-line
export function getBgGetChangedTeamById(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/bg/getChangedTeamById`, params)
}
// 市场承包商管理-承包商变更-企业信息变更编辑
// @method getBgCbsqyxx
// @type get
// @return url
//getBgCbsqyxx: `/sccbsgl/bg/cbsqyxx`,

// eslint-disable-next-line
export function getBgCbsqyxx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/bg/cbsqyxx`, params)
}
// 市场承包商管理-承包商变更-队伍变更列表
// @method getBgCbsdwxx
// @type get
// @return url
//getBgCbsdwxx: `/sccbsgl/bg/cbsdwxx`,

// eslint-disable-next-line
export function getBgCbsdwxx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/bg/cbsdwxx`, params)
}
// 市场承包商管理-承包商变更-队伍信息变更保存
// @method postBgCbsdwxx
// @type post
// @return url
//postBgCbsdwxx: `/sccbsgl/bg/cbsdwxx`,

// eslint-disable-next-line
export function postBgCbsdwxx(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/bg/cbsdwxx`, params)
}
// 市场承包商管理-查询分析-队伍明细导出
// @method getReportDwmxExport
// @type downloadFile
// @return url
//getReportDwmxExport: `/sccbsgl/report/dwmx/export`,

// eslint-disable-next-line
export function getReportDwmxExport(params,fileName) {
    return axiosUtil.downloadFile(`${baseUrl}/sccbsgl/report/dwmx/export`, params,fileName)
}
// 市场承包商管理-查询分析-队伍统计汇总导出
// @method getReportDwtjhzExport
// @type downloadFile
// @return url
//getReportDwtjhzExport: `/sccbsgl/report/dwtjhz/export`,

// eslint-disable-next-line
export function getReportDwtjhzExport(params,fileName) {
    return axiosUtil.downloadFile(`${baseUrl}/sccbsgl/report/dwtjhz/export`, params,fileName)
}
// 市场承包商管理-查询分析-队伍超期预警_乙方用_导出
// @method getReportDwcqyjExport
// @type downloadFile
// @return url
//getReportDwcqyjExport: `/sccbsgl/report/dwcqyj/export`,

// eslint-disable-next-line
export function getReportDwcqyjExport(params,fileName) {
    return axiosUtil.downloadFile(`${baseUrl}/sccbsgl/report/dwcqyj/export`, params,fileName)
}
// 市场承包商管理-查询分析-队伍超期预警_乙方用
// @method getReportDwcqyj
// @type get
// @return url
//getReportDwcqyj: `/sccbsgl/report/dwcqyj`,

// eslint-disable-next-line
export function getReportDwcqyj(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/report/dwcqyj`, params)
}
// 市场承包商管理-查询分析-队伍明细
// @method getReportDwmx
// @type get
// @return url
//getReportDwmx: `/sccbsgl/report/dwmx`,

// eslint-disable-next-line
export function getReportDwmx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/report/dwmx`, params)
}
// 市场承包商管理-查询分析-队伍统计汇总
// @method getReportDwtjhz
// @type get
// @return url
//getReportDwtjhz: `/sccbsgl/report/dwtjhz`,

// eslint-disable-next-line
export function getReportDwtjhz(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/report/dwtjhz`, params)
}
// 市场承包商管理-查询分析-企业明细导出
// @method getReportQymxExport
// @type downloadFile
// @return url
//getReportQymxExport: `/sccbsgl/report/qymx/export`,

// eslint-disable-next-line
export function getReportQymxExport(params,fileName) {
    return axiosUtil.downloadFile(`${baseUrl}/sccbsgl/report/qymx/export`, params,fileName)
}
// 市场承包商管理-查询分析-企业明细
// @method getReportQymx
// @type get
// @return url
//getReportQymx: `/sccbsgl/report/qymx`,

// eslint-disable-next-line
export function getReportQymx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/report/qymx`, params)
}
// 市场承包商管理-承包商引进-队伍信息结果-根据队伍id查询专业明细
// @method getTeamreslutGetProDetails
// @type get
// @return url
//getTeamreslutGetProDetails: `/sccbsgl/cbsyj/teamreslut/getProDetails`,

// eslint-disable-next-line
export function getTeamreslutGetProDetails(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/teamreslut/getProDetails`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-根据队伍业务id查询队伍结果信息
// @method getCbsyjTeamresultbyid
// @type get
// @return url
//getCbsyjTeamresultbyid: `/sccbsgl/cbsyj/teamresultbyid`,

// eslint-disable-next-line
export function getCbsyjTeamresultbyid(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/teamresultbyid`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-查询组织机构
// @method getCbsyjGetEntOrganizations
// @type get
// @return url
//getCbsyjGetEntOrganizations: `/sccbsgl/cbsyj/getEntOrganizations`,

// eslint-disable-next-line
export function getCbsyjGetEntOrganizations(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getEntOrganizations`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-根据队伍id查询专业明细
// @method getCbsyjGetProDetails
// @type get
// @return url
//getCbsyjGetProDetails: `/sccbsgl/cbsyj/getProDetails`,

// eslint-disable-next-line
export function getCbsyjGetProDetails(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getProDetails`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-承包商队伍选择专业列表查询
// @method getCbsyjGetTeamSpecials
// @type get
// @return url
//getCbsyjGetTeamSpecials: `/sccbsgl/cbsyj/getTeamSpecials`,

// eslint-disable-next-line
export function getCbsyjGetTeamSpecials(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getTeamSpecials`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-队伍信息保存
// @method postCbsyjSaveTeam
// @type post
// @return url
//postCbsyjSaveTeam: `/sccbsgl/cbsyj/saveTeam`,

// eslint-disable-next-line
export function postCbsyjSaveTeam(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveTeam`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-根据队伍业务id查询队伍信息
// @method getCbsyjTeambyid
// @type get
// @return url
//getCbsyjTeambyid: `/sccbsgl/cbsyj/teambyid`,

// eslint-disable-next-line
export function getCbsyjTeambyid(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/teambyid`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-模版明细按多个模版id查询
// @method getCbsyjTempletequerys
// @type get
// @return url
//getCbsyjTempletequerys: `/sccbsgl/cbsyj/templetequerys`,

// eslint-disable-next-line
export function getCbsyjTempletequerys(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/templetequerys`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-查询队伍结果信息
// @method getCbsyjGetTeamResultInfo
// @type get
// @return url
//getCbsyjGetTeamResultInfo: `/sccbsgl/cbsyj/getTeamResultInfo`,

// eslint-disable-next-line
export function getCbsyjGetTeamResultInfo(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getTeamResultInfo`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-根据队伍id查询承包商虚拟队伍信息
// @method getCbsyjGetCbsTeamById
// @type get
// @return url
//getCbsyjGetCbsTeamById: `/sccbsgl/cbsyj/getCbsTeamById`,

// eslint-disable-next-line
export function getCbsyjGetCbsTeamById(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getCbsTeamById`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-队伍信息删除
// @method deleteCbsyjTeamdelete
// @type del
// @return url
//deleteCbsyjTeamdelete: `/sccbsgl/cbsyj/teamdelete/${params.dwid}`,

// eslint-disable-next-line
export function deleteCbsyjTeamdelete(params) {
    return axiosUtil.del(`${baseUrl}/sccbsgl/cbsyj/teamdelete/${params.dwid}`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-队伍信息列表
// @method getCbsyjTeamlist
// @type get
// @return url
//getCbsyjTeamlist: `/sccbsgl/cbsyj/teamlist`,

// eslint-disable-next-line
export function getCbsyjTeamlist(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/teamlist`, params)
}
// 市场承包商管理-承包商引进-队伍信息-列表-查询队伍信息
// @method getCbsyjGetTeamInfo
// @type get
// @return url
//getCbsyjGetTeamInfo: `/sccbsgl/cbsyj/getTeamInfo`,

// eslint-disable-next-line
export function getCbsyjGetTeamInfo(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getTeamInfo`, params)
}
// 市场承包商管理-承包商引进-引入申请信息-查询引入申请信息
// @method getCbsyjGetYrsqxx
// @type get
// @return url
//getCbsyjGetYrsqxx: `/sccbsgl/cbsyj/getYrsqxx`,

// eslint-disable-next-line
export function getCbsyjGetYrsqxx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getYrsqxx`, params)
}
// 市场承包商管理-承包商引进-引入申请信息-查询承包商业务信息草稿
// @method getCbsyjGetYwxxCg
// @type get
// @return url
//getCbsyjGetYwxxCg: `/sccbsgl/cbsyj/getYwxxCg`,

// eslint-disable-next-line
export function getCbsyjGetYwxxCg(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getYwxxCg`, params)
}
// 市场承包商管理-承包商引进-选择专业-生成承包商信息和虚拟队伍信息
// @method postCbsyjGenerateCbsAndDwxx
// @type post
// @return url
//postCbsyjGenerateCbsAndDwxx: `/sccbsgl/cbsyj/generateCbsAndDwxx`,

// eslint-disable-next-line
export function postCbsyjGenerateCbsAndDwxx(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/generateCbsAndDwxx`, params)
}
// 市场承包商管理-承包商引进-选择专业-保存选择专业
// @method postCbsyjSaveZy
// @type post
// @return url
//postCbsyjSaveZy: `/sccbsgl/cbsyj/saveZy`,

// eslint-disable-next-line
export function postCbsyjSaveZy(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveZy`, params)
}
// 市场承包商管理-承包商引进-选择专业-查询末级专业级别
// @method getCbsyjCxmjzyjb
// @type get
// @return url
//getCbsyjCxmjzyjb: `/sccbsgl/cbsyj/cxmjzyjb`,

// eslint-disable-next-line
export function getCbsyjCxmjzyjb(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/cxmjzyjb`, params)
}
// 市场承包商管理-承包商引进-选择专业-选择专业列表查询
// @method getCbsyjXzzyList
// @type get
// @return url
//getCbsyjXzzyList: `/sccbsgl/cbsyj/xzzyList`,

// eslint-disable-next-line
export function getCbsyjXzzyList(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/xzzyList`, params)
}
// 市场承包商管理-承包商引进-选择专业-保存选择区域
// @method postCbsyjSaveXzqy
// @type post
// @return url
//postCbsyjSaveXzqy: `/sccbsgl/cbsyj/saveXzqy`,

// eslint-disable-next-line
export function postCbsyjSaveXzqy(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveXzqy`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-承包商准入唯一标识校验
// @method getCbsyjCheckOnlyId
// @type get
// @return url
//getCbsyjCheckOnlyId: `/sccbsgl/cbsyj/checkOnlyId`,

// eslint-disable-next-line
export function getCbsyjCheckOnlyId(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/checkOnlyId`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-保存承包商队伍成员
// @method postCbsyjSaveCbsdwcy
// @type post
// @return url
//postCbsyjSaveCbsdwcy: `/sccbsgl/cbsyj/saveCbsdwcy`,

// eslint-disable-next-line
export function postCbsyjSaveCbsdwcy(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveCbsdwcy`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-保存承包商队伍奖惩情况
// @method postCbsyjSaveCbsdwjcqk
// @type post
// @return url
//postCbsyjSaveCbsdwjcqk: `/sccbsgl/cbsyj/saveCbsdwjcqk`,

// eslint-disable-next-line
export function postCbsyjSaveCbsdwjcqk(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveCbsdwjcqk`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-承包商基本信息保存
// @method postCbsyjSaveCbsxx
// @type post
// @return url
//postCbsyjSaveCbsxx: `/sccbsgl/cbsyj/saveCbsxx`,

// eslint-disable-next-line
export function postCbsyjSaveCbsxx(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveCbsxx`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-根据队伍id查询证书信息
// @method getCbsyjGetCbszsByDwywid
// @type get
// @return url
//getCbsyjGetCbszsByDwywid: `/sccbsgl/cbsyj/getCbszsByDwywid`,

// eslint-disable-next-line
export function getCbsyjGetCbszsByDwywid(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getCbszsByDwywid`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-查询承包商基本信息
// @method getCbsyjGetCbsjbxx
// @type get
// @return url
//getCbsyjGetCbsjbxx: `/sccbsgl/cbsyj/getCbsjbxx`,

// eslint-disable-next-line
export function getCbsyjGetCbsjbxx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getCbsjbxx`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-查询承包商所需参数
// @method getCbsyjGetCbsParams
// @type get
// @return url
//getCbsyjGetCbsParams: `/sccbsgl/cbsyj/getCbsParams`,

// eslint-disable-next-line
export function getCbsyjGetCbsParams(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getCbsParams`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-保存承包商证书
// @method postCbsyjSaveCbszs
// @type post
// @return url
//postCbsyjSaveCbszs: `/sccbsgl/cbsyj/saveCbszs`,

// eslint-disable-next-line
export function postCbsyjSaveCbszs(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveCbszs`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-需要展示的TAB页
// @method getCbsyjGetTabShow
// @type get
// @return url
//getCbsyjGetTabShow: `/sccbsgl/cbsyj/getTabShow`,

// eslint-disable-next-line
export function getCbsyjGetTabShow(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getTabShow`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-保存承包商队伍业绩
// @method postCbsyjSaveCbsdwyj
// @type post
// @return url
//postCbsyjSaveCbsdwyj: `/sccbsgl/cbsyj/saveCbsdwyj`,

// eslint-disable-next-line
export function postCbsyjSaveCbsdwyj(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveCbsdwyj`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-保存承包商_准入证号
// @method postCbsyjSaveCbszrzh
// @type post
// @return url
//postCbsyjSaveCbszrzh: `/sccbsgl/cbsyj/saveCbszrzh`,

// eslint-disable-next-line
export function postCbsyjSaveCbszrzh(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/saveCbszrzh`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-承包商组织机构id获取当前用户承包商队伍信息
// @method getCbsyjGetTeamInfoByOrgId
// @type get
// @return url
//getCbsyjGetTeamInfoByOrgId: `/sccbsgl/cbsyj/getTeamInfoByOrgId`,

// eslint-disable-next-line
export function getCbsyjGetTeamInfoByOrgId(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getTeamInfoByOrgId`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-TAB页初次进入内容
// @method getCbsyjGetTabData
// @type get
// @return url
//getCbsyjGetTabData: `/sccbsgl/cbsyj/getTabData`,

// eslint-disable-next-line
export function getCbsyjGetTabData(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getTabData`, params)
}
// 市场承包商管理-承包商引进-承包商基本信息-保存承包商队伍设备
// @method postCbsyjSavecbsdwsb
// @type post
// @return url
//postCbsyjSavecbsdwsb: `/sccbsgl/cbsyj/savecbsdwsb`,

// eslint-disable-next-line
export function postCbsyjSavecbsdwsb(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/cbsyj/savecbsdwsb`, params)
}
// 市场承包商管理-承包商引进-承包商增项-增项队伍列表
// @method getCbszxAddTeamList
// @type get
// @return url
//getCbszxAddTeamList: `/sccbsgl/cbsyj/cbszx/addTeamList`,

// eslint-disable-next-line
export function getCbszxAddTeamList(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/cbszx/addTeamList`, params)
}
// 市场承包商管理-承包商引进-承包商增项-复制增项信息
// @method getCbszxCopyAddInfo
// @type get
// @return url
//getCbszxCopyAddInfo: `/sccbsgl/cbsyj/cbszx/copyAddInfo`,

// eslint-disable-next-line
export function getCbszxCopyAddInfo(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/cbszx/copyAddInfo`, params)
}
// 市场承包商管理-承包商引进-删除承包商准入所有信息
// @method deleteCbsyjDeleteCbsyj
// @type del
// @return url
//deleteCbsyjDeleteCbsyj: `/sccbsgl/cbsyj/deleteCbsyj`,

// eslint-disable-next-line
export function deleteCbsyjDeleteCbsyj(params) {
    return axiosUtil.del(`${baseUrl}/sccbsgl/cbsyj/deleteCbsyj`, params)
}
// 市场承包商管理-准入模板管理-准入模板分页查询
// @method getZrmbglPaging
// @type get
// @return url
//getZrmbglPaging: `/sccbsgl/zrmbgl/paging`,

// eslint-disable-next-line
export function getZrmbglPaging(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zrmbgl/paging`, params)
}
// 市场承包商管理-准入模板管理-模板保存
// @method postZrmbglSave
// @type post
// @return url
//postZrmbglSave: `/sccbsgl/zrmbgl/save`,

// eslint-disable-next-line
export function postZrmbglSave(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/zrmbgl/save`, params)
}
// 市场承包商管理-准入模板管理-模板明细
// @method getZrmbglDetail
// @type get
// @return url
//getZrmbglDetail: `/sccbsgl/zrmbgl/detail/${params.id}`,

// eslint-disable-next-line
export function getZrmbglDetail(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zrmbgl/detail/${params.id}`, params)
}
// 市场承包商管理-准入模板管理-模板删除
// @method deleteZrmbgl
// @type del
// @return url
//deleteZrmbgl: `/sccbsgl/zrmbgl/${params.id}`,

// eslint-disable-next-line
export function deleteZrmbgl(params) {
    return axiosUtil.del(`${baseUrl}/sccbsgl/zrmbgl/${params.id}`, params)
}
// 市场承包商管理-准入模板管理-查询模板专业
// @method getZrmbglCheckDisable
// @type get
// @return url
//getZrmbglCheckDisable: `/sccbsgl/zrmbgl/checkDisable`,

// eslint-disable-next-line
export function getZrmbglCheckDisable(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zrmbgl/checkDisable`, params)
}
// 市场承包商管理-准入模板管理-模板复制
// @method postZrmbglCopy
// @type post
// @return url
//postZrmbglCopy: `/sccbsgl/zrmbgl/${params.id}/copy`,

// eslint-disable-next-line
export function postZrmbglCopy(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/zrmbgl/${params.id}/copy`, params)
}
// 市场承包商管理-准入模板管理-模板禁用
// @method getZrmbglDisabled
// @type get
// @return url
//getZrmbglDisabled: `/sccbsgl/zrmbgl/${params.id}/disabled`,

// eslint-disable-next-line
export function getZrmbglDisabled(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zrmbgl/${params.id}/disabled`, params)
}
// 市场承包商管理-准入模板管理-删除专业引进模板关系
// @method deleteZrmbglZymbgx
// @type del
// @return url
//deleteZrmbglZymbgx: `/sccbsgl/zrmbgl/zymbgx`,

// eslint-disable-next-line
export function deleteZrmbglZymbgx(params) {
    return axiosUtil.del(`${baseUrl}/sccbsgl/zrmbgl/zymbgx`, params)
}
// 市场承包商管理-准入模板管理-按类型查询
// @method getZrmbglFindByLx
// @type get
// @return url
//getZrmbglFindByLx: `/sccbsgl/zrmbgl/findByLx`,

// eslint-disable-next-line
export function getZrmbglFindByLx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zrmbgl/findByLx`, params)
}
// 市场承包商管理-准入模板管理-保存模板与专业关系
// @method postZrmbglSaveZyflZrmbGx
// @type post
// @return url
//postZrmbglSaveZyflZrmbGx: `/sccbsgl/zrmbgl/saveZyflZrmbGx`,

// eslint-disable-next-line
export function postZrmbglSaveZyflZrmbGx(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/zrmbgl/saveZyflZrmbGx`, params)
}
// 市场承包商管理-准入模板管理-模板明细导出
// @method getZrmbglDetailExport
// @type downloadFile
// @return url
//getZrmbglDetailExport: `/sccbsgl/zrmbgl/detail/${params.id}/export`,

// eslint-disable-next-line
export function getZrmbglDetailExport(params,fileName) {
    return axiosUtil.downloadFile(`${baseUrl}/sccbsgl/zrmbgl/detail/${params.id}/export`, params,fileName)
}
// 市场承包商管理-准入模板管理-未分配模板专业
// @method getZrmbglZrzy
// @type get
// @return url
//getZrmbglZrzy: `/sccbsgl/zrmbgl/zrzy`,

// eslint-disable-next-line
export function getZrmbglZrzy(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zrmbgl/zrzy`, params)
}
// 市场承包商管理-准入模板管理-模板启用
// @method getZrmbglEnabled
// @type get
// @return url
//getZrmbglEnabled: `/sccbsgl/zrmbgl/${params.id}/enabled`,

// eslint-disable-next-line
export function getZrmbglEnabled(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zrmbgl/${params.id}/enabled`, params)
}
// 市场承包商管理-准入模板管理-专业引进模板关系
// @method getZrmbglZymbgx
// @type get
// @return url
//getZrmbglZymbgx: `/sccbsgl/zrmbgl/zymbgx`,

// eslint-disable-next-line
export function getZrmbglZymbgx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zrmbgl/zymbgx`, params)
}
// 市场承包商管理-注册管理-用户注册
// @method postZcglUserRegister
// @type post
// @return url
//postZcglUserRegister: `/sccbsgl/zcgl/userRegister`,

// eslint-disable-next-line
export function postZcglUserRegister(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/zcgl/userRegister`, params)
}
// 市场承包商管理-注册管理-承包商注册
// @method postZcglContractorRegister
// @type post
// @return url
//postZcglContractorRegister: `/sccbsgl/zcgl/contractorRegister`,

// eslint-disable-next-line
export function postZcglContractorRegister(params) {
    return axiosUtil.post(`${baseUrl}/sccbsgl/zcgl/contractorRegister`, params)
}
// 市场承包商管理-注册管理-单位全称校验
// @method getZcglCheckDwqc
// @type get
// @return url
//getZcglCheckDwqc: `/sccbsgl/zcgl/checkDwqc`,

// eslint-disable-next-line
export function getZcglCheckDwqc(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zcgl/checkDwqc`, params)
}
// 市场承包商管理-注册管理-登录帐号校验
// @method getZcglCheckDlzh
// @type get
// @return url
//getZcglCheckDlzh: `/sccbsgl/zcgl/checkDlzh`,

// eslint-disable-next-line
export function getZcglCheckDlzh(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zcgl/checkDlzh`, params)
}
// 市场承包商管理-注册管理-统一信用代码校验
// @method getZcglCheckTyxydm
// @type get
// @return url
//getZcglCheckTyxydm: `/sccbsgl/zcgl/checkTyxydm`,

// eslint-disable-next-line
export function getZcglCheckTyxydm(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zcgl/checkTyxydm`, params)
}
// 市场承包商管理-注册管理-联系人手机号校验
// @method getZcglCheckLxrsj
// @type get
// @return url
//getZcglCheckLxrsj: `/sccbsgl/zcgl/checkLxrsj`,

// eslint-disable-next-line
export function getZcglCheckLxrsj(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/zcgl/checkLxrsj`, params)
}

export function getCllxList(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getCllxList`, params)
}

export function getCllxCxList(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getCllxCxList`, params)
}

// 查询基本信息检验
export function getCheckedJbxx(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/getCheckedJbxx`, params)
}

// 查询准入检验
export function getCheckIsFirstZr(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsyj/checkIsFirstZr`, params)
}

// 查询队伍信息
export function getTeamInfo(params) {
    return axiosUtil.get(`${baseUrl}/sccbsgl/cbsxxgl/getTeamInfo`, params)
}

