<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="GDMC">
            <el-input ref="input45296" placeholder="请输入名称" v-model="listQuery.GDMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="CJRQKS">
            <el-date-picker
                v-model="listQuery.CJRQKS"
                type="date"
                style="width: 100%"
                value-format="YYYY-MM-DD"
                placeholder="创建开始日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="CJRQJS">
            <el-date-picker
                v-model="listQuery.CJRQJS"
                type="date"
                style="width: 100%"
                value-format="YYYY-MM-DD"
                placeholder="创建结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CJRXM">
            <el-input ref="input45296" placeholder="请输入创建人" v-model="listQuery.CJRXM" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
            <el-button ref="button9527" type="primary" class="lui-button-add" >
              <el-icon>
                <Plus/>
              </el-icon>
              新建
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 280px)"
                      :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="CBSDWQC" label="承包商单位全称" align="center" header-align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="GDMC" label="股东名称" align="center" header-align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="GDLX" label="股东类型" align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="CZBL" label="出资比例(%)" align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="CZJE" label="出资金额(万元)" align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="CZSJ" label="出资时间" align="center"
                               :show-overflow-tooltip="true" ></el-table-column>

            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import gqctglEdit from "@views/gqct/gqctgl/gqctglEdit";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,gqctglEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/gqct/gqsjjc/selectGdxxPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }




    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
    }
  }

})
</script>

<style scoped>

</style>
