<template>
  <div style="height: calc(100% - 40px)">
    <el-form style="height: 100%;" size="default" class="lui-page" v-loading="loading">
      <el-table :data="tableData" height="calc(100% - 80px)" border size="default" class="lui-table">
        <el-table-column label="队伍名称" prop="unitName" width="200" show-overflow-tooltip header-align="center"
                         align=""></el-table-column>
        <el-table-column label="队伍编码" prop="dwbm" width="200" header-align="center"
                         align=""></el-table-column>
        <el-table-column label="申请服务范围" show-overflow-tooltip prop="specialName"
                         header-align="center" align=""></el-table-column>
        <el-table-column v-for="(item,index) in templates" :key="index" :label="item.SJMBMC"
                         :prop="item.SJMBBM" header-align="center" align="center" width="100">
          <template v-slot="scope">
            <div v-if="scope.row.templates.filter(x=>x.SJMBBM == item.SJMBBM).length > 0">
              <el-button type="success" v-if="scope.row[item.SJMBBM]" :icon="Check" circle
                         style="width: 30px;height: 30px"/>
              <el-button type="danger" v-else :icon="Close" circle
                         style="width: 30px;height: 30px"/>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" align="center" fixed="right" width="150">
          <template v-slot="scope">
            <div>
              <el-button v-if="editable" class="lui-table-button" @click="editRow(scope.row)">录入信息</el-button>
              <el-button v-if="!editable" class="lui-table-button" @click="viewRow(scope.row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="队伍数据"
        @closed="dialogVisible=false"
        z-index="1000"
        top="1vh"
        width="1400px">
      <div>
        <dwqsjEdit v-if="dialogVisible" :params="params" @close="dialogVisible=false"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, onUnmounted} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {Check, Close} from '@element-plus/icons-vue'
import tabFun from "@lib/tabFun";
import {mixin} from "@core";
import dwqsjEdit from "@views/cbs/queryanalysis/cbsqsjck/dwqsjEdit";

export default defineComponent({
  name: '',
  components: {dwqsjEdit},
  props: {
    DWYWID: {
      type: String,
      defaultData: () => '',
    },
    BGJL: {
      type: Object,
      required: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    editable: {
      type: String,
      defaultData: () => false,
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      tableData: [],
      templates: [],
      loading: false,
      LSDWList: [],
      params: {},
      dialogVisible: false,
    })


    const getDataList = () => {
      const params = {
        DWYWID: props.DWYWID
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/cbsyj/teamlist', params).then((res) => {
        let resData = res.data || []
        resData.forEach(x => {
          x.unitName = x.DWMC;
          x.specialName = x.SQFWFW;
          x.dwbm = x.DWBM
        })
        state.tableData = resData
        state.loading = false
      });
    }



    const getMbxxList = () => {
      const params = {
      }
      axiosUtil.get('/backend/sccbsgl/report/cbsqsj/selectAllBqxx', params).then(res => {
        state.templates = res.data.tabList || []
      })
    }

    const editRow = (row) => {
      state.params = {editable: true, id: row.DWYWID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.params = {editable: false, id: row.DWYWID, operation: 'view'}
      state.dialogVisible = true
    }

    onMounted(() => {
      getDataList()
      getMbxxList()
    })

    onUnmounted(()=>{
    })

    return {
      ...toRefs(state),
      Check, Close,
      editRow,
      viewRow

    }
  }

})
</script>

<style scoped>

</style>
