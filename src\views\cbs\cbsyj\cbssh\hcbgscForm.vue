<template>
  <div>

    <el-input style="margin-top: 20px" v-model="shyj" type="textarea" :rows="5" placeholder="请输入审核意见"></el-input>
    <div style="display: flex;margin-top: 20px;min-height: 80px" v-if="apprValue !== '0'&&showHcbg">
      <div>上传核查报告：</div>
      <vsfileupload style="margin-left: 10px" :busId="params.businessId" :maxSize="10"
                    :key="params.businessId" v-model:files="fileList"
                    :editable="true" ywlb="hcbg"/>
    </div>

    <div style="display: flex;align-items: center;margin-top: 10px" v-if="apprValue !== '0'">
      <div style="width: 100px;flex-shrink: 0;text-align: right">专业管理部门人员：</div>
      <el-select
          v-model="formData.BLRList[0].VALUE"
          multiple
          collapse-tags
          collapse-tags-tooltip
          filterable
          placeholder="请选择"
          style="width: 100%">
        <el-option
            v-for="item in YTZYBMLDOptions"
            :key="item.USER_LOGINNAME"
            :label="`${item.USER_NAME}（${item.ORGNA_NAME}）`"
            :value="item.USER_LOGINNAME"/>
      </el-select>
    </div>

    <div style="text-align: center; height: 40px; padding: 10px">
      <el-button type="primary" @click="onConfirm">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import vsfileupload from "@views/components/vsfileupload";
import {ElMessage} from "element-plus";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    apprValue: String,
    Activityid: String,
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      shyj: "",
      sffsdx: '1',
      showHcbg:false,
      blrArray: [],
      showBlrXz: false,
      nextPerformer: [],
      blfs:'BR',
      fileList: [],
      formData: {
        BLRList: [
          {VALUE: [], TYPE: 'GR', ACTIVITYID: props.Activityid, LABEL: '专业管理部门人员'},
        ]
      },
      YTZYBMLDOptions: [],
    })


    const onConfirm = () => {
      if(state.fileList.length===0&&state.showHcbg){
        ElMessage.warning('请上传核查报告')
        return
      }
      let nullRow=state.formData.BLRList.find(item=>!item.VALUE || item.VALUE.length===0)
      if (nullRow) {
        ElMessage.warning('请选择'+nullRow.LABEL)
        return
      }
      saveBlrInfo().then(res=>{
        emit("confirm", state.shyj, state.sffsdx, state.nextPerformer)
      })
    }
    const saveBlrInfo = () => {
      state.loading=true
      let params={
        BLRList: state.formData.BLRList.map((item,index)=>{
          return{
            ...item,
            BUSINESSID: props.params.businessId,
            PROCESSID: props.params.processId,
            TASKID: props.params.taskId,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
            XGRZH: state.userInfo.userLoginName,
            XGSJ: comFun.getNowTime(),
            NAME: item.VALUE.map(ii=>{
              return state.YTZYBMLDOptions.find(iii=>iii.USER_LOGINNAME===ii)?.USER_NAME
            }).join(','),
            VALUE: item.VALUE.join(',')
          }
        })
      }
      return axiosUtil.post('/backend/common/saveLcblr',params)
    }

    const getBlrOptions = (optionName,ROLE) => {
      let params={
        ROLE: ROLE,
        businessId: props.params.businessId
      }
      axiosUtil.get('/backend/cbsxx/common/getUserZyglbmByCbsKey',params).then(res=>{
        state[optionName]=res.data || []
      })
    }
//判断是不是要上传核查报告
    const getSfHcbg = () => {
      let params={
        businessId: props.params.businessId
      }
      axiosUtil.get('/backend/cbsxx/common/getSfHcbg',params).then(res=>{
        if(res.data&&res.data.length>0){
          state.showHcbg=true;
        }else{
          state.showHcbg=false;
        }
      })
    }

    const onCancel = () => {
      emit("close")
    }

    onMounted(() => {
      getSfHcbg();
      getBlrOptions('YTZYBMLDOptions','CBSGL_YTZYGLBM')
      console.error(props)
      if (props.apprValue !== '0') {
        state.shyj = '同意'
      }
      // emit('changeTitle','这是一个测试审核页面')
      // emit('changeWidth',1000)

    })

    return {
      ...toRefs(state),
      onCancel,
      onConfirm

    }
  }

})
</script>

<style scoped>

</style>
