<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.glcj" placeholder="请选择管理层级">
            <el-option
                v-for="item in [{label:'企业',value:'企业'},{label:'队伍',value:'队伍'},{label:'专业',value:'专业'}]"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="data.queryForm.gldx" placeholder="请输入管理对象名称" @keyup.enter="query"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.zt" placeholder="请选择当前状态">
            <el-option
                v-for="item in [{label:'正常',value:'ZC'},{label:'暂停',value:'ZT'},{label:'取消',value:'QX'}]"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-button @click="resetQuery"><el-icon><RefreshRight/></el-icon>重置</el-button>
        <el-button @click="query" type="primary">
          <el-icon>
            <Search/>
          </el-icon>
          查询
        </el-button>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          highlight-current-row
          ref="table"
          fit
          size="default"
          height="calc(100vh - 250px)"
          :border="false"
          :data="data.tableData"
          v-loading="tableLoading"
      >
        <EleProTableColumn
            v-for="prop in data.tableColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #opration="{row,$index}">
            <div>
              <el-button class="lui-table-button" @click="edit(row,$index)">状态变更</el-button>
            </div>
          </template>
          <template #status="{row,$index}">
            <span v-if="row.ZT=='ZC'">正常</span>
            <span v-if="row.ZT=='QX'">取消</span>
            <span v-if="row.ZT=='ZT'">暂停</span>
          </template>
        </EleProTableColumn>
      </el-table>
    </div>
    <el-dialog
        v-model="data.saveVisible"
        title="数据维护"
        width="1100px"
        custom-class="lui-dialog">
      <el-form
          class="lui-card-form"
          ref="saveFormRef"
          :model="data.saveForm"
          :rules="rules"
          label-width="120px"
          status-icon
          size="default"
      >
        <el-row class="grid-row">
          <el-col :span="14" class="grid-cell">
            <el-form-item label="管理对象" prop="GLDX">
              <el-input v-model="data.saveForm.GLDX" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10" class="grid-cell">
            <el-form-item label="管理层级" prop="GLCJ">
              <el-input v-model="data.saveForm.GLCJ" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="当前状态" prop="ZT">
              <el-select v-model="data.saveForm.ZT" disabled>
                <el-option v-for="(item, index) in data.teamStatus" :key="index + 'zt'" :label="item.name"
                           :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="变更后状态" prop="BGHZT">
              <el-select v-model="data.saveForm.BGHZT">
                <el-option v-for="(item, index) in (data.teamStatus.filter(x=>x.value != data.saveForm.ZT))"
                           :key="index + 'bgh'" :label="item.name" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="暂停期限" prop="DWBM" v-if="data.saveForm.BGHZT == 'ZT'">
              <el-date-picker
                  v-model="data.saveForm.date"
                  type="daterange"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  :shortcuts="data.shortcuts"
                  range-separator="-"
                  start-placeholder="暂停开始时间"
                  end-placeholder="暂停结束时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell">
            <el-form-item label="变更原因" prop="BGYY">
              <el-select v-model="data.saveForm.BGYY">
                <el-option v-for="(item, index) in data.changeReason" :key="index + 'bgyy'" :label="item.DMMC"
                           :value="item.DMMC"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell">
            <el-form-item label="变更说明" prop="BGSM">
              <el-input v-model="data.saveForm.BGSM" type="textarea"/>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell">
            <el-form-item label="相关附件" prop="SYZT">
              <vsfileupload :busId="data.saveForm.RCGLID" :key="data.saveForm.RCGLID" ywlb="DWZTBGFJ" busType="dwxx"
                            :limit="100" style="margin-left: 10px" :maxSize="10"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
            <span class="dialog-footer">
              <el-button @click="data.saveVisible = false">取消</el-button>
              <el-button type="primary" @click="save">
                确定
              </el-button>
            </span>
      </template>
    </el-dialog>
  </el-form>
</template>
<script setup>
import {
  ref,
  reactive,
  onMounted,
} from "vue";
import {ElMessage, ElMessageBox} from 'element-plus';
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getDwrcglDwztbgPage, postDwrcglDwztbg} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam, getCommonSelectDMB} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import {v4 as uuidv4} from "uuid";
import comFun from "@src/lib/comFun";
import vsfileupload from "@src/views/components/vsfileupload.vue";

import {Search, Upload, Plus,RefreshRight} from '@element-plus/icons-vue'

const data = reactive({
  shortcuts: [
    {
      text: '三个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 90)
        return [start, end]
      },
    }, {
      text: '半年',
      value: () => {
        const end = new Date()
        const start = new Date()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 180)
        return [start, end]
      },
    }, {
      text: '一年',
      value: () => {
        const end = new Date()
        const start = new Date()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 365)
        return [start, end]
      },
    },
  ],
  queryForm: {
    glcj: null,
    gldx: null,
    zt: null,
  },
  changeReason: [],
  saveForm: {
    date: ''
  },
  teamStatus: [
    {
      value: 'ZC',
      name: '正常'
    },
    {
      value: 'ZT',
      name: '暂停'
    },
    {
      value: 'QX',
      name: '取消'
    },
  ],
  currentUser: {},
  saveVisible: false,
  tableData: [],
  tableColumn: [
    {
      label: "序号",
      prop: "order",
      width: 100,
      align: "left"
    },
    {
      label: "管理对象",
      prop: "GLDX",
      align: "left",
      showOverflowTooltip: true,
    },
    {
      label: "管理层级",
      prop: "GLCJ",
      align: "left",
      // slot: "faultHours",
    },
    {
      label: "当前状态",
      prop: "ZT",
      align: "center",
      showOverflowTooltip: true,
      slot: 'status'
    },
    {
      label: "暂停期限开始",
      prop: "ZTQXKS",
      align: "center",
      width: 150,
    },
    {
      label: "暂停期限结束",
      prop: "ZTQXJS",
      align: "center",
      width: 150,
    },
    {
      label: "操作",
      align: "center",
      width: 150,
      fixed: "right",
      slot: "opration",
    },
  ],
})

const resetQuery = () => {
  data.queryForm = {
    glcj: null,
    gldx: null,
    zt: null
  }
}
const tableLoading = ref(false);
const query = () => {
  tableLoading.value = true;
  getDwrcglDwztbgPage(data.queryForm).then(res => {
    console.log(res)
    let qys = res.data.filter(x => x.GLCJ == '企业')
    let dw = res.data.filter(x => x.GLCJ == '队伍')
    let zy = res.data.filter(x => x.GLCJ == '专业')
    for (let i = 0; i < qys.length; i++) {
      qys[i].CBSDWQC = qys[i].GLDX
      qys[i].order = i + 1;
      qys[i].FLOOR = qys[i].YWID
      let children = res.data.filter(x => x.GLCJ != '企业' && x.CBSYWID == qys[i].CBSYWID)
      let c = [];
      let z = [];
      let xh = 1;
      children.forEach(x => {
        if (x.GLCJ == '队伍') {
          x.order = qys[i].order + '.' + xh;
          x.CBSDWQC = qys[i].GLDX
          x.FLOOR = qys[i].FLOOR + ',' + x.YWID
          c.push(x);
          xh += 1;
        } else {
          if (x.DWYWID == qys[i].YWID) {
            x.order = qys[i].order + '.' + xh;
            x.CBSDWQC = qys[i].GLDX
            x.FLOOR = qys[i].FLOOR + ',' + x.YWID
            xh += 1;
          } else {
            z.push(x)
          }
        }
      })
      c.forEach(x => {
        let zyxh = 1;
        for (let j = 0; j < z.length; j++) {
          if (x.YWID == z[j].DWYWID) {
            z[j].order = x.order + '.' + zyxh;
            z[j].CBSDWQC = qys[i].GLDX
            z[j].FLOOR = x.FLOOR + ',' + z[j].YWID
            zyxh += 1;
          }
        }
      })

    }
    console.log('res.data', res.data)
    data.tableData = res.data
  }).catch((err) => {
    console.log(err);
  }).finally(() => {
    tableLoading.value = false
  })

}
const edit = (row, index) => {
  data.saveForm = {
    RCGLID: uuidv4().replace(/-/g, ''),
    date: [
      comFun.getNowDate(),
      comFun.addDate(30)
    ],
    ...row,
  }
  console.log(data.saveForm)
  data.saveVisible = true;

}
const getChangeReason = () => {
  getCommonSelectDMB({DMLBID: 'DWZTBGYY'}).then(res => {
    data.changeReason = res.data
  })
}
const saveFormRef = ref(null)
const save = () => {
  if (data.saveForm.BGHZT != 'ZT') {
    data.saveForm.ZTKSSJ = null
    data.saveForm.ZTJSRQ = null
  } else {
    data.saveForm.ZTKSSJ = data.saveForm.date[0]
    data.saveForm.ZTJSRQ = data.saveForm.date[1]
  }
  let bgh = {
    ZTKSSJ: data.saveForm.ZTKSSJ,
    ZTJSRQ: data.saveForm.ZTJSRQ,
    ZT: data.saveForm.BGHZT,
  }
  let bgq = {
    ZTKSSJ: data.saveForm.ZTQXKS,
    ZTJSRQ: data.saveForm.ZTQXJS,
    ZT: data.saveForm.ZT,
  }
  data.saveForm.CJSJ = comFun.getNowTime();
  data.saveForm.CJRXM = data.currentUser.USER_NAME;
  data.saveForm.CJRZH = data.currentUser.USER_LOGINNAME;
  data.saveForm.CJDWID = data.currentUser.ORGNA_ID
  data.saveForm.DWMC = data.saveForm.GLDX
  data.saveForm.BGH = JSON.stringify(bgh)
  data.saveForm.BGQ = JSON.stringify(bgq)
  data.saveForm.DWZT = data.saveForm.BGHZT
  let all = data.tableData.filter(x => x.FLOOR.indexOf(data.saveForm.YWID) != -1);
  let dwids = all.filter(x => x.GLCJ != '专业').map(x => x.YWID)
  let zyids = all.filter(x => x.GLCJ == '专业').map(x => x.YWID)
  data.saveForm.DWIDS = dwids
  data.saveForm.ZYIDS = zyids
  console.log(data.saveForm);
  postDwrcglDwztbg(data.saveForm).then(res => {
    ElMessage({
      type: 'success',
      message: '保存成功!'
    });
    query();
    data.saveVisible = false;
  }).catch(e => {
    ElMessage({
      type: 'error',
      message: '保存失败!'
    });
  })
}

const getUserInfo = () => {
  let user = VSAuth.getAuthInfo().permission
  getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
    data.currentUser = res.data;
  })
}


onMounted(() => {
  getChangeReason();
  getUserInfo();
  query();
})
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF
}

.header {
  padding: 10px 10px 0;
  line-height: 50px;
}

.main {
  flex: 1;
  overflow: hidden;
  padding: 10px;
}
</style>
