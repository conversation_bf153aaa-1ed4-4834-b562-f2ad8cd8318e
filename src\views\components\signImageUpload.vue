<template>
  <div>
    <div class="image-context" style="width: 150px;height: 60px;margin-bottom: 10px;background-color: white" v-if="showImage">
      <el-image v-if="signImage" style="width: 150px;height: 60px"
                :src="'/backend/minio/download?id='+signImage.id"></el-image>
      <div v-if="signImage && editable" class="deleteIcon" @click="delPic(null)">
        <el-icon><Delete /></el-icon>
      </div>
    </div>


    <div style="display: flex;gap: 10px;">
      <el-button v-if="editable" size="default" ref="button91277" @click="openSign" type="primary">签字</el-button>
      <vsfileupload
          ref="signImageUpload"
          :editable="editable"
          :busId="busId"
          :ywlb="ywlb"
          :busType="busType"
          v-model:files="files"
          :noTableButton="true"
          :limit="1"
          :showFileList="false"
          :multiple="false"
          accept=".png,.PNG,.jpg,.jpeg,.JPEG,.gif"/>
    </div>






    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="签字"
        @closed="dialogVisible=false"
        z-index="1000"
        top="5vh"
        append-to-body
        width="600px">
      <div>
        <canvas ref="canvas" class="signature-canvas"></canvas>
        <div class="buttons">
          <el-button size="default" ref="button91277" @click="clearSign" type="primary">清除</el-button>
          <el-button size="default" ref="button91277" @click="saveSign" type="primary">提交</el-button>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {
  defineComponent,
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeMount,
  ref,
  nextTick,
  watch
} from "vue";
import SignaturePad from 'signature_pad'
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import {Delete} from "@element-plus/icons-vue";
import {axios} from "@core";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";

export default defineComponent({
  name: '',
  components: {vsfileupload,Delete},
  props: {
    busId: { //附件业务ID
      type: String,
      required: true,
    },
    busType: {  //后台配置文件中的业务类型
      type: String,
      default: 'zysc'
    },
    ywlb: {  //业务类别，用于标识用同一id有不同业务类型的附件
      type: String,
      default: 'default'
    },
    maxSize: {      //允许上传的文件大小
      type: Number,
    },
    editable: { //是否可编辑，默认可编辑
      type: Boolean,
      default: true
    },
    signFile:{
      type: Object
    },
    showImage:{
      type: Boolean,
      default: true
    }

  },
  setup(props, {emit}) {
    const state = reactive({
      dialogVisible: false,
      signImage: null,
      files: []
    })


    watch(()=>state.files,()=>{
      if(state.files.length>0){
        state.signImage = state.files[0]
      }else {
        state.signImage = null
      }
      emit('update:signFile',state.signImage)
    },{deep:true})

    const canvas = ref(null)
    const signaturePad = ref(null)

    const initDialog = () => {
      const canvasEl = canvas.value
      canvasEl.width = canvasEl.offsetWidth
      canvasEl.height = canvasEl.offsetHeight

      signaturePad.value = new SignaturePad(canvasEl, {
        backgroundColor: 'rgba(0,0,0,0)', // 透明背景
        penColor: '#000',
        onEnd: () => {
          console.log('签名结束')
        }
      })
    }

    const openSign = () => {
      state.dialogVisible = true
      nextTick(()=>{
        initDialog()
      })
    }


    const clearSign = () => {
      signaturePad.value.clear()
    }

    const saveSign = () => {
      if (signaturePad.value.isEmpty()) {
        ElMessage.warning('请签名')
        return
      }

      const dataURL = signaturePad.value.toDataURL('image/png') // base64
      const arr = dataURL.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      const blob = new Blob([u8arr], { type: mime });
      const fileName = 'signature.png';
      const file = new File([blob], fileName, { type: mime });

      const formdata = new FormData()
      formdata.append('files', file)
      formdata.append('busType', 'dwxx')
      formdata.append('busId', props.busId)
      formdata.append('standbyField0', props.ywlb)

      if(state.signImage){
        delPic(formdata)
      }else {
        uploadPic(formdata)

      }
    }

    const instance = getCurrentInstance()
    const delPic = (formdata) => {
      axios.post('/backend/minio/del', {id: state.signImage.id, delFlag: "1"}).then((res) => {
        if(!formdata){
          instance.proxy.$refs['signImageUpload'].loadFileList()
        }else {
          uploadPic(formdata)
        }
      })
    }

    const uploadPic = (formdata) => {
      axiosUtil.uploadFile('/backend/minio/upload',formdata).then(res=>{
        instance.proxy.$refs['signImageUpload'].loadFileList()
        state.dialogVisible=false
      })
    }

    const loadFileList = () => {
      instance.proxy.$refs['signImageUpload'].loadFileList()
    }

    onMounted(() => {
    })

    return {
      ...toRefs(state),
      canvas,
      signaturePad,
      openSign,
      saveSign,
      clearSign,
      delPic,
      loadFileList
    }
  }

})
</script>

<style scoped>
.buttons{
  display: flex;
  padding: 10px;
  align-items: center;
  justify-content: center;
}
.signature-canvas{
  border: 1px solid #ccc;
  width: 100%;
  height: 250px;
}

.image-context{
  position: relative;
}
.image-context:hover>.deleteIcon{
  display: block;
}

.deleteIcon{
  font-size: 20px;
  color: #2A96F9;
  position: absolute;
  right: 5px;
  bottom: 5px;
  cursor: pointer;
  display: none;
}
</style>
