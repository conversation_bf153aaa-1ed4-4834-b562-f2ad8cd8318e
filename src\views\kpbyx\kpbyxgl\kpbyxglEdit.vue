<template>
  <div class="main-context" v-loading="loading">
    <div style="color: #c03c29;padding-left: 20px">（完成后此项目开评标不能再做任何修改 只有完成后才可发起中标结果审批。流标后此项目开评标不能再做任何修改。）</div>
    <h2 style="width: 100%;text-align: center;color: #c03c29">{{busType==='DJTP' ? '评审会议管理' : '开评标运行管理'}}</h2>
    <div style="text-align: right;padding-left: 40px;padding-right: 40px">
      <el-button size="default" type="primary" @click="">完成</el-button>
      <el-button size="default" type="danger" @click="">流标</el-button>
      <el-button size="default" @click="closeForm">返回</el-button>
    </div>
    <component :is="pageComponent[busType]" v-if="pageComponent[busType]" :params="params" v-model:formData="formData"
               @close="closeForm"/>
  </div>
</template>

<script>

import {
  defineComponent,
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeMount,
  markRaw,
  onUnmounted
} from "vue";
import djtpKpbyxCard from "@views/kpbyx/kpbyxgl/djtpKpbyxCard";
import zbfbKpbyxCard from "@views/kpbyx/kpbyxgl/zbfbKpbyxCard";
import axiosUtil from "@lib/axiosUtil";
import {mixin} from "@core";

export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      JLID: props.params.id,
      formData:{

      },
      loading: false,
      busType: '',
      pageComponent:{
        DJTP: markRaw(djtpKpbyxCard),
        ZBFB: markRaw(zbfbKpbyxCard),
      },
    })

    const getFormData = () => {
      let params={
        JLID: state.JLID
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/kpbgl/selectKpbyxById', params).then((res) => {
        state.formData=res.data
        if(state.formData.XSFS==='DJTP'){
          state.busType='DJTP'
        }else {
          state.busType='ZBFB'
        }
        state.loading=false
      })
    }

    const closeForm = () => {
      emit('close')
    }

    const {vsuiRoute, vsuiEventbus} = mixin();

    onMounted(() => {
      vsuiEventbus.on("reloadYxList", getFormData)
      getFormData()
    })

    onUnmounted(()=>{
      vsuiEventbus.off("reloadYxList", getFormData)
    })

    return {
      ...toRefs(state),
      closeForm,
      getFormData

    }
  }

})
</script>

<style scoped>
.main-context{
  padding: 10px 20px 20px;
}
</style>
