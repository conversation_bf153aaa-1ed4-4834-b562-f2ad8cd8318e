<template>
	<div class="Word">
	  <div v-html="poHtmlCode" />
	</div>
</template>
<style>
	 div{    /* div的CSS样式 */
		position : absolute;
		width : 100%;
		height : 100%;
	}
</style>
<script>
	import axios from 'axios';
	  export default{
	    name: 'Word',
	    data(){
	      return {
	        poHtmlCode: '',
	
	      }
	    },
		created: function(){
			//由于vue中的axios拦截器给请求加token都得是ajax请求，所以这里必须是axios方式去请求后台打开文件的controller
			axios.post("/api/NoBlank/Word").then((response) => {
			this.poHtmlCode = response.data;

			}).catch(function (err) {
			console.log(err)
			})
		},
	    methods:{
	      //控件中的一些常用方法都在这里调用，比如保存，打印等等
	      
	    },
	    mounted: function(){
	      // 将vue中的方法赋值给window
	      
	    }
	}
</script>

