<template>
  <div class="context">
    <div class="label-text">
      <div style="flex: 1" v-if="params.DMXX==='WBCC'">重复字数：{{modelValue.WBCFSL}}</div>
      <div style="flex: 1" v-if="params.DMXX==='WBCC'">对A重复率：{{modelValue.DACFL}}%</div>
      <div style="flex: 1" v-if="params.DMXX==='WBCC'">对B重复率：{{modelValue.DBCFL}}%</div>
    </div>

    <el-table ref="datatable91634" :data="modelValue[params.DMXX+'List']" height="calc(100vh - 430px)"
              :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
      <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
      <el-table-column label="原文件（A）" align="center">

        <el-table-column prop="AWJNR" label="原始内容" align="center"
                         min-width="200">
          <template #default="{row}">
            <div v-html="getHtml(row,'A')" v-if="['WBCC','GSXXCC','CWYZXCC'].includes(params.DMXX)"></div>
            <img @click="previewPictures(`/backend/minio/downloadByPath?path=${row.AWJNR}`)"
                 class="preview-pictures"
                 :src="`/backend/minio/downloadByPath?path=${row.AWJNR}`" v-if="params.DMXX==='TPCC'" />
          </template>
        </el-table-column>
        <el-table-column prop="AWJYM" label="页码" align="center"
                         width="100"></el-table-column>

      </el-table-column>
      <el-table-column label="对比文件（B）" align="center">

        <el-table-column prop="BWJNR" label="原始内容" align="center"
                         min-width="200">
          <template #default="{row}">
            <div v-html="getHtml(row,'B')" v-if="['WBCC','GSXXCC','CWYZXCC'].includes(params.DMXX)"></div>
            <img @click="previewPictures(`/backend/minio/downloadByPath?path=${row.BWJNR}`)"
                 class="preview-pictures"
                 :src="`/backend/minio/downloadByPath?path=${row.BWJNR}`" v-if="params.DMXX==='TPCC'" />
          </template>
        </el-table-column>
        <el-table-column prop="BWJYM" label="页码" align="center"
                         width="100"></el-table-column>
      </el-table-column>
      <el-table-column prop="CFZS" label="重复字数" align="center"
                       width="100" v-if="params.DMXX==='WBCC'">
        <template #default="{row}">
          {{Number(row.AWBJS || 0) - Number(row.AWBKS || 0)}}
        </template>
      </el-table-column>
      <el-table-column prop="BZ" label="备注" align="center"
                       width="100" v-if="['CWYZXCC','GSXXCC'].includes(params.DMXX)">
      </el-table-column>

    </el-table>


    <picPreview v-model="dialogTPVisible" :imageList="previewPath"/>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import picPreview from "@views/components/picPreview";

export default defineComponent({
  name: '',
  components: {picPreview},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      previewPath: [],
      dialogTPVisible: false
    })
    const getHtml = (row,p) => {
      let text=row[p+'WJNR']
      let start=row[p+'WBKS']
      let end=row[p+'WBJS']
      if(start!==null && end!==null){
        let part1=text.slice(0,Number(start))
        let part2=text.slice(Number(start),Number(end)+1)
        let part3=text.slice(Number(end)+1)
        text=`${part1}<span style="color: red">${part2}</span>${part3}`
      }
      return text
    }

    const previewPictures = (path) => {
      state.previewPath=[path]
      state.dialogTPVisible=true
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      getHtml,
      previewPictures

    }
  }

})
</script>

<style scoped>
.context {
  height: calc(100vh - 400px);
  overflow: auto;
  padding: 0 20px 20px;
}

.label-text{
  display: flex;
  font-family: 黑体;
  color: black;
  padding: 0 20px;
  height: 30px;

}

.preview-pictures{
  max-height: 50px;
  cursor: pointer;
}
</style>
