<template>
  <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
           size="default" @submit.prevent>
    <div style="display: flex;height: calc(100% - 30px);gap: 20px">
      <div style="height: 100%">
        <div style="display: flex;align-items: center;gap: 20px">
          <el-input style="margin-bottom: 10px" ref="input45296" placeholder="代码类别名称或编码"
                    v-model="listQuery.DMLBMC" type="text" clearable @input="getDmlbList">
          </el-input>
          <el-icon :size="30" style="cursor: pointer;margin-bottom: 10px" @click="addRow"><Plus/></el-icon>
        </div>
        <div style="height: calc(100% - 60px);width: 300px;box-shadow: var(--el-box-shadow-light);overflow-y: auto">
          <div :class="{'DMLB-row': true,'DMLB-row-select': item.DMLBID===DMLBChecked}"
               v-for="(item,index) in DMLBList" :key="index" @click="checkRow(item)">
            <div>{{ item.DMLBMC }}</div>
            <div class="DMLB-row-more" @click.stop="editRow(item)">
              <el-icon :size="25">
                <Edit/>
              </el-icon>
            </div>
          </div>
        </div>
      </div>
      <div style="width: calc(100% - 320px)">
        <el-row :gutter="20" class="lui-search-form">
          <!--          <el-col :span="6" class="grid-cell">-->
          <!--            <el-form-item label="" prop="userName">-->
          <!--              <el-input ref="input45296" placeholder="请输入代码名称" v-model="roleName" type="text" clearable>-->
          <!--              </el-input>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <div style="line-height: 40px;margin-left: 20px;margin-right: 20px">
            {{getDMLBXQ()?.DMLBMC}}-{{getDMLBXQ()?.DMLBID}}
          </div>
          <el-col :span="4" class="grid-cell">
            <div class="static-content-item" style="display: flex;">
              <!--              <el-button ref="button91277" @click="getDataList" type="primary"><el-icon><Search/></el-icon>查询</el-button>-->
              <el-button ref="button9527" type="primary" class="lui-button-add" @click="addTableRow"><el-icon><Plus/></el-icon>新建</el-button>
              <el-button ref="button9527" type="primary" @click="saveTable"><el-icon><Finished/></el-icon>保存</el-button>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 180px)"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}" v-loading="loading">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column v-if="true" prop="DMLBID" label="代码类别ID" align="left"
                             :show-overflow-tooltip="true" width="150"></el-table-column>
            <el-table-column v-if="true" prop="DMMC" label="代码名称" align="left"
                             :show-overflow-tooltip="true" min-width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="代码名称" v-model="row.DMMC" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="DMXX" label="代码编码" align="center"
                             :show-overflow-tooltip="true" width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="代码编码" v-model="row.DMXX" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column v-if="true" prop="SFYX" label="是否有效" align="center"
                             :show-overflow-tooltip="true" width="60">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="是否有效" v-model="row.SFYX" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column v-if="true" prop="SSPT" label="所属平台" align="center"
                             :show-overflow-tooltip="true" width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="所属平台" v-model="row.SSPT" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="FDMXX" label="父代码信息" align="center"
                             :show-overflow-tooltip="true" width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="父代码信息" v-model="row.FDMXX" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="BYZD1" label="备用字段1" align="center"
                             :show-overflow-tooltip="true" width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="备用字段1" v-model="row.BYZD1" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="BYZD2" label="备用字段2" align="center"
                             :show-overflow-tooltip="true" width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="备用字段2" v-model="row.BYZD2" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="BYZD3" label="备用字段3" align="center"
                             :show-overflow-tooltip="true" width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="备用字段3" v-model="row.BYZD3" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="BYZD4" label="备用字段4" align="center"
                             :show-overflow-tooltip="true" width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="备用字段4" v-model="row.BYZD4" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="FDMXXID" label="父代码ID" align="center"
                             :show-overflow-tooltip="true" width="150">
              <template #default="{row}">
                <el-input ref="input45296" placeholder="父代码ID" v-model="row.FDMXXID" type="text" clearable>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column v-if="true" prop="PXH" label="排序号" align="center"
                             :show-overflow-tooltip="true" width="100"></el-table-column>
            <el-table-column v-if="true" prop="YD" label="移动" align="center" fixed="right"
                             :show-overflow-tooltip="true" width="100">
              <template #default="{row,$index}">
                <el-icon style="cursor: pointer" @click="moveDown($index)" v-if="$index+1!==tableData.length"><SortDown/></el-icon>
                <el-icon style="cursor: pointer" @click="moveUp($index)" v-if="$index>0"><SortUp/></el-icon>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="CZ" label="操作" align="center" width="150" fixed="right">
              <template #default="{row,$index}">
                <el-button size="small" class="lui-table-button" type="primary" @click="copyTableRow(row,$index)">复制</el-button>
                <el-button size="small" class="lui-table-button" type="primary" @click="delTableRow(row,$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
      </div>

    </div>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="代码类别编辑"
        @closed="closeForm"
        z-index="1000"
        width="500px">
      <div>
        <dmbpzEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>


  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import {Edit,Search,Plus,Finished,SortUp,SortDown} from '@element-plus/icons-vue'
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "../../../lib/comFun";
import dmbpzEdit from "./dmbpzEdit";

export default defineComponent({
  name: '',
  components: {Edit,Search,Plus,Finished,SortUp,SortDown,dmbpzEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        DMLBMC: ''
      },
      DMLBChecked: '',
      DMLBList: [],
      tableData: [],
      loading: false,
      saved: true,
      dialogVisible: false,
      params:{}
    })

    const getDmlbList = () => {
      axiosUtil.get('/backend/dmbpz/selectDmlbList', {...state.listQuery}).then(res => {
        state.DMLBList = res.data || []
        state.DMLBChecked=''
        state.saved=true
        state.tableData=[]
      })
    }

    const getDataList = () => {
      let params={
        ...state.listQuery,
        DMLBID: state.DMLBChecked

      }
      state.loading=true
      axiosUtil.get('/backend/dmbpz/selectDmxxList', params).then(res => {
        state.tableData = res.data || []
        state.loading=false
        state.saved=true
      })
    }

    const getDMLBXQ = () => {
      if(state.DMLBChecked){
        return state.DMLBList.find(item=>item.DMLBID===state.DMLBChecked)
      }else {
        return {}
      }
    }

    const moveUp = (index) => {
      let temp = state.tableData[index]
      state.tableData[index] = state.tableData[index - 1]
      state.tableData[index - 1] = temp
      reloadPXH()
      state.saved=false
    }

    const moveDown = (index) => {
      let temp = state.tableData[index]
      state.tableData[index] = state.tableData[index + 1]
      state.tableData[index + 1] = temp
      reloadPXH()
      state.saved=false
    }


    const checkRow = (row) => {
      if(state.saved){
        state.DMLBChecked=row.DMLBID
        getDataList()
      }else {
        ElMessageBox.confirm(
            '当前页面未保存，确定切换?',
            '警告',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
        ).then(() => {
          state.DMLBChecked=row.DMLBID
          getDataList()
        }).catch(() => {})
      }

    }

    const addTableRow = () => {
      if(!state.DMLBChecked){
        ElMessage.error('请选择代码类别')
        return
      }
      state.tableData.push({
        DMXXID: comFun.newId(),
        DMLBID: state.DMLBChecked,
        PXH: state.tableData.length+1,
        SFYX: '1'
      })
      state.saved=false
    }

    const delTableRow = (row,index) => {
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        state.tableData.splice(index,1)
        reloadPXH()
        state.saved=false
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }

    const copyTableRow = (row,index) => {
      let copyOne={
        ...row,
        DMXXID: comFun.newId(),
      }
      state.tableData.splice(index,0,copyOne)
      reloadPXH()
      state.saved=false
    }

    const reloadPXH = () => {
      state.tableData.forEach((item,index)=>{
        item.PXH=index+1
      })
    }

    const saveTable = () => {
      if(!state.DMLBChecked){
        return
      }
      let params={
        DMLBID: state.DMLBChecked,
        DMXXList: state.tableData,
      }
      axiosUtil.post('/backend/dmbpz/saveDmxxFrom',params).then(res=>{
        ElMessage.success('保存成功')
        getDataList()
        state.saved=true
      })
    }

    const closeForm=()=>{
      state.dialogVisible=false
      getDmlbList()
    }

    const editRow = (row) => {
      state.params = {editable: true, id: row.DMLBID, operation: 'edit'}
      state.dialogVisible = true
    }


    const addRow = () => {
      state.params = {editable: true, id: '', operation: 'add'}
      state.dialogVisible = true
    }


    onMounted(() => {
      getDmlbList()
    })

    return {
      ...toRefs(state),
      checkRow,
      getDataList,
      delTableRow,
      copyTableRow,
      addTableRow,
      saveTable,
      moveUp,
      moveDown,
      getDmlbList,
      closeForm,
      editRow,
      addRow,
      getDMLBXQ
    }
  }

})
</script>

<style scoped>
.DMLB-row {
  display: flex;
  align-items: center;
  height: 20px;
  font-size: 15px;
  padding: 10px;
  border-bottom: 1px solid rgba(140, 147, 157, 0.37);
  cursor: pointer;
  position: relative;
}

.DMLB-row:hover {
}

.DMLB-row-more {
  display: none;
  position: absolute;
  left: 240px;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(56, 58, 59, 0.09);
  height: 20px;
  padding: 10px;

}

.DMLB-row:hover > .DMLB-row-more {
  display: flex;
  justify-content: center;
  align-items: center;
}

.DMLB-row-select{
  background-color: #1B5FBC;
  color: white;
}

</style>
