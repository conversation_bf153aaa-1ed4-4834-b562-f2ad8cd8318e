<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Save() {
	pageofficectrl.SaveFilePage = "/InsertSeal/Word/AddSign/save?savePath=/InsertSeal/Word/AddSign1/";
	pageofficectrl.WebSave();
}

//加盖印章
function InsertHandSign() {
	try {
		pageofficectrl.zoomseal.AddHandSign();
	} catch (e) {
	}
}

//验证印章
function VerifySeal() {
	pageofficectrl.zoomseal.VerifySeal();
}

//修改密码
function ChangePsw() {
	pageofficectrl.zoomseal.ShowSettingsBox();
}


function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("保存", "Save", 1);
	pageofficectrl.AddCustomToolButton("签字", "InsertHandSign()", 2);
	if (("linux") != (pageofficectrl.ClientOS)) {
		pageofficectrl.AddCustomToolButton("验证印章", "VerifySeal()", 5);
		pageofficectrl.AddCustomToolButton("修改密码", "ChangePsw()", 0);
	}

}

function openFile() {
	// 发起GET请求到后端Controller的路由
	return request({
		url: '/InsertSeal/Word/AddSign/Word1',
		method: 'get',
	});
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit, Save, InsertHandSign, VerifySeal, ChangePsw };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<span style="color: red">操作说明：</span>点“签字”按钮即可，插入印章时的用户名为：李志，密码默认为：111111。
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
