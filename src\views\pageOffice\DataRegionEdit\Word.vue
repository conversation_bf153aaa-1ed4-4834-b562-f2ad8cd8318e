<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const searchKey1 = ref('');
const searchKey2 = ref('');
const definedDataRegionJson = ref([]); // 服务器预定义的数据区域
const dataRegionJson = ref([]); // 已添加的数据区域
const daiDataRegionJson = ref([]);//待添加的数据区域
const poHtmlCode = ref('');


function loadData() {
    // 加载数据的逻辑
    dataRegionJson.value = JSON.parse(pageofficectrl.word.DataRegionsAsJson);

    searchDataRegion(definedDataRegionJson.value, dataRegionJson.value, searchKey1.value);
    searchDataRegion2(dataRegionJson.value, searchKey2.value);
}

const addRegion = (index, row) => {
    // 添加区域的逻辑
    pageofficectrl.word.AddDataRegion(row.name, row.value);
    daiDataRegionJson.value.splice(index, 1);//待添加数据区域的数据删除一个对象
    pageofficectrl.word.RefreshDataRegionList();//刷新当前已添加到文档中的数据区域
    let newItem = {};
    newItem.name = row.name;
    newItem.value = row.value;
    dataRegionJson.value.push(newItem);//已添加数据区域新增一个对象
};

const deleteRegion = (index, row) => {
    // 删除区域的逻辑
    pageofficectrl.word.RefreshDataRegionList();//刷新当前已添加到文档中的数据区域
    let newItem = {};
    newItem.name = row.name;
    newItem.value = pageofficectrl.word.GetValueFromDataRegion(row.name);
    daiDataRegionJson.value.push(newItem);//待添加数据区域新增一个对象

    pageofficectrl.word.DeleteDataRegion(row.name);
    dataRegionJson.value.splice(index, 1);//已添加数据区域的数据删除一个对象

};

const locateRegion = (row) => {
    // 定位区域的逻辑
    pageofficectrl.word.LocateDataRegion(row.name);
};

//加载待添加的dataRegion(服务端预定义的数据区域减去文档中已经有的数据区域就是待添加的数据区域)
function searchDataRegion(definedDRJson, DRJson, kWord1) {
    daiDataRegionJson.value = [];//每次搜索前置空该数组是为了避免搜索到的数据重复
    for (let i = 0; i < definedDRJson.length; i++) {
        let flag = false; //表示每个元素definedDataRegionJson都和arr2中去比较，但凡有一个名称相同，则flag=true，如果flag=false，则将此元素添加到新的json数组中
        for (let j = 0; j < DRJson.length; j++) {
            if (definedDRJson[i].name == DRJson[j].name) {
                flag = true;
                break;
            }
        }
        if (!flag) {
            daiDataRegionJson.value.push(definedDRJson[i]);
        }
    }
    if (kWord1 != "" && kWord1 != null) {
        let searchDataRegionJson = [];//定义一个搜索到的数据的数组

        for (let k = 0; k < daiDataRegionJson.value.length; k++) {
            if (
                daiDataRegionJson.value[k].name
                    .toLocaleLowerCase()
                    .indexOf(kWord1.toLocaleLowerCase()) > -1
            ) {
                searchDataRegionJson.push(daiDataRegionJson.value[k]);
            }
        }
        daiDataRegionJson.value = searchDataRegionJson;
    }
}

//加载已添加的dataRegion(文档中已经有的数据区域)
function searchDataRegion2(drJson, kWord2) {
    if (kWord2 != "" && kWord2 != null) {
        let searchDataRegionJson = [];//定义一个搜索到的数据的数组
        for (let k = 0; k < drJson.value.length; k++) {
            if (
                drJson.value[k].name
                    .toLocaleLowerCase()
                    .indexOf(kWord2.toLocaleLowerCase()) > -1
            ) {
                searchDataRegionJson.push(drJson.value[k]);
            }
        }
        dataRegionJson.value = searchDataRegionJson;
    }
}

//控件中的一些常用方法都在这里调用，比如保存，打印等等
function OnPageOfficeCtrlInit() {
    pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function AfterDocumentOpened() {
    loadData();
}

function Save() {
    pageofficectrl.SaveFilePage = "/DataRegionEdit/save";
    pageofficectrl.WebSave();
}

const fetchData = async () => {
  try {
    const response = await request({
			url: '/DataRegionEdit/Word',
			method: 'get',
		});
		poHtmlCode.value = response.result.poHtml;
        definedDataRegionJson.value=response.result.dataRegions;

  } catch (error) {
    console.error('There has been a problem with your fetch operation:', error);
  }
};

onMounted(() => {
    fetchData();
    window.POPageMounted = { OnPageOfficeCtrlInit, Save, AfterDocumentOpened };//其中OnPageOfficeCtrlInit必须

})
</script>

<style scoped>
.Word {
  margin: 0;
  padding: 0;
  display: flex;
}

.left-container {
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow: auto;
  font-size: 12px;
  height: 100vh;
}

.right-container {
  flex: 1;
  padding: 0px;
  height: 100vh;
}

#podiv {
  height: 100%;
  margin: 0;
  padding: 0;
}

#left-title {
  text-align: center;
  font-size: 16px;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: solid 1px #ccc;
}
</style>
<template>
    <el-container class="Word">
        <el-aside class="left-container" width="400px">
            <div id="left-title">定义数据区域</div>
            <el-input v-model="searchKey1" placeholder="请输入数据区域关键字搜索" @input="loadData" size="small">
                <template #prepend>待添加区域：</template>
            </el-input>
            <el-table :data="daiDataRegionJson"  border height="480px" >
                <el-table-column prop="name" label="数据区域" width="160"></el-table-column>
                <el-table-column prop="value" label="显示文字" width="140" ></el-table-column>
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="addRegion(scope.$index, scope.row)">添加</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-input v-model="searchKey2" placeholder="请输入数据区域关键字搜索" @input="loadData" size="small"
                style="margin-top: 20px">
                <template #prepend>已添加区域：</template>
            </el-input>
            <el-table :data="dataRegionJson"  id="bkmkTable2" border height="480px" >
                <el-table-column prop="name" label="数据区域"></el-table-column>
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-button type="danger" size="small"
                            @click="deleteRegion(scope.$index, scope.row)">删除</el-button>
                        <el-button type="info" size="small" @click="locateRegion(scope.row)">定位</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-aside>

        <el-main class="right-container">
            <div id="podiv" v-html="poHtmlCode"></div>
            <!-- 右侧内容 -->
        </el-main>
    </el-container>
</template>
