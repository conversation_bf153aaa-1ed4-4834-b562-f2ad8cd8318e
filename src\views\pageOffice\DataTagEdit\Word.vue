<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const searchKey1 = ref('');
const definedDataTagJson = ref([]); // 服务器端前定义的数据标签
const daiDefinedDataTagJson = ref([]); // 待添加的数据标签
const isFromStart = ref(false);
const lastOpTag = ref('');
const poHtmlCode = ref('');

function loadData() {
    // 加载数据的逻辑
    searchDataTag(definedDataTagJson.value, searchKey1.value);
}

const addDataTag = (row) => {
    pageofficectrl.word.SetTextToSelection(row.name);
};

const deleteDataTag = (row) => {
    let selectText = pageofficectrl.word.GetTextFromSelection();
    if (row.name != selectText) {
        alert('请先执行‘' + row.name + '’的定位操作，然后再删除。');
    } else {
        pageofficectrl.word.SetTextToSelection('');
    }

};

const locateDataTag = (row) => {
    pageofficectrl.word.SelectionCollapse(0);
    if (isFromStart.value) {
        if (lastOpTag.value == row.name) {
            pageofficectrl.word.HomeKey(6);
        }

        isFromStart.value = false;
    }

    if (!pageofficectrl.word.FindNextText(row.name)) {
        alert('已经搜索到文档末尾。');
        isFromStart.value = true;
    }

    lastOpTag.value = row.name;
};

//加载可以添加的数据标签
function searchDataTag(definedDTJson, kWord1) {
    if (kWord1 != "" && kWord1 != null) {
        let searchDataTagJson = [];//定义一个搜索到的数据的数组

        for (let k = 0; k < definedDTJson.length; k++) {
            if (
                definedDTJson[k].name
                    .toLocaleLowerCase()
                    .indexOf(kWord1.toLocaleLowerCase()) > -1
            ) {
                searchDataTagJson.push(definedDTJson[k]);
            }
        }
        daiDefinedDataTagJson.value = searchDataTagJson;
    } else {
        daiDefinedDataTagJson.value = definedDTJson;
    }

}

//控件中的一些常用方法都在这里调用，比如保存，打印等等
function OnPageOfficeCtrlInit() {
    pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function AfterDocumentOpened() {
    loadData();
}

function Save() {
    pageofficectrl.SaveFilePage = "/DataTagEdit/save";
    pageofficectrl.WebSave();
}

const fetchData = async () => {
  try {
    const response = await request({
			url: '/DataTagEdit/Word',
			method: 'get',
		});
		poHtmlCode.value = response.result.poHtml;
        definedDataTagJson.value=response.result.dataTags;

  } catch (error) {
    console.error('There has been a problem with your fetch operation:', error);
  }
};

onMounted(() => {
    fetchData();
    window.POPageMounted = { OnPageOfficeCtrlInit, Save, AfterDocumentOpened };//其中OnPageOfficeCtrlInit必须

})
</script>

<style scoped>
.Word {
	margin: 0;
	padding: 0;
	display: flex;
}

.left-container {
	width: 470px;
	display: flex;
	flex-direction: column;
	padding: 10px;
	font-size: 12px;
	height: 100vh;
}

.right-container {
	flex: 1;
	padding: 0px;
	height: 100vh;
}

#podiv {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
}

#left-title {
	text-align: center;
	font-size: 16px;
	padding-bottom: 10px;
	margin-bottom: 10px;
	border-bottom: solid 1px #ccc;
}
</style>
<template>
    <el-container class="Word">
        <el-aside class="left-container" width="400px">
            <div id="left-title">定义数据标签</div>
            <el-input v-model="searchKey1" placeholder="请输入数据标签关键字搜索" @input="loadData" size="small">
                <template #prepend>待添加区域：</template>
            </el-input>
            <el-table :data="daiDefinedDataTagJson" border >
                <el-table-column prop="name" label="数据标签"></el-table-column>
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="addDataTag(scope.row)">添加</el-button>
                        <el-button type="danger" size="small" @click="deleteDataTag(scope.row)">删除</el-button>
                        <el-button type="info" size="small" @click="locateDataTag(scope.row)">定位</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-aside>

        <el-main class="right-container">
            <div id="podiv" v-html="poHtmlCode"></div>
            <!-- 右侧内容 -->
        </el-main>
    </el-container>
</template>
