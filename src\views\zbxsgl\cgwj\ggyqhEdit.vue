<template>
    <el-form :model="formData" :rules="rules" ref="vForm" label-position="right" label-width="180px" size="default" class="lui-card-form">
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标项目名称" prop="XMMC">
                    <el-input v-model="formData.XMMC" placeholder="请输入" disabled >
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标项目编号" prop="XMBH">
                    <el-input v-model="formData.XMBH" placeholder="请输入" disabled >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标人" prop="SSDWMC">
                    <el-input v-model="formData.SSDWMC" placeholder="请输入" disabled >
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="资金来源" prop="ZJLY">
                    <el-select v-model="formData.ZJLY" placeholder="请选择" disabled >
                        <el-option v-for="(item, index) in zjlyList" :key="index" :value="item.DMXX" :label="item.DMMC" >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="24" class="grid-cell">
                <el-form-item label="实施地点" prop="SSDD">
                    <el-input v-model="formData.SSDD" placeholder="请输入" clearable >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="24" class="grid-cell">
                <el-form-item label="招标规模及范围" prop="ZBGMJFW">
                    <el-input v-model="formData.ZBGMJFW" type="textarea" :rows="3" placeholder="请输入" clearable >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="报价方式" prop="BJFS">
                    <el-select v-model="formData.BJFS" placeholder="请选择" clearable >
                        <el-option v-for="(item, index) in bjfsList" :key="index" :value="item.DMXX" :label="item.DMMC" >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标项目计划工期" prop="ZBXMJHGQ">
                    <el-input v-model="formData.ZBXMJHGQ" placeholder="请输入" clearable >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="24" class="grid-cell">
                <el-form-item label="工程质量要求" prop="GCZLYQ">
                    <el-input v-model="formData.GCZLYQ" type="textarea" :rows="3" placeholder="请输入" clearable >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="24" class="grid-cell">
                <el-form-item label="资格要求" prop="ZZYQ">
                    <el-input v-model="formData.ZZYQ" type="textarea" :rows="3" placeholder="请输入" disabled >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标文件获取时间" prop="ZBWJHQKSSJ">
                    <el-date-picker 
                        v-model="formData.ZBWJHQKSSJ" 
                        type="datetime"
                        value-format="YYYY-MM-DD HH:mm"
                        format="YYYY-MM-DD HH:mm"
                        placeholder="请选择"
                        :disabled-date="disabledDate"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="结束时间" prop="ZBWJHQJSSJ">
                    <el-date-picker 
                        v-model="formData.ZBWJHQJSSJ" 
                        type="datetime" 
                        value-format="YYYY-MM-DD HH:mm"
                        format="YYYY-MM-DD HH:mm"
                        placeholder="请选择"
                        :disabled-date="disabledDate"
                        @change="changeJssj"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="投标文件递交截止时间" prop="TBWJDJJZSJ">
                    <el-date-picker 
                        v-model="formData.TBWJDJJZSJ" 
                        type="datetime" 
                        value-format="YYYY-MM-DD HH:mm" 
                        format="YYYY-MM-DD HH:mm"
                        placeholder="请选择"
                        :disabled-date="disabledDate"
                        @change="changeKbsj"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="确认是否参加投标时间" prop="QRSFCJTBSJ">
                    <el-date-picker 
                        v-model="formData.QRSFCJTBSJ" 
                        type="datetime" 
                        value-format="YYYY-MM-DD HH:mm" 
                        format="YYYY-MM-DD HH:mm"
                        placeholder="请选择"
                        :disabled-date="disabledDate"
                        @change="changeJssj"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="开标时间" prop="KBSJ">
                    <el-date-picker 
                        v-model="formData.KBSJ" 
                        type="datetime" 
                        value-format="YYYY-MM-DD HH:mm" 
                        format="YYYY-MM-DD HH:mm"
                        placeholder="请选择"
                        :disabled-date="disabledDate"
                        @change="changeKbsj"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="开标地点" prop="KBDD">
                    <el-input v-model="formData.KBDD" placeholder="请输入" clearable >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
    <el-row style="line-height: 50px;">
        <el-col :span="24" style="width: 100%;text-align: center;position: fixed;bottom: 20px;">
            <el-button size="default" type="primary" @click="saveData"> 保存 </el-button>
            <el-button size="default" @click="close"> 关闭 </el-button>
        </el-col>
    </el-row>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
});
const emit = defineEmits(["closeDialog","reload"]);
const formData = ref({});
const rules = ref({
    SSDD: [{ required: true, message: "请输入实施地点", trigger: "blur" }],
    ZBGMJFW: [{ required: true, message: "请输入招标规模及范围", trigger: "blur" }],
    BJFS: [{ required: true, message: "请选择报价方式", trigger: "change" }],
    ZBXMJHGQ: [{ required: true, message: "请输入招标项目计划工期", trigger: "blur" }],
    GCZLYQ: [{ required: true, message: "请输入工程质量要求", trigger: "blur" }],
    ZBWJHQKSSJ: [{ required: true, message: "请选择招标文件获取开始时间", trigger: "change" }],
    ZBWJHQJSSJ: [{ required: true, message: "请选择招标文件获取结束时间", trigger: "change" }],
    TBWJDJJZSJ: [{ required: true, message: "请选择投标文件递交截止时间", trigger: "change" }],
    QRSFCJTBSJ: [{ required: true, message: "请选择确认是否参加投标时间", trigger: "change" }],
    KBSJ: [{ required: true, message: "请选择开标时间", trigger: "change" }],
    KBDD: [{ required: true, message: "请输入开标地点", trigger: "blur" }],
});
const disabledDate = (time) => {
    return time.getTime() < Date.now() - 1000*60*60*24;
};
onMounted(() => {
    formData.value = Object.assign({}, props.data);
    queryBjfsList();
    queryZjlyList();
});
// 报价方式
const bjfsList = ref([]);
const queryBjfsList = () => {
    axiosUtil.get('/backend/cbsxx/common/selectDMB', {
        DMLBID: "BJFS"
    }).then(res=>{
        bjfsList.value = res.data;
    })
}

// 资金来源
const zjlyList = ref([]);
const queryZjlyList = () => {
    axiosUtil.get('/backend/cbsxx/common/selectDMB', {
        DMLBID: "ZJLY"
    }).then(res=>{
        zjlyList.value = res.data;
    })
}

const changeJssj = (val) => {
    formData.value.ZBWJHQJSSJ = val;
    formData.value.QRSFCJTBSJ = val;
}

const changeKbsj = (val) => {
    formData.value.TBWJDJJZSJ = val;
    formData.value.KBSJ = val;
}

// 保存
const vForm = ref(null);
const saveData = () => {
    vForm.value.validate()
    .then((result) => {
        let days = getDaysDifference(formData.value.ZBWJHQKSSJ,formData.value.ZBWJHQJSSJ);
        if(days < 0){
            ElMessage.warning('招标文件获取结束时间必须大于开始时间！');
            return;
        }
        // 若为招标项目，从开始时间到结束时间不能小于20天，若为竞标竞价项目，开始时间到结束时间不能小于3天
        if(formData.value.FXSFS === 'GKZB' && days < 20){
            ElMessage.warning('招标文件获取结束时间必须大于开始时间至少20天！');
            return;
        }
        if(formData.value.FXSFS === 'GKJJ' && days < 3){
            ElMessage.warning('招标文件获取结束时间必须大于开始时间至少3天！');
            return;
        }
        if(formData.value.ZBWJHQJSSJ !== formData.value.QRSFCJTBSJ){
            ElMessage.warning('招标文件获取结束时间必须与确认是否参加投标时间相同！');
            return;
        }
        if(formData.value.TBWJDJJZSJ !== formData.value.KBSJ){
            ElMessage.warning('投标文件递交截止时间必须与开标时间一致！');
            return;
        }
        formData.value.XGRZH = VSAuth.getAuthInfo().permission.userLoginName;
        axiosUtil.post('/backend/xsgl/xswj/saveGgyqh', formData.value).then(res=>{
            if(res.data.success){
                emit('reload');
                ElMessage.success('保存成功！');
            }else{
                ElMessage.error('保存失败！');
            }
        })
    }).catch((err) => {

    });
}

const close = () => {
    emit('closeDialog');
}

const getDaysDifference = (dateStr1, dateStr2) => {
    // 将字符串转换为日期对象
    const date1 = new Date(dateStr1);
    const date2 = new Date(dateStr2);
    // 计算时间（差单位为毫秒）
    const timeDifference = Math.abs(date2 - date1);
    // 将时间差转换为天数（1天 = 24 * 60 * 60 * 1000 毫秒）
    const daysDifference = (timeDifference / (24 * 60 * 60 * 1000)).toFixed(2);
    return daysDifference;
}
defineExpose({});
</script>
<style scoped>
.lui-card-form{
    height: calc(100vh - 160px);
    overflow-y: auto
}
</style>