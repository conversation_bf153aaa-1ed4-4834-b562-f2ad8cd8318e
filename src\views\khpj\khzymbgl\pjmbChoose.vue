<template>
  <div>
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="MBMC">
            <el-input ref="input45296" placeholder="请输入模板名称" v-model="listQuery.MBMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="400px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      @selection-change="handleSelectionChange"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="selection" width="55"/>
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="MBMC" label="模板名称" align="center"
                               :show-overflow-tooltip="true" min-width="230"></el-table-column>
              <el-table-column prop="MBMS" label="模板描述" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="BBH" label="版本号" align="center"
                               :show-overflow-tooltip="true" width="150"></el-table-column>
              <el-table-column prop="CJSJ" label="创建时间" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="XGSJ" label="更新时间" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
<!--              <el-table-column prop="CZ" label="操作" align="center" width="120">-->
<!--                <template #default="scope">-->
<!--                  <el-button size="small" class="lui-table-button" type="primary" @click="saveData(scope.row)">选择</el-button>-->
<!--                </template>-->
<!--              </el-table-column>-->
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      listQuery:{
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      checkList: [],

      PJLBOptions: [],
    })

    const getDataList = () => {
      let params={
        ...state.listQuery,
        SFQY: '1'
      }
      axiosUtil.get('/backend/sckhpj/pjmbwh/selectPjmbPage',params).then(res=>{
        state.tableData = res.data.list || []
        state.total = res.data.total
      })

    }

    const saveData = (value) => {
      if(state.checkList.length===0){
        ElMessage.warning('请选择评价模板')
        return
      }
      emit('submit',state.checkList)

    }

    const handleSelectionChange = (value) => {
      state.checkList=value
    }


    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      emit('close')
    }
    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      closeForm,
      saveData,
      handleSelectionChange

    }
  }

})
</script>

<style scoped>

</style>
