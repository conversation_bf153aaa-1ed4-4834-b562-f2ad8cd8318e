<!-- 专家评标统计明细 -->
<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" label-position="right" label-width="110px" size="default"
             class="lui-page">
      <el-row :gutter="12">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="姓名:" prop="PSSJ" label-width="60px">
            <span>{{ formData.XM }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <div class="zdTitle">评委明细</div>
        <el-table ref="datatable91634" :data="formData.pwmxData" height="300px" :border="true" class="lui-table"
                  :show-summary="false" size="default" :stripe="false" :highlight-current-row="true"
                  :cell-style="{ padding: '10px 0 ' }">
          <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
          <el-table-column prop="KBSJ" label="评标日期" align="center" min-width="160"></el-table-column>
          <el-table-column prop="XMMC" label="项目名称" align="center" min-width="100"></el-table-column>
          <el-table-column prop="CDZT" label="迟到早退" align="center" min-width="100"></el-table-column>
          <el-table-column prop="PBTD" label="评标态度" align="center" min-width="100"></el-table-column>
          <el-table-column prop="PFBHG" label="评分不合格" align="center" min-width="100"></el-table-column>
          <el-table-column prop="JSDFJG" label="技术打分结果" align="center" min-width="100"></el-table-column>
          <el-table-column prop="JSPJF" label="技术平均分" align="center" min-width="100"></el-table-column>
          <el-table-column prop="PLD" label="偏离度" align="center" min-width="100"></el-table-column>
        </el-table>
      </el-row>
    </el-form>
    <div style="width: 100%;display: flex;justify-content: center; margin-top: 10px;margin-bottom: 10px">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-button @click="closeForm()">返回</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted
}
  from 'vue'
import axiosUtil from "../../../../lib/axiosUtil";
import vsAuth from "../../../../lib/vsAuth.js";
import {ElMessage} from 'element-plus'
import comFun from "../../../../lib/comFun";
import Vsfileupload from "../../../components/vsfileupload";

export default defineComponent({
  components: {Vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    }
  },

  setup(props, context) {
    const state = reactive({
      editable: props.params.editable,
      operation: props.params.operation,
      id: props.params.id,

      userInfo: vsAuth.getAuthInfo().permission,
      formData: {
        pwmxData: [],
      },
      rules: {},
    })
    const loadTableData = () => {
      let params={
        RYBS: state.id,
        ...props.params.otherParams
      }
      axiosUtil.get('/backend/zjgl/zjpbtj/selectZjpbxxMx', params).then((res) => {
        if(res.data){
          state.formData=res.data[0]
        }
      });
    }
    const instance = getCurrentInstance()
    const submitForm = (type) => {

    }
    const closeForm = () => {
      context.emit("closeForm")
    }
    const getDMBData = async (DMLBID, resList) => {

    }
    const validateForm = (type) => {

    }


    onMounted(() => {
      loadTableData()
    })
    return {
      ...toRefs(state),
      submitForm,
      validateForm,
      closeForm
    }
  }
})
</script>

<style scoped>
.zdTitle {
  background-color: #E4E6F6;
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  font-weight: 600;
  margin-bottom: 20px;
}

:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}
</style>
