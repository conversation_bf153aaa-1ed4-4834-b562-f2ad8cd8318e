<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');
function Save() {
  pageofficectrl.SaveFilePage = "/CommentOnly/save";
  pageofficectrl.WebSave();
}
function newComment() {
  //插入键盘批注，InsertComment()参数可以为空，也可以不为空，不为空则指批注内容
  pageofficectrl.word.InsertComment();
}

// PageOffice事件回调函数
function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
  // 您可以在这里添加自定义按钮，执行您自定义的js。比如添加保存、打印、另存、关闭等按钮
  pageofficectrl.AddCustomToolButton("保存", "Save()", 1);
  pageofficectrl.AddCustomToolButton("插入批注", "newComment()", 0);
}

function openFile() {
  return request({
    url: '/CommentOnly/Word',
    method: 'get',
  })
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit, newComment, Save };//其中OnPageOfficeCtrlInit必须

})
</script>
<template>
  <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
  <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
</template>
