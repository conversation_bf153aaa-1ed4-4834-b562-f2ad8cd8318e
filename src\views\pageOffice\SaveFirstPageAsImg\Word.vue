<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Save() {
  pageofficectrl.SaveFilePage = "/SaveFirstPageAsImg/save";
  pageofficectrl.WebSave();
}

function OnPageOfficeCtrlInit() {
  pageofficectrl.AddCustomToolButton("保存", "Save()", 1);
  pageofficectrl.AddCustomToolButton("保存首页为图片", "SaveFirstAsImg()", 1);
}

function SaveFirstAsImg() {
  pageofficectrl.SaveFilePage = "/SaveFirstPageAsImg/save";
  pageofficectrl.WebSaveAsImage();
  alert('图片被保存到“SaveFirstPageAsImg/images/”目录下');
}

function openFile() {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/SaveFirstPageAsImg/Word',
    method: 'get',
  });
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit, Save, SaveFirstAsImg };//其中OnPageOfficeCtrlInit必须

})
</script>
<template>
  <div class="Word">
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
  </div>
</template>
