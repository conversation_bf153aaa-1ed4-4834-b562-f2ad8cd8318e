{"_args": [["@vsui/lib-vsui-auth4-vseaf@1.0.0", "F:\\VUEProject\\scgl-product\\scgl-prodect-front-1.0"]], "_from": "@vsui/lib-vsui-auth4-vseaf@1.0.0", "_id": "@vsui/lib-vsui-auth4-vseaf@1.0.0", "_inBundle": false, "_integrity": "sha512-za1CZLqTOSpE1nS1SdMIDRbtvOyahtNoWEhiJzgLJ6LcJF7SirCHPnFVLpBCu+91qSXx9omf4jKhbvstcB/reg==", "_location": "/@vsui/lib-vsui-auth4-vseaf", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vsui/lib-vsui-auth4-vseaf@1.0.0", "name": "@vsui/lib-vsui-auth4-vseaf", "escapedName": "@vsui%2flib-vsui-auth4-vseaf", "scope": "@vsui", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/"], "_resolved": "http://***********:7001/@vsui/lib-vsui-auth4-vseaf/download/@vsui/lib-vsui-auth4-vseaf-1.0.0.tgz", "_spec": "1.0.0", "_where": "F:\\VUEProject\\scgl-product\\scgl-prodect-front-1.0", "author": {"name": "<EMAIL>"}, "bakDependencies": {"jsencrypt": "^3.0.0-rc.1"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "dependencies": {"@vsui/lib-jsext": "file:jsext", "jsencrypt": "^3.2.1"}, "description": "胜软前端vsui.vue框架鉴权组件，后端对接胜软VSEAF框架，支持SECURITY,CAS,SIAM,DEV模式", "devDependencies": {"@babel/core": "^7.14.3", "@babel/plugin-transform-runtime": "^7.14.3", "@babel/polyfill": "7.12.1", "@babel/preset-env": "^7.14.4", "@babel/runtime": "^7.14.0", "@babel/runtime-corejs3": "^7.14.0", "autoprefixer": "^9.7.4", "clean-webpack-plugin": "^4.0.0", "webpack": "5.64.1", "webpack-cli": "^4.7.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "files": ["dist"], "homepage": "http://***********/UE/vsui.lib/vsui-auth4-vseaf/tree/v1.0.0/auth/core", "keywords": ["@vsui", "vsui", "victory", "victorysoft", "lib", "lib-vsui-auth4-vseaf"], "license": "MIT", "main": "dist/lib-vsui-auth4-vseaf.umd.min.js", "name": "@vsui/lib-vsui-auth4-vseaf", "peerDependencies": {}, "private": false, "publishConfig": {"registry": "http://***********:7001/"}, "repository": {"type": "git", "url": "http://***********/UE/vsui.lib/vsui-auth4-vseaf.git"}, "scripts": {"postinstall": "node dist/postinstall"}, "version": "1.0.0"}