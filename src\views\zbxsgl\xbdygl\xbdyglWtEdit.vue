<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="问题标题：" prop="WT">
            <el-input v-model="formData.WT" type="text" placeholder="请输入" clearable :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="问题内容：" prop="NR">
            <el-input v-model="formData.NR" :rows="12"
                      type="textarea" clearable
                      show-word-limit :maxlength="2000"
                      placeholder="请输入"/>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item label="附件上传：" prop="PBDD">
            <vsfileupload v-if="params.type === 'edit'" ref="vsfileupload" style="margin-left: 10px;min-width: 200px" :busId="params.wtid"
                :key="params.wtid" v-model:files="fileList"
                :editable="params.editable" ywlb="xbwtglfj" busType="xbwtglfj"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
     <div style="width: 100%;margin-bottom: 10px;justify-content: center;display: flex">
        <el-button v-if="params.type === 'edit'" type="primary" @click="saveData">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import vsAuth from "@lib/vsAuth";
export default defineComponent({
  name: '',
  components: {axiosUtil,vsfileupload},
  props: {
    params: {
      type: Object,
      default: {},
    },
  },
  setup(props, {emit}) {
    const state = reactive({
        editable: props.params.editable,
        formData: {
            WT: '',
            NR: '',
            FAID: props.params.faid,
            WTID: props.params.wtid,
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        },
        fileList: [],
    })

    const queryDygd = (() =>{
        axiosUtil.get('/backend/xsgl/xbdygl/queryXbWtlbList', props.params).then((res) => {
            if(!!res.data.list){
              state.formData = res.data.list[0];
            }
        });
    })
   
   const closeForm = (() =>{
       emit('closeWtForm' ,false);
   })

   //修改状态
   const saveData = (() =>{
        axiosUtil.post('/backend/xsgl/xbdygl/saveWtxxData', state.formData).then((res) => {
            if (res.code === 1) {
                emit('closeWtForm',true);
                ElMessage.success("保存成功");
            }
        }).catch((err) =>{
            ElMessage.error("保存失败");
        });
   })

    onMounted(() => {
      queryDygd();
    })

    return {
      ...toRefs(state),
      queryDygd,
      closeForm,
      saveData,
    }
  }

})
</script>

<style scoped>
</style>
