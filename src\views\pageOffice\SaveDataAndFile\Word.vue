<script setup>
import { ref, onMounted } from 'vue';
import request from '@/utils/request';

const poHtmlCode = ref('');

function Save() {
	//同时保存数据和文件时的PageOffice的保存逻辑：默认先保存数据，再保存文件，如果保存数据失败了，则不保存文件；如果数据保存成功了，文件保存失败了，则正常返回保存数据成功的返回值
	pageofficectrl.SaveDataPage = "/SaveDataAndFile/SaveData";
	pageofficectrl.SaveFilePage = "/SaveDataAndFile/SaveFile";
	pageofficectrl.WebSave();
	//获取保存结果，根据保存结果进行下一步业务逻辑处理。保存数据和保存文件的返回值用\n分割。
	let saveResult = pageofficectrl.CustomSaveResult;
	let saveDataResult = saveResult.split("\n")[0];
	let saveFileResult = saveResult.split("\n")[1];
	alert("数据保存结果为：" + saveDataResult);
	alert("文件保存结果为：" + saveFileResult);
}

function Close() {
	pageofficectrl.CloseWindow();
}

function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function openFile() {
	return request({
		url: '/SaveDataAndFile/Word',
		method: 'get',
	});
}

onMounted(() => {
	openFile().then(response => {
		poHtmlCode.value = response;
	});

	// 将当前页面methods中定义的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = {
		Save,
		Close,
		OnPageOfficeCtrlInit,
	}; // 此行必须
});
</script>

<template>
	<div class="Word">
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
