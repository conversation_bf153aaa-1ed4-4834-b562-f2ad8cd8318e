<template>
  <div>

    <el-input style="margin-top: 20px" v-model="shyj" type="textarea" :rows="5" placeholder="请输入审核意见"></el-input>
    <div style="display: flex;margin-top: 20px;min-height: 80px" v-if="apprValue !== '0'">
      <div>上传中标通知书：</div>
      <vsfileupload style="margin-left: 10px" :busId="params.businessId" :maxSize="10"
                    :key="params.businessId" v-model:files="fileList"
                    :editable="true" ywlb="zbtzs"/>
    </div>

    <div style="text-align: center; height: 40px; padding: 10px">
      <el-button type="primary" @click="onConfirm">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import vsfileupload from "@views/components/vsfileupload";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    apprValue: String,
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      shyj: "",
      sffsdx: '1',
      blrArray: [],
      showBlrXz: false,
      nextPerformer: [],
      blfs:'BR',
      fileList: []
    })


    const onConfirm = () => {
      if(state.fileList.length===0){
        ElMessage.warning('请上传中标通知书')
        return
      }
      emit("confirm", state.shyj, state.sffsdx, state.nextPerformer)
    }

    const onCancel = () => {
      emit("close")
    }

    onMounted(() => {
      console.error(props)
      if (props.apprValue !== '0') {
        state.shyj = '同意'
      }
      // emit('changeTitle','这是一个测试审核页面')
      // emit('changeWidth',1000)

    })

    return {
      ...toRefs(state),
      onCancel,
      onConfirm

    }
  }

})
</script>

<style scoped>

</style>
