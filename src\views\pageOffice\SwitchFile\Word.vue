<template>
  <div class="Word">
    <div class="sidebar">
      <h3>文件列表</h3>
      <ul>
        <li v-for="file in files" :key="file.id" @click="selectFile(file)" class="file-link">
          {{ file.title }}
        </li>
      </ul>
    </div>
    <div class="content">
      <h4>{{ selectedFile?.title }}</h4>
           <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
           <div style="width:auto; height:100%;" v-html="poHtmlCode"></div>
    </div>
  </div>
</template>

<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

const files = ref([
  { id: 1, title: 'PageOffice对客户端有什么要求', fileName: 'test1.docx' },
  { id: 2, title: 'PageOffice授权协议',  fileName: 'test2.docx' },
  { id: 3, title: '试用版和正式版有什么区别',  fileName: 'test3.docx' },
]);

const selectedFile = ref(null);

function selectFile(file) {
  selectedFile.value = file;
  switchFile(file.fileName);
}

function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
  pageofficectrl.CustomToolbar = false; //隐藏自定义工具栏
}

function openFile(fileName) {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/SwitchFile/Word?fileName='+fileName,
    method: 'get',
  });
}

function switchFile(fileName) {
      openFile(fileName).then(response => {
        poHtmlCode.value = response;
        pageofficectrl.Reload(); //必须，切换打开文件时必须调用pageofficectrl.Reload()
      });
  }

function firstLoadFile(fileName){
  openFile(fileName).then(response => {
        poHtmlCode.value = response;
      });
}

onMounted(() => {
    //如果想要首次打开POBrowser浏览器窗口时不打开文件，则下面的代码可以注释掉，并且注意如果首次打开POBrowser浏览器窗口想要打开文件，则千万不能调用pageofficectrl.Reload()，否则控件会加载两次
    selectedFile.fileName="test1.docx";//默认打开test1.docx
    firstLoadFile(selectedFile.fileName);
    
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit };//其中OnPageOfficeCtrlInit必须

})

</script>

<style>
/* 设置整个页面的样式 */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.Word {
  display: flex;
}

.sidebar {
  flex: 0 0 30%;
  /* 启用Flex布局 */
  height: 100vh;
  /* 占满整个视口宽度（或省略，因为默认就是100%） */
  border-right: 1px solid #ccc;
  /* padding: 10px 100px */
  text-align: center;
  padding: 10px 50px;
}


.file-link {
  cursor: pointer;
  padding: 8px;
  border-bottom: 1px solid #eee;
  text-decoration: underline; 
  color: #3568d7; /* Default link color */
  display: block; /* Make the link fill the entire li element */
}

.file-link:hover {
  background-color: #f0f0f0; /* Background color on hover */
  color: #42b983; /* Text color on hover */
}

.content {
  flex: 0 0 70%;
  padding: 10px;
}

h4{
  color:#42b983;
}

</style>
