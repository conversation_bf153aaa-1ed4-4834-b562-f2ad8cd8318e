<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="200px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{ formData.XMXX.XMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMXX.XMBH }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="招标单位：" prop="ZGDWID">
            <div style="margin-left: 10px">{{ formData.XMXX.SSDWMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系人：" prop="LXR">
            <el-input v-model="formData.LXR" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系人联系方式：" prop="LXFS">
            <el-input v-model="formData.LXFS" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="纪检监督部门联系人：" prop="JJLXR">
            <el-input v-model="formData.JJLXR" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="纪检监督部门联系方式：" prop="JJLXFS">
            <el-input v-model="formData.JJLXFS" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
         <el-col :span="24" class="grid-cell">
          <el-form-item label="公示时间：" prop="GSSJ">
            <div style="display: flex;gap: 10px;align-items: center">
              <el-date-picker
                  v-model="formData.GSSJKS"
                  :disabled="!editable"
                  type="datetime"
                  clearable
                  placeholder="请选择"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
              <div>至</div>
              <el-date-picker
                  v-model="formData.GSSJJS"
                  :disabled="!editable"
                  type="datetime"
                  clearable
                  placeholder="请选择"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
            </div>
          </el-form-item>
        </el-col>




        <el-col :span="24" class="grid-cell">
          <el-form-item label="附件资料：" prop="FJZL" >
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <vsFileUploadTable style="width: 100%;height: 200px" YWLX="TPGS" :key="params.id"
                             :busId="params.id" v-model:fileTableData="fileTableData"  :editable="editable"/>
        </el-col>




      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsFileUploadTable from "@views/components/vsFileUploadTable";


export default defineComponent({
  name: '',
  components: {vsFileUploadTable},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GGID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        XMXX: {}
      },
      rules: {
        LXR: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXFS: [{
          required: true,
          message: '字段值不可为空',
        }],
        JJLXR: [{
          required: true,
          message: '字段值不可为空',
        }],
        JJLXFS: [{
          required: true,
          message: '字段值不可为空',
        }],

      },



      dialogVisible: false,
      ggParams: {},
      fileTableData: []
    })

    const getFormData = () => {
      let params = {
        GGID: state.GGID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/xstzfb/selectXstzById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      })
    }


    const saveData = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params = {
        ...state.formData,
        GGID: state.GGID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        GGMC: state.formData.XMXX.XMMC + '-选商通知'
      }
      if (type === 'submit') {
        params.SHZT = '2'
      }
      state.loading = true
      axiosUtil.post('/backend/xsgl/xstzfb/saveXstzForm', params).then(res => {
        ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading = false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getXmxx = () => {
      let params = {
        WJID: state.formData.WJID
      }
      axiosUtil.get('/backend/xsgl/xstzfb/selectZbxmxx', params).then(res => {
        state.formData.XMXX = res.data
      })
    }





    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      } else {
        state.formData.WJID = props.params.WJID
        getXmxx()
      }

    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,

    }
  }

})
</script>

<style scoped>

</style>
