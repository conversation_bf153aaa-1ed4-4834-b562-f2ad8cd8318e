<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        class="lui-page"
      :model="state"
      ref="vForm"
      label-width="0"
      :inline="false"
        size="default"
      style="height: 100%"
      :disabled="!editable"
    >
      <el-table
        highlight-current-row
        size="default"
        ref="table"
        height="100%"
        fit
        class="lui-table"
        :border="false"
        :data="state.tableData"
      >
        <EleProTableColumn
          v-for="prop in state.tableColumn"
          :col="prop"
          :key="prop.columnKey"
        >
          <template #fileList="{ row, $index }">
            <vsfileupload
                :maxSize="10"
              @getFileList="getFileList"
              :index="$index"
              :editable="editable"
              :busId="row.ZSYWID"
              :key="row.ZSYWID"
              ywlb="DWZTBGFJ"
              busType="dwxx"
              :limit="100"
            ></vsfileupload>
          </template>
          <template #select="{ row, $index }">
            <el-select
              size="normal"
              v-model="row[prop.prop]"
              clearable
              placeholder="请选择"
              @change="changePro(row)"
            >
              <el-option
                v-for="(item, index) in proDetails"
                :key="index"
                :value="item.ZYBM"
                :label="item.ZYMC"
              ></el-option>
            </el-select>
          </template>
          <template #input="{ row, $index }">
            <el-form-item
              v-if="$index > -1"
              :prop="`tableData.${$index}.${prop.prop}`"
              :rules="[
                {
                  required: prop.required,
                  message: `请输入${prop.label}`,
                  trigger: 'blur',
                },
                {
                  max: prop.maxlength,
                  message: `最多输入${prop.maxlength}个字符`,
                  trigger: 'change',
                },
              ]"
            >
              <el-input
                v-model="row[prop.prop]"
                v-tooltip="{
                  newValue: row[prop.prop],
                  oldValue: resultTableData?.find(i => i.ZSWYBS == row.ZSWYBS)?.[prop.prop],
                  label: resultTableData?.find(i => i.ZSWYBS == row.ZSWYBS)?.[prop.prop],
                }"
                :maxlength="prop.maxlength"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </template>
          <template #titleinput="{ row }">
            <el-input
              v-if="row.SHZT != 1"
              v-model="row[prop.prop]"
              placeholder="请输入"
            ></el-input>
            <span v-else>{{ row[prop.prop] }}</span>
          </template>
          <template #time="{ row }">
            <el-date-picker
              v-model="row[prop.prop]"
              type="date"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            >
            </el-date-picker>
          </template>
          <template #opration="{ row, $index }">
            <div>
              <el-button class="lui-table-button" @click="copyRow(row, $index)">复制</el-button>
              <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button v-if="row.SHZT != 1" class="lui-table-button" @click="deleteRow(row, $index)"
                >删除</el-button
              >
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-form>
  </div>
</template>
<script setup>
import { defineProps, reactive, watch, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";

import { v4 as uuidv4 } from "uuid";
import vsfileupload from "@src/views/components/vsfileupload.vue";

const getFileList = (res) => {
  state.tableData[res.index].fileList = res.fileList;
};
const props = defineProps({
  defaultData: {
    type: Array,
    defaultData: () => [],
  },
  proDetails: {
    type: Array,
    defaultData: () => [],
  },
  // 结果表数据，比对用
  resultTableData: {
    type: Object,
    default: () => null,
  },
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  }
});
const state = reactive({
  tableData: [{}],
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    {
      label: "专业名称",
      prop: "ZYMC",
      align: "center",
      showOverflowTooltip: true,
      width: 200,
      // slot: "select"
    },
    {
      label: "业务要求",
      prop: "YWYQ",
      align: "center",
      showOverflowTooltip: true,
      width: 200,
      // slot: "select"
    },
    {
      label: "录入资料说明",
      prop: "LRZLSM",
      align: "center",
      showOverflowTooltip: true,
      width: 200,
      // slot: "select"
    },
    {
      label: "信息项",
      prop: "XXXMC",
      align: "center",
      showOverflowTooltip: true,
      width: 150,
    },
    {
      label: "许可名称",
      prop: "ZSMC",
      align: "center",
      width: 150,
      maxlength: 128,
      required: true,
      slot: "input",
    },
    {
      label: "许可编号",
      prop: "ZSBH",
      align: "center",
      width: 150,
      maxlength: 64,
      required: true,
      slot: "input",
    },
    {
      label: "许可范围",
      prop: "XKFW",
      align: "center",
      width: 150,
      maxlength: 100,
      slot: "input",
    },
    {
      label: "有效开始日期",
      prop: "YXQKS",
      align: "center",
      width: 150,
      slot: "time",
    },
    {
      label: "有效结束日期",
      prop: "YXQJS",
      align: "center",
      width: 150,
      slot: "time",
    },
    {
      label: "发证部门",
      prop: "FZBM",
      align: "center",
      width: 150,
      maxlength: 128,
      slot: "input",
    },
    {
      label: "附件",
      prop: "fileList",
      headerAlign: "center",
      align: "left",
      slot: "fileList",
      width: 150,
    },
    {
      label: "操作",
      align: "center",
      width: 200,
      fixed: "right",
      slot: "opration",
      hide: !props.editable
    },
  ],
});
const changePro = (row) => {
  row.ZYMC = props.proDetails.filter((x) => x.ZYBM == row.ZYBM)[0].ZYMC;
};
watch(
  () => props.defaultData,
  (val) => {
    if (val) {
      val.forEach((x) => {
        const UUID = uuidv4().replace(/-/g, "");
        x.ZSYWID = x.ZSYWID || UUID;
        x.ZSWYBS = x.ZSWYBS || UUID;
      });
    }
    state.tableData = val;
  },
  {
    immediate: true,
  }
);
const copyRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, "");
  state.tableData.splice(index, 0, { ...row, ZSYWID: UUID, ZSWYBS: UUID ,SHZT:''});
};
const insertRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, "");
  state.tableData.splice(index + 1, 0, {
    ZSYWID: UUID,
    ZSWYBS: UUID,
    ZYMC: row.ZYMC,
    ZYBM: row.ZYBM,
    XXXMC: row.XXXMC,
    MBMXID: row.MBMXID,
    YWYQ: row.YWYQ,
    LRZLSM: row.LRZLSM,
    XXX: "",
    ZSMC: "",
    ZSDJ: "",
    YXQKS: null,
    YXQJS: null,
    FZBM: "",
    FJ: "",
  });
};

const deleteRow = (row, index) => {
  ElMessageBox.confirm("是否删除此条数据？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      state.tableData.splice(index, 1);
      ElMessage({
        message: "删除成功!",
        type: "success",
      });
    })
    .catch(() => {});
};
const vForm = ref(null);
const validateForm = () => {
  return vForm.value.validate();
};
defineExpose({
  validateForm,
});
</script>
<style scoped>
:deep(.el-table-fixed-column--right) {
  background-color: rgba(255, 255, 255, 1) !important;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form-item__error) {
  top: 50%;
  transform: translateY(-50%);
  left: 40px;
}
</style>
