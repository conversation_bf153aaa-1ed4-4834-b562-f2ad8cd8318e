<template>
  <div class="zhyy-list-container">
    <el-row class="zhyy-list-searchArea">
      <el-col :span="24">
        <label>模板名称：</label>
        <el-input
          style="width: 150px"
          placeholder="模板名称"
          v-model="params.mbmc"
          clearable
        >
        </el-input>
        <label>模板类型：</label>
        <el-select
          style="width: 200px"
          v-model="params.mblx"
          placeholder="请选择模板类型"
          clearable
        >
          <el-option
            v-for="item in params.mblxArry"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-button
          type="primary"
          style="margin-left: 10px"
          @click="getDataList()"
          >查询</el-button
        >
        <el-button type="primary" style="margin-left: 10px" @click="adddwkh()"
          >新增</el-button
        >
        <el-button type="primary" style="margin-left: 10px" @click="exportExcel"
          >导出</el-button
        >
      </el-col>
    </el-row>
    <!--表格数据-->
    <el-row class="zhyy-list-tableArea">
      <!--&lt;!&ndash; 表格 &ndash;&gt;-->
      <el-table
        class="customer-no-border-table"
        :data="tableData"
        stripe
        border
        width="100%"
        :height="pageHeight"
        :header-cell-style="{ background: '#F4F7FA' }"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          align="center"
          width="50"
        ></el-table-column>
        <el-table-column
          prop="MBLXMC"
          label="模板类型"
          header-align="center"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="MBMC"
          label="模板名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="MBMS"
          label="描述"
          header-align="center"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="CZR"
          label="操作人"
          header-align="center"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="CZSJ"
          label="操作时间"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="SHDWSL"
          label="操作"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            <el-button
              @click="viewMbmx(scope.$index, scope.row)"
              type="text"
              size="small"
            >
              详情</el-button
            >
            <el-button
              v-if="scope.row.STATUS == '0' || scope.row.STATUS == '2'"
              @click="deleteRow(scope.$index, scope.row)"
              type="text"
              size="small"
            >
              删除</el-button
            >
            <el-button
              v-if="scope.row.STATUS == '0' || scope.row.STATUS == '2'"
              @click="editMbmx(scope.$index, scope.row)"
              type="text"
              size="small"
            >
              编辑</el-button
            >
            <el-button
              v-if="scope.row.STATUS == '0' || scope.row.STATUS == '2'"
              @click="ableRow(scope.$index, scope.row, '1')"
              type="text"
              size="small"
            >
              启用</el-button
            >
            <el-button
              v-if="scope.row.STATUS == '1'"
              @click="ableRow(scope.$index, scope.row, '2')"
              type="text"
              size="small"
            >
              禁用</el-button
            >
            <el-button
              v-if="scope.row.STATUS == '1'"
              @click="copyMbxx(scope.$index, scope.row, '0')"
              type="text"
              size="small"
            >
              复制</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="params.currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next,jumper "
        :total="params.total"
      ></el-pagination>
    </div>
    <el-dialog
      title="模板创建"
      center
      v-model="dwkhdialogVisible"
      v-if="dwkhdialogVisible"
      :append-to-body="false"
      width="85%"
      top="3vh"
      style="margin-top: 0vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="onClose"
      class="abow_dialog"
    >
      <div class="el-dialog-div">
        <div style="text-align: center">
          <zrmbgMx
            :appUser="params.appUser"
            :edit="this.edit"
            :MBID="params.MBID"
            v-if="dwkhdialogVisible"
          ></zrmbgMx>
          <el-button type="primary" @click="onClose" size="mini">
            返回
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElNotification } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import zrmbgMx from "./zrmbgMx.vue";
import axiosUtil from '@src/lib/axiosUtil.js';

let dwkhdialogVisible = ref(false);
let params = reactive({
    MBID: '',
    ZYLB: [],
    hkqj: [],
    SJYWBM: '',
    PJDX: '',
    JDLX: '',
    mbmc: '',
    mblx: '',
    mblxArry: [{
    value: "1",
    label: "企业",
  },
  {
    value: "2",
    label: "队伍",
  }],
    appUser: '',
    model: 'dwjdkh',
    xmnd: String(new Date().getFullYear()),
    currentPage: 1,
    pageSize: 10,
    total: 0,
    recent: 0,
    editRowId: null,
    clickArr: [],
    formLabelWidth: '100px',
    tableStyle: "width:100%;height:calc(100vh - 310px)",
    modelTemesMap: {},
    inputZymc: '',
    gclbdm: '',
    cbsdwbs: '',
    jyData: [],
    zylbJsonArray: []
})
//表数据
let tableData = reactive([])
let multipleSelection = reactive([])
let form = reactive({})

let pageHeight = computed(() =>{
  return 'calc(100vh - 295px)'
})
/**导出Excel */
const exportExcel = () =>{
    let par = {
        pageNum: params.currentPage,
        pageSize: params.pageSize,
        total: params.total,
        LOGINNAME: params.appUser.loginName,
        mbmc: params.mbmc,
        mblx: params.mblx
    }
    let KHMC='引进模板管理'
    let finparams={
        title:KHMC,
        name:KHMC,
        params:par,
        column:[[
            {field:'MBLXMC',title:'模板类型',width:300,halign:'center',align:'center'},
            {field:'MBMC',title:'模板名称',width:300,halign:'center',align:'center'},
            {field:'MBMS',title:'描述',width:300,halign:'center',align:'center'},
            {field:'CZR',title:'操作人',width:300,halign:'center',align:'center'},
            {field:'CZSJ',title:'操作时间',width:300,halign:'center',align:'center'}
        ]]
    }
    axiosUtil.exportExcel('/sldwgl/mbgl/exportExcel', finparams)
}
const copyMbxx = (index, row) =>{
    // await util.postJson('/sldwgl/mbgl/copyMbxx', row, this.model)
    axiosUtil.post('/sldwgl/mbgl/copyMbxx', row).then(res =>{
        if(res.data.meta.success){
            ElMessage({
                message: '复制成功',
                type: 'success'
            })
            getDataList()
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
}
const editMbmx = (index, row) =>{
    // util.postJson('/sldwgl/mbgl/checkDisable', row, this.model)
    axiosUtil.post('/sldwgl/mbgl/checkDisable', row).then(res =>{
        if(res.data.data.length > 0){
            ElMessage({
                message: '该模板已存在关联专业，无法编辑',
                type: 'warning'
            })
            return
        }
        params.edit = 'edit'
        params.MBID = row.MBID
        dwkhdialogVisible.value = true
    })
}
const ableRow = (index, row, type) =>{
    row.type = type
    // util.postJson('/sldwgl/mbgl/ableRow', row, this.model)
    axiosUtil.post('/sldwgl/mbgl/ableRow', row).then(res =>{
        if(res.data.meta.success){
            ElMessage({
                message: '操作成功',
                type: 'success'
            })
            getDataList()
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
}
const deleteRow = (index, row) =>{
    // util.postJson('/sldwgl/mbgl/checkDisable', row, this.model)
    axiosUtil.post('/sldwgl/mbgl/checkDisable', row).then(res =>{
        if(res.data.data.length > 0){
            ElMessage({
                message: '该模板已存在关联专业，无法删除',
                type: 'warning'
            })
            return
        }else{
            // util.postJson('/sldwgl/mbgl/deleteRow', row, this.model)
            axiosUtil.post('/sldwgl/mbgl/deleteRow', row).then(resp =>{
                if(resp.data.meta.success){
                    ElMessage({
                        message: '删除成功',
                        type: 'success'
                    })
                    getDataList()
                }else{
                    ElMessage({
                        message: resp.data.meta.message,
                        type: 'error'
                    })
                }
            })
        }
    })
}
const pubRow = (index, row, status) =>{
    row.SHZT = status
    axiosUtil.post('/sldwgl/dwkh/pubRow', row).then(res =>{
        if(res.data.meta.success){
            ElMessage({
                message: '操作成功',
                type: 'success'
            })
            getDataList()
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
}
const adddwkh = () =>{
    // params.KHMC = ''
    // this.KHBS=util.newId(); -- 无util文件，无法判断newId生成规则
    params.edit = true
    dwkhdialogVisible.value = true
}
const viewMbmx = (index, row) =>{
    params.edit = 'view'
    params.MBID = row.MBID
    dwkhdialogVisible.value = true
}
//关闭对话框
const onClose = () =>{
    params.ZYLB=[]
    params.khqj=[]
    params.SJYWBM="",
    params.PJDX="",
    params.JDLX=""
    getDataList()
    dwkhdialogVisible.value = false;
}
const getAppUser = () =>{
    // params.appUser = util.getAppUser()
    return new Promise((resolve, reject) =>{
        resolve()
    })
}
/**序号 */
const indexMethod = (index) =>{
    return index + params.pageSize * (params.currentPage - 1) + 1;
}
/**页面数据条数改变时 */
const handleSizeChange = (val) =>{
    params.currentPage = 1;
    params.pageSize = val;
    getDataList();
}
/**翻页 */
const handleCurrentChange = (val) =>{
    params.currentPage = val;
    getDataList();
}
/**获取数据 */
const getDataList = () =>{
    let par = {
        pageNum: params.currentPage,
        // 每页的数据条数
        pageSize: params.pageSize,
        total: params.total,/**/
        LOGINNAME:params.appUser.loginName,
        mbmc:params.mbmc,
        mblx:params.mblx
    }
    // util.getObjectResult(await util.get('/sldwgl/mbgl/queryMb',params,this.model))
    axiosUtil.get('/sldwgl/mbgl/queryMb',params).then(res =>{
        if(res.data.meta.success){
            tableData.length = 0
            tableData.push(...res.data.data.rows)
            params.total = res.data.data.total
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
}
/**分页多行变少行，点击翻页不刷新问题 */
const pageClick = e =>{
    if(!tableData.length){
        return false
    }
    let dom = e.target
    if(dom.className === "btn-next" || (dom.className === "el-icon el-icon-arrow-right" && dom.parentNode.className !== "btn-next disabled")){
        params.currentPage += 1;
        params.currentPage >= Math.ceil(params.total / params.pageSize) ? (params.currentPage = Math.ceil(params.total / params.pageSize)) : params.currentPage;
    }else if (dom.className === "btn-prev" || (dom.className === "el-icon el-icon-arrow-left" && dom.parentNode.className !== "btn-prev disabled")) {
        params.currentPage -= 1;
        params.currentPage <= 1 ? (params.currentPage = 1) : params.currentPage;
    } else if ( dom.className === "el-icon more btn-quicknext el-icon-d-arrow-right") {
        params.currentPage = Math.ceil(params.total / params.pageSize);
    } else if ( dom.className === "el-icon more btn-quickprev el-icon-d-arrow-left") {
        params.currentPage = 1;
    } else if (dom.className === "number") {
        params.currentPage = Number(dom.innerHTML);
    } else {
        return false;
    }
    getDataList();
}
const querySqlbData = () =>{
    let par = {
        zymc : params.inputZymc ,
        gclbdm: params.gclbdm,
        CBSDWBS: params.cbsdwbs
    }
    axiosUtil.get('/sldwgl/cbsDwxx/whDwzy', par).then(res =>{
        if(res.data.meta.success){
            let row = res.data.data
            params.jyData = row
            if(row.length){
                row.forEach(item =>{
                    item.value = item.YWBM
                    item.label = item.YWMC
                })
            }
            let treeData = transData(row, 'YWBM', 'PYWBM', 'children')
            params.zylbJsonArray = treeData
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
}
/**
 * json格式转树状结构
 * @param   {json}      json数据
 * @param   {String}    id的字符串
 * @param   {String}    父id的字符串
 * @param   {String}    children的字符串
 * @return  {Array}     数组
 */
const transData = (a, idStr, pidStr, childrenStr) =>{
let r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
for(; i < len; i++){
    a[i]['isparent'] = false;
    hash[a[i][id]] = a[i];
}
for(; j < len; j++){
    let aVal = a[j], hashVP = hash[aVal[pid]];
    if(hashVP){
        !hashVP[children] && (hashVP[children] = []);
        hashVP[children].push(aVal);
    }else{
        r.push(aVal);
    }
}
//this.setIsParent(r);
return r;
}
const setIsParent = (arr) => {
    for(let j=0; j < arr.length; j++){
        if(arr[j].children && arr[j].children.length > 0){
            arr[j].isparent = true;
            setIsParent(arr[j].children);
        }
    }
}
const handleChangeLb = (value) => {
    params.ZYLB=value
    params.SJYWBM=value[2];
    getDataList();
}
onMounted(() =>{
    getAppUser().then(() =>{
        getDataList()
    })
    querySqlbData()
})

</script>

<style scoped>
::v-deep .el-cascader__dropdown {
  height: 250px;
}
.dialog-footer {
  text-align: center;
}
.el-cascader-menu__wrap {
  height: 250px;
}

body .el-table th.gutter {
  display: table-cell !important;
}
</style>
