'use strict';
const utils = require('../utils');
const devBuildConfig = require('./dev.conf.js');
const devWebpackConfig = require("./webpack.conf");
const chalk = require('chalk');
const { merge } = require('webpack-merge');
const FriendlyErrorsPlugin = require('clean-friendly-errors-webpack-plugin');
const portfinder = require('portfinder');

const webpackConfig = merge(devWebpackConfig,{
    entry: {
        app: ['./vsui.vue/main.js']
    },
});
    
const SUCCMSG = chalk.cyan(utils.sign + `

    [框架核心模式，如想运行框架自带示例请运行：npm run dev:demo，如想运行项目请运行：npm run dev]

    最后编译时间：{date}

    应用程序运行在这里: ${chalk.white('http://{host}:{port}{path}')}`);

module.exports = new Promise((resolve, reject) => {
    portfinder.basePort = webpackConfig.devServer.port;
    portfinder.getPort((err, port) => {
        if (err) {
            reject(err);
        } else {
            // publish the new Port, necessary for e2e tests
            process.env.PORT = port;
            // add port to devServer config
            webpackConfig.devServer.port = port;
            // Add FriendlyErrorsPlugin
            webpackConfig.plugins.push(new FriendlyErrorsPlugin({
                compilationSuccessInfo: {
                    messages: [SUCCMSG.format({
                        date: (new Date()).format('yyyy-MM-dd hh:mm:ss'),
                        host: webpackConfig.devServer.host,
                        port: webpackConfig.devServer.port,
                        path: webpackConfig.devServer.publicPath
                    })]
                },
                onErrors: devBuildConfig.notifyOnErrors
                    ? utils.createNotifierCallback()
                    : undefined
            }));

            resolve(webpackConfig);
        }
    });
});
