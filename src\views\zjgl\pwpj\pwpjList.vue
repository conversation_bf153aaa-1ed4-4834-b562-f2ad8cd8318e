<!-- 评委评价列表 -->
<template>
    <el-form style="position: relative;" ref="vForm" :rules="rules" class="container" label-position="right" label-width="120px"
        size="default" @submit.prevent>
        <el-row ref="grid70953" v-show="true">
            <el-col :span="6" class="grid-cell">
                <el-form-item label="项目名称:" prop="hymc">
                    <el-input placeholder="请输入" v-model="listQuery.XMMC" type="text" clearable>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6" class="grid-cell">
                <el-form-item label="项目编号:" prop="hymc">
                    <el-input placeholder="请输入" v-model="listQuery.XMBM" type="text" clearable>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6" class="grid-cell">
                <el-form-item label="项目类别:" prop="hydd">
                    <el-select ref="select14540" v-model="listQuery.XMLB" class="full-width-input" clearable>
                        <el-option v-for="(item, index) in XMLBOptions" :key="index" :label="item.DMMC" :value="item.DMXX"
                            :disabled="item.disabled"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="1" class="grid-cell" >
                <div class="static-content-item" v-show="true" style="display: block;">
                    <el-button ref="button91277" @click="getDataList" type="primary">查询</el-button>
                </div>
            </el-col>
            <!-- <el-col :span="2" class="grid-cell">
                <div class="static-content-item" v-show="true" style="display: block;">
                    <el-button ref="button91277" @click="gjSearch" type="primary">高级查询</el-button>
                </div>
            </el-col> -->
            <el-col :span="1">
                <div class="static-content-item" v-show="true" style="display: block;">
                    <el-button ref="button91277" @click="exportExcel" type="primary">导出</el-button>
                </div>
            </el-col>
        </el-row>
        <el-row ref="grid71868" v-show="true">
            <el-col :span="24" class="grid-cell">
                <div class="container-wrapper" v-show="true">
                  <el-table ref="dataTable" :data="tableData" height="calc(100vh - 250px)"
                            :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                            :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
                        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
                        <el-table-column prop="XMMC" label="项目名称" header-align="center" align="left"
                            min-width="160"></el-table-column>
                        <el-table-column prop="XMBM" label="项目编号" header-align="center" align="left"
                            min-width="100"></el-table-column>
                        <el-table-column prop="XMLB" label="项目类别" header-align="center" align="left"
                            min-width="100"></el-table-column>
                        <el-table-column prop="SSDW" label="所属单位" header-align="center" align="left"
                            min-width="100"></el-table-column>
                        <el-table-column prop="XMED" label="项目额度" header-align="center" align="right"
                            min-width="100"></el-table-column>
                        <el-table-column prop="HYMC" label="会议名称" header-align="center" align="left"
                            min-width="160"></el-table-column>
                        <el-table-column prop="PJZT" label="评价状态" header-align="center" align="center"
                            min-width="100"></el-table-column>
                        <el-table-column prop="CZ" label="操作" align="center"  min-width="100">
                            <template #default="scope">
                              <div v-if="scope.row.PBHYBS">
                                <el-button v-if="scope.row.PJSHZT==='1'" size="small" text type="primary"
                                           @click="viewData(scope.row)">查看</el-button>
                                <el-button v-else size="small" text type="primary"
                                            @click="editData(scope.row)">评价</el-button>
                              </div>
                                    <span v-else>无法评价</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                        :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, jumper, sizes"
                        @size-change="getDataList" @current-change="getDataList" :total="total">
                    </el-pagination>
                </div>
            </el-col>
        </el-row>
        <!-- 评委抽取 非招标弹出页 -->
        <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="评委单项目评价" z-index="1000" width="80%">
            <div>
                <pwpjEdit :key="XMBS" :params="params" @closeForm="closeForm" />
            </div>
        </el-dialog>
    </el-form>
</template>

<script>
import {
    defineComponent,
    toRefs,
    reactive,
    getCurrentInstance,
    onMounted
}
    from 'vue'
// 请求方法 get,post,put,del,downloadFile
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import pwpjEdit from "./pwpjEdit.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import comFun from '../../../lib/comFun';


export default defineComponent({
    name: '',
    components: {pwpjEdit},
    props: {},
    setup(props, { emit }) {
        const state = reactive({
            userInfo: vsAuth.getAuthInfo().permission,
            // XMBS: '',
            listQuery: {
                XMMC: '',
                XMBM: '',
                XMLB: '',
                page: 1,
                size: 10,
            },
            tableData: [],
            total: 0,
            rules: {},
            xmlbArray: [],
            dialogVisible: false,
            dialogPbVisible:false,
            params: null,
            XMLBOptions: [],
        })

        const getDataList = () => {
            axiosUtil.get('/backend/zjgl/pwpb/pwpjList', state.listQuery).then((res) => {
              state.tableData = res.data.list
              state.total = res.data.total
          });

        }
        // const delRowData = (XMBS) => {
        //     ElMessageBox.confirm(
        //         '确定删除该条数据?',
        //         '警告',
        //         {
        //             confirmButtonText: '确定',
        //             cancelButtonText: '取消',
        //             type: 'warning',
        //         }
        //     ).then(() => {

        //     }).catch(() => {
        //         ElMessage({
        //             type: 'info',
        //             message: '取消删除',
        //         })
        //     })
        // }
        /**打开评价编辑页*/
        const editData = (value) => {
            console.log("value========",value);
            if (value) {
                if(value.PWPJBS){
                     state.params = { editable: true, id: value.PWPJBS, operation: 'edit'}
                }else{
                     state.params = { editable: true, id: comFun.newId(), operation: 'add', HYBS:value.PBHYBS, XMBS:value.CGXMBS} //,editRow: value
                }

            }
            state.dialogVisible = true
        }

        // 点击编辑
        const viewData = (value) => {
            state.params = { editable: false, id: value.PWPJBS, operation: 'view' ,editRow: value}
            state.dialogVisible = true
        }

        // 新增
        const add = () => {
            state.dialogPbVisible = true
            state.params = { editable: true, id: comFun.newId(), operation: 'add' }
        }

        /**从代码表获取码值 */
        const getDMBData = async (DMLBID, resList) => {
            console.log(resList);
        let params = {
            DMLBID
        }
        let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
        state[resList] = res.data
        }


        const exportExcel = ()=>{
            let column = [[
                { field: 'XMMC', title: '项目名称', width: 280, halign: 'center', align: 'left' },
                { field: 'XMBM', title: '项目编号', width: 280, halign: 'center', align: 'left' },
                { field: 'XMLB', title: '项目类别', width: 280, halign: 'center', align: 'left' },
                { field: 'SSDW', title: '所属单位', width: 280, halign: 'center', align: 'left' },
                { field: 'XMED', title: '项目额度', width: 400, halign: 'center', align: 'left' },
                { field: 'HYMC', title: '会议名称', width: 280, halign: 'center', align: 'left' },
                { field: 'PJZT', title: '评价状态', width: 280, halign: 'center', align: 'left' },
            ]]
            let finparams = {
                title: '评委评价',
                name: '评委评价',
                params: state.listQuery,
                column: column
            }
            axiosUtil.exportExcel('/backend/commonExport/pwpjExcel',finparams)
//
        }

        const closeForm = () => {
            state.dialogVisible = false
            getDataList()
        }
        const closePbForm = () => {
            state.dialogPbVisible = false
            getDataList()
        }
        onMounted(async () => {
            getDMBData("XMLB", "XMLBOptions")
            getDataList()
        })

        return {
            ...toRefs(state),
            getDataList,
            closeForm,
            // delRowData,
            viewData,
            editData,
            add,
            exportExcel,
            closePbForm
        }
    }
})

</script>
<style>
.container {
    padding: 10px 8px;
}
</style>

