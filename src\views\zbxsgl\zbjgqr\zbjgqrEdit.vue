<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{ formData.XMXX.XMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMXX.XMBH }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="招标单位：" prop="ZGDWID">
            <div style="margin-left: 10px">{{ formData.XMXX.SSDWMC }}</div>
          </el-form-item>
        </el-col>
      </el-row>



      <div v-if="formData.XMXX.SFFBD==='1'">
        <el-tabs
            v-model="ActiveTab"
            type="border-card">
          <el-tab-pane
              v-for="(item,index) in formData.XSBDList"
              :key="index"
              :label="'标段：'+(item.BDMC || '')"
              :name="index+''">
            <el-row :gutter="0">
              <el-col :span="24" class="grid-cell">
                <el-form-item label="标段名称：" prop="BDMC">
                  <div style="margin-left: 10px">{{ item.BDMC }}</div>
                </el-form-item>
              </el-col>

<!--              <el-col :span="8" class="grid-cell border-right">-->
<!--                <el-form-item label="最高限价（元/%）：" :prop="`XSBDList.${index}.BD`" :rules="rules.BD">-->
<!--                  <el-input v-model="item.BD" type="text" placeholder="请输入" clearable :disabled="!editable"-->
<!--                            @input="item.BD=item.BD.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')">-->
<!--                  </el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->


              <el-col :span="24" class="grid-cell border-right">
                <el-form-item label="标段内容说明：" prop="BDNRSM">
                  <div style="margin-left: 10px">{{ item.BDNRSM }}</div>
                </el-form-item>
              </el-col>

<!--              <el-col :span="8" class="grid-cell border-right">-->
<!--                <el-form-item label="报价方式：" :prop="`XSBDList.${index}.BJFS`" :rules="rules.BJFS">-->
<!--                  <el-select v-model="item.BJFS" class="full-width-input" :disabled="!editable">-->
<!--                    <el-option v-for="(item, index) in BJFSOptions" :key="index" :label="item.DMMC"-->
<!--                               :value="item.DMXX"></el-option>-->
<!--                  </el-select>-->
<!--                </el-form-item>-->
<!--              </el-col>-->

              <el-col :span="24" class="grid-cell border-right no-border-bottom">
                <el-form-item label="评标情况：" prop="PBQK">
                  <el-button type="primary" style="margin-bottom: 5px;margin-left: 5px;margin-top: 5px" @click="addPBQK(item)" v-if="editable && formData.XMXX.XSFS==='GKZB' && params.tableEdit">添加</el-button>
                  <el-table ref="datatable91634" :data="item.JGMXList" height="200px"
                            :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                            :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                    <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                    <el-table-column prop="SFZB" label="是否中标" align="center"
                                      width="120">
                      <template #default="{row,$index}">
                        <el-form-item label="" :prop="`XSBDList.${index}.JGMXList.${$index}.SFZB`" label-width="0" :rules="rules.SFZB">
                          <el-select v-model="row.SFZB" class="full-width-input" :disabled="!editable">
                            <el-option v-for="(item, index) in SFZBOptions" :key="index" :label="item.DMMC"
                                       :value="item.DMXX"></el-option>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="TJZBSX" label="中标顺序" align="center"
                                      width="120">
                      <template #default="{row,$index}" v-if="params.tableEdit">
                        <el-form-item label="" :prop="`XSBDList.${index}.JGMXList.${$index}.TJZBSX`" label-width="0" :rules="row.SFZB==='1'?rules.TJZBSX : null">
                          <el-input v-model="row.TJZBSX" type="text" placeholder="请输入" :disabled="!editable"
                                    @input="row.TJZBSX=row.TJZBSX.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="DWMC" label="投标人名称" align="center"
                                      min-width="160">
                      <template #default="{row,$index}" v-if="params.tableEdit">
                        <el-form-item label="" :prop="`XSBDList.${index}.JGMXList.${$index}.DWMC`" label-width="0" :rules="rules.DWMC" v-if="editable && formData.XMXX.XSFS==='GKZB'">
                          <el-input v-model="row.DWMC" type="text" placeholder="请输入" :disabled="!editable">
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="BJ" label="投标报价（元/%）" align="center"
                                      width="120">
                      <template #default="{row,$index}" v-if="params.tableEdit">
                        <el-form-item label="" :prop="`XSBDList.${index}.JGMXList.${$index}.BJ`" label-width="0" :rules="row.SFZB==='1'?rules.BJ : null">
                          <el-input v-model="row.BJ" type="text" placeholder="请输入" :disabled="!editable"
                                    @input="row.BJ=row.BJ.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>

                    <el-table-column prop="XMJL" label="项目负责人" align="center"
                                      width="120">
                      <template #default="{row,$index}" v-if="params.tableEdit">
                        <el-form-item label="" :prop="`XSBDList.${index}.JGMXList.${$index}.XMJL`" label-width="0" :rules="row.SFZB==='1'?rules.XMJL : null">
                          <el-input v-model="row.XMJL" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>

                    <el-table-column prop="ZBJMS" label="备注" align="center"
                                      width="120">
                      <template #default="{row,$index}" v-if="params.tableEdit">
                        <el-form-item label="" :prop="`XSBDList.${index}.JGMXList.${$index}.ZBJMS`" label-width="0" :rules="rules.ZBJMS">
                          <el-input v-model="row.ZBJMS" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>

                  </el-table>
                </el-form-item>
              </el-col>

              <!--              <el-col :span="16" class="grid-cell border-right">-->
              <!--                <el-form-item label="中标单位：" prop="ZBDW">-->
              <!--                  <div style="margin-left: 10px">{{ getZbdw(item.JGMXList) }}</div>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->

              <!--              <el-col :span="8" class="grid-cell border-right">-->
              <!--                <el-form-item label="中标金额（万元）：" prop="ZBJE">-->
              <!--                  <div style="margin-left: 10px">{{ getZbje(item.JGMXList,item) }}</div>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>

      <el-row :gutter="0" v-else-if="formData.XSBDList[0]">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="标段名称：" prop="BDMC">
            <div style="margin-left: 10px">{{ formData.XSBDList[0]?.BDMC }}</div>
          </el-form-item>
        </el-col>

<!--        <el-col :span="8" class="grid-cell border-right">-->
<!--          <el-form-item label="最高限价（元/%）：" :prop="`XSBDList.0.BD`" :rules="rules.BD">-->
<!--            <el-input v-model="formData.XSBDList[0].BD" type="text" placeholder="请输入" clearable :disabled="!editable"-->
<!--                      @input="formData.XSBDList[0].BD=formData.XSBDList[0].BD.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')">-->
<!--            </el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->

        <el-col :span="24" class="grid-cell">
          <el-form-item label="标段内容说明：" prop="BDNRSM">
            <div style="margin-left: 10px">{{ formData.XSBDList[0]?.BDNRSM }}</div>
          </el-form-item>
        </el-col>

<!--        <el-col :span="8" class="grid-cell border-right">-->
<!--          <el-form-item label="报价方式：" :prop="`XSBDList.0.BJFS`" :rules="rules.BJFS">-->
<!--            <el-select v-model="formData.XSBDList[0].BJFS" class="full-width-input" :disabled="!editable">-->
<!--              <el-option v-for="(item, index) in BJFSOptions" :key="index" :label="item.DMMC"-->
<!--                         :value="item.DMXX"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->

        <el-col :span="24" class="grid-cell border-right no-border-bottom">
          <el-form-item label="评标情况：" prop="PBQK">
            <el-button type="primary" style="margin-bottom: 5px;margin-left: 5px;margin-top: 5px" @click="addPBQK(formData.XSBDList[0])" v-if="editable && formData.XMXX.XSFS==='GKZB' && params.tableEdit">添加</el-button>
            <el-table ref="datatable91634" :data="formData.XSBDList[0]?.JGMXList" height="200px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="SFZB" label="是否中标" align="center"
                                width="120">
                <template #default="{row,$index}">
                  <el-form-item label="" :prop="`XSBDList.0.JGMXList.${$index}.SFZB`" label-width="0" :rules="rules.SFZB">
                    <el-select v-model="row.SFZB" class="full-width-input" :disabled="!editable">
                      <el-option v-for="(item, index) in SFZBOptions" :key="index" :label="item.DMMC"
                                 :value="item.DMXX"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="TJZBSX" label="中标顺序" align="center"
                                width="120">
                <template #default="{row,$index}" v-if="params.tableEdit">
                  <el-form-item label="" :prop="`XSBDList.0.JGMXList.${$index}.TJZBSX`" label-width="0" :rules="row.SFZB==='1'? rules.TJZBSX : null">
                    <el-input v-model="row.TJZBSX" type="text" placeholder="请输入" :disabled="!editable"
                              @input="row.TJZBSX=row.TJZBSX.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="DWMC" label="投标人名称" align="center"
                                min-width="160">
                <template #default="{row,$index}" v-if="params.tableEdit">
                  <el-form-item label="" :prop="`XSBDList.0.JGMXList.${$index}.DWMC`" label-width="0" :rules="rules.DWMC" v-if="editable && formData.XMXX.XSFS==='GKZB'">
                    <el-input v-model="row.DWMC" type="text" placeholder="请输入" :disabled="!editable">
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="BJ" label="投标报价（元/%）" align="center"
                                width="120">
                <template #default="{row,$index}" v-if="params.tableEdit">
                  <el-form-item label="" :prop="`XSBDList.0.JGMXList.${$index}.BJ`" label-width="0" :rules="row.SFZB==='1'? rules.BJ : null">
                    <el-input v-model="row.BJ" type="text" placeholder="请输入" :disabled="!editable"
                              @input="row.BJ=row.BJ.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column prop="XMJL" label="项目负责人" align="center"
                                width="120">
                <template #default="{row,$index}" v-if="params.tableEdit">
                  <el-form-item label="" :prop="`XSBDList.0.JGMXList.${$index}.XMJL`" label-width="0" :rules="row.SFZB==='1'? rules.XMJL : null">
                    <el-input v-model="row.XMJL" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column prop="ZBJMS" label="备注" align="center"
                                width="120">
                <template #default="{row,$index}" v-if="params.tableEdit">
                  <el-form-item label="" :prop="`XSBDList.0.JGMXList.${$index}.ZBJMS`" label-width="0" :rules="rules.ZBJMS">
                    <el-input v-model="row.ZBJMS" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>

        <!--        <el-col :span="16" class="grid-cell border-right">-->
        <!--          <el-form-item label="中标单位:" prop="ZBDW">-->
        <!--            <div style="margin-left: 10px">{{ getZbdw(formData.XSBDList[0]?.JGMXList) }}</div>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->

        <!--        <el-col :span="8" class="grid-cell border-right">-->
        <!--          <el-form-item label="中标金额（万元）:" prop="ZBJE">-->
        <!--            <div style="margin-left: 10px">{{ getZbje(formData.XSBDList[0]?.JGMXList,formData.XSBDList[0]) }}</div>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
      </el-row>

<!--      <el-row :gutter="0" class="grid-row">-->
<!--        <el-col :span="24" class="grid-cell no-border-bottom">-->
<!--          <el-form-item label="备注：" prop="BZ">-->
<!--            <el-input v-model="formData.BZ" :rows="3"-->
<!--                      type="textarea" clearable-->
<!--                      :disabled="!editable"/>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="24" class="grid-cell">-->
<!--          <el-form-item label="附件资料:" prop="FJZL" >-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="24" class="grid-cell">-->
<!--          <vsFileUploadTable style="width: 100%;height: 150px" YWLX="DBSQ" :key="params.id"-->
<!--                             :busId="params.id" v-model:fileTableData="fileTableData"  :editable="editable"/>-->
<!--        </el-col>-->
<!--      </el-row>-->

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsFileUploadTable from "@views/components/vsFileUploadTable";


export default  defineComponent({
  name: '',
  components: {vsFileUploadTable},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      XSJGID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        XMXX: {},

        XSBDList: [],
      },
      rules: {
        SFZB: [{
          required: true,
          message: '请选择',
        }],
        TJZBSX: [{
          required: true,
          message: '请填写',
        }],
        BJ: [{
          required: true,
          message: '请填写',
        }],
        XMJL: [{
          required: true,
          message: '请填写',
        }],
        BJFS: [{
          required: true,
          message: '请填写',
        }],
        DWMC: [{
          required: true,
          message: '请填写',
        }],

      },
      ActiveTab: '0',
      SFZBOptions: [{DMXX: '1',DMMC: '是'},{DMXX: '0',DMMC: '否'}],

      BJFSOptions: [],

      fileTableData: []
    })

    const getFormData = () => {
      let params = {
        XSJGID: state.XSJGID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/zbjgqr/selectZbjgById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      })
    }


    const saveData = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params = {
        ...state.formData,
        XSJGID: state.XSJGID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        SHZT: '0'
      }
      if (type === 'submit') {
        params.SHZT = '2'
      }
      state.loading = true
      axiosUtil.post('/backend/xsgl/zbjgqr/saveZbjgForm', params).then(res => {
        ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading = false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getXmxx = () => {
      let params = {
        FAID: state.formData.FAID,
        GGID: state.formData.GGID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/dbsqgl/selectXmxxByFaid', params).then(res => {
        state.formData.XMXX = res.data
        state.formData.XSBDList = res.data.XSBDList
        state.formData.XSBDList.forEach(item=>{
          item.JGMXList.forEach(ii=>{
            Object.assign(ii,{
              XSJGMXID: comFun.newId(),
              XSJGID: state.XSJGID,
              CJRZH: state.userInfo.userLoginName,
              CJRXM: state.userInfo.userName,
              CJDWID: state.userInfo.orgnaId,
              CJSJ: comFun.getNowTime(),
              SHZT: '0',
              YWZT: '1',
              TJZB: '1'
            })
          })
        })
        state.formData.PBRQ = res.data.PBRQ
        state.formData.PBDD = res.data.PBDD
        state.formData.PBWYHCYMD = res.data.PBWYHCYMD
        state.loading=false
      })
    }

    const getZbdw = (JGList) => {
      let res=[]
      if(JGList){
        JGList.forEach(item=>{
          if(item.SFZB==='1'){
            res.push(item.DWMC)
          }
        })
      }
      return res.join(',')
    }

    const getZbje = (JGList,row) => {
      let res=0
      if(JGList && row.BJFS){
        JGList.forEach(item=>{
          if(item.SFZB==='1'){
            let BJ=Number(item.BJ)
            if(!isNaN(BJ) && row.BJFS==='JG'){
              res+=BJ
            }else if(!isNaN(BJ) && row.BJFS==='ZHJD' && row.BD){
              res+= Number(row.BD) * (100-BJ) /100
            }
          }
        })
      }
      return res
    }

    const addPBQK = (row) => {
      if(!row.JGMXList){
        row.JGMXList=[]
      }
      row.JGMXList.push({
        XSJGMXID: comFun.newId(),
        XSJGID: state.XSJGID,
        CJRZH: state.userInfo.userLoginName,
        CJRXM: state.userInfo.userName,
        CJDWID: state.userInfo.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',
        FABDID: row.FABDID,
        TJZB: '1'
      })


    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation!=='add') {
        getFormData()
      } else {
        state.formData.FAID = props.params.FAID
        state.formData.GGID = props.params.GGID
        getXmxx()
      }
      getDMBData("BJFS", "BJFSOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      getZbdw,
      getZbje,
      addPBQK
    }
  }

})
</script>

<style scoped>
:deep(.lui-card-form .border-right .el-form-item__content){
  border-right: 1px solid rgba(227, 234, 248, 1) !important;
}
</style>
