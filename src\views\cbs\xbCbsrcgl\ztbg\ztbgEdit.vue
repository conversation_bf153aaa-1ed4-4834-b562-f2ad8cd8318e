<template>
  <el-form class="lui-card-form" :model="formData" ref="saveFormRef" :rules="rules" label-width="120px" status-icon
    size="default">
    <el-row>
      <div class="button-box">
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button type="info" @click="handleClose">关闭</el-button>
      </div>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="企业名称">
          <el-input v-model="formData.cbsmc" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="队伍名称">
          <el-input v-if="formData.dwlx == 'DW'" v-model="formData.dwmc" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="准入证号">
          <el-input v-model="formData.zrzh" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="有效期">
          <span style="padding-left: 11px;">{{ formData.yxqks.split(' ')[0] }} - {{ formData.yxqjs.split(' ')[0]
          }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="当前状态">
          <el-select v-model="formData.dwzt" disabled>
            <el-option v-for="item in dqztOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="变更状态" prop="bgzt" required>
          <el-select v-model="formData.bgzt" clearable>
            <el-option v-for="item in bgztOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="变更原因" prop="bgyy" class="custom-label" required>
          <el-input type="textarea" :rows="2" placeholder="请输入变更原因" v-model="formData.bgyy"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="相关附件">
          <vsfileupload class="custom-upload" v-if="formData.dwywid" :key="formData.dwywid" :busId="formData.dwywid"
            :busType="'xbcbsrcgl'" :ywlb="'xbcbsrcgl'"></vsfileupload>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import { computed, onMounted, ref, reactive, toRefs } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { queryDwxx, createRgczjl, queryDwxxByJl, updateRgczjl } from "@/api/xbcbsrcgl.js";
import VSAuth from "@src/lib/vsAuth";
import vsfileupload from "@views/components/vsfileupload";
import vsflow from "@views/vsflow/index.js";
import comFun from "@src/lib/comFun.js";

const state = reactive({
  loginName: "",
  formData: {
    dwywid: "",
    dwwybs: "", // 队伍唯一标识
    cbsmc: "", // 承包商名称 
    dwmc: "", // 队伍名称
    dwlx: "", // 队伍类型
    zrzh: "", // 准入证号
    yxqks: "", // 有效期开始
    yxqjs: "", // 有效期结束
    dwzt: "", // 队伍状态
    bgzt: "", // 变更状态
    xgrzh: "", // 修改人账号
    xgsj: "", // 修改时间
    bgyy: "", // 变更原因
  },
  dqztOptions: [
    { value: "1", label: "正常" },
    { value: "2", label: "暂停投标" },
    { value: "3", label: "停工整顿" },
    { value: "4", label: "取消投标资格" },
  ],
  bgztOptions: [
    { value: "DW_JY_HF", label: "正常" },
    { value: "DW_JY_ZT", label: "暂停投标" },
    { value: "DW_JY_TG", label: "停工整顿" },
    { value: "DW_JY_QX", label: "取消投标资格" },
  ],
  rules: {
    bgzt: [
      { required: true, message: '请选择变更状态', trigger: 'change' }
    ],
    bgyy: [
      { required: true, message: '请输入变更原因', trigger: 'blur' }
    ]
  },
  fromFlow: false,
});

const { loginName, formData, dqztOptions, bgztOptions, rules } = toRefs(state);
const saveFormRef = ref(null);

const lxMap = new Map([
  ["DW_JY_HF", "队伍恢复"],
  ["DW_JY_ZT", "队伍暂停"],
  ["DW_JY_TG", "队伍停工"],
  ["DW_JY_QX", "队伍取消"]
]);

// 定义 props
const props = defineProps({
  params: {
    type: Object,
    default: () => ({
      processId: null,
      dwywid: null
    }),
  },
});
const emit = defineEmits(['handleclose']); // 定义关闭事件

// 查询队伍信息
const queryDwInfo = () => {
  const params = {
    jlid: props.params.processInstanceId,
    dwywid: props.params.dwywid
  }
  // 如果传递的参数中存在 processId 不为空，则是通过流程进的
  if (props.params.processId) {
    state.fromFlow = true;
    queryDwxxByJl(params).then((res) => {
      if (res.data) {
        formData.value = res.data;
        formData.value.bgyy = res.data.yy;
        formData.value.bgzt = res.data.lx;
      }
    })
  }
  else {
    queryDwxx(props.params).then((res) => {
      if (res.data) {
        formData.value = res.data;
      }
    })
  }

};

// 保存
const handleSave = async () => {

};

// 创建记录
const createJl = async () => {
  const params = {
    jlid: comFun.newId(), // 记录 id
    wybsid: formData.value.dwwybs, // 唯一标识
    lx: formData.value.bgzt, // 变更状态
    lxmc: lxMap.get(formData.value.bgzt) || "",
    yy: formData.value.bgyy, // 变更原因
    czrid: VSAuth.getAuthInfo().permission.userId,
    czrmc: VSAuth.getAuthInfo().permission.userName,
    zt: "1"
  };
  try {
    const res = await createRgczjl(params);
    return res.data;
  } catch (error) {
    throw new Error(`保存失败: ${error.message}`);
  }
}

// 更新记录
const updateJl = async (jlid) => {
  const params = {
    jlid: jlid,
    wybsid: formData.value.dwwybs,
    lx: formData.value.bgzt,
    lxmc: lxMap.get(formData.value.bgzt) || "",
    yy: formData.value.bgyy, // 变更原因
  }
  try {
    const res = await updateRgczjl(params);
    return res.data;
  } catch (error) {
    throw new Error(`更新失败: ${error.message}`);
  }
}

const createTask = async (jlid) => {
  return vsflow.createTask(
    vsflow.processData.xbcbsztbg.processId,
    formData.value.dwmc + "队伍状态变更流程",
    loginName.value,
    jlid,
    null,
    null,
    1,
    null
  );
}

const finishTask = async () => {
  return vsflow.finishTask(
    props.params.taskId,
    loginName.value,
    null,
    null,
    null,
    null,
    1,
    null,
  );
}

const handleSubmit = async () => {
  saveFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        let jlid = null;
        let updateRows = 0;
        let taskResult = {};
        if (state.fromFlow) {
          jlid = props.params.processInstanceId;
          updateRows = await updateJl(jlid);
          if (updateRows > 0) {
            taskResult = await finishTask();
          }
        } else {
          jlid = await createJl();
          if (jlid) {
            // 创建任务
            taskResult = await createTask(jlid);
          }
        }
        if (taskResult.success) {
          ElMessage.success("提交成功");
          handleClose();
        } else {
          ElMessage.error("流程发起失败");
        }
      } catch (error) {
        ElMessage.error("提交过程中发生错误");
      }
    } else {
      console.log('表单校验失败！', fields);
    }
  });
};

// 关闭表单
const handleClose = () => {
  emit('handleClose');
};

onMounted(() => {
  queryDwInfo();
  loginName.value = VSAuth.getAuthInfo().permission.userLoginName;
});

</script>
<style scoped>
.button-box {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0.5rem;
}

.el-form-item>>>.el-input__validateIcon {
  display: none;
}

.custom-label>>>.el-form-item__label {
  border-bottom: 0;
}

.custom-upload {
  margin-left: 0.5rem;
}

.custom-upload>>>div:first-child {
  display: flex;
  gap: 0.5rem;
}

.custom-upload>>>.el-upload-list {
  display: flex;
  align-items: center;
  margin: 0;
}

.custom-upload>>>.el-upload-list__item {
  margin-bottom: 0;
  transition: opacity 0.5s cubic-bezier(.55, 0, .1, 1);
}

.custom-upload>>>.el-upload-list__item-file-name {
  max-width: 10rem;
}

.custom-upload>>>.el-progress__text {
  color: gray;
  position: absolute;
  right: -15px;
  top: -18px;
}

.lui-card-form>>>.el-form-item__label {
  border-bottom: 0px;
}

.lui-card-form>>>.el-form-item__content {
  border-bottom: 0px;
}
</style>