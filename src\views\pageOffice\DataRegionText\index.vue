<script setup>
import request from '@/utils/request'
import { ref, onMounted } from 'vue'
import { POBrowser } from "js-pageoffice"

const titleText = ref('');

onMounted(async () => {
    try {
        const response = await request({
            url: '/index',
            method: 'get',
        });
        titleText.value = response;
    } catch (error) {
        console.error('Failed to fetch title:', error);
    }
});

function open_pageoffice(vue_page_url) {
    POBrowser.openWindow(vue_page_url, 'width=1200px;height=800px;');
}
</script>

<template>
    <div>
        <input type="button" value="查看使用程序控制数据区域文本样式的效果" @click="open_pageoffice('/DataRegionText/Word')" />
        <br /><br />
        <input type="button" value="查看原文件内容样式" @click="open_pageoffice('/DataRegionText/Word2')" /> <br /><br />
    </div>
</template>
