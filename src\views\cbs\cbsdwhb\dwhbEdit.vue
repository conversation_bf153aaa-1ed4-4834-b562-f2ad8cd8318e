<template>
    <el-form
        :model="form"
        ref="vForm"
        :rules="vFormRules"
        label-position="left"
        label-width="160px"
        size="default"
        class="lui-card-form"
        :disabled="!editable"
    >
        <el-row :gutter="0" class="grid-row">
            <el-col :span="24" class="grid-cell">
                <el-form-item label="队伍合并情况及描述" prop="DWHBQKMS">
                <el-input
                    v-model="form.DWHBQKMS"
                    type="textarea"
                    :rows="3"
                    clearable
                    placeholder="请输入"
                ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="grid-cell">
                <el-form-item label="相关附件">
                <vsfileupload
                    :maxSize="10"
                    :editable="editable"
                    :busId="dwhbid"
                    :key="dwhbid"
                    ywlb="dwhbfj"
                    busType="dwxx"
                    :limit="100"
                ></vsfileupload>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="0" style="margin: 10px 0px">
            <el-col :span="24">
                <el-button type="success" @click="addRow" style="float: right">新增</el-button>
            </el-col>
        </el-row>
        <el-row :gutter="0" class="grid-row">
            <el-table
                class="lui-table"
                highlight-current-row
                ref="table"
                size="default"
                height="300px"
                border
                :data="tableData"
                v-loading="listLoading"
            >
                <el-table-column type="index" width="60" label="序号" align="center"/>
                <el-table-column label="被合并队伍" min-width="200" header-align="center" align="center">
                    <template #default="{ row,$index }">
                        <el-button v-if="row.DWYWID_OLD" type="text" @click="selectDwxx(row,$index)">{{row.DWMC_OLD}}</el-button>
                        <el-button v-else class="lui-table-button" @click="selectDwxx(row,$index)">选择</el-button>
                    </template>
                </el-table-column>
                <el-table-column label="新归属承包商单位" min-width="200" header-align="center" align="center">
                    <template #default="{ row,$index }">
                        <el-button v-if="row.CBSYWID_NEW" type="text" @click="selectCbsxx(row,$index)">{{row.CBSMC_NEW}}</el-button>
                        <el-button v-else class="lui-table-button" @click="selectCbsxx(row,$index)">选择</el-button>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="60" header-align="center" align="center">
                    <template #default="{ row,$index }">
                        <div class="table-col-btn">
                            <el-button class="lui-table-button" @click="deleteRow(row,$index)">
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-row>
        <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
            <el-button type="primary" @click="saveData('save')" v-if="editable">保存</el-button>
            <el-button type="success" @click="saveData('submit')" v-if="editable">提交</el-button>
            <el-button @click="closeForm">返回</el-button>
        </div>
    </el-form>
    <el-dialog
        custom-class="lui-dialog"
        title="选择被合并队伍"
        v-model="showDwxxDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        top="100px"
        width="50%"
    >
        <selectDwxx v-if="showDwxxDialog" @callBackDwxx="callBackDwxx"></selectDwxx>
    </el-dialog>
    <el-dialog
        custom-class="lui-dialog"
        title="选择新归属承包商单位"
        v-model="showCbsxxDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        top="100px"
        width="50%"
    >
        <selectCbsxx v-if="showCbsxxDialog" @callBackCbsxx="callBackCbsxx"></selectCbsxx>
    </el-dialog>
</template>
<script>
import {defineComponent, onMounted, reactive, toRefs} from "vue";
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import vsfileupload from "../../components/vsfileupload";
import { v4 as uuidv4 } from "uuid";
import selectDwxx from "./selectDwxx.vue";
import selectCbsxx from "./selectCbsxx.vue";
import {ElMessage, ElMessageBox} from "element-plus";
export default defineComponent({
components: {
    vsfileupload,selectDwxx,selectCbsxx
},
props: {
    editable:{
        type: Boolean,
        default: true
    },
    dwhbid:{
        type: String,
        default: ''
    }
},
setup(props, {emit}) {
    const state = reactive({
        listLoading: false,
        form:{},
        tableData:[],
        currentIndex: 0,
        showDwxxDialog: false,
        showCbsxxDialog: false,
    })

    const addRow = ()=>{
        state.tableData.push({
            DWHBID: props.dwhbid,
            DWHBMXID: uuidv4().replace(/-/g,''),
        });
    }

    const deleteRow = (row,index)=>{
        state.tableData.splice(index, 1);
    }

    const closeForm = () => {
        emit('closeForm')
    }

    const selectDwxx = (row,index) => {
        state.currentIndex = index;
        state.showDwxxDialog = true;
    }

    const selectCbsxx = (row,index) => {
        state.currentIndex = index;
        state.showCbsxxDialog = true;
    }

    const callBackDwxx = (row) => {
        state.tableData[state.currentIndex].DWYWID_OLD = row.DWYWID;
        state.tableData[state.currentIndex].DWMC_OLD = row.DWMC;
        state.showDwxxDialog = false;
    }

    const callBackCbsxx = (row) => {
        state.tableData[state.currentIndex].CBSYWID_NEW = row.CBSYWID;
        state.tableData[state.currentIndex].CBSMC_NEW = row.CBSDWQC;
        state.showCbsxxDialog = false;
    }

    const saveData = (val)=>{
        if(!state.form.DWHBQKMS){
            ElMessage.warning("请填写队伍合并情况及描述！");
            return;
        }
        if(state.tableData.length == 0){
            ElMessage.warning("请先添加被合并的队伍！");
            return;
        }
        for(let i=0;i<state.tableData.length;i++){
            if(!state.tableData[i].DWYWID_OLD || !state.tableData[i].CBSYWID_NEW){
                ElMessage.warning("请先选择队伍或承包商！");
                return;
            }
        }
        state.form.DWHBID = props.dwhbid;
        state.form.CJRZH = VSAuth.getAuthInfo().permission.userLoginName;
        state.form.CJRXM = VSAuth.getAuthInfo().permission.userName;
        state.form.CJDWID = VSAuth.getAuthInfo().permission.orgnaId;

        axiosUtil.post('/backend/sccbsgl/dwrcgl/dwhb/saveDwhb', {
            form: state.form,
            rows: state.tableData,
            flag: val
        }).then((res) => {
            if(res.data.success){
                ElMessage.success("保存成功！");
                closeForm();
            }else{
                ElMessage.error("保存失败！");
            }
        });
    }

    const initData = () =>{
        axiosUtil.get('/backend/sccbsgl/dwrcgl/dwhb/queryDwhbForm', { dwhbid: props.dwhbid }).then((res) => {
            if(res.data && res.data.form){
                state.form = res.data.form;
                state.tableData = res.data.list;
            }
            state.listLoading = false;
        });
    }
    onMounted(() => {
        initData();
    })

    return {
        ...toRefs(state),
        initData,
        saveData,
        addRow,
        deleteRow,
        closeForm,
        selectDwxx,
        selectCbsxx,
        callBackDwxx,
        callBackCbsxx,
    }
}

})
</script>

<style scoped>

</style>
