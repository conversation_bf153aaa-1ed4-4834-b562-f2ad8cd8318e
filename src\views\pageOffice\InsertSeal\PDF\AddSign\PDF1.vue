<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Save() {
	pdfctrl.SaveFilePage = "/InsertSeal/PDF/AddSeal/save?savePath=/InsertSeal/PDF/AddSign1/"
	pdfctrl.WebSave();
}

function AddHandSign() {
	try {
		pdfctrl.zoomseal.AddHandSign();
	} catch (e) {
	}
}
//修改密码
function ChangePsw() {
	pdfctrl.zoomseal.ShowSettingsBox();
}
function AfterDocumentOpened() {
	//alert(document.getElementById("PDFCtrl1").Caption);
}
function SetBookmarks() {
	pdfctrl.BookmarksVisible = pdfctrl.BookmarksVisible;
}
function PrintFile() {
	pdfctrl.ShowDialog(4);
}
function SwitchFullScreen() {
	pdfctrl.FullScreen = !pdfctrl.FullScreen;
}
function SetPageReal() {
	pdfctrl.SetPageFit(1);
}
function SetPageFit() {
	pdfctrl.SetPageFit(2);
}
function SetPageWidth() {
	pdfctrl.SetPageFit(3);
}
function ZoomIn() {
	pdfctrl.ZoomIn();
}
function ZoomOut() {
	pdfctrl.ZoomOut();
}
function FirstPage() {
	pdfctrl.GoToFirstPage();
}
function PreviousPage() {
	pdfctrl.GoToPreviousPage();
}
function NextPage() {
	pdfctrl.GoToNextPage();
}
function LastPage() {
	pdfctrl.GoToLastPage();
}
function SetRotateRight() {
	pdfctrl.RotateRight();
}
function SetRotateLeft() {
	pdfctrl.RotateLeft();
}
function OnPDFCtrlInit() {
	pdfctrl.AddCustomToolButton("保存", "Save()", 6);
	pdfctrl.AddCustomToolButton("签字", "AddHandSign()", 0);
	//PDF的初始化事件回调函数，您可以在这里添加自定义按钮
	if (("linux") != (pdfctrl.ClientOS)) {
		pdfctrl.AddCustomToolButton("修改密码", "ChangePsw()", 0);
		pdfctrl.AddCustomToolButton("隐藏/显示书签", "SetBookmarks()", 0);
	}
	pdfctrl.AddCustomToolButton("打印", "PrintFile()", 6);
	pdfctrl.AddCustomToolButton("-", "", 0);
	pdfctrl.AddCustomToolButton("实际大小", "SetPageReal()", 16);
	pdfctrl.AddCustomToolButton("适合页面", "SetPageFit()", 17);
	pdfctrl.AddCustomToolButton("适合宽度", "SetPageWidth()", 18);
	pdfctrl.AddCustomToolButton("缩小", "ZoomOut()", 17);
	pdfctrl.AddCustomToolButton("放大", "ZoomIn()", 18);
	pdfctrl.AddCustomToolButton("-", "", 0);
	pdfctrl.AddCustomToolButton("首页", "FirstPage()", 8);
	pdfctrl.AddCustomToolButton("上一页", "PreviousPage()", 9);
	pdfctrl.AddCustomToolButton("下一页", "NextPage()", 10);
	pdfctrl.AddCustomToolButton("尾页", "LastPage()", 11);
	pdfctrl.AddCustomToolButton("-", "", 0);
	pdfctrl.AddCustomToolButton("向左旋转90度", "SetRotateLeft()", 12);
	pdfctrl.AddCustomToolButton("向右旋转90度", "SetRotateRight()", 13);
}

function openFile() {
	// 发起GET请求到后端Controller的路由
	return request({
		url: '/InsertSeal/PDF/AddSign/PDF1',
		method: 'get',
	});
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { 
		OnPDFCtrlInit, 
		Save,
		AddHandSign,
		ChangePsw,
		AfterDocumentOpened,
		SetBookmarks,
		PrintFile, 
		SetPageFit,
		SetPageReal,
		SetPageWidth,
		SetRotateLeft,
		SetRotateRight,
		SwitchFullScreen,
		ZoomIn,
		ZoomOut,
		FirstPage,
		PreviousPage,
		NextPage,
		LastPage,
	};//其中OnPDFCtrlInit必须

})
</script>

<template>
	<div class="PDF">
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
