<template>
  <div style="padding: 20px">
    <div style="color: #b20909;font-size: 30px;text-align: center">评委签到</div>

    <div style="text-align: center;font-weight: bold;margin-bottom: 20px">评标委员会声明书</div>
    <p style="font-size: 18px;text-indent: 2em">声明：本人严格遵守党和国家有关廉洁从业的纪律和法规，遵守中国石化、江苏油田廉洁相关规定，恪守职业道德；坚决维护国家利益和中国石化、江苏油田整体利益，认真、公正、诚实、廉洁履行专家职责。</p>

    {{formData}}
    {{signFile}}

    <div style="display: flex;align-items: center">
      <div style="font-weight: bolder">签字人：</div>
      <el-image v-if="formData.signImage" style="width: 150px;height: 60px"
                 :src="'/backend/minio/download?id='+formData.signImage"></el-image>
      <div v-if="signFile?.mongoDBId"
           style="color: red;cursor: pointer;margin-left: 10px"
      @click="delSignImage">删除</div>
    </div>


    <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;">
<!--      <el-button v-if="params.RYXX.CHSF==='PBZJ'" size="default" type="primary" @click="agreeSign">同意并签字确认</el-button>-->
      <signImageUpload
          ref="signImage"
          style="margin-right: 100px"
          :busId="formData.PBPWBS"
          :key="formData.PBPWBS"
          :editable="true"
          ywlb="dzqm"
          busType="dzqm"
          :showImage="false"
          v-model:signFile="signFile">
      </signImageUpload>
      <el-button size="default" type="primary" @click="saveData('submit')">提交</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import signImageUpload from "@views/components/signImageUpload";
import axios from "axios";


export default defineComponent({
  name: '',
  components: {signImageUpload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      formData:{
        SFQD: '',
        PBPWBS: props.params.RYXX.PBPWBS,
        signImage: '',
      },
      signFile: {}
    })

    watch(()=>state.signFile,()=>{
      if(state.signFile && state.signFile.mongoDBId){
        state.formData.signImage=state.signFile.mongoDBId
      }else {
        state.formData.signImage=''
      }
    },{deep: true})

    const agreeSign = () => {
      let params={
        PBPWBS: state.formData.PBPWBS
      }
      axiosUtil.get('/backend/xsgl/pbyx/pwqdqr/agreeSign', params).then((res) => {
        state.formData.signImage = res.data
      })
    }
    const instance = getCurrentInstance()
    const delSignImage = () => {
      const file = state.signFile
      axios.post('/backend/minio/del', {id: file.id, delFlag: "1"}).then((res) => {
        instance.proxy.$refs['signImage'].loadFileList()
      })
    }
    
    const saveData = () => {
      if(!state.formData.signImage){
        ElMessage.warning('请先签字确认')
        return
      }

      axiosUtil.post('/backend/kpbyx/pwqd/saveQdxx', state.formData).then((res) => {
        ElMessage.success('提交成功')
        emit('close')
      })
      
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      agreeSign,
      saveData,
      delSignImage

    }
  }

})
</script>

<style scoped>

</style>
