<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="width: 100%;text-align: center">
        <h2>{{ formData.XMXX.XMMC }}{{GJC[formData.XMXX.XSFS][0]}}结果公告</h2>
        <h4>发布日期：{{ formData.CJSJ }}</h4>
      </div>

      <el-row :gutter="0" class="grid-row">
        <el-col :span="14" class="grid-cell" :offset="2">
          <h3 style="margin-left: 10px">{{GJC[formData.XMXX.XSFS][1]}}项目名称：{{ formData.XMXX.XMMC }}</h3>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <h3 style="margin-left: 10px">{{GJC[formData.XMXX.XSFS][1]}}项目编号：{{ formData.XMXX.XMBH }}</h3>
        </el-col>

      </el-row>
      <div style="padding: 20px 100px;min-height: 300px;overflow: auto">
        <div class="ql-editor" v-html="formData.GGNR"></div>
      </div>

      <div style="width: 100%;margin-top: 10px;justify-content: center;display: flex;margin-bottom: 10px">
        <el-button @click="closeForm">返回</el-button>
      </div>


    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";



export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      JGGSID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '0',

        XMXX: {
          XSFS: 'GKZB'
        },
      },
      rules: {},

      GJC: {
        JB: ['成交','采购','响应'],
        JJ: ['成交','采购','响应'],
        GKJB: ['成交','采购','响应'],
        GKJJ: ['成交','采购','响应'],
        GKZB: ['中标','招标','投标'],
        YQZB: ['中标','招标','投标'],
      },
    })

    const getFormData = () => {
      let params = {
        JGGSID: state.JGGSID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/zbjggs/selectZbjgById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      closeForm,
    }
  }

})
</script>

<style scoped>
@import '@vueup/vue-quill/dist/vue-quill.snow.css';
/deep/ .el-collapse-item__header {
  background-color: #dcdfef !important;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
  font-size: 16px;
  color: black !important;
}
</style>
