<template>
  <div v-loading="loading">
    <el-tabs v-model="ActiveTab" type="border-card">
      <el-tab-pane :name="index+''" :label="item.DMMC+'设置'" v-for="(item,index) in CCFWOptions">
        <gqctszEdit :params="item" v-model="formData"/>
      </el-tab-pane>
    </el-tabs>

    <div class="bottom-button">
      <el-button type="primary" size="default" @click="saveData('save')">保存</el-button>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import gqctszEdit from "@views/gqct/gqctsz/gqctszEdit";
import comFun from "@lib/comFun";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {gqctszEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      ActiveTab: '0',
      CCFWOptions: [],
      loading: false,
      formData:{

      }
    })

    const getFormData = () => {
      state.loading=true
      axiosUtil.get('/backend/gqct/gqctsz/selectGqctszData', null).then((res) => {
        state.formData=res.data
        getDMBData('GQCTSZ', 'CCFWOptions')
      })
    }

    const saveData = (type) => {
      let CCCSList=[]

      // state.CCFWOptions.forEach(item=>{
        CCCSList.push(...state.formData['RZGXList'])
      // })

      let params={
        ...state.formData,
        CCCSList
      }
      state.loading=true
      axiosUtil.post('/backend/gqct/gqctsz/saveGqctsz',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        state.loading=false
      })
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
        state.loading=false
      })
    }
    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      saveData


    }
  }

})
</script>

<style scoped>
.bottom-button{
  height: 50px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #dfe1e3;
}
</style>
