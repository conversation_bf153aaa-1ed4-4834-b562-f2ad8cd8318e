<script setup>
import request from '@/utils/request';
import { ref, onMounted, reactive } from 'vue'

const poHtmlCode = ref('');
const paramValue = reactive({});


function OnPageOfficeCtrlInit() {
	//PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.Caption = "简单的给Excel赋值";//标题栏赋值
	pageofficectrl.CustomToolbar = false;//隐藏自定义工具栏
}

function openFile() {
	// 发起GET请求到后端Controller的路由
	return request({
		url: '/DefinedNameTable/Excel2',
		method: 'get',
		params: paramValue
	});
}

onMounted(() => {
	// 请求后端打开文件
	let open_params = pageofficectrl.WindowParams;
	paramValue.tempFileName = open_params;
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Excel">
		自定义Excel模板填充后的显示效果
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>