<template>
  <el-form :model="listQuery" ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="name">
          <el-input v-model="listQuery.XM" type="text" placeholder="请输入姓名" style="width:100%;" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="workAddress">
          <el-input v-model="listQuery.GZDW" type="text" placeholder="请输入工作单位" style="width:100%;" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="" prop="major">
          <el-cascader
              style="width:100%;"
              clearable
              placeholder="请选择申报专业"
              v-model="listQuery.SBZYList"
              :options="XCSZYOptions"
              :show-all-levels="false"
              collapse-tags
              :props="{expandTrigger: 'hover',label:'ZYMC',value:'ZYBM',emitPath:false,multiple:true}"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell" style="display: flex;gap: 10px;text-align: right;">
        <div class="ml10 static-content-item">
          <el-button type="primary" @click="queryData">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
        </div>
        <div class="ml10 static-content-item">
          <el-button type="primary" @click="queryDialogVisible=true">
            <el-icon>
              <Search/>
            </el-icon>
            高级查询
          </el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="primary" @click="exportExcel">
            <el-icon>
              <Upload/>
            </el-icon>
            导出
          </el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="primary" class="lui-button-add" @click="openDialog('add')">
            <el-icon>
              <Plus/>
            </el-icon>
            新增
          </el-button>
        </div>
      </el-col>

    </el-row>
    <div class="container-wrapper">
      <el-table ref="dataTable"
                class="lui-table"
                :data="tableData" height="calc(100vh - 250px)"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column type="index" width="50" fixed="left"></el-table-column>
        <el-table-column v-if="true" prop="XM" label="姓名" :fixed="false" align="center"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.XM }}</span></template>
        </el-table-column>
        <el-table-column v-if="true" prop="GZDWMC" label="工作单位" :fixed="false" align="center"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.GZDWMC }}</span></template>
        </el-table-column>
        <el-table-column v-if="true" prop="ZCMC" label="职称" :fixed="false" align="center"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.ZCMC }}</span></template>
        </el-table-column>
        <el-table-column v-if="true" prop="XCSZYMC" label="现从事专业" :fixed="false" align="center"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.XCSZYMC }}</span></template>
        </el-table-column>
        <el-table-column v-if="true" prop="SBZY" label="申报专业" :fixed="false" align="center"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.RKZJBXX.SBZY }}</span></template>
        </el-table-column>
        <el-table-column v-if="true" prop="SJH" label="手机号" :fixed="false" align="center"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.RKZJBXX.LXDH }}</span></template>
        </el-table-column>
        <el-table-column v-if="true" prop="BLZT" label="办理状态" :fixed="false" align="center"
                         :show-overflow-tooltip="true" min-width="60">
          <template
              #default="scope">
            <el-tag type="primary" v-if="scope.row.SHZT==='0'">未提交</el-tag>
            <el-tag type="warning" v-if="scope.row.SHZT==='1'">审批中</el-tag>
            <el-tag type="success" v-if="scope.row.SHZT==='2'">已入库</el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="true" prop="BLSJ" label="办理时间" :fixed="false" align="center"
                         :show-overflow-tooltip="true">
          <template
              #default="scope"><span>{{ scope.row.CJSJ.substring(0, 10) }}</span></template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="150">
          <template #default="scope">
            <div v-if="scope.row.SHZT==='0'">
              <el-button class="lui-table-button" size="small" type="primary" @click="openDialog('edit',scope.row)">编辑
              </el-button>
              <el-button class="lui-table-button" size="small" type="primary" @click="delRowData(scope.row)">删除
              </el-button>
            </div>
            <div v-else>
              <el-button size="small" class="lui-table-button" type="primary" @click="openDialog('view',scope.row)">查看
              </el-button>
              <!--<el-button size="small" class="lui-table-button" type="primary" @click="handleMonitor(scope.row)">
                跟踪
              </el-button>-->
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background
                     class="lui-pagination"
                     v-model:current-page="listQuery.page"
                     v-model:page-size="listQuery.size" :page-sizes="[10,20,50,100]"
                     layout="total, prev, pager, next, sizes"
                     @size-change="pageOrSizeChange"
                     @current-change="pageOrSizeChange"
                     :total="total">
      </el-pagination>
    </div>
    <!-- <el-dialog v-if="dialogVisible"
               custom-class="lui-dialog"
               append-to-body
               v-model="dialogVisible" :title="dialogParams.title" width="1200px" z-index="1000" @close="closeDialog">
      <zjrk-edit :params="dialogParams" @closeDialog="closeDialog"/>
    </el-dialog> -->
     <el-dialog
        custom-class="lui-dialog"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="专家入库申请"
        @closed="closeForm"
        top="1vh"
        z-index="1000"
        width="1200px">
      <div>
        <!--<AuditFrame v-if="dialogVisible && params.operation!=='view'" :business-params="params"
                    :process-params="processParams" @close="closeForm"/>
        <ZjrkEdit v-else-if="dialogVisible" :params="params" @closeDialog="closeForm"></ZjrkEdit>-->
        <ZjrkEdit :params="params" @closeDialog="closeForm"></ZjrkEdit>
      </div>
    </el-dialog>
    <el-dialog z-index="1000" title="业务流程跟踪" v-model="showDialog" width="80%" top="35px" :close-on-click-modal="false">
      <monitorForm2 v-if="showDialog" :queryParams="params"/>
    </el-dialog>

    <el-dialog z-index="1000"
               v-model="queryDialogVisible" title="高级查询条件设置" width="800px" class="dialogClass"
               custom-class="lui-dialog"
               append-to-body>
      <tcgjcx @executeQuery="executeQuery"/>
    </el-dialog>
    <el-dialog z-index="1000"
               custom-class="lui-dialog"
               v-model="ZYDialogVisible" title="申报专业选择" width="800px" class="dialogClass" top="1vh"
               append-to-body>
      <zyxz v-if="ZYDialogVisible" :show-z-z-y="false" @close="closeZYDialog" @parentMethod="getCheck"></zyxz>
    </el-dialog>
  </el-form>

</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
}
  from 'vue'
import ZjrkEdit from "./zjrkEdit";
import comFun from "../../../lib/comFun";
import axiosUtil from "../../../lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import Tcgjcx from "./tcgjcx";
import Zyxz from "./common/zyxz";
import {Search, Upload, Plus} from '@element-plus/icons-vue'
import monitorForm2 from "../../workflow/MonitorForm2";
import AuditFrame from "../../workflow/AuditFrame";

export default defineComponent({
  components: {Tcgjcx, ZjrkEdit, Zyxz, Search, Plus, Upload,monitorForm2,AuditFrame},
  props: {},
  setup() {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      rules: {},
      dialogVisible: false,
      ZYDialogVisible: false,
      dialogParams: {
        title: ''
      },
      showDialog:false,
      params: {},
      processParams: {
        activityId: "new",
        processId: "ZJGL_ZJRK",
        Processversion: "1",
        processVersion:"1"
      },
      queryDialogVisible: false,
      total: 0,
      seniorQuery: null,
      XCSZYOptions: [],
    })
    const getZYData = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy', {}).then((res) => {
        state.XCSZYOptions = comFun.treeData(res.data, 'ZYBM', 'FZYBM', 'children', '0')
      });
    }
    const getDataList = (value) => {
      let params = value ? value : state.listQuery
      axiosUtil.post(`/backend/zjgl/zjrkgl/selectRkzjPage?page=${state.listQuery.page ? state.listQuery.page : ''}&size=${state.listQuery.size ? state.listQuery.size : ''}`, params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }
    const handleMonitor = (row) => {
      state.params.id = row.GLZJMXID
      state.showDialog = true
    }
    const delRowData = (row) => {
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        axiosUtil.get('/backend/zjgl/zjrkgl/delRkzj', row).then((res) => {
          getDataList()
          ElMessage({
            message: '删除成功',
            customClass: "myMessageClass",
            type: 'success',
          })
        })
      }).catch(() => {
      })
    }
    const exportExcel = () => {
      let column = [[
        {field: 'XM', title: '姓名', width: 280, halign: 'center', align: 'left'},
        {field: 'GZDW', title: '工作单位', width: 280, halign: 'center', align: 'left'},
        {field: 'ZCMC', title: '职称', width: 280, halign: 'center', align: 'left'},
        {field: 'XCSZYMC', title: '现从事专业', width: 280, halign: 'center', align: 'left'},
        {field: 'SBZY', title: '申报专业', width: 400, halign: 'center', align: 'left'},
        {field: 'BGDH', title: '手机号', width: 280, halign: 'center', align: 'left'},
        {field: 'BLZT', title: '办理状态', width: 280, halign: 'center', align: 'left'},
        {field: 'CJSJ', title: '办理时间', width: 280, halign: 'center', align: 'left'},
      ]]
      let finparams = {
        title: '专家入库信息',
        name: '专家入库信息',
        params: state.listQuery,
        column: column
      }
      axiosUtil.exportExcel('/backend/commonExport/zjrkxx', finparams)
    }
    const openDialog = (type, row) => {
      if (type === 'add') {
        state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      } else if (type === 'edit') {
        state.params = {editable: true, id: row.GLZJMXID, operation: 'edit'}
      } else if (type === 'view') {
        state.params = {editable: false,id: row.GLZJMXID, operation: 'view'}
      }
      state.dialogVisible = true;
    }
    const executeQuery = (value) => {
      if (value) {
        state.seniorQuery = value
        getDataList(value)
      }
      state.queryDialogVisible = false
    }
    const pageOrSizeChange = () => {
      getDataList(state.seniorQuery)
    }
    const getCheck = (e, ZZY) => {
      let res = []
      let SBZY = []
      e.forEach(item => {
        res.push(item.ZYBM)
        SBZY.push(item.ZYMC)
      })
      state.listQuery.SBZY = SBZY.join(',')
      state.listQuery.SBZYList = res
      state.ZYDialogVisible = false

    }
    const closeDialog = () => {
      state.dialogVisible = false
      getDataList(state.seniorQuery)
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }
    const closeZYDialog = () => {
      state.ZYDialogVisible = false
    }
    const queryData = () => {
      state.seniorQuery = null
      getDataList()
    }
    onMounted(() => {
      getDataList()
      getZYData()
    })
    return {
      ...toRefs(state),
      openDialog,
      closeDialog,
      getDataList,
      delRowData,
      executeQuery,
      pageOrSizeChange,
      queryData,
      exportExcel,
      getCheck,
      closeZYDialog,
      closeForm,
      handleMonitor
    }
  }
})

</script>

<style scoped>


/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: white;
}

:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}
</style>
