<template>
  <div style="font-size: 14px;">
    <div style="margin-bottom: 10px;border: 1px solid #cbcbcb;padding: 5px"
         v-for="(item,index) in tableData" :key="index">
      <div style="font-weight: bold;color: #409EFF">{{item['ZYMC']}}</div>
      <div v-for="(iitem,iindex) in item.SJXXList" :key="iindex">
        <div style="font-weight: bold;color: #409EFF;cursor: pointer;width: 100%;text-align: right" @click="viewRow(iitem)">查看人员完整信息》</div>
        <appRow label="信息项：" label-width="90px">{{iitem['XXXMC']}}</appRow>
        <appRow label="姓名：" label-width="90px">{{iitem['RYXM']}}</appRow>
        <appRow label="岗位：" label-width="90px">{{iitem['GWJZW']}}</appRow>
        <appRow label="工作年限：" label-width="90px">{{iitem['BGGZSC']}}</appRow>
        <appRow label="证书数量：" label-width="90px">{{iitem['ZSXX']?.length}}</appRow>
        <div style="width: 100%;border-bottom: 1px dashed #c4c4c4;margin-top: 10px;margin-bottom: 10px"
             v-if="iindex+1!==item.SJXXList.length"></div>
      </div>
    </div>

    <div class="info-view" v-if="showView">
      <div class="banner">
        <el-icon :size="25" @click="showView=false"><ArrowLeftBold /></el-icon>
        <div style="font-weight: bold;font-size: 16px;color: #409EFF">人员信息查看</div>
        <div></div>
      </div>

      <div style="padding: 10px;display: flex;align-items: center;gap: 10px;color: #409EFF;font-weight: bold;font-size: 16px">
        <el-icon :size="25"><User /></el-icon>
        人员基本信息
      </div>

      <div style="padding: 10px;margin: 10px;border: 1px solid #8c939d;background-color: white">
        <appRow label="姓名：" label-width="110px">{{checkRow['RYXM']}}</appRow>
        <appRow label="身份证号：" label-width="110px">{{checkRow['SFZH']}}</appRow>
        <appRow label="岗位：" label-width="110px">{{checkRow['GWJZW']}}</appRow>
        <appRow label="工作年限：" label-width="110px">{{checkRow['BGGZSC']}}</appRow>
        <appRow label="社保缴纳日期：" label-width="110px">{{checkRow['EXTENSION']?.SBJNRQ}}</appRow>
        <appRow label="学历：" label-width="110px">{{checkRow['DYXL']}}</appRow>
        <appRow label="毕业院校：" label-width="110px">{{checkRow['BYYX']}}</appRow>
        <appRow label="所学专业：" label-width="110px">{{checkRow['SXZY']}}</appRow>
        <appRow label="毕业时间：" label-width="110px">{{checkRow['BYSJ']}}</appRow>
        <appRow label="外语水平：" label-width="110px">{{checkRow['EXTENSION']?.WYSP}}</appRow>
        <appRow label="职称：" label-width="110px">{{checkRow['ZC']}}</appRow>
        <appRow label="职称专业：" label-width="110px">{{checkRow['EXTENSION']?.ZCZY}}</appRow>
        <appRow label="职称等级：" label-width="110px">{{checkRow['EXTENSION']?.ZCDJ}}</appRow>
      </div>

      <div style="padding: 10px;display: flex;align-items: center;gap: 10px;color: #409EFF;font-weight: bold;font-size: 16px">
        <el-icon :size="25"><Postcard /></el-icon>
        人员证书信息
      </div>

      <div style="padding: 10px;margin: 10px;border: 1px solid #8c939d;background-color: white">
       <div v-for="(item,index) in checkRow.ZSXX" :key="index">
         <appRow label="证书名称：" label-width="120px">{{item['ZSMC']}}</appRow>
         <appRow label="证书编号：" label-width="120px">{{item['ZSBH']}}</appRow>
         <appRow label="有效开始日期：" label-width="120px">{{item['YXQKS']}}</appRow>
         <appRow label="有效结束日期：" label-width="120px">{{item['YXQJS']}}</appRow>
         <appRow label="证书附件：" label-width="120px">
           <vsfileupload
               :busId="item.ZSYWID"
               :key="item.ZSYWID"
               :editable="false"
               ywlb="DWZTBGFJ"
               busType="dwxx"
               :limit="100"
           ></vsfileupload>
         </appRow>
         <div style="width: 100%;border-bottom: 1px dashed #c4c4c4;margin-top: 10px;margin-bottom: 10px;margin-bottom: 10px"
              v-if="index+1!==checkRow.ZSXX.length"></div>
       </div>
      </div>

    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import appRow from "@views/app/common/appRow";
import vsfileupload from "@views/components/vsfileupload";
import {ArrowLeftBold,User,Postcard} from "@element-plus/icons-vue";

export default defineComponent({
  name: '',
  components: {appRow,vsfileupload,ArrowLeftBold,User,Postcard},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
  },



  setup(props, {emit}) {
    const state = reactive({
      tableData:[],
      checkRow: {},
      showView: false
    })
    watch(()=>props.defaultData,()=>{
      if(props.defaultData){
        let res=[]
        props.defaultData.forEach(item=>{
          let ZYXX=res.find(ii=>item.ZYBM===ii.ZYBM)
          if(ZYXX){
            ZYXX.SJXXList.push(item)
          }else {
            res.push({
              ZYBM: item.ZYBM,
              ZYMC: item.ZYMC,
              SJXXList: [item]
            })
          }
        })
        console.log(res)
        state.tableData=res
      }
    },{immediate: true,deep: true})

    const viewRow = (row) => {
      state.checkRow=row
      state.showView=true
    }


    onMounted(() => {

    })

    return {
      ...toRefs(state),
      viewRow

    }
  }

})
</script>

<style scoped>
.info-view{
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 666;
  background-color: #e1e1e1;
  font-size: 16px;
}
.banner{
  height: 40px;
  background-color: white;
  align-items: center;
  justify-content: space-between;
  display: flex;
}
</style>
