<template>
  <el-row :gutter="0" class="grid-row">
    <el-col v-if="editable" :span="1" class="grid-cell">
      <el-button :disabled="!editable" type="primary" :icon="Plus" size="small" @click="addRow"></el-button>
    </el-col>
    <el-col :span="24" class="grid-cell">
      <div class="container-wrapper">
        <el-table ref="dataTable" :data="tableData" height="200px" class="lui-table"
                  :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                  :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
          <el-table-column type="index" width="50" fixed="left" align="center"></el-table-column>
          <el-table-column v-if="true" prop="QZRQ" label="起止日期" :fixed="false" header-align="center" align="left"
                           :show-overflow-tooltip="true" min-width="160">
            <template #default="scope">
              <el-input v-if="editable" v-model="scope.row.QZRQ" type="text" :disabled="!editable" clearable></el-input>
              <span v-else> {{scope.row.GZDW}} </span>
            </template>
          </el-table-column>
          <el-table-column v-if="true" prop="GZDW" label="工作单位" :fixed="false" header-align="center" align="left"
                           :show-overflow-tooltip="true" min-width="160">
            <template #default="scope">
              <el-input v-if="editable" v-model="scope.row.GZDW" type="text" :disabled="!editable" clearable></el-input>
              <span v-else> {{scope.row.GZDW}} </span>
            </template>
          </el-table-column>
          <el-table-column v-if="true" prop="ZW" label="职务" :fixed="false" header-align="center" align="left"
                           :show-overflow-tooltip="true" min-width="160">
            <template #default="scope">
              <el-input v-if="editable"  v-model="scope.row.ZW" type="text" :disabled="!editable" clearable></el-input>
              <span v-else> {{scope.row.ZW}} </span>
            </template>
          </el-table-column>
          <el-table-column v-if="true" prop="GZJJ" label="工作简介" :fixed="false" header-align="center" align="left"
                           :show-overflow-tooltip="true" min-width="150">
            <template #default="scope">
              <el-input v-if="editable"  v-model="scope.row.GZJJ" type="text" :disabled="!editable" clearable></el-input>
              <span v-else> {{scope.row.GZJJ}} </span>
            </template>
          </el-table-column>
          <el-table-column v-if="editable" fixed="right" class-name="data-table-buttons-column" header-align="center"
                           align="center" label="操作"
                           :width="90">
            <template #default="scope">
              <el-button class="lui-table-button" size="small" :disabled="!editable"
                         @click.stop="deleteRow(scope.$index, tableData)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import {defineComponent, onMounted, reactive, toRefs} from "vue";
import {Plus} from '@element-plus/icons-vue'
import comFun from "../../../../lib/comFun";

export default defineComponent({
  components: {},
  props: {
    tableData: {
      required: true,
      type: Object
    },
    params: {
      type: Object
    }
    // formData：{
    //   require： true

  },
  setup(props, {emit}) {
    const state = reactive({
      editable: false,
      Plus: Plus,
    })
    const addRow = () => {
      props.tableData.push({
        ZJLLID: comFun.newId(),
        WJID: props.params.id,
        QZRQ: null,
        GZDW: null,
        ZW: null,
        GZJJ: null
      })
    }
    const deleteRow = (index, data) => {
      data.splice(index, 1);
      // RealDelete(data)//对tableData中的数据删除一行
    }
    onMounted(() => {
      state.editable = props.params.editable
    })
    return {
      ...toRefs(state),
      addRow,
      deleteRow
    }
  },

})
</script>

<style scoped>
/*:deep(.required .el-form-item__label):before {*/
/*  content: "*";*/
/*  color: #F56C6C;*/
/*  margin-right: 4px;*/
/*}*/

/*:deep(.is-required .el-form-item__label):before {*/
/*  content: "*";*/
/*  color: #F56C6C;*/
/*  margin-right: 4px;*/
/*}*/
</style>
