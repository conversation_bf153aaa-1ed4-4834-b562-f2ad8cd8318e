<template>
  <div>

    <el-input style="margin-top: 20px" v-model="shyj" type="textarea" :rows="5" placeholder="请输入审核意见"></el-input>
    <div style="display: flex;margin-top: 20px;align-items: center" v-if="apprValue !== '0'">
      <div style="width: 160px">专业部门审核人：</div>
      <el-input size="default" v-model="SHR" type="text" placeholder="请选择专业部门审核人" readonly>
        <template #append>
          <el-button @click="dialogVisible=true">选择</el-button>
        </template>
      </el-input>
    </div>

    <div style="text-align: center; height: 40px; padding: 10px">
      <el-button type="primary" @click="onConfirm">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="办理人选择"
        z-index="1000"
        top="5vh"
        width="1400px">
      <div>
        <ryChoose v-if="dialogVisible" :queryParams="params" @getData="getData"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import vsfileupload from "@views/components/vsfileupload";
import {ElMessage} from "element-plus";
import ryChoose from "@views/commons/ryxz/ryChoose";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {vsfileupload,ryChoose},
  props: {
    apprValue: String,
    Activityid: String,
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      shyj: "",
      sffsdx: '1',
      blrArray: [],
      showBlrXz: false,
      nextPerformer: [],
      blfs:'BR',
      dialogVisible: false,
      params: {
        userToTwoOrg: vsAuth.getAuthInfo().permission.userLoginName,
      },
      SHR: '',
    })

    const onConfirm = () => {
      if(state.nextPerformer.length===0){
        ElMessage.warning('请选择专业部门审核人')
        return
      }
      saveBlrInfo();//保存办理人
      emit("confirm", state.shyj, state.sffsdx, state.nextPerformer)
    }

    const onCancel = () => {
      emit("close")
    }

    const getData = (value) => {
      if(!value || value.length===0){
        ElMessage.warning('请选择专业部门审核人')
        return
      }

      state.SHR=value.map(item=>item.USER_NAME).join(',')
      state.nextPerformer=value.map(item=>item.USER_LOGINNAME)
      state.dialogVisible=false

    }

    const saveBlrInfo = () => {
      let params={
        BLRList: [{
            TYPE: 'GR', 
            ACTIVITYID: props.Activityid,
            LABEL: '专业部门审核人',
            BUSINESSID: props.params.businessId,
            PROCESSID: props.params.processId,
            TASKID: props.params.taskId,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
            XGRZH: state.userInfo.userLoginName,
            XGSJ: comFun.getNowTime(),
            NAME: state.SHR,
            VALUE: state.nextPerformer.join(',')
        }]
      }
      return axiosUtil.post('/backend/common/saveLcblr',params)
    }

    onMounted(() => {
      console.error(props)
      if (props.apprValue !== '0') {
        state.shyj = '同意'
      }
      // emit('changeTitle','这是一个测试审核页面')
      // emit('changeWidth',1000)

    })

    return {
      ...toRefs(state),
      onCancel,
      onConfirm,
      getData

    }
  }

})
</script>

<style scoped>

</style>
