<template>
  <div class="ollama-container">
    <!-- 交互面板 -->
    <div class="chat-panel">
      <!-- 消息展示区 -->
      <div class="message-flow" ref="messageContainer">
        <div v-for="(msg, index) in messages" :key="index" 
             :class="['message-bubble', msg.role]">
          <div class="message-header">
            <span class="role-tag">{{ msg.role.toUpperCase() }}</span>
            <span class="timestamp">{{ msg.timestamp }}</span>
          </div>
          <pre class="model-output" v-html="msg.content"></pre>
        </div>
      </div>

      <!-- 输入控制区 -->
      <div class="input-control">
        <textarea v-model="inputPrompt" 
                  @keydown.enter.exact.prevent="submitPrompt"
                  placeholder="输入你的提示词..."></textarea>
        <button @click="submitPrompt" 
                :disabled="isGenerating">
          {{ isGenerating ? '生成中...' : '提交' }}
        </button>
      </div>

      <!-- 状态指示器 -->
      <div v-if="isGenerating" class="neuro-loader">
        <div class="neuro-dots"></div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data() {
    return {
      inputPrompt: '',
      messages: [],
      isGenerating: false,
      abortController: null
    }
  },
  methods: {
    async submitPrompt() {
      if (!this.inputPrompt.trim()) return
      
      try {
        this.isGenerating = true
        this.abortController = new AbortController()
        
        // 添加用户消息
        this.messages.push({
          role: 'user',
          content: this.inputPrompt,
          timestamp: new Date().toLocaleTimeString()
        })

        // 调用Ollama API
        // const response = await fetch("/deepseekApi/v1/chat/completions", {
        //   method: "POST",
        //   headers: {
        //     "Content-Type": "application/json",
        //   },
        //   body: JSON.stringify({
        //     model: "ds-r1-32b",
        //     stream: true,
        //     "messages": [
        //       {
        //         "role": "user",
        //         "content": this.inputPrompt
        //       }],
        //     "stop": []
        //   }),
        //   //signal: this.abortController.signal
        // });

        var data = JSON.stringify({"model":"ds-r1-32b","messages":[{"role":"user","content":this.inputPrompt}],"stream":false,"stop":[]});
        // var config = {
        //   method: 'post',
        //   url: '/deepseekApi/v1/chat/completions',
        //   headers: { 
        //     'Content-Type': 'application/json'
        //   },
        //   data : data
        // };

        const response = await axios({
      method: 'post',
      url: '/deepseekApi/v1/chat/completions',
      headers: { 'Content-Type': 'application/json' },
      data: data
    })

        // 添加AI消息容器
        const aiMessage = {
          role: 'assistant',
          content: '',
          tempcontent:'',
          timestamp: new Date().toLocaleTimeString()
        }
        this.messages.push(aiMessage);
        aiMessage.content=this.processSpecialTags(response.data.choices[0].message.content);
        
        // 流式处理响应
        console.log('response',response)
        const reader = response.data.choices[0].message.content;

        console.log('reader',reader)
        const decoder = new TextDecoder();
        
        let buffer = '';

        //while (true) {
          // const { done, value } = await reader.read();
          // if (done) break;
          
          // buffer += decoder.decode(value);
          
          // const chunks = buffer.split('\n');
          // buffer = chunks.pop() || '';

          // for (const chunk of chunks) {
          //   try {
          //     const parsed = JSON.parse(chunk);
          //     aiMessage.tempcontent += parsed.response;
          //     aiMessage.content = this.processSpecialTags(aiMessage.tempcontent);
          //     // 处理数据
          //   } catch (e) {
          //     console.warn('解析失败，已丢弃无效数据:', chunk);
          //   }
          // }

          
          // 自动滚动到底部
          this.$nextTick(() => {
            this.$refs.messageContainer.scrollTop = 
              this.$refs.messageContainer.scrollHeight;
          })
        //}

      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error('API错误:', error)
        }
      } finally {
        this.inputPrompt = ''
        this.isGenerating = false
      }
    },

  //处理deepseek返回特殊标签
  processSpecialTags(content) {
      // 处理 <think> 标签
      let processed = content
        .replace(/<think>/g, '<div class="think-box">')
        .replace(/<\/think>/g, '</div>');

      // 处理代码块 ```
      processed = processed
        .replace(/```([\s\S]*?)```/g, (_, code) => {
          const langMatch = code.match(/^\s*(\w+)\n/);
          const lang = langMatch ? ` language-${langMatch[1]}` : '';
          const codeContent = langMatch ? code.replace(langMatch[0], '') : code;
          return `<pre class="code-block"><code${lang}>${this.escapeHtml(codeContent.trim())}</code></pre>`;
        });

      // 处理行内代码 `
      processed = processed
        .replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

      return processed;
    },
    escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      };
      return text.replace(/[&<>"']/g, m => map[m]);
    }


  }
}
</script>

<style scoped>
/* 容器布局 */
.ollama-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 20px;
  background: #1a1a1a;
  border-radius: 16px;
  box-shadow: 0 12px 24px rgba(0,0,0,0.3);
}

/* 消息流样式 */
.message-flow {
  height: 70vh;
  overflow-y: auto;
  padding: 20px;
  background: #252525;
  border-radius: 12px;
  margin-bottom: 20px;
}

/* 消息气泡 */
.message-bubble {
  margin: 15px 0;
  padding: 18px;
  border-radius: 8px;
  background: #2d2d2d;
  border: 1px solid #3a3a3a;
}

.message-bubble.assistant {
  background: rgba(59,130,246,0.15);
  border-color: #3b82f6;
}

/* 消息头部 */
.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 0.9em;
}

.role-tag {
  padding: 4px 8px;
  border-radius: 4px;
  background: #3b82f6;
  color: white;
}

.timestamp {
  color: #888;
}

/* 模型输出样式 */
.model-output {
  color: #e0e0e0;
  font-family: 'Fira Code', monospace;
  font-size: 14px;
  white-space: pre-wrap;
  line-height: 1.8;
}

/* 输入控制区 */
.input-control {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 15px;
  align-items: center;
}

textarea {
  background: #2d2d2d;
  border: 1px solid #3a3a3a;
  color: white;
  padding: 12px;
  border-radius: 8px;
  resize: none;
  height: 80px;
}

button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

button:disabled {
  background: #1e3a8a;
  cursor: not-allowed;
}

/* 神经风格加载动画 */
.neuro-loader {
  margin-top: 20px;
  text-align: center;
}

.neuro-dots {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: #3b82f6;
  border-radius: 50%;
  animation: neuro-pulse 1.5s infinite;
}

@keyframes neuro-pulse {
  0% { transform: scale(0.8); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(0.8); opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ollama-container {
    margin: 10px;
    padding: 15px;
  }
  
  .message-flow {
    height: 60vh;
  }
  
  .input-control {
    grid-template-columns: 1fr;
  }
}
  
/* 处理deepseek思维标签 */
*>>>.think-box {
  background-color: #232527;
  border-left: 4px solid hsl(207, 90%, 54%);
  padding: 12px;
  margin: 10px 0;
  color: #d1d8df;
}


*>>>.code-block {
  background-color: #1f1e1e;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
  overflow-x: auto;
}

*>>>.code-block code {
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

*>>>.inline-code {
  background-color: #414040;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

/* 可选语法高亮 */
*>>>.language-javascript {
  color: #2c3e50;
}
*>>>.language-javascript .keyword {
  color: #2196f3;
}
*>>>.language-javascript .function {
  color: #e91e63;
}

</style>
