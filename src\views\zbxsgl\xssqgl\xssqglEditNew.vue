<template>
    <div v-loading="loading">

        <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
                 label-width="231px"
                 size="default" @submit.prevent>
            <div class="titleDiv">
                <div class="titleFontDiv">基本信息</div>
            </div>
            <el-row :gutter="0" class="grid-row">
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="所属单位：" prop="SSDWID">
                        <div style="margin-left: 10px">{{formData.XMXX.SSDWMC}}</div>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="资金来源：" prop="ZJLY">
                        <el-select v-model="formData.ZJLY" class="full-width-input" clearable :disabled="!editable">
                            <el-option v-for="(item, index) in ZJLYOptions" :key="index" :label="item.DMMC"
                                       :value="item.DMXX"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="是否委托：" prop="SFWT">
                        <el-radio-group v-model="formData.SFWT" :disabled="!editable">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="16" class="grid-cell">
                    <el-form-item label="项目名称：" prop="XMXX.XMMC">
                        <el-input v-model="formData.XMXX.XMMC" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable && !params.XMID">
                                <el-button @click="dialogCGXMVisible=true">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="项目编号：" prop="XMXX.XMBH">
                        <el-input v-model="formData.XMXX.XMBH" type="text" placeholder="请输入" disabled></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="工程类别：" prop="XMXX.XMLB">
                        <el-cascader v-model="formData.XMXX.XMLB" :options="ZRZYTree" filterable :disabled="!editable"
                                     :props="{label:'ZYMC',value:'ZYBM',emitPath: false}" @change="gclbChange"
                                     clearable/>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item :label="formData.XSFS==='DJTP'?'项目金额（不含税）：':'项目预计金额（万元）：'" prop="XMXX.JE">
                        <el-input v-model="formData.XMXX.JE" type="text" placeholder="请输入"
                                  :disabled="!editable"
                                  @input="formData.XMXX.JE=formData.XMXX.JE.replace(/^([0-9-]\d*\.?\d{0,4})?.*$/, '$1')"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell" v-if="formData.XSFS === 'DJTP'">
                    <el-form-item label="有无计划：" prop="YWJH">
                        <el-radio-group v-model="formData.YWJH" :disabled="!editable">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell" v-if="formData.XSFS !== 'DJTP'">
                    <el-form-item label="是否框架项目：" prop="XMXX.SFKJXM">
                        <el-radio-group v-model="formData.XMXX.SFKJXM" :disabled="!editable">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell" v-if="formData.XSFS !== 'DJTP'">
                    <el-form-item label="有无标的：" prop="YWBD">
                        <el-radio-group v-model="formData.YWBD" :disabled="!editable">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="拟采购时间：" prop="NCGSJ">
                        <el-date-picker
                                v-model="formData.NCGSJ"
                                :disabled="!editable"
                                type="date"
                                clearable
                                style="width: 100%"
                                placeholder="请选择"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="拟采购地点：" prop="NCGDD">
                        <el-input v-model="formData.NCGDD" type="text" placeholder="请输入"
                                  :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="curXsfsFdmxx==='GKJJ'">
                    <el-form-item label="达到招标标准，但无法组织：" prop="ZBWFZZ">
                        <el-radio-group v-model="formData.ZBWFZZ" :disabled="!editable" @change="zbwfzzChange">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="formData.XSFS === 'DJTP'">
                    <el-form-item label="独家谈判原因：" prop="DJTPYY">
                        <el-input v-model="formData.DJTPYY" type="text" placeholder="请输入"
                                  :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="grid-cell" v-if="formData.XSFS === 'DJTP'">
                    <el-form-item label="独家谈判证明材料：" prop="DJTPZM">
                        <el-input v-model="formData.DJTPZM" type="text" placeholder="请输入"
                                  :disabled="!editable" style="width: 50%"></el-input>
                        <vsfileupload
                                :editable="editable"
                                :busId="params.id"
                                :key="params.id"
                                ywlb="DJTPZMCL"
                                busType="dwxx"
                                :limit="100"
                        ></vsfileupload>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="grid-cell" v-if="formData.XSFS === 'DJTP'">
                    <el-form-item label="独家谈判补充说明：" prop="DJTPBCSM">
                        <el-input v-model="formData.DJTPBCSM" type="text" placeholder="请输入"
                                  :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="是否需要相关部门审核：" prop="SFXGBMSH">
                        <el-radio-group v-model="formData.SFXGBMSH" :disabled="!editable">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell" v-if="params.FQRLX === 'EJDW'">
                    <el-form-item label="是否需要分公司相关部门审核：" prop="SFFGSBMSH">
                        <el-radio-group v-model="formData.SFFGSBMSH" :disabled="!editable || fgsDisabled">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell" v-if="curXsfsFdmxx==='GKJJ'">
                    <el-form-item label="是否需要局领导审核：" prop="SFJLDSH">
                        <el-radio-group v-model="formData.SFJLDSH" :disabled="!editable || jldshDisabled"
                            @change="jldshChange">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="24" class="grid-cell">
                    <el-form-item label="标段信息：">
                        <el-button style="margin-top: 4px;margin-left: 10px" type="primary" @click="addBD" v-if="editable">新增标段</el-button>
                        <span style="margin-left: 20px; color: #3c85fb">已添加{{ formData.BDList.length }}个标段</span>
                    </el-form-item>

                </el-col>

                <el-col :span="24" class="grid-cell">
                    <el-table ref="datatable91634" :data="formData.BDList" height="250px"
                              :border="true" :show-summary="false" size="default" :stripe="false"
                              class="lui-table"
                              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                        <el-table-column type="index" width="60" fixed="left" label="序号"
                                         align="center"/>
                        <el-table-column label="工程类别" align="center"
                                         :show-overflow-tooltip="true" disabled
                                         width="160">
                            <template #default="{row,$index}">
                                <span>{{ formData.XMXX.XMLBMC }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="BDMC" label="单项目工程名称" align="center"
                                         :show-overflow-tooltip="true" min-width="120">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`BDList.${$index}.BDMC`"
                                              label-width="0" :rules="tableRules.BDMC">
                                    <el-input v-model="row.BDMC" type="text" placeholder="请输入" clearable
                                              :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="BDNRSM" label="标段内容" align="center"
                                         :show-overflow-tooltip="true" min-width="160">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`BDList.${$index}.BDNRSM`"
                                              label-width="0" :rules="tableRules.BDNRSM">
                                    <el-input v-model="row.BDNRSM" type="text" placeholder="请输入" clearable
                                              :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="ZBRGS" label="中标人个数" align="center" v-if="curXsfsFdmxx==='GKJJ'"
                                         :show-overflow-tooltip="true" width="150">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`BDList.${$index}.ZBRGS`"
                                              label-width="0" :rules="tableRules.ZBRGS">
                                    <el-input v-model="row.ZBRGS" type="text" placeholder="请输入" clearable
                                              :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="SFAECF" label="是否按额拆分" align="center" v-if="curXsfsFdmxx==='GKJJ'"
                                         :show-overflow-tooltip="true" width="160">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`BDList.${$index}.SFAECF`"
                                              label-width="0" :rules="tableRules.SFAECF">
                                    <el-radio-group v-model="row.SFAECF" :disabled="!editable">
                                        <el-radio label="1">是</el-radio>
                                        <el-radio label="0">否</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="BZ" label="备注" align="center"
                                         :show-overflow-tooltip="true" min-width="120">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`BDList.${$index}.BZ`"
                                              label-width="0" :rules="tableRules.BZ">
                                    <el-input v-model="row.BZ" type="text" placeholder="请输入"
                                              clearable :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="CZ" label="操作" align="center" width="100" fixed="right"
                                         v-if="editable">
                            <template #default="scope">
                                <el-button size="small" class="lui-table-button" type="primary"
                                           @click="delRow(formData.BDList, scope.$index)">删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>

            </el-row>

            <div class="titleDiv">
                 <div class="titleFontDiv">拟邀请单位</div>
            </div>
            <el-row :gutter="0" class="grid-row">
                <el-col :span="24" class="grid-cell no-border-bottom">
                    <el-form-item label="资质要求：" prop="ZZYQ" >
                        <el-input v-model="formData.ZZYQ" type="textarea"
                                  :rows="3" placeholder="请输入" :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell" >
                    <el-form-item label="采购方式:" prop="XSFS">
                        <el-select v-model="formData.XSFS" class="full-width-input" clearable
                                   :disabled="!editable" @change="xsfsChange">
                            <el-option v-for="(item, index) in XSFSOptions" :key="index" :label="item.DMMC"
                                       :value="item.DMXX"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="16" class="grid-cell" v-if="('GKZB、GKJB、GKJJ').includes(formData.XSFS)">
                    <el-form-item label="承包商所属专业:" prop="CBSSSZY">
                        <el-input v-model="formData.CBSSSZY" type="text" placeholder="请输入" clearable disabled>

                            <template #append v-if="editable">
                                <el-button v-if="formData.CBSSSZY" style="margin-right: 21px;" @click="openCbsXqDialog">详细</el-button>
                                <el-button @click="openZyDialog">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="16" class="grid-cell" v-if="('YQZB、YQJB、YQJJ、DJTP').includes(formData.XSFS)">
                    <el-form-item label="" >
                        <el-button style="margin-top: 4px;margin-left: 10px" type="primary"
                                   @click="openNyqdwDialog" v-if="editable">添加拟邀请单位</el-button>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="('YQZB、YQJB、YQJJ、DJTP').includes(formData.XSFS)">
                    <el-table ref="datatable91634" :data="formData.DWList" height="250px"
                              :border="true" :show-summary="false" size="default" :stripe="false"
                              class="lui-table"
                              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                        <el-table-column type="index" width="60" fixed="left" label="序号"
                                         align="center"/>
                        <el-table-column label="单位名称" align="center" prop="DWMC"
                                         :show-overflow-tooltip="true" width="160">
                        </el-table-column>
                        <el-table-column label="队伍名称" align="center" prop="DWMC"
                                         :show-overflow-tooltip="true" width="160">
                        </el-table-column>
                        <el-table-column prop="TJRXM" label="推荐人姓名" align="center"
                                         :show-overflow-tooltip="true" min-width="120">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`DWList.${$index}.TJRXM`"
                                              label-width="0" :rules="tableRules.TJRXM">
                                    <el-input v-model="row.TJRXM" type="text" placeholder="请输入" clearable
                                              :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="TJLY" label="推荐理由" align="center"
                                         :show-overflow-tooltip="true" min-width="160">
                            <template #default="{row,$index}">
                                <el-form-item label="" :prop="`DWList.${$index}.TJLY`"
                                              label-width="0" :rules="tableRules.TJLY">
                                    <el-input v-model="row.TJLY" type="text" placeholder="请输入" clearable
                                              :disabled="!editable">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="CZ" label="操作" align="center" width="100" fixed="right"
                                         v-if="editable">
                            <template #default="scope">
                                <el-button size="small" class="lui-table-button" type="primary"
                                           @click="delRow(formData.DWList, scope.$index)">删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
            </el-row>

            <div class="titleDiv">
                <div class="titleFontDiv">其他信息</div>
            </div>
            <el-row :gutter="0" class="grid-row">
                <el-col :span="24" class="grid-cell">
                    <el-form-item label="相关资料:" prop="FJZL">
                        <vsfileupload
                                :editable="editable"
                                :busId="params.id"
                                :key="params.id"
                                ywlb="XGZL"
                                busType="dwxx"
                                :limit="100"
                        ></vsfileupload>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="grid-cell">
                    <el-form-item label="其他说明:" prop="QTSM">
                        <el-input v-model="formData.QTSM" type="textarea"
                                  :rows="3" placeholder="请输入" :disabled="!editable"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell">
                    <el-form-item label="授权评标委员会现场确定中标人：" prop="SQXCQR">
                        <el-radio-group v-model="formData.SQXCQR" :disabled="!editable">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>

            <div class="titleDiv">
                <div class="titleFontDiv">办理人选择</div>
            </div>
            <el-row :gutter="0" class="grid-row"
                    v-if="['1', 'new'].includes(value.activityId)">


                <el-col :span="24" class="grid-cell" v-if="formData.BLRList[0] && !formData.BLRList[0].hide">
                    <el-form-item label="项目主管部门领导审查：" prop="BLRList.0.VALUE">
                        <el-input v-model="formData.BLRList[0].VALUE" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable">
                                <el-button @click="openBlrDialog('XSGL_EJDW_XMZBBMKSZ', '0')">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="formData.BLRList[1] && !formData.BLRList[1].hide">
                    <el-form-item label="计划财务科审查：" prop="BLRList.1.VALUE">
                        <el-input v-model="formData.BLRList[1].VALUE" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable">
                                <el-button @click="openBlrDialog('LC_XSSQ_QGBKSZ', '1')">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="formData.BLRList[2] && !formData.BLRList[2].hide">
                    <el-form-item label="二级市场部门审核：" prop="BLRList.2.VALUE">
                        <el-input v-model="formData.BLRList[2].VALUE" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable">
                                <el-button @click="openBlrDialog('XSGL_EJDW_YWFGLD', '2')">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="formData.BLRList[3] && !formData.BLRList[3].hide">
                    <el-form-item label="分管领导审查：" prop="BLRList.3.VALUE">
                        <el-input v-model="formData.BLRList[3].VALUE" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable">
                                <el-button @click="openBlrDialog('XSGL_YTFGLD', '3')">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="formData.BLRList[4] && !formData.BLRList[4].hide">
                    <el-form-item label="单位领导审查：" prop="BLRList.4.VALUE">
                        <el-input v-model="formData.BLRList[4].VALUE" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable">
                                <el-button @click="openBlrDialog('XSGL_YTLD', '4')">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="formData.BLRList[5] && !formData.BLRList[5].hide">
                    <el-form-item label="分公司相关部门审核：" prop="BLRList.5.VALUE">
                        <el-input v-model="formData.BLRList[5].VALUE" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable">
                                <el-button @click="openBlrDialog('XSGL_YTLD', '5')">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="formData.BLRList[6] && !formData.BLRList[6].hide">
                    <el-form-item label="局领导审核：" prop="BLRList.6.VALUE">
                        <el-input v-model="formData.BLRList[6].VALUE" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable">
                                <el-button @click="openBlrDialog('XSGL_YTLD', '6')">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="grid-cell" v-if="formData.BLRList[7] && !formData.BLRList[7].hide">
                    <el-form-item label="相关部门审查：" prop="BLRList.7.VALUE">
                        <el-input v-model="formData.BLRList[7].VALUE" type="text" placeholder="请输入" clearable disabled>
                            <template #append v-if="editable">
                                <el-button @click="openBlrDialog('XSGL_YTLD', '7')">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

            </el-row>
        </el-form>

        <div style="width: 100%;margin-top: 10px;margin-bottom: 10px;justify-content: center;display: flex">
            <el-button size="default" type="success" @click="onSave('0')" v-if="editable">保存</el-button>
            <el-button size="default" type="primary" @click="onSave('1')" v-if="editable">提交</el-button>
            <el-button size="default" @click="closeForm">返回</el-button>
        </div>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogCGXMVisible"
                v-model="dialogCGXMVisible"
                title="采购项目选择"
                z-index="1000"
                top="6vh"
                width="1100px">
            <div>
                <xmChoose @close="dialogCGXMVisible=false" @submit="getCgxmRes" :params="params"/>
            </div>
        </el-dialog>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogXzzyVisible"
                v-model="dialogXzzyVisible"
                title="选择专业"
                z-index="1000"
                top="6vh"
                width="800px">
            <div>
                <zyChoose v-model="formData" @close="dialogXzzyVisible=false" @submit="getZyxxRes" />
            </div>
        </el-dialog>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="blrDialogVisible"
                v-model="blrDialogVisible"
                title="办理人选择"
                z-index="1000"
                top="6vh"
                width="800px">
            <div>
                <ryChoose @close="blrDialogVisible=false" @submit="getBlrRes" :params="blrParams"/>
            </div>
        </el-dialog>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogYQDWVisible"
                v-model="dialogYQDWVisible"
                title="邀请单位选择"
                z-index="1000"
                top="6vh"
                width="1100px">
            <div>
                <dwChoose @close="dialogYQDWVisible=false" @submit="getYqdwRes" :params="params"/>
            </div>
        </el-dialog>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogCbsViewVisible"
                v-model="dialogCbsViewVisible"
                title="承包商列表"
                z-index="1000"
                top="6vh"
                width="1100px">
            <div>
                <cbsViewList @close="dialogCbsViewVisible=false" :params="cbsParams"/>
            </div>
        </el-dialog>

    </div>
</template>

<script>

    import {
        defineComponent,
        reactive,
        toRefs,
        getCurrentInstance,
        onMounted,
        onBeforeMount,
        watch,
        computed
    } from "vue";
    import vsAuth from "@lib/vsAuth";
    import comFun from "@lib/comFun";
    import axiosUtil from "@lib/axiosUtil";
    import {ElMessage} from "element-plus";
    import zbwjTab from "@views/zbxsgl/xssqgl/commonTab/zbwjTab";
    import xmChoose from "@views/zbxsgl/xssqgl/xmChoose";
    import dwChoose from "@views/zbxsgl/xssqgl/dwChoose";
    import zyChoose from "@views/zbxsgl/xssqgl/zyChoose";
    import ryChoose from "@views/zbxsgl/xssqgl/ryChoose";
    import cbsViewList from "@views/zbxsgl/xssqgl/cbsViewList";
    import vsfileupload from "@src/views/components/vsfileupload.vue";

    export default defineComponent({
        name: '',
        components: {zbwjTab, vsfileupload, xmChoose, dwChoose, zyChoose, ryChoose, cbsViewList},
        props: {
            params: {
                type: Object,
                required: true
            },
            value: {
                type: Object,
                default: {}
            }
        },
        setup(props, {emit}) {
            const state = reactive({
                userInfo: vsAuth.getAuthInfo().permission,
                loading: false,
                editable: props.params.editable,
                FAID: props.params.id,
                formData: {
                    CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
                    CJRXM: vsAuth.getAuthInfo().permission.userName,
                    CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
                    CJSJ: comFun.getNowTime(),
                    SHZT: '0',
                    YWZT: '1',

                    SFFBD: '0',

                    XMXX: {},
                    XSWJ: {
                        WJID: comFun.newId(),
                        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
                        CJRXM: vsAuth.getAuthInfo().permission.userName,
                        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
                        CJSJ: comFun.getNowTime(),
                        SHZT: '0',
                        YWZT: '1',
                        SFWSPS: '0'
                    },
                    BDList: [],
                    DWList: [],
                    BLRList: [],
                    dwywfwTable: [],
                },

                BLRModel: {
                    'XSGL_XXSQ_NEW': [
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_EJDW_XMZBBMKSZ', ACTIVITYID: '3', LABEL: '项目主管部门领导审查', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'LC_XSSQ_QGBKSZ', ACTIVITYID: '6', LABEL: '计划财务科审查', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_EJDW_YWFGLD', ACTIVITYID: '7', LABEL: '二级市场部门审核', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTFGLD', ACTIVITYID: '8', LABEL: '分管领导审查', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTLD', ACTIVITYID: '9', LABEL: '单位领导审查', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTLD', ACTIVITYID: '11', LABEL: '分公司相关部门审核', required: true, hide: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTLD', ACTIVITYID: '13', LABEL: '局领导审核', required: true, hide: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTLD', ACTIVITYID: '15', LABEL: '相关部门审查', required: true, hide: true},
                    ],
                    'XSGL_XXSQ_JGBM': [
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_EJDW_XMZBBMKSZ', ACTIVITYID: '3', LABEL: '项目主管部门领导审查', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'LC_XSSQ_QGBKSZ', ACTIVITYID: '6', LABEL: '计划财务科审查', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_EJDW_YWFGLD', ACTIVITYID: '7', LABEL: '二级市场部门审核', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTFGLD', ACTIVITYID: '8', LABEL: '分管领导审查', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTLD', ACTIVITYID: '9', LABEL: '单位领导审查', required: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTLD', ACTIVITYID: '11', LABEL: '分公司相关部门审核', required: true, hide: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTLD', ACTIVITYID: '13', LABEL: '局领导审核', required: true, hide: true},
                        {VALUE: '', TYPE: 'GR', ROLE_CODE: 'XSGL_YTLD', ACTIVITYID: '15', LABEL: '相关部门审查', required: true, hide: true},
                    ],
                },
                rules: {
                    'XMXX.SFWT': [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    'XMXX.SFKJXM': [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    'XMXX.XMLB': [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    'XMXX.JE': [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    'XMXX.XMMC': [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    'XMXX.XMBH': [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    XMID: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    DJTPYY: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    DJTPZM: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    DJTPBCSM: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    YWJH: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    SFJLDSH: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    ZBWFZZ: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    YWBD: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    NCGSJ: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    NCGDD: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    SFXGBMSH: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    SFFGSBMSH: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    SFWT: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    ZJLY: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    ZZYQ: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    XSFS: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    CBSSSZY: [{
                        required: true,
                        message: '字段值不可为空',
                    }],

                },
                tableRules: {
                    BDMC: [{
                        required: true,
                        message: '待填写',
                    }],
                    BDNRSM: [{
                        required: true,
                        message: '待填写',
                    }],
                    BZ: [{
                        required: true,
                        message: '待填写',
                    }],
                    TJRXM: [{
                        required: true,
                        message: '待填写',
                    }],
                    TJLY: [{
                        required: true,
                        message: '待填写',
                    }],
                    ZBRGS: [{
                        required: true,
                        message: '待填写',
                    }],
                    SFAECF: [{
                        required: true,
                        message: '待填写',
                    }],
                },

                ZYGLBMKSZOptions: [],
                YWFGLDOptions: [],
                JGBMFGLDOptions: [],//机关部门分管领导
                ZYLDOptions: [],//二级单位主要领导

                ZYGLBMKSList: [],
                ZYGLBMKSTree: [],
                YTZYBMBSList: [],
                YTZYBMBSTree: [],
                ZRZYTree: [],
                zrzyList: [],
                XSFSOptions: [],
                fileTableData: [],
                blrParams: {},
                ZJLYOptions: [],
                dialogCGXMVisible: false,
                dialogXzzyVisible: false,
                blrDialogVisible: false,
                zyParams: {},
                blrIndex: '',
                parentNode: {},
                fgsDisabled: false,
                jldshDisabled: false,
                dialogYQDWVisible: false,
                // 当前选中的选商方式的父代码信息
                curXsfsFdmxx: '',
                dialogCbsViewVisible: false,
                cbsParams: {},
                parentXsfs: {},
            });

            watch(() => state.formData.SFFGSBMSH, () => {
                changeBlrList()
            }, {deep: true});

            watch(() => state.formData.SFJLDSH, () => {
                changeBlrList()
            }, {deep: true});

            watch(() => state.parentXsfs, () => {
                changeBlrList()
            }, {deep: true});

            const getFormData = () => {
                let params = {
                    FAID: state.FAID
                }
                state.loading = true
                axiosUtil.get('/backend/xsgl/xssqgl/selectXssqById', params).then((res) => {
                    state.formData = res.data;
                    if (state.formData.BLRList.length === 0) {
                        state.formData.BLRList = state.BLRModel[props.value.processId];
                    }
                    if (state.formData.dwywfwTable.length > 0) {
                        state.formData.CBSSSZY = state.formData.dwywfwTable.map((i) => i.YWMC).join(",");
                    }

                    // 触发 选商方式 赋值，判断输入框展示
                    getDMBData("XSFS", "XSFSOptions", '', state.formData.XSFS);

                    state.loading = false;
                })
            }


            const saveData = (value) => {
                let type = value === '1' ? 'submit' : 'save';
                return new Promise((resolve, reject) => {
                    if (type === 'save') {
                        resolve(submitForm(type));
                    } else {
                        validateForm().then(() => {
                            resolve(submitForm(type));
                        }).catch(msg => {
                            ElMessage.error(msg);
                            reject(msg)
                        })
                    }
                })


            }

            const submitForm = (type) => {
                return new Promise((resolve, reject) => {
                    /*let data = {
                        ...props.value,
                        businessId: props.value.businessId || props.params.id,
                        processInstanceName: state.formData.XMXX.XMMC + '-选商申请',
                        conditionStr: "lczx=1",
                    };
                    emit("update:value", data);

                    if (!['1', 'new'].includes(props.value.activityId)) {
                        resolve(true);
                        return;
                    }*/


                    let params = {
                        ...state.formData,
                        FAID: state.FAID,
                        ZZFS: state.formData.SFWT==='1'?'WTZZ':'ZXZZ',
                        XSWJ: {
                            ...state.formData.XSWJ,
                            FAID: state.FAID,
                            XGRZH: state.userInfo.userLoginName,
                            XGSJ: comFun.getNowTime(),
                        },
                        XGRZH: state.userInfo.userLoginName,
                        XGSJ: comFun.getNowTime(),

                        BLRList: state.formData.BLRList.map((item, index) => {
                            return {
                                ...item,
                                BUSINESSID: props.params.id,
                                PROCESSID: props.value.processId,
                                CJRZH: state.userInfo.userLoginName,
                                CJRXM: state.userInfo.userName,
                                CJDWID: state.userInfo.orgnaId,
                                CJSJ: comFun.getNowTime(),
                                SHZT: '1',
                                XGRZH: state.userInfo.userLoginName,
                                XGSJ: comFun.getNowTime(),
                            }
                        }),
                        DWList: state.formData.DWList.map((item, index) => {
                            return {
                                ...item,
                                YQDWID: comFun.newId(),
                                FAID: state.FAID,
                                CJRZH: state.userInfo.userLoginName,
                                CJRXM: state.userInfo.userName,
                                CJDWID: state.userInfo.orgnaId,
                                CJSJ: comFun.getNowTime(),
                                SHZT: type==='submit'?'1':'0',
                                YWZT: '1',
                                XGRZH: state.userInfo.userLoginName,
                                XGSJ: comFun.getNowTime(),
                            }
                        }),
                    };

                    if (type === 'submit') {
                        params.SHZT = '2';
                    }
                    state.loading = true;
                    axiosUtil.post('/backend/xsgl/xssqgl/saveXssqForm', params).then(res => {
                        state.loading = false;
                        /*if (state.formData.ZJLY === 'TZ' && state.formData.XMXX.ZYFL === 'ADMGC') {
                            ElMessage.success('提交成功');
                            emit("closeAudit");
                            reject();
                        } else {
                            resolve(true);
                        }*/
                        resolve(true);
                    })
                })

            }

            const getBlrName = (values, index) => {
                if (props.value.processId === 'XSGL_XXSQ_NEW') {
                    return values.map(ii => {
                        if (index === 0) {
                            return state.ZYGLBMKSList.find(iii => iii.ORGNA_ID === ii)?.ORGNA_NAME
                        } else if (index === 1) {
                            return state.YWFGLDOptions.find(iii => iii.USER_LOGINNAME === ii)?.USER_NAME
                        } else if (index === 2) {
                            return state.ZYLDOptions.find(iii => iii.USER_LOGINNAME === ii)?.USER_NAME
                        } else if (index === 3) {
                            return state.YTZYBMBSList.find(iii => iii.ORGNA_ID === ii)?.ORGNA_NAME
                        }
                    }).join(',')
                } else if (props.value.processId === 'XSGL_XXSQ_JGBM') {
                    return values.map(ii => {
                        if (index === 0) {
                            return state.JGBMFGLDOptions.find(iii => iii.USER_LOGINNAME === ii)?.USER_NAME
                        } else if (index === 1) {
                            return state.YTZYBMBSList.find(iii => iii.ORGNA_ID === ii)?.ORGNA_NAME
                        }
                    }).join(',')
                }


            }

            const instance = getCurrentInstance();
            const validateForm = () => {
                return Promise.all([
                    validateData(),
                    validateList()
                ])
            };

            const validateData = () => {
                return new Promise((resolve, reject) => {
                    instance.proxy.$refs['vForm'].validate((valid) => {
                        if (valid) {
                            resolve(true)
                        } else {
                            reject('请完善表单信息页面')
                        }
                    })
                })
            };

            const validateList = () => {
                return new Promise((resolve, reject) => {

                    if(state.formData.BDList.length === 0){
                        reject('请至少添加一条标段信息')
                    }

                    // 指定的选商方式里，且拟邀请单位数量是0
                    if(('YQZB、YQJB、YQJJ、DJTP').includes(state.formData.XSFS) && state.formData.DWList.length === 0){
                        reject('请至少添加一条单位信息')
                    }

                    let error = '';
                    for(let i = 0; i < state.formData.BLRList.length; i++) {
                        let map = state.formData.BLRList[i];
                        // 办理人没有值，且hide=false，没有隐藏的
                        if(!map.VALUE && !map.hide) {
                            console.log(map, '----map')
                            error = '办理人选择模块，各节点请选择至少一名节点办理人';
                            break;
                        }
                    }
                    if(error) {
                        reject(error)
                    }

                    resolve(true)
                })
            };


            const changeBlrList = () => {
                if (state.formData.BLRList.length > 0) {
                    if(state.formData && state.formData.BLRList.length > 0 && state.formData.BLRList[5]) {
                        state.formData.BLRList[5].hide = state.formData.SFFGSBMSH !== '1';
                    }
                    if(state.formData && state.formData.BLRList.length > 0 && state.formData.BLRList[6]) {
                        state.formData.BLRList[6].hide = state.formData.SFJLDSH !== '1';
                    }
                    console.log(state.parentXsfs, '--state.parentXsfs')
                    if(state.formData && state.formData.BLRList.length > 0 && state.formData.BLRList[7]) {
                        if(state.parentXsfs &&state.parentXsfs.ZYBM === 'XXHFW' && props.params.FQRLX === 'JGBM'
                            && state.formData.XSFS === 'DJTP') {
                            state.formData.BLRList[7].hide = false;
                        }else {
                            state.formData.BLRList[7].hide = true;
                        }

                    }
                }
            }

            const getXmxx = (XMID) => {
                let params = {
                    XMID: XMID
                }
                axiosUtil.get('/backend/xsgl/xssqgl/selectXmxxByXmid', params).then(res => {
                    state.formData.XMXX = res.data
                    state.formData.XMID = XMID
                })
            }

            const closeForm = () => {
                emit('close')
            }

            const getBlrOptions = (optionName, ROLE) => {
                let params = {
                    ROLE: ROLE,
                    ORGNA_TWO_ID: state.userInfo.orgnaId
                }
                axiosUtil.get('/backend/common/selectUserByRole', params).then(res => {
                    state[optionName] = res.data || []
                })
            }

            const getDMBData = async (DMLBID, resList, FDMXX, DMXX_XSFS) => {
                let params = {
                    DMLBID, FDMXX, DMXX_XSFS
                }
                const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
                state[resList] = res.data;
                if(DMLBID === 'XSFS') {
                    xsfsChange(FDMXX?FDMXX:DMXX_XSFS);
                }
            };

            const getZrzyList = () => {
                let params = {};
                axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
                    state.zrzyList = [...res.data];
                    state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0');
                    // 触发 工程类别 赋值， 判断办理人展示
                    setTimeout(() => {
                        gclbChange(state.formData.XMXX.XMLB);
                    }, 300)
                });
            };

            const openBlrDialog = (val, blrIndex) => {
                state.blrIndex = blrIndex;
                state.blrParams = {
                    blrRoleCode: val,
                };
                state.blrDialogVisible = true;
            };

            const getBlrRes = (val) => {
                state.formData.BLRList[state.blrIndex].NAME = val.RYMCS;
                state.formData.BLRList[state.blrIndex].VALUE = val.RYZHS;
                state.blrDialogVisible = false;
            };

            const addBD = () => {
                state.formData.BDList.push({
                    FABDID: comFun.newId(),
                    FAID: state.FAID,
                    CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
                    CJRXM: vsAuth.getAuthInfo().permission.userName,
                    CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
                    CJSJ: comFun.getNowTime(),
                    XMLBMC: state.formData.XMXX.XMLBMC,
                });
            };

            const getCgxmRes = (value) => {
                //state.formData.XMXX = value;
                state.formData.XMID = value.XMID;
                state.formData.XMXX.XMID = value.XMID;
                state.formData.XMXX.XMMC = value.XMMC;
                state.formData.XMXX.SSDWMC = value.SSDWMC;
                state.formData.XMXX.XMBH = value.XMBH;
                state.dialogCGXMVisible = false
            };

            const gclbChange = (val) => {
                if(val) {
                    // 最顶层父专业是 信息化服务时，且当前是二级单位发起
                    let parent = findTopParentNode(state.ZRZYTree, val);

                    state.parentXsfs = parent;
                    if(parent.ZYBM === 'XXHFW' && props.params.FQRLX === 'EJDW') {
                        // 是否需要分公司相关部门审核 默认 是， 且不可修改
                        state.formData.SFFGSBMSH = '1';
                        state.fgsDisabled = true;
                    }else {
                        state.fgsDisabled = false;
                    }

                    state.zrzyList.forEach(item => {
                        if(item.ZYBM === val) {
                            state.formData.XMXX.XMLBMC = item.ZYMC;
                        }
                    });
                    state.formData.BDList.forEach(it => {
                        it.XMLBMC = state.formData.XMXX.XMLBMC;
                    });

                }
            };

            // 根据当前子节点找到最顶层的父节点信息
            const findTopParentNode = (treeData, childId) => {
                let result = null;
                function traverse(nodes, parent = null) {
                    for (const node of nodes) {
                        if (node.ZYBM === childId) {
                            // 如果当前节点是目标子节点，则返回其父节点
                            result = parent;
                            return;
                        }
                        if (node.children && node.children.length > 0) {
                            // 递归遍历子节点，当前节点作为父节点
                            traverse(node.children, node);
                        }
                    }
                }
                traverse(treeData);
                // 如果父节点还有父节点，则继续向上查找
                while (result && result.FZYBM) {
                    const parent = findParentById(treeData, result.FZYBM);
                    if (parent) {
                        result = parent;
                    } else {
                        break;
                    }
                }
                return result;
            };
            const findParentById = (nodes, parentId) => {
                for (const node of nodes) {
                    if (node.ZYBM === parentId) {
                        return node;
                    }
                    if (node.children && node.children.length > 0) {
                        const found = findParentById(node.children, parentId);
                        if (found) {
                            return found;
                        }
                    }
                }
                return null;
            };


            const findParent = (tree, targetId) => {
                for (let node of tree) {
                    if (node.children && node.children.some(child => child.ZYBM === targetId)) {
                        return node; // 找到父元素，返回它
                    } else if (node.children) {
                        state.parentNode = node;
                        const result = findParent(node.children, targetId); // 递归查找子节点中的父元素
                        if (result) return result; // 如果在子节点中找到父元素，返回它
                    }
                }
                return null; // 如果没有找到，返回null
            };

            const openZyDialog = ( ) => {
                state.formData.editable = state.editable;
                state.formData.FAID = state.FAID;
                state.dialogXzzyVisible = true;
            };

            const getZyxxRes = (param) => {
                state.formData.CBSSSZY = param.CBSSSZY;
                state.formData.YWFWGZ = param.YWFWGZ;
                state.formData.dwywfwTable = param.tableData;
                state.dialogXzzyVisible = false;
            };

            const delRow = (list, index) => {
                list.splice(index, 1)
            };

            const openNyqdwDialog = () => {
                state.dialogYQDWVisible = true;
            };

            const getYqdwRes = (list) => {
                state.formData.DWList = list;
                state.dialogYQDWVisible = false;
            };

            const xsfsChange = (val) => {
                state.XSFSOptions.forEach(item => {
                    if(item.DMXX === val) {
                        state.curXsfsFdmxx = item.FDMXX;
                    }
                });
                // 选择相应的选商方式后，没有显示的 邀请单位、 或者 承包商所属专业就会清空
                if("GKZB、GKJB、GKJJ".includes(val)) {
                    state.formData.DWList = [];
                }else if("YQZB、YQJB、YQJJ".includes(val)) {
                    state.formData.CBSSSZY = '';
                    state.formData.YWFWGZ = '';
                    state.formData.dwywfwTable = [];
                }
            };

            const zbwfzzChange = (val) => {
                if(val === '1') {
                    state.formData.SFJLDSH = '1';
                    jldshChange(state.formData.SFJLDSH);
                    state.jldshDisabled = true;
                }else {
                    state.jldshDisabled = false;
                }
            };

            const jldshChange = (val) => {
                // 局机关节点 显示出来
                state.formData.BLRList.forEach(item => {
                    if(item.ACTIVITYID === '13') {
                        item.hide = (val !== '1');
                    }
                })
            };

            const openCbsXqDialog = () => {

                let YWBM_ARR = state.formData.dwywfwTable.map((i) => i.YWBM).join(",").split(",");
                state.cbsParams = {
                    YWBM_ARR: YWBM_ARR,
                };
                state.dialogCbsViewVisible = true;
            };

            /**
             * 保存
             */
            const onSave = (type) => {
                saveData(type)
                    .then(() => {
                        ElMessage({
                            message: '保存成功！',
                            customClass: "myMessageClass",
                            type: 'success',
                        })
                    })
                    .catch((error) => {
                        console.log(error);
                        ElMessage({
                            message: '保存失败！',
                            customClass: "myMessageClass",
                            type: 'error',
                        })
                    });
            }

            onMounted(() => {
                if (props.params.operation !== 'add') {
                    getFormData()
                } else {
                    state.formData.BLRList = state.BLRModel[props.value.processId];

                    state.formData.XSFS = props.params.XSFS;
                    state.formData.SQLX = props.params.SQLX;
                    if (props.params.XMID) {
                        getXmxx(props.params.XMID);
                    }

                    getDMBData("XSFS", "XSFSOptions", props.params.XSFS);

                }

                getDMBData("ZJLY", "ZJLYOptions");
                getZrzyList();

            });

            return {
                ...toRefs(state),
                closeForm,
                saveData,
                getZrzyList,
                addBD,
                openBlrDialog,
                getCgxmRes,
                gclbChange,
                openZyDialog,
                getZyxxRes,
                getBlrRes,
                onSave,
                delRow,
                openNyqdwDialog,
                getYqdwRes,
                xsfsChange,
                zbwfzzChange,
                jldshChange,
                openCbsXqDialog
            }
        }

    })
</script>

<style scoped>
    .tab-pane {
        overflow: auto;
    }

    .titleDiv {
        margin-bottom: 10px; font-size: 18px;color: black;border-bottom: 1px solid #BBBBBB;
    }
    .titleFontDiv {
        border-bottom: 4px solid #A6D0FE; width: 100px
    }
</style>
