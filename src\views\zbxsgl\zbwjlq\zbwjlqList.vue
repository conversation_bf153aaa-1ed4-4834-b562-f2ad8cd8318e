<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMMC">
            <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="XMMC" label="项目名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="SSDWMC" label="所属单位" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="ZBWJHQSJJS" label="截止时间" align="center"
                               :show-overflow-tooltip="true" width="180"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">领取文件
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="招标文件领取"
        @closed="closeForm"
        z-index="1000"
        width="1200px">
      <div>
        <zbwjlqView v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import zbwjlqView from "@views/zbxsgl/zbwjlq/zbwjlqView";
import vsAuth from "@lib/vsAuth";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,zbwjlqView},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        LQR: vsAuth.getAuthInfo().permission.userLoginName
      }
      axiosUtil.get('/backend/xsgl/zbwjlq/selectLqzbwjxmPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }



    const viewRow = (row) => {
      state.params = {editable: false, id: row.GGID, operation: 'view'}
      state.dialogVisible = true
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      closeForm,
      viewRow

    }
  }

})
</script>

<style scoped>

</style>
