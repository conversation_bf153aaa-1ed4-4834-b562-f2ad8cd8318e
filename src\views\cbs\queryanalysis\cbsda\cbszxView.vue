<template>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="190px" size="default" v-loading="loading" @submit.prevent>
        <el-row>
            <el-col :span="24">
                <span class="title">>>>征信查询</span>
            </el-col>
            <el-divider></el-divider>
        </el-row>
        <el-row style="height: 60px;">
            <el-col :span="20">
                <span>主要包括基本信息、经营风险、司法负面、经营情况、企业财务情况、与中石化合作表现等。</span>
            </el-col>
            <el-col :span="4" style="text-align: right;">
                <el-button size="default" type="primary" @click="viewZx">查看详情</el-button>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <span class="title">>>>关系图谱</span>
            </el-col>
            <el-divider></el-divider>
            
        </el-row>
        <el-row style="height: 60px;">
            <el-col :span="20">
                <span>通过股权关联法人股东、对外投资，分析同一股东、对外投资同一企业等情况，实现企企关联分析。包括单企业扩展及找关系。</span>
            </el-col>
            <el-col :span="4" style="text-align: right;">
                <el-button size="default" type="primary" @click="viewGxtp">查看详情</el-button>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <span class="title">>>>信用报告</span>
            </el-col>
            <el-divider></el-divider>
            <el-col :span="24" style="text-align: right;">
                <el-button size="default" type="primary" @click="createXybg">生成新报告</el-button>
            </el-col>
        </el-row>
        <el-row class="grid-row" style="margin-top: 10px">
            <el-col :span="24" class="grid-cell">
                <el-table 
                    :data="tableData" 
                    class="lui-table"
                    border
                    stripe
                    height="320px"
                    size="default" 
                    highlight-current-row>
                    <el-table-column prop="companyName" label="相对人名称" header-align="center" align="left" min-width="220">
                    </el-table-column>
                    <el-table-column label="生成时间" header-align="center" align="center" min-width="150">
                        <template #default="scope">
                            <span>{{scope.row.inputFileDetailCreateTime.slice(0,-2)}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="报告下载" align="center" min-width="100">
                        <template #default="scope">
                            <el-button size="small" class="lui-table-button" type="primary" @click="preview(scope.row)">预览
                            </el-button>
                            <el-button size="small" class="lui-table-button" type="primary" @click="download(scope.row)">下载
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axiosUtil from "@lib/axiosUtil";
const props = defineProps({
    params: {
        type: Object,
        default: () => ({})
    },
});
const emit = defineEmits([]);
const tableData = ref([]);
const token = ref("");
const apiCode = ref("");
onMounted(() => {
    getJkToken();
    queryXybgList();
});
// 获取金科平台token
const getJkToken = () => {
    axiosUtil.get('/backend/extApi/getJkToken', {}).then((res) => {
        token.value = "___" + res.data.token;
        apiCode.value = res.data.apiCode;
    });
};
// 获取信用报告列表
const queryXybgList = () => {
    axiosUtil.get('/backend/extApi/getXybgListFromJK', {
        CBSDWQC: props.params.CBSDWQC
    }).then((res) => {
        if(res.data && res.data.data && res.data.data.msg === 'success'){
            tableData.value = res.data.data.rows;
        }else{
            ElMessage.error("接口调用失败！");
        }
    });
};
// 生成新报告
const createXybg = () => {
    axiosUtil.get('/backend/extApi/createXybgFromJK', {
        CBSDWQC: props.params.CBSDWQC
    }).then((res) => {
        if(res.data && res.data.data && res.data.data.msg === 'success'){
            ElMessage.success("新报告已生成！");
            queryXybgList();
        }else{
            ElMessage.error("接口调用失败！");
        }
    });
};
// 预览
const preview = (row) => {
    let url = "https://fintech.sinopec.com/report/reportTask/viewPdf?inputFileDetailId=" + row.inputFileDetailId;
    window.open(url);
}
// 下载
const download = (row) => {
    let url = "https://fintech.sinopec.com/report/reportTask/downloadSignal?inputFileDetailId=" + row.inputFileDetailId;
    const link = document.createElement('a');
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

const viewZx = () => {
    let url = "https://fintech.sinopec.com/report/index.html#/credit-center/companydetail/"
            + decodeURI(props.params.CBSDWQC) +"/null?type=1&token="+ token.value +"&apiCode=" + apiCode.value;
    window.open(url);
}

const viewGxtp = () => {
    let url = "https://fintech.sinopec.com/report/index.html#/relationship-graph/relation-extend?companyName="
            + decodeURI(props.params.CBSDWQC) +"&token="+ token.value +"&apiCode=" + apiCode.value;
    window.open(url);
}
defineExpose({});
</script>

<style scoped>
.el-divider--horizontal {
    margin: 12px 0;
}
.title{
    font-size: 16px;
    font-weight: bold;
}
</style>