<template>
  <div>
    <el-form :model="tableData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;color: #2A96F9;
      border-bottom: 3px solid #2A96F9;text-align: center;padding-bottom: 2px;padding-left: 20px;padding-right: 20px;">
        标底或最高限价
      </div>
      <div style="width: 100%;margin-bottom: 10px;justify-content: right;display: flex">
        <el-button type="primary" @click="">造价系统业务查询</el-button>
      </div>
      <div style="color: #c03c29">* 浮动率招标   填写注意: (例)上浮5% 写成 0.05, 下浮5%写成-0.05。</div>
      <el-table ref="datatable91634" :data="tableData" height="300px"
                :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column prop="BDMC" label="标段名称" align="center"
                         :show-overflow-tooltip="true" min-width="160"></el-table-column>
        <el-table-column prop="BDJ" label="标的浮动率/标的（元）" align="center" width="160">
        </el-table-column>
        <el-table-column prop="ZGXJ" label="最高限价（元）" align="center" width="160">
        </el-table-column>

      </el-table>

      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="标的或最高限价附件：" prop="BDZGXJFJ">
            <vsfileupload
                ref="BDZGXJFJ"
                :editable="editable"
                :busId="KPBYXID"
                :key="KPBYXID"
                ywlb="BDZGXJFJ"
                busType="BDZGXJFJ"
            ></vsfileupload>
          </el-form-item>
        </el-col>
      </el-row>


      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
<!--        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>-->
        <el-button @click="getTableData">刷新</el-button>
      </div>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";


export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.parentForm.TBRQD_WCZT!=='1',
      role: props.fromParams.role,
      KPBYXID: props.params.KPBYXID,
      JLID: props.params.JLID,
      KBJLID: props.params.KBJLID,
      tableData: [],
      rules: {

      }
    })

    const getTableData = () => {
      let params={
        kbjlid: state.KBJLID
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/kbjl/queryBdZgxj', params).then((res) => {
        state.tableData=res.data.list || []
        state.loading=false
      })
    }

    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      // let params={
      //   QZXXList: state.tableData.map(item=>{
      //     return{
      //       ...item,
      //       SHZT: '1'
      //
      //     }
      //   })
      // }
      // state.loading=true
      // axiosUtil.post('/backend/kpbyx/tbrqd/saveTbrqzxx',params).then(res=>{
      //   ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
      //   emit('saveFromData', {TBRQD_WCSJ: comFun.getNowTime(),TBRQD_WCZT: '1'}, {})
      //   nextTick(() => {
      //     state.editable=false
      //     emit('nextStep', `已完成投标人签到`)
      //   })
      //   state.loading=false
      // })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    onMounted(() => {
        getTableData();
    })

    return {
      ...toRefs(state),
      getTableData,
      saveData

    }
  }

})
</script>

<style scoped>

</style>
