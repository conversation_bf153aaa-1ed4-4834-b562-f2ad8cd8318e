import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"


// 通用接口-附件表查询
// @method getCommonSelectFjb
// @type get
// @return url
//getCommonSelectFjb: `/common/selectFjb`,

// eslint-disable-next-line
export function getCommonSelectFjb(params) {
    return axiosUtil.get(`${baseUrl}/common/selectFjb`, params)
}
// 通用接口-代码表查询
// @method getCommonSelectDMB
// @type get
// @return url
//getCommonSelectDMB: `/common/selectDMB`,

// eslint-disable-next-line
export function getCommonSelectDMB(params) {
    return axiosUtil.get(`${baseUrl}/common/selectDMB`, params)
}
// 通用接口-查询当前登录人所在单位
// @method getCommonSelectOrganByUserId
// @type get
// @return url
//getCommonSelectOrganByUserId: `/common/selectOrganByUserId`,

// eslint-disable-next-line
export function getCommonSelectOrganByUserId(params) {
    return axiosUtil.get(`${baseUrl}/common/selectOrganByUserId`, params)
}




