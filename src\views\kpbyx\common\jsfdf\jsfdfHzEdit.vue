<template>
  <div style="width: 100%" v-loading="loading">
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;
      border-bottom: 3px solid #2A96F9;padding-left: 10px;padding-right: 10px;text-align: center;padding-bottom: 2px">
        技术分打分统计确认
      </div>

      <el-table :data="formData.PSXXList" border class="lui-table" :highlight-current-row="true" size="default"
                :cell-style="{ padding: '10px 0 ' }" height="calc(100vh - 580px)">
        <el-table-column type="index" width="50" fixed="left" label="" align="center"/>
        <el-table-column property="PJBZ" header-align="center" align="left" width="100px"
                         label="评委姓名"></el-table-column>
        <el-table-column header-align="center" align="left" label="技术分评分">
          <el-table-column v-for="item in formData.TBRList" :key="item" :property="item.TBBSID"
                           header-align="center" align="center" :label="item.DWMC" width="220">
            <template #default="{row}">
              <span :style="`${row.type!=='PX' ? '' : 'color: red'}`">{{ row[item.TBBSID] }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column property="DFTJ" header-align="center" align="center" label="打分状态" width="100px">
          <template #default="scope">
            <span class="redStyle" v-if="scope.row.type === 'SJ' && scope.row.SFTJ === '1'">已提交</span>
            <span class="deepRedStyle" v-else-if="scope.row.type === 'SJ'">未提交</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" label="详情" width="150">
          <template #default="scope">
            <el-button text v-if="scope.row.type === 'SJ'" @click="viewRow(scope.row, 'PW', '技术分评分详情查看')">查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>


      <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;"
           v-if="editable">
        <el-button size="default" type="primary" @click="saveData('submit')">确认</el-button>
        <el-button size="default" @click="getFormData">刷新</el-button>
      </div>

      <el-button size="default" type="primary" @click="saveData('submit')">测试下一步</el-button>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import comFun from "@lib/comFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: false,
      editable: props.parentForm.JSDF_WCZT !== '1',
      formData: {
        PSXXList: [],
        TBRList: [],
      },
      rules: {}
    })

    const getFormData = () => {

    }

    const saveData = (type) => {
      emit('saveFromData', {JSDF_WCZT: '1', JSDF_WCSJ: comFun.getNowTime(), JSDF_SFGS: '1'}, {})
      state.editable = false
      nextTick(() => {
        emit('nextStep', `已提交确认技术标打分结果`)
      })
    }

    const viewRow = () => {

    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      getFormData,
      saveData,
      viewRow

    }
  }

})
</script>

<style scoped>

</style>
