<!-- 专家信息查询列表 -->
<template>
  <div>
    <div v-if="isRes">
      <el-form :model="listQuery" ref="vForm" class="lui-page" label-position="right" label-width="0" size="default"
        @submit.prevent>
        <el-row :gutter="20" class="lui-search-form">
          <el-col :span="5" class="grid-cell">
            <el-form-item label="" prop="name">
              <el-input v-model="listQuery.XM" type="text" placeholder="请输入姓名" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="grid-cell" v-if="isOrg">
            <el-form-item label="" prop="workAddress">
              <el-input v-model="listQuery.GZDW" type="text" placeholder="请输入工作单位" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="grid-cell">
            <el-form-item label="" prop="major">
              <el-cascader placeholder="请选择申报专业" clearable v-model="listQuery.SBZYList" :options="XCSZYOptions"
                :show-all-levels="false" collapse-tags
                :props="{ expandTrigger: 'hover', label: 'ZYMC', value: 'ZYBM', emitPath: false, multiple: true }" />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="grid-cell" style="display: flex;gap: 10px">
            <div class="ml10 static-content-item">
              <el-button type="primary" @click="queryData">
                <el-icon>
                  <Search />
                </el-icon>
                查询
              </el-button>
            </div>
            <div class="ml10 static-content-item" v-if="isOrg">
              <el-button type="primary" @click="queryDialogVisible = true">
                <el-icon>
                  <Search />
                </el-icon>
                高级查询
              </el-button>
            </div>
            <div class="button_folat static-content-item">
              <el-button type="primary" @click="exportExcel"><el-icon>
                  <Upload />
                </el-icon>导出</el-button>
            </div>
          </el-col>
        </el-row>
        <div class="container-wrapper">
          <el-table ref="dataTable" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
            :style="{ width: '100%' }" :border="true" :show-summary="false" size="default" :stripe="true"
            :highlight-current-row="true" :cell-style="{ padding: '8px 0 ' }">
            <el-table-column type="index" width="50" fixed="left"></el-table-column>
            <el-table-column v-if="true" prop="XM" label="姓名" :fixed="false" align="center"
              :show-overflow-tooltip="true">
              <template #default="scope"><span>{{ scope.row.XM }}</span></template>
            </el-table-column>
            <el-table-column v-if="true" prop="GZDWMC" label="工作单位" :fixed="false" align="center"
              :show-overflow-tooltip="true">
              <template #default="scope"><span>{{ scope.row.GZDWMC }}</span></template>
            </el-table-column>
            <el-table-column v-if="true" prop="ZCMC" label="职称" :fixed="false" align="center"
              :show-overflow-tooltip="true">
              <template #default="scope"><span>{{ scope.row.ZC }}</span></template>
            </el-table-column>
            <el-table-column v-if="true" prop="XCSZYMC" label="现从事专业" :fixed="false" align="center"
              :show-overflow-tooltip="true">
              <template #default="scope"><span>{{ scope.row.XCSZYMC }}</span></template>
            </el-table-column>
            <el-table-column v-if="true" prop="SBZY" label="申报专业" :fixed="false" align="center"
              :show-overflow-tooltip="true">
              <template #default="scope"><span>{{ scope.row.RKZJBXX.SBZY }}</span></template>
            </el-table-column>
            <el-table-column v-if="true" prop="SJH" label="手机号" :fixed="false" align="center"
              :show-overflow-tooltip="true">
              <template #default="scope"><span>{{ scope.row.RKZJBXX.LXDH }}</span></template>
            </el-table-column>
            <el-table-column v-if="true" prop="ZJZT" label="专家状态" :fixed="false" align="center"
              :show-overflow-tooltip="true" min-width="60">
              <template #default="scope">
                <span v-if="scope.row.ZJZT === '1'">在用</span>
                <span v-if="scope.row.ZJZT === '9'">停用</span>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="BLSJ" label="入库时间" :fixed="false" align="center"
              :show-overflow-tooltip="true">
              <template #default="scope"><span>{{ scope.row.CJSJ.substring(0, 10) }}</span></template>
            </el-table-column>
            <el-table-column v-if="true" prop="PBCS" label="评标次数" :fixed="false" align="center"
              :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column fixed="right" align="center" label="操作">
              <template #default="scope">
                <el-button class="lui-table-button" size="small" type="primary" @click="openDialog('view', scope.row)">详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
            :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes" class="lui-pagination"
            @size-change="pageOrSizeChange" @current-change="pageOrSizeChange" :total="total">
          </el-pagination>
        </div>
        <el-dialog custom-class="lui-dialog" v-if="dialogVisible" v-model="dialogVisible" :title="dialogParams.title"
          width="1200px" z-index="1000">
          <zjxxcx-edit :params="dialogParams" @closeDialog="closeDialog" />
        </el-dialog>
        <el-dialog custom-class="lui-dialog" z-index="1000" v-model="queryDialogVisible" title="高级查询条件设置" width="800px"
          class="dialogClass" append-to-body>
          <tcgjcx @executeQuery="executeQuery" />
        </el-dialog>
        <el-dialog custom-class="lui-dialog" z-index="1000" v-model="ZYDialogVisible" title="申报专业选择" width="800px"
          class="dialogClass" top="1vh" append-to-body>
          <zyxz v-if="ZYDialogVisible" :show-z-z-y="false" @close="closeZYDialog" @parentMethod="getCheck"></zyxz>
        </el-dialog>
      </el-form>
    </div>
    <div class="error-page" v-else>
      <div class="error-code" v-if="userLoginName != 'empty'">4<span>0</span>3</div>
      <div class="error-desc" v-if="userLoginName != 'empty'">没有权限访问该页面</div>
      <div class="error-handle" v-if="userLoginName != 'empty'">
        <router-link to="/">
          <el-button type="primary" size="large">返回首页</el-button>
        </router-link>
        <el-button class="error-btn" type="primary" size="large" @click="$router.go(-1)">返回上一页</el-button>
      </div>
      <div class="error-desc" v-if="userLoginName == 'empty'">账号未开通，请联系管理员开通账号！</div>

    </div>
  </div>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
}
  from 'vue'

import Tcgjcx from "../../zjcrk/tcgjcx";
import axiosUtil from "../../../../lib/axiosUtil";
import ZjxxcxEdit from "./zjxxcxEdit";
import Zyxz from "../../zjcrk/common/zyxz";
import comFun from "../../../../lib/comFun";
import { Search, Upload, Plus } from '@element-plus/icons-vue'
import vsAuth from "@lib/vsAuth";
export default defineComponent({
  components: { ZjxxcxEdit, Tcgjcx, Zyxz, Search, Plus, Upload },
  props: {},
  setup() {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 20,
      },
      isRes: true,
      isOrg: true,
      tableData: [],
      rules: {},
      dialogVisible: false,
      ZYDialogVisible: false,
      dialogParams: {
        title: ''
      },
      queryDialogVisible: false,
      total: 0,
      seniorQuery: null,
      XCSZYOptions: [],
    })
    const getZYData = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy', {}).then((res) => {
        state.XCSZYOptions = comFun.treeData(res.data, 'ZYBM', 'FZYBM', 'children', '0')
      });
    }
    const getDataList = (value) => {
      let params = value ? value : state.listQuery
      axiosUtil.post(`/backend/zjgl/zjcxtj/selectZjjbxx?page=${state.listQuery.page ? state.listQuery.page : ''}&size=${state.listQuery.size ? state.listQuery.size : ''}`, params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }
    // const delRowData = (row) => {
    //   ElMessageBox.confirm(
    //       '确定删除该条数据?',
    //       '警告',
    //       {
    //         confirmButtonText: '确定',
    //         cancelButtonText: '取消',
    //         type: 'warning',
    //       }
    //   ).then(() => {
    //     axiosUtil.get('/backend/zjgl/zjrkgl/delRkzj',row).then((res) => {
    //       getDataList()
    //       ElMessage({
    //         message: '删除成功',
    //         customClass: "myMessageClass",
    //         type: 'success',
    //       })
    //     })
    //   })
    // }
    const exportExcel = () => {
      let column = [[
        { field: 'XM', title: '姓名', width: 280, halign: 'center', align: 'left' },
        { field: 'GZDW', title: '工作单位', width: 280, halign: 'center', align: 'left' },
        { field: 'ZCMC', title: '职称', width: 280, halign: 'center', align: 'left' },
        { field: 'XCSZYMC', title: '现从事专业', width: 280, halign: 'center', align: 'left' },
        { field: 'SBZY', title: '申报专业', width: 400, halign: 'center', align: 'left' },
        { field: 'BGDH', title: '手机号', width: 280, halign: 'center', align: 'left' },
        { field: 'ZJZT', title: '专家状态', width: 280, halign: 'center', align: 'left' },
        { field: 'CJSJ', title: '入库时间', width: 280, halign: 'center', align: 'left' },
        { field: 'PBCS', title: '评标次数', width: 280, halign: 'center', align: 'left' },
      ]]
      let finparams = {
        title: '专家信息',
        name: '专家信息',
        params: state.listQuery,
        column: column
      }
      axiosUtil.exportExcel('/backend/commonExport/zjxx', finparams)
    }
    const openDialog = (type, data) => {
      state.dialogParams.type = type
      state.dialogParams.title = '专家信息详情'
      state.dialogParams.id = data.ZJBS
      state.dialogParams.editable = false
      state.dialogVisible = true
    }
    const executeQuery = (value) => {
      if (value) {
        state.seniorQuery = value
        getDataList(value)
      }
      state.queryDialogVisible = false
    }
    const getCheck = (e, ZZY) => {
      let res = []
      let SBZY = []
      e.forEach(item => {
        res.push(item.ZYBM)
        SBZY.push(item.ZYMC)
      })
      state.listQuery.SBZY = SBZY.join(',')
      state.listQuery.SBZYList = res
      state.ZYDialogVisible = false

    }
    const pageOrSizeChange = () => {
      getDataList(state.seniorQuery)
    }
    const closeDialog = () => {
      state.dialogVisible = false
      getDataList(state.seniorQuery)
    }
    const closeZYDialog = () => {
      state.ZYDialogVisible = false
    }
    const queryData = () => {
      state.seniorQuery = null
      getDataList()
    }
    const queryRoleRes = () =>{
      let params = {
        loginName: vsAuth.getAuthInfo().permission.userLoginName,
        orgCode: '31400006', // 企管法规部
        resCode: 'ZJXXCX' // 专家信息查询
      }
      axiosUtil.get('/backend/zjgl/queryOrgRes', params).then((res) => {
        if(res.data){
          state.isOrg = res.data.isOrg
          state.isRes = res.data.isRes
          if(state.isRes){
            state.listQuery.GZDW = res.data.orgTwoName
            getDataList()
          }
        }
        
      });
    }
    onMounted(() => {
      queryRoleRes()
      getZYData()
    })
    return {
      ...toRefs(state),
      openDialog,
      closeDialog,
      getDataList,
      executeQuery,
      pageOrSizeChange,
      queryData,
      exportExcel,
      getCheck,
      closeZYDialog,
      queryRoleRes
    }
  }
})

</script>

<style scoped>
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: white;
}

.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #f3f3f3;
  box-sizing: border-box;
}

.error-code {
  line-height: 1;
  font-size: 250px;
  font-weight: bolder;
  color: #f02d2d;
}

.error-code span {
  color: #00a854;
}

.error-desc {
  font-size: 30px;
  color: #777;
}

.error-handle {
  margin-top: 30px;
  padding-bottom: 200px;
}

.error-btn {
  margin-left: 100px;
}
</style>
