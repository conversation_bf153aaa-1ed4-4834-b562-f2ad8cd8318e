<template>
  <el-form :model="formData" ref="vForm" :rules="rules" label-position="right" label-width="0" class="lui-page"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="name">
          <el-input v-model="listQuery.XM" type="text" placeholder="请输入姓名" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="workAddress">
          <el-input v-model="listQuery.GZDW" type="text" placeholder="请输入工作单位" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="major">
          <el-cascader
              placeholder="请选择申报专业"
              clearable
              v-model="listQuery.SBZYList"
              :options="XCSZYOptions"
              :show-all-levels="false"
              collapse-tags
              :props="{expandTrigger: 'hover',label:'ZYMC',value:'ZYBM',emitPath:false,multiple:true}"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell" style="display: flex;gap: 10px;text-align: right;">

        <el-button type="primary" @click="getDataList">
          <el-icon>
            <Search/>
          </el-icon>
          查询
        </el-button>
        <el-button type="primary" @click="queryDialogVisible=true">
          <el-icon>
            <Search/>
          </el-icon>
          高级查询
        </el-button>
        <el-button type="primary" @click="exportExcel">
          <el-icon>
            <Upload/>
          </el-icon>
          导出
        </el-button>

      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table ref="dataTable" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column type="index" width="50" fixed="left"></el-table-column>
        <el-table-column prop="XM" label="姓名" align="center" :show-overflow-tooltip="true">

        </el-table-column>
        <el-table-column prop="GZDWMC" label="工作单位" align="center" :show-overflow-tooltip="true">

        </el-table-column>
        <el-table-column prop="ZC" label="职称" align="center" :show-overflow-tooltip="true">

        </el-table-column>
        <el-table-column prop="XCSZYMC" label="现从事专业" align="center" :show-overflow-tooltip="true">

        </el-table-column>
        <el-table-column prop="RKZJBXX.SBZY" label="申报专业" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="RKZJBXX.LXDH" label="手机号" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column fixed="right" class-name="data-table-buttons-column" align="center" label="操作"
                         :width="300">
          <template #default="scope">
            <div v-if="scope.row['SHZT']==0">
              <el-button class="lui-table-button" size="small" @click="openDialog(scope.row,'editInfo','信息编辑')">信息编辑
              </el-button>
<!--              <el-button class="lui-table-button" size="small" @click="openDialog(scope.row,'editStatu','状态编辑')">状态编辑-->
<!--              </el-button>-->
<!--              <el-button class="lui-table-button" size="small" @click="openDialog(scope.row,'editProf','专业编辑')">专业编辑-->
<!--              </el-button>-->
            </div>
            <div v-else-if="scope.row['SHZT']==null || scope.row['SHZT']==2 ">
              <el-button class="lui-table-button" size="small" @click="openDialog(scope.row,'addInfo','信息变更')">信息变更
              </el-button>
<!--              <el-button class="lui-table-button" size="small" @click="openDialog(scope.row,'addStatu','状态变更')">状态变更-->
<!--              </el-button>-->
<!--              <el-button class="lui-table-button" size="small" @click="openDialog(scope.row,'addProf','专业变更')">专业变更-->
<!--              </el-button>-->
            </div>
            <!-- <el-button type="text" size="small"
             @click="openDialog(scope.row)">信息变更</el-button>
            <el-button type="text" size="small"
              :class="['data-table-ztedit-button']" @click="openDialog(scope.row)">状态变更</el-button>
            <el-button type="text" size="small"
              :class="['data-table-zyedit-button']" @click="openDialog(scope.row)">专业调整</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                     :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes" class="lui-pagination"
                     @size-change="getDataList" @current-change="getDataList" :total="total">
      </el-pagination>
    </div>
  </el-form>
  <el-dialog
      custom-class="lui-dialog"
      v-if="dialogVisible"
      v-model="dialogVisible"
      :title="BGTitle"
      @closed="closeForm"
      top="2vh"
      z-index="1000"
      width="1200px">
    <div>
      <zjxxbgForm :key="params.ZJBS" :params="params" :kzxxParams="kzxxParams" :llxxParams="llxxParams"
                  @closeForm="closeForm"/>
    </div>
  </el-dialog>
  <el-dialog z-index="999" v-model="queryDialogVisible" title="高级查询条件设置" width="800px" class="dialogClass"
             append-to-body custom-class="lui-dialog">
    <tcgjcx @executeQuery="executeQuery"/>
  </el-dialog>
  <el-dialog z-index="1000" v-model="ZYDialogVisible" title="申报专业选择" width="800px" class="dialogClass" top="1vh"
             append-to-body custom-class="lui-dialog">
    <zyxz v-if="ZYDialogVisible" :show-z-z-y="false" @close="closeZYDialog" @parentMethod="getCheck"></zyxz>
  </el-dialog>

</template>

<script>
import {
  defineComponent,
  ref,
  shallowRef,
  toRefs,
  reactive,
  getCurrentInstance,
  watchEffect, onMounted
}
  from 'vue'
// 请求方法 get,post,put,del,downloadFile
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "../../../lib/comFun";
import zjxxbgForm from "./zjxxbgEdit.vue"
import Tcgjcx from "./tcgjcx";
import Zyxz from "@src/views/zjgl/zjcrk/common/zyxz";
import {Search, Upload, Plus} from '@element-plus/icons-vue'


export default defineComponent({
  components: {zjxxbgForm, Tcgjcx, Zyxz, Search, Plus, Upload},
  props: {},
  setup() {
    const state = reactive({
      listQuery: {
        XM: null,
        GZDW: null,
        SBZY: null,
        page: 1,
        size: 20,
      },
      seniorQuery: {},//高级查询对象
      total: null,
      rules: {},
      BGTitle: '',
      dialogVisible: false,
      queryDialogVisible: false,
      ZYDialogVisible: false,//专业选择按钮
      params: null,
      kzxxParams: {},
      llxxParams: {},
      tableData: [],
      XCSZYOptions: [],
    })
    const getZYData = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy', {}).then((res) => {
        state.XCSZYOptions = comFun.treeData(res.data, 'ZYBM', 'FZYBM', 'children', '0')
      });
    }
    const methods = {}
    const instance = getCurrentInstance()
    const submitForm = () => {
      instance.proxy.$refs['vForm'].validate(valid => {
        if (!valid) return
        //TODO: 提交表单
      })
    }
    const resetForm = () => {
      instance.proxy.$refs['vForm'].resetFields()
    }

    const getDataList = () => {
      axiosUtil.post('/backend/zjgl/zjbggl/selectZjbgPage', state.listQuery).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const getDataListHigh = () => {
      axiosUtil.post('/backend/zjgl/zjbggl/selectZjbgPage', state.seniorQuery).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const exportExcel = () => {
      let column = [[
        {field: 'XM', title: '姓名', width: 280, halign: 'center', align: 'left'},
        {field: 'GZDW', title: '工作单位', width: 280, halign: 'center', align: 'left'},
        {field: 'ZCMC', title: '职称', width: 280, halign: 'center', align: 'left'},
        {field: 'XCSZYMC', title: '现从事专业', width: 280, halign: 'center', align: 'left'},
        {field: 'SBZY', title: '申报专业', width: 400, halign: 'center', align: 'left'},
        {field: 'BGDH', title: '手机号', width: 280, halign: 'center', align: 'left'},
      ]]
      let finparams = {
        title: '专家变更信息',
        name: '专家变更信息',
        params: state.listQuery,
        column: column
      }
      axiosUtil.exportExcel('/backend/commonExport/zjbgxx', finparams)
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }
    const openDialog = (value, type, title) => {
      /*  if(value){
           state.params = {editable:true,id:value.GLZJID,operation:'edit'}
       }else{
           state.params = {editable:true,id:comFun.newId(),operation:'add'}
       } */
      if (type.indexOf('add') > -1) {
        state.params = {editable: true, id: comFun.newId(), operation: type, ZJBS: value.ZJBS}
      } else {
        state.params = {editable: true, id: value.GLZJMXID, operation: type, ZJBS: value.ZJBS}
      }
      getPageType(type)
      state.BGTitle = '专家' + title
      state.dialogVisible = true
    }

    //挂载时，加载页面来源
    const getPageType = (type) => {
      if (type.indexOf('Info') > -1) {
        state.params.pageType = 'info'
      } else if (type.indexOf('Statu') > -1) {
        state.params.pageType = 'statu'
      } else if (type.indexOf('Prof') > -1) {
        state.params.pageType = 'prof'
      }

      state.llxxParams = {...state.params}
      state.kzxxParams = {...state.params}
      state.llxxParams.editable = state.kzxxParams.editable = state.params.pageType == 'info' && state.params.editable
      state.kzxxParams.id = state.params.ZJBS ? state.params.ZJBS : state.params.id
      state.llxxParams.id = state.params.id
    }

    const executeQuery = (value) => {
      if (value) {
        state.seniorQuery = value
        getDataListHigh()
      }
      state.queryDialogVisible = false
      state.seniorQuery = null
    }

    const closeZYDialog = () => {
      state.ZYDialogVisible = false
    }

    const getCheck = (e, ZZY) => {
      let res = []
      let SBZY = []
      e.forEach(item => {
        res.push(item.ZYBM)
        SBZY.push(item.ZYMC)
      })
      state.listQuery.SBZY = SBZY.join(',')
      state.listQuery.SBZYList = res
      state.ZYDialogVisible = false

    }

    onMounted(() => {
      getDataList()
      getZYData()
    })
    return {
      ...toRefs(state),
      ...methods,
      submitForm,
      resetForm,
      getDataList,
      openDialog,
      closeForm,
      exportExcel,
      closeZYDialog,
      getCheck,
      executeQuery
    }
  }
})

</script>

<style scoped>

.grid-cell .el-input {
  max-width: 250px;
}


/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: white;
}
</style>
