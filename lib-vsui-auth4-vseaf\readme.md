<h1 align="center">@vsui/lib-vsui-auth4-vseaf</h1>

### **胜软vsui.vue前端鉴权组件** (支持 VSUI.VUE 2.1.0)

##### 作者：崔良

##### @vsui/lib-vsui-auth4-vseaf 是对鉴权功能的封装，对应公司的vseaf框架，支持SECURITY、SIAM、CAS登录。实用性强，使用简单。

##### 版本说明:

因前端框架VSUI与后端框架VSEAF的升级难以同步，导致负责链接双方认证的本组件也难以对齐某一方的版本，且鉴权组件自身具有独立的且较宽泛的适用范围，所以前端鉴权组件形成脱离前后端框架版本的独立版本体系。

##### **适配性：**

前端框架版本: vsui.vue@2.1.0-rc4及其后升级的框架版本

后端框架版本：vseaf未来升级的版本及4.4.2版本

##### **使用注意:** 

请区别于早期的鉴权组件：[@vsui/lib-vueauth4vseaf](http://10.68.7.155:7002/package/@vsui/lib-vueauth4vseaf)

如有某些前后端不适应性，本组件会升级适配，请查看其适配性范围进行使用或咨询框架组前后端框架负责人



[ **[在线演示](http://10.68.8.30:30669/)]



此组件集成了vseaf框架的认证鉴权与资源授权验证功能，支持CAS、SIAM、SECURITY模式，为快速开发仍保留了在@vsui/lib-vueauth4vseaf组件中实现的DEV模式


### 此组件优势如下

* 1：使用方便：初始化语句简单

* 2：自动化注入路由拦截，根据路由安全配置方案自动匹配路由定义启用相应的鉴权方式

* 3：支持目前公司业务线常见的客户鉴权方式，支持CAS、SIAM、SECURITY模式

* 4：提供DEV鉴权模式，为开发过程提供更简单和便于维护的用户权限支撑信息

* 5：鉴权方式可以快速切换不同模式，配合vsui.vue@2版本可快速的应对生产环境的变化与应用复制部署




## 使用指南

全面介绍前后端配置方式

### 1.vseaf@4.4.2后端配置内容如下：

详细文档请参考：[研发管理平台](http://10.68.199.18:9008/) - 在线文档 - vseaf4.4.2框架文档 - 认证鉴权 - 后端鉴权 - 鉴权配置

如有区别，请按照研发管理平台的说明配置。

#### 1.1.修改pom文件，找到文件：vseaf4.4-template-starter/pom.xml

##### 1.1.1.通用配置

引入权限中心的rest接口sdk开发包：
```xml
	<dependency>
		<groupId>cn.com.victorysoft</groupId>
		<artifactId>vseaf-spring-boot-security-authm-client</artifactId>
	</dependency>
```

##### 1.1.2.个性化配置

security（form）模式：
```xml
	<dependency>
		<groupId>cn.com.victorysoft</groupId>
		<artifactId>vseaf-spring-boot-starter-security</artifactId>
	</dependency>
```
		
cas模式：
```xml
	<dependency>
		<groupId>cn.com.victorysoft</groupId>
		<artifactId>vseaf-spring-boot-starter-security-cas</artifactId>
	</dependency>
```
		
siam模式：
```xml
	<dependency>
		<groupId>cn.com.victorysoft</groupId>
		<artifactId>vseaf-spring-boot-starter-security-siam</artifactId>
	</dependency>
```
	
#### 1.2.修改application.properties文件，找到文件：vseaf4.4-template-starter/src/main/resource/application.properties

##### 1.2.1.通用配置 

无论何种模式都需要增加应用编号：
```xml
	app.code=					#权限中心中应用编号
```

##### 1.2.2.个性化配置 

security模式：
```xml
	vseaf.security.authm.url=			#权限中心的rest接口服务地址，如：http://localhost:8848/authm-rest
```
cas模式：
```xml
	vseaf.security.authm.url=			#权限中心的rest接口服务地址，如：http://localhost:8848/authm-rest
	vseaf.security.cas.cas-server-url-prefix=	#CAS地址，如：http://***********:8888/cas
```
siam模式：
```xml
	vseaf.security.authm.url=			#权限中心的rest接口服务地址，如：http://localhost:8848/authm-rest
	vseaf.security.siam.server-name=		#接收siam应答的前端地址，如：http://appmonitor.slof.com/vseaf-service
```
			

### 2.前端配置方式：

<red>前端不再配置登陆模式，而是通过识别后端引入的包从而获知登陆方式，这是一种自动化的方式，您只需要关心在pom中使用了哪个鉴权包</red>

#### 2.1.安装并启用鉴权组件：@vsui/lib-vsui-auth4-vseaf

##### 2.1.1.安装 @vsui/lib-vsui-auth4-vseaf 组件

```javascript
	npm i @vsui/lib-vsui-auth4-vseaf@1.0.0 -S
```

##### 2.1.2.引入 @vsui/lib-vsui-auth4-vseaf 组件

```javascript
	import VSAuth from '@vsui/lib-vsui-auth4-vseaf';
```

vsui.vue@2.1.0版本的src\lib\vsAuth.js中默认引入了该组件，并提供了一个扩展示例，具体请参见代码

##### 2.1.3.初始化@vsui/lib-vsui-auth4-vseaf组件

```javascript

	VSAuth.init({
		router,//路由定义,鉴权组件会向路由定义中自动注入路由拦截
		store,//状态服务，登录过程会将信息写入vuex中
		axios,//框架使用axios作为异步请求库，会拦截请求中的未认证信息进行处理，如使用其他AJAX库，则无法触发拦截验证机制
		whereVSEAF:"/vseaf-service",//vseaf服务端基地址在前端的统一基地址名称，用于便于代理统一处理
		isDev:false,//在DEV模式下，用户信息来源为目录：/apidata/auth/login
		DEBUG:true,//调试模式下会输出大量处理过程日志
		//以下节点默认情况下无需打开
		//customCfg:"vsuiauth-detail",//声明自定义处理节点名称，避免key值冲突
		//"vsuiauth-detail":customOpt//使用自定义处理用户获取用户信息
	})

```
tips："whereVSEAF"属性会作用于前端向后端发出的调用请求地址前缀、页面跳转地址前缀中，这样做的目的是通过一个统一的名称前端与认证相关的请求与后端的认证接口代理（转接）起来，无需将所有地址枚举出来逐一代理（转接）

vsui.vue@2.1.0版本的src\lib\vsAuth.js中默认引入了该组件，并提供了一个扩展示例，具体请参见代码

##### 2.1.4.开发模式的配置代理

 vsui.vue@2.1.0版本的build\dev\dev.proxy.auth.js文件已经提前定义好了针对此4种登陆模式的代理，请在build\dev\dev.proxy.js中按需要打开注释。
 
 tips：该配置视后端运行地址为：http://localhost:8888 ;前端运行地址为：http://localhost:8088 ;
 				

##### 2.1.5.配置build\dev\dev.server.option.custom.js的域名端口信息

security、cas模式：

```javascript
host: "localhost",
port: 8088,
```


siam模式下：

该模式需要使用域名测试，假设域名为mdm.slof.com

本地host文件中需要增加

```
127.0.0.1	mdm.slof.com
```

```javascript
host: "mdm.slof.com",
port: 80,
```



### 3.前端使用方式


#### 3.1.获取鉴权用户等相关信息 VSAuth.getAuthInfo()

VSAuth.getAuthInfo()函数定义如下

```javascript

	VSAuth.getAuthInfo(){
		return
		{
			isLogined:boolean,//当前鉴权组件是否登录成功：true或false
			userName:String||"",//当前登录用户的用户名，如"cuiliang"
			permission:Object,//用户的权限完整信息
			modulesTree:[],//用户资源转换为树状菜单后的数组
			token:String,//保留值
			auth：{
				toLoginPage：function(),//跳转入登陆页面（同步函数）适用于SECURITY,SIMA,CAS模式，以及日后加入的OAuth2
				login:Promise(),//登录过程，返回Promise对象，适用于SECURITY，DEV模式
				logout:Promise(),//退出过程，返回Promise对象，适用于SECURITY,SIMA,CAS,DEV全系列模式
			},
			authMode:"DEV||SIAM||CAS||SECURITY",//后端告诉前端的认证模式字符串
            storeName:"vsui-auth4-[hash]",//用户信息存储的store名称，以"vsui-auth4-"开头，后跟一个随机的hash值
		}
	}
```

#### 3.2.获取鉴权定义信息 VSAuth.getAuthDefine()

VSAuth.getAuthDefine()函数定义如下

```javascript

	//登录退出过程为了更好的支持异步操作，均返回Promise类型，请在外部使用login().then(()=>{}).catch((err)=>{})的方式处理

	unknown:
	{
		login():Promise,//触发异常，请在外面使用.catch(err=>{})捕获
		logout():Promise,//触发异常，请在外面使用.catch(err=>{})捕获
	},
	CAS:
	{
		toLoginPage():null,
		logout():Promise,
	},
	SIAM:
	{
		toLoginPage():null,
		logout():Promise,
	},
	SECURITY:
	{
		toLoginPage():null,
		login(userName,passWord):Promise,
		logout():Promise,
	},
	DEV:
	{
		toLoginPage():null,
		login(userName, passWord):Promise,
		logout():Promise,
	}

```

#### 3.3.DEV模式的使用

* isDev:true

DEV模式的用户信息来源为文件：/apidata/auth/login，您可修改此文件内的用户权限来快速搭建开发环境

#### 3.4.路由鉴权的配置

* 鉴权组件不仅提供了对各种登录鉴权方式，还对路由进行拦截与鉴权，鉴权方式支持三种：

  例如：以下是路由定义
 ```javascript
   {
          path: "路由地址",
          name: "路由名称",
          component:()=> import("文件地址"),
          
          meta: { title: "页面标题", permission: 鉴权 }
    },
```
   请注意permission鉴权，鉴权方式可以为：
   
##### 3.4.1.自定义函数

这是一个鉴权过程的回调函数，由应用层实现，鉴权组件会自动调用

参数为：
   to 要跳到的路由地址
   userName 当前用户用户名
   userPermission 用户拥有的权限信息
   
###### *******:使用src\router\router.define.js中定义的函数checkLogined，函数定义如下：
```javascript
	   
	function checkLogined(to,userName,userPermission){
		return VSAuth.getAuthInfo().isLogined
	}
	   
```
	   
路由定义中meta: { title: "", permission: checkLogined }
	   
###### *******:自定义鉴权过程，

您可以定义如下meta对象：

```javascript
	meta: { title: "", permission: (to,userName,userPermission)=>{
		//你可以使用传入的参数来实现自己的鉴权过程
		//return 鉴权结果,布尔值
	} }
 ```
##### 3.4.2.Boolean值

您可以定义如下meta对象：
```javascript
	meta: { title: "", permission: boolean }
 ```
   
       a:true 需要鉴权，鉴权过程首先验证用户是否登陆，然后验证前往的地址是否在用户可用的资源列表中
       b:false 无需鉴权，不做任何权限验证，不验证用户是否登陆，也不验证地址可用性
	   
##### 3.4.3.array值
 
 您可以定义如下meta对象：
```javascript
	meta: { title: "", permission: ["resId1","resId2",..."resIdN"] }
 ``` 
 
	   a:明确指定用户拥有某些资源值（资源间为或关系）时可以访问此地址，指定访问路由所拥有的任一资源值列表B，验证用户所拥有资源A中是否有资源C存在于列表B中,且用户的资源地址是使用此路由地址解析的
          为了处理两个模块使用同一个路由（动态路由），路由匹配的前提下还需要验证用户拥有的权限是哪个模块的
       b:空数组[],无权访问
	   
   
##### 3.4.4.无需鉴权的情况

 如下meta对象无需鉴权：
```javascript
	meta: { title: "", permission: false }
 ```
 ```javascript
	meta: { title: "" }
 ```
 ```javascript
	meta: { title: "", permission: ()=>{return true;} }
 ```

 
### 4.组件高阶使用之扩展

#### 4.1.扩展支持的模式

您可以对接自己的登陆模式，使用VSAuth.getAuthDefine()函数将您的函数过程注入其中，如：
```javascript

	VSAuth.getAuthDefine().CUSTOM=
	{
		toLoginPage():null,
		login(userName, passWord):Promise,
		logout():Promise,
	}

```

注意：后端需要返回鉴权模式应为"CUSTOM"

#### 4.2.拦截器的使用

#### 4.2.1.router拦截器

路由拦截器会自动注入到路由中，并自动拦截用户对路由的权限访问，

如用户已登陆但无权限则自动调入403页面，

如用户未登录，则拦截器自动跳入相应的登录模式登录页

#### 4.2.2.Axios拦截器

Axios拦截器会自动注入到Axios的默认静态实例对象中，并自动拦截服务器端返回的用户登陆状态

如服务器端会话丢失，在使用Axios发出请求后会拦截到用户未登录的状态，跳入相应的登录模式登录页

#### 4.2.3.拦截器机制介绍

路由拦截器与Axios拦截器均是数组，允许注入多个拦截器，本例不再对路由拦截器与Axios拦截器多做介绍，如需了解更多，请翻阅官方文档。

本组件的拦截器是遵从路由拦截器与Axiosl拦截器允许的机制实现，在vsui.vue框架中，其注入层级不在最外层，也不再最内层，这就使得允许用户在组件拦截器内外层进行扩展，可以控制此组件拦截器的工作机制

### 5.组件高阶使用之自定义：

#### 5.1.使用自定义配置

该组件支持在配置中，通过使用"customCfg"属性定义自定义配置节点名称，如：
```script
	customCfg:"vsuiauth-detail",
	"vsuiauth-detail":customOpt
```


#### 5.2.使用自定义过程获取用户信息

能否从后端获取到用户信息意味着前端判定用户登陆与否，因此，这是需要鉴权的地址会使用到的重要接口

通过5.1的配置，使用customOpt的配置信息，customOpt可以定义如下代码，启用自定义获取用户信息的过程：
```
{
	/**
	* 向后端请求用户信息
	* @returns Promise 返回一个异步对象
	**/
	getUserInfo:Promise
}
```
以上代码存在于vsui.vue@2.1.0的src\lib\vsAuth.customOpt.js文件中。

请在Promise的resolve调用中，返回如下结构的数据
```
	{
        userName:"登录账号,即用户名,不可留空",
        realName:"用户真实名称，如：张三"
        passwd:"用户密码,为安全考虑,可留空",
        token:"无状态模式下的凭据信息,可留空",
        permission:保存后端返回的完整用户信息,不可留空,数据结构见下方,
        modulesTree:用户菜单资源数组,请转换后按照固定结构传入,不可留空,数据结构见下方
    }
```
permission的数据结构如下：
```
	{
		"resList":[
			{
				"resId": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
				"resName": "Dashboard",
				"resPid": "0",
				"iconClass": "fa fa-tachometer",
				"resPath": "/dashboard",
				...资源其他属性
			},
			{
				"resId": "9B45ADRVGSD45GDED4FTTWSSW128JHFA",
				"resPid": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
				"resName": "控制台",
				"iconClass": "fa fa-podcast",
				"resPath": "/dashboard/workplace",
				...资源其他属性
			},
			...其他资源
		]
		...资源其他属性
	},
```
modulesTree的数据结构如下：
```
	{
		"resId": "9B45ADRVGSD45GDED4FTTWSSW128JHFA",
		"resPid": "9B45ADRVGSD45GDEBD46C3791GTBSE45",
		"resName": "控制台",
		"iconClass": "fa fa-podcast",
		"resPath": "/dashboard",
		"children":[
			{
				"resId": "D54FFDFC3791GTBSE45FDWEF7SFA",
				"resName": "Dashboard",
				"iconClass": "fa fa-bus",
				"resPath": "/dashboard",
				"resPid": "9B45ADRVGSD45GDED4FTTWSSW128JHFA",
				...其他属性
			},
			...其他菜单
		]
		...其他属性
	},
```

如果未定义getUserInfo，将使用vsui.vue与vseaf约定的默认接口获取用户信息。

tips:如果您使用自定义过程获取用户信息，意味着您可以更自主的决定如何获取用户信息，如何转换用户数据，如何自定义存储用户信息等过程，只不过在异步过程的最后，将认证组件需要的用户信息传入即可。

tips:前端鉴权组件获取用户的接口与数据结构已在发布本组件时约定好，但因后端升级滞后，因此，src\lib\vsAuth.customOpt.js实现的是对接vseaf@4.4.2的方法，待vseaf发布新版后，除特殊情况需要使用自定义完成外，大部分情况可使用默认实现。


### 6.部署配置

部署配置依赖于Nginx的反向代理机制，这是通过统一代理了组件初始化中定义的名为"whereVSEAF"的值来实现的,

代理的匹配地址为认证组件中配置项"whereVSEAF"所指向的地址，proxy_pass为后端框架的运行地址

	location ^~ /vseaf-service/ {
		proxy_pass  http://127.0.0.1:8081;
		#proxy_cookie_path / /vseaf-service;
		proxy_set_header Host $host:$server_port;
		proxy_set_header Page-Uri $request_uri;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header REMOTE-HOST $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	}

 
## 如有问题请反馈至[ 济南研发中心框架组-崔良 ]

