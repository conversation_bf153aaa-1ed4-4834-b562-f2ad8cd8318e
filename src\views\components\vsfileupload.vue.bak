<template>
  <div>
    <el-upload
        name="files"
        class="upload-demo"
        :action="uploadUrl"
        :headers="headers"
        :multiple="multiple"
        :accept="accept"
        :with-credentials="true"
        :data="fileUploadData"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :on-success="onSuccess"
        :before-upload="beforeUpload"
        :limit="limit"
        :disabled="!editable"
        :on-exceed="handleExceed"
        :file-list="fileList">
      <el-button size="small" type="primary" v-if="editable">上传</el-button>
      <div slot="tip" class="el-upload__tip" v-if="editable&&showTip">
        文件只支持{{ accept }}，单个文件最大不超过{{ maxSize }}M
      </div>
    </el-upload>
    <el-dialog
            v-if="dialogVisible"
            v-model="dialogVisible"
            z-index="1000"
            top="1vh"
            :title="tilte"
            width="80%">
      <div>
        <div>
          <!-- <vue-office-pdf :src="filePath" @rendered="rendered"/> -->
        </div>
      </div>
    </el-dialog>

  </div>
</template>
<script>


import {defineComponent, onMounted, reactive, toRefs} from "vue";
//import { preview } from 'vue3-preview-image' // 使用setup组合式api时引入方法调用
import axiosUtil from "../../lib/axiosUtil"
import axios from "axios";
//import VueOfficePdf from '@vue-office/pdf'

export default defineComponent({
  components: { },
      props: {
        callBackFun: {  //上传前的回调函数
          type: Function,
          default: null
        },
        callBackParam: {
          type: Object,
          default: null
        },
        busId: { //附件业务ID
          type: String,
          required: true,
        },
        busType: {  //后台配置文件中的业务类型
          type: String,
          default: 'zysc'
        },
        ywlb: {  //业务类别，用于标识用同一id有不同业务类型的附件
          type: String,
          default: 'default'
        },
        multiple: { //支持文件多选，默认支持
          type: Boolean,
          default: true,
        },
        accept: {
          type:String,
          default:'.pdf,.PDF,.jpj,.jpej,.png,.PNG,.JPG,.JPEG,.gif,.GIF'
        }, //允许上传文件类型
        limit: {       //允许上传的文件个数
          type: Number,
          default: 10
        },
        // maxSize:{      //允许上传的文件大小
        //   type:Number,
        //   default:100
        // },
        editable: { //是否可编辑，默认可编辑
          type: Boolean,
          default: true
        },
        showTip: { //是否显示提示文字
          type: Boolean,
          default: false
        },
        files: { //是否显示提示文字
          type: Array,
          default: []
        }
      },

      setup(props, {emit}) {
        const state = reactive({
          maxSize: 5000000,
          model: 'fileUpload',
          uploadUrl: '/backend/minio/upload',
          //附件列表
          fileList: [],
          headers: {Authorization: "Bearer " + 'getToken()'},
          beforeRemoveMessage: true,
          //busType 对应配置文件中的业务类型
          //busId 业务ID
          fileUploadData: {
            busType: 'dwxx',
            busId: props.busId,
            standbyField0: props.ywlb
          },
          dialogVisible: false,
          filePath:"",
          Dialog:false,
          fileType:"",
          tilte:""
        })
        const handleRemove = (file) => {
          axios.post('/backend/minio/del', {id: file.id, delFlag: "1"}).then((res) => {
            console.log(res)
          })
        }
        const onSuccess = (res) => {
          loadFileList()
        }
        const loadFileList = () => {
          if (props.busId) {
            axios.post('/backend/minio/list', {busId: props.busId, standbyField0: props.ywlb}).then((res) => {
              let fileList = [];
              console.log(res)
              for (let i = 0; i < res.data.data.length; i++) {
                let file = res.data.data[i];
                fileList.push({
                  name: file.fileName,
                  id: file.id,
                  operationId: file.operationId,
                  mongoDBId: file.downloadId,
                  type: file.fileType,
                });
              }
              emit('update:files',fileList)
              state.fileList = fileList
            })
          }
        }
        const handlePreview = (file) => {

          state.filePath = "/backend/common/fileupload/download?id=" + file.id;
          state.fileType = file.type;
          // state.dialogVisible = true;
          state.tilte = file.name+'预览';
          if (state.fileType == '.png' || state.fileType == '.jpg' || state.fileType == '.jpeg' || state.fileType == '.gif'){
            preview(state.filePath)
          }else{
            state.dialogVisible = true;
          }

          //window.open('/backend/common/fileupload/download' + "?id=" + file.id);
        }
        const beforeUpload = (file) => {
          state.fileUploadData.busId=props.busId
          return true
          const isLt2M = file.size / 1024 / 1024 < this.maxSize;
          // if (!isLt2M) {
          //   this.beforeRemoveMessage = false;
          //   this.$message({
          //     message: "上传文件大小不能超过" + this.maxSize + "MB!",
          //     type: "warning",
          //   });
          //   return false;
          // }
          // if (this.callBackFun) {
          //   this.callBackFun(this.callBackParam);
          // }
        }
        const handleExceed = (files, fileList) => {
          // this.$message.warning(
          //     `当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          //         files.length + fileList.length
          //     } 个文件`
          // );
        }
        //删除触发执行
        const beforeRemove = (file, fileList) => {
          // if (this.beforeRemoveMessage) {
          //   return this.$confirm("确定删除 " + file.name + "？");
          // } else {
          //   return true;
          // }
        }
        onMounted(()=>{
          loadFileList()
        })

        return {
          ...toRefs(state),
          handleRemove,
          onSuccess,
          loadFileList,
          beforeUpload,
          handleExceed,
          handlePreview,
          beforeRemove
        }
      },
    }
)
</script>

<style scoped>
* >>> .readonlyClass .el-upload {
  display: block !important;
}

* >>> .readonlyClass .el-upload-list__item:first-child {
  margin-top: 0px !important;
}
</style>
