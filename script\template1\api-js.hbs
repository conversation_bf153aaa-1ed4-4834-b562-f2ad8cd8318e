import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"
{{!-- import {getTempStr} from '@/utils/tempUtil' --}}


{{#each data}}
// {{description}}
// @method {{function}}
// @type {{method}}
// @return url
//{{function}}: `{{url}}`,

// eslint-disable-next-line
{{!-- {{function}}: (params) => `{{url}}`, --}}
export function {{function}}(params{{#if isExport}},fileName{{/if}}) {
    return axiosUtil.{{method}}(`${baseUrl}{{url}}`, params{{#if isExport}},fileName{{/if}})
}
{{/each}}




