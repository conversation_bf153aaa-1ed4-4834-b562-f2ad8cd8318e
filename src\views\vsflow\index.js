/**
 * 流程API
 */
import axios from 'axios';

export default {
    // 流程引擎地址(正式)
    wfmethod: "http://localhost:8888/vsflow/rest/workflow/",
    // 流程引擎地址(测试)
    //wfmethod : "http://*************:8888/vsflow/rest/workflow/",

    // 创建任务
    startUrl: "/vsflow/rest/workflow/start",
    // 完成任务
    completeUrl: "/vsflow/rest/workflow/complete",
    // 获取待办/已办
    getTasksListUrl: "/backend/wfExtension/getTaskList",
    // 撤回任务
    repealTaskUrl: "/vsflow/rest/workflow/repealedTask",
    // 改派任务
    reassignTaskUrl: "/vsflow/rest/workflow/reassignTask",
    // 删除流程实例
    deleteProcessInstanceUrl: "/vsflow/rest/workflow/deleteProcessInstance",
    // 终止流程
    terminatedProcessInstanceURL: "/vsflow/rest/workflow/terminatedProcessInstance",
    // 跟踪
    getTasksMonitorUrl: "/backend/wfExtension/getTasksMonitor",
    // 获取流程实例变量
    getProcessInsUrl: "/vsflow/rest/workflow/getVarData",
    // 获取流程id
    getProcessIdUrl: "/backend/wfExtension/getProcessIdByInstanceId",

    //应用code
    appId: 'AUTHM',

    //流程信息
    processData: {
        // 西北承包商状态变更流程
        xbcbsztbg: {
            processId: '17430789133879342',
            processName: '西北承包商状态变更流程',
            processEditUrl: 'cbs/xbCbsrcgl/ztbg/ztbgEdit',
            processViewUrl: 'cbs/xbCbsrcgl/ztbg/ztbgView',
        },

        // 西北承包商队伍专业状态变更流程
        xbcbszyztbg: {
            processId: '17430787146467376',
            processName: '西北承包商队伍专业状态变更流程',
            processEditUrl: 'cbs/xbCbsrcgl/zyztbg/zyztbgEdit',
            processViewUrl: 'cbs/xbCbsrcgl/zyztbg/zyztbgView',
        }

    },

    //所有流程信息 ，（待办循环流程信息使用，若无配置，待办信息无法穿透）
    processArr: [
        'xmjhsb',//项目计划上报
        'cgwxsq',//采购外协申请
        'xmjbxxbg',//项目基本信息变更
        'xmyqsq',//项目延期申请
        'xmzzsq',//项目终止流程
        'xmzlsq',//项目专利
        'xbcbsztbg', // 西北承包商状态变更
        'xbcbszyztbg', // 专业状态变更
    ],

    /**
     * 创建任务
     * @param processId / 流程id /（必填）
     * @param processName / 流程名称 / （必填）
     * @param founder / 发起人 / （必填）
     * @param processInstanceId / 流程实例id，用于在外部生成实例id的情况 / （必填）
     * @param nextPerformers / 指定下节点处理人，多个用“;”分隔 / （非必填）
     * @param toActivityId / 强制指定的下一节点 / （非必填）
     * @param tzbl 跳转变量
     * @param shbl 审核变量
     */
    async createTask(processId, processName, founder, processInstanceId, nextPerformers, toActivityId, tzbl, shbl) {
        //返回数据
        let resultData = {
            success: true,
            message: ''
        };

        //创建任务参数
        let params = {
            processId: processId,
            processName: processName,
            founder: founder,
            tzbl: tzbl,
            shbl: shbl
        }
        //判断流程实例
        if (processInstanceId) {
            params.processInstanceId = processInstanceId;
        }
        //判断指定办理人
        if (nextPerformers) {
            params.nextPerformers = nextPerformers;
        }
        //判断指定节点
        if (toActivityId) {
            params.toActivityId = toActivityId;
        }

        //发起任务
        await axios({
            method: "post",
            url: this.startUrl,
            params: params
        }).then((resp) => {
            if (resp.data && resp.data.meta && resp.data.meta.success) {
                resultData.message = '提交成功！';
                // this.processTaskCallBack();
            } else {
                resultData.success = false;
                resultData.message = '提交失败';
            }
        }).catch((error) => {
            resultData.success = false;
            resultData.message = '提交失败';
        });
        return resultData;
    },

    /**
     * 完成任务
     * @param taskId / 任务ID /（必填）
     * @param performer / 当前任务执行者 / （必填）
     * @param suggestflg / 是否同意，0 否 1是 / （非必填）
     * @param result / 审批意见内容 / （非必填）
     * @param nextPerformers / 指定下节点处理人，多个用“;”分隔 / （非必填）
     * @param toActivityId / 强制指定的下一节点 / （非必填）
     * @param tzbl 跳转变量
     * @param shbl 审核变量
    */
    async finishTask(taskId, performer, suggestflg, result, nextPerformers, toActivityId, tzbl, shbl) {
        //返回数据
        let resultData = {
            success: true,
            message: ''
        };

        //创建任务参数
        let params = {
            taskId: taskId,
            performer: performer,
        }
        //判断是否同意
        if (suggestflg != null) {
            params.suggestflg = suggestflg;
        }
        //判断审批内容
        if (result) {
            params.result = result;
        }
        //判断指定办理人
        if (nextPerformers) {
            params.nextPerformers = nextPerformers;
        }
        //判断指定节点
        if (toActivityId) {
            params.toActivityId = toActivityId;
        }
        if (tzbl != null) {
            params.tzbl = tzbl;
        }
        if (shbl != null) {
            params.shbl = shbl;
        }

        //完成任务
        await axios({
            method: "post",
            url: this.completeUrl,
            params: params
        }).then((resp) => {
            if (resp.data && resp.data.meta && resp.data.meta.success) {
                resultData.message = '办理成功！';
                // this.processTaskCallBack(taskId);
            } else {
                resultData.success = false;
                resultData.message = '办理失败';
            }
        }).catch((error) => {
            resultData.success = false;
            resultData.message = '办理失败';
        });

        return resultData;

    },


    /**
    * 获取待办/已办
     * @param loginName 用户登录账号（必填）
     * @param status 待办/已办（必填）
     * @param page 页号（必填）
     * @param rows 每页行数（必填）
     * @param processInstanceName 任务名称（选填）
     */
    async getTaskList(loginName, status, page, rows, processInstanceName) {
        let resultMap = {
            rows: [],
            total: 0
        };

        //创建任务参数
        let data = {
            appId: this.appId,
            status: status,
            loginName: loginName,
            page: page,
            rows: rows,
            processInstanceName: processInstanceName,
        }

        await axios({
            method: "post",
            url: this.getTasksListUrl,
            data: data
        }).then((resp) => {
            if (resp.data && resp.data.data && resp.data.data) {
                resultMap.rows = resp.data.data.rows;
                resultMap.total = resp.data.data.total;
            }
        });

        return resultMap;
    },

    /**
    * 获取跟踪
    * @param processInstanceId / 流程实例ID / （必填）
    */
    async getTasksMonitor(processInstanceId) {
        const data = {
            processInstanceId: processInstanceId,
        }
        let resultList = [];
        //流程跟踪
        await axios({
            method: "post",
            url: this.getTasksMonitorUrl,
            data: data,
        }).then((resp) => {
            if (resp.data && resp.data.data) {
                resultList = resp.data.data;
            }
        });
        return resultList;
    },

    /**
     * 获取流程id
     */
    async getProcessId(processInstanceId) {
        let processId = '';
        //创建任务参数
        let data = {
            processInstanceId: processInstanceId,
            appId: this.appId
        }
        await axios({
            method: "post",
            url: this.getProcessIdUrl,
            data: data
        }).then((resp) => {
            if (resp.data && resp.data.data && resp.data.data) {
                processId = resp.data.data;
            }
        });
        return processId;
    },

    /**
     * 流程节点回调（用于完成任务时回调，发起任务暂无操作）
     * @param taskId / 待办ID
     */
    processTaskCallBack(taskId) {
        let resultMap = [];

        //创建任务参数
        let params = {
            taskId: taskId,
        }

        //流程跟踪
        axios({
            method: "post",
            url: this.processTaskCallBackUrl,
            params: params
        }).then((resp) => {
            if (resp.data && resp.data.data && resp.data.data) {
                resultMap = resp.data.data;
            }
        });

        return resultMap;
    }
}