<template>
    <div class="content">
        <img v-if="disabled" :src="signatureSrc" alt="Signature" />

        <vueEsign v-else ref="esign" :width="1150" :height="400" :isCrop="isCrop" style="border:1px solid #000000" :lineWidth="lineWidth"
                   :lineColor="lineColor" />

        <div style="width: 98%;text-align: center;bottom: 50px" v-if="!disabled">
            <el-button @click="handleReset">清空画板</el-button>
            <el-button @click="handleGenerate">保存</el-button>
        </div>
    </div>
</template>
<script>
    import {
        defineComponent,
        ref,
        toRefs,
        reactive,
        onMounted,
        getCurrentInstance,
    }
        from 'vue'

    import axiosUtil from "@lib/axiosUtil";
    import vueEsign  from 'vue-esign';
    import vsAuth from "../../lib/vsAuth";
    import {ElMessage} from "element-plus";

    const lineColor = ref("#000000");

    export default defineComponent({
        name: '',
        components: { vueEsign },
        props: {
            params: {
                type: Object,
                required: true
            }
        },
        setup(props, context) {
            const state = reactive({
                lineWidth: 6,
                lineColor: '#000000',
                bgColor: '',
                resultImg: '',
                isCrop: false,
                imgSrc: '',
                busId: props.params.busId,
                busType: props.params.busType,
                standbyField0: props.params.standbyField0,
                // 查看/编辑状态
                disabled: props.params.disabled,
                // 图片url
                signatureSrc: '',
            })

            const instance = getCurrentInstance()

            // 清空画板
            const handleReset = () => {
                instance.refs.esign.reset();
            };

            //
            const handleGenerate = () => {
                instance.refs.esign.generate().then(res => {
                    state.imgSrc = res;
                    saveqm();
                }).catch(err => {
                    alert('请签字！') // 画布没有签字时会执行这里 'Not Signned'
                })
            };

            const saveqm = () => {
                let other = {
                    busType: state.busType,
                    busId: state.busId,
                    standbyField0: state.standbyField0,
                }
                let param = {
                    QRR: state.busId,
                    qmbs: state.imgSrc,
                    other: other
                };
                if (state.imgSrc == '' || state.imgSrc == null) {
                    state.$message.error("请先进行签名")
                    return;
                }
                state.disabled = true;
                //先去数据库中查询，看是否写入到了数据库中
                axiosUtil.post('/backend/component/saveqm', param).then((res) => {
                    if (res.meta.message == 'ok') {
                        let param = {
                            PDRYID: state.busId,
                            QZZT: '3'
                        }
                        context.emit("saveQmData", param)
                    }
                })
            }

            onMounted(async () => {
                if (state.busId == null || state.busId == '') {
                    ElMessage({
                        message: '没有找到业务主键',
                        type: 'error',
                    })
                    goBack();
                }

                state.signatureSrc = '/backend/minio/show?id=' + props.params.fileId;
            })

            return {
                ...toRefs(state),
                handleReset,
                handleGenerate,
                saveqm,
            }
        }
    })
</script>

<style scoped>
    .content {
        width: 100%;
        height: 100%;
    }

</style>
