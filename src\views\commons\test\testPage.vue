<!-- 专家信息查询列表 -->
<template>
  <el-form ref="vForm" :model="formData" class="lui-card-form" label-position="right" label-width="120px"
           size="default" @submit.prevent>
    <el-row :gutter="0" class="grid-row" style="margin-bottom: 10px">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="发送邮箱：" prop="fsyx">
          <el-input v-model="formData.fsyx" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="12" class="grid-cell">
        <el-form-item label="发送邮箱密码：" prop="fsyxmm">
          <el-input v-model="formData.fsyxmm" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="grid-cell">
        <el-form-item label="接收邮箱：" prop="jsyx">
          <el-input v-model="formData.jsyx" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>


      <el-col :span="12" class="grid-cell">
        <el-form-item label="主题：" prop="subject">
          <el-input v-model="formData.subject" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="12" class="grid-cell">
        <el-form-item label="内容：" prop="text">
          <el-input v-model="formData.text" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-button type="primary" @click="send()" >发送</el-button>
  </el-form>

</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
}
  from 'vue'
import {ElMessage} from "element-plus";
import axiosUtil from "../../../lib/axiosUtil";



export default defineComponent({
  components: {},
  props: {
  },
  setup() {
    const state = reactive({
      queryParams:{},
      formData:{
        fsyx:'<EMAIL>',
        fsyxmm:'ypfczllszfmyechg',
        jsyx:'',
        subject:'测试',
        text:'123456'

      }
    })

    const send = () => {
      let params={
        ...state.formData
      }
      axiosUtil.post('/backendSysMessageLog/sendMail', params).then((res) => {

      })
    }






    onMounted(() => {

    })
    return {
      ...toRefs(state),
      send

    }
  }
})

</script>

<style scoped>
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: white;
}
</style>
