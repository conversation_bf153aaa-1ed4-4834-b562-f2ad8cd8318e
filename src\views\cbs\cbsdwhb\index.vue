<template>
    <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
            size="default" @submit.prevent>
        <el-row :gutter="20" class="lui-search-form" style="margin-bottom: 20px;">
            <el-col :span="6">
                <el-input v-model="listQuery.dwhbqkms" placeholder="请输入队伍合并情况描述"></el-input>
            </el-col>
            <el-col :span="18">
                <el-button type="primary" @click="processSearch">查询</el-button>
                <el-button type="success" @click="addRow" style="float: right">新增</el-button>
            </el-col>
        </el-row>
        <div class="container-wrapper">
            <el-table
                class="lui-table"
                highlight-current-row
                ref="table"
                size="default"
                height="calc(100vh - 250px)"
                border
                :data="tableData"
                v-loading="listLoading"
            >
                <el-table-column type="index" width="60" :index="indexMethod" label="序号" align="center"/>
                <el-table-column label="队伍合并情况描述" min-width="300" prop="DWHBQKMS" header-align="center" align="left">
                </el-table-column>
                <el-table-column label="单据状态" min-width="60" prop="DWZTMC" header-align="center" align="center">
                    <template #default="{ row }">
                        <span v-if="row.SHZT === '0'">草稿</span>
                        <span v-else-if="row.SHZT === '1'">已提交</span>
                        <span v-else>审核通过</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="60" header-align="center" align="center">
                    <template #default="{ row }">
                        <div class="table-col-btn">
                            <el-button v-if="row.SHZT=='0'" class="lui-table-button" @click="editRow(row)">
                                编辑
                            </el-button>
                            <el-button v-if="row.SHZT=='0'" class="lui-table-button" @click="deleteRow(row)">
                                删除
                            </el-button>
                            <el-button v-if="row.SHZT!='0'" class="lui-table-button" @click="viewRow(row)">
                                查看
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                v-model:current-page="listQuery.page"
                v-model:page-size="listQuery.size"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, prev, pager, next, sizes"
                class="lui-pagination"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </el-form>
    <el-dialog
        custom-class="lui-dialog"
        title="队伍合并"
        v-model="dwhbDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        top="100px"
        width="80%"
    >
        <dwhbEdit v-if="dwhbDialog" :dwhbid="dwhbid" :editable="editable" @closeForm="closeForm" ></dwhbEdit>
    </el-dialog>
</template>
<script>
import {defineComponent, onMounted, reactive, toRefs} from "vue";
import {getTeamreslutGetProDetails} from '@src/api/sccbsgl.js'
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import dwhbEdit from "./dwhbEdit.vue";
import { v4 as uuidv4 } from "uuid";
export default defineComponent({
  components: {
    dwhbEdit
  },
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery:{
        page: 1,
        size: 10,
        deptId: VSAuth.getAuthInfo().permission.orgnaId,
        dwhbqkms: '',
      },
      listLoading: false,
      tableData: [],
      total: 0,
      dwhbDialog: false,
      dwhbid: '',
      editable: true
    })
    onMounted(() => {
      initListData();
    })

    // 初始化数据
    const initListData = () => {
      const params = {
        ...state.listQuery,
      }
      state.listLoading=true
      axiosUtil.get('/backend/sccbsgl/dwrcgl/dwhb/queryDwhbList', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
        state.listLoading=false
      });
    }
    // 新增
    const addRow = () => {
        state.dwhbid = uuidv4().replace(/-/g,'');
        state.dwhbDialog = true;
    }
    // 编辑
    const editRow = (row) => {
        state.dwhbid = row.DWHBID;
        state.dwhbDialog = true;
    }
    // 删除
    const deleteRow = (row) => {
        
    }
    // 查看
    const viewRow = (row) => {
        
    }
    // 关闭
    const closeForm = () => {
      state.dwhbDialog = false;
      initListData();
    }
    const handleSizeChange = (val) => {
      state.listQuery.page = 1;
      state.listQuery.size = val;
      initListData();
    };
    const handleCurrentChange = (val) => {
      state.listQuery.page = val;
      initListData();
    };
    const indexMethod = (index) => {
      return index + state.listQuery.size * (state.listQuery.page - 1) + 1;
    };
    // 条件查询， 先把页数改为1
    const processSearch = () => {
      state.listQuery.page = 1;
      initListData();
    };

    return {
      ...toRefs(state),
      addRow,
      editRow,
      deleteRow,
      viewRow,
      handleSizeChange,
      handleCurrentChange,
      indexMethod,
      processSearch,
      closeForm
    }
  }

})
</script>

<style scoped>

</style>
