<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px">
        基本信息
      </div>

      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="考核时间：" prop="PJZQ">
            <el-select v-model="formData.PJZQ" class="full-width-input"
                       :disabled="!editable" @change="getPjmxkfList"
                       clearable>
              <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC"
                         :value="item.PJZQID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="考核专业：" prop="PJZYBM">
            <el-select v-model="formData.PJZYBM" class="full-width-input"
                       :disabled="!editable" @change="changeKhzy" filterable
                       clearable>
              <el-option v-for="(item, index) in KHZYOptions" :key="index" :label="item.ZYMC"
                         :value="item.PJZYID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="8" class="grid-cell">
          <el-form-item label="承包商名称：" prop="DXMPJXX.CBSDWQC">
            <div style="margin-left: 10px">{{ formData.DXMPJXX.CBSDWQC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="队伍名称：" prop="DXMPJXX.DWMC">
            <div style="margin-left: 10px">{{ formData.DXMPJXX.DWMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目名称：" prop="DXMPJXX.XMMC">
            <el-input v-model="formData.DXMPJXX.XMMC" type="text" placeholder="请输入" clearable :disabled="true">
              <template #append v-if="editable">
                <el-button @click="dialogXMXZVisible=true">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="子项目名称：" prop="DXMPJXX.ZXMMC">
            <div style="margin-left: 10px">{{ formData.DXMPJXX.ZXMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目所属单位：" prop="DXMPJXX.XMDWID">
            <el-select v-model="formData.DXMPJXX.XMDWID" class="full-width-input"
                       :disabled="!editable"
                       @change="(value)=>formData.DXMPJXX.XMDWMC=EJDWOptions.find(item=>item.ORGNA_ID===value)?.ORGNA_NAME"
                       clearable>
              <el-option v-for="(item, index) in EJDWOptions" :key="index" :label="item.ORGNA_NAME"
                         :value="item.ORGNA_ID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="考核得分：" prop="HJDF">
            <div style="margin-left: 10px">{{ KHDF }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="备注：" prop="BZ">
            <el-input v-model="formData.DXMPJXX.BZ" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

      </el-row>

      <el-divider/>

      <div style="display: flex;gap: 20px">
        <div :style="`font-size: 16px;${showTable==='ADD' ? 'color: #2A96F9' : ''}`" v-if="showTable==='ADD'">
          评价明细（得分式）
        </div>
        <div :style="`font-size: 16px`" v-if="showTable==='SUB'">
          评价明细（扣分式）
        </div>
      </div>


      <div style="margin-top: 20px" v-show="showTable==='ADD'">
        <el-table ref="datatable91634" :data="formData.PJMXDFList" height="calc(300px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false" :span-method="arraySpanMethod"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
          <el-table-column prop="ZBLBMC" label="评价类别" align="center"
                           :show-overflow-tooltip="true" width="150">
          </el-table-column>
          <el-table-column prop="PJNR" label="评价内容" align="center"
                           :show-overflow-tooltip="true" min-width="150">
          </el-table-column>
          <el-table-column prop="ZBMC" label="指标名称" align="center"
                           :show-overflow-tooltip="true" width="150">
          </el-table-column>
          <el-table-column prop="ZBJF" label="指标记分" align="center"
                           :show-overflow-tooltip="true" width="100">
          </el-table-column>
          <el-table-column prop="PFBZXQ" label="评分标准" align="center" v-if="formData.DXMPJXX.JFCJ==='BZ'"
                           :show-overflow-tooltip="true" min-width="200">
          </el-table-column>
          <el-table-column prop="BZJF" label="标准满分" align="center" v-if="formData.DXMPJXX.JFCJ==='BZ'"
                           :show-overflow-tooltip="true" width="100">
          </el-table-column>
          <el-table-column prop="ZBDF" label="标准得分" align="center"
                           :show-overflow-tooltip="true" width="120">
            <template #default="{row,$index}">
              <el-form-item label="" :prop="`PJMXDFList.${$index}.ZBDF`" label-width="0" :rules="tableRules.ZBDF">
                <el-input v-model="row.ZBDF" type="text" placeholder="请输入" clearable :disabled="!editable"
                          @input="row.ZBDF=row.ZBDF.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')">
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="PFSM" label="扣分说明" align="center"
                           :show-overflow-tooltip="true" width="120">
            <template #default="{row,$index}">
              <el-form-item label="" :prop="`PJMXDFList.${$index}.PFSM`" label-width="0">
                <el-input v-model="row.PFSM" type="text" placeholder="请输入" clearable :disabled="!editable">
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="FJ" label="附件资料" align="left"
                           :show-overflow-tooltip="true" width="200">
            <template #default="{row,$index}">
              <vsfileupload :busId="row.DXMPJMXID"
                            :key="row.DXMPJMXID"
                            :editable="editable" ywlb="khmxfj"/>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div style="margin-top: 20px" v-show="showTable==='SUB'">
        <el-table ref="datatable91634" :data="formData.PJMXKFList" height="calc(300px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
          <el-table-column prop="WTFXSJ" label="问题发现时间" align="center"
                           :show-overflow-tooltip="true" width="150">
          </el-table-column>
          <el-table-column prop="WTMS" label="问题描述" align="center"
                           :show-overflow-tooltip="true" width="150">
          </el-table-column>
          <el-table-column prop="PFBZ" label="评分标准" align="center"
                           :show-overflow-tooltip="true" width="150">
          </el-table-column>
          <el-table-column prop="JFBZ" label="扣分标准" align="center"
                           :show-overflow-tooltip="true" width="100">
          </el-table-column>
          <el-table-column prop="JFFZ" label="扣分分值" align="center"
                           :show-overflow-tooltip="true" min-width="200">
          </el-table-column>
          <el-table-column prop="PJLB" label="附件资料" align="center"
                           :show-overflow-tooltip="true" width="200">
            <template #default="{row}">
              <vsfileupload :busId="row.XMWTID"
                            :key="row.XMWTID"
                            :editable="false" ywlb="wtsb"/>
            </template>
          </el-table-column>
        </el-table>
      </div>


      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogXMXZVisible"
        v-model="dialogXMXZVisible"
        title="项目选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <xmChoose v-if="dialogXMXZVisible" @close="dialogXMXZVisible=false" @submit="getXMXZRes"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, computed} from "vue";
import vsAuth from "@lib/vsAuth";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import vsfileupload from "@views/components/vsfileupload";
import comFun from "@lib/comFun";
import xmChoose from "@views/khpj/xmwtsb/xmChoose";

export default defineComponent({
  name: '',
  components: {xmChoose, vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      KHPJID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        PJLX: 'ZQXKH',
        DXMPJXX: {
          PJZT: 'DW',
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
          CJSJ: comFun.getNowTime(),
          SHZT: '0',
        },
        PJMXDFList: [],
        PJMXKFList: []
      },
      tableRules: {
        ZBDF: [{
          required: true,
          message: '请输入',
        }],
      },
      rules: {
        PJZQ: [{
          required: true,
          message: '字段值不可为空',
        }],
        PJZYBM: [{
          required: true,
          message: '字段值不可为空',
        }],
        DXMPJXX: {

          DWMC: [{
            required: true,
            message: '字段值不可为空',
          }],
          XMMC: [{
            required: true,
            message: '字段值不可为空',
          }],
          XMDWID: [{
            required: true,
            message: '字段值不可为空',
          }],
        }
      },
      KHSJOptions: [],
      KHZYOptions: [],
      EJDWOptions: [],

      showTable: '',

      dialogXMXZVisible: false,
    })

    const KHDF = computed(() => {
      let res
      if (state.showTable === 'ADD') {
        res = 0
        state.formData.PJMXDFList.forEach(item => {
          if (item.ZBDF) {
            res += Number(item.ZBDF)
          }
        })

      } else if (state.showTable === 'SUB') {
        res = 100
        state.formData.PJMXKFList.forEach(item => {
          if (item.JFFZ) {
            res -= Number(item.JFFZ)
          }
        })
      }
      return res
    })

    const getFormData = () => {
      let params = {
        KHPJID: state.KHPJID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhmxsbById', params).then((res) => {
        state.formData=res.data
        state.showTable=state.formData.DXMPJXX.JFFS
        state.loading=false
      })

    }


    const saveData = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params = {
        ...state.formData,
        KHPJID: state.KHPJID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        DXMPJXX: {
          ...state.formData.DXMPJXX,
          KHPJID: state.KHPJID,
          XGRZH: state.userInfo.userLoginName,
          XGSJ: comFun.getNowTime(),
          DXMPJID: state.formData.DXMPJXX.DXMPJID || comFun.newId(),
          HJDF: KHDF.value
        },
        DXMPJMX: []
      }

      if (state.showTable === 'ADD') {
        params.DXMPJMX = state.formData.PJMXDFList.map(item => {
          return {
            DXMPJMXID: item.DXMPJMXID,
            DXMPJID: params.DXMPJXX.DXMPJID,
            PFBZID: item.PFBZID,
            ZBDF: item.ZBDF,
            PFSM: item.PFSM,
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
            CJSJ: comFun.getNowTime(),
          }
        })

      } else if (state.showTable === 'SUB') {
        params.DXMPJMX = state.formData.PJMXKFList.map(item => {
          return {
            DXMPJMXID: comFun.newId(),
            DXMPJID: params.DXMPJXX.DXMPJID,
            PFBZID: item.PFBZID,
            XMWTID: item.XMWTID,
            ZBDF: item.JFFZ,
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
            CJSJ: comFun.getNowTime(),
          }
        })
      }

      if (type === 'submit') {
        params.SHZT = '1'
        params.DXMPJXX.SHZT = '1'
      }
      console.log(params)
      state.loading = true
      axiosUtil.post('/backend/sckhpj/khmxtb/saveKhmxsbForm', params).then(res => {
        ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading = false
      })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const changeKhzy = (value) => {
      if (value) {
        let zyqk = state.KHZYOptions.find(item => item.PJZYID === value)
        state.formData.PJZYMC = zyqk.ZYMC
        state.formData.DXMPJXX.PJMBID = zyqk.PJMBID
        state.formData.DXMPJXX.JFCJ = zyqk.JFCJ
        state.showTable = zyqk.JFFS
        if (state.showTable === 'ADD') {
          getPjmxDfList(value)
        } else {
          getPjmxkfList()
        }
      } else {
        state.formData.PJMXDFList = []
      }
    }

    const getXMXZRes = (value) => {
      state.formData.DXMPJXX.DWMC = value.DWMC
      state.formData.DXMPJXX.CBSDWQC = value.CBSDWQC
      state.formData.DXMPJXX.DWWYBS = value.DWWYBS
      state.formData.DXMPJXX.CBSWYBS = value.CBSWYBS
      state.formData.DXMPJXX.PJDXID = value.DWYWID
      state.formData.DXMPJXX.PJDXMC = value.DWMC
      state.formData.DXMPJXX.XMID=value.XMID
      state.formData.DXMPJXX.XMMC=value.XMMC
      state.formData.DXMPJXX.ZXMMC=value.ZXMMC
      state.formData.DXMPJXX.ZXMID=value.ZXMID
      state.formData.DXMPJXX.XMDWMC=value.JSDWMC
      state.formData.DXMPJXX.XMDWID=value.JSDWID
      if (state.showTable === 'SUB') {
        getPjmxkfList()
      }
      state.dialogXMXZVisible = false

    }

    const getPjmxDfList = (PJZYID) => {
      let params = {PJZYID}
      axiosUtil.get('/backend/sckhpj/khmxtb/selectPjmbmxByZyid', params).then(res => {
        state.formData.PJMXDFList = res.data || []
        state.formData.PJMXDFList.forEach(item=>item.DXMPJMXID=comFun.newId())
      })
    }

    const getPjmxkfList = () => {
      if (state.formData.PJZQ && state.formData.DXMPJXX.XMID && state.formData.PJZYBM) {
        let params = {
          PJZQID: state.formData.PJZQ,
          XMID: state.formData.DXMPJXX.XMID,
          PJZYID: state.formData.PJZYBM
        }
        axiosUtil.get('/backend/sckhpj/khmxtb/selectXmZQKfqkList', params).then(res => {
          state.formData.PJMXKFList = res.data || []
        })
      } else {
        state.formData.PJMXKFList = []
      }
    }

    const getEjdwList = () => {
      axiosUtil.get('/backend/common/selectEjdwList', null).then(res => {
        state.EJDWOptions = res.data || []
      })
    }

    const getKhsjList = () => {
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhqjList', null).then(res => {
        state.KHSJOptions = res.data || []
      })
    }

    const getKhzyList = () => {
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhzyList', null).then(res => {
        state.KHZYOptions = res.data || []
      })
    }

    const arraySpanMethod = ({row, column, rowIndex, columnIndex}) => {
      if (columnIndex === 1) {
        if (rowIndex > 0 && state.formData.PJMXDFList[rowIndex - 1].ZBLB === row.ZBLB) {
          return [0, 1]
        } else {
          let span = 1
          let rIndex = rowIndex + 1
          while (state.formData.PJMXDFList.length - 1 >= rIndex && state.formData.PJMXDFList[rIndex].ZBLB === row.ZBLB) {
            span++
            rIndex++
          }
          return [span, 1]
        }
      }

      if (columnIndex === 2) {
        if (rowIndex > 0 && state.formData.PJMXDFList[rowIndex - 1].PJNR === row.PJNR) {
          return [0, 1]
        } else {
          let span = 1
          let rIndex = rowIndex + 1
          while (state.formData.PJMXDFList.length - 1 >= rIndex && state.formData.PJMXDFList[rIndex].PJNR === row.PJNR) {
            span++
            rIndex++
          }
          return [span, 1]
        }
      }

      if (columnIndex === 3) {
        if (rowIndex > 0 && state.formData.PJMXDFList[rowIndex - 1].ZBMC === row.ZBMC) {
          return [0, 1]
        } else {
          let span = 1
          let rIndex = rowIndex + 1
          while (state.formData.PJMXDFList.length - 1 >= rIndex && state.formData.PJMXDFList[rIndex].ZBMC === row.ZBMC) {
            span++
            rIndex++
          }
          return [span, 1]
        }
      }

      if (columnIndex === 4) {
        if (rowIndex > 0 && state.formData.PJMXDFList[rowIndex - 1].PJMBMXID === row.PJMBMXID) {
          return [0, 1]
        } else {
          let span = 1
          let rIndex = rowIndex + 1
          while (state.formData.PJMXDFList.length - 1 >= rIndex && state.formData.PJMXDFList[rIndex].PJMBMXID === row.PJMBMXID) {
            span++
            rIndex++
          }
          return [span, 1]
        }
      }


    }


    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      }
      getEjdwList()
      getKhsjList()
      getKhzyList()
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm,
      getXMXZRes,
      getKhsjList,
      getKhzyList,
      changeKhzy,
      arraySpanMethod,
      getPjmxkfList,
      KHDF

    }
  }

})
</script>

<style scoped>

</style>
