// 西北承包商日常管理 api
import axiosUtil from '@src/lib/axiosUtil.js';
import axios from 'axios';

const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"

/**
 * 查询承包商
 */
export function queryCbsxxList(params) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/queryCbsxxList", params);
}

/**
 * 查询队伍
 */
export function queryDwxxList(params) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/queryDwxxList", params);
}

/**
 * 导出
 */
export function exportToXlsx(params) {
  return axios.post(baseUrl + "/xbcbsrcgl/exportToXlsx", params, {
    responseType: 'blob' // 确保接收二进制数据
  }).then(response => {
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', '承包商日常管理.xlsx'); // 设置下载文件名
    document.body.appendChild(link);
    link.click(); // 触发下载
    document.body.removeChild(link); // 移除链接
    window.URL.revokeObjectURL(url); // 释放URL对象
  }).catch(error => {
    console.error('导出失败:', error);
  });
}

/**
 * 查询承包商/队伍的具体信息
 */

export function queryDwxx(params) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/queryDwxx", params);
}

/**
 * 根据记录查询队伍信息
 */
export function queryDwxxByJl(params) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/queryDwxxByJl", params);
}


/**
 * 创建人工操作记录
 */
export function createRgczjl(parmas) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/createRgczjl", parmas);
}

/**
 * 更新人工操作记录
 */
export function updateRgczjl(parmas) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/updateRgczjl", parmas);
}

/**
 * 队伍状态更新的回调函数，状态变更审核时调用
 */
export function dwztbgCallback(params) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/dwztbgCallback", params);
}

/**
 * 查询队伍信息和专业状态
 */
export function queryDwxxAndZyztList(params) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/queryDwxxAndZyztList", params);
}

/**
 * 创建专业状态变更记录
 */
export function createRgczjlZy(parmas) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/createRgczjlZy", parmas);
}

/**
 * 更新专业状态变更记录
 */
export function updateRgczjlZy(parmas) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/updateRgczjlZy", parmas);
}

/**
 * 查询队伍信息和专业信息通过记录
 */
export function queryDwxxAndZyxxByJl(params) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/queryDwxxAndZyxxByJl", params);
}

/**
 * 队伍专业状态变更回调
 */
export function dwzyztbgCallback(params) {
  return axiosUtil.post(baseUrl + "/xbcbsrcgl/dwzyztbgCallback", params);
}