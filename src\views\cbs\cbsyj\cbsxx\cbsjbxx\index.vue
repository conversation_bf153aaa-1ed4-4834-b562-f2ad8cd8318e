<template>
    <el-form :model="form" ref="vForm" :rules="vFormRules" label-position="left" label-width="140px" size="default"
        class="lui-card-form" :disabled="!editable">
        <el-row :gutter="0" class="grid-row">
            <el-col :span="16" class="grid-cell">
                <el-form-item label="企业全称" prop="CBSDWQC">
                    <el-input v-model="form.CBSDWQC" v-tooltip="{
                        newValue: form.CBSDWQC,
                        oldValue: resultTableData?.CBSDWQC,
                        label: resultTableData?.CBSDWQC,
                    }" clearable disabled placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="简称" prop="DWJC">
                    <el-input v-model="form.DWJC" v-tooltip="{
                        newValue: form.DWJC,
                        oldValue: resultTableData?.DWJC,
                        label: resultTableData?.DWJC,
                    }" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="法人" prop="FDDBRXM">
                    <el-input v-model="form.FDDBRXM" v-tooltip="{
                        newValue: form.FDDBRXM,
                        oldValue: resultTableData?.FDDBRXM,
                        label: resultTableData?.FDDBRXM,
                    }" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="联系方式" prop="FDDBRLXFS">
                    <el-input v-model="form.FDDBRLXFS" v-tooltip="{
                        newValue: form.FDDBRLXFS,
                        oldValue: resultTableData?.FDDBRLXFS,
                        label: resultTableData?.FDDBRLXFS,
                    }" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="注册日期" prop="ZCRQ">
                    <el-date-picker v-model="form.ZCRQ" v-tooltip="{
                        newValue: form.ZCRQ,
                        oldValue: resultTableData?.ZCRQ,
                        label: resultTableData?.ZCRQ,
                    }" type="date" placeholder="请选择" value-format="YYYY-MM-DD" style="width: 100%" />
                </el-form-item>
            </el-col>
            <!--      <el-col :span="8" class="grid-cell">-->
            <!--        <el-form-item label="企业类型" prop="QYLXDM">-->
            <!--          <el-select-->
            <!--              v-model="form.QYLXDM"-->
            <!--              v-tooltip="{-->
            <!--                  newValue: form.QYLXDM,-->
            <!--                  oldValue: resultTableData?.QYLXDM,-->
            <!--                  label: qylxOptions.find((i) => i.DMXX == resultTableData?.QYLXDM)-->
            <!--                    ?.label,-->
            <!--                }"-->
            <!--              clearable-->
            <!--              placeholder="请选择"-->
            <!--              style="width: 100%"-->
            <!--          >-->
            <!--            <el-option-->
            <!--                v-for="(item, index) in qylxOptions"-->
            <!--                :key="index"-->
            <!--                :label="item.DMMC"-->
            <!--                :value="item.DMXX"-->
            <!--            ></el-option>-->
            <!--          </el-select>-->
            <!--        </el-form-item>-->
            <!--      </el-col>-->
            <el-col :span="16" class="grid-cell">
                <el-form-item label="注册地址" prop="ZCDZ">
                    <el-input v-model="form.ZCDZ" v-tooltip="{
                        newValue: form.ZCDZ,
                        oldValue: resultTableData?.ZCDZ,
                        label: resultTableData?.ZCDZ,
                    }" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="注册资金(万元)" prop="ZCZBJE">
                    <el-input v-model.number="form.ZCZBJE" v-tooltip="{
                        newValue: form.ZCZBJE,
                        oldValue: resultTableData?.ZCZBJE,
                        label: resultTableData?.ZCZBJE,
                    }" type="number" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="grid-cell">
                <el-form-item label="营业范围" prop="EXTENSION.yyfw">
                    <el-input v-model="form.EXTENSION.yyfw" v-tooltip="{
                        newValue: form.EXTENSION.yyfw,
                        oldValue: resultTableData?.EXTENSION?.yyfw,
                        label: resultTableData?.EXTENSION?.yyfw,
                    }" type="textarea" :rows="3" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="统一信用代码" prop="TYXYDM">
                    <el-input v-model="form.TYXYDM" v-tooltip="{
                        newValue: form.TYXYDM,
                        oldValue: resultTableData?.TYXYDM,
                        label: resultTableData?.TYXYDM,
                    }" clearable disabled placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
                <el-form-item label="是否营业截止日期" prop="EXTENSION.SFYYJZRQ">
                    <el-select v-model="form.EXTENSION.SFYYJZRQ" clearable placeholder="请选择" style="width: 100%">
                        <el-option v-for="(item, index) in [{ DMMC: '是', DMXX: '1' }, { DMMC: '无固定期限', DMXX: '0' }]"
                            :key="index" :label="item.DMMC" :value="item.DMXX"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
                <el-form-item label="营业有效截止日期" prop="YXQJS" v-if="form.EXTENSION.SFYYJZRQ === '1'">
                    <el-date-picker v-model="form.YXQJS" v-tooltip="{
                        newValue: form.YXQJS,
                        oldValue: resultTableData?.YXQJS,
                        label: resultTableData?.YXQJS,
                    }" type="date" placeholder="请选择" value-format="YYYY-MM-DD" style="width: 100%" />
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="单位系统归属" prop="DWXTGSDM">
                    <!-- <el-input v-model="form.DWXTGSDM" clearable placeholder="请输入"></el-input> -->
                    <el-select v-model="form.DWXTGSDM" v-tooltip="{
                        newValue: form.DWXTGSDM,
                        oldValue: resultTableData?.DWXTGSDM,
                        label: dwxtgsOptions.find((i) => i.value == resultTableData?.DWXTGSDM)?.DMMC,
                    }" clearable placeholder="请选择" style="width: 100%">
                        <el-option v-for="(item, index) in dwxtgsOptions" :key="index" :label="item.DMMC"
                            :value="item.DMXX"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="16" class="grid-cell">
                <el-form-item label="开户行及开户号" prop="KHXJKHH">
                    <el-input v-model="form.KHXJKHH" v-tooltip="{
                        newValue: form.KHXJKHH,
                        oldValue: resultTableData?.KHXJKHH,
                        label: resultTableData?.KHXJKHH,
                    }" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <!--<el-col :span="8" class="grid-cell">
        <el-form-item label="有效截止日期" prop="KHXYXQJS">
          <el-date-picker
              v-model="form.KHXYXQJS"
              v-tooltip="{
                  newValue: form.KHXYXQJS,
                  oldValue: resultTableData?.KHXYXQJS,
                  label: resultTableData?.KHXYXQJS,
                }"
              type="date"
              placeholder="请选择"
              value-format="YYYY-MM-DD"
              style="width: 100%"
          />
        </el-form-item>
      </el-col>-->
            <!-- <el-row>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="固定资产" prop="gdzc">
          <el-input v-model="form.gdzc" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="基层队伍个数" prop="jcdwgs">
          <el-input v-model="form.jcdwgs" type="number" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>-->
            <el-col :span="8" class="grid-cell">
                <el-form-item label="员工人数" prop="EXTENSION.ygrs">
                    <el-input v-model="form.EXTENSION.ygrs" type="number" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <!--</el-row>
       <el-col :span="8" class="grid-cell">
        <el-form-item label="股权结构" prop="gqjg">
          <el-input
              v-model="form.EXTENSION.gqjg"
              v-tooltip="{
                  newValue: form.EXTENSION.gqjg,
                  oldValue: resultTableData?.EXTENSION?.gqjg,
                  label: resultTableData?.EXTENSION?.gqjg,
                }"
              clearable
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col> -->
            <el-col :span="8" class="grid-cell">
                <el-form-item label="联系人" prop="LXRXM">
                    <el-input v-model="form.LXRXM" v-tooltip="{
                        newValue: form.LXRXM,
                        oldValue: resultTableData?.LXRXM,
                        label: resultTableData?.LXRXM,
                    }" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="联系人手机" prop="LXRSJH">
                    <el-input v-model="form.LXRSJH" v-tooltip="{
                        newValue: form.LXRSJH,
                        oldValue: resultTableData?.LXRSJH,
                        label: resultTableData?.LXRSJH,
                    }" type="number" clearable placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>

            <!-- <el-col :span="24" class="grid-cell">
        <el-form-item label="组织机构代码" prop="EXTENSION.ZZJGDM">
          <el-input
              v-model="form.EXTENSION.ZZJGDM"
              v-tooltip="{
                  newValue: form.EXTENSION.ZZJGDM,
                  oldValue: resultTableData?.EXTENSION?.ZZJGDM,
                  label: resultTableData?.EXTENSION?.ZZJGDM,
                }"
              clearable
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col> -->


            <!-- <el-col :span="8" class="grid-cell">
        <el-form-item label="选商联系人" prop="EXTENSION.XSLXRXM">
          <el-input
              v-model="form.EXTENSION.XSLXRXM"
              v-tooltip="{
                  newValue: form.EXTENSION.XSLXRXM,
                  oldValue: resultTableData?.EXTENSION?.XSLXRXM,
                  label: resultTableData?.EXTENSION?.XSLXRXM,
                }"
              clearable
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="8" class="grid-cell">
        <el-form-item label="联系电话" prop="EXTENSION.XSLXRDH">
          <el-input
              v-model="form.EXTENSION.XSLXRDH"
              v-tooltip="{
                  newValue: form.EXTENSION.XSLXRDH,
                  oldValue: resultTableData?.EXTENSION?.XSLXRDH,
                  label: resultTableData?.EXTENSION?.XSLXRDH,
                }"
              clearable
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="8" class="grid-cell">
        <el-form-item label="邮箱地址" prop="EXTENSION.YXDZ">
          <el-input
              v-model="form.EXTENSION.YXDZ"
              v-tooltip="{
                  newValue: form.EXTENSION.YXDZ,
                  oldValue: resultTableData?.EXTENSION?.YXDZ,
                  label: resultTableData?.EXTENSION?.YXDZ,
                }"
              clearable
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col> -->


            <!-- <el-row>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="是否涉诉" prop="sfss">
            <el-radio-group v-model="form.sfss">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
            </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="是否失信人" prop="sfsxr">
            <el-radio-group v-model="form.sfsxr">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
            </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="综合评定" prop="zhpd">
          <el-input v-model="form.zhpd" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
    </el-row> -->
            <el-collapse v-model="activeName" accordion>
                <el-collapse-item title="相关附件" name="xgfj">
                    <vsFileUploadTable ref="fileTable" style="width: 100%;height: 400px" YWLX="CBSJBXX"
                        :key="form.CBSYWID" :busId="form.CBSYWID" v-model:fileTableData="fileTableData"
                        :editable="editable" />
                </el-collapse-item>
            </el-collapse>
        </el-row>
    </el-form>
</template>

<script setup>
import { computed, defineProps, getCurrentInstance, onMounted, reactive, ref, watch } from "vue";
import vsFileUploadTable from "../../../../components/vsFileUploadTable.vue";
import { ElMessage, formProps } from "element-plus";
import { getCommonSelectDMB } from "@src/api/common.js";
import { getCheckedJbxx, getCheckIsFirstZr } from "@src/api/sccbsgl.js";
import { vue } from "../../../../../assets/core";
import axiosUtil from "@lib/axiosUtil";

const props = defineProps({
    row: {
        type: Object,
        defaultData: () => { },
    },
    // 结果表数据，比对用
    resultTableData: {
        type: Object,
        default: () => null,
    },
    // 是否查看模式
    editable: {
        type: Boolean,
        default: true
    },
    TYXYDM: {
        type: Object,
        default: () => null,
    },
    CBSDWQC: {
        type: Object,
        default: () => null,
    },
});
const fileTableData = vue.ref([]);
let form = ref({
    /**企业全称 */
    CBSDWQC: props.CBSDWQC,
    /**简称 */
    DWJC: "",
    /**法人 */
    FDDBRXM: "",
    /**联系方式 */
    FDDBRLXFS: "",
    /**企业类型 */
    QYLXDM: "zryxgs",
    /**注册地址 */
    ZCDZ: "",
    /**注册资金 */
    ZCZBJE: "",
    /**注册日期 */
    ZCRQ:"",
    /**营业范围 */
    yyfw: "",
    /**营业执照号 */
    yyzzh: "",
    /**营业执照号-有效截止日期 */
    yyzzhyxjzrq: "",
    /**单位系统归属 */
    DWXTGSDM: "",
    /**统一信用代码 */
    TYXYDM: props.TYXYDM,
    /**统一信用代码-有效截止日期 */
    YXQJS: "",
    /**开户行及开户号 */
    KHXJKHH: "",
    /**开户行及开户号-有效截止日期 */
    KHXYXQJS: "",
    /**固定资产 */
    gdzc: "",
    /**基层队伍个数 */
    jcdwgs: "",
    /**员工人数 */
    ygrs: "",
    /**股权结构 */
    gqjg: "",
    /**联系人 */
    LXRXM: "",
    /**联系人手机 */
    LXRSJH: "",
    /**是否涉诉 */
    sfss: "1",
    /**是否失信人 */
    sfsxr: "1",
    /**综合评定 */
    zhpd: "",
    EXTENSION: {
        yyfw: "", // 服务范围
        SFYYJZRQ: "", // 营业有效截止日期
        ygrs: "" // 员工人数
    }
});
watch(
    () => props.row,
    (val) => {
        if (val) form.value = val;
    },
    {
        immediate: true,
    }
);
let _this = getCurrentInstance();
let activeName = ref("xgfj");
// 查询企业类型
let qylxOptions = ref([]);
const getQylx = () => {
    getCommonSelectDMB({
        DMLBID: "QYLX",
    }).then(({ data }) => {
        console.log('查询企业类型', data);
        if (data) qylxOptions.value = data;
    })
        .catch((err) => {
            ElMessage.error("查询企业类型获取失败");
        });
}
onMounted(getQylx)

let fileList = reactive([]);
let tableData = reactive([]);
let currentRow = reactive({});
const setCurrentRow = (row) => {
    currentRow = row;
};
const handleUpload = (val, index) => { };
const queryTableData = () => {
    // tableData = []
    tableData.push(
        ...[
            { lx: "营业执照", fileName: "" },
            { lx: "固定资产及其他信息证明材料", fileName: "" },
            { lx: "企业资信证明", fileName: "" },
            { lx: "审计财务报告", fileName: "" },
            { lx: "改制企业改制文件", fileName: "" },
            { lx: "企业简介", fileName: "" },
            { lx: "廉洁承诺书", fileName: "" },
            { lx: "其他附件", fileName: "" },
        ]
    );
};
// 单位系统归属
const dwxtgsOptions = ref([]);
const getDwxtgsOptions = () => {
    getCommonSelectDMB({
        DMLBID: "DWXTGS",
    })
        .then(({ data }) => {
            if (data) dwxtgsOptions.value = data;
        })
        .catch((err) => {
            ElMessage.error("单位系统归属信息获取失败");
        });
};

const editable = ref(props.editable)
onMounted(async() => {
    getDwxtgsOptions();
    // 查询基本信息的校验内容
    // getCheckedByCbsywid();
    // 判断是否为首次准入
    // checkIsFirstZr();
});

const onFileChange = (file, files) => {
    console.log(file, files);
    fileList = files;
    let ind = tableData.indexOf(currentRow);
    tableData[ind].fileName = fileList.map((item) => item.name).join(",");
    tableData[ind].fileList = fileList;
};
onMounted(() => {
    queryTableData();
});
const vForm = ref(null);
const vFormRules = ref({
    CBSDWQC: [
        { required: true, message: "请输入企业名称", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    DWJC: [
        { required: false, message: "请输入企业简称", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    FDDBRXM: [
        { required: true, message: "请输入法人姓名", trigger: "blur" },
        { max: 64, message: "最多输入64个字符", trigger: "blur" },
    ],
    FDDBRLXFS: [
        { required: true, message: "请输入法人联系方式", trigger: "blur" },
        { max: 32, message: "最多输入32个字符", trigger: "blur" },
    ],
    QYLXDM: [{ required: true, message: "请选择企业类型", trigger: "blur" }],
    ZCDZ: [
        { required: true, message: "请输入注册地址", trigger: "blur" },
        { max: 256, message: "最多输入256个字符", trigger: "blur" },
    ],
    ZCZBJE: [
        { required: true, message: "请输入注册资金", trigger: "blur" },
        {
            pattern: /^[0-9]{1,16}(.[0-9]{1,6})?$/,
            message: "请输入正常范围",
            trigger: "change",
        },
        { type: "number", message: "请输入数字", trigger: "change" },

        // 整数位16，小数位6
    ],
    'EXTENSION.yyfw': [
        { required: true, message: "请输入营业范围", trigger: "blur" },
        { max: 2000, message: "最多输入2000个字符", trigger: "blur" },
    ],
    'EXTENSION.ygrs': [
        { required: true, message: "请输入员工人数", trigger: "blur" },
    ],
    TYXYDM: [
        { required: true, message: "请输入统一信用代码", trigger: "blur" },
        { max: 32, message: "最多输入32个字符", trigger: "blur" },
    ],
    YXQJS: [{ required: false, message: "请选择有效截止日期", trigger: "blur" }],
    DWXTGSDM: [{ required: true, message: "请选择单位系统归属", trigger: "blur" }],
    KHXJKHH: [
        { required: false, message: "请输入开户行及开户号", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    KHXYXQJS: [{ required: false, message: "请选择有效截止日期", trigger: "blur" }],
    gqjg: [
        { required: false, message: "请输入股权结构", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    LXRXM: [
        { required: true, message: "请输入联系人", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    LXRSJH: [
        { required: true, message: "请输入联系人手机", trigger: "blur" },
        { max: 32, message: "最多输入32个字符", trigger: "blur" },
    ],
    'EXTENSION.ZZJGDM': [
        { required: true, message: "请输入组织机构代码", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    'EXTENSION.XSLXRXM': [
        { required: true, message: "请输入选商联系人", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    'EXTENSION.XSLXRDH': [
        { required: true, message: "请输入联系电话", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    'EXTENSION.YXDZ': [
        { required: true, message: "请输入邮箱地址", trigger: "blur" },
        { max: 128, message: "最多输入128个字符", trigger: "blur" },
    ],
    // 注册日期
    ZCRQ: [{ required: true, message: "请选择注册日期", trigger: "blur" }],
});
const validateForm = () => {
    return Promise.all([vForm.value.validate(), validateJbxx(), validateFileTable()])
};

const fileTable = ref(null);

const validateFileTable = () => {
    return new Promise((resolve, reject) => {
        fileTable.value.validateData().then(res => {
            resolve(true)
        }).catch(msg => {
            reject({
                XGCL: [{
                    message: msg
                }]
            })
        })

    })
}


// 查询基本信息校验内容
const checkFrom = reactive({});
const getCheckedByCbsywid = () => {
    axiosUtil.get('/backend/sccbsgl/cbsxxgl/getCheckedJbxx', {
        cbsywid: form.value.CBSYWID,
    })
        .then(({ data }) => {
            Object.assign(checkFrom, data.form);
        })
        .catch((err) => {
            ElMessage.error("查询失败");
        });
};
const validateJbxx = () => {
    return new Promise((resolve, reject) => {
        if (checkFrom.ZDZCNX && calculateAge(form.value.ZCRQ) < checkFrom.ZDZCNX) {
            reject({ JBXX: [{ message: '不满足最低注册年限' + checkFrom.ZDZCNX + '年' }] });
        }
        // else if(checkFrom.ZDDYS && Number(form.value.EXTENSION.ygrs) < checkFrom.ZDDYS){
        //   reject({ JBXX:[{ message: '员工人数不满足最多最低定员数' + checkFrom.ZDDYS + '人' }] });
        // }
        else if (checkFrom.ZDZCZJ && Number(form.value.ZCZBJE) < checkFrom.ZDZCZJ) {
            reject({ JBXX: [{ message: '注册资金不满足最低要求' + checkFrom.ZDZCZJ + '万元' }] });
        } else {
            resolve(true);
        }
    })
}
// 计算相差年份
const calculateAge = (birthDateString) => {
    const birthDate = new Date(birthDateString);
    const currentDate = new Date();
    let timeDifference = currentDate - birthDate;
    return (timeDifference / (24 * 60 * 60 * 1000 * 365)).toFixed(2);
}
// 判断是否为首次准入
let isFirstZr = ref(true);
const checkIsFirstZr = async() => {
    await getCheckIsFirstZr({
        tyxydm: form.value.TYXYDM,
    })
        .then(({ data }) => {
            isFirstZr.value = data.rows.length == 0;
        })
        .catch((err) => {
            ElMessage.error("查询失败");
        });
}
defineExpose({
    validateForm,
    form:form.value
});
</script>

<style scoped>
.el-container {
    height: 100%;
}

.el-form .el-row .el-form-item {
    width: 100%;
}

::v-deep .el-form .el-row ::v-deep .el-form-item> ::v-deep .el-input,
.el-select {
    width: 100%;
}

.el-collapse {
    width: 100%;
}

.el-collapse .el-collapse-item ::v-deep .el-collapse-item__header {
    background-color: #e3e6f6;
    padding-left: 10px;
}
</style>
