<template>

</template>



<script>
import {store} from "@core";

const CryptoJS =require("crypto-js")
import router from "../assets/core/router/index.js"
import comFun from "@src/lib/comFun";
import axiosUtil from '@src/lib/axiosUtil.js';
import {defineComponent, reactive, toRefs, onMounted} from "vue";
import vsAuth from "@lib/vsAuth";
export default defineComponent({
  name: '',
  components: {},
  props: {},
  setup(props, {emit}) {
    const state = reactive({

    })
    const getCurrentUser = ()=>{
        let token;
        const tokenReg = /^K[a-zA-Z0-9]*$/;
        const paramTokenReg = new RegExp('(^|&)token=([^&]*)(&|$)', 'i');
        // 从当前域URL地址或localstorage中获取token，URL中token是最新的
        let regExpMatchArray = window.location.search.substr(1).match(paramTokenReg);
        token = regExpMatchArray != null ? decodeURI(regExpMatchArray[2]) : sessionStorage.getItem('token');
        token = tokenReg.test(token) ? token : '';
      sessionStorage.setItem('token', token);
        let request = new XMLHttpRequest();
        request.open('GET', 'http://gateway.jhof.sinopec.com/kepler/upms/u/users/current', true);

        // 请求头设置请求头key=Authorization,value=${"Bearer " + token}
        request.setRequestHeader('Authorization', 'Bearer ' + token);
        request.send(null);
        request.onreadystatechange = function () {
            // 请求成功返回数据并解析
            if (request.readyState === 4 && request.status === 200) {
              store.state.vsUserInfo=null
              window.location="/";
              // vsAuth.getAuthInfo().then(res=>{
              //   store.state.vsUserInfo=res
              //   console.log('登录重新获取用户信息',res)
              //   router.push("/")
              //   //location.reload();
              // })
                //login本系统
                // const response = JSON.parse(request.responseText);
                // //console.log('responseresponseresponse',response);
                // axiosUtil.get("/backend/login/backendLogin", { userName: comFun.desEncode(response.data.adAccount,'D7ECD5897BAE4D09BDFAFCE1C8F4158B')}).then((pageData) => {
                //     if(pageData.meta.success){
                //       router.push("/")
                //     }else{
                //       alert("您没有该系统的访问权限，请联系系统管理员！")
                //     }
                // })
            }
            // 未登录时，接口返回401，取401的响应头Redirect-Login-Page,携带原地址访问登录地址
            if (request.readyState === 4 && request.status === 401) {
                //alert("未登录")
                window.location.href = request.getResponseHeader('Redirect-Login-Page') + '?redirect=' + window.location.href;
            }
            // 未授权，接口返回403
            if (request.readyState === 4 && request.status === 403) {
                //alert("未授权")
                //const response = JSON.parse(request.responseText);
                //document.getElementById('context').innerText = response.message;
            }
        }
}
    onMounted(() => {
      getCurrentUser();
    })

    return {
      ...toRefs(state),
    }
  }

})
</script>
