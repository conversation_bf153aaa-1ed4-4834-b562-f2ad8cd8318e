
<!-- 设计器版本:1.1.4 前端框架版本:VSUI2.1.0 创建时间:2023-07-18 -->
<template>


</template>

<script>
import {
  vue,
  vsuiapi,
  mixin,
  eventDefine,
  eventBus,
  runtimeCfg,
  axios
}
from '@src/assets/core';
// 请求方法 get,post,put,del,downloadFile
import axiosUtil from "@lib/axiosUtil";
import {
  ElMessage as $message
}
from 'element-plus'
export default vue.defineComponent({
  name: '',
  components: {},
  props: {},
  setup(props, context) {
    // 一个 setup 函数的 props 是响应式的，并且会在传入新的 props 时同步更新
    console.log(props)
    // 透传 Attributes（非响应式的对象，等价于 $attrs）
    console.log(context.attrs)
    // 插槽（非响应式的对象，等价于 $slots）
    console.log(context.slots)
    // 触发事件（函数，等价于 $emit）
    console.log(context.emit)
    // 暴露公共属性（函数）
    console.log(context.expose)
    const {
      ref,
      shallowRef,
      toRefs,
      reactive,
      getCurrentInstance,
      watch,
      watchEffect,
      onMounted,
      onUpdated,
      onUnmounted
    } = vue
    const {
      /**
       * 前端运行的虚拟目录，取值自runtimeCfg.app_public_path，引入资源时需要将此作为前缀
       * 示例 => 图片的src路径
       *   :src = " basePath + '/img' "
       */
      basePath,
      /**
       * 当前路由信息 = useRoute()
       * 示例 => 接收路由跳转传来的参数
       * 1、接收隐式传参传来的参数 => console.log(vsuiRoute.params)
       * 注：该方法用于获取动态路由的值，例如路由配置中 path:'/test/:id'或 path:"/test:id" ,其中id的值可通过params取到
       * 2、接收显式传参传来的参数 => console.log(vsuiRoute.query)
       */
      vsuiRoute,
      /**
       * 路由定义信息，@vsui/vue-multiplex导出的router对象
       * 示例 => 跳转页面
       *
       *   1、vsuiRouter.push => 跳转到指定url路径，并想history栈中添加一个记录，点击后退会返回到上一个页面
       *   2、vsuiRouter.replace => 跳转到指定url路径，但是history栈中不会有记录，点击后退会跳转到上上个页面 (相当于直接替换了当前页面)
       *   3、vsuiRouter.go(n) => 向前或者向后跳转n个页面，n可为正整数或负整数
       *
       *  vsuiRouter.push 示例：（replace用法相同）
       *  显式传参：
       *     vsuiRouter.push({
       *      path: '/search',  // 路由里的path
       *      query: {          // 要传递的参数，无参数传递这里可省略不写
       *        name: 'test',
       *      },
       *     })
       *
       *  隐式传参：
       *     vsuiRouter.push({
       *      name: 'search', // 路由里的name
       *      params: {       // 要传递的参数，无参数传递这里可省略不写
       *        name: 'test', // 注意，vue3中，如果没有在路由配置里声明你用到的这些参数，那么将获取不到该参数
       *        age: 12       // 例如你想要获取到这里的params,你需要在路由配置中声明 path: "test/:name/:age"，否则将取不到该参数
       *      },
       *     })
       *
       *  注：params只与name搭配生效，name就是route配置时的name。
       *     path与query是一对，name和params是一对，请别混用。
       *     显式query会很明显的跟在新的url上，而隐式params不会.
       *     * 页面带参数跳转 推荐使用 => 显式传参。
       *
       */
      vsuiRouter,
      /**
       * 核心组件认证对象
       * vsuiAuth.getPermission() => 用户的权限完整信息
       * vsuiAuth.getUserName() => 当前登录用户名字符串
       * vsuiAuth.getRealName() => 当前登录用户真实姓名字符串
       * vsuiAuth.getToken() => 在REST无会话模式下使用的票据信息
       * vsuiAuth.getModulesTree() => 用户资源转换为树状菜单后的对象
       * vsuiAuth.logout() => 退出当前登录
       */
      vsuiAuth,
      /**
       * 状态定义信息，@vsui/vue-multiplex导出的vuex对象
       * 示例 =>  (以下例子都是基于框架src/assets/store/project.js文件编写)
       *  1、获取存储在Store下共享的数据：
       *      你可以直接通过store对象获取它存储的数据，例：console.log(vsuiStore.state.project)
       *      或者在配置文件里编写一个getters事件，有很多时候我们需要从store中的state中派生出一些状态，
       *      例如对列表进行过滤并计数。此时可以用到getters，getters可以看作是store的计算属性，其参数为state。例：
       *         getters:{
       *           getName:state =>{
       *             return state.name
       *           }
       *         }
       *    然后在代码中可以通过 vsuiStore.getters["project/getName"] 获取数据
       *
       *  2、修改存储在Store下共享的数据（更改 Vuex 的 store 中的状态的唯一方法是提交 mutation）
       *    在 /src/assets/store/ 下的对应声明配置文件中，在 mutations 中新增对应的修改事件，例如：
       *      mutations: {
       *        changeName(state,params){
       *          state.name = params
       *        }
       *      }
       *    然后在代码中使用  vsuiStore.commit("project/changeName", "test") 即可修改
       *    不推荐直接使用 vsuiStore.state.project.name = "test" 这种方式
       *
       *  3、因为mutations中只能是同步操作，但是在实际的项目中，会有异步操作，那么action中提交mutation,然后在组件的methods中去提交action。
       *    只是提交actions的时候使用的是dispatch函数，而mutations则是用commit函数。 例如
       *       actions: {
       *         increment(context){
       *           context.commit('changeName',"test");
       *         }
       *       }
       *     代码中调用 vsuiStore.dispatch("project/increment") 即可完成异步操作
       */
      vsuiStore,
      /**
       * 全局事件总线,@vsui/vue-multiplex导出的eventBus对象
       * 示例 => 事件的定义和接收
       *   1.事件定义 => vsuiEventbus.emit('changedDarkMode', {a:'b'})
       *   2.事件接收 =>   onMounted(()=>{
       *                     vsuiEventbus.on('changedDarkMode',(eventArgObj)=>{
       *                       console.log("接收到事件",eventArgObj);    // eventArgObj 即为上方定义时传递的 {a:'b'}
       *                     })
       *                   });
       */
      vsuiEventbus
    } = mixin();
    const state = reactive({
      formData: {

      },
      rules: {},
    })
    // 深度合并对象
    const deepMerge = function (target, source) {
      // 如果要合并的属性是对象，则递归地合并它们
      if (typeof source === 'object' && source !== null) {
        Object.keys(source).forEach(key => {
          if (typeof source[key] === 'object' && source[key] !== null) {
            if (!target[key]) {
              Object.assign(target, {
                [key]: {}
              });
            }
            deepMerge(target[key], source[key]);
          }
          else {
            Object.assign(target, {
              [key]: source[key]
            });
          }
        });
      }
      return target;
    }
    const methods = {}
    const instance = getCurrentInstance()
    let DSV = {}
    const VFR = {
      // 获取容器或字段组件
      getWidgetRef: (value) => {
        return instance.proxy.$refs[value]
      },
      // 获取表单数据对象
      getFormData: () => {
        return state.formData
      },
      // 执行数据源请求
      executeDataSource: (dsName, localDsv) => {
        DSV = deepMerge(DSV, localDsv)
        return eval(`${dsName}()`)
      }
    }
    const submitForm = () => {
      instance.proxy.$refs['vForm'].validate(valid => {
        if (!valid) return
        //TODO: 提交表单
      })
    }
    const resetForm = () => {
      instance.proxy.$refs['vForm'].resetFields()
    };
    (function () {
      // onFormCreated钩子
    })()
    // console.log(state)
    // 实现onFormDataChange
    function watchState(data) {
      for (const stateKey in data) {
        if (Object.prototype.toString.call(data[stateKey]) === '[object Object]') {
          watchState(data[stateKey])
        }
        else {
          watch(
            () => data[stateKey],
            (newValue, oldValue) => {
              onFormDataChange(stateKey, newValue, oldValue)
            }, {
              deep: true
            })
        }
      }
    }
    // 侦听 state 属性
    watchState(state)
    // 相当于 vform onFormDataChange 事件
    // 目前只支持3个参数
    function onFormDataChange(fieldName, newValue, oldValue) {
      // formConfig.onFormDataChange 生成代码
    }
    // 生成 DSV 请求
    // 注册字段组件api
    function registerElApiProperty(obj, id) {
      Object.defineProperties(obj, {
        setValue: {
          value: (value) => {
            state.formData[id] = value
          }
        },
        getValue: {
          value: () => {
            return state.formData[id]
          }
        },
      })
    }
    onMounted(async () => {})
    onUpdated((fieldName, newValue, oldValue) => {})
    onUnmounted(() => {
      console.log('onUnmounted')
    })
    return {
      ...toRefs(state),
      ...methods,
      submitForm,
      resetForm
    }
  }
})

</script>


<style scoped>
/****globalCssBegin*****
此部分本是设计器全局样式，后移到scoped中，待测试无问题后才可放心使用，另外此样式存在多个elelment+控件的class名，可能存在需要穿透的问题*/
  .el-input-number.full-width-input,
  .el-cascader.full-width-input {
    width: 100% !important;
  }

  .el-form-item--medium .el-radio {
    line-height: 36px !important;
  }

  .el-form-item--medium .el-rate {
    margin-top: 8px;
  }

  .el-form-item--small .el-radio {
    line-height: 32px !important;
  }

  .el-form-item--small .el-rate {
    margin-top: 6px;
  }

  .el-form-item--mini .el-radio {
    line-height: 28px !important;
  }

  .el-form-item--mini .el-rate {
    margin-top: 4px;
  }

  .clear-fix:before,
  .clear-fix:after {
    display: table;
    content: "";
  }

  .clear-fix:after {
    clear: both;
  }

  .float-right {
    float: right;
  }

  .button_folat {
    float: right;
    margin-right: 10px;
    display: inline-block;
  }

  .zhyy-list-tableArea {}

  .zhyy-list-searchArea {}

/****globalCssFinish*****/
div.table-container table.table-layout {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

div.table-container table.table-layout td.table-cell {
  display: table-cell;
  height: 36px;
  border: 1px solid #e1e2e3;
}

div.tab-container {}

.label-left-align :deep(.el-form-item__label) {
  text-align: left;
}

.label-center-align :deep(.el-form-item__label) {
  text-align: center;
}

.label-right-align :deep(.el-form-item__label) {
  text-align: right;
}

.custom-label {}

.static-content-item {
  min-height: 20px;
  display: flex;
  align-items: center;
}

.static-content-item :deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.required .el-form-item__label)::before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label)::before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}










</style>
<style>
    .lui-page{
        padding: 16px 24px;
        background:#fff;
        position: relative;
        height:100%;
        overflow: auto;
        /*margin:16px;*/
        /*box-shadow: 0 0 10px #ccc;*/
    }
    .lui-search-form{
    }
    .lui-search-form .grid-cell{
        max-width: 200px;
        margin-bottom:16px;
    }
    .lui-search-form .el-form-item.el-form-item--default{
        margin-bottom:0;
    }
    .lui-search-form .el-form-item.el-form-item--default .el-input__wrapper{
        border:1px solid rgba(190, 203, 220, 1);
        border-radius: 4px;
        box-shadow: none;
        height:30px;
    }
    .lui-search-form .el-form-item.el-form-item--default .el-input__wrapper::placeholder{
        color:rgba(180, 180, 180, 1);
    }
    .lui-search-form .el-form-item.el-form-item--default .el-input__wrapper.is-focus{
        border:1px solid rgba(119, 170, 255, 1);
        color:rgba(120, 164, 224, 1);
    }
    .lui-search-form .el-cascader .el-input__wrapper{
        padding-right:0;
    }
    .lui-search-form .el-cascader .el-input__wrapper .el-icon.el-input__icon.icon-arrow-down{
        margin:-1px 0;
        height:30px;
        text-align: center;
        width:30px;
        background:rgba(235, 241, 248, 1);
        color:rgba(49, 136, 255, 1);
    }
    .el-button.el-button--primary.el-button--default{
        background:rgba(49, 136, 255, 1);
        border:1px solid rgba(87, 137, 227, 1);
    }
    .el-button.el-button--primary.el-button--default .el-icon{
        color:#fff;
        margin-right:5px;
    }
    .el-button.el-button--primary.el-button--default .el-icon path{
        fill:#fff;
        opacity: 1;
    }
    .el-button.el-button--primary.el-button--default.lui-button-add{
        background:rgba(81, 208, 132, 1);
        border:1px solid rgba(112, 195, 159, 1);
    }
    .lui-table{
        border:1px solid rgba(180, 195, 217, 1);
        border-radius: 4px;
        overflow: hidden;
        color:rgba(102, 102, 102, 1);
    }
    .lui-table.el-table th.el-table-fixed-column--left,
    .lui-table.el-table th.el-table-fixed-column--right,
    .lui-table.el-table th.el-table__cell{
        background:rgba(237, 244, 255, 1)!important;
        color:rgba(51, 51, 51, 1);
    }
    .lui-table.el-table.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
        background:rgba(242, 245, 248, 1)!important;
    }
    .lui-table.el-table.el-table--striped .el-table__body tr.hover-row td.el-table__cell{
        background:rgba(180, 195, 217, 0.5)!important;
    }
    .lui-table.el-table.el-table--striped .el-table__body tr.current-row td.el-table__cell{
        background:rgba(180, 195, 217, 0.5)!important;
    }

    .lui-table.el-table--border .el-table__cell{
        border-right:1px solid rgba(229, 236, 248, 1);
    }
    .lui-table-button{
        height:22px;
        line-height: 22px;
        border-radius: 22px;
        padding:0 15px;
        font-size:12px;
        background:rgba(232, 241, 255, 1);
        border:1px solid rgba(135, 180, 255, 1);
        color:rgba(70, 140, 255, 1);
    }
    .lui-pagination{
        float:right;
        clear: both;
        margin-top:16px;
    }
    .lui-dialog{
        border-radius: 4px;
        overflow: hidden;
    }
    .lui-dialog .el-dialog__header{
        background:rgba(235, 241, 248, 1);
        margin:0;
        height:50px;
        line-height: 50px;
        padding:0 30px
    }
    .lui-dialog .el-dialog__header .el-dialog__title{
        font-size:16px;
        color:#333;
    }
    .lui-dialog .el-dialog__header .el-dialog__headerbtn{
        top:0px;
    }

    .lui-dialog .el-dialog__body{
        padding:0;
    }
    .lui-card-form .grid-cell{
        border-left:1px solid rgba(227, 234, 248, 1);
        border-top:1px solid rgba(227, 234, 248, 1);
    }
    .lui-card-form .grid-row{
        border-right:1px solid rgba(227, 234, 248, 1);
        border-bottom:1px solid rgba(227, 234, 248, 1);
    }
    .lui-card-form{
        padding:20px 30px;
        --el-disabled-text-color: #606266;
    }
    .lui-card-form .el-collapse{
        border:none;
    }
    .lui-card-form .el-form-item{
        background: rgb(248, 251, 255);
    }
    .lui-card-form .el-form-item.el-form-item--default{
        /*height:40px;*/
        margin:0;
    }
    .lui-card-form .el-form-item__label{
        height:100%;
        line-height: 40px;
        padding:0 10px;
        /*border-right:1px solid rgba(227, 234, 248, 1);*/
        border-bottom:1px solid rgba(227, 234, 248, 1);
      box-sizing: border-box;
        margin-bottom:0px;
    }
    .lui-card-form .el-form-item__content{
        border-left:1px solid rgba(227, 234, 248, 1);
        border-bottom:1px solid rgba(227, 234, 248, 1);
        margin-bottom:0px;
      box-sizing: border-box;
    }
    .lui-card-form .el-collapse-item .el-collapse-item__header{
        height:40px;
        line-height: 40px;
        margin-bottom:0;
        border-left:1px solid rgba(227, 234, 248, 1);
        border-top:1px solid rgba(227, 234, 248, 1);
        border-right:1px solid rgba(227, 234, 248, 1);
        background:#fff;
        color:rgba(87, 137, 227, 1);
    }
    .lui-card-form .el-form-item__content{
        overflow: hidden;
    }
    .lui-card-form .el-form-item__content .el-input,
    .lui-card-form .el-form-item__content .el-select{
        width:100%;
        box-sizing: border-box;
        height:40px;
    }
    .lui-card-form .el-form-item__content .el-input .el-input-group__append{
        box-shadow: none;
        border:0;
        box-sizing: border-box;
        height:40px;
        border-radius: 0;
    }
    .lui-card-form .el-radio-group {
        padding:0 10px;
    }
    .lui-card-form .el-form-item__content .el-input.el-date-editor{
        width:100%;
        height:100%;
        box-sizing: border-box;
    }
    .lui-card-form .el-form-item__content .el-input.is-disabled .el-input__wrapper{
        background: rgb(255, 255, 255);
    }
    .lui-page .el-form-item__content .el-input.is-disabled .el-input__wrapper{
      background: rgb(255, 255, 255);
    }
    .lui-page .el-input.is-disabled .el-input__wrapper{
      background: rgb(255, 255, 255);
    }
    .lui-card-form .el-form-item__content .el-input.is-disabled .el-input__wrapper,
    .lui-card-form .el-form-item__content .el-input .el-input__wrapper{
        border:0;
        box-shadow:none;
        height:40px;
        line-height: 40px;
        width:100%;
        border-image-width: 0px!important;
        padding:0 0px 0 11px;
        border-radius: 0;
    }
    .lui-card-form .el-form-item__content .el-select .el-input__wrapper,
    .lui-card-form .el-form-item__content .el-select .is-focus .el-input__wrapper{
        border:none!important;
        border-radius: 0;
        box-shadow: none!important;
    }

    .lui-card-form .el-select .el-input__wrapper {
        padding-right:0;
    }
    .lui-card-form .el-select .el-input__wrapper .el-input__suffix{
        margin:-1px 0;
        height:40px;
        width:30px;
        text-align: center;
        background:rgba(235, 241, 248, 1);
        color:rgba(49, 136, 255, 1);
        transform: rotate(180deg);
    }
    .lui-card-form .el-select .el-input__wrapper .el-input__suffix .el-input__suffix-inner .el-icon{
        color:rgba(49, 136, 255, 1);
    }

    .lui-card-form .el-cascader{
        width:100%;
    }
    .lui-card-form .el-cascader .el-input__wrapper {
        padding-right:0;
    }
    .lui-card-form .el-cascader .el-input__wrapper .el-icon.el-input__icon.icon-arrow-down{
        margin:-1px 0;
        height:40px;
        text-align: center;
        width:30px;
        background:rgba(235, 241, 248, 1);
        color:rgba(49, 136, 255, 1);
    }

    .el-upload--picture-card{
      background-color: white;
      border: none;
    }
    .el-upload--picture-card .el-icon{
      background-color: white;
      border: 1px dashed rgba(140, 147, 157, 0.4);
      width: 20px;
      height: 20px;
      padding: 5px;
      border-radius: 3px;

    }

    .lui-card-form .grid-cell.picture-cell .el-form-item.el-form-item--default{
        height:150px;
    }
    .lui-card-form .grid-cell.picture-cell .el-form-item.el-form-item--default .el-form-item__content{
        display: block;
      background-color: white;
    }
    .lui-card-form .el-collapse-item__wrap{
        border-bottom:none;
    }
    .lui-card-form .el-collapse-item{
        margin-bottom:2px;
    }

    .lui-card-form .grid-cell .el-form-item.el-form-item--default .el-textarea__inner{
        border-radius: 0;
        border:0!important;
        box-shadow: none;
        line-height: 24px;
        padding:8px 11px;
        height:100%;
        resize:none;
    }
    .lui-card-form .grid-cell .el-form-item.el-form-item--default .el-textarea{
        height:100%;
    }
    .lui-card-form .el-form-item__content .el-textarea.is-disabled .el-textarea__inner{
        background:rgb(255, 255, 255);
    }

    .lui-card-form  .el-collapse-item__content {
        padding-bottom:14px;
    }
    .lui-card-form  .el-date-editor.el-date-editor--daterange.el-input__wrapper{
        height:40px;
        border:none;
        box-shadow:none;
    }

     .el-form-item__error {
      top: unset;
      right: 30%;
      left: unset;
    }
    input:disabled::-webkit-input-placeholder{
      -webkit-text-fill-color: rgba(255,255,255,0);
    }

    textarea:disabled::-webkit-input-placeholder{
      -webkit-text-fill-color: rgba(255,255,255,0);
    }


    .no-border-bottom .el-form-item__label{
      border-bottom: none;
    }

    .lui-card-form .el-input__inner{
      z-index: 1;
    }

    .lui-card-form .el-form-item__error{
      z-index: 0;
    }



</style>
