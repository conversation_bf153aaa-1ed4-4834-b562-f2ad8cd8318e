/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require("fs");
const http = require("http");
const handlebars = require("handlebars");
const config = require("./config");

// 读取配置 初始化Map

const _apiFilePath = `${__dirname}/${config.apiFilePath}`;
const _apiFileType = `${config.apiFileType}`;

const getJson = (url) => {
  return new Promise((resolve) => {
    let json = "";
    http.get(url, (res) => {
      res.setEncoding("utf-8");
      res
        .on("data", (data) => {
          json += data;
        })
        .on("end", () => {
          resolve(JSON.parse(json));
        });
    });
  });
};

// 建立目录
if (!fs.existsSync(_apiFilePath)) {
  fs.mkdirSync(_apiFilePath);
}

// 读取模板文件
const apiTemplateFileName = `${__dirname}/${config.templatePath}/api-${_apiFileType}.hbs`;
const apiCompiler = handlebars.compile(fs.readFileSync(apiTemplateFileName, "utf-8"));

//制定模板处理函数
handlebars.registerHelper("isDiffRef", (name, refName) => {
  return name != refName;
});
handlebars.registerHelper("lowerCase", (str) => {
  return str.toLowerCase();
});
handlebars.registerHelper("isPost", (str) => {
  return str == "post"
});
handlebars.registerHelper("isGet", (str) => {
  return str == "get"
});
handlebars.registerHelper("isNew", (str) => {
  return str == "new"
});





function lazy(index) {
  return new Promise((r, j) => {
    setTimeout(() => {
      r(index)
    }, 5000);
  })
}
// 保存api文件
const saveApi = ({
  data,
  path,
  method,
  moduleName
}) => {
  // if (!fs.existsSync(`${_apiFilePath}/${moduleName}`)) {
  //   fs.mkdirSync(`${_apiFilePath}/${moduleName}`);
  // }
  // console.log(moduleName);
  // return
  fs.writeFileSync(
    `${_apiFilePath}/${moduleName}.${config.apiFileType}`,
    apiCompiler({
      data,
      moduleName
    })
  );
}

const getPath = (data, source, name = '', path = '') => {
  // source.forEach(item => {
    if (source.children.length) {
      source.children.forEach(item => getPath(data, item, `${name ? name + '-' : ''}${source.node.name}`, `${path}${source.node.path ? '/' + source.node.path.replace(/^\//,'') : ''}`))      
    } else {
      // console.log(source)
      const method = source.node.method?.toLowerCase();
      if(!method) return
      const url = `${path}/${source.node.path.replace(/^\//,'')}`.replace(/\{(\w+)\}/g, '${params.$1}');
      const isExport = url.endsWith('/export');
      data.push({
        description: `${name}-${source.node.name}`,
        "function": `${source.node.method.toLowerCase()}${path ? path.match(/\/(\w+)$/)[1]?.replace(/\b(\w)|\s(\w)/g, m => m.toUpperCase()) ?? '' : ''}${source.node.path.replace(/\b(\w)|\s(\w)/g, m => m.toUpperCase()).replace(/\//g,'').replace(/(\{(\w+)\})|(-)/g, '')}`,
        url,
        method: method == "delete" ? 'del' : isExport ? 'downloadFile' : method,
        isExport
      })
    }

  // })

}
// 处理不用模块的json文件
config.modules.forEach((module) => {
  const data = [];
  const execFunc = (swaggerJson) => {
    // 执行解析函数
    swaggerJson.forEach(item => {
      // console.log(item.node.path)
      const module = {
        data: [],
        moduleName: item.node.path
      }
      /* item.children.forEach(subItem => {
        subItem.children.forEach(cItem => {
          console.log(cItem.node)
          const method = cItem.node.method.toLowerCase();
          module.data.push({
            description: `${item.node.name}-${subItem.node.name}-${cItem.node.name}`,
            "function": `${cItem.node.method.toLowerCase()}${cItem.node.path.replace(/\b(\w)|\s(\w)/g, m => m.toUpperCase()).replace(/\//g,'').replace(/(\{(\w+)\})|(-)/g, '')}`,
            url: `${item.node.path}${subItem.node.path}${cItem.node.path}`.replace(/\{(\w+)\}/g, '${params.$1}'),
            method: method == "delete" ? 'del' : method,
          })
        })
      }) */
      getPath(module.data, item)
      data.push(module)
    })
    console.log(data)
    data.forEach(item => {
      saveApi(item)
    })
  };
  (async() => {
    const data = await getJson(module.generatorSource)
    execFunc(data.data.api.children)
  })()
  // execFunc(require(module.generatorSource));
});