<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
  pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function Save() {
  pageofficectrl.SaveFilePage = "/SendParameters/save?id=1";
  //在这里写您保存前的代码
  pageofficectrl.WebSave();
  //在这里写您保存后的代码，比如判断保存结果pageofficectrl.CustomSaveResult
  alert(pageofficectrl.CustomSaveResult);
}

function openFile() {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/SendParameters/Word',
    method: 'get',
  });
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit, Save };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <div class="Word">
    <div style="font-size: 14px;">
      <div style="border:1px solid black;">PageOffice给保存页面传值的三种方式：<br />
        <span style="color: Red;">1.通过设置保存页面的url中的?给保存页面传递参数：</span><br />
        &nbsp;&nbsp;&nbsp;例:pocCtrl.setSaveFilePage("save?id=1");<br />
        &nbsp;&nbsp;&nbsp;保存页面获取参数的方法：<br />
        &nbsp;&nbsp;&nbsp;通过save(HttpServletRequest request, HttpServletResponse response, int id)参数名称映射<br />

        <span style="color: Red;">2.通过input隐藏域给保存页面传递参数：</span><br />
        &nbsp;&nbsp;&nbsp;例:当前页面有一个隐藏的input，name为：age，value为：25<br />
        <input id="age" name="age" type="hidden" value="25" />
        <br />
        &nbsp;&nbsp;&nbsp;保存页面获取参数的方法：<br />
        &nbsp;&nbsp;&nbsp;String age=fs.getFormField("age");<br />
        &nbsp;&nbsp;&nbsp;<span
          style="color: Red;">注意：获取Form控件传递过来的参数值，fs.getFormField("参数名")方法中的参数名是当前控件的“id”属性。</span><br>

      </div>

    </div>
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
  </div>
</template>
