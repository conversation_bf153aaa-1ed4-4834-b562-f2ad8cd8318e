<template>
  <el-form class="lui-card-form" :model="formData" ref="saveFormRef" :rules="rules" label-width="120px" status-icon
    size="default">
    <el-row>
      <div class="button-box">
        <el-button type="primary" @click="handleSubmit()">提交</el-button>
        <el-button type="info" @click="handleClose()">关闭</el-button>
      </div>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="企业名称">
          <el-input v-model="formData.cbsmc" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="队伍名称">
          <el-input v-model="formData.dwmc" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="队伍状态">
          <el-select v-model="formData.dwzt" disabled>
            <el-option v-for="item in dqztOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="投标资格证号">
          <el-input v-model="formData.zrzh" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="有效期">
          <span style="padding-left: 11px;">{{ formData.yxqks.split(' ')[0] }} - {{ formData.yxqjs.split(' ')[0]
          }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="投标范围">
          <el-table :data="tableData">
            <el-table-column label="专业" prop="zymc" header-align="center" align="center"></el-table-column>
            <el-table-column label="当前状态" prop="bgqzt" header-align="center" align="center">
              <template #default="scope">
                {{dqztOptions.find(item => item.value === scope.row.bgqzt)?.label}}
              </template>
            </el-table-column>
            <el-table-column label="当前状态生效时间" header-align="center" align="center"></el-table-column>
            <el-table-column label="状态变更为" header-align="center" align="center">
              <template #default="scope">
                <el-select v-model="scope.row.bghzt">
                  <el-option v-for="item in dqztOptions" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="变更原因" prop="bgyy" class="custom-label">
          <el-input type="textarea" :rows="2" placeholder="请输入变更原因" v-model="formData.bgyy"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="相关附件">
          <vsfileupload class="custom-upload" v-if="formData.dwywid" :key="formData.dwywid" :busId="formData.dwywid"
            :busType="'xbcbsrcgl'" :ywlb="'xbcbsrcgl'"></vsfileupload>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div>
    <el-dialog title="选择专业" v-model="dialogVisible" v-if="dialogVisible" append-to-body>
      <xzzy />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import vsfileupload from "@views/components/vsfileupload";
import { queryDwxxAndZyztList, queryDwxxAndZyxxByJl, createRgczjl, createRgczjlZy, updateRgczjlZy } from "@/api/xbcbsrcgl.js";
import vsflow from "@views/vsflow/index.js";
import comFun from "@src/lib/comFun.js";
import VSAuth from "@src/lib/vsAuth";

const state = reactive({
  formData: {
    dwywid: "",
    cbsmc: "",
    dwmc: "",
    dwzt: "",
    tbzgzh: "", // 投标资格证号
    yxqks: "",
    yxqjs: "",
    bgyy: "",
  },
  tableData: [],
  // 状态候选项
  dqztOptions: [
    { value: "1", label: "正常" },
    { value: "2", label: "暂停投标" },
    { value: "3", label: "停工整顿" },
    { value: "4", label: "取消投标资格" },
  ],
  fromFlow: false,
});

const { formData, tableData, dqztOptions } = toRefs(state);

const props = defineProps({
  params: {
    type: Object,
    default: () => { },
  },
});

const emit = defineEmits(['handleClose']); // 定义关闭事件

const queryData = () => {
  const params = {
    jlid: props.params.processInstanceId,
    dwywid: props.params.dwywid
  }
  // 如果传递的参数中存在 processId 不为空，则是通过流程进的
  if (props.params.processId) {
    state.fromFlow = true;
    queryDwxxAndZyxxByJl(params).then((res) => {
      if (res.data) {
        formData.value = res.data.formData;
        formData.value.bgyy = res.data.yy;
        tableData.value = res.data.tableData;
      }
    })
  } else {
    queryDwxxAndZyztList(params).then((res) => {
      if (res.data) {
        formData.value = res.data.formData;
        formData.value.bgyy = res.data.yy;
        tableData.value = res.data.tableData;
      }
    })
  }
}

// 创建记录
const createJl = (jlid) => {
  const params = {
    jlid: jlid,
    wybsid: formData.value.dwwybs, // 唯一标识
    yy: formData.value.bgyy,
    czrid: VSAuth.getAuthInfo().permission.userId,
    czrmc: VSAuth.getAuthInfo().permission.userName,
    zt: "1"
  };
  createRgczjl(params).then(() => {
    console.log("创建成功");
  }).catch((error) => {
    console.log("创建失败", error);
  });
}

// 创建专业变更记录
const createZyjl = (jlid) => {
  tableData.value.forEach(item => { item.jlid = jlid; item.zyjlid = comFun.newId(); });
  createRgczjlZy(tableData.value).then(() => {
    console.log("创建专业状态变更记录成功");
  }).catch((error) => {
    console.log("创建失败");
  });
}

// 更新专业变更记录
const updateZyjl = (jlid) => {
  tableData.value.forEach(item => { item.jlid = jlid; });
  updateRgczjlZy(tableData.value).then(() => {
    console.log("更新成功");
  }).catch((error) => {
    console.log("更新失败", error);
  })
}

const createTask = async (jlid) => {
  return vsflow.createTask(
    vsflow.processData.xbcbszyztbg.processId,
    formData.value.dwmc + "队伍专业状态变更流程",
    VSAuth.getAuthInfo().permission.userLoginName,
    jlid,
    null,
    null,
    1,
    null
  );
}

const finishTask = async () => {
  return vsflow.finishTask(
    props.params.taskId,
    VSAuth.getAuthInfo().permission.userLoginName,
    null,
    null,
    null,
    null,
    1,
    null,
  );
}

const handleSubmit = async () => {
  try {
    let jlid = null;
    let taskResult = {};
    // 从流程进来
    if (state.fromFlow) {
      jlid = props.params.processInstanceId;
      updateZyjl(jlid);
      taskResult = await finishTask();
    } else {
      jlid = comFun.newId();
      createJl(jlid);
      createZyjl(jlid);
      taskResult = await createTask(jlid);
    }
    if (taskResult.success) {
      ElMessage.success("提交成功");
      handleClose();
    } else {
      ElMessage.error("流程发起失败");
    }
  } catch (error) {
    debugger;
    ElMessage.error("提交过程中发生错误");
  }
}

// 关闭表单
const handleClose = () => {
  emit('handleClose');
};

// 子组件暴露方法
defineExpose({
  handleSubmit

});

onMounted(() => {
  queryData();
})

</script>

<style scoped>
.button-box {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0.5rem;
}
</style>