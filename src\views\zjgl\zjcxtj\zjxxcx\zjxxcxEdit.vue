<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="150px"
           size="default">
    <el-collapse v-model="openCollapse">
      <jbxx-card v-model:form-data="formData" :params="params"/>
      <el-collapse-item title="专业信息" name="1">
        <zyxx-card v-model:form-data="formData" :params="params"/>
      </el-collapse-item>
      <el-collapse-item title="扩展信息" name="2">
        <kzxx-card v-model:form-data="formData" :params="params"/>
      </el-collapse-item>
      <el-collapse-item title="汇总计数信息" name="3">
        <el-row :gutter="0" class="grid-row">
          <el-col :span="8" class="grid-cell">
            <el-form-item label="被抽取次数:" prop="XCSZYBM">
              <div style="margin-left: 10px">0</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="出席次数:" prop="CSZYGZSJ" >
              <div style="margin-left: 10px">0</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="从事年限:" prop="ZJLX" >
              <div style="margin-left: 10px">0</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="有无签名照片:" prop="ZJLX" >
              <div style="margin-left: 10px">无</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="是否有效:" prop="ZJLX" >
              <div style="margin-left: 10px">有</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
<!--    <div style="display:flex;justify-content: center;gap: 20px;margin-top: 20px" v-if="editable">-->
<!--      <div class="static-content-item">-->
<!--        <el-button type="primary" @click="validateForm('save')">暂存</el-button>-->
<!--      </div>-->
<!--      <div class="static-content-item">-->
<!--        <el-button type="primary" @click="validateForm('submit')">提交</el-button>-->
<!--      </div>-->
<!--      <div class="static-content-item">-->
<!--        <el-button @click="closePage">返回</el-button>-->
<!--      </div>-->
<!--    </div>-->
<!--    <el-dialog z-index="1000" v-model="dialogVisible" title="队伍专业选择" width="800px" class="dialogClass" top="1vh"-->
<!--               append-to-body>-->
<!--      <zyxz v-if="dialogVisible" @close="closeDialog" @parentMethod="getCheck"></zyxz>-->
<!--    </el-dialog>-->
<!--    <el-dialog z-index="1000" v-model="inputPasswordShow" title="设置密码" width="300px" v-if="inputPasswordShow">-->
<!--      <div>-->
<!--        <el-input  v-model="formData.DZQMMM" type="password"-->
<!--                   placeholder="请输入密码"-->
<!--                   show-password clearable></el-input>-->
<!--      </div>-->
<!--      <template #footer>-->
<!--        <el-button type="primary" @click="inputPasswordShow = false">-->
<!--          关闭-->
<!--        </el-button>-->
<!--      </template>-->
<!--    </el-dialog>-->

  </el-form>

</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted, watch
}
  from 'vue'
import {Plus} from '@element-plus/icons-vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import KzxxCard from "@views/zjgl/zjcrk/compoents/kzxxCard";
import LlxxCard from "@views/zjgl/zjcrk/compoents/llxxCard";
import zyxxCard from "@views/zjgl/zjcrk/compoents/zyxxCard";
import jbxxCard from "@views/zjgl/zjcrk/compoents/jbxxCard";
import Vsfileupload from "../../../components/vsfileupload";
import VsfileuploadPic from "../../../components/vsfileuploadPic";
import VsFileUploadTable from "../../../components/vsFileUploadTable";
import vsAuth from "../../../../lib/vsAuth";
import axiosUtil from "../../../../lib/axiosUtil";
import comFun from "../../../../lib/comFun";

export default defineComponent({
  components: {LlxxCard, KzxxCard, VsfileuploadPic, Vsfileupload,VsFileUploadTable,jbxxCard,zyxxCard},
  props: {
    params: {
      required: true,
      type: Object
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: false,
      openCollapse:['1','2'],
      userInfo: vsAuth.getAuthInfo().permission,
      Plus: Plus,
      formData: {
        XM: null,
        XB: null,
        SFZH: null,
        GZDW: null,
        XL: null,

        DZQMMM:null,
        XCSZYBM: null,
        CSZYGZSJ: null,
        ZJLX: null,
        SBZYMX: [],

        ZJLB: null,
        ZJJB: null,

        RKZJBXX: {},

        tableData1: [],
        tableData2: [{LX: '专家工作经历证明', WJMC: ''}],

      },
      rules: {
        XM: [{
          required: true,
          message: '字段值不可为空',
        }],
        XB: [{
          required: true,
          message: '字段值不可为空',
        }],
        SFZH: [{
          required: true,
          message: '字段值不可为空',
        }],
        GZDW: [{
          required: true,
          message: '字段值不可为空',
        }],
        'RKZJBXX.CSNY': [{
          required: true,
          message: '字段值不可为空',
        }],
        XL: [{
          required: true,
          message: '字段值不可为空',
        }],
        // XCSZYBM: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // CSZYGZSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJLX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // SBZY: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJLB: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJJB: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJBH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZC: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BGDH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZZDH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // DZYX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // TXDZ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // MQSZD: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BYYX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // SXZY: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BYSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // CJGZSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZYZG: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZYZGZS: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZZZT: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // XZZW: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // pictureupload90411: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // pictureupload40039: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
      },
      XBOptions: [],
      XLOptions: [],
      XCSZYOptions: [],
      ZJLXOptions: [],
      ZJLBOptions: [],
      ZJJBOptions: [],
      ZCOptions: [],
      ZZZTOptions: [],

      dialogVisible: false,
      inputPasswordShow: false

    })
    const getFormDate = (ZJBS) => {
      axiosUtil.get('/backend/zjgl/zjcxtj/selectZjjbxxMX', {ZJBS}).then((res) => {
        if (res.data && res.data.length > 0) {
          state.formData = {...state.formData, ...res.data[0]}
        }
      })
    }
    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }
    const getZYData = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy',{}).then((res) => {
        state.XCSZYOptions=comFun.treeData(res.data,'ZYBM','FZYBM','children','0')
      });
    }
    const instance = getCurrentInstance()
    const validateForm = (type) => {
      if (type === 'submit') {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            //TODO: 提交表单
            submitForm(type)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
          }
        })
      } else if (type === 'save') {
        submitForm(type)
      }

    }
    const closePage = () => {
      emit('closeDialog')
    }
    const submitForm = (type) => {
      let params
      if (props.params.type === 'add') {
        let GLZJID = comFun.newId()
        params = {
          GLZJID,
          SQSJ: comFun.getNowTime(),
          CJR: state.userInfo.userLoginName,
          CJSJ: comFun.getNowTime(),
          HDFL:'RK',
          MX: {
            ...state.formData,
            GLZJID,
            ZJBS:comFun.newId()
          }
        }
      } else if (props.params.type === 'edit') {
        params = {
          GLZJID: state.formData.GLZJID,
          XGR: state.userInfo.userLoginName,
          XGSJ: comFun.getNowTime(),
          MX: {
            ...state.formData,
          }
        }
      }
      params.SHZT= type === 'submit' ? '2' : '0'
      params.MX.ZJZT= type === 'submit' ? '1' : '0'
      params.MX.ZJBH=params.MX.RKZJBXX.ZJBH
      params.MX.RYZH=params.MX.RKZJBXX.ZH
      params.MX.ZC=params.MX.RKZJBXX.ZC
      params.MX.XCSZYBM=params.MX.RKZJBXX.XCSZYBM
      axiosUtil.post('/backend/zjgl/zjrkgl/saveRkzj', params).then(res => {
        if (res.message === 'success') {
          emit('closeDialog')
          ElMessage({
            message: `${type === 'submit' ? '提交' : '保存'}成功`,
            type: 'success',
          })
        }
      })

    }
    const setPassword = () => {
      state.inputPasswordShow=true
    }
    const resetForm = () => {
      instance.proxy.$refs['vForm'].resetFields()
    }
    const addLLXX = () => {
      state.formData.tableData1.push({
        ZJLLID: comFun.newId(),
        WJID: props.params.id,
        QZRQ: null,
        GZDW: null,
        ZW: null,
        GZJJ: null
      })
    }
    const deleteRow = (index, data) => {
      data.splice(index, 1);
      // RealDelete(data)//对tableData中的数据删除一行
    }
    const closeDialog = () => {
      state.dialogVisible = false
    }
    const getCheck = (e,ZZY) => {
      console.log(e,ZZY)
      let res = []
      e.forEach(item => {
        res.push({
          ZJXGZYBS: comFun.newId(),
          WJID: props.params.id,
          SFZZY: item.ZYBM===ZZY ? '1' : '0',
          ZYBM: item.ZYBM,
          ZYMC: item.ZYMC
        })
      })
      state.formData.SBZYMX=res
      state.dialogVisible=false

    }
    const getZJBH = () => {
      return comFun.newId()
    }
    onMounted(() => {
      getZYData()
      getDMBData('XB', 'XBOptions')
      getDMBData('XL', 'XLOptions')
      getDMBData('ZJLX', 'ZJLXOptions')
      getDMBData('ZJLB', 'ZJLBOptions')
      getDMBData('ZJJB', 'ZJJBOptions')
      getDMBData('ZC', 'ZCOptions')
      getDMBData('ZZZT', 'ZZZTOptions')

      // state.formData.ZJBS = props.params.id
      state.editable = false
      getFormDate(props.params.id)
      // if (props.params.type !== 'add') {
      //   getFormDate(props.params.id)
      // }else if(props.params.type === 'add'){
      //   state.formData.RKZJBXX.ZJBH=getZJBH()
      // }
    })
    watch(()=>state.formData.SBZYMX,(newValue)=>{
      let inputShow = []
      newValue.forEach(item => {
        if(item.SFZZY==='1'){
          inputShow.push(item.ZYMC+'(主专业)')
        }else {
          inputShow.push(item.ZYMC)
        }

      })
      state.formData.RKZJBXX.SBZY=inputShow.join('、')
    })
    return {
      ...toRefs(state),
      validateForm,
      submitForm,
      resetForm,
      addLLXX,
      deleteRow,
      closePage,
      closeDialog,
      getCheck,
      setPassword
    }
  }
})

</script>


<style scoped>
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: unset;
}

/deep/ .el-collapse-item__header {
  background-color: #F2F3F5;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
}
:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}
</style>
