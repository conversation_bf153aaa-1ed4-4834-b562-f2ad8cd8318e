<template>
  <div style="height: 250px; padding: 20px">
    <el-form
      ref="cbsshFormRef"
      size="normal"
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="100px"
    >
      <el-form-item prop="SHYJ" label="退回原因：">
        <el-input
          v-model="form.SHYJ"
          :rows="3"
          maxlength="50"
          type="textarea"
          placeholder="请输入"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <div class="bottom-Button">
      <el-row class="btn" style="justify-content: center">
        <el-button type="primary" @click="submit">确定</el-button>
        <el-button @click="closeRefuse">返回</el-button>
      </el-row>
    </div>
  </div>
</template>
<script setup>
import outerBox from "@src/components/common/outerBox.vue";
import axiosUtil from "@lib/axiosUtil";
import { ElMessage } from "element-plus";
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import { postExamineSendBack } from "@src/api/sccbsgl.js";
import { auth } from "@src/assets/core/index";
const emits = defineEmits(["closeRefuseDialog", "finish"]);
const props = defineProps({
  defaultData: {
    type: Object,
    default: () => ({
      TYPE: null,
      DATAID: null,
    }),
  },
});

const form = reactive({
  SHYJ: "",
});
watch(
  () => props.defaultData,
  (val) => Object.assign(form, val),
  {
    immediate: true
  }
);
const rules = reactive({
  SHYJ: [
    {
      required: true,
      trigger: "blur",
      message: "请输入退回理由",
    },
  ],
});
const cbsshFormRef = ref(null);
const submit = () => {
  cbsshFormRef.value
    .validate()
    .then((result) => {
      // ElMessage.success("校验成功");
      const { TYPE, DATAID } = props.defaultData;
      // console.log(TYPE, DATAID);
      // return
      const userInfo = auth.getPermission();
      // 保存退回
      postExamineSendBack({
        TYPE,
        DATAID,
        SHRZH: userInfo.userLoginName,
        SHRXM: userInfo.userName,
        SHYJ: form.SHYJ,
      })
        .then((result) => {
          ElMessage.success("退回成功");
          emits("finish");
        })
        .catch((err) => {
          ElMessage.error("退回失败");
        });
    })
    .catch((err) => {});
};
const closeRefuse = () => {
  emits("closeRefuseDialog");
};
</script>
<style src="../style/index.css" scoped></style>
<style scoped>
.bottom-Button {
  width: 100%;
  position: absolute;
  bottom: 20px;
}
</style>
