<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');
const count=ref(0);

function updateCount(value) {
 count.value =count.value + parseInt(value);
  document.getElementById("Text1").value =count.value;
  return count.value.toString();
}

function ShowModalDlg() {
  pageofficectrl.ShowHtmlModalDialog(
    "../HtmlDialog/Modal",
    "abcdefg",
    "left=300px;top=390px;width=560px;height=410px;"
  )
}

function ShowModelessDlg() {
  pageofficectrl.ShowHtmlModelessDialog(
    "../HtmlDialog/Modeless",
    "123456",
    "left=300px;top=390px;width=560px;height=410px;"
  )
}

function OnPageOfficeCtrlInit() {
  pageofficectrl.AddCustomToolButton("弹出模态窗口", "ShowModalDlg()", 0);
  pageofficectrl.AddCustomToolButton("弹出非模态窗口", "ShowModelessDlg()", 0);
}

function openFile() {
  return request({
    url: '/HtmlDialog/Word',
    method: 'get',
  })
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit,updateCount,ShowModalDlg,ShowModelessDlg };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <div class="Word">
    <div>Count=<input id="Text1" type="text" value="0">
    </div>
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
  </div>
</template>
