<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function getSelectionText() {
	if (pageofficectrl.word.GetTextFromSelection() != "") {
		alert(pageofficectrl.word.GetTextFromSelection());
	}
	else {
		alert("您没有选择任何文本。");
	}
}

function OnPageOfficeCtrlInit() {
	pageofficectrl.CustomToolbar = false;
}

function openFile() {
	return request({
		url: '/WordGetSelection/Word',
		method: 'get',
	})
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit,  getSelectionText };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<div style="font-size: 12px; line-height: 20px; border-bottom: dotted 1px #ccc; border-top: dotted 1px #ccc;
		        padding: 5px;">
			<span style="color: red;">操作说明：</span>选中word文件中的一段文字，然后点“获取选中文字”按钮。<br />
			关键代码：看js函数<span style="background-color: Yellow;">getSelectionText()</span>
		</div>

		<input id="Button1" type="button" @click="getSelectionText();" value="js获取word选中的文字" /><br />

		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
