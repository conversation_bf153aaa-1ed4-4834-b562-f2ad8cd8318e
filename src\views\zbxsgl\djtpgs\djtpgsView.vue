<template>
  <div style="display: flex;justify-content: center">
    <div id="pdf-context" style="font-family: 宋体;width: 800px;font-size: 18px">
      <h2 style="width: 100%;text-align: center"> 关于{{formData.XMXX.SSDWMC}}推荐{{formData.XMXX.XMMC}}的独家公示</h2>
      <p style="text-indent: 4ch;">经{{formData.XMXX.SSDWMC}}推荐，相关部门按照独家采购审查流程进行了 审查，{{formData.XMXX?.DWList[0]?.DWMC}}的{{formData.XMXX.XMMC}}服务，根据《内控制度》有关 要求，现将独家选用情况予以公示。
      </p>

      <p style="text-indent: 4ch;">一、公示时间
      </p>

      <p style="text-indent: 4ch;">公示时间为5个工作日，自公告发布之日计算
      </p>

      <p style="text-indent: 4ch;">二、联系方式
      </p>

      <p style="text-indent: 4ch;">选商单位：{{formData.XMXX.SSDWMC}}
      </p>

      <p style="text-indent: 4ch;">联系人：{{formData.LXR}}/{{formData.LXFS}}
      </p>

      <p style="text-indent: 4ch;">纪检监督审计部：{{formData.JJLXR}}/{{formData.JJLXFS}}
      </p>


      <p style="text-indent: 4ch;">三、有关要求
      </p>

      <p style="text-indent: 4ch;">公示期内，各单位如对独家选用理由的真实性有不同意见，可采取书面以单位名义反映，要加盖公章：以个人名义反映的，应署明真实姓名、单位、联系方式等。反映意见和问题要客观公正，实事求是。
      </p>

      <p style="display:flex;justify-content: flex-end">{{formData.XMXX.SSDWMC}}</p>
      <p style="display:flex;justify-content: flex-end">{{dataToCh(formData.CJSJ)}}</p>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsFileUploadTable from "@views/components/vsFileUploadTable";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GGID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        XMXX: {
          DWList:[{}]
        }
      },
      rules: {
        LXR: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXFS: [{
          required: true,
          message: '字段值不可为空',
        }],
        JJLXR: [{
          required: true,
          message: '字段值不可为空',
        }],
        JJLXFS: [{
          required: true,
          message: '字段值不可为空',
        }],

      },



      dialogVisible: false,
      ggParams: {},
      fileTableData: []
    })

    const getFormData = () => {
      let params = {
        GGID: state.GGID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/xstzfb/selectXstzById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      })
    }

    const dataToCh = (data) => {
      if(!data){
        return ''
      }
      let _data=data.substring(0,10)
      let dataList=_data.split('-')
      return dataList[0]+'年'+dataList[1]+'月'+dataList[2]+'日'
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()

    })

    return {
      ...toRefs(state),
      closeForm,
      dataToCh

    }
  }

})
</script>

<style scoped>

</style>
