<!-- 选择专业及区域 -->
<template>
    <el-form size="default" class="lui-page">
        <el-row class="add-btn" align="center" justify="end" style="margin-bottom: 10px">
            <el-button type="primary" @click="addRow">增加</el-button>
        </el-row>
        <el-table :data="props.specialities" height="calc(100vh - 300px)" border class="lui-table">
            <el-table-column label="专业名称" prop="zymc" header-align="center" align="center">
                <template #default="scope">
                    <div>
                        <el-select v-model="scope.row.zyid" clearable @change="zymc(scope.row,scope.$index)">
                            <el-option v-for="(item, index) in props.zyOptions" :key="index" :label="item.label"
                                       :value="item.value"
                                       :disabled="selZy.includes(item.value)"></el-option>
                        </el-select>
                    </div>
                </template>
            </el-table-column>
            <!-- <el-table-column label="服务区域" prop="fwqy" header-align="center" align="center">
              <template #default="scope">
                <div>
                  <el-select v-model="scope.row.fwid" clearable placeholder="" @change="fwqy(scope.row)">
                    <el-option v-for="(item, index) in data.fwqyOptions" :key="index" :label="item.label"
                               :value="item.value"></el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column> -->
            <el-table-column
                    label="服务区域"
                    prop="FWQY"
                    header-align="center"
                    align="center"

            >
                <template v-slot="scope">
                    <!--<el-select v-model="scope.row.FWQY" clearable placeholder="请选择" v-if="props.zyOptions[0].ZYFL === 'C'">
                      <el-option
                          v-for="(item, index) in fwqyOptions"
                          :key="index"
                          :label="item.ORGNA_NAME"
                          :value="item.ORGNA_ID"
                      ></el-option>
                    </el-select>-->
                    <!--<el-checkbox size="small" v-if="props.zyOptions[0].ZYFL === 'C'"
                                 v-model="scope.row.checkAll"
                                 :indeterminate="scope.row.isIndeterminate"
                                 @change="(value)=>handleCheckAllChange(value,scope.row)">
                      全选
                    </el-checkbox>-->
                    <el-checkbox-group v-if="props.zyOptions[0].ZYFL === 'C'" v-model="scope.row.fwid" size="small">
                        <el-checkbox v-for="(item, index) in fwqyOptions"
                                     @change="(value)=>handleCheckedCitiesChange(item,scope.row, scope.$index)"
                                     :key="index"
                                     :label="item.ORGNA_ID">{{item.ORGNA_NAME}}
                        </el-checkbox>
                        <!--              <el-checkbox label="QT">-->
                        <!--                <el-input v-if="scope.row.FWQY&&scope.row.FWQY.includes('QT')" v-model="scope.row.QTFWQYMC" placeholder="请输入内容"></el-input>-->
                        <!--                <div v-else>其它</div>-->
                        <!--              </el-checkbox>-->
                    </el-checkbox-group>
                    <span v-else>全局</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" header-align="center" align="center" width="100">
                <template #default="scope">
                    <div>
                        <el-button class="lui-table-button" @click="handleDeleteRow(scope.row, scope.$index)">删除
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-row class="btn" justify="end" align="center">
            <!--      <el-button type="primary" @click="goback">上一步</el-button>-->
            <el-button type="primary" @click="submit">确定</el-button>
        </el-row>
    </el-form>
</template>

<script setup>
    import axiosUtil from "@lib/axiosUtil";
    import outerBox from "@components/common/outerBox.vue"
    import {ElMessage, ElMessageBox} from 'element-plus'
    import {ref, computed, onMounted, reactive, defineProps, defineEmits} from 'vue'
    import {getCommonGetUserByParam, getCommonSelectDMB} from '@src/api/cbsxx.js'
    import {getCommonsSelectDMB} from "@src/api/gcyztb.js";
    import {v4 as uuidv4} from "uuid";
    import {postCbsyjSaveXzqy} from "@src/api/sccbsgl.js";

    let props = defineProps({
        zyOptions: {
            type: Array,
            default: []
        },
        specialities: {
            type: Array,
            default: []
        },
        specialbk: {
            type: Array,
            default: []
        },
        dialogTitle: {
            type: String,
        }
    })
    const zymc = (row, index) => {
        console.log(index)
        row.zymc = props.zyOptions.filter(x => x.value == row.zyid)[0]?.label
        row = {
            ...row,
            yszyid: row.zyid,
            ...(props.zyOptions.filter(x => x.value == row.zyid)[0])
        }
        props.specialities[index] = {
            ...row,
            ...(props.zyOptions.filter(x => x.value == row.zyid)[0])
        }
    }
    // const fwqy = (row)=>{
    //     row.fwqy = data.fwqyOptions.filter(x=>x.value == row.fwid)[0].label
    // }
    let fwqyOptions = ref([]);
    const data = reactive({
        fwqyOptions: []
    })
    const emits = defineEmits(['return', 'sure', 'submitSpeciality'])
    let selZy = computed(() => {
        if (props.specialities.length) {
            return props.specialities.map(item => item.zyid)
        } else {
            return []
        }
    })

    const goback = () => {
        emits('goTeamBack')
    }

    const getArea = () => {
        axiosUtil.get('/backend/common/selectTwoOrgAndJgbmList', {}).then((res) => {
            fwqyOptions.value = res.data;
        });
        // getCommonsSelectDMB({
        //     DMLBID: "FWQY",
        // })
        //     .then(({ data }) => {
        //         // console.log(data);
        //         fwqyOptions.value = data;
        //     })
        //     .catch((err) => {});
    };

    const handleCheckAllChange = (val, row) => {
        row.fwid = val ? fwqyOptions.value.map(item => item.ORGNA_ID) : []
        row.isIndeterminate = false
    }

    const handleCheckedCitiesChange = (value, row, index) => {
        let arr = [];
      arr.push(value.ORGNA_ID);
        props.specialities[index].fwid = arr;

        const checkedCount = row.fwid.length;
        //row.checkAll = checkedCount === fwqyOptions.value.length
        row.isIndeterminate = checkedCount > 0 && checkedCount < fwqyOptions.value.length;
    }

    const handleSave = () => {
        let fwqyParams = []
        props.specialities.forEach(item => {
            item.fwqy = '';
            item.fwid.forEach(ii => {
                if (ii === 'all') {
                    item.fwqy = '全局';
                    fwqyParams.push({
                        FWQYID: uuidv4().replace(/-/g, ""), //"主键id",
                        ZYMXID: item.ZYMXID, //"专业明细ID",
                        FWQYWYBS: uuidv4().replace(/-/g, ""), //"服务区域唯一标识",
                        FWQYBM: ii, //"服务区域编码",
                        FWQYMC: '全局', //"服务区域名称",
                    })
                } else {
                    let _FWQYMC = fwqyOptions.value.find((i) => i.ORGNA_ID === ii).ORGNA_NAME
                    fwqyParams.push({
                        FWQYID: uuidv4().replace(/-/g, ""), //"主键id",
                        ZYMXID: item.ZYMXID, //"专业明细ID",
                        FWQYWYBS: uuidv4().replace(/-/g, ""), //"服务区域唯一标识",
                        FWQYBM: ii, //"服务区域编码",
                        FWQYMC: _FWQYMC, //"服务区域名称",
                    })
                    if (_FWQYMC) {
                        item.fwqy = item.fwqy + _FWQYMC + ';'
                    }
                }


            })
        })
        postCbsyjSaveXzqy(fwqyParams)
    };

    const submit = () => {
        let flag = true
        if (props.specialities.length == 0) {
            ElMessage({
                message: '请选择专业及区域！',
                type: 'warning'
            })
            return;
        }
        props.specialities.forEach(x => {
            x.ZYMXID = uuidv4().replace(/-/g, '');
            if (!x.fwid || x.fwid.length == 0) {
                ElMessage({
                    message: '请选择服务区域！',
                    type: 'warning'
                })
                flag = false
                return;
            }
            if (!x.zymc) {
                ElMessage({
                    message: '请选择专业！',
                    type: 'warning'
                })
                flag = false
                return;
            }
        })
        handleSave();
        if (flag) {
            emits('submitSpeciality', props.specialities)
        }
    }

    /**增加 */
    const addRow = () => {
        if (props.zyOptions[0].ZYFL === 'C') {
          if(props.specialities.length > 0) {
            ElMessage({
              message: 'C类准入一个队伍只能选择一个专业！',
              type: 'warning'
            });
            return;
          }
            props.specialities.splice(props.specialities.length, 0, {zymc: '', fwid: []})
        } else {
            props.specialities.splice(props.specialities.length, 0, {zymc: '', fwid: ['all']})
        }

        console.log(111111)
        // props.specialities.push({zymc: '', fwqy: ''})
    }
    /**删除 */
    const handleDeleteRow = (val, index) => {
        ElMessageBox.confirm('是否删除当前行？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            // TODO 删除操作
            props.specialities.splice(index, 1)
            ElMessage({
                message: '已删除',
                type: 'success'
            })
        }).catch(() => {
            ElMessage({
                message: '已取消删除',
                type: 'info'
            })
        })
    }

    /**查询服务区域 */
    const queryFwqy = () => {
        getCommonSelectDMB({DMLBID: 'FWQY'}).then(res => {
            data.fwqyOptions = res.data.map(x => {
                return {
                    value: x.DMXX,
                    label: x.DMMC
                }
            })
        })
    }
    onMounted(() => {
        emits('update:dialogTitle', '选择专业及区域')
        //queryFwqy();
        getArea();
    })
</script>

<style scoped>
    .el-container {
        height: 100%;
    }

    .out-box-content {
        height: calc(100% - 70px);
        padding: 20px 10px;
    }

    .add-btn {
        margin: 5px 0;
        height: 30px;
    }

    .btn {
        margin-top: 10px;
        height: 30px;
        padding-right: 30px;
    }
</style>
