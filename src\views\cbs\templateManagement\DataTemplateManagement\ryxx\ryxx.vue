<template>
  <div style="height: calc(100% - 40px);">
    <el-form style="height: 100%;" size="default" class="lui-page">
      <div style="text-align: right;margin-bottom: 20px" v-if="editable">
        <el-button class="lui-button-add" type="primary" @click="exportRyxx">
          <el-icon><Plus/></el-icon>导出人员信息
        </el-button>
      </div>
      <el-table
          class="lui-table"
        highlight-current-row
        size="default"
        ref="table"
        fit
        height="calc(100% - 50px)"
        :border="false"
        :data="state.tableData"
      >
        <EleProTableColumn
          v-for="prop in state.tableColumn"
          :col="prop"
          :key="prop.columnKey"
        >
          <template #info="{ row }">
            <el-tooltip :content="row[prop.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>

          <template #select="{ row }">
            <el-select
              size="normal"
              v-model="row[prop.prop]"
              clearable
              placeholder="请选择"
              @change="changePro(row)"
            >
              <el-option
                v-for="(item, index) in proDetails"
                :key="index"
                :value="item.ZYBM"
                :label="item.ZYMC"
              ></el-option>
            </el-select>
          </template>

          <template #DYXL="{ row }">
            <div>{{ryxl.find((i) => i.DMXX == row.DYXL)?.DMMC}}</div>
          </template>

          <template #CZZK="{ row }">
            <!--                    {{ row.ZSXX ? row.ZSXX.map(x => x.ZSMC).toString() : '' }}showZs-->
            <el-button :disabled="false" class="lui-table-button" @click="showZs(row, $index)">查看</el-button>
          </template>
          <template #input="{ row }">
            <el-input v-model="row[prop.prop]" placeholder="请输入"></el-input>
          </template>
          <template #titleinput="{ row }">
            <el-input
              v-if="row.SHZT != 1"
              v-model="row[prop.prop]"
              placeholder="请输入"
            ></el-input>
            <span v-else>{{ row[prop.prop] }}</span>
          </template>
          <template #opration="{ row, $index }">
            <div style="display: flex;gap: 5px;justify-content: center">
              <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="editRow(row, $index)">查看</el-button>
<!--              <el-button class="lui-table-button" @click="copyRow(row, $index)">复制</el-button>-->
              <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button class="lui-table-button" @click="setNull(row, $index)">清空</el-button>
              <el-button v-if="row.SHZT != 1 && row.EXTENSION?.SJLY!=='MBSC'" class="lui-table-button" @click="deleteRow(row, $index)"
                >删除
              </el-button>
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-form>
  </div>
  <el-dialog
      custom-class="lui-dialog"
    title="人员信息查看"
    v-model="editVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    top="50px"
    width="1200px"
    @close="() => {}"
  >
    <ryxxEdit
        :editable="false"
      :editData="editData"
      @updateData="updateData"
      :resultTableData="editDataWYBS"
      @close="editVisible = false"
    />
  </el-dialog>
  <el-dialog
      custom-class="lui-dialog"
    title="证书信息"
    v-model="zsDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    top="100px"
    width="1200px"
  >
    <el-table
      highlight-current-row
      size="default"
      ref="table"
      fit
      class="lui-table"
      height="300px"
      :border="false"
      :data="currentRow.ZSXX"
    >
      <EleProTableColumn
        v-for="prop in state.fileColumn"
        :col="prop"
        :key="prop.columnKey"
      >
        <template #fileList="{ row, $index }">
          <vsfileupload
              :maxSize="10"
            class="fjsc"
            :index="$index"
            :busId="row.ZSYWID"
            :editable="false"
            :key="row.ZSYWID"
            ywlb="DWZTBGFJ"
            busType="dwxx"
            :limit="100"
          ></vsfileupload>
        </template>
        <template #ZSLX="{ row }">
          {{ zslx[row[prop.prop]] }}
        </template>
      </EleProTableColumn>
    </el-table>
  </el-dialog>

  <el-dialog
      custom-class="lui-dialog"
      title="信息项选择"
      v-model="state.chooseVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      top="50px"
      width="1200px"
      @close="() => {}"
  >
    <ryxxXz
        :key="editIndex"
        :currentData="currentRow"
        @updateChooseData="updateChooseData"
        @updateEditData="updateEditData"
        @close="state.chooseVisible = false"
        :TYXYDM="TYXYDM"
    />
  </el-dialog>
</template>
<script setup>
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import { axios } from "@src/assets/core/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import ryxxEdit from "./ryxxEdit.vue";
import { v4 as uuidv4 } from "uuid";
import { getCommonSelectDMB } from "@src/api/cbsxx.js";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import axiosUtil from "../../../../../lib/axiosUtil";
import {InfoFilled} from "@element-plus/icons-vue";
import ryxxXz from "./ryxx_xz.vue";

const props = defineProps({
  defaultData: {
    type: Array,
    defaultData: () => [],
  },
  proDetails: {
    type: Array,
    defaultData: () => [],
  },
  // 结果表数据，比对用
  resultTableData: {
    type: Object,
    default: () => null,
  },
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  },
  DWYWID: {
    type: String,
    default: ''
  }
});
const zsDialog = ref(false);
const showZs = (row) => {
  currentRow.value = row;
  zsDialog.value = true;
};
const zslx = ref({});
const rylx = ref({});
const currentRow = ref({});
const getRylx = () => {
  getCommonSelectDMB({ DMLBID: "RYLX" }).then((res) => {
    res.data.forEach((x) => (rylx.value[x.DMXX] = x.DMMC));
  });
};

const ryxl = ref([]);
const getRyxl = () => {
  getCommonSelectDMB({ DMLBID: "XL" }).then((res) => {
    ryxl.value = res.data;
  });
};
// 查询证书类型
const getZslx = () => {
  getCommonSelectDMB({ DMLBID: "RYZSLX" }).then((res) => {
    zslx.value = {};
    for(let i=0; i<res.data.length; i++){
      zslx.value[res.data[i].DMXX] = res.data[i].DMMC;
    }
  });
};

const changePro = (row) => {
  row.ZYMC = props.proDetails.filter((x) => x.ZYBM == row.ZYBM)[0].ZYMC;
};
const state = reactive({
  chooseVisible:false,
  tableData: [],
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    {
      label: "专业名称",
      prop: "ZYMC",
      align: "center",
      showOverflowTooltip: true,
      width: 100,
      // slot: "select",
    },

    {
      label: "业务要求",
      prop: "YWYQ",
      align: "center",
      showOverflowTooltip: true,
      width: 80,
      slot: "info"
    },
    {
      label: "录入资料说明",
      prop: "LRZLSM",
      align: "center",
      showOverflowTooltip: true,
      width: 80,
      slot: "info"
    },
    {
      label: "信息项名称",
      prop: "XXXMC",
      align: "center",
      showOverflowTooltip: true,
      width: 200,
      // slot: "select",
    },
    // {
    //   label: "信息项",
    //   prop: "XXXMC",
    //   align: "center",
    //   showOverflowTooltip: true,
    //   width: 150,
    // },
    {
      label: "姓名",
      prop: "RYXM",
      align: "center",
      //width: 150,
    },
    // {
    //   label: "人员类型",
    //   prop: "RYLX",
    //   align: "center",
    //   width: 150,
    // },
    {
      label: "岗位",
      prop: "GWJZW",
      align: "center",
      //width: 150,
    },
    {
      label: "学历",
      prop: "DYXL",
      align: "center",
      //width: 150,
      slot: "DYXL",
    },
    {
      label: "工作年限",
      prop: "BGGZSC",
      align: "center",
      //width: 150,
    },
    {
      label: "持证信息",
      prop: "CZZK",
      align: "center",
      width: 150,
      slot: "CZZK",
    },
    {
      label: "操作",
      align: "center",
      width: 250,
      fixed: "right",
      slot: "opration",
      hide: !props.editable
    },
  ],
  fileColumn: [
    {
      label: "序号",
      type: "index",
      align: "center",
    },
    {
      label: "证书类型",
      prop: "ZSLBDM",
      align: "center",
      showOverflowTooltip: true,
      slot: "ZSLX",
    },
    {
      label: "证书名称",
      prop: "ZSMC",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "证书编号",
      prop: "ZSBH",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "有效开始日期",
      prop: "YXQKS",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "有效结束日期",
      prop: "YXQJS",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "附件",
      prop: "fileList",
      align: "center",
      showOverflowTooltip: true,
      slot: "fileList",
    },
  ],
});

const validateRyxx =(SFZH)=>{
  return new  Promise((resolve, reject) => {
    let params={
      DWYWID : props.DWYWID,
      SFZH: SFZH
    }
    axiosUtil.get('/backend/sccbsgl/dwxxbg/selectRydwcfxx',params).then(res=>{
      if(res.data.length>0){
        let dwmc=res.data.map(item=>item.DWMC).join('、')
        resolve({ZT: '0',msg: `该人员已存在与${dwmc}队伍中`})
      }else {
        resolve({ZT: '1'})
      }
      resolve(false)
    })
  })
}

const updateChooseData = (val) => {
  validateRyxx(val.SFZH).then(res=>{
    if(res.ZT==='1'){
      if(state.tableData.find(item=>item.CYZSJID===val.CYZSJID && currentRow.value.ZYBM===item.ZYBM)){
        ElMessage.error('该人员已选择，请勿重复选择')
        return
      }

      changeData(currentRow.value,val,editIndex.value,false)
    }else {
      ElMessage.error(res.msg)
    }
  })


};

const changeData = (oldRow,newRow,index,visible) => {
  let params={
    CYZSJID: newRow.CYZSJID,
  }

  axiosUtil.get('/backend/sccbsgl/cbsyj/ytxxx/copyRyzsxx', params).then((res) => {
    let resData=res.data || []
    let ZSXX=[]

    resData.forEach(item=>{
      ZSXX.push({
        ...item,
        ZSWYBS: item.ZSYWID,
        DWYWID: oldRow.DWYWID,
        MBMXID: oldRow.MBMXID,
        ZSCYZBS: oldRow.DWCYID,
        ZSDLDM: "RYZS",
        ZSCYZLXDM: "DWCY",
      })
    })
    if(oldRow.EXTENSION?.SJLY==='MBSC'){
      if(!newRow.EXTENSION){
        newRow.EXTENSION={
          SJLY: 'MBSC'
        }
      }else {
        newRow.EXTENSION.SJLY='MBSC'
      }
    }


    oldRow.ZSXX=ZSXX;
    oldRow.EXTENSION=newRow.EXTENSION;
    oldRow.RYLX=newRow.RYLX;
    oldRow.RYXM=newRow.RYXM;
    oldRow.XBDM=newRow.XBDM;
    oldRow.SFZH=newRow.SFZH;
    oldRow.CSNY=newRow.CSNY;
    oldRow.GWJZW=newRow.GWJZW;
    oldRow.BGGZSC=newRow.BGGZSC;
    oldRow.BYYX=newRow.BYYX;
    oldRow.SXZY=newRow.SXZY;
    oldRow.DYXL=newRow.DYXL;
    oldRow.BYSJ=newRow.BYSJ;
    oldRow.ZC=newRow.ZC;
    oldRow.CYZSJID=newRow.CYZSJID;
    oldRow.XLPXH=newRow.XLPXH;
    oldRow.RYXLPXH=newRow.RYXLPXH;
    oldRow.XLMC=newRow.XLMC;

    state.chooseVisible = visible;
  })
}

const updateEditData = (row) => {
  state.tableData.forEach((item,index)=>{
    if(item.CYZSJID===row.CYZSJID){
      changeData(item,row,index,true)
    }
  })
}

const chooseRow = (row, index) => {
  currentRow.value=row;
  console.log(currentRow.value, '--currentRow')
  editIndex.value = index;
  state.chooseVisible = true;
};


watch(
  () => props.defaultData,
  (val) => {
    console.log("val", val);
    if (!val) return;
    if (val) {
      val.forEach((x) => {
        const UUID = uuidv4().replace(/-/g, "");
        x.DWCYID = x.DWCYID || UUID;
        x.CYWYBS = x.CYWYBS || UUID;
        if (x.ZSXX) {
          x.CZZK = x.ZSXX.map((x) => x.ZSMC).toString();
        }
      });
    }
    state.tableData = val;
  },
  {
    immediate: true,
  }
);
const copyRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, "");
  state.tableData.splice(index, 0, { ...row, DWCYID: UUID, CYWYBS: UUID ,SHZT:''});
};
const insertRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, "");
  state.tableData.splice(index + 1, 0, {
    DWCYID: UUID,
    CYWYBS: UUID,
    ZYMC: row.ZYMC,
    ZYBM: row.ZYBM,
    XXXMC: row.XXXMC,
    MBMXID: row.MBMXID,
    YWYQ: row.YWYQ,
    LRZLSM: row.LRZLSM,
    XXX: null,
    ZZXXMC: null,
    ZZDJ: null,
    YXKSRQ: null,
    YXJSRQ: null,
    FZBM: null,
    FJ: null,
    RYLX: null, //人员类型
    RYXM: null, //人员姓名
    XBDM: null, //性别代码
    SFZH: null, //身份证号
    CSNY: null, //出生年月
    CJSJ: null, //参加工作时间
    SBJNRQ: null,
    GWMC: null,
    BGWGZRQ: null,
    XZZW: null,
    DYXL: null, //文化程度(学历)
    BYYX: null, //毕业院校
    SXZY: null, //所学专业
    BYSJ: null, //毕业时间
    WYSP: null, //
    ZC: null,
    ZCZY: null,
    ZCDJ: null,
    GWJZW: null,
    BGGZSC: null,
  });
};

const setNull = (row, index) => {
    row.CYZSJID='';
    row.RYLX= ''; //人员类型
    row.RYXM= ''; //人员姓名
    row.XBDM= ''; //性别代码
    row.SFZH= ''; //身份证号
    row.CSNY= ''; //出生年月
    row.CJSJ= ''; //参加工作时间
    row.SBJNRQ= '';
    row.GWMC= '';
    row.BGWGZRQ= '';
    row.XZZW= '';
    row.DYXL= ''; //文化程度(学历)
    row.BYYX= ''; //毕业院校
    row.SXZY= ''; //所学专业
    row.BYSJ= ''; //毕业时间
    row.WYSP= ''; //
    row.ZC= '';
    row.ZCZY= '';
    row.ZCDJ= '';
    row.GWJZW= '';
    row.BGGZSC= '';
    row.EXTENSION={};
    row.ZSXX=[];
};

// 查看持证状况
const viewZS = (row) => {
  console.log(row);
};

const info = ref({});
const editVisible = ref(false);
const editIndex = ref(0);
const editData = ref({});
const editRow = (row, index) => {
  row.ZSXX = row.ZSXX || [];
  editIndex.value = index;
  editData.value = row;
  editVisible.value = true;
};
const editDataWYBS = computed(() => {
  return props.resultTableData?.find(i => i.CYWYBS == editData.value?.CYWYBS)
})
const updateData = (val, isAdd) => {
  console.log(val, isAdd);
  if (isAdd) {
    state.tableData.splice(editIndex.value, 1, val);
    const UUID = uuidv4().replace(/-/g, "");
    insertRow(
      {
        DWCYID: UUID,
        CYWYBS: UUID,
        ZYMC: val.ZYMC,
        ZYBM: val.ZYBM,
        XXXMC: val.XXXMC,
        MBMXID: val.MBMXID,
      },
      editIndex.value
    );
    editRow(state.tableData[editIndex.value + 1], editIndex.value + 1);
  } else {
    state.tableData.splice(editIndex.value, 1, val);
    editVisible.value = false;
  }
};

const deleteRow = (row, index) => {
  ElMessageBox.confirm("是否删除此条数据？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      state.tableData.splice(index, 1);
      ElMessage({
        message: "删除成功!",
        type: "success",
      });
    })
    .catch(() => {});
};
const validateForm = () => {
  return new Promise((resolve, reject) => {
    //if(state.tableData.find(item=>(item.SFBT=='1' && !item.CYZSJID))){
    if(state.tableData.find(item=>(!item.CYZSJID))){
      reject({mgs:[{message:'请完善人员信息！'}]})
    }else {
      console.log(state.tableData, '---state.tableData')
      for(let i=0;i<state.tableData.length;i++){
        let xlpxh = state.tableData[i].XLPXH;
        let gznx = state.tableData[i].GZNX;
        let realgznx = state.tableData[i].BGGZSC;
        let realxlpxh = state.tableData[i].RYXLPXH;
        if(gznx){
          if(!realgznx || realgznx < gznx){
            reject({mgs:[{message:'第'+ (i+1) + '行人员工作年限不符合'+ gznx +'年的要求'}]});
            return;
          }
        }
        if(xlpxh){
          if(!realxlpxh || realxlpxh > xlpxh){
            reject({mgs:[{message:'第'+ (i+1) + '行人员学历不符合' + state.tableData[i].XLMC + '的要求'}]});
            return;
          }
        }
        // 要求最低人数
        let zdrs = state.tableData[i].ZDRS;
        let zymc = state.tableData[i].ZYMC;
        let xxxmc = state.tableData[i].XXXMC;
        let realrs = 0;
        for(let k=0;k<state.tableData.length;k++){
          if(state.tableData[k].CYZSJID && state.tableData[k].MBMXID === state.tableData[i].MBMXID){
            realrs++;
          }
        }
        if(realrs < zdrs){
          reject({mgs:[{message: zymc + '专业' + xxxmc + '人员数量不满足' + zdrs + '人要求'}]});
          return;
        }
        // 检验证书情况
        let zsStr = state.tableData[i].CZXX ? state.tableData[i].CZXX.split(",") : [];
        let realZsArr = state.tableData[i].ZSXX;
        // 缺少的证书
        let nohaStr = "";
        for(let m = 0;m < zsStr.length;m++){
          let has = false;
          for(let n = 0;n < realZsArr.length;n++){
            if(zsStr[m] === realZsArr[n].ZSLBDM){
              has = true;
            }
          }
          if(!has){
            nohaStr += zslx.value[zsStr[m]] + "、";
          }
        }
        if(nohaStr){
          reject({mgs:[{message:'第'+ (i+1) + '行人员缺少' + nohaStr.slice(0, -1) + '证书'}]});
          return;
        }

      }
      resolve(true)
    }
  })
};

const exportRyxx = () => {
  let dwywid = props.proDetails[0].DWYWID;
  let column = [[
    {field: 'XXXMC', title: '信息项名称'},
    {field: 'RYXM', title: '姓名'},
    {field: 'XBMC', title: '性别'},
    {field: 'CSNY', title: '出生年月'},
    {field: 'SFZH', title: '身份证号'},
    {field: 'GWJZW', title: '岗位及职务'},
    {field: 'BGGZSC', title: '本岗位工作时长'},
    {field: 'WHCD', title: '文化程度'},
    {field: 'SXZY', title: '所学专业'},
    {field: 'BYYX', title: '毕业院校'},
    {field: 'BYSJ', title: '毕业时间'},
    {field: 'ZC', title: '职称'},
    {field: 'ZSMC', title: '证件名称'},
    {field: 'ZSBH', title: '证件编号'},
    {field: 'ZSLBMC', title: '证件类型'},
    {field: 'ZJYXSJ', title: '证件有效时间'},
  ]];
  let params = {
    title: "队伍人员信息",
    name: "队伍人员信息",
    params: {DWYWID: dwywid},
    url: '/excel/dwryxxExport',
  };
  params.column = column;
  axiosUtil.exportExcel('/backend/commonExport/magicExcel', params);
};

defineExpose({
  validateForm,
});
onMounted(() => {
  getRylx();
  getRyxl();
  getZslx();
});
</script>
<style scoped>

</style>
