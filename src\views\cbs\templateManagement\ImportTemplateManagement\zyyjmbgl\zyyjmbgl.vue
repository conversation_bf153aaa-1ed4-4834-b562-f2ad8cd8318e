<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
        <el-input
            v-model="form.zymc"
            placeholder="搜索一级/二级/三级专业"
            clearable
        ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
        <el-input v-model="form.qymbmc" placeholder="请输入企业模板名称" clearable></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="form.dwmbmc" placeholder="请输入队伍模板名称" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <div style="display: flex">
          <el-button type="primary" @click="getTemplate"><el-icon><Search/></el-icon>查询</el-button>
          <el-button type="primary" class="lui-button-add" @click="addTemplate">
            <el-icon><Plus/></el-icon>新增
          </el-button>
          <el-button type="primary" @click="exportData"><el-icon><Upload/></el-icon>导出</el-button>
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table :data="tableData"
                class="lui-table"
                size="default"
                height="calc(100vh - 250px)"
                border stripe>
        <EleProTableColumn v-for="prop in tableColumn" :col="prop" :key="prop.columnKey">
          <template #opration="{ row }">
            <el-button class="lui-table-button" @click="delTemplate(row)">删除</el-button>
          </template>
        </EleProTableColumn>
      </el-table>
      <el-pagination
          @size-change="
          paginationObj.page = 1;
          getTemplate();
        "
          @current-change="getTemplate"
          v-model:current-page="paginationObj.page"
          :page-sizes="[20, 40, 80, 100]"
          v-model:page-size="paginationObj.size"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="paginationObj.totalNum"
          background
          :pager-count="7">
      </el-pagination>
    </div>
    <el-dialog
        custom-class="lui-dialog"
        title="引进模板维护"
        v-model="dwkhdialogVisible"
        width="85%"
        top="3vh"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        destroy-on-close>
      <glgxbjkp @close="onClose" @update="update" />
    </el-dialog>
  </el-form>
</template>
<script setup>
import { onMounted, ref } from "vue";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import glgxbjkp from "./glgxbjkp.vue";

import {
  getZrmbglZymbgx, //专业引进模板关系
  deleteZrmbglZymbgx, //删除专业引进模板关系
} from "@src/api/sccbsgl.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import axiosUtil from "@lib/axiosUtil";
// 上册查询条件
const form = ref({
  zymc: "",
  qymbmc: "",
  dwmbmc: "",
});
// 模板类型下拉选项
const templateOptions = ref([]);
const tableColumn = ref([
  { label: "序号", type: "index", width: 55, align: "center" },
  {
    label: "专业类别",
    align: "center",
    children: [
      {
        label: "一级",
        prop: "DLZYMC",
        align: "center",
        showOverflowTooltip: true,
      },
      {
        label: "二级",
        prop: "ZLZYMC",
        align: "center",
        showOverflowTooltip: true,
      },
      {
        label: "三级",
        prop: "XLZYMC",
        align: "center",
        showOverflowTooltip: true,
      },
    ],
  },
  {
    label: "企业模板",
    prop: "QYMBMC",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "队伍模板",
    prop: "DWMBMC",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作人",
    prop: "CZR",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作时间",
    prop: "CZSJ",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "操作",
    align: "center",
    showOverflowTooltip: true,
    slot: "opration",
  },
]);
// 表格数据
const tableData = ref([]);
// 翻页对象
const paginationObj = ref({
  page: 1,
  size: 10,
  totalNum: 0,
});

// 弹窗visible
const dwkhdialogVisible = ref(false);
// 模板ID，弹窗用
const editDataId = ref("");
// 是否为编辑状态
const editStatus = ref(true);
// 模板创建
const isNew = ref(false);
const addTemplate = () => {
  isNew.value = true;
  dwkhdialogVisible.value = true;
};

// 获取模板列表
const getTemplate = () => {
  const { page, size } = paginationObj.value;
  getZrmbglZymbgx({
    ...form.value,
    page,
    size,
  })
    .then(({ data }) => {
      console.log(data);
      if (data) {
        tableData.value = data.list;
        paginationObj.value.totalNum = data.total;
      } else {
        throw new Error();
      }
    })
    .catch((err) => {
      ElMessage.error("查询失败");
    });
};
onMounted(() => getTemplate());
/**
 * @author: ddhhh
 * @param {*} ZYFLDM
 * @return {*}
 * @description: 删除模板关联关系
 * @Date: 2023-10-07 09:00:39
 */
const delTemplate = async ({ ZYFLDM }) => {
  const res = await ElMessageBox.confirm("确认删除该模板？", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(() => true)
    .catch(() => false);
  if (!res) return;
  // console.log(ZYFLDM);
  // return
  deleteZrmbglZymbgx({
    params: {
      ZYFLDM,
    },
  })
    .then((result) => {
      ElMessage.success("删除成功");
      getTemplate();
    })
    .catch((err) => {
      ElMessage.error("删除失败");
    });
};
const onClose = () => {
  dwkhdialogVisible.value = false;
};
const update = () => {
  getTemplate();
  onClose()
}

const exportData = () => {
  let column = [[
    {  title: '专业类别',colspan: 3},
    { field: 'QYMBMC', title: '企业模板',rowspan: 2},
    { field: 'DWMBMC', title: '队伍模板',rowspan: 2},
    { field: 'CZR', title: '操作人',rowspan: 2},
    { field: 'CZSJ', title: '操作时间',rowspan: 2},
  ], [
    { field: 'DLZYMC', title: '一级'},
    { field: 'ZLZYMC', title: '二级'},
    { field: 'XLZYMC', title: '三级'},
  ]]

  let params = {
    title: "专业引进模板关联信息",
    name: "专业引进模板关联信息",
    params: form.value,
    url: '/excel/zyyjmbglxxExport',
  }
  params.column = column
  axiosUtil.exportExcel('/backend/excelExport/magicExcel', params)
};
</script>
<style lang="scss" scoped>
.el-container {
  height: 100%;
  padding: 20px;
  background: #fff;
}
</style>
