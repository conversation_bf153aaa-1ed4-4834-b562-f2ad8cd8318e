<template>
  <div class="error-page">
        <div class="error-code" v-if="userLoginName != 'empty'" >4<span>0</span>3</div>
        <div class="error-desc" v-if="userLoginName != 'empty'" >没有权限访问该页面</div>
        <div class="error-handle"  v-if="userLoginName != 'empty'">
            <router-link to="/">
                <el-button type="primary" size="large">返回首页</el-button>
            </router-link>
            <el-button class="error-btn" type="primary" size="large" @click="$router.go(-1)">返回上一页</el-button>
        </div>
        <div class="error-desc" v-if="userLoginName == 'empty'" >账号未开通，请联系管理员开通账号！</div>

    </div>


</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
  onUnmounted
}
  from 'vue'
    import vsAuth from "@src/lib/vsAuth";


export default defineComponent({
  name: '',
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      userLoginName:'',
      
    })

    onMounted(() => {
      state.userLoginName = state.userInfo.userLoginName;
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>
<style>
.vsui-content-view-scoped{
  height: 100%;
}
</style>
<style scoped>
    .error-page{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 100%;
        height: 100%;
        background: #f3f3f3;
        box-sizing: border-box;
    }
    .error-code{
        line-height: 1;
        font-size: 250px;
        font-weight: bolder;
        color: #f02d2d;
    }
    .error-code span{
        color: #00a854;
    }
    .error-desc{
        font-size: 30px;
        color: #777;
    }
    .error-handle{
        margin-top: 30px;
        padding-bottom: 200px;
    }
    .error-btn{
        margin-left: 100px;
    }
</style>
