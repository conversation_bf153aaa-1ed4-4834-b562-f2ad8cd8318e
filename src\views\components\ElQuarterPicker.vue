<template>
    <div class="el-quarter-picker">
        <el-popover v-model:visible="visible" :disabled="!canPopover" :tabindex="null" placement="bottom-start"
            transition="el-zoom-in-top" popper-class="my-popover-style" style="width: 400px;">
            <div class="el-date-picker">
                <div class="el-picker-panel__body">
                    <div class="el-date-picker__header el-date-picker__header--bordered"
                        style="margin:0px; line-height:30px">
                        <el-row>
                            <el-col :span="4">
                                <!-- 上一年 -->
                                <el-icon @click="clickLast">
                                    <DArrowLeft />
                                </el-icon>
                            </el-col>
                            <el-col :span="16">
                                <span role="button" class="el-date-picker__header-label"
                                    @click="clickYear">{{ title }}</span>
                            </el-col>
                            <el-col :span="4">
                                <!-- 下一年 -->
                                <el-icon @click="clickNext">
                                    <DArrowRight />
                                </el-icon>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="el-picker-panel__content" style="margin:0px; width:100%">
                        <table class="el-month-table" style="">
                            <tbody>
                                <tr v-for="line in lineCount" :key="line">
                                    <td v-for="index in (line * 4 <= viewList.length ? 4 : viewList.length - (line - 1) * 4)"
                                        :key="index"
                                        :class="{ current: viewList[(line - 1) * 4 + index - 1].active }">
                                        <div><a class="cell" @click="clickItem(viewList[(line - 1) * 4 + index - 1])">{{
                                            viewList[(line - 1) * 4 + index - 1].label }}</a></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <template #reference>
                <el-input ref="input" @click="changeText" :placeholder="placeholder" v-model="text" :size="size"
                    :readonly="!canEdit" :disabled="disabled" :prefix-icon="Calendar" clearable @clear="clearInput">
                </el-input>
            </template>
        </el-popover>
    </div>
</template>

<script>
import {
    defineComponent,
    ref,
    shallowRef,
    toRefs,
    reactive,
    getCurrentInstance,
    watchEffect,
    onMounted,
    watch,
    onUnmounted
}
    from 'vue'
import { Calendar, DArrowLeft, DArrowRight } from '@element-plus/icons-vue'
export default defineComponent({
    name: 'ElQuarterPicker',
    props: {
        placeholder: {
            type: String,
            default: ''
        },
        size: {
            type: String,
            default: ''
        },
        readonly: {
            type: Boolean,
            default: false
        },
        clearable: {
            type: Boolean,
            default: true
        },
        editable: {
            type: Boolean,
            default: true
        },
        disabled: {
            type: Boolean,
            default: false
        },
        format: {
            type: String,
            default: 'yyyy年第Q季度'
        },
        valueFormat: {
            type: String,
            default: 'yyyy-q'
        },
        value: {
            type: String,
            default: ''
        }
    },
    components: {
         DArrowLeft, DArrowRight
    },
    setup(props, context) {
        const state = reactive({
            value: props.value,
            valueFormat: props.valueFormat,
            format: props.format,
            disabled: props.disabled,
            editable: props.editable,
            clearable: props.clearable,
            readonly: props.readonly,
            size: props.size,
            placeholder: props.placeholder,
            visible: false,
            showClear: false, // 控制清空按钮展示
            canEdit: true, // 是否可编辑
            canPopover: true, // 选择器弹出是否可用
            text: '', // 文本框值
            viewType: 1, // 视图类型，1季度，2年度
            viewYear: 0, // 当前年份
            viewList: [], // 数据列表
            lineCount: 0, // 数据行数
            title: '', // 选择器标题
            data: [0, 0], // 当前选择年度-季度
        })
        const instance = getCurrentInstance()
        const clearInput = () =>{
            context.emit('change', '')
        }
        // 季度文本变更
        const changeText = () => {
            state.visible = true
            if (checkFormat(state.format, state.text)) {
                // 设置值
                formatFrom(state.text, state.format)
                context.emit('change', formatTo(state.data, state.valueFormat))
            } else {
                // 输入了无效的格式，还原回原来的值
                if (state.data[0] < 1 || state.data[1] < 1) {
                    state.text = ''
                } else {
                    state.text = formatTo(state.data, state.format)
                }
            }
        }
        // 鼠标进入
        const mouseEnter = () => {
            if (!state.disabled && !state.readonly && state.clearable && state.text !== '') {
                state.showClear = true
            }
        }
        // 鼠标离开
        const mouseLeave = () => {
            if (!state.disabled && state.clearable) {
                state.showClear = false
            }
        }
        // 季度值变更
        const changeValue = (val) => {
            state.viewType = 1
            if (val) {
                // 反向格式化
                formatFrom(val, state.valueFormat)
                state.text = formatTo(state.data, state.format)
                state.viewYear = state.data[0]
            } else {
                state.text = ''
                state.data = [0, 0]
                state.viewYear = new Date().getFullYear()
            }
            initView()
        }
        // 校验季度格式是否正确
        const checkFormat = (pattern, val) => {
            // 格式转成正则表达式
            let text = ''
            for (const char of pattern) {
                const dict = '\\^$.+?*[]{}!'
                if (dict.indexOf(char) === -1) {
                    text += char
                } else {
                    text += '\\' + char
                }
            }
            text = text.replace('yyyy', '[1-9]\\d{3}')
            text = text.replace('qq', '0[1-4]')
            text = text.replace('q', '[1-4]')
            text = text.replace('Q', '[一二三四]')
            text = '^' + text + '$'
            const patt = new RegExp(text)
            return patt.test(val)
        }
        // 格式化季度到指定格式
        const formatTo = (data, pattern) => {
            let text = pattern.replace('yyyy', '' + data[0])
            text = text.replace('qq', '0' + data[1])
            text = text.replace('q', '' + data[1])
            text = text.replace('Q', '一二三四'.substr(data[1] - 1, 1))
            return text
        }
        // 以指定格式解析季度
        const formatFrom = (str, pattern) => {
            const year = findText(str, pattern, 'yyyy')
            const quarter = findText(str, pattern, ['qq', 'q', 'Q'])
            state.data = [year, quarter]
        }
        // 查找文本数值
        const findText = (str, pattern, find) => {
            if (find instanceof Array) {
                for (const f of find) {
                    const val = findText(str, pattern, f)
                    if (val !== -1) {
                        return val
                    }
                }
                return -1
            }
            const index = pattern.indexOf(find)
            if (index === -1) {
                return index
            }
            const val = str.substr(index, find.length)
            if (find === 'Q') {
                return '一二三四'.indexOf(val) + 1
            } else {
                return parseInt(val)
            }
        }
        // 季度选择
        const clickItem = (item) => {
            if (state.viewType === 1) {
                // 选择季度
                state.text = formatTo([item.year, item.quarter], state.format);
                formatFrom(item.year+'-'+ item.quarter, state.valueFormat)
                context.emit('change', formatTo([item.year, item.quarter], state.valueFormat))
                state.visible = false
                initView()
            } else {
                // 选择年度
                state.viewType = 1
                state.viewYear = item.year
                initView()
            }
        }
        // 年份点击
        const clickYear = () => {
            if (state.viewType !== 1) {
                return
            }
            // 切换年度选择器
            state.viewType = 2
            initView()
        }
        // 上一年
        const clickLast = () => {
            if (state.viewYear > 1000) {
                if (state.viewType === 1) {
                    state.viewYear--
                    initView()
                } else {
                    state.viewYear = state.viewYear - 10
                    initView()
                }
            }
        }
        // 下一年
        const clickNext = () => {
            if (state.viewYear < 9999) {
                if (state.viewType === 1) {
                    state.viewYear++
                    initView()
                } else {
                    state.viewYear = state.viewYear + 10
                    initView()
                }
            }
        }
        // 初始化视图数据
        const initView = () => {
            const list = []
            const curDate = new Date()
            const curYear = curDate.getFullYear()
            const curQuarter = parseInt(curDate.getMonth() / 3) + 1
            if (state.viewType === 1) {
                let index = 0
                for (const i of '一二三四') {
                    index++
                    const item = { label: '第' + i + '季度', year: state.viewYear, quarter: index, current: false, active: false }
                    // if (state.viewYear === curYear && index === curQuarter) {
                    //     item.current = true
                    // } else 
                    if (state.viewYear === state.data[0] && index === state.data[1]) {
                        item.active = true
                    }
                    list.push(item)
                }
                state.title = state.viewYear + ' 年'
            } else {
                const start = parseInt(state.viewYear / 10) * 10
                state.viewYear = start
                for (let i = 0; i < 10; i++) {
                    const year = start + i
                    const item = { label: year + '', year: year, current: false, active: false }
                    // if (year === curYear) {
                    //     item.current = true
                    // } else 
                    if (year === state.data[0]) {
                        item.active = true
                    }
                    list.push(item)
                }
                state.title = start + ' 年 - ' + (start + 9) + ' 年'
            }

            state.viewList = list
            state.lineCount = parseInt(list.length / 4)
            if (list.length % 4 > 0) {
                state.lineCount++
            }
        }
        // 清除季度
        const clear = () => {
            state.showClear = false
            state.visible = false
            context.emit('change', '')
        }
        watch(state.value, (newVal, oldVal) => {
            changeValue(newVal)
        })
        watch(()=>props.value, (newVal, oldVal) => {
            changeValue(newVal)
        })
        watch(state.readonly, (newVal, oldVal) => {
            state.canEdit = !newVal && state.editable
            state.canPopover = !state.disabled && !newVal
        })
        watch(state.editable, (newVal, oldVal) => {
            state.canEdit = !state.readonly && newVal
        })
        watch(state.disabled, (newVal, oldVal) => {
            state.canPopover = !newVal && !state.readonly
        })
        onUnmounted(() => {
            document.onkeydown = null
        })
        onMounted(() => {
            changeValue(state.value)

            // 设置文本框是否可编辑
            state.canEdit = !state.readonly && state.editable
            state.canPopover = !state.disabled && !state.readonly
            // 监听按键(上下左右键可以切换季度)
            document.onkeydown = (event) => {
                if (state.visible) {
                    const data = [state.data[0], state.data[1]]
                    if (data[0] < 1 || data[1] < 1) {
                        // 以当前季度为标准
                        const curDate = new Date()
                        data[0] = curDate.getFullYear()
                        data[1] = parseInt(curDate.getMonth() / 3) + 1
                    }
                    if (event.code === 'ArrowLeft') {
                        // 上一个季度
                        if (data[1] === 1) {
                            data[0] = data[0] - 1
                            data[1] = 4
                        } else {
                            data[1] = data[1] - 1
                        }
                    } else if (event.code === 'ArrowRight') {
                        // 下一个季度
                        if (data[1] === 4) {
                            data[0] = data[0] + 1
                            data[1] = 1
                        } else {
                            data[1] = data[1] + 1
                        }
                    } else if (event.code === 'ArrowUp') {
                        // 上一年季度
                        data[0] = data[0] - 1
                    } else if (event.code === 'ArrowDown') {
                        // 下一年季度
                        data[0] = data[0] + 1
                    } else {
                        return
                    }

                    // 超过年限的不处理
                    if (data[0] < 1000 || data[0] > 9999) {
                        return
                    }
                    state.data = data
                    state.viewType = 1
                    state.viewYear = data[0]
                    context.emit('change', state.formatTo(data, state.valueFormat))
                }
            }
        })
        return {
            ...toRefs(state),
            changeText,
            mouseEnter,
            mouseLeave,
            clear,
            changeValue,
            initView,
            checkFormat,
            formatTo,
            formatFrom,
            findText,
            clickYear,
            clickItem,
            clickLast,
            clickNext,
            Calendar,
            clearInput
        }
    }
})
</script>

<style>
.el-quarter-picker {
    width: 200px;
    display: inline-block;
}

.el-popover.my-popover-style {
    width: 350px !important;
}
</style>