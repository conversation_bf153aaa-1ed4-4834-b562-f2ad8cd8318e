
<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue';
import { POBrowser } from 'js-pageoffice';

const tableData = ref([]);
const selectedQuestions = ref([]); // 用于存储选中的试题 ID

const fetchData = async () => {
	try {
		const response = await request({
			url: '/ExaminationPaper/index',
			method: 'get',
		});
		tableData.value = response;
	} catch (error) {
		console.error('There has been a problem with your axios operation:', error);
	}
};

const handleSelectionChange = (selection) => {
  selectedQuestions.value = selection.map((row) => row.id); // 获取选中的试题 ID
};

//生成试卷
const generateExamPaper = () => {
  // 获取选中的试题 ID
  let selectedQuestionIds = selectedQuestions.value;
  POBrowser.openWindow("Compose", 'width=1150px;height=900px;',selectedQuestionIds);

}
const handleEdit = (row) => {
	POBrowser.openWindow("Word", 'width=1150px;height=900px;',row.id);
};

onMounted(() => {
	fetchData();
});

</script>
<template>
	<div class="container">
		<h3 style="margin-top: 25px">演示：动态生成试卷</h3>
			<div style="width:600px;margin-top: 15px; font-size:14px;">
				<p style="text-align: center;">
					请先选择试题，然后点击生成试卷按钮进行生成试卷。
				</p>
			</div>
		<el-table :data="tableData" style="width: 595px" border  @selection-change="handleSelectionChange">
			<el-table-column type="selection" width="55" />
			<el-table-column prop="id" label="题库编号" width="180" />
			<el-table-column label="题库名称" width="180">
				<template #default="scope">
					<span>试题</span><span>{{ scope.row.id }}</span>
				</template>
			</el-table-column>
			<el-table-column label="操作" width="180">
				<template #default="scope">
					<el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
				</template>
			</el-table-column>

		</el-table>
		<div class="button-container">
			<el-button type="primary" style="margin: 0;" @click="generateExamPaper">生成试卷</el-button>
		</div>
	</div>
</template>
<style>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 100vh;
	/* 确保容器可以填充整个视口的高度 */
}

.button-container {
	margin-top: 10px;
	/* 给按钮留出一些垂直空间 */
}

.table-top-text {
	margin-bottom: 10px;
	/* 给表格和按钮留出一些垂直空间 */
	vertical-align: middle;
	/* 确保文字和按钮居中对齐 */
}

.el-table th,
.el-table td {
	border: 2px solid #ebeef5;
}
</style>
