<template>
    <el-form v-if="!params.editable" :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="190px" size="default" v-loading="loading" @submit.prevent>
        <el-row>
            <el-col :span="24">
                <div style="padding: 10px 30px;" v-html="formData.GGNR"></div>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="24" class="grid-cell">
                <el-form-item label="公告文件：">
                    <vsfileupload style="width:100%" :busId="formData.GGID" :key="formData.GGID" :editable="false" ywlb="GGWJ" busType="GGWJ">
                    </vsfileupload>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
            <el-col :span="24" style="text-align: center;">
                <el-button @click="closeForm">返回</el-button>
            </el-col>
        </el-row>
    </el-form>
    <el-form v-else :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="190px" size="default" v-loading="loading" @submit.prevent>
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标项目名称：">
                    <el-input v-model="formData.XMMC" placeholder="请输入" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标项目编号：">
                    <el-input v-model="formData.XMBH" placeholder="请输入" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="招标单位：">
                    <el-input v-model="formData.SSDWMC" placeholder="请输入" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="24" class="grid-cell no-border-bottom">
                <el-form-item label="公告内容：">
                    <newQuillEditor style="width: 100%;background-color: white" v-model="formData.GGNR" :editable="params.editable"/>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="12" class="grid-cell">
                <el-form-item label="确认是否参加截止时间：">
                    <el-date-picker
                        v-model="formData.QRSFCJTBSJ"
                        type="datetime"
                        clearable
                        style="width: 100%"
                        placeholder="请选择"
                        format="YYYY-MM-DD HH:mm"
                        value-format="YYYY-MM-DD HH:mm"
                        :disabled="!params.editable"
                    ></el-date-picker>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row class="grid-row">
            <el-col :span="24" class="grid-cell">
                <el-form-item label="公告文件：">
                    <vsfileupload style="width:100%" :busId="formData.GGID" :key="formData.GGID" :editable="params.editable" ywlb="GGWJ" busType="GGWJ">
                    </vsfileupload>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
            <el-col :span="24" style="font-size: 20px; font-weight: bold;">
                以下承包商/队伍能够收到公告:
            </el-col>
        </el-row>
        <el-row class="grid-row" style="margin-top: 10px">
            <el-col :span="24" class="grid-cell">
                <el-table 
                    :data="tableData" 
                    class="lui-table"
                    border
                    stripe
                    size="default" 
                    highlight-current-row>
                    <el-table-column prop="CBSDWQC" label="单位名称" header-align="center" align="left" min-width="220">
                    </el-table-column>
                    <el-table-column prop="DWMC" label="队伍名称" header-align="center" align="left" min-width="220">
                    </el-table-column>
                    <el-table-column prop="DWLBMC" label="队伍类型" header-align="center" align="center" min-width="220">
                    </el-table-column>
                    <el-table-column prop="DWZTMC" label="队伍状态" header-align="center" align="center" min-width="80">
                    </el-table-column>
                    <el-table-column label="征信" align="center" min-width="100">
                        <template #default="scope">
                            <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row,'ZX')">查看
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="风险" align="center" min-width="100">
                        <template #default="scope">
                            <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row,'FX')">查看
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
            <el-col :span="24" style="text-align: center;">
                <el-button type="primary" @click="saveData('0')" v-if="params.editable">保存</el-button>
                <el-button type="success" @click="saveData('2')" v-if="params.editable">发布</el-button>
                <el-button @click="closeForm">返回</el-button>
            </el-col>
        </el-row>
    </el-form>
    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="showDialog"
        v-model="showDialog"
        :title="title"
        top="5vh"
        width="80%">
        <cbszxView v-if="dialogType === 'ZX'" :params="dialogParam"></cbszxView>
        <cbsfxList v-if="dialogType === 'FX'" :params="dialogParam"></cbsfxList>
    </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import newQuillEditor from "@views/components/newQuillEditor";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import VSAuth from "@src/lib/vsAuth";
import cbszxView from "@views/cbs/queryanalysis/cbsda/cbszxView.vue";
import cbsfxList from "@views/cbs/queryanalysis/cbsda/cbsfxList.vue";
const props = defineProps({
    params: {
      type: Object,
      required: true
    },
});
const emit = defineEmits(["close"]);
const loading = ref(false);
const formData = ref({});
const tableData = ref([]);

const showDialog = ref(false);
const dialogType = ref('');
const dialogParam = ref({});
const title = ref('');
onMounted(() => {
    initFormData();
});

const initFormData = () => {
    loading.value = true;
    axiosUtil.get('/backend/xsgl/xstzfb/queryCgggForm', {
        wjid: props.params.WJID
    }).then(res=>{
        loading.value = false;
        formData.value = res.data.form;
        if(!formData.value.GGID){
            formData.value.GGID = comFun.newId();
        }
        if(!formData.value.GGNR){
            formData.value.GGNR = "";
        }
        tableData.value = res.data.list;
    }).catch((err) => {
        loading.value = false;
    });
}

const saveData = (val) => {
    let params = {
        JLID: props.params.JLID,
        GGID: formData.value.GGID,
        GGNR: formData.value.GGNR,
        WJID: props.params.WJID,
        XMMC: formData.value.XMMC,
        QRSFCJTBSJ: formData.value.QRSFCJTBSJ,
        CJRZH: VSAuth.getAuthInfo().permission.userLoginName,
        CJRXM: VSAuth.getAuthInfo().permission.userName,
        CJDWID: VSAuth.getAuthInfo().permission.orgnaId,
        SHZT: val,
        YWZT: '1'
    }
    axiosUtil.post('/backend/xsgl/xstzfb/saveCgggfb', 
        params
    ).then(res=>{
        if(res.data.success){
            ElMessage.success("保存成功！");
            closeForm();
        }else{
            ElMessage.error("保存失败！");
        }
    })
}

const viewRow = (row,type) => {
    if(type === 'ZX'){
        title.value = '征信';
    }else if(type === 'FX'){
        title.value = '风险';
    }
    dialogType.value = type;
    dialogParam.value = {
        CBSDWQC: row.CBSDWQC
    };
    showDialog.value = true;
}

const closeForm = () => {
    emit('close')
}
defineExpose({});
</script>

<style scoped>
</style>