<template>
  <div>
    <el-form :model="tableData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;color: #2A96F9;
      border-bottom: 3px solid #2A96F9;text-align: center;padding-bottom: 2px;padding-left: 20px;padding-right: 20px;">
        投标文件解密
      </div>

      <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 520px)"
                :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column prop="CBSDWQC" label="投标单位名称" align="center"
                         :show-overflow-tooltip="true" min-width="160"></el-table-column>
        <el-table-column prop="YSBCZ" label="压缩包操作" align="center"
                         :show-overflow-tooltip="true" width="220">
          <template #default="{row,$index}">
            <el-button size="small" type="primary" class="lui-table-button" @click="decryptTBWJ(row,$index)"
                       :loading="loadingRow.has(row.HHID)" v-if="editable">解密</el-button>
            <vsfileupload
                :ref="'YSBCZ_'+$index"
                :editable="!row.BSJYSJ && fromParams.role==='ZCR' && editable"
                :busId="row.HHID"
                :key="row.HHID"
                ywlb="TBWJ"
                busType="TBWJ"
                :limit="1"
                accept=".zip,.ZIP"
            ></vsfileupload>
          </template>
        </el-table-column>

        <el-table-column prop="TBWJSC" label="投标文件上传" align="center"
                         :show-overflow-tooltip="true" width="220">
          <template #default="{row,$index}">
            <vsfileupload
                :ref="'TBWJJM_'+$index"
                :editable="false"
                :busId="row.HHID"
                :key="row.HHID"
                ywlb="TBWJJM"
                busType="TBWJJM"
                :limit="1"
                accept=".PDF,.pdf"
            ></vsfileupload>
          </template>
        </el-table-column>

        <el-table-column prop="SQWTS" label="授权委托书" align="center"
                         :show-overflow-tooltip="true" width="150">
          <template #default="{row,$index}">
            <vsfileupload
                :ref="'SQWTS_'+$index"
                :editable="parentForm.BSJY_WCZT!=='1'"
                :busId="row.QRHHID"
                :key="row.QRHHID"
                ywlb="SQWTS"
                busType="SQWTS"
                :limit="1"
            ></vsfileupload>
          </template>
        </el-table-column>
      </el-table>


      <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;">
        <el-button size="default" @click="refreshFile">刷新</el-button>
        <el-button size="default" type="primary" @click="saveData('submit')" v-if="fromParams.role==='ZCR' && parentForm.BSJY_WCZT!=='1'">下一步</el-button>
        <el-button size="default" type="primary" v-else-if="fromParams.role==='ZCR'">已完成</el-button>
      </div>
    </el-form>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import vsAuth from "@lib/vsAuth";
import vsfileupload from "@views/components/vsfileupload";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "@lib/comFun";



export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.parentForm.BSJY_WCZT!=='1',
      role: props.fromParams.role,
      KPBYXID: props.params.KPBYXID,
      JLID: props.params.JLID,
      tableData: [],
      rules: {},
      loadingRow: new Set()
    })


    const getTableData = () => {
      let params={
        JLID: state.JLID
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/bsjm/selectBsjmList', params).then((res) => {
        state.tableData=res.data || []
        state.loading=false
      })
    }


    const instance = getCurrentInstance()
    const decryptTBWJ = (row,index) => {
      ElMessageBox.prompt('请输入解压密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '解压密码不能为空',

      }).then(({value}) => {
        let params={
          HHID: row.HHID,
          JYMM: value,
          BSJYSJ: comFun.getNowTime(),
          KBJLID: props.params.KBJLID,
          CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
          CJRXM: vsAuth.getAuthInfo().permission.userName,
          CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        }
        state.loadingRow.add(row.HHID)
        axiosUtil.get('/backend/kpbyx/bsjm/decryptTBWJ', params).then((res) => {
          let resData=Object.values(res.data)
          let CWXX=resData.find(item=>item.ZT!=='1')
          if(CWXX){
            ElMessage.error('解压失败：'+CWXX.msg)
          }else {
            row.BSJYSJ = params.BSJYSJ
            instance.proxy.$refs['TBWJJM_'+index].loadFileList()
            ElMessage.success("解压成功")
          }
          state.loadingRow.delete(row.HHID)
        }).catch((err) => {
          state.loadingRow.delete(row.HHID)
          ElMessage.error("解压失败：其它错误")
        })
        console.log(value)

      }).catch(() => {

      })


    }

    const saveData = () => {
      ElMessageBox.confirm('确定提交投标文件解密?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.loading=true
        emit('saveFromData', {BSJY_WCZT: '1',BSJY_WCSJ: comFun.getNowTime(),BSJY_SFGS: '1'},{})
        nextTick(()=>{
          emit('nextStep','投标文件已解密，请在文件查看窗口进行查看')
        })
      }).catch(() => {})


    }


    const refreshFile = () => {
      state.tableData.forEach((item,index) => {
        instance.proxy.$refs['YSBCZ_'+index].loadFileList()
        instance.proxy.$refs['TBWJJM_'+index].loadFileList()
        instance.proxy.$refs['SQWTS_'+index].loadFileList()
      })
    }

    onMounted(() => {
      getTableData()
    })

    return {
      ...toRefs(state),
      decryptTBWJ,
      refreshFile,
      saveData

    }
  }

})
</script>

<style scoped>

</style>
