<template>
  <el-form class="lui-card-form" :model="formData" ref="saveFormRef" label-width="120px" status-icon size="default">
    <el-row>
      <!-- <div class="button-box">
        <el-button type="info" @click="handleClose()">关闭</el-button>
      </div> -->
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="企业名称">
          <el-input v-model="formData.cbsmc" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="队伍名称">
          <el-input v-if="formData.dwlx == 'DW'" v-model="formData.dwmc" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="准入证号">
          <el-input v-model="formData.zrzh" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="有效期">
          <span style="padding-left: 11px;">{{ formData.yxqks.split(' ')[0] }} - {{ formData.yxqjs.split(' ')[0]
          }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="当前状态">
          <el-select v-model="formData.dwzt" disabled>
            <el-option v-for="item in dqztOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="变更状态" prop="bgzt">
          <el-select v-model="formData.bgzt" clearable disabled>
            <el-option v-for="item in bgztOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="变更原因" prop="bgyy" class="custom-label">
          <el-input type="textarea" :rows="2" placeholder="请输入变更原因" v-model="formData.bgyy" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="相关附件">
          <vsfileupload class="custom-upload" v-if="formData.dwywid" :key="formData.dwywid" :busId="formData.dwywid"
            :busType="'xbcbsrcgl'" :ywlb="'xbcbsrcgl'" :editable="false"></vsfileupload>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import { onMounted, ref, reactive, toRefs } from 'vue';
import { queryDwxxByJl, dwztbgCallback } from "@/api/xbcbsrcgl.js";
import { ElMessage, ElMessageBox } from 'element-plus';
import vsfileupload from "@views/components/vsfileupload";
import vsflow from "@views/vsflow/index.js";
import VSAuth from "@src/lib/vsAuth";

const state = reactive({
  loginName: "",
  formData: {
    dwywid: "",
    cbsmc: "", // 承包商名称
    dwlx: "", // 队伍类型
    dwmc: "", // 队伍名称
    zrzh: "", // 准入证号
    yxqks: "", // 有效期开始
    yxqjs: "", // 有效期结束
    dwzt: "", // 当前状态
    bgzt: "", // 变更状态
    bz: "", // 备注
    xgrzh: "", // 修改人账号
    xgsj: "", // 修改时间
    bgyy: "", // 变更原因
  },
  dqztOptions: [
    { value: "1", label: "正常" },
    { value: "2", label: "暂停投标" },
    { value: "3", label: "停工整顿" },
    { value: "4", label: "取消投标资格" },
  ],
  bgztOptions: [
    { value: "DW_JY_HF", label: "正常" },
    { value: "DW_JY_ZT", label: "暂停投标" },
    { value: "DW_JY_TG", label: "停工整顿" },
    { value: "DW_JY_QX", label: "取消投标资格" },
  ],
  blrInfo: {}, // 办理人信息
  dialogVisible: false,
});
const { loginName, formData, dqztOptions, bgztOptions, dialogVisible } = toRefs(state);
const saveFormRef = ref(null);

// 记录表中类型与人工调整状态值的映射
const lxMap = new Map([
  ["DW_JY_HF", ""],
  ["DW_JY_ZT", "2"],
  ["DW_JY_TG", "3"],
  ["DW_JY_QX", "4"]
]);

// 定义 props
const props = defineProps({
  params: {
    type: Object,
    default: () => { },
  },
});
const emit = defineEmits(['handleClose']); // 定义关闭事件

// 查询队伍/承包商信息
const queryDwInfo = () => {
  const params = {
    jlid: props.params.processInstanceId,
  }
  queryDwxxByJl(params).then((res) => {
    if (res.data) {
      formData.value = res.data;
      formData.value.bgyy = res.data.yy;
      formData.value.bgzt = res.data.lx;
    }
  })
};

// 传入跳转变量和审核变量
const handleSubmit = async (suggestflg, tzbl, shbl) => {
  try {
    let taskResult = {};
    const result = await callback(suggestflg);
    if (result.meta.success) {
      // 完成任务
      taskResult = await vsflow.finishTask(
        props.params.taskId,
        loginName.value,
        suggestflg,
        props.params.spyj,
        null,
        null,
        tzbl,
        shbl,
      );
    }
    if (taskResult.success) {
      ElMessage.success("提交成功");
      handleClose();
    } else {
      ElMessage.error("流程发起失败");
    }
  } catch (error) {
    ElMessage.error("提交过程中发生错误");
  }
};

// 回调函数
const callback = async (flag) => {
  const params = {
    dwwybs: formData.value.dwwybs, // 队伍唯一标识
    rgtzzt: lxMap.get(formData.value.lx), // 人共同调整状态
    jlid: props.params.processInstanceId,
    shrid: VSAuth.getAuthInfo().permission.userId,
    shrmc: VSAuth.getAuthInfo().permission.userName,
    shyj: null,
    zt: '2',
    flag: flag ? flag : '0'
  }
  return dwztbgCallback(params);
}

// 关闭表单
const handleClose = () => {
  emit('handleClose');
};

// 子组件暴露方法
defineExpose({
  handleSubmit

});

onMounted(() => {
  queryDwInfo();
  loginName.value = VSAuth.getAuthInfo().permission.userLoginName;
});
</script>

<style scoped>
.button-box {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0.5rem;
}

.el-form-item>>>.el-input__validateIcon {
  display: none;
}

.custom-label>>>.el-form-item__label {
  border-bottom: 0;
}

.custom-upload {
  margin-left: 0.5rem;
}

.custom-upload>>>div:first-child {
  display: flex;
  gap: 0.5rem;
}

.custom-upload>>>.el-upload-list {
  display: flex;
  align-items: center;
  margin: 0;
}

.custom-upload>>>.el-upload-list__item {
  margin-bottom: 0;
  transition: opacity 0.5s cubic-bezier(.55, 0, .1, 1);
}

.custom-upload>>>.el-upload-list__item-file-name {
  max-width: 10rem;
}

.custom-upload>>>.el-progress__text {
  color: gray;
  position: absolute;
  right: -15px;
  top: -18px;
}

.lui-card-form>>>.el-form-item__label {
  border-bottom: 0px;
}

.lui-card-form>>>.el-form-item__content {
  border-bottom: 0px;
}
</style>