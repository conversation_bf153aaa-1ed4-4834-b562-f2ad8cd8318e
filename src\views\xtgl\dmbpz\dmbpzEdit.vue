<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="140px"
           size="default" v-loading="loading" @submit.prevent>
    <el-row :gutter="0" class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="代码类别ID：" prop="DMLBID">
          <el-input v-model="formData.DMLBID" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
        <el-form-item label="代码类别名称：" prop="DMLBMC">
          <el-input v-model="formData.DMLBMC" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="grid-cell">
        <el-form-item label="排序号：" prop="PXH">
          <el-input v-model="formData.PXH" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>



    </el-row>

    <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
      <el-button type="success" @click="saveData()" v-if="editable">保存</el-button>
      <el-button @click="closeForm">返回</el-button>
    </div>

  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: true,
      DMLBID: props.params.id,
      formData:{

      },
      rules: {
        DMLBMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        DMLBID: [{
          required: true,
          validator: async (rule, value, callback) => {
            if (!value) {
              callback(new Error('字段值不可为空'))
            } else {
              let isCF = await checkProp(value,'DMLBID')
              if(Boolean(isCF.data)){
                callback()
              }else {
                callback(new Error('代码类别ID不能重复'))
              }
            }
          },
        }],
      },
      loading: false,

    })

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getFormData = () => {
      let params = {
        DMLBID: state.DMLBID
      }
      state.loading = true
      axiosUtil.get('/backend/dmbpz/selectDmlbById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      });
    }

    const checkProp = (value,prop) => {
      let params={
        DMLBID: state.DMLBID,
        value: value,
        prop: prop
      }
      return axiosUtil.get('/backend/dmbpz/checkProp', params)
    }


    const submitForm = () => {
      let params={
        ...state.formData,
        SFYX: '1'
      }
      axiosUtil.post('/backend/dmbpz/saveDmlbForm', params).then((res) => {
        ElMessage.success('保存成功')
      });
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }


    const closeForm = () => {
      emit('close')
    }


    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      }

    })

    return {
      ...toRefs(state),
      saveData,
      closeForm

    }
  }

})
</script>

<style scoped>

</style>
