<template>
    <div style="width: 100%;height: 100%;background-color: #FFFFFF">
        <div class="header">
            <el-form :inline="true">
                <el-form-item label="队伍名称">
                    <el-input v-model="data.queryForm.dwmc" @keyup.enter="query"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="query">查询</el-button>
                </el-form-item>

            </el-form>
        </div>
        <div class="main">
            <el-table
                    highlight-current-row
                    size="small"
                    ref="table"
                    fit
                    border
                    :border="false"
                    :data="data.tableData"
            >
                <EleProTableColumn
                        v-for="prop in data.tableColumn"
                        :col="prop"
                        :key="prop.columnKey"
                >
                    <template #opration="{row,$index}">
                        <div>
                            <!--todo-->
                            <el-button type="text" @click="goWrite(row)">发起复审</el-button>
                        </div>
                    </template>
                    <template #status="{row,$index}">
                        <span v-if="row.DWZT=='ZC'">正常</span>
                        <span v-if="row.DWZT=='QX'">取消</span>
                        <span v-if="row.DWZT=='ZT'">暂停</span>
                        <span v-if="row.DWZT=='YQ'">延长期限</span>
                    </template>
                </EleProTableColumn>
            </el-table>
        </div>
        <div class="footer">
            <el-pagination
                v-model:current-page="data.queryForm.page"
                v-model:page-size="data.queryForm.size"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="data.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>
<script setup>
import {onMounted, onUnmounted, reactive,} from "vue";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getBgGetChangedTeamById, getCbszxAddTeamList, getCbszxCopyAddInfo,getBgCbsqyxx,getTeamreslutGetProDetails} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import {v4 as uuidv4} from "uuid";
import tabFun from "@src/lib/tabFun";
import {mixin} from '@src/assets/core/index';
import zxzyzx from "./index.vue";

const {vsuiEventbus} = mixin()
const data = reactive({
    total: 0,
    queryForm: {
        dwmc: null,
        page: 1,
        size: 10
    },
    changeReason:[],
    saveForm: {
        date: ''
    },
    teamStatus:[
        {
            value: 'ZC',
            name: '正常'
        },
        {
            value: 'ZT',
            name: '暂停'
        },
        {
            value: 'QX',
            name: '取消'
        },
        {
            value: 'YQ',
            name: '延长期限'
        },
    ],
    currentUser: {},
    saveVisible: false,
    tableData: [],
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 55,
            align: "center"
        },
        {
            label: "队伍名称",
            prop: "DWMC",
            align: "left",
            showOverflowTooltip: true,
            width: '200px'
            // slot: "faultHours",
        },
        {
            label: "申请类别",
            prop: "SQLBMC",
            align: "center",
            width: '200px'
            // slot: "faultHours",
        },
        {
            label: "备案类型",
            prop: "BALXMC",
            align: "center",
            width: '200px'
            // slot: "faultHours",
        },
        {
            label: "队伍类型",
            prop: "DWLBMC",
            align: "center",
            width: '200px'
            // slot: "faultHours",
        },
        {
            label: "服务范围",
            prop: "fwfw",
            align: "LEFT",
            showOverflowTooltip: true,
        },
        {
            label: "操作",
            align: "center",
            width: 150,
            fixed: "right",
            slot: "opration",
        },
    ],
})

const resetQuery = () => {
    data.queryForm = {
        yjlx: null,
        dwmc: null,
        dwzt: null,
        page: 1,
        size: 10
    }
}

const goWrite = (row) => {
    let param = {
        DWYWID: row.DWYWID,
        XNDWYWID: uuidv4().replace(/-/g, ''),
        NEWTYPE: 'FS'
    }
    getBgCbsqyxx(param).then(res=>{
        if(row.DWLX == 'CBS'){
            getTeamreslutGetProDetails({dwid:row.DWYWID}).then(res=>{
                tabFun.addTabByRoutePath(
                  "承包商基本信息复审",
                  "/contractors/cbsjbxxIndex",
                  {
                      uuId : param.XNDWYWID, //队伍业务ID
                      MBID: res.data.MBID, //模板ID
                      MBLX: "QY", //模板类型、
                      ZYFLDM: res.data.ZYFLDM, //专业分类代码
                      YWLXDM: "FS", //业务类型代码
                  },
                  {}
                );
                vsuiEventbus.emit("reloadCbsjbxx", {
                    uuId : param.XNDWYWID, //队伍业务ID
                    MBID: res.data.MBID, //模板ID
                    MBLX: "QY", //模板类型、
                    ZYFLDM: res.data.ZYFLDM, //专业分类代码
                    YWLXDM: "FS", //业务类型代码
                });
            })
        }else if(row.DWLX == 'DW'){
            getBgGetChangedTeamById({dwid:param.XNDWYWID}).then(r=>{
                tabFun.addTabByRoutePath('队伍信息复审', '/contractors/yrsqxxIndex', {DWYWID: r.data.DWYWID,YWLXDM: 'FS',editable:true,backPath: '/contractors/teamReviewList'}, );
            })

        }
    })

    // tabFun.addTabByRoutePath('引入申请信息', '/contractors/yrsqxxIndex', {DWYWID: row.DWYWID, type: 'cqyj'}, );
    // router.push("/contractors/yrsqxx")
}

const query = () => {
    getCbszxAddTeamList({
        ...data.queryForm,
        orgid: data.currentUser.ORGNA_ID
    }).then(res => {
        data.tableData = res.data.list
        data.total = res.data.total
    })

}

const getUserInfo = () => {
    let user = VSAuth.getAuthInfo().permission
    getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
        data.currentUser = res.data;
        query();
    })
}

const handleSizeChange = (val) => {
    data.queryForm.size = val;
    query();
}
const handleCurrentChange = (val) => {
    data.queryForm.page = val;
    query();
}


onMounted(() => {
    getUserInfo();
    vsuiEventbus.on('reloadTableData',getUserInfo);
})
onMounted(() => {

})
onUnmounted(()=>{
    // timer.value = null;
    vsuiEventbus.off('reloadTableData',getUserInfo);
})
</script>

<style scoped>
.header {
    padding: 10px 10px 0;
    height: 50px;
    line-height: 50px;
}

.main {
    height: calc(100% - 155px);
    padding: 10px;
}
.footer {
    height: 100px;
    line-height: 100px;
}
</style>
