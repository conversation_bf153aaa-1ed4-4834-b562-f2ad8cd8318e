<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="120px"
           size="default" @submit.prevent>
    <el-collapse v-model="openCollapse">
      <el-row :gutter="0" class="grid-row" style="margin-bottom:16px;">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="申请时间:" prop="SQSJ" class="required">
            <el-input v-model="formData.SQSJ" :disabled="true" type="text" placeholder="携带" clearable>
            </el-input>
          </el-form-item>
        </el-col>
<!--        <el-col :span="8" class="grid-cell">-->
<!--          <el-form-item label="快捷出库原因:" label-width="120px" prop="CKYYFL" class="required">-->
<!--            <el-select v-model="formData.CKYYFL" class="full-width-input" :disabled="!editable" clearable>-->
<!--              <el-option v-for="(item, index) in CKYYFLOptions" :key="index" :label="item.DMMC"-->
<!--                         :value="item.DMXX"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="8" class="grid-cell">-->
<!--          <div class="static-content-item">-->
<!--            <div style="margin-left: 10px;margin-top: 10px;color: red">提示:如因后三种原因，需要有处理应用文件资料。</div>-->
<!--          </div>-->
<!--        </el-col>-->
        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="出库原因:" prop="CKYYSM" class="required">
            <el-input type="textarea" v-model="formData.CKYYSM" placeholder="请输入" rows="3"
                      :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-collapse-item title="出库专家明细" name="1">
        <el-row :gutter="0" class="grid-row">
          <el-col :span="24" class="grid-cell">
            <div class="button_folat static-content-item">
              <el-button type="primary" style="margin-top: 5px;margin-bottom: 5px" @click="openDialog"
                         v-if="editable">选择
              </el-button>
            </div>
            <div class="container-wrapper">
              <el-table ref="dataTable" :data="ckzjMxData" height="250px" class="lui-table"
                        :style="{width: '100%'}" :border="true" :show-summary="false" :stripe="true"
                        :highlight-current-row="true" :cell-style="{padding: '5px 0 '}">
                <el-table-column type="index" width="50" fixed="left" label="序号"></el-table-column>
                <el-table-column v-if="true" prop="XM" label="姓名" :fixed="false" align="left"
                                 :show-overflow-tooltip="true"
                                 min-width="160">
                </el-table-column>
                <el-table-column v-if="true" prop="GZDWMC" label="工作单位" :fixed="false" align="left"
                                 :show-overflow-tooltip="true" min-width="160">
                </el-table-column>
                <el-table-column v-if="true" prop="XZZW" label="职务" :fixed="false" align="left"
                                 :show-overflow-tooltip="true" min-width="160">
                </el-table-column>
                <el-table-column v-if="true" prop="RKZJBXX.SBZY" label="申报专业" :fixed="false" align="left"
                                 :show-overflow-tooltip="true" min-width="160">
                </el-table-column>
                <el-table-column v-if="true" prop="ZJLBMC" label="专家类别" :fixed="false" align="left"
                                 :show-overflow-tooltip="true" min-width="160">
                </el-table-column>
                <el-table-column fixed="right" class-name="data-table-buttons-column" align="center" label="操作"
                                 :width="120" v-if="editable">
                  <template #default="scope">
                    <el-button size="small" :disabled="false" class="lui-table-button"
                               @click.stop="deleteRow(scope.$index, ckzjMxData,scope.row)">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item title="业务资料" name="2">

        <el-row :gutter="0" class="grid-row">
          <el-col :span="24" class="grid-cell">
            <VsFileUploadTable style="width: 100%;height: 300px" YWLX="ZJCKYWZL" :key="params.id" :busId="params.id"
                               v-model:fileTableData="fileTableData" viewType="YL" :editable="editable"/>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
    <el-row :gutter="12">
      <el-col :span="24" class="grid-cell button_center" style="padding-top: 10px">
        <el-button type="success" @click="validateForm('save')" v-if="operation!=='view'">暂存</el-button>
        <el-button type="primary" @click="validateForm('submit')" v-if="operation!=='view'">提交</el-button>
        <el-button @click="closeForm">关闭</el-button>
      </el-col>
    </el-row>

  </el-form>
  <el-dialog
      v-if="zjdialogVisible"
      v-model="zjdialogVisible"
      title="专家出库选择"
      @closed="closeZjForm"
      top="2vh"
      width="70%">
    <div>
      <zjckXzForm :zjParams="zjParams" @closeZjForm="closeZjForm" @parentMethod="parentMethod"/>
    </div>
  </el-dialog>

</template>

<script>
import {
  defineComponent,
  ref,
  shallowRef,
  toRefs,
  reactive,
  getCurrentInstance,
  watchEffect,
  onMounted
}
  from 'vue'
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth.js";
import {ElMessage} from 'element-plus'
import comFun from "../../../lib/comFun";
import zjckXzForm from "./ckzjxzList.vue"
import vsfileupload from "../../components/vsfileupload";
import VsFileUploadTable from "../../components/vsFileUploadTable";

export default defineComponent({
  components: {zjckXzForm, vsfileupload, VsFileUploadTable},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, context) {
    const state = reactive({
      editable: props.params.editable,
      openCollapse: ['1', '2'],
      operation: props.params.operation,
      id: props.params.id,
      userInfo: vsAuth.getAuthInfo().permission,
      zjdialogVisible: false,
      zjParams: {
        tableData: [],
      },
      formData: {
        SQSJ: comFun.getNowTime(),
        CKYYFL: null,
        CKYYSM: null,
        fileList: null,
      },
      ckywzlData: [
        {
          LX: '相关附件',
          id: props.params.id
        }
      ],
      ckzjMxData: [],
      rules: {
        SQSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        CKYYFL: [{
          required: true,
          message: '字段值不可为空',
        }],
        CKYYSM: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      CKYYFLOptions: [],
      fileTableData: []
    })
    const methods = {}
    const instance = getCurrentInstance()
    const resetForm = () => {
      instance.proxy.$refs['vForm'].resetFields()
    }
    const closeZjForm = () => {
      state.zjdialogVisible = false
      //
    }
    const parentMethod = (val) => {
      let data = val
      data.forEach(item => {
        item.GLZJMXID = comFun.newId();
        item.GLZJID = props.params.id;
        item.XCSZYBM = item.XCSZY
      })
      state.ckzjMxData = data
      state.zjdialogVisible = false
    }
    const openDialog = () => {
      state.zjParams.tableData = state.ckzjMxData
      state.zjdialogVisible = true
    }
    const validateForm = async (type) => {
      return new Promise(resolve => {
        console.log(state.formData)
        if (type === 'submit') {
          console.log(state.formData)
          instance.proxy.$refs['vForm'].validate(valid => {
            // if(state.formData.fileList.length==0){
            //   ElMessage({
            //     message: `请上传相关附件`,
            //     type: 'error',
            //   })
            //   return
            // }
            if (state.ckzjMxData.length == 0) {
              ElMessage({
                message: `请选择出库专家`,
                type: 'error',
              })
              return
            }
            if (valid) {
              state.formData.SHZT = '2'
              //TODO: 提交表单
              resolve(submitForm(type))
            } else {
              resolve(false)
            }
          })
        } else {
          state.formData.SHZT = '0'
          resolve(submitForm(type))
        }
      })
    }
    const submitForm = (type) => {
      let params = {
        ...state.formData,
        JGDM: state.userInfo.orgnaId,
      }
      if (state.operation != 'add') {
        params.XGR = state.userInfo.userLoginName
        params.XGSJ = comFun.getNowTime();
      } else {
        params.GLZJID = props.params.id;
        params.HDFL = 'CK';
        params.CJR = state.userInfo.userLoginName
        params.CJSJ = comFun.getNowTime();
      }
      if (type == 'submit') {
        params.SHR = state.userInfo.userLoginName
        params.SHRQ = comFun.getNowTime();
      }
      params.tableData = state.ckzjMxData
      return new Promise(resolve => {
        axiosUtil.post('/backend/zjgl/zjckgl/saveZjckData', params).then((res) => {
          if (res.message === 'success') {
            resolve(true)
            ElMessage({
              message: `${type === 'submit' ? '提交' : '保存'}成功`,
              type: 'success',
            })
            if (type == 'submit') {
              context.emit("closeForm")
            }
          } else {
            resolve(false)
          }
        });
      })
    }
    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      var res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }
    const getCkxx = () => {
      let params = {
        GLZJID: state.id
      }
      axiosUtil.get('/backend/zjgl/zjckgl/selectCkxxByGlzjid', params).then((res) => {
        if (res.data.length > 0) {
          state.formData = {...res.data[0]}
        }
      });
    }
    const getCkzjList = () => {
      let params = {
        GLZJID: state.id
      }
      axiosUtil.get('/backend/zjgl/zjckgl/selectCkmxByGlzjid', params).then((res) => {
        if (res.data.length > 0) {
          state.ckzjMxData = res.data
        }
      });
    }
    const closeForm = () => {
      context.emit("closeForm")
    }
    const deleteRow = (index, data, row) => {
      data.splice(index, 1);
      RealDelete(row)//对tableData中的数据删除一行
    }
    const RealDelete = (row) => {
      axiosUtil.post('/backend/zjgl/zjckgl/deleteZjmx', row).then((res) => {
        if (res.message === 'success') {
          ElMessage({
            message: `删除成功`,
            type: 'success',
          })
          getCkzjList();
        }

      });
    }

    onMounted(() => {
      let params = props.params
      if (params && params.operation !== 'add') {
        getCkxx();
        getCkzjList();
      }
      getDMBData("CKYY", "CKYYFLOptions")
    })
    return {
      ...toRefs(state),
      ...methods,
      submitForm,
      resetForm,
      closeZjForm,
      openDialog,
      validateForm, parentMethod, getDMBData, getCkxx, getCkzjList, deleteRow, RealDelete, closeForm
    }
  }
})

</script>

<style scoped>

.button_center {
  text-align: center;
  margin: 0 auto;
}

.button_center div {
  margin: 5px 30px;
}

.button_folat {
  float: right;
  margin-right: 10px;
  display: inline-block;
}
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: unset;
}

/deep/ .el-collapse-item__header {
  background-color: #dcdfef !important;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
  font-size: 16px;
  color: black !important;
}
</style>


