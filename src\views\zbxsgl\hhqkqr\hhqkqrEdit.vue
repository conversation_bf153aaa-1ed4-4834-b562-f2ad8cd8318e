<template>
  <div>
    <el-form :model="formData" ref="vForm" class="lui-card-form" label-position="right"
             label-width="180px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{formData.XMXX.XMMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{formData.XMXX.XMBH}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标单位：" prop="ZGDWID">
            <div style="margin-left: 10px">{{formData.XMXX.SSDWMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="确认是否参加截止时间：" prop="ZGDWID">
            <div style="margin-left: 10px">{{formData.QRSFCJJZSJ}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="报名回函单位明细：" prop="HMHDDWMC">
            <el-button style="margin-left: 10px" type="primary" @click="addCjdw">添加</el-button>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-table ref="datatable91634" :data="[...formData.QRHHList,getCountRow()]" height="400px"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :span-method="arraySpanMethod"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="CBSDWQC" label="回函单位名称" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
<!--            <el-table-column prop="DWMC" label="回函队伍名称" align="center"-->
<!--                             :show-overflow-tooltip="true" min-width="160"></el-table-column>-->
            <el-table-column prop="SFCY" label="是否参与" align="center"
                             :show-overflow-tooltip="true" width="80">
              <template #default="{row}">
                <div v-if="row.SFCY==='1'">参与</div>
                <div v-if="row.SFCY==='0'">不参与</div>
              </template>
            </el-table-column>

            <el-table-column prop="SFCY" :label="`标段${index+1}（${item.BDMC}）`" align="center"
                             v-for="(item,index) in formData.XMXX.SFFBD==='1' ?formData.XMXX.BDList : []" :key="index"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row}">
                <el-checkbox :disabled="row.LY!=='SDTJ' || !editable" v-model="row.BDCYQK[item.FABDID]" label="" size="default" v-if="row.type!=='count'"/>
                <div v-else>{{row.BDCYQK[item.FABDID]}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="LXR" label="联系人" align="center"
                             :show-overflow-tooltip="true" width="100"></el-table-column>
            <el-table-column prop="LXDH" label="联系电话" align="center"
                             :show-overflow-tooltip="true" width="100"></el-table-column>
            <el-table-column prop="LXYX" label="联系邮箱" align="center"
                             :show-overflow-tooltip="true" width="100"></el-table-column>
            <el-table-column prop="BMSJ" label="确认时间" align="center"
                             :show-overflow-tooltip="true" width="180"></el-table-column>
            <el-table-column prop="QRH" label="确认函" align="center"
                             :show-overflow-tooltip="true" width="160">
              <template #default="{row}">
                <vsfileupload
                    style="margin-left: 10px"
                    :editable="false"
                    :busId="row.QRHHID"
                    :key="row.QRHHID"
                    ywlb="QRH"
                    busType="QRH"
                    :limit="100"
                ></vsfileupload>
              </template>
            </el-table-column>
            <el-table-column prop="SHZT" label="审核状态" align="center"
                             :show-overflow-tooltip="true" width="160">
              <template #default="{row,$index}">
                <el-form-item label="" :prop="`QRHHList.${$index}.SHZT`" label-width="0" :rules="rules.SHZT" v-if="row.type!=='count'">
                  <el-select v-model="row.SHZT" class="full-width-input" :disabled="!editable">
                    <el-option v-for="(item, index) in SHZTOptions" :key="index" :label="item.DMMC"
                               :value="item.DMXX"></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>


        </el-col>

      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogBmdwVisible"
        v-model="dialogBmdwVisible"
        title="报名单位选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <dwChoose @close="dialogBmdwVisible=false" @submit="getBmdwRes" :params="params"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import dwChoose from "@views/zbxsgl/xssqgl/dwChoose";

export default defineComponent({
  name: '',
  components: {vsfileupload,dwChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GGID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',
        XMXX: {
          BDList: []
        },

        QRHHList: []
      },
      rules: {
        SHZT: [{
          required: true,
          validator: (rule, value, callback) => {
            if(!value || value==='1'){
              callback(new Error('待选择'))
            }else {
              callback()
            }
          }
        }],
        SFGMBS: [{
          required: true,
          message: '待选择',
        }],
      },

      SHZTOptions:[{DMXX: '1',DMMC: '待审核'},{DMXX: '2',DMMC: '通过'},{DMXX: '3',DMMC: '驳回'}],
      BSJFOptions:[{DMXX: '1',DMMC: '已缴费'},{DMXX: '0',DMMC: '未缴费'}],


      dialogBmdwVisible: false
    })

    const getFormData = () => {
      let params={
        GGID: state.GGID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/hhqkqr/selectHhqkById', params).then((res) => {
        state.formData=res.data
        state.formData.QRHHList.forEach(item=>{
          let BDCYQK={}
          if(item.SFCY==='1'){
            item.ZBHHList.forEach(ii=>{
              BDCYQK[ii.FABDID]=true
            })
          }
          item.BDCYQK=BDCYQK
        })
        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        GGID: state.GGID,
      }
      params.QRHHList.forEach(item=>{
        if(item.LY==='SDTJ'){
          let ZBHH=[]
          state.formData.XMXX.BDList.forEach(ii=>{
            if(state.formData.XMXX.SFFBD==='0' || item.BDCYQK[ii.FABDID]){
              ZBHH.push({
                ...item,
                HHID: comFun.newId(),
                FABDID: ii.FABDID,
              })
            }
          })
          item.ZBHHList=ZBHH
        }

        item.ZBHHList.forEach(ii=>{
          ii.SHZT=item.SHZT
          ii.SFGMBS=item.SFGMBS
        })
      })
      if(type==='submit'){
        params.SHZT='2'
      }else {
        params.SHZT='0'
      }
      console.log(params)
      state.loading=true
      axiosUtil.post('/backend/xsgl/hhqkqr/saveHhqrForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            if(state.formData.XMXX.SFFBD==='1'){
              let noCheck=state.formData.QRHHList.find(item=>{
                if(item.LY==='SDTJ'){
                  return !Object.keys(item.BDCYQK).find(ii => ii);
                }else {
                  return false
                }
              })

              if(noCheck){
                ElMessage.error(`请选择单位${noCheck.DWMC}参与的标段`)
                resolve(false)
                return
              }


            }
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getCountRow = () => {
      let res={
        type: 'count',
        CBSDWQC: '参与单位数量合计',
        SFCY: state.formData.QRHHList.filter(item=>item.SFCY==='1').length,
        BDCYQK: {}
      }
      state.formData.XMXX.BDList.forEach(item=>{
        res.BDCYQK[item.FABDID]=state.formData.QRHHList.filter(ii=>ii.BDCYQK && ii.BDCYQK[item.FABDID]===true).length
      })
      return res
    }

    const arraySpanMethod = ({rowIndex, columnIndex}) => {
      if(rowIndex===state.formData.QRHHList.length){
        if([0,2].includes(columnIndex)){
          return [0,0]
        }else if(1===columnIndex){
          return [1,3]
        }
      }

    }



    const addCjdw = () => {
      state.dialogBmdwVisible=true
    }

    const getBmdwRes = (value) => {
      value.forEach(item=>{
        if(!state.formData.QRHHList.find(ii=>ii.DWWYBS===item.DWWYBS)){
          state.formData.QRHHList.push({
            ...item,
            SFCY: '1',
            LY: 'SDTJ',
            QRHHID: comFun.newId(),
            GGID: state.GGID,
            BMSJ: comFun.getNowTime(),
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
            YWZT: '1',
            BDCYQK: {}
          })
        }
      })
      state.dialogBmdwVisible=false
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      getCountRow,
      arraySpanMethod,
      addCjdw,
      getBmdwRes
    }
  }

})
</script>

<style scoped>

</style>
