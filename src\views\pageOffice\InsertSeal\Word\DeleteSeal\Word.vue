<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Save() {
  pageofficectrl.SaveFilePage = "/InsertSeal/Word/AddSeal/save?savePath=/InsertSeal/Word/DeleteSeal/";
  pageofficectrl.WebSave();
}

function OnPageOfficeCtrlInit() {
  pageofficectrl.AddCustomToolButton("保存", "Save", 1);
  pageofficectrl.AddCustomToolButton("加盖测试公章", "InsertSeal()", 2);
  pageofficectrl.AddCustomToolButton("加盖李志签名", "InsertSign()", 3);
  pageofficectrl.AddCustomToolButton("删除李志签名", "DeleteSign()", 21);
  pageofficectrl.AddCustomToolButton("删除所有李志的印章", "DeleteBySigner()", 21);
}


function InsertSeal() {
  try {
    //第一个参数，必填项，标识印章名称（当存在重名的印章时，默认选取第一个印章），且该印章签章人的签章类型方式必须为用户名+密码；
    //第二个参数，可选项，标识是否保护文档，true:保护文档，false:不保护文档；
    //第三个参数，可选项，标识盖章指定位置名称，须为英文或数字，不区分大小写
    pageofficectrl.zoomseal.AddSealByName("测试公章", true);
  } catch (e) {
  }
}

function InsertSign() {
  try {
    //第一个参数，必填项，标识印章名称（当存在重名的印章时，默认选取第一个印章），且该印章签章人的签章类型方式必须为用户名+密码；
    //第二个参数，可选项，标识是否保护文档，true:保护文档，false:不保护文档；
    //第三个参数，可选项，标识盖章指定位置名称，须为英文或数字，不区分大小写
    pageofficectrl.zoomseal.AddSealByName("李志签名", true);
  } catch (e) {
  }
}

//根据印章名称删除印章（李志签名）
function DeleteSign() {
  let sealsJson = pageofficectrl.zoomseal.SealsAsJson;
  let sealObj = JSON.parse(sealsJson);
  for (var key in sealObj) {
    let sealName = sealObj[key].name;
    if ("李志签名" == sealName) {
      pageofficectrl.zoomseal.DeleteByName(sealName);
    }
  }
}
//根据签章人删除印章（李志）
function DeleteBySigner() {
  let sealsJson = pageofficectrl.zoomseal.SealsAsJson;
  let sealObj = JSON.parse(sealsJson);
  for (var key in sealObj) {
    let sealSigner = sealObj[key].signer;
    if ("李志" == sealSigner) {
      pageofficectrl.zoomseal.DeleteBySigner("李志");
    }
  }
}

function openFile() {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/InsertSeal/Word/DeleteSeal/Word',
    method: 'get',
  });
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit, Save, InsertSeal, InsertSign, DeleteSign, DeleteBySigner };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <div class="Word">
    <span style="color: red">操作说明：</span>点“加盖印章”按钮即可，插入印章时的用户名为：李志，密码默认为：111111。
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
  </div>
</template>
