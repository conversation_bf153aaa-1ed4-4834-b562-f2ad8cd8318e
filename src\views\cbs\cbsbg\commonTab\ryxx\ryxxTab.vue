<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        :model="tableData"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        class="lui-page"
        >
<!--            <div style="color: red">-->
<!--              {{ BGXX }}-->
<!--            </div>-->
      <el-table ref="dataTable" :data="[...tableData,...getDelRow()]" height="500px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column v-for="(item,index) in tableColumn" :key="index"
                         :prop="item.prop" :label="item.label" :align="item.align" :width="item.width"
                         :min-width="item.minWidth"
                         :show-overflow-tooltip="item.showOverflowTooltip">

          <template #default="{row,$index}" v-if="item.slot==='format'">
            {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='change'">
            <div :class="{dataChange : isChangeT(row.CYWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.CYWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.CYWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span>{{ row[item.prop] }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='formatChange'">
            <div :class="{dataChange : isChangeT(row.CYWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.CYWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.CYWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span> {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='DYXL'">
            <div>{{ryxl.find((i) => i.DMXX == row.DYXL)?.DMMC}}</div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='DYXLBG'">
            <div :class="{dataChange : isChangeT(row.CYWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.CYWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.CYWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span>{{ryxl.find((i) => i.DMXX == row.DYXL)?.DMMC}}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='CZZK'">
            <el-button :disabled="false" class="lui-table-button" @click="showZs(row, $index)">查看</el-button>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='info'">
            <el-tooltip :content="row[item.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>
        </el-table-column>



        <el-table-column prop="CZ" label="操作" align="center" min-width="200" fixed="right" >
          <template #default="{row,$index}">
            <div v-if="tableRowClassName({row})!=='warning-row'">
              <el-button v-if="editable" class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
              <el-button :disabled="false" class="lui-table-button" @click="editRow(row, $index)">查看</el-button>
              <el-button v-if="editable" class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button v-if="editable" class="lui-table-button" @click="deleteRow(row, $index)">删除</el-button>
            </div>
            <div v-else>
              已删除
            </div>
          </template>
        </el-table-column>

      </el-table>

    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        title="人员信息查看"
        v-model="editVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        top="50px"
        width="1200px">
      <ryxxEdit
          :editable="false"
          :editData="editData"
          @close="editVisible = false"
      />
    </el-dialog>
    <el-dialog
        custom-class="lui-dialog"
        title="证书信息"
        v-model="zsDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        destroy-on-close
        top="100px"
        width="1200px"
    >
      <el-table
          highlight-current-row
          size="default"
          ref="table"
          fit
          class="lui-table"
          height="300px"
          :border="false"
          :data="currentRow.ZSXX"
      >
        <EleProTableColumn
            v-for="prop in fileColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #fileList="{ row, $index }">
            <vsfileupload
                :maxSize="10"
                class="fjsc"
                :index="$index"
                :busId="row.ZSYWID"
                :editable="false"
                :key="row.ZSYWID"
                ywlb="DWZTBGFJ"
                busType="dwxx"
                :limit="100"
            ></vsfileupload>
          </template>
          <template #ZSLX="{ row }">
            {{ zslx[row[prop.prop]] }}
          </template>
        </EleProTableColumn>
      </el-table>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        title="信息项选择"
        v-model="chooseVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        top="50px"
        width="1200px">
      <ryxxXz
          :key="editIndex"
          :currentRow="currentRow"
          @updateChooseData="updateChooseData"
          @updateEditData="updateEditData"
          :TYXYDM="TYXYDM"
          @close="chooseVisible = false"/>
    </el-dialog>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, ref, watch} from "vue";
import vsfileupload from "@views/components/vsfileupload";
import {InfoFilled} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import {getCommonSelectDMB} from "@src/api/cbsxx";
import ryxxEdit from "@views/cbs/templateManagement/DataTemplateManagement/ryxx/ryxxEdit.vue";
import ryxxXz from "@views/cbs/templateManagement/DataTemplateManagement/ryxx/ryxx_xz.vue";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import comFun from "@lib/comFun";



export default defineComponent({
  name: '',
  components: {vsfileupload, InfoFilled,ryxxEdit,ryxxXz,EleProTableColumn},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    TYXYDM: {
      type: String,
      default: ''
    }

  },
  setup(props, {emit}) {
    const state = reactive({
      chooseVisible:false,
      tableData: [],
      tableColumn: [
        {
          label: "专业名称",
          prop: "ZYMC",
          align: "center",
          showOverflowTooltip: true,
          width: 100,
          // slot: "select",
        },

        {
          label: "业务要求",
          prop: "YWYQ",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "录入资料说明",
          prop: "LRZLSM",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "信息项名称",
          prop: "XXXMC",
          align: "center",
          showOverflowTooltip: true,
          width: 200,
          // slot: "select",
        },
        {
          label: "姓名",
          prop: "RYXM",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "岗位",
          prop: "GWJZW",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "学历",
          prop: "DYXL",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'DYXLBG' : 'DYXL',
        },
        {
          label: "工作年限",
          prop: "BGGZSC",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "持证信息",
          prop: "CZZK",
          align: "center",
          width: 150,
          slot: "CZZK",
        },
      ],
      fileColumn: [
        {
          label: "序号",
          type: "index",
          align: "center",
        },

        {
          label: "证书名称",
          prop: "ZSMC",
          align: "center",
          showOverflowTooltip: true,
        },
        {
          label: "证书编号",
          prop: "ZSBH",
          align: "center",
          showOverflowTooltip: true,
        },
        {
          label: "有效开始日期",
          prop: "YXQKS",
          align: "center",
          showOverflowTooltip: true,
        },
        {
          label: "有效结束日期",
          prop: "YXQJS",
          align: "center",
          showOverflowTooltip: true,
        },
        {
          label: "附件",
          prop: "fileList",
          align: "center",
          showOverflowTooltip: true,
          slot: "fileList",
        },
      ],
    })
    const updateChooseData = (val) => {
      changeData(currentRow.value,val,editIndex.value,false)
    };

    const changeData = (oldRow,newRow,index,visible) => {
      let params={
        CYZSJID: newRow.CYZSJID,
      }

      axiosUtil.get('/backend/sccbsgl/cbsyj/ytxxx/copyRyzsxx', params).then((res) => {
        let resData=res.data || []
        let ZSXX=[]

        resData.forEach(item=>{
          ZSXX.push({
            ...item,
            ZSWYBS: item.ZSYWID,
            DWYWID: oldRow.DWYWID,
            MBMXID: oldRow.MBMXID,
            ZSCYZBS: oldRow.DWCYID,
            ZSDLDM: "RYZS",
            ZSCYZLXDM: "DWCY",
          })
        })

        oldRow.ZSXX=ZSXX
        oldRow.EXTENSION=newRow.EXTENSION
        oldRow.RYLX=newRow.RYLX
        oldRow.RYXM=newRow.RYXM
        oldRow.XBDM=newRow.XBDM
        oldRow.SFZH=newRow.SFZH
        oldRow.CSNY=newRow.CSNY
        oldRow.GWJZW=newRow.GWJZW
        oldRow.BGGZSC=newRow.BGGZSC
        oldRow.BYYX=newRow.BYYX
        oldRow.SXZY=newRow.SXZY
        oldRow.DYXL=newRow.DYXL
        oldRow.BYSJ=newRow.BYSJ
        oldRow.ZC=newRow.ZC
        oldRow.CYZSJID=newRow.CYZSJID
        oldRow.RYXLPXH=newRow.XLPXH;

        state.chooseVisible = visible;
      })
    }

    watch(() => state.tableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])
    const initBgInfo = () => {
      const getObjValue = (obj,prop) => {
        let propNameList= prop.split('.')
        let value=obj;
        propNameList.forEach(name=>{
          value=value ? value[name] || '' : ''
        })
        return value
      }

      if (state.tableData && props.resultTableData) {
        let res = []
        let BGQBS = props.resultTableData.map(i => i.CYWYBS)
        let BGHBS = state.tableData.map(i => i.CYWYBS)
        let allBS = new Set([...BGQBS, ...BGHBS])
        allBS.forEach(item => {
          if (BGQBS.includes(item) && BGHBS.includes(item)) {
            let BGQXX = props.resultTableData.find(i => i.CYWYBS === item)
            let BGHXX = state.tableData.find(i => i.CYWYBS === item)

            let isBg = false
            let dbsj = []
            let checkProp = ['RYLX', 'RYXM', 'XBDM', 'SFZH', 'CSNY', 'GWJZW', 'BGGZSC', 'BYYX', 'SXZY', 'DYXL',
              'BYSJ', 'ZC','EXTENSION.CJGZSJ','EXTENSION.SBJNRQ','EXTENSION.BGWGZRQ','EXTENSION.XZZW',
              'EXTENSION.WYSP','EXTENSION.ZCZY','EXTENSION.ZCDJ','EXTENSION.CJGZSJ']
            checkProp.forEach(ii => {
              if (getObjValue(BGQXX,ii) !== getObjValue(BGHXX,ii)) {
                dbsj.push({
                  BGQ: getObjValue(BGQXX,ii),
                  BGH: getObjValue(BGHXX,ii),
                  ZDMC: ii
                })
                isBg = true
              }
            })
            if (isBg) {
              res.push({
                YWLX: 'RYXX',
                BGZT: 'BG',
                WYBS: item,
                BGXQ: dbsj
              })
            }

          } else if (BGQBS.includes(item) && !BGHBS.includes(item)) {
            res.push({
              YWLX: 'RYXX',
              BGZT: 'SC',
              WYBS: item,
            })
          } else if (!BGQBS.includes(item) && BGHBS.includes(item)) {
            res.push({
              YWLX: 'RYXX',
              BGZT: 'XZ',
              WYBS: item,
            })
          }
        })
        BGXX.value = res
      }

    }

    const getDelRow = () => {
      if(props.resultTableData && state.tableData){
        return props.resultTableData.filter(item => !state.tableData.find(ii => ii.CYWYBS === item.CYWYBS))
      }else {
        return []
      }
    }

    const tableRowClassName = ({row,index}) => {
      let info=BGXX.value.find(ii=>ii.WYBS===row.CYWYBS) || {}
      if (info.BGZT==='XZ'){
        return "success-row"
      }else if(info.BGZT==='SC'){
        return "warning-row"
      }

    }

    const isChangeT = (CYWYBS, prop) => {
      let info = BGXX.value.find(item => item.WYBS === CYWYBS && item.BGZT === 'BG')
      if (info) {
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if(res){
          if(prop==='DYXL'){
            res.BGMS = `${ryxl.value.find((i) => i.DMXX === res.BGQ)?.DMMC} 变更为 ${ryxl.value.find((i) => i.DMXX === res.BGH)?.DMMC}`
          }else {
            res.BGMS = `${res.BGQ} 变更为 ${res.BGH}`
          }
          return res
        }
      }
      return null
    }

    const updateEditData = (row) => {
      state.tableData.forEach((item,index)=>{
        if(item.CYZSJID===row.CYZSJID){
          changeData(item,row,index,true)
        }
      })
    }

    const chooseRow = (row, index) => {
      currentRow.value=row;
      editIndex.value = index;
      state.chooseVisible = true;
    };


    watch(
        () => props.defaultData,
        (val) => {
          console.log("val", val);
          if (!val) return;
          if (val) {
            val.forEach((x) => {
              const UUID = comFun.newId();
              x.DWCYID = x.DWCYID || UUID;
              x.CYWYBS = x.CYWYBS || UUID;
              if (x.ZSXX) {
                x.CZZK = x.ZSXX.map((x) => x.ZSMC).toString();
              }
            });
          }
          state.tableData = val;
        },
        {
          immediate: true,
        }
    );

    const insertRow = (row, index) => {
      const UUID = comFun.newId();
      state.tableData.splice(index + 1, 0, {
        DWCYID: UUID,
        CYWYBS: UUID,
        ZYMC: row.ZYMC,
        ZYBM: row.ZYBM,
        XXXMC: row.XXXMC,
        MBMXID: row.MBMXID,
        YWYQ: row.YWYQ,
        LRZLSM: row.LRZLSM,
        XXX: null,
        ZZXXMC: null,
        ZZDJ: null,
        YXKSRQ: null,
        YXJSRQ: null,
        FZBM: null,
        FJ: null,
        RYLX: null, //人员类型
        RYXM: null, //人员姓名
        XBDM: null, //性别代码
        SFZH: null, //身份证号
        CSNY: null, //出生年月
        CJSJ: null, //参加工作时间
        SBJNRQ: null,
        GWMC: null,
        BGWGZRQ: null,
        XZZW: null,
        DYXL: null, //文化程度(学历)
        BYYX: null, //毕业院校
        SXZY: null, //所学专业
        BYSJ: null, //毕业时间
        WYSP: null, //
        ZC: null,
        ZCZY: null,
        ZCDJ: null,
        GWJZW: null,
        BGGZSC: null,
      });
    };

    const zsDialog = ref(false);
    const showZs = (row) => {
      currentRow.value = row;
      zsDialog.value = true;
    };
    const zslx = ref({});
    const rylx = ref({});
    const currentRow = ref({});
    const getRylx = () => {
      getCommonSelectDMB({ DMLBID: "RYLX" }).then((res) => {
        res.data.forEach((x) => (rylx.value[x.DMXX] = x.DMMC));
      });
    };

    const ryxl = ref([]);
    const getRyxl = () => {
      getCommonSelectDMB({ DMLBID: "XL" }).then((res) => {
        ryxl.value = res.data;
      });
    };

    // 查询证书类型
    const getZslx = () => {
      getCommonSelectDMB({ DMLBID: "RYZSLX" }).then((res) => {
        zslx.value = {};
        for(let i=0; i<res.data.length; i++){
          zslx.value[res.data[i].DMXX] = res.data[i].DMMC;
        }
      });
    };




    const editVisible = ref(false);
    const editIndex = ref(0);
    const editData = ref({});
    const editRow = (row, index) => {
      row.ZSXX = row.ZSXX || [];
      editIndex.value = index;
      editData.value = row;
      editVisible.value = true;
    };




    const deleteRow = (row, index) => {
      ElMessageBox.confirm("是否删除此条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
            state.tableData.splice(index, 1);
            ElMessage({
              message: "删除成功!",
              type: "success",
            });
          }).catch(() => {});
    };

    const validateForm = () => {
      return new Promise((resolve, reject) => {
        if(state.tableData.find(item=>(item.SFBT=='1' && !item.CYZSJID))){
          reject({mgs:[{message:'请完善人员信息！'}]})
        }else {
          for(let i=0;i<state.tableData.length;i++){
            let xlpxh = state.tableData[i].XLPXH;
            let gznx = state.tableData[i].GZNX;
            let realgznx = state.tableData[i].BGGZSC;
            let realxlpxh = state.tableData[i].RYXLPXH;
            if(gznx){
              if(!realgznx || realgznx < gznx){
                reject({mgs:[{message:'第'+ (i+1) + '行人员工作年限不符合'+ gznx +'年要求'}]});
                return;
              }
            }
            if(xlpxh){
              if(!realxlpxh || realxlpxh > xlpxh){
                reject({mgs:[{message:'第'+ (i+1) + '行人员学历不符合' + state.tableData[i].XLMC + '的要求'}]});
                return;
              }
            }
            // 要求最低人数
            let zdrs = state.tableData[i].ZDRS;
            let xxxmc = state.tableData[i].XXXMC;
            let realrs = 0;
            for(let k=0;k<state.tableData.length;k++){
              if(state.tableData[k].CYZSJID && state.tableData[k].MBMXID === state.tableData[i].MBMXID){
                realrs++;
              }
            }
            if(realrs < zdrs){
              reject({mgs:[{message:xxxmc + '人员数量不满足' + zdrs + '人要求'}]});
              return;
            }
            // 检验证书情况
            let zsStr = state.tableData[i].CZXX ? state.tableData[i].CZXX.split(",") : [];
            let realZsArr = state.tableData[i].ZSXX;
            // 缺少的证书
            let nohaStr = "";
            for(let m = 0;m < zsStr.length;m++){
              let has = false;
              for(let n = 0;n < realZsArr.length;n++){
                if(zsStr[m] === realZsArr[n].ZSLBDM){
                  has = true;
                }
              }
              if(!has){
                nohaStr += zslx.value[zsStr[m]] + "、";
              }
            }
            if(nohaStr){
              reject({mgs:[{message:'第'+ (i+1) + '行人员缺少' + nohaStr.slice(0, -1) + '证书'}]});
              return;
            }
          }
          resolve(true)
        }
      })
    };
    onMounted(() => {
      getRylx();
      getRyxl();
      getZslx();
    })

    return {
      ...toRefs(state),
      getDelRow,
      tableRowClassName,
      isChangeT,
      chooseRow,
      editRow,
      insertRow,
      deleteRow,
      editVisible,
      editData,
      zsDialog,
      currentRow,
      zslx,
      editIndex,
      updateChooseData,
      updateEditData,
      ryxl,
      BGXX,
      showZs,
      // validateForm

    }
  }

})
</script>

<style scoped>

</style>
