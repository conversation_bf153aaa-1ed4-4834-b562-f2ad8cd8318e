<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="180px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{formData.XMMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{formData.XMBH}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标单位：" prop="SSDWMC">
            <div style="margin-left: 10px">{{formData.SSDWMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标文件获取截止时间：" prop="ZBWJHQSJJS">
            <div style="margin-left: 10px">{{formData.ZBWJHQSJJS}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="招标文件领取情况：" prop="ZBWJLQQK">
            <el-table ref="datatable91634" :data="formData.LQQKList" height="400px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
<!--              <el-table-column prop="DWMC" label="领取队伍名称" align="center"-->
<!--                               :show-overflow-tooltip="true" min-width="160"></el-table-column>-->
              <el-table-column prop="SFCY" label="是否参与" align="center"
                               :show-overflow-tooltip="true" width="80">
                <template #default="{row}">
                  <div v-if="row.SFCY==='1'">参与</div>
                  <div v-if="row.SFCY==='0'">不参与</div>
                </template>
              </el-table-column>
              <el-table-column prop="LXR" label="联系人" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="LXDH" label="联系电话" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="LXYX" label="联系邮箱" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="LQZT" label="领取状态" align="center"
                               :show-overflow-tooltip="true" width="100">
                <template #default="{row}">
                  <div v-if="row.LQZT==='1'">已领取</div>
                  <div v-else>未领取</div>
                </template>
              </el-table-column>
              <el-table-column prop="LQRXM" label="领取人" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="LQSJ" label="领取时间" align="center"
                               :show-overflow-tooltip="true" width="170"></el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GGID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',

        LQQKList: []
      },
      rules: {
      }
    })

    const getFormData = () => {
      let params={
        GGID: state.GGID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/zbwjlq/selectZbwjlqqkById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }




    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      closeForm,
    }
  }

})
</script>

<style scoped>

</style>
