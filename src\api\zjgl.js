import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"


// 专家管理-专家查询统计-专家基本信息明细查询
// @method getZjcxtjSelectZjjbxxMX
// @type get
// @return url
//getZjcxtjSelectZjjbxxMX: `/zjgl/zjcxtj/selectZjjbxxMX`,

// eslint-disable-next-line
export function getZjcxtjSelectZjjbxxMX(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjcxtj/selectZjjbxxMX`, params)
}
// 专家管理-专家查询统计-专家基本信息查询
// @method postZjcxtjSelectZjjbxx
// @type post
// @return url
//postZjcxtjSelectZjjbxx: `/zjgl/zjcxtj/selectZjjbxx`,

// eslint-disable-next-line
export function postZjcxtjSelectZjjbxx(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjcxtj/selectZjjbxx`, params)
}
// 专家管理-专家出库-专家出库-查询已入库专家
// @method getZjckglQueryYrkZjList
// @type get
// @return url
//getZjckglQueryYrkZjList: `/zjgl/zjckgl/queryYrkZjList`,

// eslint-disable-next-line
export function getZjckglQueryYrkZjList(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjckgl/queryYrkZjList`, params)
}
// 专家管理-专家出库-专家出库信息删除
// @method getZjckglDeleteZjckxxByGlzjid
// @type get
// @return url
//getZjckglDeleteZjckxxByGlzjid: `/zjgl/zjckgl/deleteZjckxxByGlzjid`,

// eslint-disable-next-line
export function getZjckglDeleteZjckxxByGlzjid(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjckgl/deleteZjckxxByGlzjid`, params)
}
// 专家管理-专家出库-专家出库明细查询
// @method getZjckglSelectCkmxByGlzjid
// @type get
// @return url
//getZjckglSelectCkmxByGlzjid: `/zjgl/zjckgl/selectCkmxByGlzjid`,

// eslint-disable-next-line
export function getZjckglSelectCkmxByGlzjid(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjckgl/selectCkmxByGlzjid`, params)
}
// 专家管理-专家出库-专家出库查询分页
// @method postZjckglQueryZjckList
// @type post
// @return url
//postZjckglQueryZjckList: `/zjgl/zjckgl/queryZjckList`,

// eslint-disable-next-line
export function postZjckglQueryZjckList(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjckgl/queryZjckList`, params)
}
// 专家管理-专家出库-专家出库信息查询
// @method getZjckglSelectCkxxByGlzjid
// @type get
// @return url
//getZjckglSelectCkxxByGlzjid: `/zjgl/zjckgl/selectCkxxByGlzjid`,

// eslint-disable-next-line
export function getZjckglSelectCkxxByGlzjid(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjckgl/selectCkxxByGlzjid`, params)
}
// 专家管理-专家出库-专家出库明细删除
// @method postZjckglDeleteZjmx
// @type post
// @return url
//postZjckglDeleteZjmx: `/zjgl/zjckgl/deleteZjmx`,

// eslint-disable-next-line
export function postZjckglDeleteZjmx(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjckgl/deleteZjmx`, params)
}
// 专家管理-专家出库-专家出库信息保存
// @method postZjckglSaveZjckData
// @type post
// @return url
//postZjckglSaveZjckData: `/zjgl/zjckgl/saveZjckData`,

// eslint-disable-next-line
export function postZjckglSaveZjckData(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjckgl/saveZjckData`, params)
}
// 专家管理-采购项目管理-采购项目分页查询
// @method getCgxmglQueryCgxmList
// @type get
// @return url
//getCgxmglQueryCgxmList: `/zjgl/cgxmgl/queryCgxmList`,

// eslint-disable-next-line
export function getCgxmglQueryCgxmList(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/cgxmgl/queryCgxmList`, params)
}
// 专家管理-采购项目管理-采购项目表单查询
// @method getCgxmglQueryCgxmForm
// @type get
// @return url
//getCgxmglQueryCgxmForm: `/zjgl/cgxmgl/queryCgxmForm`,

// eslint-disable-next-line
export function getCgxmglQueryCgxmForm(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/cgxmgl/queryCgxmForm`, params)
}
// 专家管理-采购项目管理-采购项目信息删除
// @method getCgxmglDeleteCgxmgl
// @type get
// @return url
//getCgxmglDeleteCgxmgl: `/zjgl/cgxmgl/deleteCgxmgl`,

// eslint-disable-next-line
export function getCgxmglDeleteCgxmgl(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/cgxmgl/deleteCgxmgl`, params)
}
// 专家管理-采购项目管理-采购项目信息保存
// @method postCgxmglSaveCgxmData
// @type post
// @return url
//postCgxmglSaveCgxmData: `/zjgl/cgxmgl/saveCgxmData`,

// eslint-disable-next-line
export function postCgxmglSaveCgxmData(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/cgxmgl/saveCgxmData`, params)
}
// 专家管理-评委抽取管理-人员查询
// @method getPwcqglQueryRy
// @type get
// @return url
//getPwcqglQueryRy: `/zjgl/pwcqgl/queryRy`,

// eslint-disable-next-line
export function getPwcqglQueryRy(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwcqgl/queryRy`, params)
}
// 专家管理-评委抽取管理-评委抽取表单查询
// @method getPwcqglQueryPwcqForm
// @type get
// @return url
//getPwcqglQueryPwcqForm: `/zjgl/pwcqgl/queryPwcqForm`,

// eslint-disable-next-line
export function getPwcqglQueryPwcqForm(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwcqgl/queryPwcqForm`, params)
}
// 专家管理-评委抽取管理-保存评委专家数据
// @method postPwcqglSavaPwzj
// @type post
// @return url
//postPwcqglSavaPwzj: `/zjgl/pwcqgl/savaPwzj`,

// eslint-disable-next-line
export function postPwcqglSavaPwzj(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/pwcqgl/savaPwzj`, params)
}
// 专家管理-评委抽取管理-保存评委抽取管理数据
// @method postPwcqglSavePwcqglData
// @type post
// @return url
//postPwcqglSavePwcqglData: `/zjgl/pwcqgl/savePwcqglData`,

// eslint-disable-next-line
export function postPwcqglSavePwcqglData(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/pwcqgl/savePwcqglData`, params)
}
// 专家管理-评委抽取管理-随机抽取专家
// @method postPwcqglSavaPwzjJS
// @type post
// @return url
//postPwcqglSavaPwzjJS: `/zjgl/pwcqgl/savaPwzjJS`,

// eslint-disable-next-line
export function postPwcqglSavaPwzjJS(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/pwcqgl/savaPwzjJS`, params)
}
// 专家管理-评委抽取管理-随机抽取条件查询
// @method getPwcqglQueryCqtjData
// @type get
// @return url
//getPwcqglQueryCqtjData: `/zjgl/pwcqgl/queryCqtjData`,

// eslint-disable-next-line
export function getPwcqglQueryCqtjData(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwcqgl/queryCqtjData`, params)
}
// 专家管理-评委抽取管理-待评审项目删除
// @method postPwcqglDeleteDpsxm
// @type post
// @return url
//postPwcqglDeleteDpsxm: `/zjgl/pwcqgl/deleteDpsxm`,

// eslint-disable-next-line
export function postPwcqglDeleteDpsxm(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/pwcqgl/deleteDpsxm`, params)
}
// 专家管理-评委抽取管理-评委抽取管理删除
// @method getPwcqglDeletePwcqgl
// @type get
// @return url
//getPwcqglDeletePwcqgl: `/zjgl/pwcqgl/deletePwcqgl`,

// eslint-disable-next-line
export function getPwcqglDeletePwcqgl(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwcqgl/deletePwcqgl`, params)
}
// 专家管理-评委抽取管理-抽取专家查询
// @method getPwcqglQueryZjcqData
// @type get
// @return url
//getPwcqglQueryZjcqData: `/zjgl/pwcqgl/queryZjcqData`,

// eslint-disable-next-line
export function getPwcqglQueryZjcqData(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwcqgl/queryZjcqData`, params)
}
// 专家管理-评委抽取管理-保存抽取规则与抽取记录
// @method postPwcqglSaveCqgzCqjlData
// @type post
// @return url
//postPwcqglSaveCqgzCqjlData: `/zjgl/pwcqgl/saveCqgzCqjlData`,

// eslint-disable-next-line
export function postPwcqglSaveCqgzCqjlData(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/pwcqgl/saveCqgzCqjlData`, params)
}
// 专家管理-评委抽取管理-评委抽取查询分页
// @method getPwcqglQueryPwcqList
// @type get
// @return url
//getPwcqglQueryPwcqList: `/zjgl/pwcqgl/queryPwcqList`,

// eslint-disable-next-line
export function getPwcqglQueryPwcqList(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwcqgl/queryPwcqList`, params)
}
// 专家管理-评委抽取管理-随机抽取条件明细删除
// @method postPwcqglDeleteCqtj
// @type post
// @return url
//postPwcqglDeleteCqtj: `/zjgl/pwcqgl/deleteCqtj`,

// eslint-disable-next-line
export function postPwcqglDeleteCqtj(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/pwcqgl/deleteCqtj`, params)
}
// 专家管理-评委抽取管理-专家查询
// @method getPwcqglQueryZj
// @type get
// @return url
//getPwcqglQueryZj: `/zjgl/pwcqgl/queryZj`,

// eslint-disable-next-line
export function getPwcqglQueryZj(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwcqgl/queryZj`, params)
}
// 专家管理-评委抽取管理-待评审项目查询
// @method getPwcqglQueryDpsxmData
// @type get
// @return url
//getPwcqglQueryDpsxmData: `/zjgl/pwcqgl/queryDpsxmData`,

// eslint-disable-next-line
export function getPwcqglQueryDpsxmData(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwcqgl/queryDpsxmData`, params)
}
// 专家管理-评委评标-保存评委评价
// @method postPwpbSavePwpj
// @type post
// @return url
//postPwpbSavePwpj: `/zjgl/pwpb/savePwpj`,

// eslint-disable-next-line
export function postPwpbSavePwpj(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/pwpb/savePwpj`, params)
}
// 专家管理-评委评标-评委评标列表
// @method getPwpbPwpjMxList
// @type get
// @return url
//getPwpbPwpjMxList: `/zjgl/pwpb/pwpjMxList`,

// eslint-disable-next-line
export function getPwpbPwpjMxList(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwpb/pwpjMxList`, params)
}
// 专家管理-评委评标-评委评价列表
// @method getPwpbPwpjList
// @type get
// @return url
//getPwpbPwpjList: `/zjgl/pwpb/pwpjList`,

// eslint-disable-next-line
export function getPwpbPwpjList(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/pwpb/pwpjList`, params)
}
// 专家管理-专家变更-专家变更分页查询
// @method postZjbgglSelectZjbgPage
// @type post
// @return url
//postZjbgglSelectZjbgPage: `/zjgl/zjbggl/selectZjbgPage`,

// eslint-disable-next-line
export function postZjbgglSelectZjbgPage(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjbggl/selectZjbgPage`, params)
}
// 专家管理-专家变更-专家变更保存
// @method postZjbgglSaveBgzj
// @type post
// @return url
//postZjbgglSaveBgzj: `/zjgl/zjbggl/saveBgzj`,

// eslint-disable-next-line
export function postZjbgglSaveBgzj(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjbggl/saveBgzj`, params)
}
// 专家管理-专家变更-专家基本信息明细查询
// @method getZjbgglSelectZjMxByZjbs
// @type get
// @return url
//getZjbgglSelectZjMxByZjbs: `/zjgl/zjbggl/selectZjMxByZjbs`,

// eslint-disable-next-line
export function getZjbgglSelectZjMxByZjbs(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjbggl/selectZjMxByZjbs`, params)
}
// 专家管理-专家入库-专家入库分页查询
// @method postZjrkglSelectRkzjPage
// @type post
// @return url
//postZjrkglSelectRkzjPage: `/zjgl/zjrkgl/selectRkzjPage`,

// eslint-disable-next-line
export function postZjrkglSelectRkzjPage(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjrkgl/selectRkzjPage`, params)
}
// 专家管理-专家入库-专家入库删除
// @method getZjrkglDelRkzj
// @type get
// @return url
//getZjrkglDelRkzj: `/zjgl/zjrkgl/delRkzj`,

// eslint-disable-next-line
export function getZjrkglDelRkzj(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjrkgl/delRkzj`, params)
}
// 专家管理-专家入库-专家入库保存
// @method postZjrkglSaveRkzj
// @type post
// @return url
//postZjrkglSaveRkzj: `/zjgl/zjrkgl/saveRkzj`,

// eslint-disable-next-line
export function postZjrkglSaveRkzj(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjrkgl/saveRkzj`, params)
}
// 专家管理-专家入库-工作单位信息
// @method getZjrkglSelectGzdw
// @type get
// @return url
//getZjrkglSelectGzdw: `/zjgl/zjrkgl/selectGzdw`,

// eslint-disable-next-line
export function getZjrkglSelectGzdw(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjrkgl/selectGzdw`, params)
}
// 专家管理-专家入库-专家入库明细查询
// @method getZjrkglSelectRkzj
// @type get
// @return url
//getZjrkglSelectRkzj: `/zjgl/zjrkgl/selectRkzj`,

// eslint-disable-next-line
export function getZjrkglSelectRkzj(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjrkgl/selectRkzj`, params)
}
// 专家管理-专家入库-专家专业信息
// @method getZjrkglSelectZjzy
// @type get
// @return url
//getZjrkglSelectZjzy: `/zjgl/zjrkgl/selectZjzy`,

// eslint-disable-next-line
export function getZjrkglSelectZjzy(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjrkgl/selectZjzy`, params)
}
// 专家管理-专家入库-生成专家编号
// @method getZjrkglGenerateZJBH
// @type get
// @return url
//getZjrkglGenerateZJBH: `/zjgl/zjrkgl/generateZJBH`,

// eslint-disable-next-line
export function getZjrkglGenerateZJBH(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjrkgl/generateZJBH`, params)
}
// 专家管理-专家评标统计-专家评标信息查询
// @method postZjpbtjSelectZjpbxx
// @type post
// @return url
//postZjpbtjSelectZjpbxx: `/zjgl/zjpbtj/selectZjpbxx`,

// eslint-disable-next-line
export function postZjpbtjSelectZjpbxx(params) {
    return axiosUtil.post(`${baseUrl}/zjgl/zjpbtj/selectZjpbxx`, params)
}
// 专家管理-专家评标统计-专家评标信息明细查询
// @method getZjpbtjSelectZjpbxxMx
// @type get
// @return url
//getZjpbtjSelectZjpbxxMx: `/zjgl/zjpbtj/selectZjpbxxMx`,

// eslint-disable-next-line
export function getZjpbtjSelectZjpbxxMx(params) {
    return axiosUtil.get(`${baseUrl}/zjgl/zjpbtj/selectZjpbxxMx`, params)
}




