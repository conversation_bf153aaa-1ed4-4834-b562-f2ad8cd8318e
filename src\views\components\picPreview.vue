<template>
  <div v-if="modelValue" @touchmove.prevent>
    <teleport to="body">
      <div class="back-cover">
        <div class="image-show" @touchstart="onTouchstart" @touchmove.prevent="onTouchmove"
             @mousedown="onMousedown"
             :style="`--rotate-num: ${rotateNum}deg;--scale-num: ${scaleNum};--translate-x: ${rect.x}px;--translate-y: ${rect.y}px`">
          <img @mousedown.prevent class="image-pic" :src="imageList[showIndex]">
          <div class="image-cover">
            <slot :pIndex="showIndex"></slot>
          </div>
        </div>
        <div class="control-bar">
          <el-icon><Back @click="toNext('L')"/></el-icon>
          <el-icon><ZoomIn @click="scaleNum<3? scaleNum+=0.1 : null"/></el-icon>
          <el-icon><ZoomOut @click="scaleNum>0.2? scaleNum-=0.1 : null"/></el-icon>
          <el-icon><Download @click="download"/></el-icon>
          <el-icon><RefreshLeft @click="rotateNum-=90"/></el-icon>
          <el-icon><RefreshRight @click="rotateNum+=90"/></el-icon>
          <el-icon><Right @click="toNext('R')"/></el-icon>
        </div>

        <div class="control-close">
          <el-icon><Close @click="closePreview"/></el-icon>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import { Back, ZoomIn, ZoomOut,Download,RefreshLeft,RefreshRight,Right,Close} from '@element-plus/icons-vue'

export default defineComponent({
  name: '',
  components: {Back, ZoomIn, ZoomOut,Download,RefreshLeft,RefreshRight,Right,Close},
  props: {
    imageList: {
      type: Array,
      required: true,
      default: []
    },
    modelValue:{
      type: Boolean,
      required: true,
      default: false
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      showIndex: 0,
      rotateNum: 0,
      scaleNum: 1,

      startPoint:{},
      startRect:{},
      rect:{
        x: 0,
        y: 0,
      },
      PCMove: false
    })

    const setIndex = (val) => {
      state.showIndex=val
    }

    const toNext = (val) => {
      if(val==='L'){
        state.showIndex>0 ? state.showIndex-=1 : null
      }else if(val==='R'){
        state.showIndex<props.imageList.length-1 ? state.showIndex+=1 : null
      }
    }
    const download = () => {
      window.open(
          props.imageList[state.showIndex]
      );
    }

    const onTouchstart = (e) => {
      const { clientX, clientY } = e.changedTouches[0]
      state.startPoint = { x: clientX, y: clientY }
      state.startRect = { ...state.rect }
    }

    const onTouchmove = (e) => {
      const { clientX, clientY } = e.changedTouches[0]
      const diffX = clientX - state.startPoint.x
      const diffY = clientY - state.startPoint.y
      const distX = state.startRect.x + diffX
      const distY = state.startRect.y + diffY
      state.rect.x = distX
      state.rect.y = distY
    }

    const onMousedown = (e) => {
      state.PCMove=true
      state.startPoint = { x: e.clientX, y: e.clientY }
      state.startRect = { ...state.rect }

      const onMousemove = (e) => {
        e.preventDefault()
        if(state.PCMove){
          const { clientX, clientY } = e
          const diffX = clientX - state.startPoint.x
          const diffY = clientY - state.startPoint.y
          const distX = state.startRect.x + diffX
          const distY = state.startRect.y + diffY
          state.rect.x = distX
          state.rect.y = distY
        }
      }

      const onMouseup = (e) => {
        state.PCMove=false

        document.removeEventListener('mousemove', onMousemove);
        document.removeEventListener('mouseup', onMouseup);
      }


      document.addEventListener('mousemove', onMousemove);
      document.addEventListener('mouseup', onMouseup);

    }

    const closePreview = () => {
      emit('update:modelValue',false)
    }



    onMounted(() => {
    })

    return {
      ...toRefs(state),
      toNext,
      download,
      onTouchmove,
      onTouchstart,
      onMousedown,
      closePreview,
      setIndex

    }
  }

})
</script>

<style scoped>
.back-cover{
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(140, 147, 157, 0.41);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.control-bar{
  position: absolute;
  height: 50px;
  background-color: #b4bbc4;
  bottom: 10%;
  border-radius: 50px;
  color: white;
  font-size: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding-left: 20px;
  padding-right: 20px;
  max-width: 500px;

}
.image-show{
  position: relative;
  transform: rotate(var(--rotate-num)) scale(var(--scale-num)) translate(var(--translate-x),var(--translate-y));
  transform-origin: calc(var(--translate-x) + 50%) calc(var(--translate-y) + 50%);
}
.image-cover{
  position: absolute;
  left: 0;
  top: 0;
}
.image-pic{
  max-width: 95vw;
  max-height: 95vh;
  user-select: none;
}
.control-close{
  position: absolute;
  right:10%;
  top: 10%;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 30px;
  background-color: #8c939d;
  font-size: 25px;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.control-bar>.el-icon){
  cursor: pointer;
}
</style>
