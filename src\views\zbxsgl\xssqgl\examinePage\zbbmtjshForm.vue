<template>
  <div v-loading="loading">
    <el-input style="margin-top: 20px" v-model="shyj" type="textarea" :rows="5" placeholder="请输入审核意见"></el-input>
    <div style="display: flex;align-items: center;margin-top: 10px" v-if="apprValue !== '0'">
      <div style="width: 200px;flex-shrink: 0;text-align: right">二级单位专业管理部门科室长：</div>
      <el-select
          v-model="formData.BLRList[0].VALUE"
          multiple
          collapse-tags
          collapse-tags-tooltip
          filterable
          placeholder="请选择"
          style="width: 100%">
        <el-option
            v-for="item in ZYGLBMKSZOptions"
            :key="item.USER_LOGINNAME"
            :label="`${item.USER_NAME}（${item.ORGNA_NAME}）`"
            :value="item.USER_LOGINNAME"/>
      </el-select>
    </div>

    <div style="display: flex;align-items: center;margin-top: 10px" v-if="apprValue !== '0'">
      <div style="width: 200px;flex-shrink: 0;text-align: right">二级单位业务分管领导：</div>
      <el-select
          v-model="formData.BLRList[1].VALUE"
          multiple
          collapse-tags
          collapse-tags-tooltip
          filterable
          placeholder="请选择"
          style="width: 100%">
        <el-option
            v-for="item in YWFGLDOptions"
            :key="item.USER_LOGINNAME"
            :label="`${item.USER_NAME}（${item.ORGNA_NAME}）`"
            :value="item.USER_LOGINNAME"/>
      </el-select>
    </div>

    <div style="display: flex;align-items: center;margin-top: 10px" v-if="apprValue !== '0'">
      <div style="width: 200px;flex-shrink: 0;text-align: right">油田专业部门部室：</div>
      <el-cascader v-model="formData.BLRList[2].VALUE" :options="orgTree" filterable
                   :props="{checkStrictly: true,label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false,multiple: true}"
                   clearable />
    </div>




    <div style="text-align: center; height: 40px; padding: 10px">
      <el-button type="primary" @click="onConfirm">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import {ElMessage} from "element-plus";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {},
  props: {
    apprValue: String,
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      shyj: "",
      sffsdx: '1',
      nextPerformer: [],
      fileList: [],
      loading: false,

      formData: {
        BLRList: [
          {VALUE: [], TYPE: 'GR', ACTIVITYID: '3', LABEL: '二级单位专业管理部门科室长'},
          {VALUE: [], TYPE: 'GR', ACTIVITYID: '5', LABEL: '二级单位业务分管领导'},
          {VALUE: [], TYPE: 'BM', ACTIVITYID: '6', LABEL: '油田专业部门部室'}
        ]
      },

      ZYGLBMKSZOptions: [],
      YWFGLDOptions: [],

      orgList: [],
      orgTree: []
    })


    const onConfirm = () => {
      let nullRow=state.formData.BLRList.find(item=>!item.VALUE || item.VALUE.length===0)
      if (nullRow) {
        ElMessage.warning('请选择'+nullRow.LABEL)
        return
      }
      saveBlrInfo().then(res=>{
        emit("confirm", state.shyj, state.sffsdx, state.nextPerformer)
      })



    }

    const saveBlrInfo = () => {
      state.loading=true
      let params={
        BLRList: state.formData.BLRList.map((item,index)=>{
          return{
            ...item,
            BUSINESSID: props.params.businessId,
            PROCESSID: props.params.processId,
            TASKID: props.params.taskId,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
            XGRZH: state.userInfo.userLoginName,
            XGSJ: comFun.getNowTime(),
            NAME: item.VALUE.map(ii=>{
              if(index===0){
                return state.ZYGLBMKSZOptions.find(iii=>iii.USER_LOGINNAME===ii)?.USER_NAME
              }else if(index===1){
                return state.YWFGLDOptions.find(iii=>iii.USER_LOGINNAME===ii)?.USER_NAME
              }else if(index===2){
                return state.orgList.find(iii=>iii.ORGNA_ID===ii)?.ORGNA_NAME
              }
            }).join(','),
            VALUE: item.VALUE.join(',')
          }
        })
      }
      return axiosUtil.post('/backend/common/saveLcblr',params)
    }

    const getBlrOptions = (optionName,ROLE) => {
      let params={
        ROLE: ROLE
      }
      axiosUtil.get('/backend/common/selectUserByRole',params).then(res=>{
        state[optionName]=res.data || []
      })
    }

    const getOrgList = () => {
      let params={
      }
      axiosUtil.get('/backend/common/selectTwoOrgList', params).then((res) => {
        state.orgList=res.data.orgList || []
        state.orgTree = comFun.treeData(state.orgList,'ORGNA_ID','PORGNA_ID','children',res.data.root)
      });
    }

    const onCancel = () => {
      emit("close")
    }

    onMounted(() => {
      getOrgList()
      getBlrOptions('ZYGLBMKSZOptions','SCGL_SCGLY')
      getBlrOptions('YWFGLDOptions','SCGL_SCGLY')

      if (props.apprValue !== '0') {
        state.shyj = '同意'
      }
      emit('changeTitle', '提交申请')
      emit('changeWidth',600)

    })

    return {
      ...toRefs(state),
      onCancel,
      onConfirm

    }
  }

})
</script>

<style scoped>

</style>
