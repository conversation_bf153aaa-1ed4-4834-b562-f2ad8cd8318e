<template>
  <div style="font-size: 14px;">
    <div style="margin-bottom: 10px;border: 1px solid #cbcbcb;padding: 5px"
         v-for="(item,index) in tableData" :key="index">
      <div style="font-weight: bold;color: #409EFF">{{item['ZYMC']}}</div>
      <div v-for="(iitem,iindex) in item.SJXXList" :key="iindex">
        <appRow label="信息项：" label-width="110px">{{iitem['XXXMC']}}</appRow>
        <appRow label="所在地：" label-width="110px">{{iitem['SZD']}}</appRow>
        <appRow label="产权所有人：" label-width="110px">{{iitem['CQSYR']}}</appRow>
        <appRow label="面积：" label-width="110px">{{iitem['MJ']}}</appRow>
        <appRow label="数量：" label-width="110px">{{iitem['SL']}}</appRow>

        <appRow label="相关附件：" label-width="110px">
          <vsfileupload
              :editable="false"
              :busId="iitem.TDID"
              :key="iitem.TDID"
              ywlb="TDFJ"
              busType="dwxx"
              :limit="100"
          ></vsfileupload>
        </appRow>
        <div style="width: 100%;border-bottom: 1px dashed #c4c4c4;margin-top: 10px;margin-bottom: 10px"
             v-if="iindex+1!==item.SJXXList.length"></div>
      </div>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import appRow from "@views/app/common/appRow";
import vsfileupload from "@views/components/vsfileupload";

export default defineComponent({
  name: '',
  components: {appRow,vsfileupload},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
  },



  setup(props, {emit}) {
    const state = reactive({
      tableData:[]
    })
    watch(()=>props.defaultData,()=>{
      if(props.defaultData){
        let res=[]
        props.defaultData.forEach(item=>{
          let ZYXX=res.find(ii=>item.ZYBM===ii.ZYBM)
          if(ZYXX){
            ZYXX.SJXXList.push(item)
          }else {
            res.push({
              ZYBM: item.ZYBM,
              ZYMC: item.ZYMC,
              SJXXList: [item]
            })
          }
        })
        console.log(res)
        state.tableData=res
      }
    },{immediate: true,deep: true})


    onMounted(() => {

    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>

</style>
