<template>
  <div style="display: flex;margin-bottom: 4px;">
    <div :style="`width: ${labelWidth};flex-shrink: 0;text-align: right;font-weight: bold`">{{label}}</div>
    <div>
      <slot></slot>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";


export default defineComponent({
  name: '',
  components: {},
  props: {
    label:{
      type: String,
      default: ''
    },
    labelWidth: {
      type: String,
      default: '100px'
    }
  },
  setup(props, {emit}) {
    const state = reactive({})
    onMounted(() => {

    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>

</style>
