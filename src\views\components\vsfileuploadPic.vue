<template>
  <div>
    <el-upload
        name="files"
        :class="{ 'none-up' : !editable||fileList.length!==0}"
        list-type="picture-card"
        :action="uploadUrl"
        :headers="headers"
        :multiple="multiple"
        :accept="accept"
        :with-credentials="true"
        :data="fileUploadData"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :on-success="onSuccess"
        :before-upload="beforeUpload"
        :limit="1"
        :disabled="!editable"
        :on-exceed="handleExceed"
        :show-file-list="!text"
        :file-list="fileList">
      <el-icon v-if="editable&&fileList.length<limit"><Plus /></el-icon>
      <template #file="{ file }">
        <div>
          <el-image v-if="file.mongoDBId" class="el-upload-list__item-thumbnail" :src="'/backend/minio/download?id=' + file.mongoDBId" alt="" >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <span class="el-upload-list__item-actions">
          <span
              class="el-upload-list__item-delete"
              @click="handlePreview(file)"
          >
            <el-icon><Download /></el-icon>
          </span>
          <span
              v-if="editable"
              class="el-upload-list__item-delete"
              @click="handleRemove(file)"
          >
            <el-icon><Delete /></el-icon>
          </span>
        </span>
        </div>
      </template>
    </el-upload>
  </div>
</template>
<script>


import {defineComponent, onMounted, reactive, toRefs} from "vue";
import axiosUtil from "../../lib/axiosUtil"
import axios from "axios";
import { Delete, Download, Plus, ZoomIn,Picture  } from '@element-plus/icons-vue'
export default defineComponent({
      components: {Delete, Download, Plus, ZoomIn,Picture },
      props: {
        text:{
          type: Boolean,
          default: false
        },
        callBackFun: {  //上传前的回调函数
          type: Function,
          default: null
        },
        callBackParam: {
          type: Object,
          default: null
        },
        busId: { //附件业务ID
          type: String,
          required: true,
        },
        busType: {  //后台配置文件中的业务类型
          type: String,
          default: 'zysc'
        },
        ywlb: {  //业务类别，用于标识用同一id有不同业务类型的附件
          type: String,
          default: 'default'
        },
        multiple: { //支持文件多选，默认支持
          type: Boolean,
          default: true,
        },
        accept: {
          type:String,
          default:'.png,.jpg'
        }, //允许上传文件类型
        limit: {       //允许上传的文件个数
          type: Number,
          default: 10
        },
        // maxSize:{      //允许上传的文件大小
        //   type:Number,
        //   default:100
        // },
        editable: { //是否可编辑，默认可编辑
          type: Boolean,
          default: true
        },
        showTip: { //是否显示提示文字
          type: Boolean,
          default: false
        },
        files: { //是否显示提示文字
          type: Array,
          default: []
        },
        listType:{
          type:String,
          default:'text'
        },
        prefix: {
          type: String,
          default: ''
        }
      },
      setup(props, {emit}) {
        const state = reactive({
          maxSize: 5000000,
          model: 'fileUpload',
          uploadUrl: '/backend/minio/upload',
          //附件列表
          fileList: [],
          headers: {Authorization: "Bearer " + 'getToken()'},
          beforeRemoveMessage: true,
          //busType 对应配置文件中的业务类型
          //busId 业务ID
          fileUploadData: {
            busType: 'dwxx',
            busId: props.busId,
            standbyField0: props.ywlb,
            prefix: props.prefix,
          },
          dialogVisible: false,
        })
        const handleRemove = (file) => {
          axios.post('/backend/minio/del', {id: file.id, delFlag: "1"}).then((res) => {
            loadFileList()
          })
        }
        const onSuccess = (res) => {
          loadFileList()
        }
        const loadFileList = () => {
          if (props.busId) {
            axios.post('/backend/minio/list', {busId: props.busId, standbyField0: props.ywlb}).then((res) => {
              let fileList = [];
              console.log(res)
              for (let i = 0; i < res.data.data.length; i++) {
                let file = res.data.data[i];
                fileList.push({
                  name: file.fileName,
                  id: file.id,
                  operationId: file.operationId,
                  mongoDBId: file.downloadId,
                  type: file.fileType,
                });
              }
              emit('update:files',fileList)
              state.fileList = fileList
            })
          }
        }
        const handlePreview = (file) => {
          window.open('/backend/minio/download' + "?id=" + file.id);
        }
        const beforeUpload = (file) => {
          state.fileUploadData.busId=props.busId
          return true
          const isLt2M = file.size / 1024 / 1024 < this.maxSize;
          // if (!isLt2M) {
          //   this.beforeRemoveMessage = false;
          //   this.$message({
          //     message: "上传文件大小不能超过" + this.maxSize + "MB!",
          //     type: "warning",
          //   });
          //   return false;
          // }
          // if (this.callBackFun) {
          //   this.callBackFun(this.callBackParam);
          // }
        }
        const handleExceed = (files, fileList) => {
          // this.$message.warning(
          //     `当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          //         files.length + fileList.length
          //     } 个文件`
          // );
        }
        //删除触发执行
        const beforeRemove = (file, fileList) => {
          // if (this.beforeRemoveMessage) {
          //   return this.$confirm("确定删除 " + file.name + "？");
          // } else {
          //   return true;
          // }
        }
        onMounted(()=>{
          loadFileList()
        })

        return {
          ...toRefs(state),
          handleRemove,
          onSuccess,
          loadFileList,
          beforeUpload,
          handleExceed,
          handlePreview,
          beforeRemove
        }
      },
    }
)
</script>

<style scoped>
* >>> .readonlyClass .el-upload {
  display: block !important;
}

* >>> .readonlyClass .el-upload-list__item:first-child {
  margin-top: 0px !important;
}
.none-up /deep/ .el-upload--picture-card {
  display: none;

}

 /deep/ .el-upload--picture-card {
  background-color: #eff0f1;

}
</style>
