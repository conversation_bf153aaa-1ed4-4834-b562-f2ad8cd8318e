<template>
    <div style="display: inline-block;margin: 0 12px;">
        <el-upload
            name="files"
            :action="props.url"
            :multiple="false"
            accept=".xls,.xlsx"
            :data="props.importData"
            :on-success="onSuccess"
            :show-file-list="false">
            <el-button type="primary"><el-icon><Upload/></el-icon>{{props.bntName}}</el-button>
        </el-upload>
    </div>
</template>
<script setup>
import {ref} from "vue";
import axios from "axios";
import { Upload } from '@element-plus/icons-vue'
import { ElMessage } from "element-plus";
const emits = defineEmits(["refresh"]);
const props = defineProps({
    url: {
        type: String,
        default: ''
    },
    bntName: {
        type: String,
        default: '选择文件'
    },
    importData: {
        type: Object,
        default: {}
    }
});

const importData = ref({

});

const onSuccess = (res, file, fileList) => {
    if(res.data && res.data.success){
        ElMessage.success("导入成功！");
        emits("refresh");
    }else{
        ElMessage.error(res.data.msg);
    }
}

</script>