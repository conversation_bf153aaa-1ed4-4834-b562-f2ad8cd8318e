<template>
  <div style="width: 100%" v-loading="loading">
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 160px;text-align: center;padding-bottom: 2px">
        选择评委主任
      </div>
      <el-row ref="grid71868" :gutter="12" style="width: 100%">
        <el-col :span="24">
          <el-table ref="datatable91634" :data="formData.PWQDList" height="300px"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="ZJLBMC" label="人员类型" align="center"
                             :show-overflow-tooltip="true" width="160">
              <template #default="{row}">
                <div v-if="row.SFZBRDB==='1'">
                  需求单位代表
                </div>
                <div v-else>
                  {{row.ZJLBMC}}专家
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="XM" label="姓名" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="PWJS" label="评委角色" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row}">
                评委
              </template>
            </el-table-column>

            <el-table-column prop="CZ" label="操作" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row}">
                <div style="color: #6ad297" v-if="formData.XDPWBS===row.PBPWBS">【已选择】</div>
                <div style="cursor: pointer" v-else @click="formData.XDPWBS=row.PBPWBS">【未选择】</div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;">
        <el-button size="default" type="primary" @click="saveData('submit')" v-if="formData.SHZT!=='1'">提交</el-button>
        <el-button size="default" type="primary"  v-else>已提交</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import vsAuth from "@lib/vsAuth";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      formData: {
        PWQDList: [],
        XDPWBS: ''
      },
      loading: false,
      rules: {}
    })

    const getFormData = () => {
      let params={
        PBHYBS: props.params.PBHYBS,
        PBPWBS: props.parentForm.RYXX.PBPWBS,
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/szpwzr/selectXzpwzrxx', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const saveData = () => {
      if(!state.formData.XDPWBS){
        ElMessage.warning('请选择评委')
        return
      }
      let params={
        ID: comFun.newId(),
        PBPWBS: props.parentForm.RYXX.PBPWBS,
        XDPWBS: state.formData.XDPWBS,
        PBHYBS: props.params.PBHYBS,
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '1',
        YWZT: '1',
      }
      axiosUtil.post('/backend/kpbyx/szpwzr/saveXzpwzrxx', params).then(res=>{
        ElMessage.success('提交成功')
        getFormData()
      })
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      saveData

    }
  }

})
</script>

<style scoped>

</style>
