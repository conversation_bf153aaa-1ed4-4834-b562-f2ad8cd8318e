<template>
  <div style="width: 100%">
    <div class="file-row" v-for="(item,index) in fileList" :key="index">
      <el-icon :color="`#c0c0c0`"><Document /></el-icon>
      <div @click="handlePreview(item)">{{ item.name }}</div>
      <div class="button-text" style="margin-left: auto" @click="openPageOffice(item)">编辑</div>
    </div>


    <el-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        z-index="2999"
        append-to-body
        top="1vh"
        :title="tilte"
        width="80%">
      <div>
        <div>
          <el-button type="success" class="btn-down" @click="download">下载</el-button>
          <pdfView :source="{url: filePath}"/>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axios from "axios";
import {Document} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";
import VueOfficePdf from '@vue-office/pdf'
import pdfView from 'vue-pdf-embed'
import { POBrowser } from "js-pageoffice";
export default defineComponent({
  name: '',
  components: {Document,VueOfficePdf, pdfView},
  props: {
    busId: { //附件业务ID
      type: String,
      required: true,
    },
    busType: {  //后台配置文件中的业务类型
      type: String,
      default: 'zysc'
    },
    ywlb: {  //业务类别，用于标识用同一id有不同业务类型的附件
      type: String,
      default: 'default'
    },
    editable: { //是否可编辑，默认可编辑
      type: Boolean,
      default: true
    },
    files: { //是否显示提示文字
      type: Array,
      default: []
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      fileList: [],
      dialogVisible: false,
      filePath: "",
      tilte: "",
    })

    const loadFileList = () => {
      if (props.busId) {
        axios.post('/backend/minio/list', {
          busId: props.busId,
          standbyField0: props.ywlb
        }).then((res) => {

          let fileList = [];
          for (let i = 0; i < res.data.data.length; i++) {
            let file = res.data.data[i];
            fileList.push({
              name: file.fileName,
              id: file.id,
              operationId: file.operationId,
              mongoDBId: file.downloadId,
              type: file.fileType,
            });
          }
          emit('update:files', fileList)
          state.fileList = fileList
        })
      }
    }

    const handlePreview = (file) => {
      state.filePath = "/backend/minio/download?id=" + file.id;
      state.fileType = file.type;
      // state.dialogVisible = true;
      state.tilte = file.name + '预览';
      if (state.fileType == '.png' || state.fileType == '.jpg' || state.fileType == '.jpeg' || state.fileType == '.gif') {
        preview(state.filePath)
      } else if (state.fileType == '.pdf') {
        //window.open('/backend/minio/download' + "?id=" + file.id);
        state.dialogVisible = true;
      } else {
        window.open('/backend/minio/download' + "?id=" + file.id);
      }

    }

    const openPageOffice = (file) => {
      if(['.doc','.docx'].includes(file.type)){
        let paramJson={};
        //文件ID(可做入参)
        paramJson.file_id=file.id;
        //文件名称(可做入参)
        paramJson.file_name=file.name;
        //文件类型(可做入参)
        paramJson.file_type="word";
        let paramString=JSON.stringify(paramJson);

        //openWindow()第三个参数用来向弹出的PageOffice浏览器（POBrowser）窗口传递参数(参数长度不限)，支持json格式字符串。
        //此处为了方便演示，我们传递了file_id和file_name两个参数，具体以您实际开发为准。
        POBrowser.setProxyBaseAPI('/backend');

        //word编辑保存
        if("word" === paramJson.file_type){
          POBrowser.openWindow('/SimpleWord/Word', 'width=1200px;height=700px;',paramString);
        }else if("excel" === paramJson.file_type){

        }else if("ppt" === paramJson.file_type){

        }

      }else {
        ElMessage.warning('该文件格式不支持在线编辑')
      }
    }

    onMounted(() => {
      loadFileList()
    })

    return {
      ...toRefs(state),
      handlePreview,
      openPageOffice,
      loadFileList

    }
  }

})
</script>

<style scoped>
.file-row{
  display: flex;
  gap: 5px;
  align-items: center;
  cursor: pointer;
  padding-left: 10px;
  padding-right: 10px;
  font-size: 13px;
  border-radius: 4px;


}
.file-row:hover{
  background-color: #d0d4da;
}

.button-text{
  color: #2A96F9;
}
</style>
