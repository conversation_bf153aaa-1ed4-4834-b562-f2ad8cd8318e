<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function ShowFile1View() {
	pageofficectrl.word.ShowCompareView(1);
}

function ShowFile2View() {
	pageofficectrl.word.ShowCompareView(2);
}

function ShowCompareView() {
	pageofficectrl.word.ShowCompareView(0);
}

function SetFullScreen() {
	pageofficectrl.FullScreen = !pageofficectrl.FullScreen;
}

function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮    
	pageofficectrl.AddCustomToolButton("显示A文档", "ShowFile1View()", 0);
	pageofficectrl.AddCustomToolButton("显示B文档", "ShowFile2View()", 0);
	pageofficectrl.AddCustomToolButton("显示比较结果", "ShowCompareView()", 0);

}

function openFile() {
	// 发起GET请求到后端Controller的路由
	return request({
		url: '/WordCompare/Word',
		method: 'get',
	});
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit, ShowCompareView, ShowFile1View, ShowFile2View, SetFullScreen };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
	<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
</template>
