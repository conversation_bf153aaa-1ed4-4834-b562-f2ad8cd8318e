<template>
  <div class="container-wrapper">
    <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 280px)"
              :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
      <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
      <el-table-column prop="ORGNA_TWO_NAME" label="单位名称" align="center"
                       :show-overflow-tooltip="true" min-width="150"></el-table-column>
      <el-table-column prop="ZJSL" label="专家数量" align="center"
                       :show-overflow-tooltip="true" width="100"></el-table-column>
    </el-table>
    <el-pagination background v-model:current-page="pageQuery.page" v-model:page-size="pageQuery.size"
                   :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                   class="lui-pagination"
                   @size-change="getDataList" @current-change="getDataList" :total="total">
    </el-pagination>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";


export default defineComponent({
  name: '',
  components: {},
  props: {
    listQuery: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      pageQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
    })

    const getDataList = () => {
      const params = {
        ...state.pageQuery,
        ...props.listQuery,
      }
      axiosUtil.get('/backend/zjgl/zjcxtj/selectZjslByDwPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const exportData = () => {
      let column = [[
        { field: 'ORGNA_TWO_NAME', title: '单位名称'},
        { field: 'ZJSL', title: '专家数量'},
      ]]
      let params = {
        title: "专家汇总统计-按单位",
        name: "专家汇总统计-按单位",
        params: props.listQuery,
        url: '/excel/zjtjadwExport',
      }
      params.column = column
      axiosUtil.exportExcel('/backend/excelExport/magicExcel', params)
    }

    const indexMethod = (index) => {
      return (state.pageQuery.page - 1) * state.pageQuery.size + index + 1;
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      exportData

    }
  }

})
</script>

<style scoped>

</style>
