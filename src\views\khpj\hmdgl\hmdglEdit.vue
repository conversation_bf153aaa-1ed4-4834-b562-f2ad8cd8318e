<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="200px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="12" class="grid-cell">
          <el-form-item label="维护方式：" prop="WHFS">
            <el-radio-group v-model="formData.WHFS" @change="getCldxRes({})" :disabled="!editable">
              <el-radio :label="item.DMXX"
                        v-for="(item,index) in HMDWHFSOptions"  :key="index">
                {{item.DMMC}}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="类型：" prop="CLDXLX">
            <el-select v-model="formData.CLDXLX" class="full-width-input"
                       :disabled="!editable" @change="getCldxRes({})"
                       clearable>
              <el-option v-for="(item, index) in CLDXLXOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='CZYKXZ'">
          <el-form-item label="企业（队伍/人员）名称：" prop="CLDXMC">
            <el-input v-model="formData.CLDXMC" type="text" placeholder="请选择" clearable :disabled="true">
              <template #append v-if="editable">
                <el-button @click="chooseCldx">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='CZYKXZ' && formData.CLDXLX==='DW'">
          <el-form-item label="编号：" prop="DWBM">
            <el-input v-model="formData.DWBM" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>


        <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='SGLR'">
          <el-form-item label="企业名称：" prop="QYMC">
            <el-input v-model="formData.QYMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='SGLR'">
          <el-form-item label="统一信用代码：" prop="TYXYDM">
            <el-input v-model="formData.TYXYDM" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='SGLR' && formData.CLDXLX==='DW'">
          <el-form-item label="队伍名称：" prop="DWMC">
            <el-input v-model="formData.DWMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell" v-if="formData.WHFS==='SGLR' && formData.CLDXLX==='RY'">
          <el-form-item label="人员姓名：" prop="RYXM">
            <el-input v-model="formData.RYXM" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell" v-if="formData.WHFS==='SGLR' && formData.CLDXLX==='RY'">
          <el-form-item label="身份证号：" prop="SFZH">
            <el-input v-model="formData.SFZH" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>




        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="纳入黑名单原因：" prop="HMDYY">
            <el-input v-model="formData.HMDYY" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>


        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="相关附件：" prop="XGFJ">
            <vsfileupload style="margin-left: 10px" :busId="params.id"
                          :key="params.id"
                          :editable="editable" ywlb="hmdfj"/>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="纳入经办人：" prop="JFBZ">
            <div style="margin-left: 10px">{{formData.CJRXM}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="纳入时间：" prop="JFBZ">
            <el-date-picker
                  v-model="formData.CJSJ"
                  :disabled="!editable"
                  type="date"
                  style="width: 200px"
                  placeholder="请选择"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogCLDXVisible"
        v-model="dialogCLDXVisible"
        :title="title"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <cldxChoose LX="HMD" :CLDXLX="formData.CLDXLX" @close="dialogCLDXVisible=false" @submit="getCldxRes"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import cldxChoose from "@views/khpj/cbsclsq/cldxChoose";
import vsfileupload from "@views/components/vsfileupload";


export default defineComponent({
  name: '',
  components: {vsfileupload,cldxChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      HMDID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJDWMC: vsAuth.getAuthInfo().permission.orgnaName,
        CJSJ: comFun.getNowTime(),
        XGSJ: comFun.getNowTime(),
        SHZT: '0',
        WHFS: 'CZYKXZ',
        SJLY: 'ZJWH'
      },

      rules: {
        WHFS: [{
          required: true,
          message: '字段值不可为空',
        }],
        CLDXLX: [{
          required: true,
          message: '字段值不可为空',
        }],
        CLDXMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        HMDYY: [{
          required: true,
          message: '字段值不可为空',
        }],

        QYMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        TYXYDM: [{
          required: true,
          message: '字段值不可为空',
        }],
        DWMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        RYXM: [{
          required: true,
          message: '字段值不可为空',
        }],
        SFZH: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      CLDXLXOptions: [],
      HMDWHFSOptions: [],

      dialogCLDXVisible: false,
      title: '',

    })

    const getFormData = () => {
      let params={
        HMDID: state.HMDID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/hmdgl/selectHmdById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      getCLDXMC()
      let params={
        ...state.formData,
        HMDID: state.HMDID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      if(type==='submit'){
        params.SHZT='1'
      }
      state.loading=true
      axiosUtil.post('/backend/sckhpj/hmdgl/saveHmdglForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getCldxRes = (value) => {
      state.formData.CLDXID=value.CLDXID
      state.formData.QYMC=value.QYMC
      state.formData.TYXYDM=value.TYXYDM
      state.formData.DWMC=value.DWMC
      state.formData.DWBM=value.DWBM
      state.formData.RYXM=value.RYXM
      state.formData.SFZH=value.SFZH
      getCLDXMC()
      state.dialogCLDXVisible=false

    }

    const getCLDXMC = () => {
      if(state.formData.CLDXLX==='QY'){
        state.formData.CLDXMC=state.formData.QYMC
      }else if(state.formData.CLDXLX==='DW'){
        state.formData.CLDXMC=state.formData.DWMC
      }else if(state.formData.CLDXLX==='RY'){
        state.formData.CLDXMC=state.formData.RYXM
      }
    }

    const chooseCldx = () => {
      if(!state.formData.CLDXLX){
        ElMessage.warning('请选择类型')
        return
      }
      state.title=state.CLDXLXOptions.find(item=>item.DMXX===state.formData.CLDXLX)?.DMMC+'选择'
      state.dialogCLDXVisible=true
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      getDMBData('CLDXLX', 'CLDXLXOptions')
      getDMBData('HMDWHFS', 'HMDWHFSOptions')
    })

    return {
      ...toRefs(state),
      closeForm,
      getCldxRes,
      saveData,
      chooseCldx

    }
  }

})
</script>

<style scoped>

</style>
