<!-- 修改密码 -->
<template>
  <div class="lui-card-form">
    <el-form
        class="form-context"
        size="default"
        :model="form"
        ref="vForm"
        :rules="rules"
        label-position="right"
        label-width="120px"
        :disabled="props.isCheck"
    >
      <el-row :gutter="0" class="grid-row" style="margin-bottom:16px;">
        <el-col :span="24" class="grid-cell" style="padding-left: 10px"
        >提示：密码由英文+字母+字符组成，不得少于8位,并且不能连续出现3个大小连续或相同的数字&nbsp;(如：456、654、888)
        </el-col>
        <el-col v-if="props.isCheck" :span="24" class="grid-cell">
          <el-form-item label="登录账号" prop="GLYZH">
            <el-input
                v-model.trim="form.GLYZH"
                maxlength="30"
                minlength="8"
                clearable
                :disabled="true"
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item label="新密码" prop="MM">
            <el-input
                v-model.trim="form.MM"
                type="password"
                clearable
                placeholder="请输入"
                show-password
                minlength="8"
            ></el-input>
          </el-form-item>
        </el-col>
        
        <el-col :span="24" class="grid-cell">
          <el-form-item label="确认密码" prop="surePw">
            <el-input
                v-model="form.surePw"
                type="password"
                clearable
                placeholder="请输入"
                show-password
                minlength="8"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="btn" justify="space-evenly" v-if="!props.isCheck">
        <el-button type="primary" @click="submit">提交</el-button>
      </el-row>
    </el-form>


  </div>
</template>

<script setup>
import {reactive, getCurrentInstance, ref, defineEmits, onMounted, watch} from "vue";
import {vue, auth, runtimeCfg, mixin} from "@src/assets/core/index";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";

const {vsuiRouter} = mixin();
const instance = getCurrentInstance();
import {v4 as uuidv4} from "uuid";
import comFun from "../../../lib/comFun";
import vsAuth from "../../../lib/vsAuth";
const CryptoJS =require("crypto-js")

const props = defineProps({
  isCheck: {
    type: Boolean,
    default: false,
  },
  defaultData: {
    type: Object,
    default: () => ({
      TYPE: null,
      DATAID: null,
    }),
  },
});

const form = reactive({
  GLYZH: "",
  MM: "",
  surePw: "",
});
const fileTableData = vue.ref([]);
const rules = reactive({
  // 密码
  MM: [
    {required: true, message: "密码不能为空", trigger: blur},
    {
      min: 8,
      max: 16,
      message: "不得少于8位，不得超过16位",
      trigger: "change",
    },
    {
      pattern: /^(?=.*\d)(?!.*(\d)\1{2})(?!.*(012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210))(?=.*[a-zA-Z])(?=.*[^\da-zA-Z\s]).{8,16}$/,
      message: "校验未通过！",
      trigger: "blur"
    },
    {
      required: true,
      validator: async (rule, value, callback) => {
        if(value){
          let isCF = await checkMM(value,vsAuth.getAuthInfo().permission.userId)
          if(Boolean(isCF.data)){
            callback()
          }else {
            callback(new Error('密码近期已经使用'))
          }
        }else {
          callback(new Error('密码不能为空'))
        }

      },
    }
    // 
  ],
  //确认密码
  surePw: [
    {required: true, message: "密码不能为空", trigger: blur},
    {
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (value === form.MM) callback();
        else callback(new Error("两次密码输入不一致"));
      },
    },
  ],
});


onMounted(() => {

});
const vForm = ref(null);
const emits = defineEmits(["close", "finish"]);

const checkMM = (MM,USERID) => {
  return axiosUtil.get('/backend/common/dlgl/selectZjxgmm',{MM: CryptoJS.MD5(MM).toString(),USERID})
}

// 注册保存
const submit = () => {
  vForm.value.validate().then(res => {
    console.log(res)
    ElMessageBox.confirm("请您记好修改后的密码，后续请您使用修改后的密码登录?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      const password=CryptoJS.MD5(form.MM).toString()
      let param = {
        password: password,
      }
      console.log(param)
      axiosUtil.get('/backend/login/changePassword', param).then(res => {
        ElMessage.success("修改成功")
        emits("close")
      })
    }).catch(msg => {
      console.log(msg)
    });
  }).catch((err) => {
    console.log(err);
    ElMessage.warning("校验未通过");
  });
};


</script>

<style scoped>
.lui-card-form {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-context {
  width: 800px;
  padding: 30px;
  border: 1px solid rgba(140, 147, 157, 0.58);
  box-shadow: var(--el-box-shadow);
  border-radius: 20px;


}
</style>
