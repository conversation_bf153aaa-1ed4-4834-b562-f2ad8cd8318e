<template>
    <el-form :model="formData" ref="vForm" :rules="vFormRules" label-position="left" class="lui-card-form"
             :label-width="150" size="default">
        <el-row class="grid-row" style="margin-bottom:16px;">
            <!-- <el-col :span="8" class="grid-cell">
                <el-form-item label="人员类型" prop="RYLX">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.RYLX"
                            v-tooltip="{
                newValue: formData.RYLX,
                oldValue: resultTableData?.RYLX,
                label: resultTableData?.RYLX,
              }"
                            placeholder="请输入"
                    ></el-input> -->
                    <!-- <el-select
                        style="width: 100%"
                        v-model="formData.RYLX"
                        v-tooltip="{
                          newValue: formData.RYLX,
                          oldValue: resultTableData?.RYLX,
                          label: rylx.find((i) => i.DMXX == resultTableData?.RYLX)?.DMMC,
                        }"
                        value-key=""
                        placeholder="请选择人员类型"
                        clearable
                        filterable
                    >
                      <el-option
                          v-for="item in rylx"
                          :key="item.DMXX"
                          :label="item.DMMC"
                          :value="item.DMXX"
                      >
                      </el-option>
                    </el-select> -->
                <!-- </el-form-item>
            </el-col> -->
            <el-col :span="8" class="grid-cell">
                <el-form-item label="姓名" prop="RYXM">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.RYXM"
                            v-tooltip="{
                newValue: formData.RYXM,
                oldValue: resultTableData?.RYXM,
                label: resultTableData?.RYXM,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="性别" prop="XBDM">
                    <el-radio-group
                            :disabled="!editable"
                            v-model="formData.XBDM"
                            v-tooltip="{
                newValue: formData.XBDM,
                oldValue: resultTableData?.XBDM,
                label: resultTableData?.XBDM
                  ? { '1': '男', '2': '女' }[resultTableData.XBDM]
                  : null,
              }"
                    >
                        <el-radio label="1">男</el-radio>
                        <el-radio label="2">女</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="身份证号" prop="SFZH">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.SFZH"
                            v-tooltip="{
                newValue: formData.SFZH,
                oldValue: resultTableData?.SFZH,
                label: resultTableData?.SFZH,
              }"
                            placeholder="请输入"
                            @change="setIdCard"
                    ></el-input>
                </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
                <el-form-item label="出生年月" prop="CSNY">
                    <el-date-picker
                            :disabled="!editable"
                            disabled
                            v-model="formData.CSNY"
                            v-tooltip="{
                newValue: formData.CSNY,
                oldValue: resultTableData?.CSNY,
                label: resultTableData?.CSNY,
              }"
                            type="month"
                            value-format="YYYY-MM"
                            placeholder="出生年月"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="参加工作时间" prop="EXTENSION.CJGZSJ">
                    <el-date-picker
                            :disabled="!editable"
                            v-model="formData.EXTENSION.CJGZSJ"
                            v-tooltip="{
                newValue: formData.EXTENSION.CJGZSJ,
                oldValue: resultTableData?.EXTENSION.CJGZSJ,
                label: resultTableData?.EXTENSION.CJGZSJ,
              }"
                            type="date"
                            value-format="YYYY-MM-DD"
                            placeholder="参加工作时间"
                            :disabled-date="disabledDate"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="社保缴纳日期" prop="EXTENSION.SBJNRQ">
                    <el-date-picker
                            :disabled="!editable"
                            v-model="formData.EXTENSION.SBJNRQ"
                            v-tooltip="{
                newValue: formData.EXTENSION.SBJNRQ,
                oldValue: resultTableData?.EXTENSION?.SBJNRQ,
                label: resultTableData?.EXTENSION?.SBJNRQ,
              }"
                            type="date"
                            value-format="YYYY-MM-DD"
                            placeholder="社保缴纳日期"
                            :disabled-date="disabledDate"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="岗位及职务" prop="GWJZW">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.GWJZW"
                            v-tooltip="{
                newValue: formData.GWJZW,
                oldValue: resultTableData?.GWJZW,
                label: resultTableData?.GWJZW,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="本岗工作时长" prop="BGGZSC">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.BGGZSC"
                            v-tooltip="{
                newValue: formData.BGGZSC,
                oldValue: resultTableData?.BGGZSC,
                label: resultTableData?.BGGZSC,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="本岗位工作时间">
                    <el-date-picker
                            :disabled="!editable"
                            v-model="formData.EXTENSION.BGWGZRQ"
                            type="date"
                            value-format="YYYY-MM-DD"
                            placeholder="合同日期开始"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="行政职务">
                    <el-input :disabled="!editable" v-model="formData.EXTENSION.XZZW" placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
                <el-form-item label="毕业院校" prop="BYYX">
                    <el-input
                            v-model="formData.BYYX"
                            :disabled="!editable"
                            v-tooltip="{
                newValue: formData.BYYX,
                oldValue: resultTableData?.BYYX,
                label: resultTableData?.BYYX,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="所学专业" prop="SXZY">
                    <el-input
                            v-model="formData.SXZY"
                            :disabled="!editable"
                            v-tooltip="{
                newValue: formData.SXZY,
                oldValue: resultTableData?.SXZY,
                label: resultTableData?.SXZY,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="学历" prop="DYXL">
                    <!--          <el-input-->
                    <!--              v-model="formData.DYXL"-->
                    <!--              v-tooltip="{-->
                    <!--                newValue: formData.DYXL,-->
                    <!--                oldValue: resultTableData?.DYXL,-->
                    <!--                label: resultTableData?.DYXL,-->
                    <!--              }"-->
                    <!--              placeholder="请输入"-->
                    <!--          ></el-input>-->
                    <el-select
                            style="width: 100%"
                            :disabled="!editable"
                            v-model="formData.DYXL"
                            @change="dyxlChange"
                            v-tooltip="{
               newValue: formData.DYXL,
               oldValue: resultTableData?.DYXL,
               label: ryxl.find((i) => i.DMXX == resultTableData?.DYXL)?.DMMC,
             }"
                            value-key=""
                            placeholder="请选择学历"
                            clearable
                            filterable
                    >
                        <el-option
                                v-for="item in ryxl"
                                :key="item.DMXX"
                                :label="item.DMMC"
                                :value="item.DMXX"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
                <el-form-item label="毕业时间" prop="BYSJ">
                    <el-date-picker
                            :disabled="!editable"
                            v-model="formData.BYSJ"
                            v-tooltip="{
                newValue: formData.BYSJ,
                oldValue: resultTableData?.BYSJ,
                label: resultTableData?.BYSJ,
              }"
                            type="date"
                            value-format="YYYY-MM-DD"
                            placeholder="毕业时间"
                            :disabled-date="disabledDate"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="外语水平" prop="WYSP">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.EXTENSION.WYSP"
                            v-tooltip="{
                newValue: formData.EXTENSION.WYSP,
                oldValue: resultTableData?.EXTENSION?.WYSP,
                label: resultTableData?.EXTENSION?.WYSP,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="职称" prop="ZC">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.ZC"
                            v-tooltip="{
                newValue: formData.ZC,
                oldValue: resultTableData?.ZC,
                label: resultTableData?.ZC,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
                <el-form-item label="职称专业" prop="ZCZY">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.EXTENSION.ZCZY"
                            v-tooltip="{
                newValue: formData.EXTENSION.ZCZY,
                oldValue: resultTableData?.EXTENSION?.ZCZY,
                label: resultTableData?.EXTENSION?.ZCZY,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="职称等级" prop="ZCDJ">
                    <el-input
                            :disabled="!editable"
                            v-model="formData.EXTENSION.ZCDJ"
                            v-tooltip="{
                newValue: formData.EXTENSION.ZCDJ,
                oldValue: resultTableData?.EXTENSION?.ZCDJ,
                label: resultTableData?.EXTENSION?.ZCDJ,
              }"
                            placeholder="请输入"
                    ></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-collapse v-model="activeNames">
            <el-collapse-item name="1">
                <template #title>人员证书信息</template>
                <div style="width: 100%;text-align: right;padding-top: 10px;padding-bottom: 10px" v-if="editable">
                    <el-button type="primary" @click="addRyzsxx(row)">新增行</el-button>
                </div>
                <el-table
                        highlight-current-row
                        size="default"
                        ref="table"
                        class="lui-table"
                        fit
                        height="300px"
                        :border="false"
                        :data="formData.ZSXX"
                >
                    <EleProTableColumn
                            v-for="prop in tableColumn"
                            :col="prop"
                            :key="prop.columnKey"
                    >
                        <template #fileList="{ row, $index }">
                            <vsfileupload
                                    :maxSize="10"
                                    @getFileList="getFileList"
                                    :index="$index"
                                    :busId="row.ZSZSJID"
                                    :key="row.ZSZSJID"
                                    :editable="editable"
                                    ywlb="DWZTBGFJ"
                                    busType="dwxx"
                                    :limit="100"
                            ></vsfileupload>
                        </template>
                        <template #select="{ row }">
                            <el-select
                                    size="normal"
                                    :disabled="!editable"
                                    v-model="row[prop.prop]"
                                    clearable
                                    placeholder="请选择"
                                    @change="changePro(row)"
                            >
                                <el-option
                                        v-for="(item, index) in proDetails"
                                        :key="index"
                                        :value="item.ZYBM"
                                        :label="item.ZYMC"
                                ></el-option>
                            </el-select>
                        </template>
                        <template #input="{ row }">
                            <el-input v-model="row[prop.prop]" :disabled="!editable" placeholder="请输入"></el-input>
                        </template>
                        <template #titleinput="{ row }">
                            <el-input
                                    :disabled="!editable"
                                    v-if="row.SHZT != 1"
                                    v-model="row[prop.prop]"
                                    placeholder="请输入"
                            ></el-input>
                            <span v-else>{{ row[prop.prop] }}</span>
                        </template>
                        <template #time="{ row }">
                            <el-date-picker
                                    :disabled="!editable"
                                    v-model="row[prop.prop]"
                                    type="date"
                                    placeholder="选择日期"
                                    value-format="YYYY-MM-DD"
                                    style="width: 100%"
                            >
                            </el-date-picker>
                        </template>
                        <template #FJ="{ row }">
                            <el-button type="text" @click="updateFile(row)">上传</el-button>
                        </template>
                        <template #ZSLX="{ row }">
                            <el-select v-model="row.ZSLBDM" :disabled="!editable" value-key="id" placeholder="请选择">
                                <el-option
                                        v-for="item in certificateTypeOptions"
                                        :key="item.DMXX"
                                        :label="item.DMMC"
                                        :value="item.DMXX"
                                >
                                </el-option>
                            </el-select>
                        </template>
                        <template #opration="{ row, $index }">
                            <div>
                                <el-button class="lui-table-button" @click="deleteRow(row, $index)">删除</el-button>
                            </div>
                        </template>
                    </EleProTableColumn>
                </el-table>
            </el-collapse-item>
        </el-collapse>
        <div v-if="editable">
            <el-button type="primary" size="default" @click="continueAdd">继续添加</el-button>
            <el-button type="primary" size="default" @click="confirm()">确定</el-button>
            <el-button type="primary" size="default" @click="cancel">取消</el-button>
        </div>
    </el-form>
</template>
<script setup>
    import {defineEmits, defineProps, onMounted, ref, watch} from "vue";
    import EleProTableColumn from "@src/components/ele-pro-table-column";
    import vsfileupload from "@src/views/components/vsfileupload.vue";
    import {v4 as uuidv4} from "uuid";
    import {getCommonSelectDMB} from "@src/api/cbsxx.js";
    import comFun from "../../../../../lib/comFun";
    import {auth} from "../../../../../assets/core";
    import {ElMessage} from "element-plus";

    const defaultData = ref({
        RYLX: null, //人员类型
        RYXM: null, //人员姓名
        XBDM: null, //性别代码
        SFZH: null, //身份证号
        CSNY: null, //出生年月
        SBJNRQ: null,
        GWJZW: null,
        DYXL: null, //文化程度(学历)
        BYYX: null, //毕业院校
        SXZY: null, //所学专业
        BYSJ: null, //毕业时间
        WYSP: null, //
        ZC: null,
        ZCZY: null,
        ZCDJ: null,
        EXTENSION: {},
    });
    const formData = ref({
        RYLX: null, //人员类型
        RYXM: null, //人员姓名
        XBDM: null, //性别代码
        SFZH: null, //身份证号
        CSNY: null, //出生年月
        SBJNRQ: null,
        GWJZW: null,
        DYXL: null, //文化程度(学历)
        BYYX: null, //毕业院校
        SXZY: null, //所学专业
        BYSJ: null, //毕业时间
        WYSP: null, //
        ZC: null,
        ZCZY: null,
        ZCDJ: null,
        EXTENSION: {},
    });
    const props = defineProps({
        editData: {
            type: Object,
            default: () => {
            },
        },
        // 结果表数据，比对用
        resultTableData: {
            type: Object,
            default: () => null,
        },
        // 是否查看模式
        editable: {
            type: Boolean,
            default: true
        }
    });
    const addRyzsxx = () => {
        const userinfo = auth.getPermission();
        let userParams = {
            CJRZH: userinfo.userLoginName,
            CJRXM: userinfo.userName,
            CJDWID: userinfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
        }
        formData.value.ZSXX.push({
            ZSZSJID: comFun.newId(),
            ZSCYZBS: formData.value.CYZSJID,
            ZSDLDM: "RYZS",
            ZSCYZLXDM: "DWCY",
            ...userParams,
        });
    };

    const rylx = ref([]);
    const getRylx = () => {
        getCommonSelectDMB({DMLBID: "RYLX"}).then((res) => {
            rylx.value = res.data;
        });
    };
    const ryxl = ref([]);
    const getRyxl = () => {
        getCommonSelectDMB({DMLBID: "XL"}).then((res) => {
            ryxl.value = res.data;
        });
    };
    // 证书类型下拉框
    const getZslx = () => {
        getCommonSelectDMB({DMLBID: "RYZSLX"}).then((res) => {
            certificateTypeOptions.value = res.data;
        });
    };
    onMounted(() => {
        getRylx();
        getRyxl();
        getZslx();
    });

    watch(
        () => props.editData,
        (val) => {
            console.log("val", val);
            formData.value = Object.assign(formData.value, {...defaultData.value}, {...val});
        },
        {
            immediate: true,
            deep: false,
        }
    );
    const personTypeOptions = ref([
        {
            label: "项目负责人",
            value: "XMFZR",
        },
    ]);
    const certificateTypeOptions = ref([]);
    const activeNames = ref("1");
    const tableColumn = ref([
        {
            label: "序号",
            type: "index",
            align: "center",
        },
        {
            label: "证书类型",
            prop: "ZSLBDM",
            align: "center",
            showOverflowTooltip: true,
            slot: "ZSLX",
        },
        {
            label: "证书名称",
            prop: "ZSMC",
            align: "center",
            showOverflowTooltip: true,
            slot: "input",
        },
        {
            label: "证书编号",
            prop: "ZSBH",
            align: "center",
            showOverflowTooltip: true,
            slot: "input",
        },
        {
            label: "有效开始日期",
            prop: "YXQKS",
            align: "center",
            showOverflowTooltip: true,
            slot: "time",
        },
        {
            label: "有效结束日期",
            prop: "YXQJS",
            align: "center",
            showOverflowTooltip: true,
            slot: "time",
        },
        {
            label: "附件",
            prop: "fileList",
            align: "center",
            showOverflowTooltip: true,
            slot: "fileList",
        },
        {
            label: "操作",
            align: "center",
            width: 150,
            fixed: "right",
            slot: "opration",
            hide: !props.editable
        },
    ]);

    const updateFile = (row, index) => {
    };
    const deleteRow = (row, index) => {
        formData.value.ZSXX.splice(index, 1);
    };

    const emits = defineEmits(["updateData", "close"]);
    // 继续添加
    const continueAdd = () => {
        confirm(true);
    };
    //确认
    const confirm = (isAdd = false) => {
        validateForm()
            .then((result) => {
                emits(
                    "updateData",
                    {
                        ...formData.value,
                    },
                    isAdd
                );
            })
            .catch((err) => {
                console.log(err);
                ElMessage.error(Object.values(err)[0]?.[0]?.message);
            });
    };
    // 取消
    const cancel = () => {
        emits("close");
    };
    const vForm = ref(null);
    const idCardReg = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
    const vFormRules = ref({
        RYLX: [{required: true, message: "请输入人员类型", trigger: "blur"}],
        RYXM: [
            {required: true, message: "请输入姓名", trigger: "blur"},
            {max: 128, message: "最多输入128个字符", trigger: "blur"},
        ],
        XBDM: [{required: false, message: "请选择性别", trigger: "change"}],
        SFZH: [
            {required: true, message: "请输入身份证号", trigger: "blur"},
            {max: 32, message: "最多输入32个字符", trigger: "blur"},
            {
                pattern: idCardReg,
                message: "请输入正确的身份证号",
            },
        ],
        CSNY: [{required: false, message: "请选择出生年月", trigger: "blur"}],
        'EXTENSION.CJGZSJ': [{required: false, message: "请选择参加工作时间", trigger: "blur"},
            {
                validator: (rule, value, callback) => {//参加工作日期日期格式校验
                    if (value && formData.value.BYSJ) {
                        if (value < formData.value.BYSJ) {
                            callback(new Error('参加工作日期必须大于毕业时间'));
                        } else {
                            callback();
                        }
                    } else {
                        callback();
                    }
                }, trigger: 'blur'
            }
        ],
        EXTENSION: {
            SBJNRQ: [{required: false, message: "请选择社保缴纳日期", trigger: "change"}
                , {
                    validator: (rule, value, callback) => {
                        if (value && formData.value.EXTENSION.CJGZSJ) {
                            if (value < formData.value.EXTENSION.CJGZSJ) {
                                callback(new Error('社保缴纳日期必须大于参加工作日期'));
                            } else {
                                callback();
                            }
                        } else {
                            callback();
                        }
                    }, trigger: 'blur'
                }
            ],
            WYSP: [
                {required: false, message: "请输入外语水平", trigger: "blur"},
                {max: 128, message: "最多输入128个字符", trigger: "blur"},
            ],
            ZCZY: [
                {required: false, message: "请输入职称专业", trigger: "blur"},
                {max: 128, message: "最多输入128个字符", trigger: "blur"},
            ],
            ZCDJ: [
                {required: false, message: "请输入职称等级", trigger: "blur"},
                {max: 128, message: "最多输入128个字符", trigger: "blur"},
            ],
        },

        GWJZW: [
            {required: false, message: "请输入岗位及职务", trigger: "blur"},
            {max: 128, message: "最多输入128个字符", trigger: "blur"},
        ],
        BGGZSC: [
            {required: false, message: "请输入本岗工作时长", trigger: "blur"},
            {max: 40, message: "最多输入40个字符", trigger: "blur"},
        ],
        DYXL: [
            {required: false, message: "请输入学历", trigger: "blur"},
            {max: 100, message: "最多输入100个字符", trigger: "blur"},
        ],
        BYYX: [
            {required: false, message: "请输入毕业院校", trigger: "blur"},
            {max: 32, message: "最多输入100个字符", trigger: "blur"},
        ],
        SXZY: [
            {required: false, message: "请输入所学专业", trigger: "blur"},
            {max: 32, message: "最多输入100个字符", trigger: "blur"},
        ],
        BYSJ: [{required: false, message: "请选择毕业时间", trigger: "blur"},
            {
                validator: (rule, value, callback) => {
                    if (value && formData.value.EXTENSION.CJGZSJ) {
                        if (value > formData.value.EXTENSION.CJGZSJ) {
                            callback(new Error('毕业日期必须小于参加工作日期'));
                        } else {
                            callback();
                        }
                    } else {
                        callback();
                    }
                }, trigger: 'blur'
            }],

        ZC: [
            {required: false, message: "请输入职称", trigger: "blur"},
            {max: 100, message: "最多输入100个字符", trigger: "blur"},
        ],
    });
    const setIdCard = val => {
        if (idCardReg.test(val)) {
            formData.value.CSNY = val.slice(6, 12).replace(/(.{4})(.{2})/, "$1-$2")
        }
    }
    const validateForm = () => {
        return Promise.all([vForm.value.validate(), validateZsxx()]);
    };

    const validateZsxx = () => {
        return new Promise((resolve, reject) => {
            if (formData.value.ZSXX.length > 0) {
                let error = '';
                for (let i = 0; i < formData.value.ZSXX.length; i++) {
                    let map = formData.value.ZSXX[i];
                    if (!map.ZSLBDM) {
                        error = '请选择第' + (i + 1) + '行证书类型';
                        break;
                    }
                    if (!map.ZSMC) {
                        error = '请填写第' + (i + 1) + '行证书名称';
                        break;
                    }
                    if (!map.ZSBH) {
                        error = '请填写第' + (i + 1) + '行证书编号';
                        break;
                    }
                    if (!map.YXQKS) {
                        error = '请选择第' + (i + 1) + '行有效开始日期';
                        break;
                    }
                    if (!map.YXQJS) {
                        error = '请选择第' + (i + 1) + '行有效结束日期';
                        break;
                    }
                    if (map.fileList.length === 0) {
                        error = '请上传第' + (i + 1) + '行附件';
                        break;
                    }
                }
                if (error) {
                    reject({JBXX: [{message: error}]});
                } else {
                    resolve(true);
                }
            }
            resolve(true);

        })
    };

    const getFileList = (res) => {
        formData.value.ZSXX[res.index].fileList = res.fileList;
    };

    const disabledDate = (time) => {
        return time.getTime() > Date.now()
    };

    const dyxlChange = (val) => {
        ryxl.value.forEach(item => {
            if(val === item.DMXX) {
                formData.value.RYXLPXH = item.PXH;
            }
        })
    };
    defineExpose({
        validateForm
    });
</script>
<style scoped>
    /deep/ .el-collapse-item__header {
        background-color: #F2F3F5;
        padding-left: 10px;
        margin-bottom: 10px;
        height: 30px;
    }
</style>
