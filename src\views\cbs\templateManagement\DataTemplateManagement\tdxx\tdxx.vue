<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        class="lui-page"
      :model="state"
      ref="vForm"
      label-width="0"
      :inline="false"
        size="default"
      style="height: 100%"
      :disabled="!editable"
    >
      <el-table
        highlight-current-row
        size="default"
        ref="table"
        height="100%"
        fit
        class="lui-table"
        :border="false"
        :data="state.tableData"
      >
        <EleProTableColumn
          v-for="prop in state.tableColumn"
          :col="prop"
          :key="prop.columnKey"
        >
          <template #info="{ row }">
            <el-tooltip :content="row[prop.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>
          <template #fileList="{ row, $index }">
            <vsfileupload
                :maxSize="10"
              @getFileList="getFileList"
              :index="$index"
              :ref="addRefs($index)"
              :editable="false"
              :busId="row.TDID"
              :key="row.TDID"
              ywlb="TDFJ"
              busType="dwxx"
              :limit="100"
            ></vsfileupload>
          </template>
          <template #select="{ row, $index }">
            <el-select
              size="normal"
              v-model="row[prop.prop]"
              clearable
              placeholder="请选择"
              @change="changePro(row)"
            >
              <el-option
                v-for="(item, index) in proDetails"
                :key="index"
                :value="item.ZYBM"
                :label="item.ZYMC"
              ></el-option>
            </el-select>
          </template>
          <template #input="{ row, $index }">
            <el-form-item
              v-if="$index > -1"
              :prop="`tableData.${$index}.${prop.prop}`"
              :rules="[
                {
                  required: prop.required,
                  message: `请输入${prop.label}`,
                  trigger: 'blur',
                },
                {
                  max: prop.maxlength,
                  message: `最多输入${prop.maxlength}个字符`,
                  trigger: 'change',
                },
              ]"
            >
              <el-input
                v-model="row[prop.prop]"
                v-tooltip="{
                  newValue: row[prop.prop],
                  oldValue: resultTableData?.find(i => i.TDWYBS == row.TDWYBS)?.[prop.prop],
                  label: resultTableData?.find(i => i.TDWYBS == row.TDWYBS)?.[prop.prop],
                }"
                :maxlength="prop.maxlength"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </template>
          <template #titleinput="{ row }">
            <el-input
              v-if="row.SHZT != 1"
              v-model="row[prop.prop]"
              placeholder="请输入"
            ></el-input>
            <span v-else>{{ row[prop.prop] }}</span>
          </template>
          <template #time="{ row }">
            <el-date-picker
              v-model="row[prop.prop]"
              type="date"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            >
            </el-date-picker>
          </template>
          <template #opration="{ row, $index }">
            <div>
              <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
<!--              <el-button class="lui-table-button" @click="copyRow(row, $index)">复制</el-button>-->
              <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button v-if="row.SHZT != 1 && row.EXTENSION?.SJLY!=='MBSC'" class="lui-table-button" @click="deleteRow(row, $index)"
                >删除</el-button
              >
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-form>
  </div>

  <el-dialog
      custom-class="lui-dialog"
      title="信息项选择"
      v-model="state.chooseVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      top="50px"
      width="1200px"
      @close="() => {}"
  >
    <tdxxXz
        :key="editIndex"
        :currentRow="currentRow"
        @updateChooseData="updateChooseData"
        @updateEditData="updateEditData"
        @close="state.chooseVisible = false"
        :TYXYDM="TYXYDM"
    />
  </el-dialog>
</template>
<script setup>
import { defineProps, reactive, watch, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";

import { v4 as uuidv4 } from "uuid";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import tdxxXz from "./tdxx_xz.vue";
import axiosUtil from "../../../../../lib/axiosUtil";
import {InfoFilled} from "@element-plus/icons-vue";

const getFileList = (res) => {
  state.tableData[res.index].fileList = res.fileList;
};
const props = defineProps({
  defaultData: {
    type: Array,
    defaultData: () => [],
  },
  proDetails: {
    type: Array,
    defaultData: () => [],
  },
  // 结果表数据，比对用
  resultTableData: {
    type: Object,
    default: () => null,
  },
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  }
});
const state = reactive({
  chooseVisible:false,
  tableData: [{}],
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    {
      label: "专业名称",
      prop: "ZYMC",
      align: "center",
      showOverflowTooltip: true,
      width: 100,
      // slot: "select"
    },
    {
      label: "业务要求",
      prop: "YWYQ",
      align: "center",
      showOverflowTooltip: true,
      width: 80,
      slot: "info"
    },
    {
      label: "录入资料说明",
      prop: "LRZLSM",
      align: "center",
      showOverflowTooltip: true,
      width: 80,
      slot: "info"
    },
    {
      label: "信息项",
      prop: "XXXMC",
      align: "center",
      showOverflowTooltip: true,
      width: 150,
    },



    // {
    //   label: "信息项",
    //   prop: "XXXMC",
    //   align: "center",
    //   showOverflowTooltip: true,
    //   width: 150,
    // },Name	Code	Comment	Domain	Data Type	Length	Precision	Primary	Foreign Key	Mandatory
    {
      label: "所在地",
      prop: "SZD",
      align: "center",
      width: 250,
      maxlength: 128,
      required: true,
      // slot: "input",
    },
    {
      label: "产权所有人",
      prop: "CQSYR",
      align: "center",
      //width: 200,
      maxlength: 64,
      width: 150,
      required: true,
      // slot: "input",
    },
     {
      label: "面积",
      prop: "MJ",
      align: "center",
      //width: 200,
      maxlength: 64,
       width: 150,
      required: true,
      // slot: "input",
    },
     {
      label: "数量",
      prop: "SL",
      align: "center",
      //width: 200,
      maxlength: 128,
       width: 150,
      required: true,
      // slot: "input",
    },
    // {
    //   label: "知识产权类型",
    //   prop: "ZSCQLX",
    //   align: "center",
    //   width: 150,
    //   maxlength: 100,
    //   slot: "input",
    // },
    // {
    //   label: "申请日期",
    //   prop: "SQRQ",
    //   align: "center",
    //   width: 150,
    //   slot: "time",
    // },
    {
      label: "附件",
      prop: "fileList",
      headerAlign: "center",
      align: "left",
      slot: "fileList",
      width: 200,
    },
    {
      label: "操作",
      align: "center",
      width: 200,
      fixed: "right",
      slot: "opration",
      hide: !props.editable
    },
  ],
});

const currentRow = ref({});
const editIndex = ref(0);
const refs = ref([]);
const addRefs=(id)=> {
  return (el) => {
    refs.value[id] = el;
  };
}
const updateChooseData = (val) => {
  changeData(currentRow.value,val,editIndex.value,false)
};

const changeData = (oldRow,newRow,index,visible) => {
  let params={
    newId: oldRow.TDID,
    oldId: newRow.TDZSJID,
    cover: true
  }

  axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
    if(oldRow.EXTENSION?.SJLY==='MBSC'){
      if(!newRow.EXTENSION){
        newRow.EXTENSION={
          SJLY: 'MBSC'
        }
      }else {
        newRow.EXTENSION.SJLY='MBSC'
      }
    }
    oldRow.EXTENSION=newRow.EXTENSION
    oldRow.TDZSJID=newRow.TDZSJID
    oldRow.SZD=newRow.SZD
    oldRow.CQSYR=newRow.CQSYR
    oldRow.MJ=newRow.MJ
    oldRow.SL=newRow.SL
    refs.value[editIndex.value].loadFileList()
    state.chooseVisible = visible;
  })
}

const updateEditData = (row) => {
  state.tableData.forEach((item,index)=>{
    if(item.TDZSJID===row.TDZSJID){
      changeData(item,row,index,true)
    }
  })
}

const chooseRow = (row, index) => {
  currentRow.value=row;
  editIndex.value = index;
  state.chooseVisible = true;
};

const changePro = (row) => {
  row.ZYMC = props.proDetails.filter((x) => x.ZYBM == row.ZYBM)[0].ZYMC;
};
watch(
  () => props.defaultData,
  (val) => {
    if (val) {
      val.forEach((x) => {
        const UUID = uuidv4().replace(/-/g, "");
        x.TDID = x.TDID || UUID;
        x.TDWYBS = x.TDWYBS || UUID;
      });
    }
    state.tableData = val;
  },
  {
    immediate: true,
  }
);
const copyRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, "");
  state.tableData.splice(index, 0, { ...row, TDID: UUID, TDWYBS: UUID ,SHZT:''});
};
const insertRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, "");
  state.tableData.splice(index + 1, 0, {
    TDID: UUID,
    TDWYBS: UUID,
    ZYMC: row.ZYMC,
    ZYBM: row.ZYBM,
    XXXMC: row.XXXMC,
    MBMXID: row.MBMXID,
    YWYQ: row.YWYQ,
    LRZLSM: row.LRZLSM,
    XXX: "",
    XMMC:"",
    JCQK:""
  });
};

const deleteRow = (row, index) => {
  ElMessageBox.confirm("是否删除此条数据？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      state.tableData.splice(index, 1);
      ElMessage({
        message: "删除成功!",
        type: "success",
      });
    })
    .catch(() => {});
};
const vForm = ref(null);
const validateForm = () => {
        return new Promise((resolve, reject) => {
          if(state.tableData.find(item=>(item.SFBT=='1' && !item.TDZSJID))){
            reject({mgs:[{message:'请完善土地信息！'}]})
          }else {
            resolve(true)
          }
        })
};
defineExpose({
  validateForm,
});
</script>
<style scoped>
:deep(.el-table-fixed-column--right) {
  background-color: rgba(255, 255, 255, 1) !important;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form-item__error) {
  top: 50%;
  transform: translateY(-50%);
  left: 40px;
}
</style>
