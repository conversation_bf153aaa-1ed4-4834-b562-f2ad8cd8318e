<template>
  <div v-loading="loading">
    <component ref="busiCom" :is="componentChild[formData.BYZD1]" class="busiCom" @closeMsg="closeMsg"
               v-model:value="params" @close="closeForm" @toAskList="toAskList" :params="businessParams"></component>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw} from "vue";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import comFun from "@lib/comFun";


import cbsfmqdEdit from "@views/khpj/cbsfmqd/cbsfmqdEdit";
import xstzfbView from "@views/zbxsgl/xstzfb/xstzfbView";
import zbtzsqfView from "@views/zbxsgl/zbtzsqf/zbtzsqfView";
import hytxEdit from "@views/zbxsgl/pwcq/hytxEdit";
import cbshhEdit from "./cbshhEdit.vue";

export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      ID: props.params.id,

      formData: {
        BYZD1: ''
      },

      componentChild: {
        'cbsclsq': markRaw(cbsfmqdEdit),
        'xstzgg': markRaw(xstzfbView),
        'zbjgtz': markRaw(zbtzsqfView),
        'hytxEdit': markRaw(hytxEdit),
        'hhEdit': markRaw(cbshhEdit)
      },
      businessParams: {
        editable: false,
        operation: props.params.editable ? 'msg' : 'view'
      },



    })

    const getFormData = () => {
      let params = {
        ID: state.ID
      }
      state.loading=true
      axiosUtil.get('/backend/xxtx/selectXxtxById', params).then((res) => {
        state.businessParams.id=res.data.BUSINESSID
        state.formData=res.data
        state.loading=false
      })

    }

    const closeMsg = () => {
      let params={
        ID: state.ID,
        SHZT: '1',
        BLSJ: comFun.getNowTime()
      }
      axiosUtil.post('/backend/xxtx/saveXxtxForm',params).then(res=>{
        ElMessage.success('消息已阅')
        closeForm()
      })
    }

    const closeForm = () => {
      emit('close')
    }
    const toAskList = (faid) => {
      emit('toAskList',faid)
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      closeForm,
      closeMsg,
      toAskList

    }
  }

})
</script>

<style scoped>

</style>
