<!-- 审核框架页 -->
<template>
  <div id="auditDiv">
    <el-container>
      <el-main style="padding: 10px">
        <component ref="busiCom" :is="componentChild[busiUrl]" v-model:value="params" @closeAudit="close"
                   @confirm="finishTask" @addTask="addTask" :params="businessParams"></component>
      </el-main>
      <el-footer style="height: 40px; margin: 4px auto;">
        <el-button size="default" v-for="item in buttons" :type="item.BYZD1||'primary'" v-bind:key="item.ACTIVITYID"
                   @click="onOperate(item)">{{ item.APPROVENAME }}
        </el-button>
        <el-button size="default" type="success" v-if="processParams.flag != 'view' &&processParams.status != '3' && businessParams.operation!=='view' &&
            processParams.activityId != undefined && (processParams.activityId == 'new' ||processParams.activityId == '1')"
                   @click="onSave()">保存
        </el-button>
        <el-button size="default" type="primary"
                   v-if="processParams.flag != 'view' &&processParams.status != '3' && businessParams.operation!=='view' &&
            processParams.activityId != undefined && (processParams.activityId == 'new')"
                   @click="onSubmit">提交
        </el-button>
         <el-button size="default" type="primary" v-if="processParams.flag != 'view' && processParams.status != '3'&&processParams.activityId == '1'" @click="onSubmitReturn">提交退回节点</el-button>
        <el-button size="default" type="warning"
                   v-if="processParams.flag != 'view' &&processParams.status != '3' && businessParams.operation!=='view' &&
            processParams.activityId != undefined && (processParams.activityId == '1')&&processParams.processId!='XSGL_PWCQ'"
                   @click="onSubmit">重新发起审核
        </el-button>
       
        <el-button size="default" @click="close">关闭</el-button>
      </el-footer>
    </el-container>
    <el-dialog v-model="showDialog" :title="shTItle" :append-to-body="true" :width="width+'px'"  z-index="1000">
      <component ref="lcshForm" v-if="showDialog && examUrl &&apprValue=='1'" :is="AuditingChild[examUrl]"
                 :apprValue="apprValue" :params="processParams" :Activityid="nextActivityid"
                 @changeTitle="changeTitle" @changeWidth="changeWidth" @confirm="finishTask" @close="closeOpinion"/>
      <opinion-form v-else-if="showDialog" :apprValue="apprValue" :params="params" @close="closeOpinion" @confirm="finishTask"></opinion-form>
    </el-dialog>
  </div>
</template>
<script>

import {
  defineComponent,
  defineAsyncComponent,
  toRefs,
  reactive,
  markRaw,
  nextTick,
  ref,
  getCurrentInstance,
  onMounted
}
  from 'vue'
import {ElLoading, ElMessage, ElMessageBox} from "element-plus";
import api from "../../api/lc";
import OpinionForm from "./OpinionForm.vue";
import axios from "axios";
import vsAuth from "@src/lib/vsAuth";
import axiosUtil from "../../lib/axiosUtil";


//承包商准入
import cbszr from "../cbs/cbsyj/index";
//队伍变更
import bgdwzyEdit from "@views/cbs/cbsbg/bgdwzyEdit";
//企业变更
import qybgEdit from "@views/cbs/cbsbg/cbsqybg/qybgEdit";
//选商申请
import xssqglEdit from "@views/zbxsgl/xssqgl/xssqglEdit";
//问题整改
import wtzgyssqEdit from "@views/khpj/wtzgyssq/wtzgyssqEdit";

import khmxtbEdit from "../khpj/khmxtb_jh/khmxtbEdit";

import zjrkForm from "@views/zjgl/zjcrk/zjrkEdit.vue";//专家入库审批流程

import pwcqglEdit from "@views/zbxsgl/pwcq/pwcqglEdit";

// //測試
// import testDialog from "../test/workflowTest/testDialog";

import hcbgscForm from "@views/cbs/cbsyj/cbssh/hcbgscForm";
import zbtzsscForm from "@views/cbs/cbsyj/cbssh/zbtzsscForm";
import zbbmtjshForm from "@views/zbxsgl/xssqgl/examinePage/zbbmtjshForm";
import ytzybmtjshForm from "@views/zbxsgl/xssqgl/examinePage/ytzybmtjshForm";
import ytfgbshForm from "@views/zbxsgl/xssqgl/examinePage/ytfgbshForm";
import cbsqgffForm from "@views/cbs/cbsyj/cbssh/cbsqgffForm";//企管分发
import chooseCbsYtZyglbm from "@views/zbxsgl/xssqgl/examinePage/chooseCbsYtZyglbmForm";//承包商二级单位专业部门选择机关部门主要人员

//选择二级单位领导
import chooseEjdwZyld from "@views/zbxsgl/xssqgl/examinePage/chooseEjdwZyldForm";

export default defineComponent({
  name: 'AuditFrame',
  components: {OpinionForm, cbszr},
  props: {
    processParams: {
      type: Object,
      default: {},
    },
    businessParams: {
      type: Object,
      default: {},
    },
  },
  emits: ['close'],
  setup(props, context) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      busiUrl: null,
      examUrl: null,
      nextActivityid: "",//下节点，用于选人参数
      busiCom: null,
      params: props.processParams,
      businessParams: props.businessParams,
      strSystemCode: "",
      buttons: [],
      showDialog: false,
      apprValue: null,
      apprResult: null,
      height: "calc(100vh - 150px)!important",
      nextPerformer: "",
      shTItle: '审核意见',
      componentChild: {
        'cbszr': markRaw(cbszr),
        'cbsbg' : markRaw(bgdwzyEdit),
        'zjrk':markRaw(zjrkForm),
        'qybg':markRaw(qybgEdit),
        'khmxsb' : markRaw(khmxtbEdit),
        'xssq' : markRaw(xssqglEdit),
        'wtzgys' : markRaw(wtzgyssqEdit),
        'pwcq' : markRaw(pwcqglEdit),
      },
      AuditingChild:{
        'schcbg': markRaw(hcbgscForm),
        'cbsqgff': markRaw(cbsqgffForm),
        'sczbtzs' : markRaw(zbtzsscForm),
        'xszbbmsh' : markRaw(zbbmtjshForm),
        'xsytzybmsh' : markRaw(ytzybmtjshForm),
        'xsytfgbsh' : markRaw(ytfgbshForm),
        'chooseEjdwZyld':markRaw(chooseEjdwZyld),
        'chooseCbsYtZyglbm':markRaw(chooseCbsYtZyglbm),
      },
      width: 500,

      activityInfo:{},
    })

    const instance = getCurrentInstance()


    /**
     * 加载审核页面
     */
    const loadAuditPage = async () => {
      let ld = ElLoading.service({target: "#auditDiv", text: "正在加载页面，请稍后...",});
      if (props.processParams.status == "3") {
        //已办查看页面
        try {
          let res = await axios({
            url: api.getTaskInfo(),
            data: "varJson=" + JSON.stringify(state.params),
            method: "post",
          });
          if (res.data) {
            state.busiUrl = res.data.businessUrl;
            if(res.data.examineUrl){
              var arr=res.data.examineUrl.split('?');
              if(arr.length>1){
                state.nextActivityid=arr[1];
              }
              state.examUrl=arr[0];
            }
          }
        } catch (error) {
          ld.close();
        }
      } else {
        //待办审核页面
        try {
          let resInfo = await axios({
            url: api.getTaskInfo(),
            data: "varJson=" + JSON.stringify(state.params),
            method: "post",
          });
          if (resInfo.data) {
            state.busiUrl = resInfo.data.businessUrl;
            if(resInfo.data.examineUrl){
              var arr=resInfo.data.examineUrl.split('?');
              if(arr.length>1){
                state.nextActivityid=arr[1];
              }
              state.examUrl=arr[0];
            }
          }
        } catch (error) {
          ld.close();
        }
      }
      //componentChild = defineAsyncComponent(() => import(`@views/${state.busiUrl}.vue`));
      ld.close();
    }
    /**
     * 获取审核按钮
     */
    const getApproveValue = async () => {
      if (state.params.status == "3") {
        return;
      }
      let ld = ElLoading.service({target: "#auditDiv", text: "正在加载审核按钮，请稍后...",});
      if (!state.params.Processversion) {
        state.params.Processversion = '1'
      }
      try {
        let res = await axios({
          method: "post",
          url: api.getApproveValue(),
          data: "varJson=" + JSON.stringify(state.params),
        });
        if (res.data && res.data.appValueList) {
          state.buttons = res.data.appValueList;
          ld.close();
        }
      } catch (error) {
        ld.close();
      }
    }

    const close = () => {
      context.emit("close");
    }

    const closeOpinion = () => {
      state.showDialog = false;
    }
    /**
     * 动态按钮事件
     */
    const onOperate = (item) => {
      console.log('***************************')
      //业务变量
      if (item.APPROVEVAR == "ywbl") {
        let f = instance.proxy.$refs["busiCom"][item.APPROVEVALUE];
        if (f && f instanceof Function) {
          let valName = item.APPROVENAME;
          state.apprValue = 1;
          state.apprResult = valName;
          f();
        }
        return;
      } else if (item.APPROVENAME === '指派') {
        reassignTask()
      } else {
        let val = item.APPROVEVALUE;
        let valName = item.APPROVENAME;
        console.log('执行了嘛？',val)
        instance.proxy.$refs["busiCom"].saveData(val)
            .then(() => {
              if (state.params.activityId == "new") {
                addTask();
              } else {
                state.apprValue = val;
                state.apprResult = valName;
                if (state.params.activityId == "1" || item.isSubmit) {
                  finishTask();
                } else {
                  state.showDialog = false;
                  nextTick(() => {
                    if (state.apprValue == 0) {
                      state.shTItle = '驳回意见'
                    } else {
                      state.shTItle = '审核意见'
                    }
                    state.showDialog = true;
                  });
                }
              }
            })
            .catch((error) => {
              console.log(error);
            });
      }
    }
    /**
     * 保存
     */
    const onSave = () => {
      instance.proxy.$refs["busiCom"]
          .saveData(null)
          .then(() => {
            ElMessage({
              message: '保存成功！',
              customClass: "myMessageClass",
              type: 'success',
            })
          })
          .catch((error) => {
            console.log(error);
            ElMessage({
              message: '保存失败！',
              customClass: "myMessageClass",
              type: 'error',
            })
          });
    }
    /**
     * 提交
     */
    const onSubmit = () => {
      onOperate({
        APPROVEVALUE: "1",
        APPROVENAME: "提交",
        isSubmit: true,
      });
    }
    /**
     * 创建流程
     */
    const addTask = (sffsdx) => {
      console.log("222222222222222222222222222", state.params)

      let _params = {};
      if (state.params.nextPerformer) { //如果参数里有下节点办理人
        _params.nextPerformer = state.params.nextPerformer;
      }
      if (sffsdx) {
        _params.conditionStr = 'sffsdx=' + sffsdx;
      } else {
        _params.conditionStr = 'sffsdx=0';
      }
      if (state.params.conditionStr) {
        _params.conditionStr = _params.conditionStr + '@' + state.params.conditionStr;
      } else {
        _params.conditionStr = _params.conditionStr + '@lczx=1';
      }
      // _params.nextPerformer = 'admin';

      _params.strSystemCode = state.strSystemCode;
      _params.processId = state.params.processId;
      _params.engineType = state.params.engineType;
      _params.activityId = state.params.activityId;
      _params.processInstanceName = state.params.processInstanceName;
      _params.businessId = state.params.businessId;
      _params.apprValue = "1";
      _params.apprResult = "提交";

      _params.loginName = state.userInfo.userLoginName;
      _params.userOrgId = state.userInfo.orgnaId;

      let ld = ElLoading.service({
        target: "#auditDiv",
        text: "正在提交数据，请稍后...",
      });
      axios({
        method: "post",
        url: api.createTask(),
        data: "varJson=" + JSON.stringify(_params),
      })
          .then((res) => {
            ld.close();
            if (res.data && res.data.result == 1) {
              ElMessage({message: '提交成功！', type: 'success',})
              context.emit("close");
            } else {
              axiosUtil.get('/backend/workFlow/backBusinessStatus', {processId:_params.processId,businessId:_params.businessId})
              ElMessage({message: res.data.error,type: 'error',})
            }
          })
          .catch((error) => {
            axiosUtil.get('/backend/workFlow/backBusinessStatus', {processId:_params.processId,businessId:_params.businessId})
            console.log(error);
            ld.close();
            ElMessage({message: '提交失败！', type: 'error',})
          });
    }
    /**
     * 办理待办
     */
    const finishTask = (shyj, sffsdx ,nextPerformer) => {
      if (!sffsdx) {
        sffsdx = '1';
      }
      state.showDialog = false;
      let _params = {};
      if (state.params.nextPerformer) { //如果参数里有下节点办理人
        _params.nextPerformer = state.params.nextPerformer;
      }
      _params.strSystemCode = state.strSystemCode;
      if (state.apprValue != null) {
        _params.apprValue = state.apprValue;
      } else {
        _params.apprValue = state.params.apprValue;
      }
      _params.apprResult = state.apprResult;
      if (_params.apprResult == '同意') {
        _params.apprResult = '审核通过'
      } else if (_params.apprResult == '不同意') {
        _params.apprResult = '审核驳回'
      }
      if(state.params.activityId=='1'){
          _params.apprResult = '提交'
      }
      // _params.nextPerformer = 'admin';
      _params.opinion = shyj;
      _params.taskId = state.params.taskId;
      _params.processId = state.params.processId;
      _params.activityId = state.params.activityId;
      _params.processInstanceName = state.params.processInstanceName;
      _params.processInstanceId = state.params.processInstanceId;
      _params.engineType = state.params.engineType;
      _params.doType = "send";
      _params.businessId = state.params.businessId;

      _params.loginName = state.userInfo.userLoginName;
      _params.userOrgId = state.userInfo.orgnaId;
      _params.conditionStr = 'sffsdx=' + sffsdx;
      if (state.params.conditionStr) {
        _params.conditionStr = _params.conditionStr + '@' + state.params.conditionStr;
      } else {
        _params.conditionStr = _params.conditionStr + '@lczx=1';
      }
      if(nextPerformer){
        if(_params.nextPerformer){
          nextPerformer.push(_params.nextPerformer)
        }
        _params.nextPerformer=nextPerformer.join(',')
      }

      delTbmBlr(_params.apprValue).then(res=>{
        let ld = ElLoading.service({
          target: "#auditDiv",
          text: "正在提交数据，请稍后...",
        });
        axios({
          method: "post",
          url: api.finishTask(),
          data: "varJson=" + JSON.stringify(_params),
        }).then((res) => {
          ld.close();
          if (res.data && res.data.result == 1) {
            ElMessage({message: '提交成功！', type: 'success',})
            context.emit("close");
          }else{
            ElMessage({message: res.data.error,type: 'error',})
          }
        }).catch((error) => {
          console.log(error);
          ElMessage({message: '提交失败！', type: 'error',})
          ld.close();
        });
      })


    }

    /**
     * 删除同部门办理人
     */
    const delTbmBlr = (val) => {
      return new Promise((resolve, reject) => {
        if(val!='1') {
          resolve(true)
          return
        }
        if(state.activityInfo.BYZD5!=='1'&&state.activityInfo.BYZD5!=='2'){//1是同部门抢签，2是整个节点抢签
          resolve(true)
          return
        }


        let params={
          BLR: state.userInfo.userLoginName,
          BLRORG: state.userInfo.orgnaId,
          businessId: state.params.businessId,
          activityId: state.params.activityId,
          BYZD5:state.activityInfo.BYZD5
        }
        axiosUtil.get('/backend/workFlow/deltdwBlr',params).then(res=>{
          resolve(true)
        })
      })
    }

    /**
     * 获取下一节点办理人
     */
    const getNextPerformer = async (varJson) => {
      let ld = ElLoading.service({
        target: "#auditDiv",
        text: "正在获取下一节点办理人，请稍后...",
      });
      try {
        let res = await axios({
          method: "post",
          url: api.getNextPerformer(),
          data: "varJson=" + JSON.stringify(varJson),
        });
        if (res.data && res.data.nextPerformer) {
          ld.close();
          return res.data.nextPerformer;
        }
      } catch (error) {
        console.log(error);
        ld.close();
        return "";
      }
    }

    /**
     * 指派任务
     * @param shyj
     * @param sffsdx
     * @returns {Promise<void>}
     */
    const reassignTask = async (shyj, sffsdx) => {
      if (!sffsdx) {
        sffsdx = '1';
      }
      let _params = {};
      _params.toUser = state.params.nextPerformer;
      _params.toUser = 'dongyingzhao'
      _params.fromUser = state.userInfo.userLoginName;
      _params.taskId = state.params.taskId;
      // _params.engineType = 'h3'
      _params.opinion = shyj;
      _params.taskId = state.params.taskId;
      _params.processId = state.params.processId;
      _params.activityId = state.params.activityId;
      _params.processInstanceName = state.params.processInstanceName;
      _params.processInstanceId = state.params.processInstanceId;
      _params.engineType = state.params.engineType;
      _params.doType = "send";
      _params.businessId = state.params.businessId;
      _params.loginName = state.userInfo.username;
      _params.userOrgId = state.userInfo.orgId;
      _params.sffsdx = sffsdx;
      let ld = ElLoading.service({
        target: "#auditDiv",
        text: "正在提交数据，请稍后...",
      });
      axios({
        method: "post",
        url: api.reassignTask(),
        data: "varJson=" + JSON.stringify(_params),
      }).then((res) => {
        if (res.data && res.data.result == 1) {
          ElMessage({message: '委派成功！', type: 'success',})
          context.emit("close");
          ld.close();
        } else {
          ElMessage({message: '委派失败！', type: 'error',})
          ld.close();
        }
      }).catch((error) => {
        console.log(error);
        ld.close();
      });
    }

    const onSubmitReturn = () =>{
      let val = '1';
      let valName = "";
      instance.proxy.$refs["busiCom"].saveData(val).then(() => {
        let activityParams = {};
        activityParams.processId = state.params.processId;
        activityParams.activityId = state.params.activityId;
        activityParams.businessId = state.params.businessId;
        activityParams.loginName =  state.userInfo.userLoginName;
        activityParams.taskId = state.params.taskId;
        queryReturnActivity(activityParams).then((activityRes)=>{
          console.log(activityRes);
          if(activityRes.data&&activityRes.data.length>0){
            //更新当前twfext_tasks 表的BY9和BY10
            var taskParams={
              taskId:state.params.taskId,
              by9:"1",
              by10:activityRes.data[0].ACTIVITYID
            }
            axiosUtil.get('/backend/workFlow/updateTaskType', taskParams)
            let senderId=activityRes.data[0].SENDERID;
            state.apprValue = val;
            state.apprResult = valName;
            let _params = {};
            //下节点办理人
            _params.nextPerformer = senderId;
            _params.strSystemCode = state.strSystemCode;
            _params.apprValue = state.apprValue;
            _params.apprResult = state.apprResult;
            if(_params.apprResult=='同意'){
              _params.apprResult='审核通过'
            }else if(_params.apprResult=='不同意'){
              _params.apprResult='审核驳回'
            }
            _params.apprResult="提交";
            _params.opinion = '';
            _params.taskId = state.params.taskId;
            _params.processId = state.params.processId;
            _params.activityId = state.params.activityId;
            _params.processInstanceName = state.params.processInstanceName;
            _params.processInstanceId = state.params.processInstanceId;
            _params.engineType = state.params.engineType;
            _params.doType = "send";
            _params.businessId = state.params.businessId;
            _params.loginName =  state.userInfo.userLoginName;
            _params.userOrgId =  state.userInfo.orgnaId;
            //控制流程走向指定流程
            _params.conditionStr='sffsdx=0';
            _params.conditionStr=_params.conditionStr+'@lczx='+activityRes.data[0].ACTIVITYID;

            let ld = ElLoading.service({
              target: "#auditDiv",
              text: "正在提交数据，请稍后...",
            });
            axios({
              headers:{
                "Content-Type": "application/x-www-form-urlencoded"
              },
              method: "post",
              url: api.finishTask(),
              data: "varJson=" + JSON.stringify(_params),
            })
                .then((res) => {
                  ld.close();
                  if (res.data&& res.data.result == '1') {
                    ld.close();
                    ElMessage({message: '提交成功！',type: 'success',})
                    context.emit("close");
                  } else {
                    ElMessage({message: res.data.error,type: 'error',})
                  }
                })
                .catch((error) => {
                  console.log(error);
                  ld.close();
                });
          }
        })
      }).catch((error) => {
        console.log(error);
      });
    }

    const queryReturnActivity = async (params) =>{
        let res = await axiosUtil.get('/backend/workFlow/queryReturnActivity', params)
        return res;
    }

    const changeTitle = (title) => {
      state.shTItle=title
    }

    const changeWidth = (value) => {
      state.width=value
    }

    const getActivityInfo = async ()=>{
      let params={
        processId: props.processParams.processId,
        activityId: props.processParams.activityId
      }
      console.log(props.processParams)
      axiosUtil.get('/backend/workFlow/selectActivityInfo', params).then(res=>{
        state.activityInfo=res.data || {}
      })
    }

    onMounted(async () => {
      await loadAuditPage();
      await getActivityInfo();
      await getApproveValue();
    })

    return {
      ...toRefs(state),
      loadAuditPage,
      getApproveValue,
      close,
      closeOpinion,
      onOperate,
      onSave,
      onSubmit,
      addTask,
      finishTask,
      getNextPerformer,
      changeTitle,
      changeWidth,
      onSubmitReturn
    }
  }
})
</script>
<style scoped>
</style>
