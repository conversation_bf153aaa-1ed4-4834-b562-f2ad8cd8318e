<template>
  <el-container>
    <el-main class="vs-workbench-main-scoped">
      <!-- 查询条件区域 -->
      <el-row :gutter="15" style="padding-bottom:12px">
        <el-col :span="20">
          <el-input v-model="zbpjmbmc" placeholder="请输入模板名称" style="width:140px" clearable></el-input>
          <el-button type="primary" style="margin-left:10px" @click="queryData">查询</el-button>
        </el-col>
      </el-row>
      <!--数据表格-->
      <el-row>
        <!-- 表格 -->
        <el-table ref="datatable91634" :data="tableData" height="720px"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" label="序号" width="50" align="center" :index="indexMethod"></el-table-column>
          <el-table-column prop="ZBPJMBMC" label="模板名称" header-align="center"
                           align="left"></el-table-column>
          <el-table-column prop="PSLXMC" label="评审类型" width="180" header-align="center" align="left"></el-table-column>
          <el-table-column prop="CJDWDM" label="明细" header-align="center" align="center" width="90">
            <template #default="scope">
              <el-button @click="viewRow(scope.row)" class="lui-table-button" size="mini">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="90">
            <template #default="scope">
              <el-button @click="selectRow(scope.row)" class="lui-table-button" size="mini">复制</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </el-main>
    <!-- 弹出查看模板具体信息页面 -->
    <el-dialog v-model="dialogFormVisible" title="模板具体信息" top="5vh" width="900px" @close="dialogFormVisible=false"
               :close-on-click-modal="false" append-to-body custom-class="lui-dialog">
      <selectModelDetail :model="model" :ZBPJMBBS="ZBPJMBBS" :key="{ sqid }" @handleClose="handleClose"
                     :sqid="sqid"/>
    </el-dialog>
  </el-container>
</template>
<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
} from 'vue'
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "../../../../lib/axiosUtil";
import comFun from "../../../../lib/comFun";
import selectModelDetail from "./selectModelDetail";

export default defineComponent({
  name: '',
  components: {selectModelDetail},
  props: {
    model: String,
    mbl: String,
  },
  setup(props, {emit}) {
    const state = reactive({
      ifReadonly: "javascript:return true",
      zbpjmbmc: '',
      // 当前页码
      currentPage: 1,
      // 每页的数据条数
      pageSize: 10,
      total: 0,
      //样本类别
      selectmodelTheme: "",
      //弹出框
      dialogFormVisible: false,

      //弹出查看框
      dialogViewFormVisible: false,

      activeIndex: "1",
      //表数据
      tableData: [],
      multipleSelection: [],
      recent: 0,
      form: {},
      zyflJsonArray: [],
      pslxArr: [],
      ZYDM: '',
      PSLXDM: '',
      ZBPJMBBS: '',

      zySelectVisible: false,
      zySelectIsMulti: false,
      zySelectFrom: undefined,

      //-------------评审类型树start----------
      xsTree: [],
      pop: {
        parent: 'PID',
        value: 'ID',
        label: 'TEXT',
        children: 'children',
      },
      //-------------评审类型树end----------
    })
    const selectPbzy = () => {
      state.zySelectIsMulti = false;
      state.zySelectFrom = 'pbzy';
      state.zySelectVisible = true;
    }
    const handleSelectClose = () => {
      state.zySelectVisible = false;

    }
    const callbackSelectCszy = (rows, from) => {
      state.zySelectVisible = false;
      if (from === 'pbzy') {
        state.ZYDM = rows[0].ID;
        state.ZYMC = rows[0].TEXT;
      }
    }
    const callbackClearCszy = (from) => {
      state.zySelectVisible = false;
      if (from === 'pbzy') {
        state.ZYDM = '';
        state.ZYMC = '';
      }
    }

    const getPslxDmData = () => {
      let param = {
        DMLBID: 'PSLX'
      };
      axiosUtil.get('/backend/gcyztb/commons/selectDMBTree', param).then(result=>{
        let dmData = result.data
        let treeData = comFun.transData(dmData, 'ID', 'PID', 'children');
        state.xsTree = treeData
      })

    }
    //选商方式回调
    const handleXsfs = (node) => {
      state.pslxdm = node.ID;
    }
    //-------------评审类型树end----------
    //复制
    const selectRow = (row) => {
      emit('handleClose', row);
    }
    /**
     * 序号
     */
    const indexMethod = (index) => {
      return index + state.pageSize * (state.currentPage - 1) + 1;
    }
    /**
     * 页面数据条数改变时
     */
    const handleSizeChange = (val) => {
      state.currentPage = 1;
      state.pageSize = val;
      queryData();
    }
    /**
     * 翻页
     */
    const handleCurrentChange = (val) => {
      state.currentPage = val;
      queryData();
    }
    /**
     * 每页多少条数据
     */
    const handleSelect = (item) => {
      state.activeIndex = item
      if (item == 2) {
        state.pageSize = 10;
        state.currentPage = 1;
      }
      queryData();
    }
    //专业分类
    const getZyData = () => {
      let param = {zymc: ''};
      // axiosUtil.get('/scgl/dmcommon/queryCszyTreeList', param).then(result=>{
      //   if (result.data.meta.success) {
      //     state.zyflJsonArray = result.data.data.rows
      //   } else {
      //     ElMessage({type: "warning", message: result.data.meta.message});
      //   }
      // })
    }
    const queryData = () => {
      getDataList();
    }
    const getDataList = () => {
      let params = {
        zbpjmbmc: state.zbpjmbmc,
        cszyfldm: state.ZYDM,
        pslxdm: state.PSLXDM,
        sfyx: '1',
      }
      axiosUtil.get('/backend/gcyztb/cgwjgl/selectZbpjmb', params).then(res=>{
        state.tableData = res.data
      })

    }
    //编辑
    const editRow = (row, index) => {
      state.dialogFormVisible = true;
      state.id = row.id


    }
    //新增
    const addData = () => {
      state.dialogFormVisible = true;
      state.id = "";

    }
    //查看
    const viewRow = (row, index) => {
      state.ZBPJMBBS = row.ZBPJMBBS;
      state.dialogFormVisible = true;

    }
    const handleClose = () => {
      state.dialogFormVisible = false;

      queryData();
    }
    const handleViewClose = () => {
      state.dialogViewFormVisible = false;

    }
    //删除
    const deleteRow = (row, index) => {
      ElMessageBox.confirm("确定要删除选择的数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        //删除逻辑...
        ElMessage({
          message: "删除成功",
          type: "success"
        });
      })
          .catch(() => {
            ElMessage({
              type: "info",
              message: "已取消删除"
            });
          });
    }
    //分页多行变少行，点击翻页不刷新问题
    const pageClick = (e) => {
      if (!state.tableData.length) {
        return false;
      }
      let dom = e.target;
      if (
          dom.className === "btn-next" ||
          (dom.className === "el-icon el-icon-arrow-right" &&
              dom.parentNode.className !== "btn-next disabled")
      ) {
        state.currentPage += 1;
        state.currentPage >= Math.ceil(state.total / state.pageSize)
            ? (state.currentPage = Math.ceil(state.total / state.pageSize))
            : state.currentPage;
      } else if (
          dom.className === "btn-prev" ||
          (dom.className === "el-icon el-icon-arrow-left" &&
              dom.parentNode.className !== "btn-prev disabled")
      ) {
        state.currentPage -= 1;
        state.currentPage <= 1 ? (state.currentPage = 1) : state.currentPage;
      } else if (
          dom.className === "el-icon more btn-quicknext el-icon-d-arrow-right"
      ) {
        state.currentPage = Math.ceil(state.total / state.pageSize);
      } else if (
          dom.className === "el-icon more btn-quickprev el-icon-d-arrow-left"
      ) {
        state.currentPage = 1;
      } else if (dom.className === "number") {
        state.currentPage = Number(dom.innerHTML);
      } else {
        return false;
      }
      queryData();
    }

    onMounted(() => {
      if (props.mbl !== null && props.mbl !== '') {
        state.PSLXDM = props.mbl;
      }
      getZyData();
      getPslxDmData();
      queryData();
    })

    return {
      ...toRefs(state),
      selectPbzy,
      handleXsfs,
      queryData,
      indexMethod,
      viewRow,
      selectRow,
      handleClose,
      handleSelectClose,
      callbackSelectCszy,
      callbackClearCszy

    }
  }
})

</script>
<style scoped>
.dialog-footer {
  text-align: center;
}

.el-dialog__body {
  padding-top: 1px;
}

* >>> .el-form-item__content .el-input__inner .el-textarea__inner {
  width: 98%;
}

</style>
