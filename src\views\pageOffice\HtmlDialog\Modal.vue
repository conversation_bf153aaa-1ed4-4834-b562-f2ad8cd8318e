
<script setup>
import { onMounted } from 'vue'

function test(num) {
  pohtmldialog.CallParentJSFunc({
        funcName: "updateCount",
        paramJson: num,
        success: function (strRet) {
          document.getElementById("msg").innerText =
            "success: 父窗口updateCount函数返回值 :" + strRet;
        },
      });
    }

    function  test2() {
      pageofficectrl.word.SetTextToSelection("==来自模态对话框的文本==");
    } 

onMounted(() => {
 //获取父页面pageofficectrl.ShowHtmlModelessDialog()方法第二个参数
 document.getElementById("span1").innerText = window.external.UserParams;

})
</script>
<template>
  <div class="model" style="background-color:#FFE5B4;">
    <h3>这是一个模态网页对话框</h3>
    <p style="color: red">此窗口弹出之后，用户无法操作父窗口。</p>
    <p>
      父窗口执行ShowHtmlModalDialog时传递过来的参数：
      <span
        id="span1"
        style="
          background-color: white;
          font-size: 14px;
          padding: 5px;
          color: green;
          border: dashed 1px blue;
          border-radius: 10px;">
          </span>
    </p>
    <p>点击下面按钮可以与父窗口进行交互操作。</p>

    <ul>
      <li>
        通过CallParentJSFunc调用父窗口js函数updateCount：<br />
        <input type="button" value="Count 加 5" @click="test(5)" />
        <span
          id="msg"
          style="color: darkorange; background-color: white"
        ></span>
      </li>
      <li>
        直接调用父窗口pageofficectrl对象：(<span style="color: red"
          >点击后，注意word内容变化</span
        >)<br />
        <input type="button" value="插入文本到Word" @click="test2()" />
      </li>
    </ul>
    <div style="text-align: center">
      <img src="../HtmlDialog/working2.gif" style="width: 100px" />
    </div>
  </div>
</template>

<style scoped>
h3 {
    display: block;
    font-size: 1.17em;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    font-weight: bold;
}
.model {
  overflow: hidden;
  height: 100%;
}
</style>