<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="140px"
           size="default" v-loading="loading" @submit.prevent>
    <el-row :gutter="0" class="grid-row">
      <el-col :span="8" class="grid-cell">
        <el-form-item label="姓名：" prop="USER_NAME">
          <el-input v-model="formData.USER_NAME" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="账号：" prop="USER_LOGINNAME">
          <el-input v-model="formData.USER_LOGINNAME" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="手机号码：" prop="USER_MOBILE">
          <el-input v-model="formData.USER_MOBILE" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell no-border-bottom">
        <el-form-item label="组织机构：" prop="ORGNA_ID">
          <el-cascader v-model="formData.ORGNA_ID" :options="orgList" filterable
                       :props="{checkStrictly: true,label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false}"
                       clearable />
        </el-form-item>
      </el-col>

      <el-col :span="24" class="grid-cell no-border-bottom">
        <el-form-item label="权限：" prop="roleList">
          <el-select
              v-model="formData.roleList"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择权限">
            <el-option
                v-for="item in roleList"
                :key="item.ROLE_ID"
                :label="item.ROLE_NAME"
                :value="item.ROLE_ID"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell no-border-bottom">
        <el-form-item label="微信号：" >
          <el-input v-model="formData.WECHAT_ACCOUNT" type="text" placeholder="请输入" clearable :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>


      <el-col :span="24" class="grid-cell no-border-bottom">
        <el-form-item label="管理部门：" prop="GLBMList">
          <el-cascader v-model="formData.GLBMList" :options="orgGLBMList" filterable
                       :props="{checkStrictly: true,label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false,multiple: true}"
                       clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
      <el-button type="success" @click="saveData()" v-if="editable">保存</el-button>
      <el-button @click="closeForm">返回</el-button>
    </div>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import {ElMessage} from "element-plus";
import comFun from "../../../lib/comFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      userId: props.params.id,
      formData:{
        USER_NAME: null,
        USER_LOGINNAME:null,
        USER_MOBILE:null,
        ORGNA_ID:null,
        roleList:[],
        GLBMList:[],
      },
      rules: {
        USER_NAME: [{
          required: true,
          message: '字段值不可为空',
        }],
        USER_LOGINNAME: [{
          required: true,
          message: '字段值不可为空',
        }],
        USER_MOBILE: [{
          required: true,
          message: '字段值不可为空',
        }],
        ORGNA_ID: [{
          required: true,
          message: '字段值不可为空',
        }],

      },
      editable: props.params.editable,
      loading: false,
      orgList:[],
      roleList:[],
      orgGLBMList: [],
    })
    const getFormData = () => {
      let params = {
        userId: state.userId
      }
      state.loading = true
      axiosUtil.get('/backend/common/getUserInfoById', params).then((res) => {
        state.formData = res.data
        state.formData.roleList = state.formData.roleList.map(item=>item.ROLE_ID)
        state.formData.GLBMList = state.formData.GLBMList.map(item=>item.ORGNA_ID)
        state.loading = false
      });
    }

    const getOrgList = () => {
      let params={
        //loginName:state.userInfo.userLoginName
      }
      axiosUtil.get('/backend/common/selectTwoOrgList', params).then((res) => {
        let data=res.data.orgList || []
        state.orgList = treeData(data,'ORGNA_ID','PORGNA_ID','children',res.data.root)
      });
    }

    const getOrgGLBMList = () => {
      let params={
        //loginName:state.userInfo.userLoginName
      }
      axiosUtil.get('/backend/common/selectpccbsZzjg', params).then((res) => {
        let data=res.data || []
        state.orgGLBMList = treeData(data,'ORGNA_ID','PORGNA_ID','children','0')
      });
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const submitForm = () => {
      let params
      if(props.params.operation === 'add'){
        params={
          USER_ID: props.params.id,
          ...state.formData,
          ENABLED: '1',
          CREATOR: state.userInfo.userId,
          CREATE_TIME: comFun.getNowTime(),
          SRC_TYPE:'1',
          USER_CODE: state.formData.USER_LOGINNAME,
          ZT: 'add'
        }
      }else {
        params={
          ...state.formData
        }
      }
      console.error(params)
      axiosUtil.post('/backend/common/saveUserInfo', params).then((res) => {
        if(res.data?.BCZT==='1'){
          ElMessage.success('保存成功')
        }else {
          ElMessage.error(res.data.msg ? res.data.msg : '保存失败')
        }
      });
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }
    
    const getRoleList = () => {
      axiosUtil.get('/backend/common/selectRoleList', null).then((res) => {
        state.roleList = res.data
      });
    }
    
    const closeForm = () => {
      emit('close')
    }

    /**
     * 数据转树型结构
     * @param data
     * @param id id名称
     * @param parentId 父id名称
     * @param childName 生成的子节点名称
     * @param root 根节点的值
     * @returns {*}
     */
    const treeData = (data, id, parentId, childName, root) => {
      let cloneData = data
      return cloneData.filter((father) => {
        father[childName] = cloneData.filter((child) => {
          return father[id] === child[parentId]
        })
        return father[parentId] === root
      })
    }

    onMounted(() => {
      getOrgList()
      getOrgGLBMList()
      getRoleList()
      if (props.params.operation !== 'add') {
        getFormData()
      }
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm

    }
  }

})
</script>

<style scoped>

</style>
