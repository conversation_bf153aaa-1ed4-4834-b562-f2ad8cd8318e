

import {customCheck} from './router.auth';

/**
 *  这些路由是不使用vsui.vue@2.1.x框架的路由地址
 * 
 * 
 */
const routesUnUseVsuiVue =  [
  {
    path: "/login",
    name: "login",
    component:()=>import("../../../views/login.vue"),
    meta: { title: "项目开发模式" }
  },
]


/**
 * 这些路由是使用vsui.vue@2.1.x框架的路由地址
 * 
 */
const routesUseVsuiVue = [
  {
    path: "dashboard",
    name: "dashboard",
    component:()=>import("../../../views/dashboard.vue"),
    meta: { title: "项目开发模式11",permission: customCheck }
  },
]


export {
  routesUnUseVsuiVue,
  routesUseVsuiVue
} 