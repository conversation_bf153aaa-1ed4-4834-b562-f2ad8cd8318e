<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="180px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{formData.XMMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{formData.XMBH}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标单位：" prop="SSDWMC">
            <div style="margin-left: 10px">{{formData.SSDWMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标文件获取截止时间：" prop="ZBWJHQSJJS">
            <div style="margin-left: 10px">{{formData.ZBWJHQSJJS}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="招标文件：" prop="ZBWJ">
            <vsfileupload v-show="false"
                style="margin-left: 10px"
                :editable="false"
                v-model:files="fileList"
                :busId="formData.WJID"
                :key="formData.WJID"
                ywlb="ZBWJ"
                busType="ZBWJ"
                :limit="100"
            ></vsfileupload>
            <el-button style="margin-left: 10px" type="primary" @click="dowloadZbwj">下载招标文件</el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";


export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GGID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
      },
      rules: {
      },
      fileList: [],
      DWXX: {}
    })

    const getFormData = () => {
      let params={
        GGID: state.GGID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/zbwjlq/selectLqzbwjById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const dowloadZbwj = () => {
      let params={
        WJLQID: comFun.newId(),
        WJID: state.formData.WJID,
        DWWYBS: state.DWXX.DWWYBS,
        DWMC: state.DWXX.DWMC,
        LQRZH: state.userInfo.userLoginName,
        LQSJ: comFun.getNowTime(),
        CJRZH: state.userInfo.userLoginName,
        CJRXM: state.userInfo.userName,
        CJDWID: state.userInfo.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '1',
        YWZT: '1'
      }
      axiosUtil.post('/backend/xsgl/zbwjlq/saveLqjlxx',params).then(res=>{
        state.fileList.forEach(item=>{
          window.open('/backend/minio/download' + "?id=" + item.mongoDBId)
        })

      })
    }

    const getDwxx = () => {
      state.loading=true
      let params={
        GGID: state.GGID,
        RYZH: state.userInfo.userLoginName,
        RYXM: state.userInfo.userName,
      }

      axiosUtil.get('/backend/xsgl/xstzfb/selectDwxxByGgid',params).then(res=>{
        if(res.data){
          state.DWXX=res.data
          getFormData()
        }else {
          ElMessage.error('获取登陆人队伍信息失败')
        }
      })
    }



    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getDwxx()
    })

    return {
      ...toRefs(state),
      closeForm,
      dowloadZbwj
    }
  }

})
</script>

<style scoped>

</style>
