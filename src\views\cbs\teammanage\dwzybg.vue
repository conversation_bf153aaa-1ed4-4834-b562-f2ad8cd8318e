<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="data.queryForm.qymc" placeholder="请输入企业名称" @keyup.enter="query"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.dwzt" placeholder="请选择单位状态">
            <el-option
                v-for="item in [{label:'正常',value:'ZC'},{label:'暂停',value:'ZT'},{label:'取消',value:'QX'}]"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-button type="primary" @click="query">
          <el-icon><Search/></el-icon>查询
        </el-button>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          height="calc(100vh - 250px)"
          highlight-current-row
          size="default"
          ref="table"
          fit
          border
          :border="false"
          :data="data.tableData"
          v-loading="loading"
      >
        <EleProTableColumn
            v-for="prop in data.tableColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #opration="{row,$index}">
            <div>
              <el-button class="lui-table-button" @click="edit(row,$index)">专业变更</el-button>
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
      <el-pagination
          @size-change="
                    paginationObj.page == 1
                    ? query()
                    : ((paginationObj.page = 1), query())
                "
          @current-change="query"
          v-model:current-page="paginationObj.page"
          v-model:page-size="paginationObj.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="paginationObj.total"
          background
          :pager-count="7">
      </el-pagination>
    </div>
    <el-dialog
        custom-class="lui-dialog"
        v-model="data.saveVisible"
        title="专业变更"
        top="5vh"
        width="80%"
    >
      <div style="height: 100%;overflow: hidden;">
        <xzzy v-if="currentStep == 0" :change="'1'" @nextStep="nextStep" @selectUpdate="setSelect"></xzzy>
        <xzqy v-if="currentStep == 1" :change="'1'" @handleChange="handleChange" @preStep="preStep"
              :selectData="selectData"></xzqy>
      </div>
    </el-dialog>
  </el-form>
</template>
<script setup>
import {
    ref,
    reactive,
    onMounted, watch,
} from "vue";
import {ElMessage, ElMessageBox} from 'element-plus';
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getDwrcglDwzybglb, postDwrcglDwzybg} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam, getCommonSelectDMB} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import {v4 as uuidv4} from "uuid";
import comFun from "@src/lib/comFun";
import xzqy from "@src/views/cbs/cbsyj/xzzy/xzqy.vue";
import xzzy from "@src/views/cbs/cbsyj/xzzy/xzzy.vue";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
let currentStep = ref(0)
const selectData = ref([])
// 翻页对象
const paginationObj = ref({
  page: 1,
  size: 10,
  total: 0,
});
const getFwqys = ()=>{
    getCommonSelectDMB({DMLBID: 'FWQY'}).then(res=>{
        data.fwqys = res.data
    })
}

const setSelect = (data) => {
    selectData.value = data
}

const nextStep = () => {
    currentStep.value = 1
}
const preStep = () => {
    currentStep.value = 0
}

const handleChange = (zys) => {
  console.error(data.fwqys)
  console.log('zys', zys);
    data.saveForm.CJSJ = comFun.getNowTime();
    data.saveForm.CJRXM = data.currentUser.USER_NAME;
    data.saveForm.CJRZH = data.currentUser.USER_LOGINNAME;
    data.saveForm.DWWYBS = uuidv4().replace(/-/g, '')
    data.saveForm.CJDWID = data.currentUser.ORGNA_ID
    let param = {
        ...data.saveForm,
        BGQ: data.saveForm.fwfw,
        BGH: '',
        FW: zys.value?.map(x => {
          let _FWQY=[]
          if(x.FZYBM==='SYGC'){
            x.FWQY.forEach(i=>{
              let _FWQYMC=''
              if(i==='QT') _FWQYMC = x.QTFWQYMC
              else _FWQYMC = data.fwqys.filter(item=>item.DMXX === i)[0].DMMC
              _FWQY.push({
                FWQYWYBS: uuidv4().replace(/-/g, ''),
                FWQYBM: i,
                FWQYMC: _FWQYMC
              })
            })
          }
            return {
                ZYWYBS: x.ZYID,
                ZYMC: x.ZYMC,
                ZYBM: x.ZYBM,
                STATUS: '1',
                CJRXM: data.currentUser.USER_NAME,
                CJRZH: data.currentUser.USER_LOGINNAME,
                CJDWID: data.currentUser.ORGNA_ID,
                FWQY: _FWQY
            }
        })
    };
  console.log(param)
    postDwrcglDwzybg(param).then(res=>{
        ElMessage({
            type: 'success',
            message: '保存成功!'
        });
        data.saveVisible = false;
        currentStep.value = 0
        query();
    })


}

const data = reactive({
    fwqys: [],
    currentTeam: {},
    queryForm: {
        qymc: null,
        dwmc: null,
        dwzt: 'ZC',
    },
    saveForm: {},
    currentUser: {},
    saveVisible: false,
    tableData: [],
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 55,
            align: "center",
            index:(index) => index + paginationObj.value.size * (paginationObj.value.page - 1) + 1
        },
        {
            label: "企业名称",
            prop: "ORGNA_NAME",
            align: "center",
            showOverflowTooltip: true,
        },
        // {
        //     label: "队伍名称",
        //     prop: "DWMC",
        //     align: "center",
        //     // slot: "faultHours",
        // },
        {
            label: "服务范围",
            prop: "fwfw",
            align: "center",
            showOverflowTooltip: true,
        },
        {
            label: "操作",
            align: "center",
            width: 150,
            fixed: "right",
            slot: "opration",
        },
    ],
})
const loading = ref(false);
const query = () => {
    loading.value = true;
    getDwrcglDwzybglb({
        ...data.queryForm,
        ...paginationObj.value
    }).then(({data:res}) => {
        // console.log(res)
        if(!res) return;
        data.tableData = res.list?.map(x => {
            return {
                ...x,
                specials: [],
                specialbk: [],
            }
        }) ?? [];
        paginationObj.value.total = res.total
    })
    .finally(() => {
      loading.value = false;
    });

}

watch(
    () => data.saveVisible,
    (val) => {
        if(!val){
            currentStep.value = 0;
        }

    },
    {
        immediate: true,
    }
);

const edit = (row, index) => {
    data.saveForm = {
        ...row
    }
    data.saveVisible = true;

}
const saveFormRef = ref(null)
const save = () => {
    saveFormRef.value.validate().then(res => {
        let type = data.saveForm.DWWYBS ? 'edit' : 'add'
        if (type == 'add') {
            data.saveForm.CJSJ = comFun.getNowTime();
            data.saveForm.CJRXM = data.currentUser.USER_NAME;
            data.saveForm.CJRZH = data.currentUser.USER_LOGINNAME;
            data.saveForm.DWWYBS = uuidv4().replace(/-/g, '')
            data.saveForm.CJDWID = data.currentUser.ORGNA_ID
        } else {
            data.saveForm.XGSJ = comFun.getNowTime();
            data.saveForm.XGRZH = data.currentUser.USER_LOGINNAME;
        }

        postZjdwglSave(data.saveForm).then(res => {
            ElMessage({
                type: 'success',
                message: '保存成功!'
            });
            query();
            data.saveVisible = false;
        }).catch(e => {
            ElMessage({
                type: 'error',
                message: '保存失败!'
            });
        })
    }).catch(e => {

    })
}

const getUserInfo = () => {
    let user = VSAuth.getAuthInfo().permission
    getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
        data.currentUser = res.data;
    })
}


onMounted(() => {
    getFwqys();
    getUserInfo();
    query()
})
</script>

<style scoped>
.container{
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #FFFFFF;
    overflow: hidden;
}
.header {
    padding: 10px 10px 0;
    height: 50px;
    line-height: 50px;
}

.main {
    /* height: calc(100% - 55px); */
    flex: 1;
    padding: 10px;
    overflow: hidden;
}
.footer{
    height: auto;
    padding: 10px;
}
:deep(.el-radio){
    margin-right: 10px;
}
:deep(.el-form--inline .el-form-item){
    margin-right: 10px;
}
</style>
