<template>
  <div class="context">
    <el-form :model="formData" ref="vForm" class="lui-page" label-position="right"
             label-width="0"
             size="default" v-loading="loading" @submit.prevent>
      <div class="worming-massage">
        <el-icon :size="25" color="#409EFF">
          <InfoFilled/>
        </el-icon>
        <div>基础设置：对查重基础规则进行设置，系统内统一，不允许单个项目配置。</div>
      </div>

<!--      <div class="form-label">-->
<!--        <div class="label">断句分隔符</div>-->
<!--        <el-icon :size="20" color="#409EFF">-->
<!--          <InfoFilled/>-->
<!--        </el-icon>-->
<!--        <div class="label-worming">以分隔符为标识进行断句，作为查重的最小单元。</div>-->
<!--      </div>-->

<!--      <el-form-item label="" prop="DJFGF" size="large" style="margin-top: 10px">-->
<!--        <el-input v-model="formData.DJFGF" type="text" style="width: 800px" placeholder="请输入断句分隔符" clearable>-->
<!--        </el-input>-->
<!--      </el-form-item>-->



      <div class="form-label">
        <div class="label">报告水印</div>
        <el-icon :size="20" color="#409EFF">
          <InfoFilled/>
        </el-icon>
        <div class="label-worming">查重报告下载时，作为背景水印底图。</div>
      </div>
      <div style="margin-top: 10px">
        <vsfileuploadPic :busId="formData.JCSZID"
                         :key="formData.JCSZID" :limit="1"
                         :editable="true" ywlb="bgsy"/>
      </div>

      <div class="bottom-button">
        <el-button type="primary" @click="saveData('save')">保存</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import {InfoFilled} from "@element-plus/icons-vue";
import vsfileuploadPic from "@views/components/vsfileuploadPic";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import comFun from "@lib/comFun";


export default defineComponent({
  name: '',
  components: {vsfileuploadPic, InfoFilled},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      formData: {}
    })

    const getFormData = () => {
      state.loading=true
      axiosUtil.get('/backend/bscc/ccjcsz/selectCcjcsz',null).then(res=>{
        state.formData=res.data
        if(!state.formData.CJRZH){
          state.formData={
            ...state.formData,
            CJRZH: state.formData.userLoginName,
            CJRXM: state.formData.userName,
            CJDWID: state.formData.orgnaId,
            CJDWMC: state.formData.orgnaName,
            CJSJ: comFun.getNowTime(),
          }
        }
        state.loading=false
      })
    }

    const saveData = (type) => {
      let params={
        ...state.formData,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      state.loading=true
      axiosUtil.post('/backend/bscc/ccjcsz/saveJcszInfo',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        state.loading=false
      })
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      getFormData,
      saveData

    }
  }

})
</script>

<style scoped>
.context {
  height: 100%;
  background-color: white;
  padding: 10px 40px 40px;
}

.worming-massage {
  padding: 20px;
  background-color: rgba(42, 187, 249, 0.2);
  display: flex;
  gap: 20px;
  font-size: 18px;
  align-items: center;
  margin-bottom: 30px;
}
.form-label{
  display: flex;
  align-items: center;
}
.form-label .label{
  font-size: 20px;
  width: 200px;
}

.form-label .label-worming{
  margin-left: 20px;
}
.bottom-button{
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 130px;
}
</style>
