<!-- 专家评标统计列表 -->
<template>
  <el-form :model="listQuery" ref="vForm" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="hymc">
          <el-input placeholder="请输入姓名" v-model="listQuery.XM" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell" style="margin-right: 150px">
        <el-form-item label="" prop="hysj">
          <div style="display: flex;gap: 5px">
            <el-date-picker style="width: 150px;" v-model="listQuery.PBKSSJ" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" type="date" placeholder="评标开始时间"/>
            <div>至</div>
            <el-date-picker style="width: 150px;" v-model="listQuery.PBJSSJ" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" type="date" placeholder="评标结束时间"/>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell" style="display: flex;gap: 10px">
        <div class="ml10 static-content-item">
          <el-button type="primary" @click="queryData">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
        </div>
        <div class="ml10 static-content-item">
          <el-button type="primary" @click="queryDialogVisible=true">
            <el-icon>
              <Search/>
            </el-icon>
            高级查询
          </el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="primary" @click="exportExcel"><el-icon><Upload/></el-icon>导出</el-button>
        </div>
      </el-col>
    </el-row>

    <div class="container-wrapper" v-show="true">
      <el-table ref="dataTable" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
        <el-table-column prop="XM" label="姓名" align="center"
                         min-width="100"></el-table-column>
        <el-table-column prop="GZDWMC" label="工作单位" align="center"
                         min-width="150"></el-table-column>
        <el-table-column prop="RKZJBXX.SBZY" :show-overflow-tooltip="true" label="申报专业" align="center"
                         min-width="200"></el-table-column>
        <el-table-column prop="PBCS" label="评标次数" align="center"
                         width="100">
          <template #default="scope">
            <el-button plain size="small" text type="primary"
                       @click="editData(scope.row)">{{ scope.row.PBCS }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="CDZTCS" label="迟到早退次数" align="center"
                         min-width="100"></el-table-column>
        <el-table-column prop="WGCS" label="违规次数" align="center"
                         min-width="100"></el-table-column>
        <el-table-column prop="PFBHGCS" label="评分不合格次数" align="center"
                         min-width="100"></el-table-column>
        <el-table-column prop="TDFYCS" label="态度非优次数" align="center"
                         min-width="100"></el-table-column>
      </el-table>
      <el-pagination background
                     v-model:current-page="listQuery.page"
                     v-model:page-size="listQuery.size" :page-sizes="[10,20,50,100]"
                     layout="total, prev, pager, next, sizes"
                     class="lui-pagination"
                     @size-change="pageOrSizeChange"
                     @current-change="pageOrSizeChange"
                     :total="total">
      </el-pagination>
    </div>
    <!-- 评标统计明细弹出页 -->
    <el-dialog custom-class="lui-dialog" v-if="dialogVisible" v-model="dialogVisible" title="评委评标明细" z-index="1000" width="80%">
      <zjpbtjmxView :params="params" @closeForm="closeForm"/>
    </el-dialog>
    <el-dialog custom-class="lui-dialog" z-index="1000" v-model="queryDialogVisible" title="高级查询条件设置" width="800px" class="dialogClass"
               append-to-body>
      <tcgjcx @executeQuery="executeQuery" />
    </el-dialog>

  </el-form>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted
}
  from 'vue'
// 请求方法 get,post,put,del,downloadFile
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "../../../../lib/vsAuth";
import zjpbtjmxView from "./zjpbtjmxView.vue";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "../../../../lib/comFun";
import Tcgjcx from "../../zjcrk/tcgjcx";
import {Plus, Search, Upload} from "@element-plus/icons-vue";

export default defineComponent({
  name: '',
  components: {Tcgjcx, zjpbtjmxView,Search, Plus, Upload},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      HYBS: '',
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      rules: {},
      xmlbArray: [],
      dialogVisible: false,
      dialogPbVisible: false,
      queryDialogVisible: false,
      params: null,
      seniorQuery:null,
    })

    const getDataList = (value) => {
      let params=value ? value : state.listQuery
      axiosUtil.post(`/backend/zjgl/zjpbtj/selectZjpbxx?page=${state.listQuery.page?state.listQuery.page:''}&size=${state.listQuery.size?state.listQuery.size:''}`, params).then((res) => {
        state.tableData=res.data.list
        state.total = res.data.total
      });
    }
    const delRowData = (HYBS) => {
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {

      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }
    const editData = (row) => {
      if (row) {
        state.params = {editable: false, id: row.ZJBS, operation: 'view',otherParams: state.listQuery}
      }
      state.dialogVisible = true
    }


    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }
    const executeQuery = (value) => {
      if(value){
        state.seniorQuery=value
        getDataList(value)
      }
      state.queryDialogVisible=false
    }
    const pageOrSizeChange=()=>{
      getDataList(state.seniorQuery)
    }
    const queryData = () => {
      state.seniorQuery=null
      getDataList()
    }
    onMounted(async () => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      closeForm,
      delRowData,
      editData,
      queryData,
      executeQuery,
      pageOrSizeChange
    }
  }
})

</script>
<style scoped>
.grid-cell .el-input {
  max-width: 250px;
}
</style>

