<template>
  <div class="zhyy-list-container" style="height: calc(100vh - 200px); overflow: auto">
    <div class="zhyy-list-main">
      <el-row class="zhyy-list-searchArea" style="text-align: left">
        <el-col :span="24">
          <label>模板名称：</label>
          <el-input
            style="width: 300px"
            placeholder="请输入模板名称"
            v-model="form.MBMC"
            clearable
            :readonly="params.readonly"
          >
          </el-input>
          <label>描述：</label>
          <el-input
            style="width: 250px"
            placeholder="请输入模板描述"
            v-model="form.MBMS"
            :readonly="params.readonly"
            clearable
          >
          </el-input>
          <label>模板类型：</label>
          <el-select
            style="width: 110px"
            v-model="form.MBLXBM"
            placeholder="请选择"
            :disabled="params.readonly"
          >
            <el-option
              v-for="item in params.mblxArry"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button
            type="success"
            style="margin-left: 10px; float: right"
            @click="saveZrmb()"
            icon="el-icon-check"
            v-if="params.edit != 'view'"
            >保存</el-button
          >
          <el-button
            type="primary"
            style="margin-left: 10px; float: right"
            @click="addNewRow()"
            icon="el-icon-plus"
            v-if="params.edit != 'view'"
            >新增行</el-button
          >
          <el-button
            type="primary"
            style="margin-left: 10px; float: right"
            @click="refresRow()"
            icon="el-icon-refresh-right"
            v-if="params.edit != 'view'"
            >刷新</el-button
          >
        </el-col>
      </el-row>
      <!--表格数据-->
      <el-row class="zhyy-list-tableArea">
        <!--&lt;!&ndash; 表格 &ndash;&gt;-->
        <el-table
          class="customer-no-border-table"
          :data="tableData"
          :span-method="objectSpanMethod"
          border
          :row-class-name="tableRowClassName"
          width="100%"
          :height="pageHeight"
          @row-click="onSelectOp"
        >
          <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            align="center"
            width="50"
          ></el-table-column>
          <el-table-column
            prop="XXLBMC"
            label="信息类别"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <el-input v-model="scope.row.XXLBMC" v-if="scope.row.show"></el-input>
              <span v-if="!scope.row.show">{{ scope.row.XXLBMC }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="XXX" label="信息项" header-align="center" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.XXXMC" v-if="scope.row.show"></el-input>
              <span v-if="!scope.row.show">{{ scope.row.XXXMC }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="YWYQ"
            label="业务要求"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <el-input v-model="scope.row.YWYQ" v-if="scope.row.show"></el-input>
              <span v-if="!scope.row.show">{{ scope.row.YWYQ }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="LRZLSM"
            label="录入资料说明"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <el-input v-model="scope.row.LRZLSM" v-if="scope.row.show"></el-input>
              <span v-if="!scope.row.show">{{ scope.row.LRZLSM }}</span>
            </template>
          </el-table-column>``
          <el-table-column
            prop="BT"
            label="必填"
            header-align="center"
            align="center"
            width="100"
          >
            <template #default="scope">
              <el-select
                v-model="scope.row.SFBT"
                placeholder="请选择"
                v-if="scope.row.show"
              >
                <el-option
                  v-for="item in params.SfbtArry"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <span v-if="!scope.row.show && scope.row.SFBT == '1'">是</span>
              <span v-if="!scope.row.show && scope.row.SFBT == '0'">否</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="SJMB"
            label="数据模板"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <el-select
                v-model="scope.row.SJMBBM"
                placeholder="请选择"
                v-if="scope.row.show"
              >
                <el-option
                  v-for="item in SjmbArry"
                  :key="item.DMXX"
                  :label="item.DMMC"
                  :value="item.DMXX"
                >
                </el-option>
              </el-select>
              <span v-if="!scope.row.show && scope.row.SJMBBM == 'ZZXX'">资质信息</span>
              <span v-if="!scope.row.show && scope.row.SJMBBM == 'RYXX'">人员能力</span>
              <span v-if="!scope.row.show && scope.row.SJMBBM == 'SBQK'">装备能力</span>
              <span v-if="!scope.row.show && scope.row.SJMBBM == 'YJQK'">服务业绩</span>
              <span v-if="!scope.row.show && scope.row.SJMBBM == 'CDQK'">场地配备</span>
              <span v-if="!scope.row.show && scope.row.SJMBBM == 'QHSEGL'">制度建设</span>
              <span v-if="!scope.row.show && scope.row.SJMBBM == 'AQHB'">安全环保</span>
              <span v-if="!scope.row.show && scope.row.SJMBBM == 'CLXX'">车辆信息</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="SHDWSL"
            label="操作"
            header-align="center"
            align="center"
            v-if="params.edit != 'view'"
          >
            <template #default="scope">
              <el-button
                @click="deleteRow(scope.$index, scope.row)"
                type="text"
                size="small"
              >
                删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { computed, onMounted, reactive, ref, watch } from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import util from "@lib/comFun";

let pageHeight = computed(() => {
  return "calc(100vh - 300px)";
});
let props = defineProps({
  appUser: Object,
  edit: String,
  MBID: String,
});
let params = reactive({
  MBID: "",
  MBMXID: "",
  SjmbArry: [],
  mblxArry: [
    { value: "1", label: "企业" },
    { value: "2", label: "队伍" },
  ],
  SfbtArry: [
    { value: "1", label: "是" },
    { value: "0", label: "否" },
  ],
  ZT: "",
  LX: "WB",
  value: "",
  appUser: "",
  dwzt: "",
  model: "dwjdkh",
  //项目名称/编码
  inputXmmcbm: "",
  //工程类别取值
  inputGclb: "",
  //工程类别
  //项目年度
  xmnd: String(new Date().getFullYear()),
  // 当前页码
  currentPage: 1,
  // 每页的数据条数
  pageSize: 10,
  total: 0,
  recent: 0,
  zylbJsonArray: [],
  //行数据ID
  editRowId: null,
  clickArr: [],
  formLabelWidth: "100px",
  tableStyle: "width:100%;height:calc(100vh - 310px);",
  modelTemesMap: {},
  SJYWBM: "",
  ZYLB: "",
  KHBS: "",
  edit: "",
  khqj: [],
  KHMC: "",
  FPJDX: "",
  FJDLX: "",
  spanArr: [], // 一个空的数组，用于存放每一行记录的合并数
  pos: 0, // spanArr 的索引
  contentSpanArr: [],
  position: 0,
  readonly: false,
});
let tableData = reactive([]);
let multipleSelection = reactive([]);
let form = reactive({
  MBID: "",
  MBMC: "",
  MBMS: "",
  MBLXBM: "",
  MBLXMC: "",
  List: [],
});
let dwkhdialogVisible = ref(false);
let dwEditable = ref(false);

//刷新数据
const refresRow = () => {
  for (let i = 0; i < tableData.length; i++) {
    tableData[i].show = false;
  }
  tableData.sort(sortId);
};
const saveZrmb = () => {
  for (let i = 0; i < tableData.length; i++) {
    tableData[i].show = false;
  }
  for (let i = 0; i < params.mblxArry.length; i++) {
    if (form.MBLXBM == params.mblxArry[i].value) {
      form.MBLXMC = params.mblxArry[i].label;
      break;
    }
  }
  for (let i = 0; i < tableData.length; i++) {
    if (tableData[i].XXLBMC == "" || tableData[i].XXLBMC == null) {
      ElMessage({
        message: `请维护第${i}行的信息类别数据`,
        type: "warning",
      });
      return;
    }
    if (tableData[i].XXXMC == "" || tableData[i].XXXMC == null) {
      ElMessage({
        message: `请维护第${i}行的信息项数据`,
        type: "warning",
      });
      return;
    }
    if (tableData[i].YWYQ == "" || tableData[i].YWYQ == null) {
      ElMessage({
        message: `请维护第${i}行的业务要求数据`,
        type: "warning",
      });
      return;
    }
    if (tableData[i].LRZLSM == "" || tableData[i].LRZLSM == null) {
      ElMessage({
        message: `请维护第${i}行的录入资料说明数据`,
        type: "warning",
      });
      return;
    }
    if (tableData[i].XXLBMC == "" || tableData[i].XXLBMC == null) {
      ElMessage({
        message: `请维护第${i}行的信息类别数据`,
        type: "warning",
      });
      return;
    }
    if (tableData[i].XXLBMC == "" || tableData[i].XXLBMC == null) {
      ElMessage({
        message: `请维护第${i}行的信息类别数据`,
        type: "warning",
      });
      return;
    }
    for (let j = 0; j < params.SjmbArry.length; j++) {
      if (tableData[i].SJMBBM == params.SjmbArry[j].DMXX) {
        tableData[i].SJMBMC = params.SjmbArry[j].DMMC;
        break;
      }
    }
  }
  if (tableData.length === 0) {
    ElMessage({
      message: `请维护相关模板列表后保存`,
      type: "warning",
    });
    return;
  }
  if (form.MBMC == "" || form.MBMC == null) {
    ElMessage({
      message: `请维护模板名称数据！`,
      type: "warning",
    });
    return;
  }
  if (form.MBMS == "" || form.MBMS == null) {
    ElMessage({
      message: `请维护模板描述！`,
      type: "warning",
    });
    return;
  }
  if (form.MBLXBM == "" || form.MBLXBM == null) {
    ElMessage({
      message: `请选择模板类型！`,
      type: "warning",
    });
    return;
  }
  form.CZR = params.appUser.entUserName;
  form.CZRZH = params.appUser.userLoginName;
  form.STATUS = "0";
  tableData.sort(params.sortId);
  form.List = tableData;
  // let result= await util.postJson('/sldwgl/mbgl/saveMbmx', this.form, this.model);
  axiosUtil.post("/sldwgl/mbgl/saveMbmx", form).then((res) => {
    if (res.data.meta.success) {
      ElMessage({
        message: "保存成功！",
        type: "success",
      });
    } else {
      ElMessage({
        message: "保存失败！",
        type: "error",
      });
    }
  });
};
// 因为要合并的行数是不固定的，此函数是实现合并随意行数的功能
const getSpanArr = (data) => {
  params.spanArr = [];
  params.pos = 0;
  for (let i = 0; i < data.length; i++) {
    if (i === 0) {
      // 如果是第一条记录（即索引是0的时候），向数组中加入１
      params.spanArr.push(1);
      params.pos = 0;
    } else {
      if (data[i].XXLBMC === data[i - 1].XXLBMC) {
        tableData[i].XXLBMC = data[i].XXLBMC;
        // 如果itemCode相等就累加，并且push 0
        params.spanArr[params.pos] += 1;
        params.spanArr.push(0);
      } else {
        // 不相等push 1
        params.spanArr.push(1);
        params.pos = i;
      }
    }
  }
};
watch(
  () => tableData,
  (val, oldVal) => {
    getSpanArr(tableData);
  },
  { immediate: true }
);
//合并行
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    return {
        rowspan: 1,
        colspan: 1,
    }
  // columnIndex === xx 找到第xx列，实现合并随机出现的行数
  if (columnIndex === 1) {
    const _row = params.spanArr[rowIndex];
    const _col = _row > 0 ? 1 : 0;
    return {
      rowspan: _row,
      colspan: _col,
    };
  }
};
const tableRowClassName = ({ row, rowIndex }) => {
  row.index = rowIndex;
};
const onSelectOp = (row, column, event) => {
  if (params.edit != "view") {
    for (let i = 0; i < tableData.length; i++) {
      tableData[i].show = false;
    }
    tableData[row.index].show = true;
  }
};
const sortId = (a, b) => {
  return a["XXLBMC"].localeCompare(b["XXLBMC"]);
};
//新增行
const addNewRow = () => {
  for (let i = 0; i < tableData.length; i++) {
    tableData[i].show = false;
  }
  for (let i = 0; i < tableData.length; i++) {
    if (tableData[i].XXLBMC == "" || tableData[i].XXLBMC == null) {
      console.log(12345);
      ElMessage({
        message: `请将第${i + 1}行中的信息类别填写填写后再新增行`,
        type: "warning",
      });
      return;
    }
  }
  //新增行后对数据进行排序
  tableData.sort(params.sortId);
  params.LX = params.LX + "1";
  let list = {
    MBMXID: util.newId(),
    XXLBMC: "",
    XXXMC: "",
    YWYQ: "",
    LRZLSM: "",
    SFBT: "1",
    SJMBMC: "",
    SJMBBM: "",
    isNew: "1",
    show: false,
  };
  tableData.push(list);
};
const deleteRow = (index, row) => {
  if (row.isNew == "1") {
    tableData.splice(index, 1);
  } else {
    // let result= await util.postJson('/sldwgl/mbgl/deleteMxRow', row, this.model);
    // if (result.data.meta.message=='ok'){
    //     this.$message.success("删除成功")
    //     getDataList()
    // }else {
    //     this.$message.error(result.data.meta.message)
    // }
    axiosUtil.post("/sldwgl/mbgl/deleteMxRow", row).then((res) => {
      if (res.data.meta.success) {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        getDataList();
      } else {
        ElMessage({
          message: res.data.meta.message,
          type: "error",
        });
      }
    });
  }
  tableData.sort(params.sortId);
};
const pubRow = (index, row, status) => {
  row.SHZT = status;
  // let result= await util.get('/sldwgl/dwkh/pubRow', row, this.model);
  // if (result.data.meta.message=='ok'){
  //     this.$message.success("操作成功")
  //     this.getDataList()
  // }else {
  //     this.$message.error(result.data.meta.message)
  // }
  axiosUtil.get("/sldwgl/dwkh/pubRow", row).then((res) => {
    if (res.data.meta.success) {
      ElMessage({
        message: "操作成功",
        type: "success",
      });
      getDataList();
    } else {
      ElMessage({
        message: res.data.meta.message,
        type: "error",
      });
    }
  });
};
//关闭对话框
const onClose = () => {
  params.ZYLB = [];
  params.khqj = [];
  (params.SJYWBM = ""), (params.PJDX = ""), (params.JDLX = "");
  getDataList();
  dwkhdialogVisible.value = false;
};
const getAppUser = () => {
  // params.appUser=await util.getAppUser();
  return new Promise((resolve, reject) => {
    resolve();
  });
};
/**
 * 序号
 */
const indexMethod = (index) => {
  return index + params.pageSize * (params.currentPage - 1) + 1;
};
/**
 * 页面数据条数改变时
 */
const handleSizeChange = (val) => {
  params.currentPage = 1;
  params.pageSize = val;
  getDataList();
};
/**
 * 翻页
 */
const handleCurrentChange = (val) => {
  params.currentPage = val;
  getDataList();
};
/**
 * @Params: {{Params}}
 * @Description: 获取数据
 */

const getDataList = () => {
  let par = {
    MBID: this.MBID,
  };
  // let pageData=util.getObjectResult(await util.get('/sldwgl/mbgl/queryMbMx',par,this.model))
  axiosUtil.get("/sldwgl/mbgl/queryMbMx", par).then((res) => {
    if (res.data.meta.success) {
      let pageData = res.data.data;
      form = pageData.MbList[0];
      tableData.length = 0;
      for (let i = 0; i < pageData.MxList.length; i++) {
        let list = {
          MBMXID: "",
          XXLBMC: "",
          XXXMC: "",
          YWYQ: "",
          LRZLSM: "",
          SFBT: "",
          SJMBMC: "",
          SJMBBM: "",
          isNew: "0",
          show: false,
        };
        list.MBMXID = pageData.MxList[i].MBMXID;
        list.XXLBMC = pageData.MxList[i].XXLBMC;
        list.XXXMC = pageData.MxList[i].XXXMC;
        list.YWYQ = pageData.MxList[i].YWYQ;
        list.LRZLSM = pageData.MxList[i].LRZLSM;
        list.SFBT = pageData.MxList[i].SFBT;
        list.SJMBMC = pageData.MxList[i].SJMBMC;
        list.SJMBBM = pageData.MxList[i].SJMBBM;
        tableData.push(list);
      }
    }
  });
};
//分页多行变少行，点击翻页不刷新问题
const pageClick = (e) => {
  if (!tableData.length) {
    return false;
  }
  let dom = e.target;
  if (
    dom.className === "btn-next" ||
    (dom.className === "el-icon el-icon-arrow-right" &&
      dom.parentNode.className !== "btn-next disabled")
  ) {
    params.currentPage += 1;
    params.currentPage >= Math.ceil(params.total / params.pageSize)
      ? (params.currentPage = Math.ceil(params.total / params.pageSize))
      : params.currentPage;
  } else if (
    dom.className === "btn-prev" ||
    (dom.className === "el-icon el-icon-arrow-left" &&
      dom.parentNode.className !== "btn-prev disabled")
  ) {
    params.currentPage -= 1;
    params.currentPage <= 1 ? (params.currentPage = 1) : params.currentPage;
  } else if (dom.className === "el-icon more btn-quicknext el-icon-d-arrow-right") {
    params.currentPage = Math.ceil(params.total / params.pageSize);
  } else if (dom.className === "el-icon more btn-quickprev el-icon-d-arrow-left") {
    params.currentPage = 1;
  } else if (dom.className === "number") {
    params.currentPage = Number(dom.innerHTML);
  } else {
    return false;
  }
  getDataList();
};
const uerySqlbData = () => {
  let param = {
    DMLB: "SJMB",
  };
  // let result = await util.get('/sldwgl/dmcommon/queryCommonMb', param, this.model);
  axiosUtil.get("/sldwgl/dmcommon/queryCommonMb", param).then((res) => {
    if (res.data.meta.success) {
      params.SjmbArry = res.data.data;
    }
  });
};
/**
 * json格式转树状结构
 * @param   {json}      json数据
 * @param   {String}    id的字符串
 * @param   {String}    父id的字符串
 * @param   {String}    children的字符串
 * @return  {Array}     数组
 */
const transData = (a, idStr, pidStr, childrenStr) => {
  let r = [],
    hash = {},
    id = idStr,
    pid = pidStr,
    children = childrenStr,
    i = 0,
    j = 0,
    len = a.length;
  for (; i < len; i++) {
    a[i]["isparent"] = false;
    hash[a[i][id]] = a[i];
  }
  for (; j < len; j++) {
    let aVal = a[j],
      hashVP = hash[aVal[pid]];
    if (hashVP) {
      !hashVP[children] && (hashVP[children] = []);
      hashVP[children].push(aVal);
    } else {
      r.push(aVal);
    }
  }
  return r;
};
const setIsParent = (arr) => {
  for (let j = 0; j < arr.length; j++) {
    if (arr[j].children && arr[j].children.length > 0) {
      arr[j].isparent = true;
      setIsParent(arr[j].children);
    }
  }
};
onMounted(() => {
  if (params.edit != "view") {
    form.MBID = util.newId();
  }
  if (params.edit == "view") {
    params.readonly = true;
    getDataList();
    getAppUser();
  }
  if (params.edit == "edit") {
    getDataList();
    getAppUser();
  }
  // querySqlbData();
});
</script>

<style scoped>
::v-deep .el-cascader__dropdown {
  height: 250px;
}

.dialog-footer {
  text-align: center;
}
.el-cascader-menu__wrap {
  height: 250px;
}

body .el-table th.gutter {
  display: table-cell !important;
}
</style>
