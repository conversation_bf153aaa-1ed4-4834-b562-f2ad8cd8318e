<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row ref="grid71868" :gutter="12">

      </el-row>
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 400px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column label="队伍名称" prop="unitName" show-overflow-tooltip
                               header-align="center" align="left"
                               width="300"></el-table-column>
              <el-table-column label="申请服务范围" prop="sqfwfw" header-align="center"
                               align="left">
                <template v-slot="scope">
                  <div>
                    {{
                      scope.row.specialName
                    }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" header-align="center" align="center" width="100">
                <template v-slot="scope">
                  <div>
                    <el-button class="lui-table-button" @click="handleSelectProfession(scope.row, scope.$index)">
                      选择专业
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>

      <el-row justify="space-evenly" align="center" style="margin-top: 10px;">
        <el-button type="primary" @click="handleSure">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="选择专业及区域"
        @closed="dialogVisible=false"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <xzzyjqy :zyOptions="zyOptions" v-if="dialogVisible" :specialities="params.specials" @submitSpeciality="submitSpeciality"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import xzzyjqy from "@src/views/cbs/cbsyj/xzzyjqy.vue";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,xzzyjqy},
  props: {
    tableData: {
      type: Array,
      default: []
    },
    zyOptions: {
      type: Array,
      default: []
    },
  },
  setup(props, {emit}) {
    const state = reactive({

      params: {},
      dialogVisible: false,
    })


    const handleSelectProfession = (row,index) => {
      state.params=row
      state.dialogVisible=true
    }

    const closeForm = () => {
      emit('close')
    }

    const submitSpeciality = (res) => {
      let r = JSON.parse(JSON.stringify(res));
      let strs = r.map(x => `${x.zymc}，${x.fwqy}`)
      let re = ''
      strs.forEach(x => re += `${x}；`)
      state.params.specials = r;
      state.params.specialbk = r;
      state.params.specialName = re;
      state.dialogVisible=false
    }

    const handleSure = () => {
      let list = props.tableData.filter(x=>x.specials.length == 0)
      if(list.length > 0){
        ElMessage({
          message: '请选择申请服务范围！',
          type: 'warning'
        })
        return false;
      }
      emit('sure',props.tableData)
    }


    onMounted(() => {
    })

    return {
      ...toRefs(state),
      handleSelectProfession,
      closeForm,
      submitSpeciality,
      handleSure


    }
  }

})
</script>

<style scoped>

</style>
