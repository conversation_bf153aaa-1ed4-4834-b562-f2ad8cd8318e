<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">

      <el-tabs type="border-card" v-model="activeTab" @tab-change="getDataList">
        <el-tab-pane label="今日评标" name="JRPB">

        </el-tab-pane>
        <el-tab-pane label="待评标" name="DPB">

        </el-tab-pane>
        <el-tab-pane label="已评标" name="YPB">

        </el-tab-pane>
        <div class="context">
          <div class="context-card" v-for="(item,index) in tableData" :key="index">
            <div class="card-row-title">
              <div class="row-label">招标项目名称：</div>
              <div class="row-info">{{`${item.XMMC}`}}</div>
            </div>
            <div class="card-row">
              <div class="row-label">主办部门：</div>
              <div class="row-info">{{item.SSDWMC}}</div>
            </div>
            <div class="card-row">
              <div class="row-label">招标时间：</div>
              <div class="row-info">{{item.KBSJ}}</div>
            </div>
            <div class="card-row">
              <div class="row-label">招标地点：</div>
              <div class="row-info">{{item.PBDD}}</div>
            </div>

            <div class="card-row">
              <div class="row-label">选商方式：</div>
              <div class="row-info">{{item.XSFSMC}}</div>
            </div>

            <div class="row-button">
              <el-button size="large" link type="primary" @click="openPBDT(item)">
                进入评标大厅
              </el-button>
            </div>

          </div>


        </div>
      </el-tabs>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="会议密码"
        z-index="1000"
        width="500px">
      <div style="padding: 20px">
        请输入会议密码
        <el-input style="margin-top: 20px" size="default" v-model="confirmMM" placeholder="" type="password" show-password></el-input>
        <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
          <el-button size="default" type="primary" @click="validatorHYMM">确定</el-button>
          <el-button size="default" @click="dialogVisible=false">取消</el-button>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount,onUnmounted} from "vue";
import comFun from "@lib/comFun";
import tabFun from "@lib/tabFun";
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "@lib/vsAuth";
import {mixin} from "@core";
import {ElMessage, ElMessageBox} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    role: {
      required: true
    },
    YXLX: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      activeTab: 'JRPB',
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],

      confirmMM: '',
      dialogVisible: false,
      params: {}
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        type: state.activeTab,
        role: props.role,
        YXLX: props.YXLX,
        loginName: vsAuth.getAuthInfo().permission.userLoginName
      }
      axiosUtil.get('/backend/kpbyx/kpbxx/selectPbyxList', params).then((res) => {
        state.tableData = res.data || []
      });
    }

    const openPBDT = (row) => {
      let params={
        id: row.KPBYXID,
        editable: true,
        operation: 'edit',
        role: props.role,
        busType: row.XSFS==='DJTP' ? 'DJTP' : 'ZBFB',
      }


      if(props.YXLX==='KB'){
        params.type='ZBKB'
        tabFun.addTabByRoutePath('开标大厅','/zbxs/kpbdtPage',{params: params},{})
      }else {
        params.type='ZBPB'
        tabFun.addTabByRoutePath('评标大厅','/zbxs/kpbdtPage',{params: params},{})
      }

      // if(!row.KPBYXID){
      //   if(props.role==='YHZ'){
      //     return
      //   }
      //   params={
      //     id: comFun.newId(),
      //     FABDID: row.FABDID,
      //     JLID: row.JLID,
      //     editable: true,
      //     operation: 'add',
      //     role: props.role,
      //     SFLX: props.SFLX
      //   }
      // }else if(row.KPBYXID){
      //   if(props.role==='YHZ' && ['GKZB','YQZB'].includes(row.XSFS)){
      //     state.dialogVisible=true
      //     state.params={
      //       id: row.KPBYXID,
      //       editable: true,
      //       operation: 'edit',
      //       role: props.role,
      //       PBHYBS: row.PBHYBS,
      //       SFLX: props.SFLX
      //     }
      //     return
      //   }else {
      //     params={
      //       id: row.KPBYXID,
      //       editable: true,
      //       operation: 'edit',
      //       role: props.role,
      //       SFLX: props.SFLX
      //     }
      //   }
      // }
      // tabFun.addTabByRoutePath('评标大厅','/zbxs/pbdtPage',{params: params},{})

    }

    const validatorHYMM = () => {
      if(!state.confirmMM){
        ElMessage.error('密码不能为空')
        return
      }
      let params={
        PBHYBS: state.params.PBHYBS,
        MM: state.confirmMM
      }
      axiosUtil.get('/backend/xsgl/pbyx/hymmsz/validatorHYMM', params).then((res) => {
        if(res.data==='1'){
          tabFun.addTabByRoutePath('评标大厅','/zbxs/pbdtPage',{params: state.params},{})
          state.dialogVisible=false
          state.confirmMM=''
        } else {
          ElMessage.error('密码错误')
        }
      })
    }

    const {vsuiEventbus} = mixin();

    onMounted(() => {
      vsuiEventbus.on("reloadYxList", getDataList)
      getDataList()
    })

    onUnmounted(()=>{
      vsuiEventbus.off("reloadYxList", getDataList)
    })

    return {
      ...toRefs(state),
      openPBDT,
      getDataList,
      validatorHYMM

    }
  }

})
</script>

<style scoped>
.context{
  height: calc(100vh - 200px);
  border: 1px solid #c2c5c9;
  overflow: auto;
}
.context-card{
  border-bottom: 1px solid #c2c5c9;
  padding: 10px;
  position: relative;
}
.card-row-title{
  display: flex;
  font-size: 18px;
  margin-bottom: 10px;
  gap: 20px;
}
.card-row{
  display: flex;
  margin-bottom: 10px;
  gap: 20px;

}
.row-label{
  width: 140px;
  text-align: right;
}
.row-button{
  position: absolute;
  right: 10px;
  bottom: 10px;
}
</style>
