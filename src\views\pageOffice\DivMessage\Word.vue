<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus';
import { ElMessageBox } from 'element-plus';

const poHtmlCode = ref('');
const dialogTableVisible = ref(false);

function handleSelect(key, keyPath) {
	pageofficectrl.Enabled = true;
	console.log(key, keyPath);
}

function handleOpen() {
	pageofficectrl.Enabled = false;
	console.log('submenu opened');
}

function handleClose() {
	pageofficectrl.Enabled = true;
	console.log('submenu closed');
}

function show_message() {
	ElMessage('这是一条消息提示')
}

function show_prompt() {
	pageofficectrl.Enabled = false;
	ElMessageBox.prompt('请输入你的邮箱', '输入框', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
	})
		.then(() => {
			// 用户确定操作
			pageofficectrl.Enabled = true;
		})
		.catch(() => {
			// 用户取消操作
			pageofficectrl.Enabled = true;
		});
}

function show_alert() {
	pageofficectrl.Enabled = false;
	ElMessageBox.alert('对话框内容', '信息', {
		confirmButtonText: '确定',
		callback: () => {
			pageofficectrl.Enabled = true;
		},
	});
}

function show_message2() {
	alert('浏览器自身的alert提示消息框！');
}

function show_prompt2() {
	pageofficectrl.Enabled = false;
	ElMessageBox({
		title: '文本输入框',
		message: '<textarea style="height:200px;width:390px;"></textarea>',
		dangerouslyUseHTMLString: true,
		confirmButtonText: '确定',
		callback: () => {
			// 处理回调逻辑
			pageofficectrl.Enabled = true;
			let textareaValue = document.querySelector('textarea').value;
			alert("您输入的内容是：" + textareaValue);
		},
	});
}

function show_confirm() {
	pageofficectrl.Enabled = false;
	if (confirm('浏览器自身的确认框：你确定要执行这个操作吗？')) {
		pageofficectrl.Enabled = true;
	} else {
		pageofficectrl.Enabled = true;
	}
}

function show_confirm2() {
	pageofficectrl.Enabled = false;
	ElMessageBox.confirm('确认此操作吗？', '询问框', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			pageofficectrl.Enabled = true;
		})
		.catch(() => {
			pageofficectrl.Enabled = true;
		});
}

function dialog_open() {
	pageofficectrl.Enabled = false;
	dialogTableVisible.value = true;
}

function dialog_close() {
	pageofficectrl.Enabled = true;
	dialogTableVisible.value = false;
}


function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("弹出对话框", "dialog_open()", 0);
}

function openFile() {
	return request({
		url: '/DivMessage/Word',
		method: 'get',
	})
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit,dialog_open };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<div class="line"></div>
		<el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" menu-trigger="click" @select="handleSelect"
			@open="handleOpen" @close="handleClose" background-color="#1c9df2" text-color="#fff"
			active-text-color="#ffd04b">
			<el-sub-menu index="1">
				<template #title>菜单一</template>
				<el-menu-item index="1-1" @click="show_message()">提示消息框</el-menu-item>
				<el-menu-item index="1-2" @click="show_prompt()">输入框</el-menu-item>
				<el-menu-item index="1-3" @click="show_alert()">信息提示框</el-menu-item>
			</el-sub-menu>
			<el-sub-menu index="2">
				<template #title>菜单二</template>
				<el-menu-item index="2-1" @click="show_message2()">原生JS的alert框</el-menu-item>
				<el-menu-item index="2-2" @click="show_prompt2()">文本输入框</el-menu-item>
				<el-menu-item index="2-3" @click="show_confirm()">原生JS的确认框</el-menu-item>
			</el-sub-menu>
			<el-menu-item index="3" @click="show_confirm2()">询问框</el-menu-item>
			<el-menu-item index="4" @click="dialog_open()">对话框</el-menu-item>
		</el-menu>

		<el-dialog v-model="dialogTableVisible" title="对话框" @close="dialog_close" @open="dialog_open">
			<p>自定义HTML字符串</p>
		</el-dialog>

		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
