<template>
    <div>
        <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
                 size="default">
            <el-row :gutter="24" class="lui-search-form">

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="" prop="RYMC">
                        <el-input ref="input45296" placeholder="请输入人员名称" v-model="listQuery.USER_NAME" type="text" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="" prop="RYMC">
                        <el-input ref="input45296" placeholder="请输入统一账号" v-model="listQuery.USER_LOGINNAME" type="text" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="4" class="grid-cell">
                    <div class="static-content-item" style="display: flex;">
                        <el-button ref="button91277" @click="getDataList" type="primary">
                            查询
                        </el-button>
                        <el-button ref="button91277" @click="saveData" type="primary">
                            确认
                        </el-button>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <div class="container-wrapper">
                        <el-table ref="datatable91634" :data="tableData" height="400px"
                                  :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                                  @selection-change="handleSelectionChange"
                                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                          <el-table-column type="selection" width="55"/>
                            <el-table-column type="index" width="60" label="序号" align="center"/>
                            <el-table-column prop="USER_LOGINNAME" label="统一账号" align="center"
                                             :show-overflow-tooltip="true" min-width="120"></el-table-column>
                            <el-table-column prop="USER_NAME" label="人员名称" align="center"
                                             :show-overflow-tooltip="true" width="130"></el-table-column>
                            <el-table-column prop="ORGNA_NAME" label="单位名称" align="center"
                                             :show-overflow-tooltip="true" min-width="130"></el-table-column>
                        </el-table>
                    </div>
                </el-col>
            </el-row>


        </el-form>

    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import axiosUtil from "@lib/axiosUtil";
    import {ElMessage} from "element-plus";
    import comFun from "@lib/comFun";
    import vsAuth from "@lib/vsAuth";


    export default defineComponent({
        name: '',
        components: {},
        props: {
            params: {
                type: Object,
                required: true
            },
        },
        setup(props, {emit}) {
            const state = reactive({
                listQuery: {
                    USER_NAME: '',
                    USER_LOGINNAME: '',
                    ROLE: props.params.blrRoleCode
                },
                tableData: [],
                total: 0,
                checkList: [],

                PJLBOptions: [],
                ZRZYTree: [],
            })

            const getDataList = () => {
                let params = {
                    ...state.listQuery,
                };
                axiosUtil.get('/backend/common/selectUserByRole', params).then(res => {
                    state.tableData = res.data || [];
                })
            };

            const handleSelectionChange = (value) => {
                state.checkList = value;
            };

            const saveData = () => {
                if (state.tableData.length === 0) {
                    ElMessage.warning('请至少选择一条人员信息');
                    return
                }
                let RYMCS = state.checkList.map((i) => i.USER_NAME).join(",");
                let RYZHS = state.checkList.map((i) => i.USER_LOGINNAME).join(",");

                let params = {
                    RYMCS: RYMCS,
                    RYZHS: RYZHS,
                    checkList: state.checkList
                };
                emit('submit', params)
            };


            const indexMethod = (index) => {
                return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
            };

            const closeForm = () => {
                emit('close')
            };

            onMounted(() => {
                getDataList();
            });

            return {
                ...toRefs(state),
                indexMethod,
                getDataList,
                closeForm,
                saveData,
                handleSelectionChange
            }
        }

    })
</script>

<style scoped>

</style>
