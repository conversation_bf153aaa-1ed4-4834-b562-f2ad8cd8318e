/************************************************************************************************************************************
 *
 *
 *                                                          FBI WARNING
 *
 *      本文件引入内容为全局使用，为提升首屏加载速度，个别页面、组件使用到的组件，库，样式等在各自文件中引入，不要在此文件中引入
 *
 *
 *
 *
 *
 ***************************************************************************************************************************************/

import {
  app,
  router,
  store,
  elementPlus
} from './assets/core/index';
import locale from "element-plus/lib/locale/lang/zh-cn";
import print from 'vue3-print-nb' // 打印插件
app.use(print)
import vue3PreviewImage from 'vue3-preview-image'
import './style/index.scss'
import tooltop from './components/tooltop/directive'
import auth from "./assets/core/auth";
import jsPlumb from 'jsplumb'

import gridLayout from 'vue-grid-layout'

const debounce = (fn, delay) => {
  let timer = null;
  return function () {
    let context = this;
    let args = arguments;
    clearTimeout(timer);
    timer = setTimeout(function () {
      fn.apply(context, args);
    }, delay);
  }
}

const _ResizeObserver = window.ResizeObserver;
window.ResizeObserver = class ResizeObserver extends _ResizeObserver {
  constructor(callback) {
    callback = debounce(callback, 16);
    super(callback);
  }
}


app
  .use(router)
  .use(store)
  .use(vue3PreviewImage)
  .use(gridLayout)
  .use(elementPlus, {
    size: 'small',
    zIndex: 3000,
    locale
  })
  .use(tooltop)
  .use(jsPlumb)
  .mount('#app');

const changeLoginName = () => {
  let dom1 = document.getElementsByClassName("el-dropdown-username")[0];
  if(dom1){
    dom1.firstChild.data = auth.getPermission()?.userName
  }
  let dom2 = document.getElementsByClassName("vsui-header-el-menu-scoped")[0];
  if(dom2){
    dom2.style.maxWidth='calc(100vw - 860px)'
  }
}
const changeLogTitle = () => {
  let dom1 = document.getElementsByClassName("vsui-header-logo")[0];
  if(dom1){
    dom1.style.width='auto'
    dom1.style.minWidth='60px'
  }

  let dom2 = document.getElementsByClassName("lf sysName")[0];
  if(dom2&& dom2.parentNode.tagName==='VSUI-EXTEND-HEADER-LOGO'){
    dom2.innerHTML=`
      <div style="display: flex;align-items: center;">
        <div style="text-align: center;border-right: 2px solid white;padding-right: 10px">
            <div style="font-size: 14px;font-family: 宋体">中国石化</div>
            <div style="font-size: 10px">SINOPEC</div>
        </div>
        <div style="width: 320px;text-align: center">市场管理信息系统</div>
      </div>
    `

  }

}

const changeHeaderIcon = () => {
    let dom1 = document.getElementsByClassName("vsui-header-tools")[0];
    if(dom1){
        const children = dom1.children;
        if (children.length > 0 && children.length!==3) {
            const lastChild = children[children.length - 1];
            const clonedChild = lastChild.cloneNode(true);
            clonedChild.children[0].classList.add('fa-home');
            clonedChild.children[0].classList.remove('fa-refresh');
            clonedChild.addEventListener('click', function() {
                window.open('/WebPortal','_self')
            });
            clonedChild.style.fontSize='24px'
            dom1.appendChild(clonedChild);
        }

        dom1.style.fontSize='20px'
    }
}


const getCurrentUser = ()=>{
        let token;
        const tokenReg = /^K[a-zA-Z0-9]*$/;
        const paramTokenReg = new RegExp('(^|&)token=([^&]*)(&|$)', 'i');
        // 从当前域URL地址或localstorage中获取token，URL中token是最新的
        let regExpMatchArray = window.location.search.substr(1).match(paramTokenReg);
        token = regExpMatchArray != null ? decodeURI(regExpMatchArray[2]) : sessionStorage.getItem('token');
        token = tokenReg.test(token) ? token : '';
    sessionStorage.setItem('token', token);
        let request = new XMLHttpRequest();
        request.open('GET', 'http://gateway.jhof.sinopec.com/kepler/upms/u/users/current', true);

        // 请求头设置请求头key=Authorization,value=${"Bearer " + token}
        request.setRequestHeader('Authorization', 'Bearer ' + token);
        request.send(null);
        request.onreadystatechange = function () {
            // 请求成功返回数据并解析
            if (request.readyState === 4 && request.status === 200) {
                const response = JSON.parse(request.responseText);
                console.log('responseresponseresponseresponseresponseresponseresponse',response)
                //document.getElementById('context').innerText = request.responseText;
            }
            // 未登录时，接口返回401，取401的响应头Redirect-Login-Page,携带原地址访问登录地址
            if (request.readyState === 4 && request.status === 401) {
                window.location.href = request.getResponseHeader('Redirect-Login-Page') + '?redirect=' + window.location.href;
            }
            // 未授权，接口返回403
            if (request.readyState === 4 && request.status === 403) {
                const response = JSON.parse(request.responseText);
                //document.getElementById('context').innerText = response.message;
            }
        }

}
app.mixin({
  mounted(){
    //getCurrentUser();
    changeLoginName()
    changeLogTitle()
      changeHeaderIcon()
  }
})
