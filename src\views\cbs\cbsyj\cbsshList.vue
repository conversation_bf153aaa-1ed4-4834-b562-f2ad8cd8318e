<template>
  <div class="container">

    <div class="out-box-content">
      <el-row class="zhyy-list-searchArea">
        <el-tabs class="db_tab" v-model="state.activeName" @tab-click="handleClick" type="card">
          <el-tab-pane label="待办工作" name="0"></el-tab-pane>
          <el-tab-pane label="已办工作" name="1"></el-tab-pane>
        </el-tabs>
      </el-row>
      <el-row class="zhyy-list-searchArea">
        <el-tabs v-model="state.activeChildName">
          <el-tab-pane
              v-model:taskNum="state.CbsManageLabel"
              :label="'流程审批(' + state.CbsManageLabel + ')'" name="0">
            <template #label>
              <div style="font-size: 24px">流程审批(<span :style="state.CbsManageLabel!==0 ? 'color: red' : ''">{{state.CbsManageLabel}}</span>)</div>
            </template>
            <taskList :activeName="state.activeName" v-model:taskNum="state.CbsManageLabel"/>
          </el-tab-pane>
<!--          <el-tab-pane v-if="!state.isCbs" :label="'注册审核(' + state.RegesterLabelTitle + ')'" name="1">-->
<!--            <template #label>-->
<!--              <div style="font-size: 24px">注册审核(<span :style="state.RegesterLabelTitle!==0 ? 'color: red' : ''">{{state.RegesterLabelTitle}}</span>)</div>-->
<!--            </template>-->
<!--            <zcsh :activeName="state.activeName" v-model:taskNum="state.RegesterLabelTitle"/>-->
<!--          </el-tab-pane>-->




          <el-tab-pane :label="'消息提醒(' + state.XxtxManageLabel + ')'" name="2">
            <template #label>
              <div style="font-size: 24px">消息提醒(<span :style="state.XxtxManageLabel!==0 ? 'color: red' : ''">{{state.XxtxManageLabel}}</span>)</div>
            </template>
            <syxxtxList :activeName="state.activeName" v-model:taskNum="state.XxtxManageLabel"/>
          </el-tab-pane>


          <!-- <el-tab-pane v-if="!state.isCbs"
                       :label="'消息提醒(' + state.XxtxManageLabel + ')'" name="4">
            <template #label>
              <div style="font-size: 24px">消息提醒(<span :style="state.XxtxManageLabel!==0 ? 'color: red' : ''">{{state.XxtxManageLabel}}</span>)</div>
            </template>
            <message :activeName="state.activeName" v-model:taskNum="state.XxtxManageLabel"/>
          </el-tab-pane> -->

          <!--          <el-tab-pane-->
          <!--                       :label="'会议提醒(' + state.HytxManageLabel + ')'" name="5">-->
          <!--            <hytx :activeName="state.activeName" v-model:taskNum="state.HytxManageLabel"/>-->
          <!--            <template #label>-->
          <!--              <div style="font-size: 24px">会议提醒(<span :style="state.HytxManageLabel!==0 ? 'color: red' : ''">{{state.HytxManageLabel}}</span>)</div>-->
          <!--            </template>-->
          <!--          </el-tab-pane>-->
          <!-- <el-tab-pane
            :label="'专家管理(' + state.ZjManageLabel + ')'"
            name="3"
          ></el-tab-pane>
          <el-tab-pane
            :label="'评价管理(' + state.PjManageLabel + ')'"
            name="4"
          ></el-tab-pane> -->
        </el-tabs>
      </el-row>
    </div>
  </div>

  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="false"
      :show-close="false"
      v-if="state.dialogVisible"
      v-model="state.dialogVisible"
      :fullscreen="true"
      title="初始密码修改"
      z-index="1000">
    <div>
      <changePassword @close="state.dialogVisible=false"/>
    </div>
  </el-dialog>
</template>
<script setup>
import outerBox from "@src/components/common/outerBox.vue";
import zcsh from "./cbssh/zcsh.vue";
import cbssh from "./cbssh";
import vsAuth from "@lib/vsAuth";
import syxxtxList from "@views/syxxtx/syxxtxList";
import taskList from "@views/workflow/newWork/taskList";

import {
  vue,
  vsuiapi,
  mixin,
  eventDefine,
  eventBus,
  runtimeCfg,
  axios,
} from "@src/assets/core";
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import axiosUtil from "@src/lib/axiosUtil.js";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { useRouter } from "vue-router";
import changePassword from '../cbszc/changePassword'

const router = useRouter();
const loading = reactive(true);


import component from "../../component";
import tabFun from "../../../lib/tabFun";
const state = reactive({
  isCbs:false,
  MC: "",
  TYPE: "",
  activeName: "0",
  activeChildName: '0',
  RegesterLabelTitle: 0,
  CbsManageLabel: 0,
  ZtbManageLabel: 0,
  ZjManageLabel: 0,
  PjManageLabel: 0,
  XxtxManageLabel: 0,
  HytxManageLabel: 0,
  cpgylManageLabel: 0,

  dialogVisible: false

});

const isCsmm = () => {
  let params={
    userId:vsAuth.getAuthInfo().permission.userId
  }
  axiosUtil.get('/backend/common/selectSfCsmm',params).then(res=>{
    console.error(res)
    if(res.data){
      tabFun.setCurrentTabByPath('/dashboard')
      state.dialogVisible=true
    }
  })
}

onMounted(() => {
  console.log("用户信息",vsAuth.getAuthInfo().permission)
  // isCsmm()
  let roleList=vsAuth.getAuthInfo().permission.roleList;
  if(roleList&&roleList.length>0){
    for(let i=0;i<roleList.length;i++){
      if(roleList[i].roleCode=='SCGL_CBSGL'){
        state.isCbs=true;
        break;
      }
    }
  }
});
const handleClick = () => {};
</script>
<style scoped>
.container{
  height: 100%;
}
.out-box-content{
  height: calc(100% - 55px);
}
.el-tabs{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-tabs__content){
  flex: 1;
}
.el-form-item {
  margin-bottom: unset;
}
:deep(.el-tabs__header) {
  margin-bottom: unset;
}
.zhyy-list-searchArea:last-child {
  min-height: calc(100% - 15px);
  margin: 0;
}
:deep(.el-tab-pane) {
  overflow-y: hidden;
}


:deep(.db_tab .el-tabs__item ){
  border-radius: 8px 8px 0 0;
  border-right: 1px solid var(--el-border-color-light);
  border-left: 1px solid var(--el-border-color-light);
  border-top: 1px solid var(--el-border-color-light);
  margin-right: 10px;
}
:deep(.el-tabs--card>.el-tabs__header .el-tabs__nav){
  border-top: none;
  border-right: none;
  border-right: none;
  border-radius: 8px 0 0 0;
}
:deep(.db_tab .el-tabs__item.is-active){
  background: #2A96F9;
  color: white;
}
</style>
