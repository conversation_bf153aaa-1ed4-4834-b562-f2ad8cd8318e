<template>
  <div class="context">
    <div class="worming-massage">
      <el-icon :size="25" color="#409EFF">
        <InfoFilled/>
      </el-icon>
      <div>{{ params.DMMC + '设置' }}：{{ params.BYZD1 }}</div>
    </div>

    <div class="form-row" v-if="params.DMXX==='GQGX'">
      <div class="form-label">
        <div class="label">股权穿透层数</div>
        <el-icon :size="20" color="#409EFF">
          <InfoFilled/>
        </el-icon>
        <div class="label-worming">股权向上穿透的层级数，最大支持2层。</div>
      </div>

      <el-form-item label="" prop="GQGXCTCS" size="large" style="margin-top: 10px">
        <el-input v-model="modelValue.GQGXCTCS" type="text" style="width: 300px" placeholder="请输入控股认定比例阈值" clearable
                  @input="modelValue.GQGXCTCS=modelValue.GQGXCTCS.replace(/[^\d]/g, '')">
        </el-input>
      </el-form-item>
      <div class="form-label">
        <div class="label">控股认定比例阈值</div>
        <el-icon :size="20" color="#409EFF">
          <InfoFilled/>
        </el-icon>
        <div class="label-worming">当股权比例超过设定阈值时，认定股东对该公司控股关联。</div>
      </div>

      <el-form-item label="" prop="KGRDBLYZ" size="large" style="margin-top: 10px">
        <el-input v-model="modelValue.KGRDBLYZ" type="text" style="width: 300px" placeholder="请输入控股认定比例阈值" clearable
                  @input="modelValue.KGRDBLYZ=modelValue.KGRDBLYZ.replace(/[^\d]/g, '')">
        </el-input>
        <span style="margin-left: 10px">%</span>
      </el-form-item>
    </div>

    <div class="form-row" v-if="params.DMXX==='RZGX'">
      <el-table ref="datatable91634" :data="modelValue[params.DMXX+'List']" height="400px"
                v-show="modelValue[params.DMXX+'List'] && modelValue[params.DMXX+'List'].length>0"
                :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column prop="WD" label="维度" align="center"
                         :show-overflow-tooltip="true" width="200"></el-table-column>
        <el-table-column prop="SFQY" label="是否启用" align="center"
                         :show-overflow-tooltip="true" width="160">
          <template #default="{row}">
            <el-switch size="default" v-model="row.SFQY"
                       active-text="启用" inactive-text="禁用"
                       active-value="1" inactive-value="0"/>
          </template>
        </el-table-column>
        <el-table-column prop="FXNRSM" label="查重内容说明" align="left" header-align="center"
                         :show-overflow-tooltip="true" min-width="260"></el-table-column>

      </el-table>
    </div>


    <div class="form-row" v-if="params.DMXX==='GQRZGX'">
      <div class="form-label">
        <div class="label">股权穿透层数</div>
        <el-icon :size="20" color="#409EFF">
          <InfoFilled/>
        </el-icon>
        <div class="label-worming">依据股权向上穿透的层级数，最大支持1层。</div>
      </div>

      <el-form-item label="" prop="GQRZGXCTCS" size="large" style="margin-top: 10px">
        <el-input v-model="modelValue.GQRZGXCTCS" type="text" style="width: 300px" placeholder="请输入股权穿透层数" clearable
                  @input="modelValue.GQRZGXCTCS=modelValue.GQRZGXCTCS.replace(/[^\d]/g, '')">
        </el-input>
      </el-form-item>
    </div>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {InfoFilled} from "@element-plus/icons-vue";


export default defineComponent({
  name: '',
  components: {InfoFilled},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({})
    onMounted(() => {

    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>
.context {
  height: calc(100vh - 240px);
  overflow: auto;
}

.worming-massage {
  padding: 20px;
  background-color: rgba(42, 187, 249, 0.2);
  display: flex;
  gap: 20px;
  font-size: 18px;
  align-items: center;
  margin-bottom: 30px;
}

.el-switch {
  --el-color-primary: #409EFF;
}

.form-row{
  margin: 50px 0 20px 40px;
}

.form-label{
  display: flex;
  align-items: center;
}
.form-label .label{
  font-size: 20px;
  width: 250px;
}

.form-label .label-worming{
  margin-left: 20px;
}
</style>
