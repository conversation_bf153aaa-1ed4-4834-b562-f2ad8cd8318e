<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue';
import { filemakerctrl, POBrowser } from 'js-pageoffice';

const files = [
	{ id: 1, name: '荣誉证书' },
	{ id: 2, name: '公司工作总结大会的通知' },
	{ id: 3, name: '幻想科技销售合同' },
	{ id: 4, name: '某某科技公司公文' },
];

const titleText = ref('');
const selectedFiles = ref([]);
const isButtonDisabled = ref(false);
const progressBar1Width = ref('0%');
const progressBar1Text = ref('0%');
const progressBar2Width = ref('0%');
const progressBar2Text = ref('0/0');
const errorMsg = ref('');

onMounted(async () => {
	try {
		const response = await request({
			url: '/index',
			method: 'get',
		});
		titleText.value = response;
	} catch (error) {
		console.error('Failed to fetch title:', error);
	}
});

function PrintFiles() {
	if (selectedFiles.value.length === 0) {
		alert('请至少选择一个文件');
		return;
	}
	isButtonDisabled.value = true;//禁用按钮,防止重复点击
	PrintFile(selectedFiles.value, 0);
}

function PrintFile(idArr, index) {
	filemakerctrl.SetPrint(); // 打印
	filemakerctrl.CallFileMaker({
		url: "/FileMakerPrintFiles/filemaker?id=" + idArr[index],
		success: () => {
			console.log("completed successfully.");
			setProgress1(100);
			index++;
			setProgress2(index, idArr.length);
			if (index < idArr.length) {
				PrintFile(idArr, index);
			} else {
				isButtonDisabled.value = false;//所有文件转换完毕后启用按钮
			}
		},
		progress: (pos) => {
			console.log(`running ${pos}%`);
			setProgress1(pos);
		},
		error: (msg) => {
			errorMsg.value = `发生错误:` + msg;
			console.log(`error occurred: ` + msg);
			isButtonDisabled.value = false;//启用按钮
		},
	})
}
function setProgress1(percent) {
	progressBar1Width.value = `${percent}%`;
	progressBar1Text.value = `${percent}%`;
}

function setProgress2(index, count) {
	progressBar2Width.value = `${Math.round((index / count) * 100)}%`;
	progressBar2Text.value = `${index}/${count}`;
}

function openPageOffice(vuePageUrl, param) {
	POBrowser.openWindow(vuePageUrl, 'width=1200px;height=800px;', param);
}

</script>
<template>
	<div class="Word">
		<div style="text-align: center; margin-top: 30px;">
			<h3>演示：批量打印文件</h3>
			<div style="width: 600px; margin: 0 auto; font-size: 14px;">
				<p style="text-align: left;">
					演示内容：<br />
					&nbsp;&nbsp;&nbsp;&nbsp;本示例以Word为例，演示了批量打印文件的效果。选择文件后，点击“批量打印”按钮。
				</p>
			</div>

			<hr />
			<ul class="company-list">
				<li v-for="(file) in files" :key="file.id">
					<label>
						<input v-model="selectedFiles" :value="file.id" type="checkbox" /> {{ file.name }}
					</label>
					<a href="#" @click.prevent="openPageOffice('Preview', file.id)">预览</a>
				</li>
			</ul>

			<input id="Button1" type="button" value="批量打印" @click="PrintFiles" :disabled="isButtonDisabled" /><br />

			<div id="progressDiv">
				单文件进度：
				<div class="progressBarContainer">
					<div
						:style="{ width: progressBar1Width, height: '20px', backgroundColor: '#1A73E8', borderRadius: '5px', textAlign: 'center', lineHeight: '20px', color: 'white' }">
						{{ progressBar1Text }}
					</div>
				</div>
				整体进度：
				<div class="progressBarContainer">
					<div
						:style="{ width: progressBar2Width, height: '20px', backgroundColor: '#1A73E8', borderRadius: '5px', textAlign: 'center', lineHeight: '20px', color: 'white' }">
						{{ progressBar2Text }}
					</div>
				</div>
				<div id="errorMsg">{{ errorMsg }}</div>
			</div>
		</div>
	</div>
</template>
<style scoped>
.progressBarContainer {
	width: 100%;
	background-color: #eee;
	border-radius: 5px;
	padding: 3px;
	box-shadow: 2px 2px 3px 3px #ccc inset;
}

.progressBar {
	height: 20px;
	width: 0%;
	background-color: #1A73E8;
	border-radius: 5px;
	text-align: center;
	line-height: 20px;
	/* 使文字垂直居中 */
	color: white;
}

#progressDiv {
	width: 400px;
	margin: 10px auto;
	text-align: left;
	font-size: 14px;
	border: solid 1px #1A73E8;
	padding: 10px 20px;
	color: #1A73E8;
}

#errorMsg {
	color: red;
}

/* 样式定义 */
.company-list {
	list-style-type: none;
	padding: 0;
	margin: 0 auto;
	width: 400px;
}

.company-list li {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
}

.company-list label {
	display: block;
	font-weight: bold;
	margin-bottom: 5px;
}

.company-list input[type="checkbox"] {
	margin-right: 5px;
}
</style>
