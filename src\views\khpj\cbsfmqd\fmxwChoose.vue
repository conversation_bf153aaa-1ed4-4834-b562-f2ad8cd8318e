<template>
  <div>
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="LBBM">
            <el-select v-model="listQuery.LBBM" class="full-width-input"
                       placeholder="请选择类别名称"
                       clearable>
              <el-option v-for="(item, index) in FMLBOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="FMQDXW">
            <el-select v-model="listQuery.FMQDXW" class="full-width-input"
                       placeholder="请选择负面行为"
                       clearable>
              <el-option v-for="(item, index) in FMQDXWOptions" :key="index" :label="item.DMMC"
                         :value="item.DMMC" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="400px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      @selection-change="handleSelectionChange"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="selection" width="55"/>
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="LBMC" label="类别名称" align="center"
                               :show-overflow-tooltip="true" width="180">
              </el-table-column>
              <el-table-column prop="FMQDXW" label="负面清单行为" align="center"
                               :show-overflow-tooltip="true" min-width="180">
              </el-table-column>
              <el-table-column prop="CLFS" label="处理方式" align="center"
                               :show-overflow-tooltip="true" width="250">
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      listQuery:{
      },
      tableData: [],
      checkList: [],

      FMLBOptions: [],
      FMQDXWOptions: [],
    })

    const getDataList = () => {
      let params={
        ...state.listQuery,
        CLZY: props.params.CLZYList
      }
      axiosUtil.get('/backend/sckhpj/cbsfmqd/selectFmqdByZrzyList',params).then(res=>{
        state.tableData = res.data || []
      })

    }

    const saveData = () => {
      if(state.checkList.length===0){
        ElMessage.warning('请选择负面行为')
        return
      }
      emit('submit',state.checkList)

    }

    const handleSelectionChange = (value) => {
      state.checkList=value
    }




    const closeForm = () => {
      emit('close')
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
      })
    }

    onMounted(() => {
      getDataList()
      getDMBData('FMLB', 'FMLBOptions')
      getDMBData('FMQDXW', 'FMQDXWOptions')
    })

    return {
      ...toRefs(state),
      getDataList,
      closeForm,
      saveData,
      handleSelectionChange

    }
  }

})
</script>

<style scoped>

</style>
