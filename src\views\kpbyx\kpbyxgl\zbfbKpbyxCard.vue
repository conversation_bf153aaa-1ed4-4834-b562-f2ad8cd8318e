<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <div style="display: flex;align-items: center;border-bottom: 1px solid #d5d9e0;margin-bottom: 10px">
        <div style="width: 5px;height: 5px;background-color: #2A96F9;border-radius: 5px"></div>
        <div style="color: black;font-weight: bold;margin-left: 10px">基本信息</div>
      </div>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <el-input v-model="formData.XMBH" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16" class="grid-cell">
          <el-form-item label="资金来源：" prop="ZJLYMC">
            <el-input v-model="formData.ZJLYMC" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="主办部门：" prop="SSDWMC">
            <el-input v-model="formData.SSDWMC" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16" class="grid-cell">
          <el-form-item label="项目类别：" prop="XMLB">
            <el-cascader v-model="formData.XMLB" :options="ZRZYTree" filterable :disabled="true"
                         :props="{label:'ZYMC',value:'ZYBM',emitPath: false}"
                         clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标时间：" prop="KBSJ">
            <el-date-picker
                v-model="formData.KBSJ"
                :disabled="true"
                type="datetime"
                clearable
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>

        </el-col>
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标地点：" prop="PBDD">
            <el-input v-model="formData.PBDD" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="评标规则设置：" prop="PBGZ">
            <div style="margin-left: 10px">
              <el-button size="small" link type="primary"
                         @click="workEdit(formData,'PBGZ','PBGZ')">【未完成】
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>


      <div v-for="(item,index) in stepTypes" :key="index" style="margin-top: 20px">
        <div style="display: flex;align-items: center;border-bottom: 1px solid #d5d9e0;margin-bottom: 20px">
          <div style="width: 5px;height: 5px;background-color: #2A96F9;border-radius: 5px"></div>
          <div style="color: black;font-weight: bold;margin-left: 10px">{{ item.type }}</div>
        </div>
        <el-row :gutter="0" class="grid-row">
          <el-col :span="12" class="grid-cell" v-for="(ii,dex) in stepTypes[index].steps" :key="dex">
            <el-form-item :label="`${ii.label}：`" prop="XMMC">
              <el-button size="small" link type="primary" @click="workEdit(formData,item.key,ii.key)">
                【{{ getButtonLabel(ii, index) }}】
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </div>


    </el-form>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import tabFun from "@lib/tabFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      rules: {},
      ZRZYTree: [],
      stepTypes: [
        {
          type: '开标',
          key: 'KP',
          steps: [
            {label: '投标单位签到', key: 'TBRQD', index: 0},
            {label: '标书递交', key: 'BSJM', index: 1},
            {label: '唱标顺序', key: 'CBSX', index: 2},
            {label: '开标记录情况', key: 'KBJLQK', index: 3},
            {label: '标底或最高限价', key: 'BDHZGXJ', index: 4},
          ]
        },
        {
          type: '评标',
          key: 'PB',
          steps: [
            {label: '设置评标会密码', key: 'SZPBHMM', index: 5},
            {label: '评标小组签到', key: 'PWQD', index: 6},
            {label: '设置评委主任', key: 'SZWPJRZ', index: 7},
            {label: '设置资格初审', key: 'ZGCS', index: 8},
            {label: '技术分评分明细', key: 'JSFPFMX', index: 9},
            {label: '技术分评分统计', key: 'JSFPFHZ', index: 10},
            {label: '综合分评分明细', key: 'ZHFPFMX', index: 11},
            {label: '综合分评分统计', key: 'ZHFPFHZ', index: 12},
            {label: '单项工程计算明细', key: 'DXGCMX', index: 13},
            {label: '评标汇总', key: 'PBHZ', index: 14},
            {label: '投井表', key: 'TJB', index: 15},
          ]
        },
        {
          type: '定中标',
          key: 'DZB',
          steps: [
            {label: '选、弃标', key: 'XQB', index: 16},
            {label: '评标报告', key: 'PBBG', index: 17},
            {label: '投标单位意见', key: 'TBDWYJ', index: 18},
            {label: '中标结果维护', key: 'ZBZGHWH', index: 19},
            {label: '招标结果表', key: 'ZBJGB', index: 20},
          ]
        },
        {
          type: '其它',
          key: 'QT',
          steps: [
            {label: '评委考核', key: 'PWKH', index: 21},
            {label: '承包商违规维护', key: 'CBSKH', index: 22},
          ]
        }
      ]
    })

    const getZrzyList = () => {
      let params = {}
      axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
        state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0')
      });
    }

    const getButtonLabel = (item,index) => {
      return '未完成'
    }

    const workEdit = (row, stepType,busType) => {
      console.log('workEdit',row, stepType,busType)
      let params={
        id: row.KPBYXID || comFun.newId(),
        JLID: row.JLID,
        operation: row.KPBYXID ? 'edit' : 'add',
        role: 'ZCR',
        busType: busType,
      }
      if(stepType==='KP'){
        params.type='ZBKB'
        tabFun.addTabByRoutePath('开标大厅','/zbxs/kpbdtPage',{params: params},{})
      }else {
        params.type='ZBPB'
        tabFun.addTabByRoutePath('评标大厅','/zbxs/kpbdtPage',{params: params},{})
      }

    }

    const initSteps = () => {
      /**
       * TODO: 测试固定报价，后面删除
       */
      props.formData.BJFS='GDBJ'
      if(props.formData.BJFS==='GDBJ'){
        state.stepTypes[1].steps=state.stepTypes[1].steps.filter(item=>!['PBHZ','DXGCMX'].includes(item.key))
      }else {
        state.stepTypes[1].steps=state.stepTypes[1].steps.filter(item=>item.key!=='TJB')
      }
      let i=0
      for(let item of state.stepTypes){
        for(let ii of item.steps){
          ii.index=i
          i++
        }
      }
    }

    onMounted(() => {
      initSteps()
      getZrzyList()
    })

    return {
      ...toRefs(state),
      workEdit,
      getButtonLabel

    }
  }

})
</script>

<style scoped>

</style>
