<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Save() {
  pageofficectrl.SaveFilePage="/TaoHong/save?fileName=test.docx";
  pageofficectrl.WebSave();
}

function IsFullScreen() {
  pageofficectrl.FullScreen = !pageofficectrl.FullScreen;
}

function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
  pageofficectrl.AddCustomToolButton("保存", "Save", 1);
  pageofficectrl.AddCustomToolButton("全屏/还原", "IsFullScreen", 4);
}

function openFile() {
  return request({
    url: '/TaoHong/Word',
    method: 'get',
  })
}
onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit, Save, IsFullScreen };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <div class="Word">
    <div class="flow4">
      <span style="width: 100px"> </span><strong>文档主题：</strong>
      <span style="color: Red">测试文件</span>
    </div>
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:870px;" v-html="poHtmlCode"></div>
  </div>
</template>
