<template>
  <div v-loading="loading">
    <el-input style="margin-top: 20px" v-model="shyj" type="textarea" :rows="5" placeholder="请输入审核意见"></el-input>
    <div style="display: flex;align-items: center;margin-top: 10px" v-if="apprValue !== '0'&&showFgld">
      <div style="width: 150px;flex-shrink: 0;text-align: right">油田分管领导：</div>
      <el-select
          v-model="formData.BLRList[0].VALUE"
          multiple
          collapse-tags
          collapse-tags-tooltip
          filterable
          placeholder="请选择"
          style="width: 100%">
        <el-option
            v-for="item in YTFGLDOptions"
            :key="item.USER_LOGINNAME"
            :label="`${item.USER_NAME}（${item.ORGNA_NAME}）`"
            :value="item.USER_LOGINNAME"/>
      </el-select>
    </div>






    <div style="text-align: center; height: 40px; padding: 10px">
      <el-button type="primary" @click="onConfirm">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import {ElMessage} from "element-plus";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {},
  props: {
    apprValue: String,
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      shyj: "",
      sffsdx: '1',
      showFgld:true,
      nextPerformer: [],
      fileList: [],
      loading: false,

      formData: {
        BLRList: [
          {VALUE: [], TYPE: 'GR', ACTIVITYID: '12', LABEL: '油田分管领导'},
        ]
      },

      YTFGLDOptions: [],


    })


    const onConfirm = () => {
      if(state.showFgld){
          let nullRow=state.formData.BLRList.find(item=>!item.VALUE || item.VALUE.length===0)
          if (nullRow) {
            ElMessage.warning('请选择'+nullRow.LABEL)
            return
          }
          saveBlrInfo().then(res=>{
            emit("confirm", state.shyj, state.sffsdx, state.nextPerformer)
          })
      }else{
          emit("confirm", state.shyj, state.sffsdx)
      }
    }

    const saveBlrInfo = () => {
      state.loading=true
      let params={
        BLRList: state.formData.BLRList.map((item,index)=>{
          return{
            ...item,
            BUSINESSID: props.params.businessId,
            PROCESSID: props.params.processId,
            TASKID: props.params.taskId,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
            XGRZH: state.userInfo.userLoginName,
            XGSJ: comFun.getNowTime(),
            NAME: item.VALUE.map(ii=>{
              return state.YTFGLDOptions.find(iii=>iii.USER_LOGINNAME===ii)?.USER_NAME
            }).join(','),
            VALUE: item.VALUE.join(',')
          }
        })
      }
      return axiosUtil.post('/backend/common/saveLcblr',params)
    }

    const getBlrOptions = (optionName,ROLE) => {
      let params={
        ROLE: ROLE
      }
      axiosUtil.get('/backend/common/selectUserByRole',params).then(res=>{
        state[optionName]=res.data || []
      })
    }

    const getSfFgld = () => {
      let params={
        businessId:props.params.businessId,
        activityId:'11',
        processId:props.params.processId
      }
      axiosUtil.get('/backend/xsgl/xssqgl/selectSffgld',params).then(res=>{
        if(res.data=='16'){
          state.showFgld=false;
        }else{
          state.showFgld=true;
        }
      })
    }

    const onCancel = () => {
      emit("close")
    }

    onMounted(() => {
      getBlrOptions('YTFGLDOptions','XSGL_YTFGLD');
      getSfFgld();
      if (props.apprValue !== '0') {
        state.shyj = '同意'
      }
      emit('changeTitle', '提交审批')
      emit('changeWidth',600)

    })

    return {
      ...toRefs(state),
      onCancel,
      onConfirm,
      getSfFgld

    }
  }

})
</script>

<style scoped>

</style>
