<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="BT">
            <el-input ref="input45296" placeholder="请输入标题" v-model="listQuery.BT" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="CJRQKS">
            <el-date-picker
                v-model="listQuery.CJRQKS"
                type="date"
                style="width: 100%"
                value-format="YYYY-MM-DD"
                placeholder="分析开始日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="CJRQJS">
            <el-date-picker
                v-model="listQuery.CJRQJS"
                type="date"
                style="width: 100%"
                value-format="YYYY-MM-DD"
                placeholder="分析结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

<!--        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CJR">
            <el-input ref="input45296" placeholder="请输入操作人" v-model="listQuery.CJR" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>-->

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 280px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="BT" label="标题" align="center" header-align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="QYSL" label="公司数量" align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="CJSJ" label="分析时间" align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="CJRXM" label="操作人" align="center"
                               :show-overflow-tooltip="true" ></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="股权穿透新增"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1400px">
      <div>
        <gqctglEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import gqctglEdit from "@views/gqct/gqctgl/gqctglEdit";
import vsAuth from "../../../lib/vsAuth";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,gqctglEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        FXLX:'FXM',
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
      }
      axiosUtil.get('/backend/gqct/gqctgl/selectGqctglPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.params = {editable: true, id: row.GQCTJLID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.params = {editable: false, id: row.GQCTJLID, operation: 'view'}
      state.dialogVisible = true
    }

    const deleteRow = (row) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.del('/backend/bscc/bsccgl/delXmccwhgl?GQCTJLID=' + row.GQCTJLID, null).then(res => {
          ElMessage.success({
            message: '删除成功!'
          });
          getDataList()
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      addData,
      viewRow,
      closeForm,
      deleteRow,
    }
  }

})
</script>

<style scoped>

</style>
