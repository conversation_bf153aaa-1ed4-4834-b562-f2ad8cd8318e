<!-- 待办、已办列表 -->
<template>
  <div id="taskDiv" style="height:100%">
    <div style="height: 45px;margin-top:10px;">
      <el-form :inline="true" :model="formData">
        <el-form-item label="任务名称：">
          <el-input v-model="formData.ssummary" placeholder="请输入任务名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table style="height:calc(100% - 90px);" :data="tableData">
      <el-table-column type="index" :index="indexMethod" width="50" label="序号" align="center">
      </el-table-column>
      <el-table-column prop="PROCESSINSTANCENAME" label="任务名称" header-align="center" align="left">
        <template #default="scope">
          <el-button type="text" @click="onAudit(scope.row)">
            {{ scope.row.PROCESSINSTANCENAME }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="业务环节" prop="TASKNAME" header-align="center" align="left" width="250">
            <template #default="scope">
                {{ scope.row.BUSINESSNAME }}
            </template>
      </el-table-column>
      <el-table-column label="发送人" prop="SENDERNAME" header-align="center" align="center"></el-table-column>
      <el-table-column label="接收时间" prop="RECEIVERTIME" header-align="center" align="center" width="150"></el-table-column>
      <el-table-column label="操作" header-align="center" align="center" width="180">
        <template #default="scope">
          <el-button type="text" @click="onMonitor(scope.row)">[跟踪]</el-button>
          <el-button type="text" v-if="status==3" @click="recall(scope.row)">[撤回]</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :current-page="pageNum" :page-size="pageSize" :total="totalPage" :page-sizes="[10,20,30,40,50]" layout="total, sizes, prev, pager, next, jumper" style="float: left"></el-pagination>
    <el-dialog z-index="1000" :title="dialogTitle" v-model="showDialog" width="80%" top="35px" @close="onAuditClose" :close-on-click-modal="false">
      <audit-frame v-if="showDialog && lx == 'audit'" :model="model" :processParams="queryParams" :businessParams="otherParams" @close="showDialog = false"></audit-frame>
      <monitor-form v-if="showDialog && lx == 'monitor'" :model="model" :queryParams="queryParams"></monitor-form>
    </el-dialog>
  </div>
</template>
<script>
import {
    defineComponent,
    toRefs,
    reactive,
    nextTick,
    ref,
    getCurrentInstance,
    onMounted
}
    from 'vue'
import api from "../../api/lc";
import AuditFrame from "./AuditFrame.vue";
import MonitorForm from "./MonitorForm.vue";
import { ElLoading,ElMessageBox,ElMessage } from "element-plus";
import axios from "axios";
import vsAuth from "@lib/vsAuth";



export default defineComponent({
    name: 'TaskList',
    components: { AuditFrame, MonitorForm},
    props: {
      status: String,
      workflowid:String,//流程ID
      dbyb:String,
    },
    setup(props, { emit }) {
        const state = reactive({
            userInfo: vsAuth.getAuthInfo().permission,
            formData: {
              loginName: "",
            },
            status:props.status,
            pageSize: 10,
            pageNum: 1,
            totalPage: 0,
            tableHeight: "100%",
            tableData: [],
            showDialog: false,
            dialogTitle: "",
            queryParams: {},
            otherParams:{},
            lx: "",
        })
      const  onSearch=()=> {
          let ld = ElLoading.service({target: "#taskDiv", text: "正在加载数据，请稍后...",});
          let queryParams = {
            ...state.formData,
            page: state.pageNum,
            rows: state.pageSize,
            workflowid:state.workflowid,
          };
          axios({
            method: "post",
            url: api.getTasksList(),
            data: "varJson=" + JSON.stringify(queryParams),
          })
            .then((res) => {
              state.totalPage = res.data.data.total;
              state.tableData = res.data.data.rows;
              ld.close();
            })
            .catch((error) => {
              ld.close();
            });
        }

        const onSizeChange=(val)=> {
            state.pageSize = val;
            onSearch();
        }
        const onCurrentChange=(val)=> {
          state.pageNum = val;
          onSearch();
        }
        const onMonitor = (row) => {
          state.showDialog = false;
          nextTick(() => {
            state.lx = "monitor";
            state.dialogTitle = row.PROCESSINSTANCENAME + "-业务跟踪";
            state.queryParams = {
              processId: row.WORKFLOWID,
              processInstanceId: row.WORKFLOWINSTANCEID,
              id: row.id,
            };
            state.showDialog = true;
          });
        }

        const onAudit=(row) =>{
          state.showDialog = false;
          nextTick(() => {
            state.lx = "audit";
            if (state.formData.status == "1") {
              state.dialogTitle = row.PROCESSINSTANCENAME + "-办理";
            } else {
              state.dialogTitle = row.PROCESSINSTANCENAME + "-查看";
            }
            state.queryParams = {
              processId: row.WORKFLOWID,
              activityId: row.ACTIVITYID,
              engineType: row.ENGINETYPE,
              type: row.MKID,
              taskId: row.TASKID,
              processInstanceId: row.WORKFLOWINSTANCEID,
              businessId: row.ID,
              Processversion: row.PROCESSVERSION,
              processInstanceName: row.PROCESSINSTANCENAME,
              status: state.status,
              byzd10:row.BYZD10   //该参数配置的是角色代码，用于查询下节点办理人
            };
            var pageFlag='SH';
            var editable=false;
            if(row.ACTIVITYID=='1'&&state.formData.status == "1"){
                pageFlag='edit';
                editable=true;
            }
            state.otherParams={
                id:row.ID,
                editable:editable,
                operation:pageFlag,
            }
            state.showDialog = true;
          });
        }

        const recall = (row)=>{//撤回
          state.queryParams = {
            processId: row.WORKFLOWID,
            activityId: row.ACTIVITYID,
            engineType: row.ENGINETYPE,
            type: row.MKID,
            taskId: row.TASKID,
            processInstanceId: row.WORKFLOWINSTANCEID,
            businessId: row.ID,
            Processversion: row.PROCESSVERSION,
            processInstanceName: row.PROCESSINSTANCENAME,
            status: state.status,
            byzd10:row.BYZD10   //该参数配置的是角色代码，用于查询下节点办理人
          };
          ElMessageBox.confirm('确定撤回该流程?','提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
          }).then(() => {
            axios({
              method: "post",
              url: api.repealTask(),
              data: "varJson=" + JSON.stringify(state.queryParams),
            })
                    .then((res) => {
                      console.log(res)
                      if(res.status=='200'&&res.data.result=='1'){
                          ElMessage({ message: '撤回成功!', type: 'success',});
                          onSearch();
                      }else{
                          ElMessage({ message: result.data.info, type: 'error',});
                      }
                    })
                    .catch((error) => {
                        ElMessage({ message: '撤回失败！', type: 'warning',})
                    });
          }).catch(() => {
            ElMessage({ message: '已取消', type: 'info',})
          });
        }
          /**
       * 审核后刷新数据
       */
        const onAuditClose=()=> {
          if (state.lx == "audit" && state.status != "3") {
            setTimeout(()=>{
                onSearch();
            },2000)
          }
        }

        const indexMethod = (index) =>{
            return (state.pageNum-1) * state.pageSize + index + 1
        }

        onMounted(async () => {
            state.formData.loginName = state.userInfo.userLoginName;
            state.formData.status = props.status;
            onSearch();
        })

        return {
            ...toRefs(state),
            indexMethod,
            onSearch,
            onAuditClose,
            recall,
            onAudit,
            onSizeChange,
            onCurrentChange,
            onMonitor
        }
    }
})
</script>
<style scoped>
.root >>> .el-dialog__body {
  padding: 10px;
}
</style>
