<!-- 承包商基本信息 -->
<template>
    <div id="auditDiv" class="container" v-loading="loading">
        <el-tabs
                v-model="activeName"
                type="card"
                :before-leave="handleClick"
                style="height: calc(100% - 50px)"
        >
            <el-tab-pane
                    v-for="(item, index) in compList"
                    :key="index"
                    :label="item.SJMBMC"
                    :name="item.SJMBBM"
            >
              <template #label v-if="item.SJMBBM==='QYFXXX'">
                <div style="color: #F56C6C">企业风险信息（{{fxxxsl}}条）</div>
              </template>

                <div class="tab-pane-content">
                    <!-- <component v-if="index == 0" :is="cbsjbxx"></component>
                              <component v-if="index == 4" :is="zjdw"></component> -->
                    <!-- <span v-else>{{ item.label }}</span> -->
                    <tycList v-if="item.SJMBBM==='QYFXXX'" :key="saveForm?.['JBXX'].TYXYDM" :TYXYDM="saveForm?.['JBXX'].TYXYDM" v-model:fxxxsl="fxxxsl"></tycList>
                    <component
                        v-else
                            :ref="(el) => setRefMap(el, item.SJMBBM)"
                            :is="ComponentDic[item.SJMBBM]"
                            :defaultData="saveForm[item.SJMBBM]"
                            :row="saveForm[item.SJMBBM]"
                            :DWYWID="uuId"
                            :YWLXDM="YWLXDM"
                        :TYXYDM="saveForm?.JBXX.TYXYDM"
                        :CBSDWQC="saveForm?.JBXX.CBSDWQC"
                            :editable="editable"
                            :resultTableData="resultTableData?.[item.SJMBBM]"
                            :LSZRLX = "attrs.EXTENSION?.LSZRLX"
                    ></component>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div style="height:320px;margin: 0px 40px 20px 40px" v-if="ifShowLscz">
            <el-divider content-position="left" class="divider">历史操作</el-divider>
            <el-table
                class="lui-table"
                highlight-current-row
                size="default"
                height="300px"
                border
                :data="tableData"
            >
                <el-table-column type="index" width="60" label="序号" align="center"/>
                <el-table-column label="业务类型" min-width="150" prop="YWLXMC" header-align="center" align="left">
                </el-table-column>
                <el-table-column label="发起时间" min-width="150" prop="CJSJ" header-align="center" align="center">
                </el-table-column>
                <el-table-column label="结束时间" min-width="100" header-align="center" align="center">
                </el-table-column>
                <el-table-column label="操作" min-width="60" header-align="center" align="center">
                    <template #default="{ row }">
                        <el-button class="lui-table-button" @click="viewLsjl(row)">
                            查看
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div style="text-align: center" v-if="editable&&!value">
            <el-button type="primary" :disabled="confirmLoading" @click="handleSave('0')">保存</el-button>
            <el-button type="success" v-if="from=='YWBMBA'" :disabled="confirmLoading" @click="chooseBlr('1')">提交审核</el-button>
            <el-button type="success" v-else :disabled="confirmLoading" @click="submitProcess('1')">提交审核</el-button>
            <!-- <el-button type="success" @click="testValidate">测试</el-button> -->
        </div>

        <el-dialog v-model="showDialog" title="选择油田机关专业管理人员" :append-to-body="true" :width="400+'px'"  z-index="1000">
            <div style="display: flex;align-items: center;margin-top: 10px" v-if="apprValue !== '0'">
            <el-select
                v-model="nextPerformer"
                multiple
                collapse-tags
                collapse-tags-tooltip
                filterable
                placeholder="请选择"
                style="width: 100%">
                <el-option
                    v-for="item in YTZYBMLDOptions"
                    :key="item.USER_LOGINNAME"
                    :label="`${item.USER_NAME}（${item.ORGNA_NAME}）`"
                    :value="item.USER_LOGINNAME"/>
            </el-select>
            </div>
            <div style="text-align: center; height: 40px; padding: 10px;margin-top:100px">
                <el-button type="primary" @click="onConfirm">确定</el-button>
                <el-button @click="showDialog=false">取消</el-button>
            </div>
        </el-dialog>
    </div>

</template>

<script setup>
import {
    reactive,
    ref,
    defineAsyncComponent,
    useAttrs,
    onMounted,
    watch,
    onUnmounted,
} from "vue";
import {
    getCbsyjGetTabShow,
    getCbsyjGetTabData,
    getCbsyjGetCbsjbxx,
    getCbsyjGetTeamInfo,
    postCbsyjSaveCbsxx,
    getCbsyjGetTeamResultInfo,//查询队伍结果信息
    getCbsyjGetCbsParams
} from "@src/api/sccbsgl.js";
import {getCommonSelectDMB} from "@src/api/common.js";
import {ElMessage,ElLoading} from "element-plus";
import tabFun from "@src/lib/tabFun";
import {auth, mixin} from "@src/assets/core/index";
import {vsuiapi} from "@vsui/vue-multiplex";
import comFun from "@src/lib/comFun";
import tycList from "../tyc/tycList";
import api from "@src/api/lc";//流程接口
import axios from "axios";
import axiosUtil from "../../../lib/axiosUtil";

const {vsuiRoute, vsuiEventbus} = mixin();

const emit=defineEmits(['update:value'])

const attrs = useAttrs();
const value = ref(null);//流程框架页传过来的流程参数
const params = ref(null);//流程框架页传过来的业务参数
const uuId = ref(""); //队伍业务ID
const MBID = ref(""); //模板ID
const MBLX = ref(""); //模板类型
const ZYFLDM = ref(""); //专业分类代码
const YWLXDM = ref(""); //业务类型代码(准入ZR,变更BG,增项ZX)
const JGDWYWID = ref("");// 结果表队伍业务ID
const EXTENSION = ref(""); //拓展字段，获取企业类型模板ID
const editable = ref(true); // 是否查看
const fxxxsl = ref(0)
const isVIewJgxx = ref(false); // 是否查询正式数据
const from = ref(null);//页面来源哪里
const YTZYBMLDOptions=ref([]);
const showDialog=ref(false);
const nextPerformer =ref([]);
from.value = attrs.from;
value.value=attrs.value;
params.value=attrs.params;
isVIewJgxx.value=attrs.isVIewJgxx; // 是否查询正式数据
// 是否展示历史操作
const ifShowLscz = ref(false);
ifShowLscz.value=attrs.ifShowLscz;
const refresh = (attr) => {
    uuId.value = attr.uuId;
    MBID.value = attr.MBID;
    MBLX.value = attr.MBLX;
    ZYFLDM.value = attr.ZYFLDM;
    YWLXDM.value = attr.YWLXDM;
    JGDWYWID.value = attr.JGDWYWID;
    EXTENSION.value = attr.EXTENSION;
    editable.value = attr.editable;
    console.log(uuId.value, MBID.value, MBLX.value, ZYFLDM.value, YWLXDM.value, attr);
    getTabsDefaultData();
};
onMounted(() => {
    vsuiEventbus.on("reloadCbsjbxx", refresh);
});
onUnmounted(() => {
    vsuiEventbus.off("reloadCbsjbxx", refresh);
});
// const uuId = '3a99591c9e824909ad544846e84ce8de'
// const MBID = '97CE94A1696C4AC78E680C0DCAFF6E13'
// const MBLX = 'QY'
// const ZYFLDM = 'KJWBXZ01'

// 承包商二级单位专业部门选择机关部门主要人员
let chooseCbsYtZyglbmForm = defineAsyncComponent(() => import("@views/zbxsgl/xssqgl/examinePage/chooseCbsYtZyglbmForm"));
// 承包商基本信息
let cbsjbxxComp = defineAsyncComponent(() => import("@views/cbs/cbsyj/cbsjbxx.vue"));
// 队伍信息
let dwxxComp = defineAsyncComponent(() => import("@views/cbs/cbsyj/dwxx.vue"));
// 选择专业及区域
let xzzyjqyComp = defineAsyncComponent(() => import("@views/cbs/cbsyj/xzzyjqy.vue"));
// 模板页
// 资质信息
const zzxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/zzxx/zzxx.vue")
);
// 许可信息
const xkxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/xkxx/xkxx.vue")
);
// 体系证书
const txzsComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/txzs/txzs.vue")
);
// 人员信息
const ryxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/ryxx/ryxx.vue")
);
// 设备信息
const sbxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/sbxx/sbxx.vue")
);
// 业绩信息
const yjxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/yjxx/yjxx.vue")
);

// 奖惩信息
const jcxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/jcxx/jcxx.vue")
);
// 知识产权
const zscqComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/zscq/zscq.vue")
);
// 车辆信息
const clxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/clxx/clxx.vue")
);
// 房屋信息
const fwxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/fwxx/fwxx.vue")
);
// 土地信息
const tdxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/tdxx/tdxx.vue")
);
// 授权委托信息
const sqwtxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/sqwtxx/sqwtxx.vue")
);
// 引入申请信息
const yrsqxxComp = defineAsyncComponent(() => import("@views/cbs/cbsyj/yrsqxx.vue"));

//企业风险信息
const qyfxxxComp = defineAsyncComponent(() => import("@views/cbs/tyc/tycList.vue"));

// 制度信息
const zdxxComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/zdxx/zdxx.vue")
);

// 安全环保
const aqhbComp = defineAsyncComponent(() =>
    import("@views/cbs/templateManagement/DataTemplateManagement/aqhb/aqhb.vue")
);


const bwtrxxComp = defineAsyncComponent(() =>
    import("@src/views/cbs/templateManagement/DataTemplateManagement/bwtrxx/bwtrxx.vue")
);

const cbsyjComp = defineAsyncComponent(() =>
    import("@src/views/cbs/templateManagement/DataTemplateManagement/cbsyj/cbsyj.vue")
);

const ComponentDic = ref({
    JBXX: cbsjbxxComp,
    ZZXX: zzxxComp,
    XKXX: xkxxComp,
    TXZS: txzsComp,
    RYXX: ryxxComp,
    SBXX: sbxxComp,
    YJXX: yjxxComp,
    JCXX: jcxxComp,
    ZSCQ: zscqComp,
    CLXX: clxxComp,
    FWXX: fwxxComp,
    TDXX: tdxxComp,
    SQWTXX: sqwtxxComp,
    DWXX: dwxxComp,
    YRSQXX: yrsqxxComp,
  QYFXXX: qyfxxxComp,
  ZDXX: zdxxComp,
  AQHB: aqhbComp,
  CBSYJ: cbsyjComp,
  BWTRXX: bwtrxxComp,
});
const refMap = ref({});
const setRefMap = (el,name) =>{
    refMap.value[name] = el
}

const getBlrOptions = (optionName,ROLE) => {
      let params={
        ROLE: ROLE,
        businessId: value.businessId||uuId.value
      }
      axiosUtil.get('/backend/cbsxx/common/getUserZyglbmByCbsKey',params).then(res=>{
        YTZYBMLDOptions.value=res.data || []
      })
    }
const chooseBlr = async()=>{
    let saveFlag =await handleSave('1');
    if(!saveFlag){
        return;
    }
    getBlrOptions('YTZYBMLDOptions','CBSGL_YTZYGLBM');
    showDialog.value=true;
}
const onConfirm =()=>{
    if(nextPerformer.value.length==0){
        ElMessage.warning('请选择油田专业管理部门审批人！')
        return;
    }
    const userinfo = auth.getPermission();
     let params={
        BLRList: [{
            TYPE: 'GR',
            ACTIVITYID: '3',
            LABEL: '专业部门审核人',
            BUSINESSID: value.businessId||uuId.value,
            PROCESSID: 'CBS_GDDW_ZR',
            CJRZH: userinfo.userLoginName,
            CJRXM: userinfo.userName,
            CJDWID: userinfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '1',
            XGRZH: userinfo.userLoginName,
            XGSJ: comFun.getNowTime(),
            NAME: nextPerformer.value.join(','),
            VALUE: nextPerformer.value.join(',')
        }]
      }
      axiosUtil.post('/backend/common/saveLcblr',params)
      showDialog.value=false
      submitProcess('noSave');
}
/* 校验各tab必填项 */
const validateTemplate = async() => {
    return new Promise((resolve,reject) => {
        Promise.all(Object.values(refMap.value).filter(i => i.validateForm).map(i => i.validateForm())).then((result) => {
            ElMessage.success('校验成功')
            resolve(true)
        }).catch((err) => {
            console.log(err);
            ElMessage.error(Object.values(err)[0]?.[0]?.message)
            resolve(false)
        })
    })

}
let activeName = ref("JBXX");
let compList = ref([
    /*  { SJMBMC: "承包商基本信息", SJMBBM: "JBXX" },
      { SJMBMC: "资质信息", SJMBBM: "ZZXX" },
      { SJMBMC: "许可信息", SJMBBM: "XKXX" },
      { SJMBMC: "体系证书", SJMBBM: "TXZS" },
      { SJMBMC: "人员信息", SJMBBM: "RYXX" },
      { SJMBMC: "设备信息", SJMBBM: "SBXX" },
      { SJMBMC: "业绩信息", SJMBBM: "YJXX" },
      { SJMBMC: "授权委托信息", SJMBBM: "SQWTXX" }, */
    /*
    资质信息
    许可信息
    体系证书
    人员信息
      */
]);

// 查询tab页数量
const getTabShow = () => {
    getCbsyjGetTabShow({
        mbids: MBID.value,
    })
        .then(({data}) => {
            compList.value = [{SJMBMC: "承包商基本信息", SJMBBM: "JBXX"}, ...data];
            if (MBLX.value == "DW") compList.value.push({SJMBMC: "队伍信息", SJMBBM: "DWXX"});
            else compList.value.push({SJMBMC: "引入申请信息", SJMBBM: "YRSQXX"});

          //compList.value.push({SJMBMC: "企业风险信息", SJMBBM: "QYFXXX"})

        })
        .catch((err) => {
        });
};
//根据传过来的参数查询系统所需参数
const getParamsData = () => {
    if(attrs.uuId){//直接传值
        uuId.value = attrs.uuId;
        MBID.value = attrs.MBID;
        MBLX.value = attrs.MBLX;
        ZYFLDM.value = attrs.ZYFLDM;
        YWLXDM.value = attrs.YWLXDM;
        JGDWYWID.value = attrs.JGDWYWID;
        EXTENSION.value = attrs.EXTENSION;
        editable.value = attrs.editable;
        getTabsDefaultData();
        if(YWLXDM.value == "BG"){
            getTeamResultInfo()
        }
    }else{
        console.log('业务变量ID',params)
        if(!params.value){
            ElMessage.warning('参数缺失请关闭该页签重新打开！')
            return;
        }
        getCbsyjGetCbsParams({
                DWYWID: params.value.id,
            })
                .then(({data}) => {
                   console.log("承包商参数",data)
                   if(data.length>0){
                        uuId.value = data[0].DWYWID;
                        MBID.value = data[0].MBID;
                        MBLX.value = data[0].MBLX;
                        ZYFLDM.value = data[0].ZYFLDM;
                        YWLXDM.value = data[0].YWLXDM;
                        JGDWYWID.value = attrs.JGDWYWID;
                        EXTENSION.value = data[0].EXTENSION;
                        editable.value = params.value.editable;
                        getTabsDefaultData();
                        if(YWLXDM.value == "BG"){
                            getTeamResultInfo()
                        }
                   }
                })
                .catch((err) => {
                });
    }
};
// 查询tab基本信息
const tabDefaultData = ref([]);
// 结果表数据
const resultTableData = ref(null);
const getTeamResultInfo = () => {
    if(!JGDWYWID.value) return;
    getCbsyjGetTeamResultInfo({
        DWYWID: JGDWYWID.value
    }).then(({data}) => {
        if(!data) return;
        resultTableData.value = data;
    }).catch((err) => {
        ElMessage.warning('查询结果表数据失败')
    });
}
const loading = ref(false);
const getTabsDefaultData = () => {
    console.log("getTabsDefaultData")
    console.log(ZYFLDM.value , MBLX.value , uuId.value , MBID.value , YWLXDM.value);
    if (/* !ZYFLDM.value || */ !MBLX.value || !uuId.value || !MBID.value || !YWLXDM.value) return;
    loading.value = true;
    console.log('EXTENSION.value?.MBID ?? MBID.value',EXTENSION.value?.MBID ?? MBID.value);
    Promise.all([
        // 如果有准入模板代码，则进行查询默认数据
        ZYFLDM.value ? getCbsyjGetTabData({
            ZYFLDM: ZYFLDM.value,
            // MBLX: MBLX.value,
            MBLX: "QY",//承包商基本信息只需要考虑QY的情况
        }) : Promise.resolve({data:[]}),
        // 查看时调用结果表数据，准入、变更时调用过程表数据
        (isVIewJgxx.value ? getCbsyjGetTeamResultInfo : getCbsyjGetTeamInfo)({
            DWYWID: uuId.value,
        }),
        // 查询tab页展示数据，优先根据企业模板ID
        getCbsyjGetTabShow({
            mbids: EXTENSION.value?.MBID || MBID.value,
        }),
    ])
        .then(([{data}, {data: result}, {data: tab}]) => {
            console.log("tab基本数据", data, result);
            tabDefaultData.value =
                data?.map((i) => ({
                    ...i,
                    ZYMC: i.ZYFLMC,
                    ZYBM: i.ZYFLDM,
                    SHZT: "0",
                  EXTENSION:{
                    SJLY: 'MBSC'
                  }
                })) ?? [];
            const obj = tabDefaultData.value.reduce((t, i) => {
                t[i.SJMBBM] ? t[i.SJMBBM].push(i) : (t[i.SJMBBM] = [i]);
                return t;
            }, {});
            console.log('objobjobjobjobjobjobjobj',obj)
            Object.entries(result || {}).forEach(([key, value]) => {
                if (value) {
                    //排除null
                    if (Array.isArray(value)) {
                        //list的话且不为空数组的时赋值
                        if (value.length || key==='CBSYJ') obj[key] = value;
                    } else {
                        obj[key] = value;
                    }
                }
            });

            saveForm.value = obj;
            console.log('asdfafafasdfasfasdfasf',saveForm.value)
            if(!saveForm.value.JBXX.CBSDWQC&&from.value!='YWBMBA'){//特殊准入由二级单位发起，不携带组织机构名称和账号信息
                const userinfo = auth.getPermission();
                saveForm.value.JBXX.CBSDWQC=userinfo.orgnaName;
              axiosUtil.get('/backend/common/selectOrganByUserId',{USER_ID:userinfo.userId}).then(res=>{
                saveForm.value.JBXX.TYXYDM =res.data[0]?.ORGNA_CODE.split('-')[0]

              })
            }
            compList.value = [{SJMBMC: "承包商基本信息", SJMBBM: "JBXX"}];
            if (MBLX.value == "DW"){
              compList.value.push(...tab)
              compList.value.push({SJMBMC: "业绩信息", SJMBBM: "CBSYJ"},{SJMBMC: "被委托人信息", SJMBBM: "BWTRXX"},{SJMBMC: saveForm.value.DWXX.ZYFL=== 'ADMGC' ? "项目部信息" : '队伍信息', SJMBBM: "DWXX"});
            } else {
              compList.value.push({SJMBMC: "引入申请信息", SJMBBM: "YRSQXX"});
              compList.value.push(...tab)
              compList.value.push({SJMBMC: "业绩信息", SJMBBM: "CBSYJ"},{SJMBMC: "被委托人信息", SJMBBM: "BWTRXX"});
            }


          //compList.value.push({SJMBMC: "企业风险信息", SJMBBM: "QYFXXX"})
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            loading.value = false;
        });
};
const statusDic = ref({});
/**查询服务区域 */
const queryStatusDic = () => {
    getCommonSelectDMB({DMLBID: 'YWLXDM'}).then(({data}) => {
        console.log('data', data);
        if (data) {
            statusDic.value = data.reduce((t, i) => {
                t[i.DMXX] = i.DMMC;
                return t
            }, {})
        }
    })
}
onMounted(() => {
    queryStatusDic()
})
const saveForm = ref({});
const confirmLoading = ref(false);
const handleSave = async(status) => {
    loading.value=true;
    var flag=false;
    // 提交时添加校验
    if(status == '1'){
        const validResult = await validateTemplate();
        if(!validResult){
            loading.value=false;
            return false;
        }
    }
    console.log("auth", auth);
    // 设置登记人基本信息
    const userinfo = auth.getPermission();
    const feildList = {
        CJRZH: userinfo.userLoginName,
        CJRXM: userinfo.userName,
        CJDWID: userinfo.orgnaId,
        CJSJ: comFun.getNowTime()
    };
    const updatefeildList = {
        XGRZH: userinfo.userLoginName,
        XGSJ: comFun.getNowTime()
    };
    // 给无默认值数据添加默认数据
    const setDafaultValue = (data) => {
        if(YWLXDM.value === 'ZR'){
            //准入增加创建人信息
            Object.entries(feildList).forEach(([key, value]) => {
                if (!data[key]) {
                    data[key] = value;
                }
            });
        }else{
            //其他类型增加更新人信息
            Object.entries(updatefeildList).forEach(([key, value]) => {
                if (!data[key]) {
                    data[key] = value;
                }
            });
        }

    };

    Object.entries(saveForm.value).forEach(([key, value]) => {
        if (value) {
            //排除null
            if (Array.isArray(value)) {
                //list的话且不为空数组的时赋值
                value.forEach((x) => {
                    x.DWYWID = uuId.value;
                    setDafaultValue(x);
                });
            } else if (value instanceof Object) {
                setDafaultValue(value);
            }
        }
    });

  console.error(5555,saveForm)

  // postCbsyjSaveCbsxx
    saveForm.value.ZZXX?.forEach((x) => {
        x.ZSDLDM = "ZZZS";
        x.ZSCYZBS = saveForm.value.YRSQXX.DWYWID
    });
    saveForm.value.TXZS?.forEach((x) => {
        x.ZSDLDM = "TXZS";
        x.ZSCYZBS = saveForm.value.YRSQXX.DWYWID
    });
    saveForm.value.XKXX?.forEach((x) => {
        x.ZSDLDM = "XKZS";
        x.ZSCYZBS = saveForm.value.YRSQXX.DWYWID
    });
    saveForm.value.RYXX?.forEach(x=>{
        if(x.ZSXX){
            x.ZSXX.forEach(v=>{
                v.DWYWID = uuId.value
                setDafaultValue(v);
            })
        }
    })
    saveForm.value.JBXX.CBSWYBS = saveForm.value.JBXX.CBSWYBS || saveForm.value.JBXX.CBSYWID //承包商唯一标识
    saveForm.value.YRSQXX.DWWYBS = saveForm.value.YRSQXX.DWWYBS || saveForm.value.YRSQXX.DWYWID //队伍位移表示
    saveForm.value.YRSQXX.DWMC = saveForm.value.JBXX.CBSDWQC //队伍名称 = 企业名称
    saveForm.value.JBXX.YWLXDM = YWLXDM.value; //给承包商基本信息设置业务类型代码
    saveForm.value.YRSQXX.YWLXDM = YWLXDM.value; //给承包商基本信息设置业务类型代码

    if(saveForm.value.YRSQXX.SHZT=='0'||!saveForm.value.YRSQXX.SHZT){
        saveForm.value.JBXX.SHZT = '0'; //审核状态
        saveForm.value.YRSQXX.SHZT = '0'; //审核状态
    }
    // saveForm.value.YRSQXX.EXTENSION?.MBID = saveForm.value.YRSQXX.EXTENSION?.MBID + ',' + EXTENSION.value?.MBID; //审核状态

    saveForm.value.YRSQXX.YWBT = saveForm.value.JBXX.CBSDWQC + `-${statusDic.value[YWLXDM.value]}`;
    let form = {
        cbsjbxx: saveForm.value.JBXX,
        cbsdwxx: saveForm.value.YRSQXX,
        cbszs: (saveForm.value.ZZXX ?? []).concat(saveForm.value.TXZS ?? []).concat(saveForm.value.XKXX ?? []),
        cbsdwyj: saveForm.value.YJXX,

        cbsdwzscq: saveForm.value.ZSCQ,
        cbsdwclxx: saveForm.value.CLXX,
        cbsdwfwxx: saveForm.value.FWXX,
        cbsdwtdxx: saveForm.value.TDXX,
      cbsdwzdxx: saveForm.value.ZDXX,
      cbsdwaqhb: saveForm.value.AQHB,

        cbsdwsb: saveForm.value.SBXX,
        cbsdwjcqk: saveForm.value.JCXX,
        cbsdwcy: saveForm.value.RYXX,
        bczt: '0', //"0保存；1提交；2审核通过；"
        DWYWID: uuId.value,
      bwtrxx:  saveForm.value.BWTRXX,
      cbsyj:  saveForm.value.CBSYJ,
    };
    //统一添加持有者类型为承包商
    form.cbszs.forEach(x => x.ZSCYZLXDM = 'CBS')
    // console.log('statusDic.value',statusDic.value,YWLXDM.value);
    console.log(form);
    // return
    confirmLoading.value = true;
  let res
    try {
      res= await postCbsyjSaveCbsxx(form);
    }catch (e) {
      loading.value=false;
      confirmLoading.value = false;
    }
    if(res.code===1){
        flag=true;
        if ((status === "0"||!status ) && !value) {
            ElMessage({
                type: "success",
                message: "保存成功!",
            });
        }
    }else{
        flag=false;
        ElMessage({
                type: "error",
                message: res.message,
            });
    }
    confirmLoading.value = false;
    loading.value=false;
    return flag;

};
//工作流的保存
const saveData = (val) => {
    console.log('流程变量',value)
    console.log('业务变量',params)

    return new Promise(async (resolve, reject) => {
        console.log('saveFormsaveFormsaveFormsaveFormsaveForm',saveForm);
        let dwxx=saveForm.value.JBXX;
        let processName = '';
        if(dwxx.YWLXDM=='ZR'){
            processName=dwxx.CBSDWQC+'引入申请';
        }else if(dwxx.YWLXDM=='ZX'){
            processName=dwxx.CBSDWQC+'增项申请';
        }else if(dwxx.YWLXDM=='BG'){
            processName=dwxx.CBSDWQC+'变更申请';
        }else if(dwxx.YWLXDM=='FS'){
            processName=dwxx.CBSDWQC+'复审申请';
        }
        if(val){
            if(value.value.activityId=='1'||value.value.activityId=='new'){
                let saveFlag =await handleSave('1');
                if(!saveFlag){
                    return;
                }
            }
        }else{
            let saveFlag =await handleSave('0');
            if(!saveFlag){
                return;
            }
        }

        let data = {
            ...value,
            businessId: value.businessId||uuId.value,
            processInstanceName:value.processInstanceName||processName
        };
        emit("update:value", data);
        resolve(true)
    })
}

//提交流程const saveData = (val) => {
	const submitProcess= async(status) => {
            if(status!='noSave'){
                let saveFlag =await handleSave('1');
                if(!saveFlag){
                    return;
                }
            }

            const userinfo = auth.getPermission();
            let dwxx=saveForm.value.JBXX;
			let processId = '';
            let processName = '';
            if(dwxx.YWLXDM=='ZR'){
                processName=dwxx.CBSDWQC+'引入申请';
            }else if(dwxx.YWLXDM=='ZX'){
                processName=dwxx.CBSDWQC+'增项申请';
            }else if(dwxx.YWLXDM=='BG'){
                processName=dwxx.CBSDWQC+'变更申请';
            }else if(dwxx.YWLXDM=='FS'){
                processName=dwxx.CBSDWQC+'复审申请';
            }

            let DWLB=saveForm.value.DWXX.DWLB
            if(DWLB==='ASYGC'){
              processId='CBS_A_SYGC_ZR';
            }else if(DWLB==='ADMGC'){
              processId='CBS_A_DMGC_ZR';
            }else if(DWLB==='B'||DWLB==='B_ZXSP'){
              processId='CBS_B_ZR';
            }else if(DWLB==='GKZB'){
              processId='CBS_GKZB_ZR';
            }else if(DWLB==='LSTD'){
              processId='CBS_GDDW_ZR';
            }else if(DWLB==='CBSBL'){
              processId='CBS_XXBL';
            }
            if(dwxx.YWLXDM=='ZR'&&DWLB==='GKZB'){
                processName=dwxx.CBSDWQC+'公开招标中标单位引入登记';
            }

            if(!processId){
                ElMessage({message: '提交失败，找不到对应流程请重试！',type: 'error',})
                return;
            }
            let _params = {};
            _params.strSystemCode = 'AUTHM_CENTER';
            _params.processId = processId;
            _params.engineType = 'vs';
            _params.activityId = 'new';
            _params.processInstanceName = processName;
            _params.businessId = uuId.value;
            _params.apprValue = "1";
            _params.apprResult = "提交";
            _params.loginName = userinfo.userLoginName;
            _params.userOrgId = userinfo.orgnaId;
			let ld = ElLoading.service({target: "#auditDiv", text: "正在提交数据，请稍后...",});
            axios({
                method: "post",
                url: api.createTask(),
                data: "varJson=" + JSON.stringify(_params),
            })
                .then((res) => {
                ld.close();
                if (res.data && res.data.result == 1) {
                    if(from.value=='YWBMBA'){
                        tabFun.openNewTabClose("特殊队伍引进", "/contractors/cbsrkList", {}, {});
                        vsuiEventbus.emit("reloadBaList", {});
                    }else if(from.value=='CBSBL'){
                        tabFun.openNewTabClose("存量承包商引进", "/contractors/cbsblList", {}, {});
                        vsuiEventbus.emit("reloadCbsblList", {});
                    }else{
                        tabFun.openNewTabClose("承包商准入", "/contractors/cbsywdhPage", {}, {});
                        vsuiEventbus.emit("reloadCaoGao", {});
                    }

                    ElMessage({message: '提交成功！',type: 'success',})
                } else {
                    axiosUtil.get('/backend/workFlow/backBusinessStatus', {processId:_params.processId,businessId:_params.businessId})
                    ElMessage({message: res.data.error,type: 'error',})
                }
                })
                .catch((error) => {
                    axiosUtil.get('/backend/workFlow/backBusinessStatus', {processId:_params.processId,businessId:_params.businessId})
                console.log(error);
                ld.close();
                ElMessage({message: '提交失败！',type: 'error',})
                });
    }

onMounted(() => {
    getParamsData();
    if(ifShowLscz.value){
        initCbsLsczList();
    }
    console.log('流程变量',value)
    console.log('业务变量',params)
    // getTabShow();
});
let handleClick = (tab, event) => {
  if(!saveForm.value?.JBXX?.TYXYDM){
    ElMessage.warning('请先维护统一信用代码')
    return false
  }
};

const changeTabs = () => {
  if(!saveForm.value?.JBXX?.TYXYDM){
    activeName.value='JBXX'
    ElMessage.warning('请先维护统一信用代码')
  }
}

// 初始化数据
const tableData = ref([]);
const initCbsLsczList = () => {
    axiosUtil.get('/backend/sccbsgl/report/queryCbsLsczList', {dwywid:uuId.value}).then((res) => {
        tableData.value = res.data.rows
    });
}
// 查询历史记录
const viewLsjl = (row) => {
    if(row.YWLXDM === 'ZR' || row.YWLXDM === 'ZX'){
        const {DWYWID: uuId, MBID, MBLX, ZYFLDM, YWLXDM, EXTENSION} = row;
        tabFun.addTabByRoutePath(
        `承包商信息`,
        "/contractors/cbsjbxxIndex",
        {
            uuId,
            MBID,
            MBLX,
            ZYFLDM,
            YWLXDM: YWLXDM ?? 'ZR',
            JGDWYWID: row.JGDWYWID,
            EXTENSION,
            editable: false
        },
        {}
        );
    }else if(row.YWLXDM === 'BG'){
        tabFun.addTabByRoutePath("承包商变更信息", "/contractors/cbsbgEdit", {
          params:{
            editable: false,
            id: row.DWYWID,
            operation: 'view'
          }
        }, {});
    }

}


defineExpose({
    saveData
})
</script>

<style scoped>
.container {
    height: calc(100vh - 100px);
    background-color: #fff;
    overflow-y: auto;
}

.container .el-tabs {
    height: 100%;
    margin: 0px 40px 10px 40px;
}

.container .el-tabs ::v-deep .el-tabs__header {
    margin-bottom: 0;
}

.container .el-tabs ::v-deep .el-tabs__content {
    height: calc(100% - 60px);
    border: 2px solid #dfdfdf;
}

.tab-pane-content {
    height: 100%;
}

:deep(.el-table .el-button) {
    padding: 5px;
}

:deep(.el-table .el-button + .el-button) {
    margin-left: 5px;
}
::v-deep .divider .el-divider__text{
    color:#5f80c7;
    font-weight: bold;
}
</style>
