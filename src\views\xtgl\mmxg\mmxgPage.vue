<template>
  <div class="context">
    <div class="context-form" style="boxShadow: var(--el-box-shadow)">
      <el-form
          class="form-context"
          size="default"
          :model="formDate"
          ref="vForm"
          :rules="rules"
          label-position="right"
          label-width="120px"
      >
        <el-form-item label="新密码" prop="pw">
          <el-input
              v-model.trim="formDate.pw"
              type="password"
              clearable
              placeholder="请输入"
              show-password
              minlength="8"
          ></el-input>
        </el-form-item>
        <div :span="24" class="grid-cell" style="padding-bottom: 10px">
          提示：密码由英文+字母+字符组成，不得少于8位,并且不能连续出现3个大小连续或相同的数字&nbsp;(如：456、654、888)
        </div>
        <el-form-item label="确认密码" prop="surePw">
          <el-input
              v-model="formDate.surePw"
              type="password"
              clearable
              placeholder="请输入"
              show-password
              minlength="8"
          ></el-input>
        </el-form-item>

        <div style="width: 100%;text-align: center">
          <el-button type="primary" @click="submit">确认修改</el-button>
        </div>
      </el-form>

    </div>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import tabFun from "@lib/tabFun";
import vsAuth from "../../../lib/vsAuth";
const CryptoJS =require("crypto-js")


export default defineComponent({
  name: '',
  components: {},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      rules:{
        pw: [
          {required: true, message: "密码不能为空", trigger: blur},
          {
            min: 8,
            max: 16,
            message: "不得少于8位，不得超过16位",
            trigger: "change",
          },
          {
            pattern: /^(?=.*\d)(?!.*(\d)\1{2})(?!.*(012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210))(?=.*[a-zA-Z])(?=.*[^\da-zA-Z\s]).{8,16}$/,
            message: "至少包含字母、数字、特殊字符,并且不能连续出现3个大小连续或相同的数字(如：456、654、888)",
            trigger: "blur"
          },
          {
            required: true,
            validator: async (rule, value, callback) => {
              if(value){
                let isCF = await checkMM(value,vsAuth.getAuthInfo().permission.userId)
                if(Boolean(isCF.data)){
                  callback()
                }else {
                  callback(new Error('密码近期已经使用'))
                }
              }else {
                callback(new Error('密码不能为空'))
              }

            },
          }
          //
        ],
        //确认密码
        surePw: [
          {required: true, message: "密码不能为空", trigger: blur},
          {
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (value === state.formDate.pw) callback();
              else callback(new Error("两次密码输入不一致"));
            },
          },
        ],
      },
      formDate:{
        pw: '',
        surePw: ''
      }
    })
    
    const checkMM = (MM,USERID) => {
      return axiosUtil.get('/backend/common/dlgl/selectZjxgmm',{MM: CryptoJS.MD5(MM).toString(),USERID})
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            resolve(false)
          }
        })
      })
    }


    const submit = () => {
      validateForm().then(res=>{
        if(res){
          ElMessageBox.confirm(
              '确定修改当前用户密码?',
              '警告',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
              }
          ).then(() => {
            let params={
              password: CryptoJS.MD5(state.formDate.pw).toString()
            }
            axiosUtil.get('/backend/login/changePassword', params).then(res => {
              ElMessage.success("修改成功")
              tabFun.closeTabByPath('/workflow/mmxg')
              tabFun.setCurrentTabByIndex(0)
            })
          }).catch(() => {})
        }
      })
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      submit

    }
  }

})
</script>

<style scoped>
.context {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}

.context-form {
  width: 500px;
  height: 300px;
  background-color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
