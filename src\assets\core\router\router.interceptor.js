/**
 * Powered by vue-router ^4.0.12
 * 路由拦截器，可在路由跳转过程中进行拦截处理，
 * 2021年10月11日
 *
 *
 */
 import axiosUtil from "../../../lib/axiosUtil";



const routerInterceptorFirst = {
  beforeEach: function (to, from, next) {
    if(to.path!='/login'&&to.path!='/drag'&&to.path!='/drag2'){
      axiosUtil.log({LOG_CONTENT:to.path,METHOD:'',PARAMES:'',LOG_TYPE:'OPERATION',OPRATE_TYPE:'ROUTER',OPRATE_RESULT:'1'})
    }
    next()
  },
  afterEach: function (transition) {
  }
}


const routerInterceptorLast = {
  beforeEach: function (to, from, next) {
    next()
  },
  afterEach: function (transition) {
  }
}

export { routerInterceptorFirst, routerInterceptorLast }
