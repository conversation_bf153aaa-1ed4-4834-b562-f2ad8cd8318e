/*
 * @Description:
 * @User: ddhhh
 * @Date: 2021-01-21 16:16:14
 * @describe: generatorSource中ip地址使用自己的后端地址或服务器上地址
 */
module.exports = {
  apiFilePath: "../src/api",
  modelFilePath: "models",
  apiFileType: "js",
  templatePath: "template1",
  modules: [
    {
      name: "",
      // 0代表读取json文件，1代表从url中读取json
      generatorType: 0,
      // generatorSource: "./json/temp.json", //资源地址
      generatorSource: "http://************:8888/magic/web/resource", //资源地址
      // ],
      // 过滤字段为黑名单模式，判断方法较为简单，暂不支持正则匹配，不能为空字符串，否则都会过滤掉
      filterWords: [" "],
    },
  ],
};
