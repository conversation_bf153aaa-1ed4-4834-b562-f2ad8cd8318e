<template>
    <div>
        <h3>当前用户：{{ userName }}</h3>
        <h4>共享文档列表</h4>
        <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="" label="文档类型" width="300">
                <template #default>
                    <img :src="imageSrc" alt="文档类型" />
                </template>
            </el-table-column>
            <el-table-column prop="subject" label="文件名称" width="300"></el-table-column>
            <el-table-column label="操作">
                <template #default="scope">
                    <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button size="small" type="primary" @click="handleView(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { POBrowser } from 'js-pageoffice';
const tableData = ref([]); // 定义表格数据的响应式引用
const imageSrc = ref('');
const userName = ref('');

const route = useRoute();

const fetchData = async () => {
    try {
        const response = await request({
            url: '/BingFa/index',
            method: 'get',
        });
        tableData.value = response;
        imageSrc.value = '/src/assets/images/office-1.png';
    } catch (error) {
        console.error('There has been a problem with your fetch operation:', error);
    }
};
// 处理编辑操作
const handleEdit = (row) => {
    selectDocById(row.id).then(response => {
        //获取当前文件对应的editor，如果为空或者和当前登录用户名相同则打开PageOffice，并且修改editor的值为当前登录用户名
        if (response.editor == "" || response.editor == userName.value) {
            let params = { id: row.id, userName: userName.value, subject: row.subject, fileName: row.fileName };
            POBrowser.openWindow("/BingFa/Word1", "width=1200px;height=900px", JSON.stringify(params));
        } else {
            alert('用户“' + response.editor + '”正在编辑此文档，请稍后重试，或点击“查看”只读打开。');
        }
    })
}
function selectDocById(id) {
    // 发起Get请求到后端Controller的路由
    return request({
        url: '/BingFa/selectDocById',
        method: 'get',
        params: {
            id: id,
        }
    });
}

// 处理查看操作
const handleView = (row) => {
    let params = { id: row.id, userName: userName.value, subject: row.subject, fileName: row.fileName };
    POBrowser.openWindow("/BingFa/Word2", "width=1200px;height=900px", JSON.stringify(params));
};

onMounted(() => {
    fetchData();
    userName.value = route.params.username;

});
</script>

<style scoped>
.el-table {
    width: 100%;
    margin: 0 auto;
    margin-top: 20px;
    border: 1px solid #dcdfe6;

}

h3 {
    float: right;
    font-size: 25px;
    margin-right: 20px;
    /* 添加右侧间距 */
}

h4 {
    font-size: 20px;
    font-weight: 700;
}
</style>