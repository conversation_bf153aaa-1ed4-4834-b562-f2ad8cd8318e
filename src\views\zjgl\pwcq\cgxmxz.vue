<!-- 采购项目管理列表 -->
<template>
  <el-form class="lui-page" ref="vForm" :rules="rules" label-position="right" label-width="0" size="default"
           @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="xmmc">
          <el-input placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="xmbh">
          <el-input placeholder="请输入项目编号" v-model="listQuery.XMBM" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell">
        <div class="static-content-item" style="display: flex">
          <el-button ref="button91277" @click="getDataList" type="primary">查询</el-button>
          <el-button type="success" @click="confirm">确认</el-button>
          <el-button type="default" @click="closeForm">关闭</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row ref="grid71868" v-show="true">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper" v-show="true">
          <el-table ref="datatable91634" :data="tableData" height="500px" :border="true" :show-summary="false"
                    row-key="CGXMBS" @selection-change="handleSelectionChange" class="lui-table"
                    size="default" :stripe="false" :highlight-current-row="true" :cell-style="{ padding: '10px 0 ' }">
            <el-table-column type="selection" width="55" fixed="left" :reserve-selection="true"/>
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                             :index="indexMethod"></el-table-column>
            <el-table-column prop="XMMC" label="项目名称" align="center"
                             min-width="160"></el-table-column>
            <el-table-column prop="XMBM" label="项目编号" align="center"
                             min-width="100"></el-table-column>
            <el-table-column prop="XMLB" label="项目类别" align="center"
                             min-width="100"></el-table-column>
            <el-table-column prop="SSDW" label="所属单位" align="center"
                             min-width="100"></el-table-column>
            <el-table-column prop="XMED" label="项目额度" align="center"
                             min-width="100"></el-table-column>
            <el-table-column prop="CJRMC" label="创建人" align="center"
                             min-width="100"></el-table-column>
            <el-table-column prop="CJSJ" label="创建时间" align="center"
                             min-width="200"></el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted
}
  from 'vue'
// 请求方法 get,post,put,del,downloadFile
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "../../../lib/comFun";

export default defineComponent({
  name: '',
  components: {},
  props: {
    // xmParams: {
    //     type: Object,
    //     required: true
    // },
  },
  setup(props, context) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      CGXMBS: '',
      listQuery: {
        XMMC: '',
        XMBH: '',
        XMLB: '',
        page: 1,
        size: 10,
        SHZT: '1'
      },
      tableData: [{}],
      total: 0,
      rules: {},
      XMLBOptions: [],
      dialogVisible: false,
      params: null,
      currentRow: []
    })

    const getDataList = () => {
      axiosUtil.get('/backend/zjgl/cgxmgl/queryCgxmList', state.listQuery).then((res) => {
        state.tableData = res.data.list || []
        state.total = res.data.total
      });
    }


    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      var res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      context.emit("closeXmForm")
    }
    const handleSelectionChange = (val) => {
      state.currentRow = val
    }
    const confirm = () => {
      if (state.currentRow.length > 0) {
        context.emit('parentMethod', state.currentRow)
      } else {
        ElMessage({
          message: `请选择数据`,
          type: 'warning',
        })
      }
    }
    onMounted(async () => {
      getDataList()
      getDMBData("XMLB", "XMLBOptions")
    })

    return {
      ...toRefs(state),
      getDataList,
      getDMBData, indexMethod, closeForm, handleSelectionChange, confirm
    }
  }
})

</script>
<style>
</style>

