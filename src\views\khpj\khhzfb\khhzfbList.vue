<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="KHZQ">
            <el-select v-model="listQuery.KHZQ" class="full-width-input"
                       placeholder="请选择考核周期"
                       clearable>
              <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC"
                         :value="item.PJZQID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="KHZY">
            <el-select v-model="listQuery.KHZY" class="full-width-input"
                       placeholder="请选择考核专业" filterable
                       clearable>
              <el-option v-for="(item, index) in KHZYOptions" :key="index" :label="item.ZYMC"
                         :value="item.PJZYID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData">
              <el-icon>
                <Plus/>
              </el-icon>
              新建
            </el-button>
              <el-button ref="button9527" type="warning" class="lui-button-add" @click="wtallSummary" :loading="summing">
                <el-icon>
                <DocumentCopy/>
                </el-icon>
              问题汇总
            </el-button>
            <el-button ref="button9527" type="warning" class="lui-button-add" @click="allSummary" :loading="summing">
              <el-icon>
                <DocumentCopy/>
              </el-icon>
              排名汇总
            </el-button>
            <el-button ref="button9527" type="warning" class="lui-button-add" @click="deleteSummary" :loading="summing">
              <el-icon>
                <DocumentCopy/>
              </el-icon>
              清除排名
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="ZQMC" label="考核周期" align="center" :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="PJZYMC" label="考核专业" header-align="center" align="left" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="CBSSL" label="承包商数量" align="center" :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="DWSL" label="队伍数量" align="center" :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="CJSJ" label="汇总时间" align="center" :show-overflow-tooltip="true" width="180"></el-table-column>
              <!-- <el-table-column prop="SHZT" label="状态" align="center"
                               :show-overflow-tooltip="true" width="160">
                <template #default="{row}">
                  <el-tag type="primary" v-if="row.SHZT==='1'">提交</el-tag>
                  <el-tag type="success" v-else>保存</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="XGSJ" label="提交时间" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="CJRXM" label="提交人" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column> -->
              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                <span v-if="scope.row.SHZT==='0'">
                    <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑
                    </el-button>
                    <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.row)">删除
                    </el-button>
                  </span>
                  <span v-else>
                    <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                    </el-button>
                    <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.row)">删除
                    </el-button>
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="考核汇总情况"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <khhzfbEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload,DocumentCopy} from "@element-plus/icons-vue";
import khhzfbEdit from "@views/khpj/khhzfb/khhzfbEdit";
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "@lib/vsAuth";

export default defineComponent({
  name: '',
  components: {Search, Upload, Plus, khhzfbEdit,DocumentCopy},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,

      KHSJOptions: [],
      KHZYOptions: [],

      summing: false
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/sckhpj/khhzfb/selectHzfbPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    

    const wtallSummary = () => {
      state.summing=true
      axiosUtil.post('/backend//sckhpj/khmxtb/khWthzAuto',{}).then(res=>{
        ElMessage.success('问题汇总成功')
        state.summing=false
      })
    }

    const allSummary = () => {
      if(!state.listQuery.KHZQ){
        ElMessage.warning('请选择汇总周期')
        return
      }

      let params={
        PJZQ: state.listQuery.KHZQ,
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '1',
        PJLX: 'ZQXKHHZ',
        XGRZH: vsAuth.getAuthInfo().permission.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      state.summing=true
      axiosUtil.post('/backend/sckhpj/khhzfb/sumAllByZq',params).then(res=>{
        ElMessage.success('排名汇总成功')
        getDataList()
        state.summing=false
      })
    }

    const deleteSummary = () => {
      if(!state.listQuery.KHZQ){
        ElMessage.warning('请选择汇总周期')
        return
      }

      ElMessageBox.confirm("此功能将删除该季度的所有汇总数据，确认是否继续删除？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          let params={
            PJZQ: state.listQuery.KHZQ
          }
          state.summing=true
          axiosUtil.post('/backend/sckhpj/khhzfb/deleteAllByZq',params).then(res=>{
            ElMessage.success('删除排名汇总成功')
            getDataList()
            state.summing=false
          })
        });
    }

    

    

    // 新增
    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.params = {editable: true, id: row.KHHZID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.params = {editable: false, id: row.KHHZID, operation: 'view'}
      state.dialogVisible = true
    }

    const deleteRow = (row) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.del('/backend/sckhpj/khhzfb/delHzfb?KHHZID=' + row.KHHZID, null).then(res => {
          ElMessage.success({
            message: '删除成功!'
          });
          getDataList()
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const getKhsjList = () => {
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhqjList', null).then(res => {
        state.KHSJOptions = res.data || []
      })
    }

    const getKhzyList = () => {
      axiosUtil.get('/backend/sckhpj/khmxtb/selectKhzyList', null).then(res => {
        state.KHZYOptions = res.data || []
      })
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getDataList()
      getKhsjList()
      getKhzyList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      addData,
      closeForm,
      deleteRow,
      viewRow,
      allSummary,
      wtallSummary,
      deleteSummary
    }
  }

})
</script>

<style scoped>

</style>
