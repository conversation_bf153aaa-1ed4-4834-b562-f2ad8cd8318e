<template>
    <div class="zhyy-list-container" style=" height: calc(100vh - 150px);overflow: auto;">
        <el-row>
            <el-col :span="10" style="margin-left: 15px">
                <el-row>
                    <div>
                        <span style="color: #c10606;float: left;margin-left: 15px;line-height: 35px;font-size: 17px">引进模板：</span>
                        <span style="float: right"> <el-button type="primary" style="margin-left:10px;margin-right: 25px" @click="addZrmb()">添加</el-button></span>
                    </div>
                </el-row>
                    <!--表格数据-->
                    <el-row class="zhyy-list-tableArea">
                        <!--&lt;!&ndash; 表格 &ndash;&gt;-->
                        <el-table
                            class="customer-no-border-table"
                            :data="zrmbTableData"
                            border="1px"
                            width="100%"
                            height="250px"
                        >
                            <el-table-column
                                type="index"
                                :index="indexMethod"
                                label="序号"
                                align="center"
                                width="50"
                            ></el-table-column>
                            <el-table-column
                                prop="MBLXMC"
                                label="模板类型"
                                header-align="center"
                                align="center">
                            </el-table-column>
                            <el-table-column
                                prop="MBMC"
                                label="模板名称"
                                header-align="center"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                prop="MBMS"
                                label="描述"
                                header-align="center"
                                align="center"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="CZR"
                                label="操作人"
                                header-align="center"
                                align="center"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="CZSJ"
                                label="操作时间"
                                header-align="center"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                prop="CZ"
                                label="操作"
                                header-align="center"
                                align="center"
                            >
                                <template #default="scope">
                                    <el-button type="text" @click="deleteRow(scope.row,scope.$index,'MB')">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                </el-row>
                <div style="margin-top: 25px;color: red;font-weight: bolder">
                    <span >提示：不区分队伍时，请只选择企业模板</span>
                </div>
            </el-col>
            <el-col :span="13" style="margin-left: 25px">
                <el-row>
                    <div>
                        <span style="color: #c10606;float: left;margin-left: 15px;line-height: 35px;font-size: 17px">专业类别：</span>
                        <span style="float: right"> <el-button type="primary" style="margin-left:10px;margin-right: 25px" @click="addZylb()">添加</el-button></span>
                    </div>
                </el-row>
                <!--表格数据-->
                <el-row class="zhyy-list-tableArea">
                    <!--&lt;!&ndash; 表格 &ndash;&gt;-->
                    <el-table
                        class="customer-no-border-table"
                        :data="zyTableData"
                        border="1px"
                        width="100%"
                        :height="pageHeight"
                    >
                        <el-table-column
                            type="index"
                            :index="indexMethod"
                            label="序号"
                            align="center"
                            width="50"
                        ></el-table-column>
                        <el-table-column
                            prop="YWMC"
                            label="专业类别"
                            header-align="center"
                            align="center">
                            <el-table-column
                                prop="YJYWMC"
                                label="大类"
                                header-align="center"
                                align="center">
                            </el-table-column>
                            <el-table-column
                                prop="EJYWMC"
                                label="中类"
                                header-align="center"
                                align="center">
                            </el-table-column>
                            <el-table-column
                                prop="SJYWMC"
                                label="小类"
                                header-align="center"
                                align="center">
                            </el-table-column>
                        </el-table-column>
                        <el-table-column
                            prop="CZSJ"
                            label="操作"
                            header-align="center"
                            align="center"
                        >
                            <template #default="scope">
                                <el-button type="text" @click="deleteRow(scope.row,scope.$index,'ZY')">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-row>
            </el-col>
        </el-row>
        <el-row style="margin-top: 25px">
            <div>
                <span>
                    <el-button type="primary" style="margin-left:10px;margin-right: 25px" @click="reback()">返回</el-button>
                </span>
                <span>
                    <el-button type="primary" style="margin-left:10px;margin-right: 25px" @click="saveData()">保存</el-button>
                </span>
            </div>
        </el-row>
    <el-dialog
        title="引进模板选择"
        center
        v-model="zrmbdialog"
        v-if="zrmbdialog"
        :append-to-body="true"
        width="85%"
        top="5vh"
        style="margin-top: 0vh"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="onClose"
        class="abow_dialog"
    >
        <div class="el-dialog-div">
            <div style="text-align: center">
                <zrmbSelect v-if="zrmbdialog" @confirm="zrmbConfirm"></zrmbSelect>
                <el-button type="primary" @click="onClose" size="mini">
                    返回
                </el-button>
            </div>
        </div>
    </el-dialog>

    <el-dialog
        title="专业类别选择"
        center
        v-model="zylbdialog"
        v-if="zylbdialog"
        :append-to-body="true"
        width="85%"
        top="5vh"
        style="margin-top: 0vh"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="onClose"
        class="abow_dialog"
    >
        <div class="el-dialog-div">
            <div style="text-align: center">
                <zyxzSelect v-if="zylbdialog" @handleConfirm="zyxzConfirm"></zyxzSelect>
                <el-button type="primary" @click="onClose" size="mini">
                    返回
                </el-button>
            </div>
        </div>
    </el-dialog>
    </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import zrmbSelect from "./zrmbSelect";
import zyxzSelect from "./zy_select_list";

let emits = defineEmits(['close'])
let pageHeight = computed(() =>{
    return "calc(100vh - 250px)"
})
let params = reactive({
    mblxArry:[
        { value: '1', label: '企业' },
        { value: '2', label: '队伍' }
    ],
    ZT:'',
    LX:'WB',
    value:'',
    appUser:'',
    dwzt:'',
    dwEditable:false,
    model:'dwjdkh',
    //项目名称/编码
    inputXmmcbm : "",
    //工程类别取值
    inputGclb : "",
    //工程类别
    //项目年度
    xmnd: String(new Date().getFullYear()),
    // 当前页码
    currentPage: 1,
    // 每页的数据条数
    pageSize: 10,
    total: 0,
    recent: 0,
    zylbJsonArray:[],
    //行数据ID
    editRowId: null,
    clickArr: [],
    formLabelWidth: "100px",
    tableStyle: "width:100%;height:calc(100vh - 310px);",
    modelTemesMap: {},
    SJYWBM:'',
    ZYLB:"",
    KHBS:'',
    edit:'',
    khqj:[],
    KHMC:'',
    FPJDX:"",
    FJDLX:"",
    jyData: []
})
let zrmbTableData = reactive([])
let zyTableData = reactive([])
let zrmbdialog = ref(false)
let zylbdialog = ref(false)
let dwkhdialogVisible = ref(false)
let form = reactive({
    mbmc:"",
    mbms:"",
    mblx:""
})
let tableData = reactive([])
let multipleSelection = reactive([])

const zyxzConfirm = (row) =>{
    zyTableData.length = 0
    zyTableData.push(...row)
    zylbdialog.value = false
}
const deleteRow = (row,index,type) =>{
    if (type=='ZY'){
        zyTableData.splice(index, 1);
    }
    if (type=='MB'){
        zrmbTableData.splice(index, 1);
    }
}
const zrmbConfirm = (row) =>{
    zrmbdialog.value=false;
    zrmbTableData.length = 0;
    zrmbTableData.push(...row)
}
const saveData =() =>{
    if (zyTableData.length==0){
        ElMessage({
            message: '请维护专业信息后保存！',
            type: 'warning'
        })
        return
    }
    if (zrmbTableData.length==0){
        ElMessage({
            message: '请维护引进模板后保存！',
            type: 'warning'
        })
        return
    }
    let param={
        CZR:params.appUser.entUserName,
        CZRZH:params.appUser.userLoginName,
        zyTableData:zyTableData,
        zrmbTableData:zrmbTableData
    }
    axiosUtil.post('/sldwgl/mbgl/saveZyMbGx', param).then(res =>{
        if(res.data.meta.success){
            ElMessage({
                message: '保存成功！',
                type: 'success'
            })
            reback()
        }
    })
    // let result= await util.postJson('/sldwgl/mbgl/saveZyMbGx', params, this.model);
    // if (result.data.meta.message=='ok'){
    //     this.$message.success('保存成功！')
    //     reback()
    // }
}
const reback = () =>{
    $emit("close")
}
const addZrmb = () =>{
    zrmbdialog.value=true
}
const addZylb = () =>{
    zylbdialog.value=true
}
const pubRow = (index,row,status) =>{
    row.SHZT=status;
    axiosUtil.get('/sldwgl/dwkh/pubRow', row).then(res =>{
        if(res.data.meta.success){
            ElMessage({
                message: '操作成功',
                type: 'success'
            })
            getDataList()
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
    // let result= await util.get('/sldwgl/dwkh/pubRow', row, this.model);
    // if (result.data.meta.message=='ok'){
    //     this.$message.success("操作成功")
    //     this.getDataList()
    // }else {
    //     this.$message.error(result.data.meta.message)
    // }
}
const adddwkh = () =>{
    params.KHBS = util.newId();
    params.edit = true
    dwkhdialogVisible.value = true
}
const viewDwKh = (index,row) =>{
    params.khqj[0] = row.PJKSRQ
    params.khqj[1] = row.PJJSRQ
    params.SJYWBM = row.SCLBBM
    params.FPJDX = row.PJDX
    params.FJDLX = row.JDLX
    params.KHMC = row.KHMC
    params.edit = false
    params.KHBS = row.KHBS
    dwkhdialogVisible.value = true
}
//关闭对话框
const onClose = () => {
    dwkhdialogVisible.value = false;
    zrmbdialog.value = false;
    zylbdialog.value = false
}
const getAppUser = () =>{
    // this.appUser=await util.getAppUser();
}
/**
 * 序号
 */
const indexMethod = (index) => {
    return index + params.pageSize * (params.currentPage - 1) + 1;
}
/**
 * 页面数据条数改变时
 */
const handleSizeChange = (val) => {
    params.currentPage = 1;
    params.pageSize = val;
    getDataList();
}
/**
 * 翻页
 */
const handleCurrentChange = (val) => {
    params.currentPage = val;
    getDataList();
}
//分页多行变少行，点击翻页不刷新问题
const pageClick = e => {
    if (!tableData.length) {
        return false;
    }
    let dom = e.target;
    if (dom.className === "btn-next" || (dom.className === "el-icon el-icon-arrow-right" && dom.parentNode.className !== "btn-next disabled")) {
        params.currentPage += 1;
        params.currentPage >= Math.ceil(params.total / params.pageSize) ? (params.currentPage = Math.ceil(params.total / params.pageSize)) : params.currentPage;
    } else if ( dom.className === "btn-prev" || (dom.className === "el-icon el-icon-arrow-left" && dom.parentNode.className !== "btn-prev disabled")) {
        params.currentPage -= 1;
        params.currentPage <= 1 ? (params.currentPage = 1) : params.currentPage;
    } else if (dom.className === "el-icon more btn-quicknext el-icon-d-arrow-right") {
        params.currentPage = Math.ceil(params.total / params.pageSize);
    } else if (dom.className === "el-icon more btn-quickprev el-icon-d-arrow-left") {
        params.currentPage = 1;
    } else if (dom.className === "number") {
        params.currentPage = Number(dom.innerHTML);
    } else {
        return false;
    }
    getDataList();
}
const querySqlbData = () => {
    let param = {
        zymc : params.inputZymc ,
        gclbdm:params.gclbdm,
        CBSDWBS:params.cbsdwbs
    };
    axiosUtil.get('/sldwgl/cbsDwxx/whDwzy', param).then(res =>{
        if(res.data.meta.success){
            let rows = res.data.data
            params.jyData = rows
            for (let i = 0; i <rows.length ; i++) {
                rows[i].value=rows[i].YWBM
                rows[i].label=rows[i].YWMC
            }
            let treeData = transData(rows, 'YWBM', 'PYWBM', 'children');
            params.zylbJsonArray = treeData;
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
    // let result = await util.get('/sldwgl/cbsDwxx/whDwzy', param, this.model);
    // if(result.data.meta.success){
    //     var rows = result.data.data;
    //     this.jyData=rows
    //     for (let i = 0; i <rows.length ; i++) {
    //         rows[i].value=rows[i].YWBM
    //         rows[i].label=rows[i].YWMC
    //     }
    //     var treeData = this.transData(rows, 'YWBM', 'PYWBM', 'children');

    //     this.zylbJsonArray = treeData;
    // }else{
    //     this.$message({type: "warning", message: result.data.meta.message});
    // }
}
/**
 * json格式转树状结构
 * @param   {json}      json数据
 * @param   {String}    id的字符串
 * @param   {String}    父id的字符串
 * @param   {String}    children的字符串
 * @return  {Array}     数组
 */
const transData = (a, idStr, pidStr, childrenStr) =>{
    let r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
    for(; i < len; i++){
        a[i]['isparent'] = false;
        hash[a[i][id]] = a[i];
    }
    for(; j < len; j++){
        let aVal = a[j], hashVP = hash[aVal[pid]];
        if(hashVP){
            !hashVP[children] && (hashVP[children] = []);
            hashVP[children].push(aVal);
        }else{
            r.push(aVal);
        }
    }
    return r;
}
const setIsParent = (arr) => {
    for(let j=0; j < arr.length; j++){
        if(arr[j].children && arr[j].children.length > 0){
            arr[j].isparent = true;
            setIsParent(arr[j].children);
        }
    }
}
const handleChangeLb = (value) => {
    params.ZYLB=value
    params.SJYWBM=value[2];
    getDataList();
}
onMounted(() =>{
    getAppUser()
})
</script>

<style scoped>
    ::v-deep .el-cascader__dropdown{
        height: 250px;
    }

    .dialog-footer {
        text-align: center;
    }
    .el-cascader-menu__wrap{
        height: 250px;
    }

    body .el-table th.gutter {
        display: table-cell !important;
    }
</style>