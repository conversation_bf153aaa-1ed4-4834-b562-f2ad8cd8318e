<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
          <el-input v-model="listQuery.qymc" placeholder="请输入企业名称"></el-input>
      </el-col>
      <el-col :span="5" class="grid-cell">
          <el-input v-model="listQuery.dwmc" placeholder="请输入队伍名称"></el-input>
      </el-col>
      <el-col :span="10" class="grid-cell">
          <el-button type="primary" @click="processSearch">查询</el-button>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          highlight-current-row
          ref="table"
          size="default"
          height="calc(100vh - 250px)"
          border
          :data="tableData"
          v-loading="listLoading"
      >
        <el-table-column type="index" width="60" :index="indexMethod" label="序号" align="center"/>
        <el-table-column label="承包商名称" min-width="150" prop="CBSDWQC" header-align="center" align="left">
        </el-table-column>
        <el-table-column label="队伍名称" min-width="150" prop="DWMC" header-align="center" align="left">
        </el-table-column>
        <el-table-column label="专业名称" min-width="150" prop="ZYMC" header-align="center" align="left">
        </el-table-column>
        <el-table-column label="操作" min-width="60" header-align="center" align="center">
          <template #default="{ row }">
              <el-button :loading="deling.has(row.DWYWID)" class="lui-table-button" @click="addDwzysc(row)">
                删除专业
              </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
  </el-form>
</template>
<script>
import {defineComponent, onMounted, reactive, toRefs} from "vue";
import {getTeamreslutGetProDetails} from '@src/api/sccbsgl.js'
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {mixin} from "@core";
import {ElLoading, ElMessage} from "element-plus";
import TabFun from "@src/lib/tabFun"
export default defineComponent({
  name: 'dwzyscList',
  components: {},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: VSAuth.getAuthInfo().permission,
      listQuery:{
        page: 1,
        size: 10,
        qymc: '',
        dwmc: '',
      },
      listLoading: false,
      tableData: [],
      total: 0,
      deling: new Set()
    })
    onMounted(() => {
      initListData();
    })

    // 初始化数据
    const initListData = () => {
      const params = {
        ...state.listQuery,
      }
      state.listLoading=true
      axiosUtil.get('/backend/sccbsgl/dwxxbg/queryDwzyscList', params).then((res) => {
        state.tableData = res.data.list;
        state.total = res.data.total;
        state.listLoading=false;
      });
    }

    const addDwzysc = (row) => {
      emit('comfirm',row);
    }

    const handleSizeChange = (val) => {
      state.listQuery.page = 1;
      state.listQuery.size = val;
      initListData();
    };
    const handleCurrentChange = (val) => {
      state.listQuery.page = val;
      initListData();
    };
    const indexMethod = (index) => {
      return index + state.listQuery.size * (state.listQuery.page - 1) + 1;
    };
    // 条件查询， 先把页数改为1
    const processSearch = () => {
      state.listQuery.page = 1;
      initListData();
    };

    return {
      ...toRefs(state),
      handleSizeChange,
      handleCurrentChange,
      indexMethod,
      processSearch,
      addDwzysc
    }
  }

})
</script>

<style scoped>

</style>
