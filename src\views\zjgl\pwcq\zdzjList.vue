<template>
  <el-form :model="listQuery" ref="vForm" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <div style="margin-bottom: 10px">
      <el-radio-group v-model="ZJLX" fill="#409EFF" @change="changeLX">
        <el-radio-button label="NB">内部专家</el-radio-button>
        <el-radio-button label="WB">外部专家</el-radio-button>
      </el-radio-group>
    </div>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="" prop="name">
          <el-input v-model="listQuery.XM" type="text" placeholder="请输入姓名" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell" style="display: flex;gap: 10px">
        <div class="ml10 static-content-item">
          <el-button type="primary" @click="getDataList"><el-icon><Search/></el-icon>查询</el-button>
        </div>
        <div class="button_folat static-content-item" v-if="ZJLX==='WB'">
          <el-button type="success" @click="addData">添加</el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="primary" @click="submit">确定</el-button>
        </div>
        <div class="button_folat static-content-item">
          <el-button type="warning" @click="clear">清空</el-button>
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table ref="dataTable" :data="tableData" height="500px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column type="index" width="60" fixed="left" align="center">
          <template #default="{$index,row}">
            <el-checkbox-group v-model="checkList">
              <el-checkbox :label="row" :disabled="fitterList.includes(row.ZJBS)"><br></el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
        <el-table-column prop="XM" label="姓名" align="center" width="100"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="SFZH" label="身份证号" align="center" width="200"
                         :show-overflow-tooltip="true" v-if="ZJLX==='WB'">
        </el-table-column>
        <el-table-column prop="GZDWMC" label="工作单位" align="left" header-align="center" min-width="100"
                         :show-overflow-tooltip="true">
          <template #default="{row}">
            {{ZJLX==='NB' ? row.GZDWMC : row.GZDW}}
          </template>
        </el-table-column>
        <el-table-column prop="ZJLBMC" label="专家类别" align="center"
                         :show-overflow-tooltip="true" width="100" v-if="ZJLX==='NB'">
        </el-table-column>
        <el-table-column prop="XCSZYMC" :label="ZJLX==='NB' ? '现从事专业' : '专业'" align="left" header-align="center"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="RKZJBXX.SBZY" label="申报专业" align="left" header-align="center"
                         :show-overflow-tooltip="true" v-if="ZJLX==='NB'">
        </el-table-column>
        <el-table-column prop="RKZJBXX.LXDH" label="手机号" align="center" width="180"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="RYZH" label="统一账号" align="center" width="180"
                         :show-overflow-tooltip="true" v-if="ZJLX==='WB'">
        </el-table-column>
        <el-table-column prop="CJSJ" :label="ZJLX==='NB' ? '办理时间' : '创建时间'" align="center" width="180"
                         :show-overflow-tooltip="true">
        </el-table-column>
      </el-table>
      <el-pagination background
                     v-model:current-page="listQuery.page"
                     v-model:page-size="listQuery.size" :page-sizes="[10,20,50,100]"
                     layout="total, prev, pager, next, sizes"
                     class="lui-pagination"
                     @size-change="pageOrSizeChange"
                     @current-change="pageOrSizeChange"
                     :total="total">
      </el-pagination>
    </div>
  </el-form>

  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="false"
      v-if="dialogVisible"
      v-model="dialogVisible"
      title="评委维护"
      @closed="closeForm"
      z-index="1000"
      width="1000px">
    <div>
      <zdzjEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
    </div>
  </el-dialog>

</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
}
  from 'vue'

import { Search, Upload, Plus} from '@element-plus/icons-vue'
import axiosUtil from "@lib/axiosUtil";
import zdzjEdit from "@views/zjgl/pwcq/zdzjEdit";
import comFun from "@lib/comFun";

export default defineComponent({
  components: {Search,Plus,Upload,zdzjEdit},
  props: {
    fitterList:{
      type:Array,
      default: []
    }
  },
  setup(props,{emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      checkList:[],
      rules: {},
      total:0,
      ZJLX: 'NB',

      params: {},
      dialogVisible: false,
    })
    const getDataList = () => {
      let params={
        ...state.listQuery,
        ZJLX: state.ZJLX
      }
      axiosUtil.get(`/backend/zjgl/pwcqgl/queryZj`, params).then((res) => {
        state.tableData=res.data.list
        state.total = res.data.total
      });
    }
    const pageOrSizeChange = () => {
      getDataList()
    }

    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }

    const submit = () => {
      console.log(state.checkList)
      emit('getData',state.checkList)
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const clear = () => {
      emit('clearData')
    }

    const closeForm = () => {
      getDataList()
      state.dialogVisible=false
    }

    const changeLX = () => {
      state.listQuery.page=1
      state.listQuery.size=10
      state.tableData=[]
      getDataList()
    }



    onMounted(()=>{
      getDataList()
    })
    return {
      ...toRefs(state),
      getDataList,
      pageOrSizeChange,
      submit,
      clear,
      indexMethod,
      closeForm,
      addData,
      changeLX
    }
  }
})

</script>

<style scoped>
.grid-cell .el-input{
  max-width: 250px;
}
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: white;
}
</style>
