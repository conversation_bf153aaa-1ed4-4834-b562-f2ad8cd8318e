<template>
  <el-form class="lui-page" label-position="left" size="default">
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="8" v-if="data.zybms.length > 0">
        <el-form-item label="当前专业">
                    <span style="color: red">
                        {{ data.zybms[data.zybms.length - 1]['name'] }}
                    </span>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <div style="display:flex;">
          <el-button type="primary" v-if="data.zybms.length > 0" @click="goParent">返回上一层</el-button>
          <el-button type="primary" @click="exportData"><el-icon><Upload/></el-icon>导出</el-button>
        </div>
      </el-col>
    </el-row>
    <div>
      <el-table
          class="lui-table"
          highlight-current-row
          size="default"
          height="calc(100vh - 250px)"
          ref="table"
          fit
          border
          :data="data.tableData"
          v-loading="tableLoading">
        <EleProTableColumn
            v-for="prop in data.tableColumn"
            :col="prop"
            :key="prop.columnKey">
          <template #zy="{ row }">
            <el-button v-if="row.ZYBM != 'HJ'" type="text" @click="goChildren(row)">{{ row.ZYMC }}</el-button>
            <span v-else>{{ row.ZYMC }}</span>
          </template>
          <template #team="{ row }">
            <el-button v-if="row.ZYBM != 'HJ'" type="text" @click="goTeam(row,null)">{{ row.DW_COUNT }}</el-button>
            <span v-else>{{ row.DW_COUNT }}</span>
          </template>
          <template #CBS="{ row }">
            <el-button v-if="row.ZYBM != 'HJ'" type="text" @click="goTeam(row,'CBS')">{{ row.CBS }}</el-button>
            <span v-else>{{ row.CBS }}</span>
          </template>
          <template #SHN="{ row }">
            <el-button v-if="row.ZYBM != 'HJ'" type="text" @click="goTeam(row,'SHN')">{{ row.SHN }}</el-button>
            <span v-else>{{ row.SHN }}</span>
          </template>
          <template #QTGYQY="{ row }">
            <el-button v-if="row.ZYBM != 'HJ'" type="text" @click="goTeam(row,'QTGYQY')">{{ row.QTGYQY }}</el-button>
            <span v-else>{{ row.QTGYQY }}</span>
          </template>
          <template #SYDW="{ row }">
            <el-button v-if="row.ZYBM != 'SYDW'" type="text" @click="goTeam(row,'SYDW')">{{ row.SYDW }}</el-button>
            <span v-else>{{ row.SYDW }}</span>
          </template>
        </EleProTableColumn>
      </el-table>
      <el-pagination
          background
          v-model:current-page="data.queryForm.page"
          v-model:page-size="data.queryForm.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="data.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
  </el-form>
</template>
<script setup>
import {onMounted, onUnmounted, reactive, ref} from "vue";
import {runtimeCfg, eventBus, mixin} from '@src/assets/core/index';
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getReportDwtjhz, getReportDwtjhzExport} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam, getCommonSelectDMB} from '@src/api/cbsxx.js'
import { useRoute } from 'vue-router';
const route = useRoute();
import VSAuth from "@src/lib/vsAuth";
import tabFun from "@src/lib/tabFun";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import axiosUtil from "@lib/axiosUtil";

const data = reactive({
  total: 0,
  zybms: [],
  queryForm: {
    zybm: null,
    qygs: null,
    dwzt: null,
    page: 1,
    size: 10
  },
  systemUnits: [],
  teamStatus: [],
  currentUser: {},
  tableData: [],
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 60,
      align: "center"
    },
    {
      label: "专业",
      prop: "ZYMC",
      align: "left",
      showOverflowTooltip: true,
      slot: 'zy'
    },
    {
      label: "合计",
      prop: "DW_COUNT",
      align: "left",
      showOverflowTooltip: true,
      slot: 'team'
    },
  ],
  entType: {},
  entUnit: {},
})

const {vsuiEventbus} = mixin()
const goTeam = (row, qylx) => {
  // tabFun.getTabsLength();
  // tabFun.closeTabByPath('/query-analysis/dwmx').then().catch(e=>{
  //     console.log(e)
  // })
  vsuiEventbus.emit('reloadTeamList', {zybm: row.ZYBM, qylx: qylx})
  tabFun.addTabByRoutePath('企业信息查询', '/query-analysis/qymx', {zybm: row.ZYBM, qylx: qylx},);
}

const exportData = () => {
  if (data.zybms.length == 0) {
    data.queryForm.zybm = null;
  } else {
    data.queryForm.zybm = data.zybms[data.zybms.length - 1].value;
  }
  let column = [[
    { field: 'ZYMC', title: '专业'},
    { field: 'DW_COUNT', title: '合计'},
    { field: 'CBS', title: '承包商'},
    { field: 'SHN', title: '石化内'},
    { field: 'QTGYQY', title: '其他国有企业'},
    { field: 'SYDW', title: '事业单位'},
  ]]
  let params = {
    title: "承包商统计汇总",
    name: "承包商统计汇总",
    params: data.queryForm,
    url: '/excel/cbstjhzExport',
  }
  params.column = column
  axiosUtil.exportExcel('/backend/excelExport/magicExcel', params)
}

const goParent = (row) => {
  data.zybms.splice(data.zybms.length - 1, 1);
  query();
}
const goChildren = (row) => {
  data.zybms.push({
    name: row.ZYMC,
    value: row.ZYBM
  })
  query();
}
const resetQuery = () => {
  data.queryForm = {
    qymc: null,
    qygs: null,
    dwzt: null,
    page: 1,
    size: 10
  }
}
const tableLoading = ref(false);
const query = () => {
  if (data.zybms.length == 0) {
    data.queryForm.zybm = null;
  } else {
    data.queryForm.zybm = data.zybms[data.zybms.length - 1].value;
  }
  tableLoading.value = true;
  getReportDwtjhz(data.queryForm).then(res => {
    console.log(res)
    let info = {
      DW_COUNT: 0,
      CBS: 0,
      SHN: 0,
      QTGYQY: 0,
      SYDW: 0,
      ZYMC: '合计',
      ZYBM: 'HJ'

    }
    res.data.forEach(x => {
      info.DW_COUNT += x.DW_COUNT;
      info.CBS += x.CBS;
      info.SHN += x.SHN;
      info.QTGYQY += x.QTGYQY;
      info.SYDW += x.SYDW;
    })
    let result = [info];
    result = result.concat(res.data)
    data.tableData = result
    data.total = res.data.total

  }).catch((err) => {
    console.log(err);
  }).finally(() => {
    tableLoading.value = false
  })

}

const getUserInfo = () => {
  let user = VSAuth.getAuthInfo().permission
  getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
    data.currentUser = res.data;
  })
}

const handleSizeChange = (val) => {
  data.queryForm.size = val;
  query();
}
const handleCurrentChange = (val) => {
  data.queryForm.page = val;
  query();
}

const comBelong = () => {
  getCommonSelectDMB({DMLBID: 'DWXTGS'}).then(res => {
    res.data.forEach(x => {
      data.tableColumn.push(
          {
            label: x.DMMC,
            prop: x.DMXX,
            align: "center",
            showOverflowTooltip: true,
            slot: x.DMXX
          }
      )
    })


  })
}

const getTeamStatus = () => {
  getCommonSelectDMB({DMLBID: 'DWZT'}).then(res => {
    data.teamStatus = res.data
  })
}

const getEntType = () => {
  getCommonSelectDMB({DMLBID: 'QYLX'}).then(res => {
    res.data.forEach(x => {
      data.entType[x.DMXX] = x.DMMC
    })
  })
}


onMounted(() => {
  getEntType();
  comBelong();
  getTeamStatus();
  getUserInfo();
  // vsuiEventbus.on('reloadTeamList',query);

  if(route.query.ZYBM && route.query.ZYMC){
    goChildren(route.query)
  }else{
    query();
  }
})
onUnmounted(() => {
  // timer.value = null;
  // vsuiEventbus.off('reloadTeamList',query);
})
</script>

<style scoped>
.header {
  padding: 10px 10px 0;
  height: 50px;
  line-height: 50px;
}

.main {
  height: calc(100% - 155px);
  padding: 10px;
}

.footer {
  height: 100px;
  line-height: 100px;
}
</style>
