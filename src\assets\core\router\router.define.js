import {customCheck} from './router.auth';
import {pageOfficeRouter} from './router.pageoffice';
const routesUnUseVsuiVue = [
    {
        path: '/login',
        name: 'login',
        component: () => import('../../../views/login.vue'),
        //component: () => import('../../../views/login_xieyun.vue'),
        meta: {title: '登录页面'}
    },
    ...pageOfficeRouter,
    {
        path: '/loginxieyun',
        name: 'loginxieyun',
        component: () => import('../../../views/login.vue'),
        meta: {title: '谐云登录页面'}
    },
    {
        path: '/WebPortal',
        name: 'WebPortal',
        component: () => import('../../../views/WebPortal/index.vue'),
        meta: {title: '门户'}
    },
    {
        path: '/403',
        name: '403',
        component: () => import('../../../views/403.vue'),
        meta: {title: '403'}
    },
    {
        path: '/error',
        name: 'error',
        component: () => import('../../../views/error.vue'),
        meta: {title: 'error'}
    },
]

const routesUseVsuiVue = [
    {
        path: "/",
        name: "root",
        redirect: "/dashboard"
    },
    {
        path: "dashboard",
        name: "dashboard",
        component: () => import("@views/cbs/cbsyj/cbsshList.vue"),
        meta: {title: "工作台", permission: customCheck}
    },
    {
        path: "gztIndex",
        name: "gztIndex",
        component: () => import("@views/cbs/cbsyj/cbsshList2.vue"),
        meta: {title: "工作台", permission: customCheck}
    },

    {
        path: "gztold",
        name: "gztold",
        component: () => import("@views/cbs/cbsyj/cbsshList3.vue"),
        meta: {title: "工作台", permission: customCheck}
    },



    /** 承包商 */
    {
        path: 'contractors',
        name: 'contractors',
        component: () => import ('@views/component.vue'),
        meta: {title: '承包商', },
        children: [
            {
                path: "cbsjbxx",
                name: "cbsjbxx",
                component: () => import("../../../views/cbs/cbsyj/cbsxx/index.vue"),
                meta: {title: "承包商基本信息", permission: customCheck}
            },
            {
                path: "zrfqIndex",
                name: "zrfqIndex",
                component: () => import("@views/cbs/cbsyj/zrfqIndex.vue"),
                meta: {title: "承包商准入", permission: customCheck}
            },
            {
                path: "cbsywdhPage",
                name: "                     ",
                component: () => import("@views/cbs/ywdh/cbsywdhPage.vue"),
                meta: {title: "承包商业务导航", permission: customCheck}
            },
            {
                path: "zygl",
                name: "zygl",
                component: () => import("@views/cbs/zygl/index.vue"),
                meta: {title: "专业管理", permission: customCheck}
            },
            {
                path: "xzzy",
                name: "xzzy",
                component: () => import("@views/cbs/cbsyj/xzzy/xzzy.vue"),
                meta: {title: "选择专业", permission: customCheck}
            },
            {
                path: "xzzyIndex",
                name: "xzzyIndex",
                component: () => import("@views/cbs/cbsyj/xzzy/index.vue"),
                meta: {title: "选择专业", permission: customCheck}
            },
            {
                path: "xzqy",
                name: "xzqy",
                component: () => import("@views/cbs/cbsyj/xzzy/xzqy.vue"),
                meta: {title: "选择区域", permission: customCheck}
            },
            {
                path: "addTeamList",
                name: "addTeamList",
                component: () => import("@views/cbs/cbsyj/xzzy-zx/teamList.vue"),
                meta: {title: "增项队伍列表", permission: customCheck}
            },
            {
                path: "teamReviewList",
                name: "teamReviewList",
                component: () => import("@views/cbs/cbsyj/xzzy-zx/teamReviewList.vue"),
                meta: {title: "复审队伍列表", permission: customCheck}
            },

            {
                path: "cbsjbxxIndex",
                name: "cbsjbxxIndex",
                component: () => import("@views/cbs/cbsyj/index.vue"),
                meta: {title: "承包商基本信息", permission: customCheck}
            },
            {
                path: "cbsjbxxAdminIndex",
                name: "cbsjbxxAdminIndex",
                component: () => import("@views/cbs/cbsyj/index.vue"),
                meta: {title: "承包商基本信息", permission: customCheck}
            },
            {
                path: "cbszrList",
                name: "cbszrList",
                component: () => import("@views/cbs/cbsyj/cbszszrList.vue"),
                meta: {title: "承包商准入列表", permission: customCheck}
            },
            {
                path:"cbsshList",
                name:"cbsshList",
                component: () => import("@views/cbs/cbsyj/cbsshList.vue"),
                meta: {title: "承包商审核", permission: customCheck}
            },
            {
                path:"cbsshCheck",
                name:"cbsshCheck",
                component: () => import("@views/cbs/cbsyj/cbsshCheck.vue"),
                meta: {title: "承包商审核查看页", permission: customCheck}
            },
            {
                path: "dwxx",
                name: "dwxx",
                component: () => import("@views/cbs/cbsyj/dwxx.vue"),
                meta: {title: "队伍信息", permission: customCheck}
            },
            {
                path: "teamList",
                name: "teamList",
                component: () => import("@views/cbs/cbsyj/teamList.vue"),
                meta: {title: "队伍信息变更列表", permission: customCheck}
            },
            {
                path: "zjdwgl",
                name: "zjdwgl",
                component: () => import("@views/cbs/cbsyj/zjdwgl.vue"),
                meta: {title: "队伍信息", permission: customCheck}
            },
            {
                path: "yrsqxx",
                name: "yrsqxx",
                component: () => import("@views/cbs/cbsyj/yrsqxx.vue"),
                meta: {title: "引入申请信息", permission: customCheck}
            },
            {
                path: "yrsqxxIndex",
                name: "yrsqxxIndex",
                component: () => import("@views/cbs/cbsyj/yrsqxxIndex.vue"),
                meta: {title: "引入申请信息", permission: customCheck}
            },

            {
                path: "cbsrkList",
                name: "cbsrkList",
                component: () => import("@views/cbs/cbsrk/cbsrkList.vue"),
                meta: {title: "承包商入库列表", permission: customCheck}
            },
            {
                path: "cbsrkAdminList",
                name: "cbsrkAdminList",
                component: () => import("@views/cbs/cbsrkAdmin/cbsrkAdminList.vue"),
                meta: {title: "承包商管理列表", permission: customCheck}
            },
            {
                path: "fbsglList",
                name: "fbsglList",
                component: () => import("@views/cbs/fbsgl/fbsglList.vue"),
                meta: {title: "分包商管理列表", permission: customCheck}
            },
            {
                path: "cbszrQuery",
                name: "cbszrQuery",
                component: () => import("@views/cbs/cbscx/cbszrQuery.vue"),
                meta: {title: "承包商准入信息查询", permission: customCheck}
            },
            {
                path: "bgdwzyEdit",
                name: "bgdwzyEdit",
                component: () => import("@views/cbs/cbsbg/bgdwzyEdit.vue"),
                meta: {title: "承包商变更信息", permission: customCheck}
            },
            {
                path: "dwxxEdit",
                name: "dwxxEdit",
                component: () => import("@views/cbs/cbsbg/commonTab/dwxx/dwxxEdit.vue"),
                meta: {title: "队伍变更信息", permission: customCheck}
            },
            {
                path: "qybgEdit",
                name: "qybgEdit",
                component: () => import("@views/cbs/cbsbg/cbsqybg/qybgEdit.vue"),
                meta: {title: "区域变更信息", permission: customCheck}
            },
            {
                path: "cbsblList",
                name: "cbsblList",
                component: () => import("@src/views/cbs/cbsbl/cbsblList.vue"),
                meta: {title: "承包商信息补录(暂时废弃)", permission: customCheck}
            },
            {
                path: "cbsblNewList",
                name: "cbsblNewList",
                component: () => import("@views/cbs/cbsrk/cbsblList.vue"),
                meta: {title: "存量承包商入库", permission: customCheck}
            },
            // 队伍信息变更
            {
                path: "dwbgEdit",
                name: "dwbgEdit",
                component: () => import("@views/cbs/cbsbg/cbsdwbg/dwbgEdit.vue"),
                meta: {title: "队伍变更信息", permission: customCheck}
            },
            // 承包商信息变更
            {
                path: "cbsbgEdit",
                name: "cbsbgEdit",
                component: () => import("@views/cbs/cbsbg/cbsdwbg/cbsbgEdit.vue"),
                meta: {title: "承包商变更信息", permission: customCheck}
            },
            // 队伍专业删除
            {
                path: "dwzyscList",
                name: "dwzyscList",
                component: () => import("@views/cbs/cbsbg/cbsdwbg/dwzyscList.vue"),
                meta: {title: "队伍专业删除", permission: customCheck}
            },
            {
                path: "dwzyscEdit",
                name: "dwzyscEdit",
                component: () => import("@views/cbs/cbsbg/cbsdwbg/dwzyscEdit.vue"),
                meta: {title: "承包商变更信息", permission: customCheck}
            },
        ]
    },
    /** 主数据管理 */
    {
        path: 'mainData',
        name: 'mainData',
        meta: {title: '主数据管理', permission: customCheck},
        component: () => import ('@views/component.vue'),
        children: [
            {
                path: "zjdwgl",
                name: "zjdwgl",
                component: () => import("@views/cbs/cbsyj/zjdwgl.vue"),
                meta: {title: "子级单位管理", permission: customCheck}
            },
        ]
    },
    /** 队伍日常管理 */
    {
        path: 'teamManage',
        name: 'teamManage',
        meta: {title: '队伍日常管理', permission: customCheck},
        component: () => import ('@views/component.vue'),
        children: [
            {
                path: "dwzybg",
                name: "dwzybg",
                component: () => import("@views/cbs/teammanage/dwzybg.vue"),
                meta: {title: "队伍专业变更", permission: customCheck}
            },
            {
                path: "dwztbg",
                name: "dwztbg",
                component: () => import("@views/cbs/teammanage/dwztbg.vue"),
                meta: {title: "队伍状态变更", permission: customCheck}
            },
            {
                path: "xbcbsrcgl",
                name: "xbcbsrcgl",
                component: () => import('@views/cbs/xbCbsrcgl/index.vue'),
                meta: {title: "承包商日常管理", permission: customCheck}
            }
        ]
    },
    /** 队伍超期预警 */
    {
        path: 'overtime-warning',
        name: 'overtime-warning',
        meta: {title: '队伍超期预警', permission: customCheck},
        component: () => import ('@views/component.vue'),
        children: [
            {
                path: "cqyjlb",
                name: "cqyjlb",
                component: () => import("@views/cbs/overtimewarning/cqyjlb.vue"),
                meta: {title: "超期预警列表", permission: customCheck}
            }
        ]
    },
    /** 查询分析 */
    {
        path: 'query-analysis',
        name: 'query-analysis',
        meta: {title: '查询分析', permission: customCheck},
        component: () => import ('@views/component.vue'),
        children: [
            {
                path: "qymx",
                name: "qymx",
                component: () => import("@views/cbs/queryanalysis/qymx.vue"),
                meta: {title: "企业明细", permission: customCheck}
            },
            {
                path: "dwmx",
                name: "dwmx",
                component: () => import("@views/cbs/queryanalysis/dwmx.vue"),
                meta: {title: "队伍明细", permission: customCheck}
            },
            {
                path: "dwtjhz",
                name: "dwtjhz",
                component: () => import("@views/cbs/queryanalysis/dwtjhz.vue"),
                meta: {title: "队伍统计汇总", permission: customCheck}
            },
            {
                path: "dwcqyj",
                name: "dwcqyj",
                component: () => import("@views/cbs/queryanalysis/dwcqyj.vue"),
                meta: {title: "队伍超期预警", permission: customCheck}
            },
            {
                path: "cbsqyhz",
                name: "cbsqyhz",
                component: () => import("@views/cbs/queryanalysis/cbsqyhz/cbsqyhz.vue"),
                meta: {title: "队伍区域汇总", permission: customCheck}
            },
            {
                path: "qydjzsQuery",
                name: "qydjzsQuery",
                component: () => import("@views/cbs/queryanalysis/qydjzs/qydjzsQuery.vue"),
                meta: {title: "企业登记证书", permission: customCheck}
            },
            {
                path: "cbsqsjQuery",
                name: "cbsqsjQuery",
                component: () => import("@views/cbs/queryanalysis/cbsqsjck/cbsqsjQuery.vue"),
                meta: {title: "承包商全部数据查询", permission: customCheck}
            },
            // 承包商档案查询
            {
                path: "cbsdacxList",
                name: "cbsdacxList",
                component: () => import("@views/cbs/queryanalysis/cbsda/cbsdacxList.vue"),
                meta: {title: "承包商档案查询", permission: customCheck}
            }
        ]
    },
    /** 模板管理 */
    {
        path: 'templateManagement',
        name: 'templateManagement',
        component: () => import ('@views/component.vue'),
        meta: {title: '模板管理', permission: customCheck},
        children: [
            {
                path: "zzxx",
                name: "zzxx",
                component: () => import("@views/cbs/templateManagement/DataTemplateManagement/zzxx/zzxx.vue"),
                meta: {title: "资质信息", permission: customCheck}
            },
            {
                path: "xkxx",
                name: "xkxx",
                component: () => import("@views/cbs/templateManagement/DataTemplateManagement/xkxx/xkxx.vue"),
                meta: {title: "许可信息", permission: customCheck}
            },
            {
                path: "txzs",
                name: "txzs",
                component: () => import("@views/cbs/templateManagement/DataTemplateManagement/txzs/txzs.vue"),
                meta: {title: "体系信息", permission: customCheck}
            },
            {
                path: "ryxx",
                name: "ryxx",
                component: () => import("@views/cbs/templateManagement/DataTemplateManagement/ryxx/ryxx.vue"),
                meta: {title: "人员信息", permission: customCheck}
            },
            {
                path: "sbxx",
                name: "sbxx",
                component: () => import("@views/cbs/templateManagement/DataTemplateManagement/sbxx/sbxxEdit.vue"),
                meta: {title: "设备信息", permission: customCheck}
            },
            {
                path: "yjxx",
                name: "yjxx",
                component: () => import("@views/cbs/templateManagement/DataTemplateManagement/yjxx/yjxx.vue"),
                meta: {title: "业绩信息", permission: customCheck}
            },
            {
                path: "sqwtxx",
                name: "xksqwtxxxx",
                component: () => import("@views/cbs/templateManagement/DataTemplateManagement/sqwtxx/sqwtxx.vue"),
                meta: {title: "授权委托信息", permission: customCheck}
            },
            {
                path: "yjmbgl",
                name: "yjmbgl",
                component: () => import("@views/cbs/templateManagement/ImportTemplateManagement/yjmbgl/yjmbgl.vue"),
                meta: {title: "引进模板管理", permission: customCheck}
            },

            {
                path: "zyyjmbgl",
                name: "zyyjmbgl",
                component: () => import("@views/cbs/templateManagement/ImportTemplateManagement/zyyjmbgl/zyyjmbgl.vue"),
                meta: {title: "专业引进模板管理", permission: customCheck}
            },
        ]
    }

]

//信息发布
const xxfbRoute = {
    path: 'xxfb',
    name: 'xxfb',
    component: () => import ('@views/component.vue'),
    meta: {title: '信息发布', permission: customCheck},
    children: [
        {
            path: 'tzggList',
            name: 'tzggList',
            component: () => import ('@views/xxfb/tzgg/tzgg-list.vue'),
            meta: {title: '信息发布-通知公告', permission: customCheck}
        }
    ]
}
const workflowRoute = {
    path: 'workflow',
    name: 'workflow',
    component: () => import ('@views/component.vue'),
    meta: {title: '流程节点', permission: customCheck},
    children: [
        {
            path: 'lcjdyhpzList',
            name: 'lcjdyhpzList',
            component: () => import ('@views/workflow/lcjdpz/lcjdyhpzList.vue'),
            meta: {title: '用户角色配置', permission: customCheck}
        },
        {
            path: 'userList',
            name: 'userList',
            component: () => import ('@views/user/userList.vue'),
            meta: {title: '用户查询', permission: customCheck}
        },
        {
            path: 'lcjkList',
            name: 'lcjkList',
            component: () => import ('@views/workflow/lcjkList.vue'),
            meta: {title: '流程监控', permission: customCheck}
        },

        {
            path: 'zhpz',
            name: 'zhpz',
            component: () => import ('@views/xtgl/zhpz/userList.vue'),
            meta: {title: '人员管理', permission: customCheck}
        },
        {
            path: 'zzjgpz',
            name: 'zzjgpz',
            component: () => import ('@views/xtgl/zzjgpz/orgList.vue'),
            meta: {title: '组织机构配置', permission: customCheck}
        },

        {
            path: 'cdpz',
            name: 'cdpz',
            component: () => import ('@views/xtgl/cdpz/resList.vue'),
            meta: {title: '菜单配置', permission: customCheck}
        },
        {
            path: 'jspz',
            name: 'jspz',
            component: () => import ('@views/xtgl/jspz/roleList.vue'),
            meta: {title: '角色配置', permission: customCheck}
        },
        {
            path: 'wjgl',
            name: 'wjgl',
            component: () => import ('@views/xtgl/wjgl/wjglList.vue'),
            meta: {title: '文件管理', permission: customCheck}
        },
        {
            path: 'lcjdrypz',
            name: 'lcjdrypz',
            component: () => import ('@views/workflow/lcjdpz/lcjdrypzList.vue'),
            meta: {title: '流程节点人员配置', permission: customCheck}
        },
        {
            path: 'fgldpz',
            name: 'fgldpz',
            component: () => import ('@views/xtgl/fgldpz/fgldpzList.vue'),
            meta: {title: '分管领导配置', permission: customCheck}
        },
        {
            path: 'dmbpz',
            name: 'dmbpz',
            component: () => import ('@views/xtgl/dmbpz/dmbpzList.vue'),
            meta: {title: '代码表配置', permission: customCheck}
        },
        {
            path: 'mmxg',
            name: 'mmxg',
            component: () => import ('@views/xtgl/mmxg/mmxgPage.vue'),
            meta: {title: '密码修改', permission: customCheck}
        },
		{
            path: 'orgnaChange',
            name: 'orgnaChange',
            component: () => import ('@views/xtgl/fgldpz/orgnaChange.vue'),
            meta: {title: '单位切换', permission: customCheck}
        },
        {
            path: 'zyry',
            name: 'zyry',
            component: () => import ('@views/xtgl/zyrypz/zyrypzList.vue'),
            meta: {title: '专业人员配置', permission: customCheck}
        },
        {
            path: 'dwzhgl',
            name: 'dwzhgl',
            component: () => import ('@views/xtgl/dwzhgl/dwzhglList.vue'),
            meta: {title: '队伍账号管理', permission: customCheck}
        },
        {
            path: 'lcjspz',
            name: 'lcjspz',
            component: () => import ('@views/xtgl/lcjspz/lcjspzList.vue'),
            meta: {title: '流程角色配置', permission: customCheck}
        },
        {
            path: 'xssqlccl',
            name: 'xssqlccl',
            component: () => import ('@views/workflow/CbsWorkflowList.vue'),
            meta: {title: '选商申请流程处理', permission: customCheck}
        },
        {
            path: 'test',
            name: 'testA',
            component: () => import ('@src/views/workflow/test/workFTest.vue'),
            meta: {title: '测试流程', permission: customCheck}
        },

    ]
}

const khpjRoute = {
    path: 'khpj',
    name: 'khpj',
    component: () => import ('@views/component.vue'),
    meta: {title: '考核评价', permission: customCheck},
    children: [
        {
            path: 'khzqszList',
            name: 'khzqszList',
            component: () => import ('@views/khpj/khzqsz/khzqszList.vue'),
            meta: {title: '考核周期设置', permission: customCheck}
        },
        {
            path: 'khzyszList',
            name: 'khzyszList',
            component: () => import ('@views/khpj/khzysz/khzyszList.vue'),
            meta: {title: '考核专业设置', permission: customCheck}
        },
        {
            path: 'pjzbszList',
            name: 'pjzbszList',
            component: () => import ('@views/khpj/pjzbsz/pjzbszList.vue'),
            meta: {title: '评价指标设置', permission: customCheck}
        },
        {
            path: 'pjmbwhList',
            name: 'pjmbwhList',
            component: () => import ('@views/khpj/pjmbwh/pjmbwhList.vue'),
            meta: {title: '评价模板维护', permission: customCheck}
        },
        {
            path: 'khzybmglList',
            name: 'khzybmglList',
            component: () => import ('@views/khpj/khzymbgl/khzybmglList.vue'),
            meta: {title: '考核专业模板关联', permission: customCheck}
        },
        {
            path: 'xmwtsbList',
            name: 'xmwtsbList',
            component: () => import ('@views/khpj/xmwtsb/xmwtsbList.vue'),
            meta: {title: '项目问题上报', permission: customCheck}
        },
        {
            path: 'xmwtsbJsdwList',
            name: 'xmwtsbJsdwList',
            component: () => import ('@views/khpj/xmwtsb/xmwtsbList.vue'),
            meta: {title: '建设单位上报', permission: customCheck},
            props:{PJZT: 'JSDW'}
        },
        {
            path: 'xmwtsbZybmList',
            name: 'xmwtsbZybmList',
            component: () => import ('@views/khpj/xmwtsb/xmwtsbList.vue'),
            meta: {title: '专业部门上报', permission: customCheck},
            props:{PJZT: 'ZYBM'}
        },
        {
            path: 'xmwtsbAqbmList',
            name: 'xmwtsbAqbmList',
            component: () => import ('@views/khpj/xmwtsb/xmwtsbList.vue'),
            meta: {title: '安全部门上报', permission: customCheck},
            props:{PJZT: 'AQBM'}
        },
        {
            path: 'wtzgyssqList',
            name: 'wtzgyssqList',
            component: () => import ('@views/khpj/wtzgyssq/wtzgyssqList.vue'),
            meta: {title: '问题整改验收申请', permission: customCheck}
        },
        {
            path: 'khmxtbList',
            name: 'khmxtbList',
            component: () => import ('@views/khpj/khmxtb/khmxtbList.vue'),
            meta: {title: '考核明细填报', permission: customCheck}
        },
        {
            path: 'jhkhmxtbList',
            name: 'jhkhmxtbList',
            component: () => import ('@views/khpj/khmxtb_jh/khmxtbList.vue'),
            meta: {title: '西北考核明细填报', permission: customCheck}
        },
        {
            path: 'khhzfbList',
            name: 'khhzfbList',
            component: () => import ('@views/khpj/khhzfb/khhzfbList.vue'),
            meta: {title: '考核汇总发布', permission: customCheck}
        },
        {
            path: 'cbsclsqList',
            name: 'cbsclsqList',
            component: () => import ('@views/khpj/cbsclsq/cbsclsqList.vue'),
            meta: {title: '承包商处理申请', permission: customCheck}
        },
        {
            path: 'cbshfsqList',
            name: 'cbshfsqList',
            component: () => import ('@views/khpj/cbshfsq/cbshfsqList.vue'),
            meta: {title: '承包商恢复申请', permission: customCheck}
        },
        {
            path: 'hmdglList',
            name: 'hmdglList',
            component: () => import ('@views/khpj/hmdgl/hmdglList.vue'),
            meta: {title: '黑名单管理', permission: customCheck}
        },
        {
            path: 'xmwtsbQuery',
            name: 'xmwtsbQuery',
            component: () => import ('@views/khpj/cxtj/xmwtsbQuery.vue'),
            meta: {title: '项目问题上报查询', permission: customCheck}
        },
        {
            path: 'khmxQuery',
            name: 'khmxQuery',
            component: () => import ('@views/khpj/cxtj/khmxQuery.vue'),
            meta: {title: '考核明细查询', permission: customCheck}
        },
        {
            path: 'khjgQuery',
            name: 'khjgQuery',
            component: () => import ('@views/khpj/cxtj/khjgQuery.vue'),
            meta: {title: '考核结果查询', permission: customCheck}
        },
        {
            path: 'hmdQuery',
            name: 'hmdQuery',
            component: () => import ('@views/khpj/cxtj/hmdQuery.vue'),
            meta: {title: '黑名单查询', permission: customCheck}
        },
        {
            path: 'cbsclQuery',
            name: 'cbsclQuery',
            component: () => import ('@views/khpj/cxtj/cbsclQuery.vue'),
            meta: {title: '承包商处理查询', permission: customCheck}
        },
        {
            path: 'pjxmglList',
            name: 'pjxmglList',
            component: () => import ('@views/khpj/pjxmgl/pjxmglList.vue'),
            meta: {title: '评价项目管理', permission: customCheck}
        },
        {
            path: 'jfxxwhList',
            name: 'jfxxwhList',
            component: () => import ('@views/khpj/jfxxwh/jfxxwhList.vue'),
            meta: {title: '项目奖罚管理', permission: customCheck}
        },
        {
            path: 'fmqdmbList',
            name: 'fmqdmbList',
            component: () => import ('@views/khpj/fmqdmb/fmqdmbList.vue'),
            meta: {title: '负面清单模板配置', permission: customCheck}
        },
        {
            path: 'cbsfmqdList',
            name: 'cbsfmqdList',
            component: () => import ('@views/khpj/cbsfmqd/cbsfmqdList.vue'),
            meta: {title: '承包商负面清单', permission: customCheck}
        },
        {
            path: 'wtthAbmList',
            name: 'wtthAbmList',
            component: () => import ('@views/khpj/cxtj/wtthAbmList.vue'),
            meta: {title: '问题统计（按部门）', permission: customCheck}
        },
        {
            path: 'wtthAdwList',
            name: 'wtthAdwList',
            component: () => import ('@views/khpj/cxtj/wtthAdwList.vue'),
            meta: {title: '问题统计（按单位）', permission: customCheck}
        },
        {
            path: 'wtthAflList',
            name: 'wtthAflList',
            component: () => import ('@views/khpj/cxtj/wtthAflList.vue'),
            meta: {title: '问题统计（按分类）', permission: customCheck}
        },
        {
            path: 'khpjjkList',
            name: 'khpjjkList',
            component: () => import ('@views/khpj/khpjtbjk/khpjjkList.vue'),
            meta: {title: '考核评价监控', permission: customCheck}
        },
    ]
}
/** 专家管理 */
const zjglRoute = {
    path: 'zjgl',
    name: 'zjgl',
    component: () => import ('@views/component.vue'),
    meta: {title: '专家管理', permission: customCheck},
    children: [
        {
            path: 'zjrkList',
            name: 'zjrkList',
            component: () => import ('@views/zjgl/zjcrk/zjrkList.vue'),
            meta: {title: '专家管理-专家入库', permission: customCheck}
        },
        {
            path: 'zjckList',
            name: 'zjckList',
            component: () => import ('@views/zjgl/zjcrk/zjckList.vue'),
            meta: {title: '专家管理-专家出库', permission: customCheck}
        },
        {
            path: 'cgxmglList',
            name: 'cgxmglList',
            component: () => import ('@views/zjgl/cgxmgl/cgxmglList.vue'),
            meta: {title: '专家管理-采购项目管理', permission: customCheck}
        },
        {
            path: 'pwcqglList',
            name: 'pwcqglList',
            component: () => import ('@views/zjgl/pwcq/pwcqglList.vue'),
            meta: {title: '专家管理-评委抽取管理', permission: customCheck}
        },
        {
            path: 'pwpjList',
            name: 'pwpjList',
            component: () => import ('@views/zjgl/pwpj/pwpjList.vue'),
            meta: {title: '专家管理-评委评价', permission: customCheck}
        },
        {
            path: 'zjxxcxList',
            name: 'zjxxcxList',
            component: () => import ('@views/zjgl/zjcxtj/zjxxcx/zjxxcxList.vue'),
            meta: {title: '专家管理-专家查询统计-专家信息查询', permission: customCheck}
        },
        {
            path: 'zjpbtjList',
            name: 'zjpbtjList',
            component: () => import ('@views/zjgl/zjcxtj/zjpbtj/zjpbtjList.vue'),
            meta: {title: '专家管理-专家查询统计-专家评标统计', permission: customCheck}
        },
        {
            path: 'zjxxbgList',
            name: 'zjxxbgList',
            component: () => import ('@views/zjgl/zjcrk/zjxxbgList.vue'),
            meta: {title: '专家管理-动态管理', permission: customCheck}
        },
        {
            path: 'zjzyglList',
            name: 'zjzyglList',
            component: () => import ('@views/zjgl/zjzygl/zjzyglList.vue'),
            meta: {title: '专家管理-专业管理', permission: customCheck}
        },
        {
            path: 'zjhztjList',
            name: 'zjhztjList',
            component: () => import ('@views/zjgl/zjhztj/zjhztjList.vue'),
            meta: {title: '专家汇总统计', permission: customCheck}
        },
    ]
}

const bsccRoute = {
    path: 'bscc',
    name: 'bscc',
    component: () => import ('@views/component.vue'),
    meta: {title: '标书查重', permission: customCheck},
    children: [
        {
            path: 'ccjcszPage',
            name: 'ccjcszPage',
            component: () => import ('@views/bscc/ccmxsz/ccjcszPage.vue'),
            meta: {title: '查重基础设置', permission: customCheck}
        },
        {
            path: 'cccsszPage',
            name: 'cccsszPage',
            component: () => import ('@views/bscc/ccmxsz/cccsszPage.vue'),
            meta: {title: '查重参数设置', permission: customCheck}
        },
        {
            path: 'xmbsccglList',
            name: 'xmbsccglList',
            component: () => import ('@views/bscc/xmbsccgl/xmbsccglList.vue'),
            meta: {title: '项目标书查重管理', permission: customCheck}
        },
        {
            path: 'bsccjlQuery',
            name: 'bsccjlQuery',
            component: () => import ('@views/bscc/ccjggl/bsccQuery.vue'),
            meta: {title: '标书查重记录查询', permission: customCheck},
            props:{type: 'JL'}
        },
        {
            path: 'bsccbgQuery',
            name: 'bsccbgQuery',
            component: () => import ('@views/bscc/ccjggl/bsccQuery.vue'),
            meta: {title: '项目标书查重报告查询', permission: customCheck},
            props:{type: 'BG'}
        },
    ]


}

const gqctRoute = {
    path: 'gqct',
    name: 'gqct',
    component: () => import ('@views/component.vue'),
    meta: {title: '股权穿透', permission: customCheck},
    children: [

        {
            path: 'gqctszPage',
            name: 'gqctszPage',
            component: () => import ('@views/gqct/gqctsz/gqctszPage.vue'),
            meta: {title: '股权穿透设置', permission: customCheck}
        },
        {
            path: 'gqctglPage',
            name: 'gqctglPage',
            component: () => import ('@views/gqct/gqctgl/gqctglPage.vue'),
            meta: {title: '股权穿透管理', permission: customCheck}
        },
        {
            path: 'qyjcxxPage',
            name: 'qyjcxxPage',
            component: () => import ('@views/gqct/gqsjjc/qyjcxxPage.vue'),
            meta: {title: '企业基础信息', permission: customCheck}
        },
        {
            path: 'gdxxPage',
            name: 'gdxxPage',
            component: () => import ('@views/gqct/gqsjjc/gdxxPage.vue'),
            meta: {title: '股东信息', permission: customCheck}
        },
        {
            path: 'zyryPage',
            name: 'zyryPage',
            component: () => import ('@views/gqct/gqsjjc/zyryPage.vue'),
            meta: {title: '主要人员', permission: customCheck}
        },
        {
            path: 'gqctfxjlcxPage',
            name: 'gqctfxjlcxPage',
            component: () => import ('@views/gqct/gqctjggl/gqctfxjlcxPage.vue'),
            meta: {title: '股权穿透分析记录查询', permission: customCheck}
        },
        {
            path: 'gqctfxjgcxPage',
            name: 'gqctfxjgcxPage',
            component: () => import ('@views/gqct/gqctjggl/gqctfxjgcxPage.vue'),
            meta: {title: '股权穿透分析结果查询', permission: customCheck}
        },
        {
            path: 'xmgqctjgcxPage',
            name: 'xmgqctjgcxPage',
            component: () => import ('@views/gqct/gqctjggl/xmgqctjgcxPage.vue'),
            meta: {title: '股权穿透分析结果查询', permission: customCheck}
        },
        {
            path: 'xmgqctglPage',
            name: 'xmgqctglPage',
            component: () => import ('@views/gqct/gqctgl/xmgqctglPage.vue'),
            meta: {title: '项目股权穿透管理', permission: customCheck}
        },
    ]


}

const zbxsRoute = {
    path: 'zbxs',
    name: 'zbxs',
    component: () => import ('@views/component.vue'),
    meta: {title: '招标选商', permission: customCheck},
    children: [
        {
            path: 'cgxmglList',
            name: 'cgxmglList',
            component: () => import ('@views/zbxsgl/cgxmgl/cgxmglList.vue'),
            meta: {title: '采购项目管理', permission: customCheck}
        },
        {
            path: 'xssqglList',
            name: 'xssqglList',
            component: () => import ('@views/zbxsgl/xssqgl/xssqglList.vue'),
            meta: {title: '选商申请管理', permission: customCheck}
        },
        {
            path: 'xmyxList',
            name: 'xmyxList',
            component: () => import ('@views/zbxsgl/xmyx/xmyxList.vue'),
            meta: {title: '招标项目运行', permission: customCheck}
        },
        {
            path: 'xmyxFzbList',
            name: 'xmyxFzbList',
            component: () => import ('@views/zbxsgl/xmyx/xmyxFzbList.vue'),
            meta: {title: '独家谈判项目运行', permission: customCheck}
        },
        {
            path: 'xmyxDjtbList',
            name: 'xmyxDjtbList',
            component: () => import ('@views/zbxsgl/xmyx/xmyxDjtbList.vue'),
            meta: {title: '独家谈判项目运行', permission: customCheck}
        },

        {
            path: 'zbwjlqList',
            name: 'zbwjlqList',
            component: () => import ('@views/zbxsgl/zbwjlq/zbwjlqList.vue'),
            meta: {title: '招标文件领取', permission: customCheck}
        },
        {
            path: 'tbwjdjList',
            name: 'tbwjdjList',
            component: () => import ('@views/zbxsgl/tbwjdj/tbwjdjList.vue'),
            meta: {title: '投标文件递交', permission: customCheck}
        },
        {
            path: 'dyglList',
            name: 'dyglList',
            component: () => import ('@views/zbxsgl/dygl/dyglList.vue'),
            meta: {title: '答疑管理', permission: customCheck}
        },
        {
            path: 'dyglViewList',
            name: 'dyglViewList',
            component: () => import ('@views/zbxsgl/dygl/dyglViewList.vue'),
            meta: {title: '答疑管理', permission: customCheck}
        },
        {
            path: 'htzbList',
            name: 'htzbList',
            component: () => import ('@views/zbxsgl/htzb/htzbList.vue'),
            meta: {title: '合同准备', permission: customCheck}
        },
        {
            path: 'zbtsList',
            name: 'zbtsList',
            component: () => import ('@views/zbxsgl/zbjgts/zbtsList.vue'),
            meta: {title: '招标推送', permission: customCheck}
        },
        {
            path: 'zbtsCxList',
            name: 'zbtsCxList',
            component: () => import ('@views/zbxsgl/zbjgts/zbtsCxList.vue'),
            meta: {title: '招标推送查询', permission: customCheck}
        },

        {
            path: 'xssqmxQuery',
            name: 'xssqmxQuery',
            component: () => import ('@views/zbxsgl/cxtj/xssqmx/xssqmxQuery.vue'),
            meta: {title: '选商申请明细查询', permission: customCheck}
        },
        {
            path: 'zbjgmxQuery',
            name: 'zbjgmxQuery',
            component: () => import ('@views/zbxsgl/cxtj/zbjgmx/zbjgmxQuery.vue'),
            meta: {title: '中标结果明细查询', permission: customCheck}
        },
        {
            path: 'cjjgmxQuery',
            name: 'cjjgmxQuery',
            component: () => import ('@views/zbxsgl/cxtj/cjjgmx/cjjgmxQuery.vue'),
            meta: {title: '成交结果明细查询', permission: customCheck}
        },
		{
            path: 'xsxmblList',
            name: 'xsxmblList',
            component: () => import ('@views/zbxsgl/xsxmbl/xsxmblList.vue'),
            meta: {title: '选商项目补录', permission: customCheck}
        },

        {
            path: 'kpbyxglList',
            name: 'kpbyxglList',
            component: () => import ('@src/views/kpbyx/kpbyxgl/kpbyxglList.vue'),
            meta: {title: '开评标运行管理', permission: customCheck}
        },
        {
            path: 'kpbdtPage',
            name: 'kpbdtPage',
            component: () => import ('@src/views/kpbyx/kpbdt/kpbdtPage.vue'),
            meta: {title: '开评标大厅', permission: customCheck}
        },

        {
            path: 'kbyxPwList',
            name: 'kbyxPwList',
            component: () => import ('@src/views/kpbyx/kpbyxgl/pbyxList.vue'),
            meta: {title: '开标运行（评委）', permission: customCheck},
            props:{role: 'PW',YXLX: 'KB'}
        },

        {
            path: 'kbyxTbrList',
            name: 'kbyxTbrList',
            component: () => import ('@src/views/kpbyx/kpbyxgl/pbyxList.vue'),
            meta: {title: '开标运行（投标人）', permission: customCheck},
            props:{role: 'TBR',YXLX: 'KB'}
        },
        {
            path: 'pbyxPwList',
            name: 'pbyxPwList',
            component: () => import ('@src/views/kpbyx/kpbyxgl/pbyxList.vue'),
            meta: {title: '评标运行（评委）', permission: customCheck},
            props:{role: 'PW',YXLX: 'PB'}
        },

        {
            path: 'pbyxTbrList',
            name: 'pbyxTbrList',
            component: () => import ('@src/views/kpbyx/kpbyxgl/pbyxList.vue'),
            meta: {title: '评标运行（投标人）', permission: customCheck},
            props:{role: 'TBR',YXLX: 'PB'}
        },


    ]
}


const appRoute = {
    path: '/app',
    name: 'app',
    component: () => import ('@views/app/common/menu.vue'),
    meta: {title: '移动端'},
    children: [
        {
            path: 'qyjbxxView',
            name: 'qyjbxxView',
            component: () => import ('@views/app/cbs/cbsjbxx/qyjbxxView.vue'),
            meta: {title: '企业信息查看'}

        },
        {
            path: 'dwjbxxView',
            name: 'dwjbxxView',
            component: () => import ('@views/app/cbs/cbsjbxx/dwjbxxView.vue'),
            meta: {title: '队伍信息查看'}

        }
    ]

}

routesUseVsuiVue.push(xxfbRoute)
routesUseVsuiVue.push(workflowRoute)
routesUseVsuiVue.push(khpjRoute)
routesUseVsuiVue.push(zjglRoute)
routesUseVsuiVue.push(bsccRoute)
routesUseVsuiVue.push(gqctRoute)
routesUseVsuiVue.push(zbxsRoute)
routesUnUseVsuiVue.push(appRoute)
export {
    routesUseVsuiVue,
    routesUnUseVsuiVue
}
