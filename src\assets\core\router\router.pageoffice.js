

const pageOfficeRouter = [
  {
  path: '/pageoffice',
  name: 'pageoffice',
  component: () => import('@views/commons/pageOffice.vue'),
  meta: {title: 'pageoffice-word'}
},
//基础功能
{
  path: '/SimpleWord/Word',
  name: 'Word',
  component: () =>
      import ('@views/pageOffice/SimpleWord/Word.vue')
},
/* {
  path: '/SimpleWord/Word1',
  component: () =>
      import ("@views/pageOffice/SimpleWord/Word1.vue")
},
{
  path: '/SimpleExcel/Excel',
  component: () =>
      import ('@views/pageOffice/SimpleExcel/Excel.vue')
},
{
  path: '/SimplePPT/PPT',
  component: () =>
      import ("@views/pageOffice/SimplePPT/PPT.vue")
},
{
  path: '/TitleText/Word',
  component: () =>
      import ('@views/pageOffice/TitleText/Word.vue')
},
{
  path: '/ControlBars/Word',
  component: () =>
      import ("@views/pageOffice/ControlBars/Word.vue")
},
{
  path: '/OpenWord/Word',
  component: () =>
      import ("@views/pageOffice/OpenWord/Word.vue")
},
{
  path: '/SaveReturnValue/Word',
  component: () =>
      import ("@views/pageOffice/SaveReturnValue/Word.vue")
},
{
  path: '/SendParameters/Word',
  component: () =>
      import ("@views/pageOffice/SendParameters/Word.vue")
},
{
  path: '/DataRegionFill/Word',
  component: () =>
      import ("@views/pageOffice/DataRegionFill/Word.vue")
},
{
  path: '/ExcelFill/Excel',
  component: () =>
      import ("@views/pageOffice/ExcelFill/Excel.vue")
},
{
  path: '/SubmitWord/Word',
  component: () =>
      import ("@views/pageOffice/SubmitWord/Word.vue")
},
{
  path: '/SubmitExcel/Excel',
  component: () =>
      import ("@views/pageOffice/SubmitExcel/Excel.vue")
},
{
  path: '/InsertSeal/index',
  component: () =>
      import ("@views/pageOffice/InsertSeal/index.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word1',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word1.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word2',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word2.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word3',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word3.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word4',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word4.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word5',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word5.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word6',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word6.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word7',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word7.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word8',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word8.vue")
},
{
  path: '/InsertSeal/Word/AddSeal/Word9',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSeal/Word9.vue")
},
{
  path: '/InsertSeal/Word/DeleteSeal/Word',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/DeleteSeal/Word.vue")
},

{
  path: '/InsertSeal/Word/AddSign/Word1',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSign/Word1.vue")
},
{
  path: '/InsertSeal/Word/AddSign/Word2',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSign/Word2.vue")
},
{
  path: '/InsertSeal/Word/AddSign/Word3',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSign/Word3.vue")
},
{
  path: '/InsertSeal/Word/AddSign/Word4',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSign/Word4.vue")
},
{
  path: '/InsertSeal/Word/AddSign/Word5',
  component: () =>
      import ("@views/pageOffice/InsertSeal/Word/AddSign/Word5.vue")
},
{
  path: '/InsertSeal/PDF/AddSeal/PDF1',
  component: () =>
      import ("@views/pageOffice/InsertSeal/PDF/AddSeal/PDF1.vue")
},
{
  path: '/InsertSeal/PDF/AddSeal/PDF2',
  component: () =>
      import ("@views/pageOffice/InsertSeal/PDF/AddSeal/PDF2.vue")
},
{
  path: '/InsertSeal/PDF/AddSeal/PDF3',
  component: () =>
      import ("@views/pageOffice/InsertSeal/PDF/AddSeal/PDF3.vue")
},
{
  path: '/InsertSeal/PDF/DeleteSeal/PDF',
  component: () =>
      import ("@views/pageOffice/InsertSeal/PDF/DeleteSeal/PDF.vue")
},
{
  path: '/InsertSeal/PDF/AddSign/PDF1',
  component: () =>
      import ("@views/pageOffice/InsertSeal/PDF/AddSign/PDF1.vue")
},

{
  path: '/CommandCtrl/Word',
  component: () =>
      import ("@views/pageOffice/CommandCtrl/Word.vue")
},
{
  path: '/WordSetTable/Word',
  component: () =>
      import ("@views/pageOffice/WordSetTable/Word.vue")
},
{
  path: '/WordDataTag2/Word',
  component: () =>
      import ("@views/pageOffice/WordDataTag2/Word.vue")
},
{
  path: '/CustomToolButton/Word',
  component: () =>
      import ("@views/pageOffice/CustomToolButton/Word.vue")
},
{
  path: '/AfterDocOpened/Word',
  component: () =>
      import ("@views/pageOffice/AfterDocOpened/Word.vue")
},
{
  path: '/JsControlBars/Word',
  component: () =>
      import ("@views/pageOffice/JsControlBars/Word.vue")
},

{
  path: '/ExcelTable/Excel',
  component: () =>
      import ("@views/pageOffice/ExcelTable/Excel.vue")
},
{
  path: '/ConcurrencyCtrl/index',
  component: () =>
      import ("@views/pageOffice/ConcurrencyCtrl/index.vue")
},
{
  path: '/ConcurrencyCtrl/Word',
  component: () =>
      import ("@views/pageOffice/ConcurrencyCtrl/Word.vue")
},

{
  path: '/SaveAsHTML/Word',
  component: () =>
      import ("@views/pageOffice/SaveAsHTML/Word.vue")
},
{
  path: '/BeforeAndAfterSave/Word',
  component: () =>
      import ("@views/pageOffice/BeforeAndAfterSave/Word.vue")
},
{
  path: '/SaveDataAndFile/Word',
  component: () =>
      import ("@views/pageOffice/SaveDataAndFile/Word.vue")
},
{
  path: '/WordDisableRight/Word',
  component: () =>
      import ("@views/pageOffice/WordDisableRight/Word.vue")
},
{
  path: '/ExcelDisableRight/Excel',
  component: () =>
      import ("@views/pageOffice/ExcelDisableRight/Excel.vue")
},

{
  path: '/RevisionOnly/Word',
  component: () =>
      import ("@views/pageOffice/RevisionOnly/Word.vue")
},
{
  path: '/NoBlank/Word',
  component: () =>
      import ("@views/pageOffice/NoBlank/Word.vue")
},
{
  path: '/CommentOnly/Word',
  component: () =>
      import ("@views/pageOffice/CommentOnly/Word.vue")
},

//高级功能
{
  path: '/ReadOnly/Word',
  component: () =>
      import ("@views/pageOffice/ReadOnly/Word.vue")
},
{
  path: '/DataBase/Word',
  component: () =>
      import ("@views/pageOffice/DataBase/Word.vue")
},
{
  path: '/POPDF/PDF',
  component: () =>
      import ("@views/pageOffice/POPDF/PDF.vue")
},
{
  path: '/SaveAsPDF/Word',
  component: () =>
      import ("@views/pageOffice/SaveAsPDF/Word.vue")
},
{
  path: '/WordResWord/Word',
  component: () =>
      import ("@views/pageOffice/WordResWord/Word.vue")
},
{
  path: '/WordResImage/Word',
  component: () =>
      import ("@views/pageOffice/WordResImage/Word.vue")
},
{
  path: '/WordResExcel/Word',
  component: () =>
      import ("@views/pageOffice/WordResExcel/Word.vue")
},
{
  path: '/AddWaterMark/Word',
  component: () =>
      import ("@views/pageOffice/AddWaterMark/Word.vue")
},
{
  path: '/WordDataTag/Word',
  component: () =>
      import ("@views/pageOffice/WordDataTag/Word.vue")
},
{
  path: '/DataRegionCreate/Word',
  component: () =>
      import ("@views/pageOffice/DataRegionCreate/Word.vue")
},
{
  path: '/FileMakerSingle/Default',
  component: () =>
      import ("@views/pageOffice/FileMakerSingle/Default.vue")
},
{
  path: '/WordTable/Word',
  component: () =>
      import ("@views/pageOffice/WordTable/Word.vue")
},
{
  path: '/WordHandDraw/Word',
  component: () =>
      import ("@views/pageOffice/WordHandDraw/Word.vue")
},
{
  path: '/DataRegionTable/Word',
  component: () =>
      import ("@views/pageOffice/DataRegionTable/Word.vue")
},
{
  path: '/DataRegionText/Word',
  component: () =>
      import ("@views/pageOffice/DataRegionText/Word.vue")
},
{
  path: '/DataRegionText/Word2',
  component: () =>
      import ("@views/pageOffice/DataRegionText/Word2.vue")
},
{
  path: '/DataRegionText/index',
  component: () =>
      import ("@views/pageOffice/DataRegionText/index.vue")
},
{
  path: '/SetDrByUserWord/index',
  component: () =>
      import ("@views/pageOffice/SetDrByUserWord/index.vue")
},
{
  path: '/SetDrByUserWord/Word',
  component: () =>
      import ("@views/pageOffice/SetDrByUserWord/Word.vue")
},
{
  path: '/SetDrByUserWord2/index',
  component: () =>
      import ("@views/pageOffice/SetDrByUserWord2/index.vue")
},
{
  path: '/SetDrByUserWord2/Word',
  component: () =>
      import ("@views/pageOffice/SetDrByUserWord2/Word.vue")
},
{
  path: '/MergeWordCell/Word',
  component: () =>
      import ("@views/pageOffice/MergeWordCell/Word.vue")
},
{
  path: '/MergeExcelCell/Excel',
  component: () =>
      import ("@views/pageOffice/MergeExcelCell/Excel.vue")
},
{
  path: '/SetXlsTableByUser/index',
  component: () =>
      import ("@views/pageOffice/SetXlsTableByUser/index.vue")
},
{
  path: '/SetXlsTableByUser/Excel',
  component: () =>
      import ("@views/pageOffice/SetXlsTableByUser/Excel.vue")
},
{
  path: '/SetExcelCellBorder/Excel',
  component: () =>
      import ("@views/pageOffice/SetExcelCellBorder/Excel.vue")
},
{
  path: '/SetExcelCellText/Excel',
  component: () =>
      import ("@views/pageOffice/SetExcelCellText/Excel.vue")
},
{
  path: '/DataRegionFill2/Word',
  component: () =>
      import ("@views/pageOffice/DataRegionFill2/Word.vue")
},
{
  path: '/ExcelFill2/Excel',
  component: () =>
      import ("@views/pageOffice/ExcelFill2/Excel.vue")
},
{
  path: '/DataRegionEdit/Word',
  component: () =>
      import ("@views/pageOffice/DataRegionEdit/Word.vue")
},
{
  path: '/DataTagEdit/Word',
  component: () =>
      import ("@views/pageOffice/DataTagEdit/Word.vue")
},
{
  path: '/DefinedNameCell/Excel',
  component: () =>
      import ("@views/pageOffice/DefinedNameCell/Excel.vue")
},
{
  path: '/DefinedNameTable/index',
  component: () =>
      import ("@views/pageOffice/DefinedNameTable/index.vue")
},
{
  path: '/FileMakerPDF/Default',
  component: () =>
      import ("@views/pageOffice/FileMakerPDF/Default.vue")
},
{
  path: '/DefinedNameTable/Excel',
  component: () =>
      import ("@views/pageOffice/DefinedNameTable/Excel.vue")
},
{
  path: '/DefinedNameTable/Excel2',
  component: () =>
      import ("@views/pageOffice/DefinedNameTable/Excel2.vue")
},
{
  path: '/DefinedNameTable/Excel4',
  component: () =>
      import ("@views/pageOffice/DefinedNameTable/Excel4.vue")
},
{
  path: '/DefinedNameTable/Excel5',
  component: () =>
      import ("@views/pageOffice/DefinedNameTable/Excel5.vue")
},
{
  path: '/DefinedNameTable/Excel6',
  component: () =>
      import ("@views/pageOffice/DefinedNameTable/Excel6.vue")
},
{
  path: '/WordCompare/Word',
  component: () =>
      import ("@views/pageOffice/WordCompare/Word.vue")
},
{
  path: '/WordTextBox/Word',
  component: () =>
      import ("@views/pageOffice/WordTextBox/Word.vue")
},
{
  path: '/SplitWord/Word',
  component: () =>
      import ("@views/pageOffice/SplitWord/Word.vue")
},
{
  path: '/CommentsList/Word',
  component: () =>
      import ("@views/pageOffice/CommentsList/Word.vue")
},
{
  path: '/RevisionsList/Word',
  component: () =>
      import ("@views/pageOffice/RevisionsList/Word.vue")
},
{
  path: '/WordCreateTable/Word',
  component: () =>
      import ("@views/pageOffice/WordCreateTable/Word.vue")
},
{
  path: '/SaveFirstPageAsImg/Word',
  component: () =>
      import ("@views/pageOffice/SaveFirstPageAsImg/Word.vue")
},
{
  path: '/ExcelAdjustRC/Excel',
  component: () =>
      import ("@views/pageOffice/ExcelAdjustRC/Excel.vue")
},
{
  path: '/WordDeleteRow/Word',
  component: () =>
      import ("@views/pageOffice/WordDeleteRow/Word.vue")
},
{
  path: '/InsertPageBreak2/Word',
  component: () =>
      import ("@views/pageOffice/InsertPageBreak2/Word.vue")
},
{
  path: '/ExcelInsertImage/Excel',
  component: () =>
      import ("@views/pageOffice/ExcelInsertImage/Excel.vue")
},
{
  path: '/WordTableSetImg/Word',
  component: () =>
      import ("@views/pageOffice/WordTableSetImg/Word.vue")
},
{
  path: '/WordTableBorder/Word',
  component: () =>
      import ("@views/pageOffice/WordTableBorder/Word.vue")
},
{
  path: '/ExtractImage/Word',
  component: () =>
      import ("@views/pageOffice/ExtractImage/Word.vue")
},
{
  path: '/OpenImage/Image',
  component: () =>
      import ("@views/pageOffice/OpenImage/Image.vue")
},
{
  path: '/DisableCopyOut/Word',
  component: () =>
      import ("@views/pageOffice/DisableCopyOut/Word.vue")
},
{
  path: '/InsertImageSetSize/Word',
  component: () =>
      import ("@views/pageOffice/InsertImageSetSize/Word.vue")
},
{
  path: '/SwitchFile/Word',
  component: () =>
      import ("@views/pageOffice/SwitchFile/Word.vue")
},
{
  path: '/HtmlDialog/Word',
  component: () =>
      import ("@views/pageOffice/HtmlDialog/Word.vue")
},
{
  path: '/HtmlDialog/Modal',
  component: () =>
      import ("@views/pageOffice/HtmlDialog/Modal.vue")
},
{
  path: '/HtmlDialog/Modeless',
  component: () =>
      import ("@views/pageOffice/HtmlDialog/Modeless.vue")
},

//综合演示
{
  path: '/FileMaker/Default',
  component: () =>
      import ("@views/pageOffice/FileMaker/Default.vue")
},
{
  path: '/ExaminationPaper/index',
  component: () =>
      import ("@views/pageOffice/ExaminationPaper/index.vue")
},
{
  path: '/ExaminationPaper/Word',
  component: () =>
      import ("@views/pageOffice/ExaminationPaper/Word.vue")
},
{
  path: '/ExaminationPaper/Compose',
  component: () =>
      import ("@views/pageOffice/ExaminationPaper/Compose.vue")
},

{
  path: '/WordParagraph/Word',
  component: () =>
      import ("@views/pageOffice/WordParagraph/Word.vue")
},
{
  path: '/DrawExcel/Excel',
  component: () =>
      import ("@views/pageOffice/DrawExcel/Excel.vue")
},
{
  path: '/TaoHong/index',
  component: () =>
      import ("@views/pageOffice/TaoHong/index.vue")
},
{
  path: '/TaoHong/Word',
  component: () =>
      import ("@views/pageOffice/TaoHong/Word.vue")
},
{
  path: '/TaoHong/taoHong',
  component: () =>
      import ("@views/pageOffice/TaoHong/taoHong.vue")
},
{
  path: '/TaoHong/readOnly',
  component: () =>
      import ("@views/pageOffice/TaoHong/readOnly.vue")
},
{
  path: '/WordSalaryBill/index',
  component: () =>
      import ("@views/pageOffice/WordSalaryBill/index.vue")
},
{
  path: '/WordSalaryBill/Word',
  component: () =>
      import ("@views/pageOffice/WordSalaryBill/Word.vue")
},
{
  path: '/WordSalaryBill/OpenFile',
  component: () =>
      import ("@views/pageOffice/WordSalaryBill/OpenFile.vue")
},
{
  path: '/WordSalaryBill/Compose',
  component: () =>
      import ("@views/pageOffice/WordSalaryBill/Compose.vue")
},
{
  path: '/SaveAndSearch/index',
  component: () =>
      import ("@views/pageOffice/SaveAndSearch/index.vue")
},
{
  path: '/SaveAndSearch/Word',
  component: () =>
      import ("@views/pageOffice/SaveAndSearch/Word.vue")
},
{
  path: '/FileMakerPrintFiles/Default',
  component: () =>
      import ("@views/pageOffice/FileMakerPrintFiles/Default.vue")
},
{
  path: '/FileMakerPrintFiles/Preview',
  component: () =>
      import ("@views/pageOffice/FileMakerPrintFiles/Preview.vue")
},
{
  path: '/FileMakerConvertPDFs/Default',
  component: () =>
      import ("@views/pageOffice//FileMakerConvertPDFs/Default.vue")
},
{
  path: '/FileMakerConvertPDFs/Edit',
  component: () =>
      import ("@views/pageOffice/FileMakerConvertPDFs/Edit.vue")
},
{
  path: '/BingFa/Index',
  component: () =>
      import ("@views/pageOffice/BingFa/Index.vue"),
  children: [
      {
          path: '',//当path为''时，默认加载的组件
          component: () =>
              import ("@views/pageOffice/BingFa/loginForm.vue"),
          
      },
      {
          path: '/BingFa/list/:username',
          name: 'list',
          component: () =>
              import ("@views/pageOffice/BingFa/list.vue")
      }
  ]
},
{
  path: '/BingFa/Word1',
  component: () =>
      import ("@views/pageOffice/BingFa/Word1.vue")
},
{
  path: '/BingFa/Word2',
  component: () =>
      import ("@views/pageOffice/BingFa/Word2.vue")
},

//其他技巧
{
  path: '/WordAddBKMK/Word',
  component: () =>
      import ("@views/pageOffice/WordAddBKMK/Word.vue")
},
{
  path: '/WordLocateBKMK/Word',
  component: () =>
      import ("@views/pageOffice/WordLocateBKMK/Word.vue")
},
{
  path: '/WordGetSelection/Word',
  component: () =>
      import ("@views/pageOffice/WordGetSelection/Word.vue")
},
{
  path: '/InsertImgForJs/Word',
  component: () =>
      import ("@views/pageOffice/InsertImgForJs/Word.vue")
},
{
  path: '/JsInsertWaterMark/Word',
  component: () =>
      import ("@views/pageOffice/JsInsertWaterMark/Word.vue")
},

// //PAGEOFFICE V4.0新特性

{
  path: '/CallParentFunction/index',
  component: () =>
      import ("@views/pageOffice/CallParentFunction/index.vue")
},
{
  path: '/CallParentFunction/Word',
  component: () =>
      import ("@views/pageOffice/CallParentFunction/Word.vue")
},
{
  path: '/GetParentParamValue/index',
  component: () =>
      import ("@views/pageOffice/GetParentParamValue/index.vue")
},
{
  path: '/GetParentParamValue/Word',
  component: () =>
      import ("@views/pageOffice/GetParentParamValue/Word.vue")
},

//v6.0新特性

{
  path: '/DivMessage/Word',
  component: () =>
      import ("@views/pageOffice/DivMessage/Word.vue")
},
{
  path: '/ApplicationForm/Word',
  component: () =>
      import ("@views/pageOffice/ApplicationForm/Word.vue")
},
{
  path: '/FormToDataRegions/Word',
  component: () =>
      import ("@views/pageOffice/FormToDataRegions/Word.vue")
},
{
  path: '/FileMakerToPDF/Default',
  component: () =>
      import ("@views/pageOffice/FileMakerToPDF/Default.vue")
}, */
]

export {
  pageOfficeRouter
}