<template>
  <el-form :model="formData" ref="vForm" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="12">
      <el-col :span="24" class="grid-cell">
        <el-row :gutter="20" class="lui-search-form">
          <el-col :span="8" class="grid-cell">
            <el-form-item label="" prop="name">
              <el-input v-model="formData.XM" type="text" style="width:100%;" placeholder="请输入姓名" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="" prop="workAddress">
              <el-input v-model="formData.GZDW" type="text" style="width:100%;" placeholder="请输入工作单位" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="grid-cell">
            <el-form-item label="" prop="major">
              <el-input v-model="formData.SBZY"  type="text" placeholder="请选择申报专业" @clear="formData.SBZYList=[]"
                        clearable suffix-icon="More" :disabled="true" style="width:100%;">
                <template #append>
                  <el-button @click="ZYDialogVisible=true" plain>选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="3" :offset="18" class="grid-cell">
        <div class="button_folat static-content-item">
          <el-button type="primary" @click="addRow">添加条件</el-button>
        </div>
      </el-col>
      <el-col :span="3" class="grid-cell">
        <div class="button_folat static-content-item">
          <el-button type="primary" @click="delRows" :disabled="checkRow.length===0">删除条件</el-button>
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper" style="margin-top: 10px">
      <el-table ref="dataTable" :data="formData.tableData" height="300px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="true"
                @selection-change="handleSelectionChange" :row-class-name="rowClassName"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
        <el-table-column label="序号" type="index" width="60" fixed="left" align="center"></el-table-column>
        <el-table-column prop="LJGX" label="逻辑关系" align="center">
          <template #default="{row,$index}">
            <el-form-item :prop="`tableData.${$index}.LJGX`" class="required" label-width="0" :rules="rules.LJGX">
              <el-select v-model="row.LJGX" class="full-width-input" clearable>
                <el-option v-for="(item, index) in LJGXOptions" :key="index" :label="item.DMMC"
                           :value="item.DMXX"></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="TJZD" label="条件字段" align="center">
          <template #default="{row,$index}">
            <el-form-item :prop="`tableData.${$index}.TJZD`" class="required" label-width="0" :rules="rules.TJZD">
              <el-select v-model="row.TJZD" value-key="DMXX" class="full-width-input" @change="changeTJZD(row)" clearable>
                <el-option v-for="(item, index) in TJZDOptions" :key="index" :label="item.DMMC"
                           :value="item"></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="TJLJ" label="条件逻辑" align="center">
          <template #default="{row,$index}">
            <el-form-item :prop="`tableData.${$index}.TJLJ`" class="required" label-width="0" :rules="rules.TJLJ">
              <el-select v-model="row.TJLJ" class="full-width-input" clearable>
                <el-option v-for="(item, index) in TJLJOptions" :key="index" :label="item.DMMC"
                           :value="item.DMXX"></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="TJZ" label="条件值" align="center">
          <template #default="{row,$index}">
            <el-form-item :prop="`tableData.${$index}.TJZ`" class="required" label-width="0" :rules="rules.TJZ">
              <el-select v-model="row.TJZ" class="full-width-input" clearable v-if="row.TJZD.BYZD1">
                <el-option v-for="(item, index) in options[`${row.TJZD.BYZD1}Options`]" :key="index" :label="item.DMMC"
                           :value="item.DMXX"></el-option>
              </el-select>
              <el-input v-model="row.TJZ" type="text" clearable v-else></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55"/>
      </el-table>
      <el-row justify="center" :gutter="12" style="margin-top: 20px">
        <el-col :span="3" class="grid-cell">
          <el-button type="primary" @click="executeQuery">执行查询</el-button>
        </el-col>
        <el-col :span="3" class="grid-cell">
          <el-button type="primary" @click="close">返回</el-button>
        </el-col>
      </el-row>
    </div>
    <el-dialog z-index="1000" v-model="ZYDialogVisible" title="申报专业选择" width="800px" class="dialogClass" top="1vh"
               append-to-body>
      <zyxz v-if="ZYDialogVisible" :show-z-z-y="false" @close="closeZYDialog" @parentMethod="getCheck"></zyxz>
    </el-dialog>
  </el-form>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted,
}
  from 'vue'
import axiosUtil from "../../../lib/axiosUtil";
import Zyxz from "./common/zyxz";
export default defineComponent({
  components: {Zyxz},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      formData: {
        XM:'',
        GZDW:'',
        SBZY:'',
        tableData: [],
      },
      rules: {
        LJGX: [{
          required: true,
          message: '字段值不可为空',
        }],
        TJZD: [{
          required: true,
          message: '字段值不可为空',
        }],
        TJLJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        TJZ: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      TJLJOptions: [],
      LJGXOptions: [],
      TJZDOptions: [],
      XBOptions:[],
      options:{

      },
      checkRow: [],
      ZYDialogVisible: false
    })
    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)

      state[resList] = reactive(res.data)
    }
    const getDMBDataO = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)

      state.options[resList] = reactive(res.data)
    }
    const addRow = () => {
      state.formData.tableData.push({
        LJGX: '',
        TJZD: '',
        TJLJ: '',
        TJZ: ''
      })
    }
    const delRows = () => {
      state.formData.tableData=state.formData.tableData.filter((item, index) => {
        return !state.checkRow.includes(index)
      })
    }
    const rowClassName = (row, index) => {
      row.row.index = row.rowIndex;
    }
    const handleSelectionChange = (rows) => {
      let res=[]
      rows.forEach(item=>{
        res.push(item.index)
      })
      state.checkRow=res
    }
    const changeTJZD = (val) => {
      if(val.TJZD.BYZD1){
        getDMBDataO(val.TJZD.BYZD1, val.TJZD.BYZD1+'Options')
      }
      val.TJZ=''
    }
    const closeZYDialog = () => {
      state.ZYDialogVisible=false
    }
    const getCheck = (e,ZZY) => {
      let res = []
      let SBZY=[]
      e.forEach(item => {
        res.push(item.ZYBM)
        SBZY.push(item.ZYMC)
      })
      state.formData.SBZY=SBZY.join(',')
      state.formData.SBZYList=res
      state.ZYDialogVisible=false

    }
    const instance = getCurrentInstance()
    const executeQuery = () => {
      instance.proxy.$refs['vForm'].validate(valid => {
        if (valid) {
          let params=JSON.parse(JSON.stringify({
            ...state.formData
          }))
          params.tableData.forEach(item=>{
            if(item.TJLJ==='IN'){
              let dataList=item.TJZ.split(',')
              let res=[]
              dataList.forEach(i=>{
                res.push('\''+i+'\'')
              })
              item.TJZ=res.join(',')
            }
          })
          console.log('pppp',params)
          //TODO: 提交表单
          emit('executeQuery',params)
        }
      })
    }
    const close = () => {
      emit('executeQuery',null)
    }
    onMounted(() => {
      getDMBData('TJLJ', 'TJLJOptions')
      getDMBData('LJGX', 'LJGXOptions')
      getDMBData('TJZD', 'TJZDOptions')
    })

    return {
      ...toRefs(state),
      addRow,
      delRows,
      handleSelectionChange,
      rowClassName,
      executeQuery,
      close,
      changeTJZD,
      closeZYDialog,
      getCheck
    }
  }
})

</script>


<style>
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: white;
}
</style>
