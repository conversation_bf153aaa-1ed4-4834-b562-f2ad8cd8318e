<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="200px" :disabled="!isFirstZr"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">

        <el-col :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="被委托人姓名：" prop="BSQWTRXM">
            <el-input v-model="formData.BSQWTRXM" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="手机：" prop="SJ">
            <el-input v-model="formData.SJ" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="被委托人身份证明：" prop="BWTRSFZM">
            <vsfileupload
                style="margin-left: 10px"
                :busId="formData.SXZYMXID"
                :editable="editable"
                :key="formData.SXZYMXID"
                ywlb="BWTRSFZM"
                busType="dwxx"
                :limit="10"
            ></vsfileupload>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="被委托人与企业签订合同：" prop="BWTRYQYQDHT">
            <vsfileupload
                style="margin-left: 10px"
                :busId="formData.SXZYMXID"
                :editable="editable"
                :key="formData.SXZYMXID"
                ywlb="BWTRYQYQDHT"
                busType="dwxx"
                :limit="10"
            ></vsfileupload>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="被委托人社保证明：" prop="BWTRSBZM">
            <vsfileupload
                style="margin-left: 10px"
                :busId="formData.SXZYMXID"
                :editable="editable"
                :key="formData.SXZYMXID"
                ywlb="BWTRSBZM"
                busType="dwxx"
                :limit="10"
            ></vsfileupload>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="授权委托附件：" prop="SQWTFJ">
            <vsfileupload
                style="margin-left: 10px"
                :busId="formData.SXZYMXID"
                :editable="editable"
                :key="formData.SXZYMXID"
                ywlb="SQWTFJ"
                busType="dwxx"
                :limit="10"
            ></vsfileupload>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="相关附件：" prop="XGFJ">
            <vsfileupload
                style="margin-left: 10px"
                :busId="formData.SXZYMXID"
                :editable="editable"
                :key="formData.SXZYMXID"
                ywlb="XGFJ"
                busType="dwxx"
                :limit="10"
            ></vsfileupload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>

  import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, ref} from "vue";
import vsfileupload from "@views/components/vsfileupload";
import comFun from "@lib/comFun";
import { getCheckIsFirstZr } from "@src/api/sccbsgl.js";
  import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    DWYWID: {
      type: String,
      required: true
    },
    row: {
      type: Object,
      defaultData: () => {
      },
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    TYXYDM: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      formData: {},
      rules: {},
      loading: false,
    })
    watch(() => props.row, (val) => {
      if (val) state.formData = val;
      if(!state.formData.SXZYMXID){
        state.formData.SXZYMXID = comFun.newId();
      }
      if(!state.formData.SQWYBS){
        state.formData.SQWYBS = comFun.newId();
      }
      if(!state.formData.DWYWID){
        state.formData.DWYWID = props.DWYWID;
      }
    }, {immediate: true,});

    // 判断是否为首次准入
    const isFirstZr = ref(true);
    const checkIsFirstZr = () => {
      getCheckIsFirstZr({
        tyxydm: props.TYXYDM,
      })
              .then(({ data }) => {
                isFirstZr.value = (data.rows.length == 0);
              })
              .catch((err) => {
                ElMessage.error("查询失败");
              });
    };

    onMounted(() => {
      // 判断是否为首次准入
      checkIsFirstZr();
    })

    return {
      ...toRefs(state),
      isFirstZr

    }
  }

})
</script>

<style scoped>

</style>
