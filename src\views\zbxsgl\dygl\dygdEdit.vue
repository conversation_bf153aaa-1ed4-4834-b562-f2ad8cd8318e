<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="12" class="grid-cell">
          <el-form-item label="项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{ formData.XMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMBH }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="业务类别：" prop="XMLB">
            <div style="margin-left: 10px">{{ formData.XMLB }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="建设单位：" prop="JSDW">
            <div style="margin-left: 10px">{{ formData.JSDW }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="附件上传：" prop="PBDD">
            <vsfileupload v-if="params.type === 'edit'" ref="vsfileupload" style="margin-left: 10px;min-width: 200px" :busId="params.XMID"
                :key="params.XMID" v-model:files="fileList"
                :editable="params.editable" ywlb="dygdfj" busType="dwxx"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
     <div style="width: 100%;margin-bottom: 10px;justify-content: center;display: flex">
        <el-button v-if="params.type === 'edit'" type="primary" @click="saveData">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
export default defineComponent({
  name: '',
  components: {axiosUtil,vsfileupload},
  props: {
    params: {
      type: Object,
      default: {},
    },
  },
  setup(props, {emit}) {
    const state = reactive({
        formData: {
            XMMC: '',
            XMBH: '',
            XMLB: '',
            JSDW: '',
        },
        fileList: [],
    })

    const queryDygd = (() =>{
        axiosUtil.get('/backend/xsgl/dygl/queryDygd', props.params).then((res) => {
            state.formData = res.data;
        });
    })
   
   const closeForm = (() =>{
       emit('close');
   })

   //修改状态
   const saveData = (() =>{
        let params = {
            XMID: props.params.XMID,
            DYGD: '1',
        }
        axiosUtil.post('/backend/xsgl/dygl/updateStatus', params).then((res) => {
            if (res.code === 1) {
                emit('close');
                ElMessage.success("保存成功");
            }
        }).catch((err) =>{
            ElMessage.error("保存失败");
        });
   })

    onMounted(() => {
      queryDygd();
    })

    return {
      ...toRefs(state),
      queryDygd,
      closeForm,
      saveData,
    }
  }

})
</script>

<style scoped>
</style>
