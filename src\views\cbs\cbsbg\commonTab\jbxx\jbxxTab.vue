<template>
  <div>
    <el-form
        :model="form"
        ref="vForm"
        :rules="vFormRules"
        label-position="left"
        label-width="160px"
        size="default"
        class="lui-card-form"
        :disabled="!editable">
<!--                        <div style="color: red">-->
<!--                          {{ BGXX }}-->
<!--                        </div>-->
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="企业全称" prop="CBSDWQC" :class="{dataChange: isChangeT('CBSDWQC')}">
            <template #label>
              <div>
                企业全称:
                <el-tooltip :content="isChangeT('CBSDWQC')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('CBSDWQC')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.CBSDWQC"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="简称" prop="DWJC" :class="{dataChange: isChangeT('DWJC')}">
            <template #label>
              <div>
                简称:
                <el-tooltip :content="isChangeT('DWJC')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('DWJC')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.DWJC"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="法人" prop="FDDBRXM" :class="{dataChange: isChangeT('FDDBRXM')}">
            <template #label>
              <div>
                法人:
                <el-tooltip :content="isChangeT('FDDBRXM')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('FDDBRXM')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.FDDBRXM"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系方式" prop="FDDBRLXFS" :class="{dataChange: isChangeT('FDDBRLXFS')}">
            <template #label>
              <div>
                联系方式:
                <el-tooltip :content="isChangeT('FDDBRLXFS')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('FDDBRLXFS')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.FDDBRLXFS"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="注册日期" prop="ZCRQ" :class="{dataChange: isChangeT('ZCRQ')}">
            <template #label>
              <div>
                注册日期:
                <el-tooltip :content="isChangeT('ZCRQ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZCRQ')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-date-picker
                v-model="form.ZCRQ"
                type="date"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
                style="width: 100%"
            />
          </el-form-item>
        </el-col>
<!--        <el-col :span="8" class="grid-cell">-->
<!--          <el-form-item label="企业类型" prop="QYLXDM" :class="{dataChange: isChangeT('QYLXDM')}">-->
<!--            <template #label>-->
<!--              <div>-->
<!--                企业类型:-->
<!--                <el-tooltip :content="isChangeT('QYLXDM')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('QYLXDM')">-->
<!--                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>-->
<!--                </el-tooltip>-->
<!--              </div>-->
<!--            </template>-->
<!--            <el-select-->
<!--                v-model="form.QYLXDM"-->
<!--                clearable-->
<!--                placeholder="请选择"-->
<!--                style="width: 100%"-->
<!--            >-->
<!--              <el-option-->
<!--                  v-for="(item, index) in qylxOptions"-->
<!--                  :key="index"-->
<!--                  :label="item.DMMC"-->
<!--                  :value="item.DMXX"-->
<!--              ></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="16" class="grid-cell">
          <el-form-item label="注册地址" prop="ZCDZ" :class="{dataChange: isChangeT('ZCDZ')}">
            <template #label>
              <div>
                注册地址:
                <el-tooltip :content="isChangeT('ZCDZ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZCDZ')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.ZCDZ"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="注册资金(万元)" prop="ZCZBJE" :class="{dataChange: isChangeT('ZCZBJE')}">
            <template #label>
              <div>
                注册资金(万元):
                <el-tooltip :content="isChangeT('ZCZBJE')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZCZBJE')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model.number="form.ZCZBJE"
                type="number"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item label="营业范围" prop="EXTENSION.yyfw" :class="{dataChange: isChangeT('EXTENSION.yyfw')}">
            <template #label>
              <div>
                营业范围:
                <el-tooltip :content="isChangeT('EXTENSION.yyfw')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('EXTENSION.yyfw')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.EXTENSION.yyfw"
                type="textarea"
                :rows="3"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="统一信用代码" prop="TYXYDM" :class="{dataChange: isChangeT('TYXYDM')}">
            <template #label>
              <div>
                统一信用代码:
                <el-tooltip :content="isChangeT('TYXYDM')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('TYXYDM')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.TYXYDM"
                clearable
                placeholder="请输入"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="是否营业截止日期" prop="EXTENSION.SFYYJZRQ" :class="{dataChange: isChangeT('EXTENSION.SFYYJZRQ')}">
            <template #label>
              <div>
                是否营业截止日期:
                <el-tooltip :content="isChangeT('EXTENSION.SFYYJZRQ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('EXTENSION.SFYYJZRQ')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-select
                v-model="form.EXTENSION.SFYYJZRQ"
                clearable
                placeholder="请选择"
                style="width: 100%"
            >
              <el-option
                v-for="(item, index) in sfyyjzrqOptions"
                :key="index"
                :label="item.DMMC"
                :value="item.DMXX"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="营业有效截止日期" prop="YXQJS" :class="{dataChange: isChangeT('YXQJS')}" v-if="form.EXTENSION.SFYYJZRQ==='1'">
            <template #label>
              <div>
                营业有效截止日期:
                <el-tooltip :content="isChangeT('YXQJS')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('YXQJS')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-date-picker
                v-model="form.YXQJS"
                type="date"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
                style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="单位系统归属" prop="DWXTGSDM" :class="{dataChange: isChangeT('DWXTGSDM')}">
            <template #label>
              <div>
                单位系统归属:
                <el-tooltip :content="isChangeT('DWXTGSDM')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('DWXTGSDM')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <!-- <el-input v-model="form.DWXTGSDM" clearable placeholder="请输入"></el-input> -->
            <el-select
                v-model="form.DWXTGSDM"
                clearable
                placeholder="请选择"
                style="width: 100%"
            >
              <el-option
                  v-for="(item, index) in dwxtgsOptions"
                  :key="index"
                  :label="item.DMMC"
                  :value="item.DMXX"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16" class="grid-cell">
          <el-form-item label="开户行及开户号" prop="KHXJKHH" :class="{dataChange: isChangeT('KHXJKHH')}">
            <template #label>
              <div>
                开户行及开户号:
                <el-tooltip :content="isChangeT('KHXJKHH')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('KHXJKHH')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.KHXJKHH"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="grid-cell">
          <el-form-item label="营业有效截止日期" prop="KHXYXQJS" :class="{dataChange: isChangeT('KHXYXQJS')}">
            <template #label>
              <div>
                有效截止日期:
                <el-tooltip :content="isChangeT('KHXYXQJS')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('KHXYXQJS')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-date-picker
                v-model="form.KHXYXQJS"
                type="date"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
                style="width: 100%"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="grid-cell">
          <el-form-item label="员工人数" prop="EXTENSION.ygrs" :class="{dataChange: isChangeT('EXTENSION.ygrs')}">
            <template #label>
              <div>
                员工人数:
                <el-tooltip :content="isChangeT('EXTENSION.ygrs')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('EXTENSION.ygrs')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input v-model="form.EXTENSION.ygrs" type="number" clearable placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系人" prop="LXRXM" :class="{dataChange: isChangeT('LXRXM')}">
            <template #label>
              <div>
                联系人:
                <el-tooltip :content="isChangeT('LXRXM')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('LXRXM')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.LXRXM"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="联系人手机" prop="LXRSJH" :class="{dataChange: isChangeT('LXRSJH')}">
            <template #label>
              <div>
                联系人手机:
                <el-tooltip :content="isChangeT('LXRSJH')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('LXRSJH')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input
                v-model="form.LXRSJH"
                type="number"
                clearable
                placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-collapse v-model="activeName" accordion>
          <el-collapse-item title="相关附件" name="xgfj">
            <vsFileUploadTable
                style="width: 100%;height: 400px"
                YWLX="CBSJBXX"
                :key="form.CBSYWID"
                :busId="form.CBSYWID"
                v-model:fileTableData="fileTableData"
                :editable="editable"
            />
          </el-collapse-item>
        </el-collapse>
      </el-row>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, ref, watch} from "vue";
import {vue} from "@core";
import {getCommonSelectDMB} from "@src/api/common";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import vsFileUploadTable from "@views/components/vsFileUploadTable";
import {InfoFilled} from "@element-plus/icons-vue";


export default defineComponent({
  name: '',
  components: {vsFileUploadTable,InfoFilled},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
  },
  setup(props, {emit}) {
    const state = reactive({})

    const sfyyjzrqOptions = ref(
      [{DMMC: '是',DMXX: '1'},{DMMC: '无固定期限',DMXX: '0'}]
    );

    const fileTableData = vue.ref([]);
    let form = ref({
      /**企业全称 */
      CBSDWQC: "",
      /**简称 */
      DWJC: "",
      /**法人 */
      FDDBRXM: "",
      /**联系方式 */
      FDDBRLXFS: "",
      /**企业类型 */
      QYLXDM: "zryxgs",
      /**注册地址 */
      ZCDZ: "",
      /**注册资金 */
      ZCZBJE: "",
      /**营业范围 */
      yyfw: "",
      /**营业执照号 */
      yyzzh: "",
      /**营业执照号-有效截止日期 */
      yyzzhyxjzrq: "",
      /**单位系统归属 */
      DWXTGSDM: "",
      /**统一信用代码 */
      TYXYDM: "",
      /**统一信用代码-有效截止日期 */
      YXQJS: "",
      /**开户行及开户号 */
      KHXJKHH: "",
      /**开户行及开户号-有效截止日期 */
      KHXYXQJS: "",
      /**固定资产 */
      gdzc: "",
      /**基层队伍个数 */
      jcdwgs: "",
      /**员工人数 */
      ygrs: "",
      /**股权结构 */
      gqjg: "",
      /**联系人 */
      LXRXM: "",
      /**联系人手机 */
      LXRSJH: "",
      /**是否涉诉 */
      sfss: "1",
      /**是否失信人 */
      sfsxr: "1",
      /**综合评定 */
      zhpd: "",
    });

    watch(() => props.defaultData, (val) => {
      if (val) form.value = val;
    }, {immediate: true,});
    let activeName = ref("xgfj");
// 查询企业类型
    let qylxOptions = ref([]);
    const getQylx = () => {
      getCommonSelectDMB({
        DMLBID: "QYLX",
      }).then(({data}) => {
        console.log('查询企业类型', data);
        if (data) qylxOptions.value = data;
      }).catch((err) => {
        ElMessage.error("查询企业类型获取失败");
      });
    }

    let tableData = reactive([]);


    const queryTableData = () => {
      // tableData = []
      tableData.push(
          ...[
            {lx: "营业执照", fileName: ""},
            {lx: "固定资产及其他信息证明材料", fileName: ""},
            {lx: "企业资信证明", fileName: ""},
            {lx: "审计财务报告", fileName: ""},
            {lx: "改制企业改制文件", fileName: ""},
            {lx: "企业简介", fileName: ""},
            {lx: "廉洁承诺书", fileName: ""},
            {lx: "其他附件", fileName: ""},
          ]
      );
    };
// 单位系统归属
    const dwxtgsOptions = ref([]);
    const getDwxtgsOptions = () => {
      getCommonSelectDMB({
        DMLBID: "DWXTGS",
      })
          .then(({data}) => {
            if (data) dwxtgsOptions.value = data;
          })
          .catch((err) => {
            ElMessage.error("单位系统归属信息获取失败");
          });
    };




    const vForm = ref(null);
    const vFormRules = ref({
      CBSDWQC: [
        {required: true, message: "请输入企业名称", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      DWJC: [
        {required: false, message: "请输入企业简称", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      FDDBRXM: [
        {required: true, message: "请输入法人姓名", trigger: "blur"},
        {max: 64, message: "最多输入64个字符", trigger: "blur"},
      ],
      FDDBRLXFS: [
        {required: true, message: "请输入法人联系方式", trigger: "blur"},
        {max: 32, message: "最多输入32个字符", trigger: "blur"},
      ],
      QYLXDM: [{required: true, message: "请选择企业类型", trigger: "blur"}],
      ZCDZ: [
        {required: true, message: "请输入注册地址", trigger: "blur"},
        {max: 256, message: "最多输入256个字符", trigger: "blur"},
      ],
      ZCZBJE: [
        {required: true, message: "请输入注册资金", trigger: "blur"},
        {
          pattern: /^[0-9]{1,16}(.[0-9]{1,6})?$/,
          message: "请输入正常范围",
          trigger: "change",
        },
        {type: "number", message: "请输入数字", trigger: "change"},

        // 整数位16，小数位6
      ],
      'EXTENSION.yyfw': [
        {required: true, message: "请输入营业范围", trigger: "blur"},
        {max: 2000, message: "最多输入2000个字符", trigger: "blur"},
      ],
      TYXYDM: [
        {required: true, message: "请输入统一信用代码", trigger: "blur"},
        {max: 32, message: "最多输入32个字符", trigger: "blur"},
      ],
      YXQJS: [{required: false, message: "请选择有效截止日期", trigger: "blur"}],
      DWXTGSDM: [{required: false, message: "请选择单位系统归属", trigger: "blur"}],
      KHXJKHH: [
        {required: false, message: "请输入开户行及开户号", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      KHXYXQJS: [{required: false, message: "请选择有效截止日期", trigger: "blur"}],
      gqjg: [
        {required: false, message: "请输入股权结构", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      LXRXM: [
        {required: true, message: "请输入联系人", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      LXRSJH: [
        {required: true, message: "请输入联系人手机", trigger: "blur"},
        {max: 32, message: "最多输入32个字符", trigger: "blur"},
      ],
      'EXTENSION.ZZJGDM': [
        {required: true, message: "请输入组织机构代码", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      'EXTENSION.XSLXRXM': [
        {required: true, message: "请输入选商联系人", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      'EXTENSION.XSLXRDH': [
        {required: true, message: "请输入联系电话", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      'EXTENSION.YXDZ': [
        {required: true, message: "请输入邮箱地址", trigger: "blur"},
        {max: 128, message: "最多输入128个字符", trigger: "blur"},
      ],
      // 注册日期
      ZCRQ: [{ required: true, message: "请选择注册日期", trigger: "blur" }],
    });
    const validateForm = () => {
      return Promise.all([vForm.value.validate()
      // , validateCbslb()
    ])
    };
//校验承包商类比
    // const validateCbslb = () => {
    //   return new Promise((resolve, reject) => {
    //     let params = {
    //       CBSYWID: props.defaultData.CBSYWID
    //     }
    //     axiosUtil.get('/backend/sccbsgl/cbsyj/validateCbslb', params).then(res => {
    //       if (res.data.ZT === '1') {
    //         resolve(true)
    //         props.defaultData.CBSLX = res.data.CBSLX
    //       } else {
    //         reject({
    //           CBSLB: [{
    //             message: res.data.msg
    //           }]
    //         })
    //       }
    //     })
    //   })
    // }

    watch(() => form, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])

    const initBgInfo = () => {
      const getObjValue = (obj,prop) => {
        let propNameList= prop.split('.')
        let value=obj;
        propNameList.forEach(name=>{
          value=value ? value[name] || '' : ''
        })
        return value
      }

      if (form.value && props.resultTableData) {
        let res = []
        let checkProp = ['CBSDWQC', 'DWJC', 'FDDBRXM', 'FDDBRLXFS', 'QYLXDM', 'ZCDZ', 'ZCZBJE', 'EXTENSION.yyfw',
          'TYXYDM', 'YXQJS', 'DWXTGSDM', 'KHXJKHH', 'KHXYXQJS', 'EXTENSION.ygrs', 'LXRXM', 'LXRSJH', 'ZCRQ', 'EXTENSION.SFYYJZRQ']

        let isBg = false
        let dbsj = []
        checkProp.forEach(ii => {
          if (getObjValue(props.resultTableData,ii) !== getObjValue(form.value,ii)) {
            dbsj.push({
              BGQ: getObjValue(props.resultTableData,ii),
              BGH: getObjValue(form.value,ii),
              ZDMC: ii
            })
            isBg = true
          }
        })
        if (isBg) {
          res.push({
            YWLX: 'JBXX',
            BGZT: 'BG',
            WYBS: form.value.CBSWYBS,
            BGXQ: dbsj
          })
        }

        BGXX.value = res
      }


    }


    const isChangeT = (prop) => {
      if(BGXX.value[0]){
        let info=BGXX.value[0]
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if(res){
          if(prop==='QYLXDM'){
            res.BGMS = `${qylxOptions.value.find((i) => i.DMXX === res.BGQ)?.DMMC || '空'} 变更为 ${qylxOptions.value.find((i) => i.DMXX === res.BGH)?.DMMC || '空'}`
          }else if(prop==='DWXTGSDM'){
            res.BGMS = `${dwxtgsOptions.value.find((i) => i.DMXX === res.BGQ)?.DMMC || '空'} 变更为 ${dwxtgsOptions.value.find((i) => i.DMXX === res.BGH)?.DMMC || '空'}`
          }else if(prop==='EXTENSION.SFYYJZRQ'){
            res.BGMS = `${sfyyjzrqOptions.value.find((i) => i.DMXX === res.BGQ)?.DMMC || '空'} 变更为 ${sfyyjzrqOptions.value.find((i) => i.DMXX === res.BGH)?.DMMC || '空'}`
          } else {
            res.BGMS = `${res.BGQ || '空'} 变更为 ${res.BGH || '空'}`
          }

          return res
        }
      }
      return null
    }

    onMounted(() => {
      getDwxtgsOptions();
      queryTableData();
      getQylx()

    })

    return {
      ...toRefs(state),
      validateForm,
      form,
      vFormRules,
      dwxtgsOptions,
      qylxOptions,
      activeName,
      fileTableData,
      BGXX,
      isChangeT,
      vForm,
      sfyyjzrqOptions



    }
  }

})
</script>

<style scoped>
.el-collapse {
  width: 100%;
}


:deep( .dataChange .el-radio){
  --el-radio-text-color: #F56C6C;
}
:deep( .dataChange  .el-radio__input.is-checked + .el-radio__label ){
  color: #F56C6C !important;
}
:deep( .dataChange .el-radio__input.is-checked .el-radio__inner){
  background: #F56C6C !important;
  border-color: #F56C6C !important;
}
:deep( .dataChange .el-input){
  --el-input-text-color : #F56C6C;
  --el-disabled-text-color: #F56C6C;
  color: #F56C6C !important;
}
:deep( .dataChange .el-textarea){
  --el-input-text-color : #F56C6C;
  --el-disabled-text-color: #F56C6C;
  color: #F56C6C !important;
}
:deep(.dataChange){
  color: #F56C6C;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
