<template>
  <div>
    <el-form :model="form" ref="vForm" class="lui-card-form" :rules="vFormRules" size="default" label-position="left" label-width="160px" :disabled="!editable">
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="队伍名称" prop="DWMC">
            <el-input v-model="form.DWMC"></el-input>
          </el-form-item>
        </el-col>
        <!--<el-col :span="8" class="grid-cell">
          <el-form-item label="类别" prop="DWLB">
            <el-select v-model="form.DWLB" clearable placeholder="请选择" :disabled="true">
              <el-option v-for="(item, index) in teamTypeList" :key="index" :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>-->
        <el-col :span="24" class="grid-cell">

          <el-form-item label="申请服务范围" prop="SQFWFW">
            <el-input type="textarea" :rows="2" v-model="form.SQFWFW" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item label="申请投标类型">
            <span>队伍信息变更</span>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item :class="{dataChange: isChangeT('TJDW')}" label="推荐单位/部门" prop="TJDW">
            <template #label>
              <div>
                推荐单位/部门:
                <el-tooltip :content="isChangeT('TJDW')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('TJDW')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
              <!-- <el-select
                  v-model="form.TJDW"
                  placeholder="请选择推荐单位"
                  filterable
                  clearable>
                <el-option
                    v-for="item in tjdw"
                    :key="item.DMXX"
                    :label="item.DMMC"
                    :value="item.DMXX"
                >
                </el-option>
              </el-select> -->
              <el-cascader v-model="form.TJDW" :options="tjdw" filterable placeholder="请选择推荐单位"
                         :props="{label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false}"
                         clearable disabled />
          </el-form-item>
        </el-col>
       <!-- <el-col :span="8" class="grid-cell">
          <el-form-item :class="{dataChange: isChangeT('SQLBDM')}" label="引入类型" prop="SQLBDM">
            <template #label>
              <div>
                引入类型:
                <el-tooltip :content="isChangeT('SQLBDM')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('SQLBDM')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-select v-model="form.SQLBDM" clearable placeholder="请选择">
              <el-option v-for="(item, index) in yrlxList" :key="index" :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item :class="{dataChange: isChangeT('BALXDM')}" label="备案类型" prop="BALXDM" v-if="form.SQLBDM == 'BADJ'">
            <template #label>
              <div>
                备案类型:
                <el-tooltip :content="isChangeT('BALXDM')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('BALXDM')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-select v-model="form.BALXDM" clearable placeholder="请选择">
              <el-option v-for="(item, index) in balxList" :key="index" :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>-->
        <!--<el-col :span="24" class="grid-cell">
          <el-form-item :class="{dataChange: isChangeT('BZ')}" label="备注" prop="BZ">
            <template #label>
              <div>
                备注:
                <el-tooltip :content="isChangeT('BZ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('BZ')">
                  <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input v-model="form.BZ" :rows="3" placeholder="因特殊原因备案的请在此说明"></el-input>
          </el-form-item>
        </el-col>-->
        <el-col :span="24" class="grid-cell">
          <el-form-item label="相关附件">
            <vsfileupload
                    style="margin-left: 10px"
                    :busId="form.DWYWID"
                    :editable="editable"
                    :key="form.DWYWID"
                    ywlb="YRSQXXXGFJ"
                    busType="dwxx"
                    :limit="10"
            ></vsfileupload>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, ref} from "vue";
import {getCommonSelectDMB} from "@src/api/cbsxx";
import {getCbsyjGetEntOrganizations} from "@src/api/sccbsgl";
import cascaderTree from "@lib/cascaderTree";
import {InfoFilled} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import vsfileupload from "@views/components/vsfileupload";


export default defineComponent({
  name: '',
  components: {InfoFilled, vsfileupload},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
  },
  setup(props, {emit}) {
    const state = reactive({})
    let form = reactive({})
    watch(
        () => props.defaultData,
        (val) => {
          form = val;
        },
        {
          immediate: true,
        }
    );
    const teamTypeList = ref([]);
    const getTeamTypeList = ()=>{
      getCommonSelectDMB({DMLBID: 'DWLB'}).then(res=>{
        teamTypeList.value = res.data.map(x=>{
          return {
            label: x.DMMC,
            value: x.DMXX
          }
        })
      })
    }
    const organizations = ref([]);

// 部门/单位 字典
    const organizationsDic = ref({})
    const getEntOrganizations = ()=>{
      getCbsyjGetEntOrganizations({}).then(res=>{
        if(!res.data) return
        organizationsDic.value = res.data.reduce((t, i) => {
          t[i.CODE] = i.NAME;
          return t
        }, {})
        if (res.data) organizations.value = new cascaderTree(res.data, "ORGID", "PORGID").init();
      })
    }
    const getYrlxList = ()=>{
      getCommonSelectDMB({DMLBID: 'YRLX'}).then(res=>{
        yrlxList.value = res.data.map(x=>{
          return {
            label: x.DMMC,
            value: x.DMXX
          }
        })
      })
    }

    let yrlxList = ref([])
    const queryBalx = () => {
      getCommonSelectDMB({DMLBID: 'BALX'}).then(res=>{
        balxList.value = res.data.map(x=>{
          return {
            label: x.DMMC,
            value: x.DMXX
          }
        })
      })
    }
    let balxList = ref([])
    let tableData = reactive([])
    /**查询备案类型 */


    /** 查询相关附件 */
    const queryXgfj = () => {
      tableData.length = 0
      tableData.push(...[
        {fileType: '推荐函', fileName: '', fileList: []},
        {fileType: '廉洁从业责任书', fileName: '', fileList: []},
        {fileType: '商业伙伴合规尽职调查表', fileName: '', fileList: []},
        {fileType: '申报队伍质量、健康、安全、环保体系及相关文件', fileName: '', fileList: []},
        {fileType: '申报队伍近两年安全事故说明', fileName: '', fileList: []},
        {fileType: '其他附件', fileName: '', fileList: []},
      ])
    }

    const vForm = ref(null);
    const vFormRules = ref({
      DWMC:[
        { required: false, message: '请输入企业(队伍)名称', trigger: 'blur' },
        { max: 128, message: '最多输入128个字符', trigger: 'blur' },
      ],
      TJDW:[
        { required: true, message: '请选择推荐单位/部门', trigger: 'change' },
      ],
      SQLBDM:[
        { required: true, message: '请选择引入类型', trigger: 'change' },
      ],
      BALXDM:[
        { required: true, message: '请选择备案类型', trigger: 'change' },
      ],
      SQFWFW:[
        { required: false, message: '请输入申请服务范围', trigger: 'blur' },
        { max: 4000, message: '最多输入4000个字符', trigger: 'blur' },
      ],
      BZ:[
        { required: false, message: '请输入备注', trigger: 'blur' },
        { max: 256, message: '最多输入256个字符', trigger: 'blur' },
      ],

    })
    const validateForm = () => {
      return vForm.value.validate()
    }
    const tjdw = ref([]);
    const tjdwList = ref([]);
    const getTjdw = () => {
        axiosUtil.get('/backend/common/selectTwoOrgAndJgbmList', {}).then((res) => {
          tjdwList.value=res.data;
          tjdw.value=res.data;
          // tjdw.value = comFun.treeData(res.data || [],'ORGNA_ID','PORGNA_ID','children','1458257119443951634')
        });
    };

    watch(() => form, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])

    const initBgInfo = () => {
      const getObjValue = (obj,prop) => {
        let propNameList= prop.split('.')
        let value=obj;
        propNameList.forEach(name=>{
          value=value ? value[name] || '' : ''
        })
        return value
      }

      if (form && props.resultTableData) {

        let res = []
        let checkProp = ['TJDW', 'SQLBDM', 'BALXDM','BZ']

        let isBg = false
        let dbsj = []
        checkProp.forEach(ii => {
          if (getObjValue(props.resultTableData,ii) !== getObjValue(form,ii)) {
            dbsj.push({
              BGQ: getObjValue(props.resultTableData,ii),
              BGH: getObjValue(form,ii),
              ZDMC: ii
            })
            isBg = true
          }
        })
        if (isBg) {
          res.push({
            YWLX: 'YRSQ',
            BGZT: 'BG',
            WYBS: form.DWWYBS,
            BGXQ: dbsj
          })
        }

        BGXX.value = res
      }


    }


    const isChangeT = (prop) => {
      if(BGXX.value[0]){
        let info=BGXX.value[0]
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if(res){
          if(prop==='TJDW'){
            res.BGMS = `${tjdwList.value.find((i) => i.ORGNA_ID === res.BGQ)?.ORGNA_NAME || '空'} 变更为 ${tjdwList.value.find((i) => i.ORGNA_ID === res.BGH)?.ORGNA_NAME || '空'}`
          }else if(prop==='SQLBDM'){
            res.BGMS = `${yrlxList.value.find((i) => i.value === res.BGQ)?.label || '空'} 变更为 ${yrlxList.value.find((i) => i.value === res.BGH)?.label || '空'}`
          } else if(prop==='BALXDM'){
            res.BGMS = `${balxList.value.find((i) => i.value === res.BGQ)?.label || '空'} 变更为 ${balxList.value.find((i) => i.value === res.BGH)?.label || '空'}`
          }else {
            res.BGMS = `${res.BGQ || '空'} 变更为 ${res.BGH || '空'}`
          }

          return res
        }
      }
      return null
    }

    onMounted(() => {
      getTeamTypeList();
      getEntOrganizations();
      getYrlxList()
      queryBalx()
      queryXgfj()
      getTjdw()
    })

    return {
      ...toRefs(state),
      validateForm,
      form,
      vFormRules,
      teamTypeList,
      yrlxList,
      balxList,
      tjdw,
      isChangeT,
      BGXX,
      vForm

    }
  }

})
</script>

<style scoped>
:deep( .dataChange .el-radio){
  --el-radio-text-color: #F56C6C;
}
:deep( .dataChange  .el-radio__input.is-checked + .el-radio__label ){
  color: #F56C6C !important;
}
:deep( .dataChange .el-radio__input.is-checked .el-radio__inner){
  background: #F56C6C !important;
  border-color: #F56C6C !important;
}
:deep( .dataChange .el-input){
  --el-input-text-color : #F56C6C;
  --el-disabled-text-color: #F56C6C;
  color: #F56C6C !important;
}
:deep( .dataChange .el-textarea){
  --el-input-text-color : #F56C6C;
  --el-disabled-text-color: #F56C6C;
  color: #F56C6C !important;
}
:deep(.dataChange){
  color: #F56C6C;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
