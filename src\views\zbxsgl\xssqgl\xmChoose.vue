<template>
    <div>
        <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
                 size="default">
            <el-row :gutter="20" class="lui-search-form">

                <el-col :span="6" class="grid-cell">
                    <el-form-item label="" prop="XMMC">
                        <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" class="grid-cell">
                    <el-form-item label="" prop="XMBH">
                        <el-input ref="input45296" placeholder="请输入项目编号" v-model="listQuery.XMBH" type="text" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>


                <el-col :span="4" class="grid-cell">
                    <div class="static-content-item" style="display: flex;">
                        <el-button ref="button91277" @click="getDataList" type="primary">
                            查询
                        </el-button>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <div class="container-wrapper">
                        <el-table ref="datatable91634" :data="tableData" height="400px"
                                  :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                                             :index="indexMethod"/>
                            <el-table-column prop="XMMC" label="采购项目名称" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="XMBH" label="项目编号" align="center"
                                             :show-overflow-tooltip="true" width="160"></el-table-column>
                            <el-table-column prop="SGSJ" label="施工时间" align="center"
                                             :show-overflow-tooltip="true" min-width="130"></el-table-column>
                            <el-table-column prop="SSDWMC" label="所属单位" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="CZ" label="操作" align="center" width="120">
                                <template #default="scope">
                                    <el-button size="small" class="lui-table-button" type="primary"
                                               @click="saveData(scope.row)">选择
                                    </el-button>
                                </template>
                            </el-table-column>

                        </el-table>
                        <el-pagination background v-model:current-page="listQuery.page"
                                       v-model:page-size="listQuery.size"
                                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                                       class="lui-pagination"
                                       @size-change="getDataList" @current-change="getDataList" :total="total">
                        </el-pagination>
                    </div>
                </el-col>
            </el-row>


        </el-form>

    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import axiosUtil from "@lib/axiosUtil";
    import {ElMessage} from "element-plus";
    import comFun from "@lib/comFun";
    import vsAuth from "@lib/vsAuth";


    export default defineComponent({
        name: '',
        components: {},
        props: {
            params: {
                type: Object,
                required: true
            },
        },
        setup(props, {emit}) {
            const state = reactive({
                listQuery: {
                    page: 1,
                    size: 10,
                },
                tableData: [],
                total: 0,
                checkList: [],

                PJLBOptions: [],
                ZRZYTree: [],
            })

            const getDataList = () => {
                let params = {
                    ...state.listQuery,
                    orgnaId: vsAuth.getAuthInfo().permission.orgnaId,
                }
                axiosUtil.get('/backend/xsgl/xssqgl/selectDxcgxmPage', params).then(res => {
                    state.tableData = res.data.list || []
                    state.total = res.data.total
                })

            }

            const saveData = (row) => {
                emit('submit', row)
            }


            const indexMethod = (index) => {
                return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
            }

            const closeForm = () => {
                emit('close')
            }

            const getDMBData = async (DMLBID, resList) => {
                let params = {
                    DMLBID
                }
                const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
                state[resList] = res.data
            }

            const getZrzyList = () => {
                let params = {}
                axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
                    state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0')
                });
            }

            onMounted(() => {
                getDataList()
                getZrzyList()
            })

            return {
                ...toRefs(state),
                indexMethod,
                getDataList,
                closeForm,
                saveData,

            }
        }

    })
</script>

<style scoped>

</style>
