<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

//控件中的一些常用方法都在这里调用，比如保存，打印等等
function AfterDocumentOpened() {
	/**
	 *pageofficectrl.InsertWebImage( ImageURL, Transparent, Position );
	 *ImageURL  字符串类型，图片的路径。
	 *Transparent  布尔类型，可选参数，图片是否透明。默认值：FALSE，图片不透明；TRUE表示图片透明。注意：透明色为白色。
	 *Position  整数类型，可选参数，浮于文字上方还是下方。默认值：4，图片浮于文字上方。 5，表示图片衬于文字下方。
	 */
	//该方法默认插入图片到当前光标处，如果想插入到文档指定位置，可以在文档中插入一个书签来设置位置，然后先定位光标到书签处再插入图片
	locateBookMark();
	pageofficectrl.word.InsertWebImage("/doc/InsertImgForJs/logo.jpg", false, 4);
}

//定位书签到光标处
function locateBookMark() {
	//获取书签名称
	var bkName = "PO_logo";
	pageofficectrl.word.LocateDataRegion(bkName);
}

function OnPageOfficeCtrlInit() {
	pageofficectrl.CustomToolbar = false;
}

function openFile() {
	return request({
		url: '/InsertImgForJs/Word',
		method: 'get',
	})
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit,AfterDocumentOpened};//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
