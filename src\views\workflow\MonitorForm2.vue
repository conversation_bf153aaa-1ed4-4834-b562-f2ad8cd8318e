<!-- 流程跟踪页面 -->
<template>
  <div style="padding: 20px">
    <!-- <el-row :gutter="10" style="height: 35px">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="流程记录" name="1"></el-tab-pane>
        <el-tab-pane label="流程监控" name="2"></el-tab-pane>
      </el-tabs>
    </el-row> -->
    <div id="monitorDiv" v-if="lcShow=='1'">
      <el-row :gutter="10" style="height: 35px;margin-top: 10px;">
        <el-col :span="8">
          <span>发起时间：{{ formData.CJSJ }}</span>
        </el-col>
        <el-col :span="8">
          <span> 发起人：{{ formData.CJR }} </span>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="height: 35px">
        <el-col :span="8">
          <span>流程状态：</span>
          <span v-if="formData.LCZT == '1'">运行中</span>
          <span v-else-if="formData.LCZT == '3'">结束</span>
          <span v-else-if="formData.LCZT == '4'">已终止</span>
          <span v-else>未发起</span>
        </el-col>
        <el-col :span="8">
          <span>当前工作内容：{{ formData.GZNR }}</span>
        </el-col>
        <el-col :span="8">
          <span>完成时间：{{ formData.SINCCPMPLETDATE }}</span>
        </el-col>
      </el-row>
      <hr/>
      <el-row :gutter="24" style="height: 50px">
        <el-col :span="22" align="center">
          <H3>业务办理跟踪详情</H3>
        </el-col>
        <el-col :span="2">
          <!--          <el-button type="primary" @click="exportExcel">导出</el-button>-->
        </el-col>
      </el-row>
      <el-table :data="tableData" height="500" :border="true" class="lui-table" size="default">
        <el-table-column type="index" width="50" label="序号" align="center"/>
        <el-table-column prop="BUSINESSNAME" label="业务环节" header-align="center" align="left" min-width="150px"></el-table-column>
        <el-table-column prop="CLR" label="办理人" header-align="center" align="left" width="300">
          <template #default="scope">
            <span>{{ scope.row.CLDW + "(" + scope.row.CLR + ")" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="STATUS" label="进度状态" width="100" header-align="center" align="center"></el-table-column>
        <el-table-column prop="JSSJ" label="接收时间" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="BLSJ" label="办理时间" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="APPRRESULT" label="审核结果" width="100" header-align="center"
                         align="center"></el-table-column>
        <el-table-column prop="OPINION" label="审查意见" header-align="center" align="left" width="150"></el-table-column>
      </el-table>
    </div>
    <div v-if="lcShow=='2'">
      <iframe style="width:100%;height:700px;margin-top: 10px;" :src="this.lcUrl" frameborder=0 scrolling="no"></iframe>
    </div>
  </div>
</template>
<script>
import axios from "axios";
import {ElLoading} from "element-plus";
import api from "../../api/lc";
import axiosUtil from "../../lib/axiosUtil";

export default {
  name: "MonitorForm2",
  props: {
    model: {
      type: String,
      default: 'scgl'
    },
    queryParams: Object,
  },
  data() {
    return {
      lcShow: '1',
      lcUrl: '',
      activeName: '1',
      formData: {},
      tableData: [],
      pramas: {},
    };
  },
  computed: {},
  watch: {},
  methods: {
    handleClick(tab, event) {
      this.lcShow = tab.name
      this.activeName = tab.name
    },
    queryProcess() {
      //this.lcUrl="http://bpm.slof.com/OperationCenter/ProcessPortal/UniteAbeyance/Page/ProcessMoni.aspx?processname=直属单位非招标申请审批流程&incidentno=163"
      let ld = ElLoading.service({
        target: "#monitorDiv",
        text: "正在加载数据，请稍后...",
      });
      var queryParams = {
        id:this.queryParams.id,
      }

      axiosUtil.post('/backend/workFlow/getProcessById', queryParams).then(res => {
        if (res.data && res.data.length > 0) {
          var data = res.data[0];
          var pramas = {
            processId: data.PROCESSID,
            processInstanceId: data.PROCESSINSTANCEID,
            id: data.ID
          };
          //this.lcUrl="http://bpm.slof.com/OperationCenter/ProcessPortal/UniteAbeyance/Page/ProcessMoni.aspx?processname="+data.PROCESSINSTANCENAME+"&"+"incidentno="+data.PROCESSINSTANCEID
          axiosUtil.post('/backend/workFlow/monitorTasks',pramas).then((res) =>{
            if (res.data) {
              this.formData = res.data;
              if (
                  res.data.tasksMonitorList &&
                  res.data.tasksMonitorList.length > 0
              ) {
                this.formData.GZNR =
                    res.data.tasksMonitorList[
                    res.data.tasksMonitorList.length - 1
                        ].BUSINESSNAME;
              }
              this.tableData = res.data.tasksMonitorList;
            }
            ld.close();
          }).catch((error) => {
            console.log(error);
            ld.close();
          });
        }else{
          ld.close();
        }
      })
    },
    exportExcel() {

      let column = [[
        {field: 'TASKNAME', title: '业务环节', width: 350, halign: 'center', align: 'left'},
        {field: 'BLR', title: '办理人', width: 280, halign: 'center', align: 'left'},
        {field: 'STATUS', title: '进度状态', width: 280, halign: 'center', align: 'left'},
        {field: 'JSSJ', title: '接收时间', width: 280, halign: 'center', align: 'left'},
        {field: 'BLSJ', title: '办理时间', width: 280, halign: 'center', align: 'left'},
        {field: 'APPRRESULT', title: '审核结果', width: 280, halign: 'center', align: 'left'},
        {field: 'OPINION', title: '审查意见', width: 280, halign: 'center', align: 'left'},
      ]]
      let finparams = {
        title: this.formData.YWMC + '流程跟踪',
        name: this.formData.YWMC + '流程跟踪',
        params: this.queryParams,
        column: column
      }
      axiosUtil.exportExcel('/backend/commonExport/taskMonitorExcel2', finparams)
    }
  },
  components: {},
  created() {
  },
  mounted() {
    this.queryProcess();
  },
};
</script>
<style scoped>
</style>