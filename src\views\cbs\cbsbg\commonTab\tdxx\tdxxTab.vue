<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        :model="tableData"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        class="lui-page"
        :disabled="!editable">
<!--      <div style="color: red">-->
<!--        {{ BGXX }}-->
<!--      </div>-->
      <el-table ref="dataTable" :data="[...tableData,...getDelRow()]" height="500px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column v-for="(item,index) in tableColumn" :key="index"
                         :prop="item.prop" :label="item.label" :align="item.align" :width="item.width"
                         :min-width="item.minWidth"
                         :show-overflow-tooltip="item.showOverflowTooltip">
          <template #default="{row,$index}" v-if="item.slot==='fileList'">
            <vsfileupload
                :maxSize="10"
                :index="$index"
                :ref="addRefs($index)"
                :editable="false"
                :busId="row.TDID"
                :key="row.TDID"
                ywlb="TDFJ"
                busType="dwxx"
                :limit="100"
            ></vsfileupload>
          </template>
          <template #default="{row,$index}" v-else-if="item.slot==='format'">
            {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='change'">
            <div :class="{dataChange : isChangeT(row.TDWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.TDWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.TDWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span>{{ row[item.prop] }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='formatChange'">
            <div :class="{dataChange : isChangeT(row.TDWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.TDWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.TDWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span> {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='info'">
            <el-tooltip :content="row[item.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="CZ" label="操作" align="center" width="250" fixed="right" v-if="editable">
          <template #default="{row,$index}">
            <div v-if="tableRowClassName({row})!=='warning-row'">
              <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button class="lui-table-button" @click="deleteRow(row, $index)">删除</el-button>
            </div>
            <div v-else>
              已删除
            </div>
          </template>
        </el-table-column>

      </el-table>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        title="信息项选择"
        v-model="chooseVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        top="50px"
        width="1200px">
      <tdxxXz
          :key="editIndex"
          :currentRow="currentRow"
          @updateChooseData="updateChooseData"
          @updateEditData="updateEditData"
          @close="chooseVisible = false"
          :TYXYDM="TYXYDM"
      />
    </el-dialog>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, ref, watch} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import {InfoFilled} from "@element-plus/icons-vue";
import tdxxXz from "@views/cbs/templateManagement/DataTemplateManagement/tdxx/tdxx_xz.vue";
import comFun from "@lib/comFun";

export default defineComponent({
  name: '',
  components: {vsfileupload, InfoFilled, tdxxXz},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    TYXYDM: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      chooseVisible: false,
      tableData: [],
      tableColumn: [
        {
          label: "专业名称",
          prop: "ZYMC",
          align: "center",
          showOverflowTooltip: true,
          width: 100,
          // slot: "select"
        },
        {
          label: "业务要求",
          prop: "YWYQ",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "录入资料说明",
          prop: "LRZLSM",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "信息项",
          prop: "XXXMC",
          align: "center",
          showOverflowTooltip: true,
          width: 150,
        },

        {
          label: "所在地",
          prop: "SZD",
          align: "center",
          width: 250,
          maxlength: 128,
          required: true,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "产权所有人",
          prop: "CQSYR",
          align: "center",
          //width: 200,
          maxlength: 64,
          width: 150,
          required: true,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "面积",
          prop: "MJ",
          align: "center",
          //width: 200,
          maxlength: 64,
          width: 150,
          required: true,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "数量",
          prop: "SL",
          align: "center",
          //width: 200,
          maxlength: 128,
          width: 150,
          required: true,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "附件",
          prop: "fileList",
          headerAlign: "center",
          align: "left",
          slot: "fileList",
          width: 200,
        },

      ],
    })
    const currentRow = ref({});
    const editIndex = ref(0);
    const refs = ref([]);
    const addRefs = (id) => {
      return (el) => {
        refs.value[id] = el;
      };
    }
    const updateChooseData = (val) => {
      changeData(currentRow.value, val, editIndex.value, false)
    };

    const changeData = (oldRow, newRow, index, visible) => {
      let params = {
        newId: oldRow.TDID,
        oldId: newRow.TDZSJID,
        cover: true
      }

      axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
        oldRow.TDZSJID = newRow.TDZSJID
        oldRow.SZD = newRow.SZD
        oldRow.CQSYR = newRow.CQSYR
        oldRow.MJ = newRow.MJ
        oldRow.SL = newRow.SL
        refs.value[editIndex.value].loadFileList()
        state.chooseVisible = visible;
      })
    }

    watch(() => state.tableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])
    const initBgInfo = () => {
      if (state.tableData && props.resultTableData) {
        let res = []
        let BGQBS = props.resultTableData.map(i => i.TDWYBS)
        let BGHBS = state.tableData.map(i => i.TDWYBS)
        let allBS = new Set([...BGQBS, ...BGHBS])
        allBS.forEach(item => {
          if (BGQBS.includes(item) && BGHBS.includes(item)) {
            let BGQXX = props.resultTableData.find(i => i.TDWYBS === item)
            let BGHXX = state.tableData.find(i => i.TDWYBS === item)

            let isBg = false
            let dbsj = []
            let checkProp = ['SZD', 'CQSYR', 'MJ', 'SL']
            checkProp.forEach(ii => {
              if ((BGQXX[ii] || '') !== (BGHXX[ii] || '')) {
                dbsj.push({
                  BGQ: BGQXX[ii] || '',
                  BGH: BGHXX[ii] || '',
                  ZDMC: ii
                })
                isBg = true

              }
            })
            if (isBg) {
              res.push({
                YWLX: 'TDXX',
                BGZT: 'BG',
                WYBS: item,
                BGXQ: dbsj
              })
            }

          } else if (BGQBS.includes(item) && !BGHBS.includes(item)) {
            res.push({
              YWLX: 'TDXX',
              BGZT: 'SC',
              WYBS: item,
            })
          } else if (!BGQBS.includes(item) && BGHBS.includes(item)) {
            res.push({
              YWLX: 'TDXX',
              BGZT: 'XZ',
              WYBS: item,
            })
          }
        })
        BGXX.value = res
      }

    }

    const getDelRow = () => {
      if(props.resultTableData && state.tableData){
        return props.resultTableData.filter(item => !state.tableData.find(ii => ii.TDWYBS === item.TDWYBS))
      }else {
        return []
      }
    }

    const tableRowClassName = ({row,index}) => {
      let info=BGXX.value.find(ii=>ii.WYBS===row.TDWYBS) || {}
      if (info.BGZT==='XZ'){
        return "success-row"
      }else if(info.BGZT==='SC'){
        return "warning-row"
      }

    }

    const isChangeT = (TDWYBS, prop) => {
      let info = BGXX.value.find(item => item.WYBS === TDWYBS && item.BGZT === 'BG')
      if (info) {
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if(res){
          res.BGMS = `${res.BGQ} 变更为 ${res.BGH}`
          return res
        }
      }
      return null
    }

    const updateEditData = (row) => {
      state.tableData.forEach((item, index) => {
        if (item.TDZSJID === row.TDZSJID) {
          changeData(item, row, index, true)
        }
      })
    }

    const chooseRow = (row, index) => {
      currentRow.value = row;
      editIndex.value = index;
      state.chooseVisible = true;
    };


    watch(() => props.defaultData, (val) => {
      if (val) {
        val.forEach((x) => {
          const UUID = comFun.newId()
          x.TDID = x.TDID || UUID;
          x.TDWYBS = x.TDWYBS || UUID;
        });
      }
      state.tableData = val;
    }, {immediate: true,})


    const insertRow = (row, index) => {
      const UUID = comFun.newId()
      state.tableData.splice(index + 1, 0, {
        TDID: UUID,
        TDWYBS: UUID,
        ZYMC: row.ZYMC,
        ZYBM: row.ZYBM,
        XXXMC: row.XXXMC,
        MBMXID: row.MBMXID,
        YWYQ: row.YWYQ,
        LRZLSM: row.LRZLSM,
        XXX: "",
        XMMC: "",
        JCQK: ""
      });
    };

    const deleteRow = (row, index) => {
      ElMessageBox.confirm("是否删除此条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        state.tableData.splice(index, 1);
        ElMessage({
          message: "删除成功!",
          type: "success",
        });
      }).catch(() => {
      });
    };

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      getDelRow,
      tableRowClassName,
      isChangeT,
      chooseRow,
      insertRow,
      deleteRow,
      editIndex,
      currentRow,
      updateChooseData,
      updateEditData,
      addRefs,
      BGXX


    }
  }

})
</script>

<style scoped>

</style>
