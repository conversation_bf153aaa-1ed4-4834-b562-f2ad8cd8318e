<!-- 评委抽取 -非招标编辑页 -->
<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" label-position="right" label-width="110px" size="default"
             status-icon>
      <el-row :gutter="12">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="会议名称:" prop="HYMC" class="required">
            <el-input v-model="formData.HYMC" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="评审时间:" prop="KBSJ" class="required">
            <el-date-picker v-model="formData.KBSJ" type="date" placeholder="请选择"
                            :disabled="!editable" value-format="YYYY-MM-DD"/>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="评审地点:" label-width="110px" prop="PBDD" class="required">
            <el-input v-model="formData.PBDD" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="16" class="grid-cell">
          <el-form-item label=" 说明:" label-width="100px" prop="BZ" class="required">
            <el-input v-model="formData.BZ" type="textarea" placeholder="请输入" clearable
                      :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <div class="zdTitle">待评审项目</div>
        <el-row>
          <el-col :span="23">
            <el-table :data="formData.dpsxmData" height="160px" :border="true"
                      :show-summary="false" size="default" :stripe="false" :highlight-current-row="true"
                      :cell-style="{ padding: '3px 0 ' }">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
              <el-table-column prop="XMMC" label="项目名称" align="center" min-width="200"></el-table-column>
              <el-table-column prop="XMBM" label="项目编号" align="center" min-width="200"></el-table-column>
              <el-table-column prop="XMLB" label="项目类别" align="center" min-width="200"></el-table-column>
              <el-table-column prop="SSDW" label="所属单位" align="center" min-width="200"></el-table-column>
              <el-table-column prop="XMED" label="项目额度" align="center" min-width="200"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" min-width="100" v-if="editable">
                <template #default="scope">
                  <el-button plain size="small" text type="primary"
                             @click="delXmData(scope.row,scope.$index)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="1">
            <el-button type="primary" size="small" @click="openDialog()"
                       v-if="editable">选择
            </el-button>
          </el-col>
        </el-row>
      </el-row>
      <el-row>
        <div class="zdTitle">专家抽取</div>
        <el-col :span="12">
          <el-row :gutter="12">
            <el-col :span="20" class="grid-cell">
              <el-form-item label=" 谈判人员:" label-width="100px" prop="TPRY" class="required">
                <el-input v-model="formData.TPRY" type="text" placeholder="请选择" clearable
                          :disabled="true">
                  <template #append v-if="editable">
                    <el-button :icon="More" @click="zjDialogVisible=true"/>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="20" class="grid-cell">
              <el-form-item label=" 监督人:" label-width="100px" prop="JDR" class="required">
                <el-input v-model="formData.JDR" type="text" placeholder="请选择" clearable
                          :disabled="true">
                  <template #append v-if="editable">
                    <el-button :icon="More" @click="ryDialogVisible=true"/>
                  </template>
                </el-input>

              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

      </el-row>
    </el-form>
    <div style="width: 100%;display: flex;justify-content: center;">
      <el-row :gutter="12" style="width: 300px;">
        <el-col v-if="editable" :span="8">
          <el-button type="success" @click="validateForm('save')">暂存</el-button>
        </el-col>
        <el-col v-if="editable" :span="8">
          <el-button type="primary" @click="validateForm('submit')">提交</el-button>
        </el-col>
        <el-col :span="8">
          <el-button @click="closeForm()">返回</el-button>
        </el-col>
      </el-row>
    </div>
    <el-dialog
        v-if="xmdialogVisible"
        v-model="xmdialogVisible"
        title="待评审项目选择"
        @closed="closeXmForm"
        top="2vh"
        width="70%">
      <div>
        <cgxmxzForm :xmParams="xmParams" @closeXmForm="closeXmForm" @parentMethod="parentMethod"/>
      </div>
    </el-dialog>
    <el-dialog
        v-if="ryDialogVisible"
        v-model="ryDialogVisible"
        title="人员选择"
        top="2vh"
        width="900px">
      <div>
        <ryxz @getData="getRyxx"></ryxz>
      </div>
    </el-dialog>
    <el-dialog
        v-if="zjDialogVisible"
        v-model="zjDialogVisible"
        title="专家选择"
        top="2vh"
        width="900px">
      <div>
        <zjxz @getData="getZjxx"></zjxz>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted, watch
}
  from 'vue'
import axiosUtil from "../../../lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth.js";
import {ElMessage} from 'element-plus'
import {More} from '@element-plus/icons-vue'
import comFun from "../../../lib/comFun";
import Vsfileupload from "../../components/vsfileupload";
import cgxmxzForm from "./cgxmxz.vue"
import Ryxz from "../zjcrk/common/ryxz";
import Zjxz from "../zjcrk/common/zjxz";


export default defineComponent({
  components: {Ryxz, Vsfileupload, cgxmxzForm, Zjxz},
  props: {
    params: {
      type: Object,
      required: true
    }
  },

  setup(props, context) {
    const state = reactive({
      editable: props.params.editable,
      operation: props.params.operation,
      id: props.params.id,
      xmdialogVisible: false,
      ryDialogVisible: false,
      zjDialogVisible: false,
      userInfo: vsAuth.getAuthInfo().permission,
      CQGZBS: null,
      PWCQJLBS: null,
      CQPC: null,
      formData: {
        CBSDWQC: null,
        HYMC: null,
        PSSJ: null,
        PBDD: null,
        SM: null,
        TPRY: '',
        JDR: '',
        dpsxmData: [],
        zjcqData: []
      },
      tpryList: [],
      jdrList: [],

      xmParams: null,
      rules: {
        HYMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        KBSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        PBDD: [{
          required: true,
          message: '字段值不可为空',
        }],
        JDR: [{
          required: true,
          message: '字段值不可为空',
        }],
        TPRY: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
    })
    watch(() => state.tpryList, (newVal) => {
      let res = []
      if (newVal) {
        newVal.forEach(item => {
          res.push(item.XM)
        })
      }
      state.formData.TPRY = res.join(',')
    })
    watch(() => state.jdrList, (newVal) => {
      let res = []
      if (newVal) {
        newVal.forEach(item => {
          res.push(item.XM)
        })
      }
      state.formData.JDR = res.join(',')
    })
    const queryPwcqForm = () => {
      return new Promise(resolve => {
        let params = {
          PBHYBS: state.id
        }
        axiosUtil.get('/backend/zjgl/pwcqgl/queryPwcqForm', params).then((res) => {
          if (res.data.length > 0) {
            state.formData = {...res.data[0]}
            state.CQGZBS = state.formData.CQGZBS
            if (state.CQGZBS == null) {
              state.CQGZBS = comFun.newId()
            }
            state.PWCQJLBS = state.formData.PWCQJLBS
            if (state.PWCQJLBS == null) {
              state.PWCQJLBS = comFun.newId()
            }
            state.CQPC = state.formData.CQPC
            if (state.CQPC == null) {
              state.CQPC = 0
            }
          }
          queryDpsxmData()
          queryZjcqData()
          resolve(true)
        });
      })
    }
    const parentMethod = (val) => {
      let data = val
      data.forEach(item => {
        item.ID = comFun.newId();
        item.PBHYBS = props.params.id;
        item.PSDXBS = item.CGXMBS
      })
      state.formData.dpsxmData = data
      state.xmdialogVisible = false
    }
    //待评审项目 删除
    const delXmData = (row, i) => {
      state.formData.dpsxmData.splice(i, 1)
      //真正删除
      RealDeleteXm(row)
    }
    const RealDeleteXm = (row) => {
      axiosUtil.post('/backend/zjgl/pwcqgl/deleteDpsxm', row).then((res) => {
        if (res.message === 'success') {
          ElMessage({
            message: `删除成功`,
            type: 'success',
          })
          queryDpsxmData();
        }
      });
    }
    const queryDpsxmData = () => {
      let params = {
        PBHYBS: state.id
      }
      axiosUtil.get('/backend/zjgl/pwcqgl/queryDpsxmData', params).then((res) => {
        state.formData.dpsxmData = res.data
      });
    }
    const queryZjcqData = () => {
      let params = {
        PBHYBS: state.id
      }
      axiosUtil.get('/backend/zjgl/pwcqgl/queryZjcqData', params).then((res) => {
        state.formData.zjcqData = res.data
        if (res.data) {
          let jdrList = []
          let tpryList = []
          res.data.forEach(item => {
            if (item.SFJDRY === '1') {
              jdrList.push(item)
            } else {
              tpryList.push(item)
            }
          })
          state.jdrList = jdrList
          state.tpryList = tpryList

        }
      });
    }
    const closeForm = () => {
      context.emit("closeForm")
    }
    const instance = getCurrentInstance()
    const validateForm = (type) => {
      return new Promise(resolve => {
        if (type === 'submit') {
          if (state.formData.dpsxmData.length === 0) {
            ElMessage({
              message: `请选择待评审项目`,
              type: 'error',
            })
            resolve(false)
            return
          }
          instance.proxy.$refs['vForm'].validate(valid => {
            if (valid) {
              state.formData.SHZT = '1'
              //TODO: 提交表单
              resolve(submitForm(type))
            }
          })
        } else {
          state.formData.SHZT = '0'
          resolve(submitForm(type))
        }
      })
    }
    const submitForm = (type) => {
      let params = {
        ...state.formData,

      }
      let zjcqData = []
      zjcqData.push(...state.tpryList, ...state.jdrList)
      params.zjcqData = zjcqData
      if (state.operation !== 'add') {
        params.XGR = state.userInfo.userLoginName
        params.XGSJ = comFun.getNowTime();
      } else {
        params.PBHYBS = props.params.id;
        params.CJR = state.userInfo.userLoginName
        params.CJSJ = comFun.getNowTime();
        params.HYLX = 'FZB'
      }
      if (type === 'submit') {
        params.SHR = state.userInfo.userLoginName
        params.SHRQ = comFun.getNowTime();
      }

      return new Promise(resolve => {
        axiosUtil.post('/backend/zjgl/pwcqgl/savePwcqglData', params).then((res) => {
          if (res.message === 'success') {
            resolve(true)
            if (type) {
              ElMessage({
                message: `${type === 'submit' ? '提交' : '保存'}成功`,
                type: 'success',
              })
            }
            if (type === 'submit') {
              context.emit("closeForm")
            }
          } else {
            resolve(false)
          }
        });
      })
    }
    const closeXmForm = () => {
      state.xmdialogVisible = false
      //查询
    }
    const openDialog = () => {
      //state.xmParams.tableData = state.formData.dpsxmData
      state.xmdialogVisible = true
    }

    const getRyxx = (value) => {
      let res = []
      if (value) {
        value.forEach(item => {
          res.push({
            XM: item.USER_NAME,
            PBPWBS: comFun.newId(),
            PBHYBS: props.params.id,
            RYBS: item.USER_ID,
            ZJCQFS: 'ZD',
            SJH: item.USER_MOBILE,
            SFJDRY: '1',
            SZDWMC: item.ORGNA_NAME,
            CJR: state.userInfo.userLoginName,
            CJSJ: comFun.getNowTime()
          })
        })
      }
      state.jdrList = res
      state.ryDialogVisible = false
    }
    const getZjxx = (value) => {
      let res = []
      if (value) {
        value.forEach(item => {
          res.push({
            XM: item.XM,
            PBPWBS: comFun.newId(),
            PBHYBS: props.params.id,
            RYBS: item.ZJBS,
            ZJCQFS: 'ZD',
            SJH: item.RKZJBXX.BGDH,
            SZDWMC: item.GZDW,
            ZJLB: item.ZJLB,
            ZJLX: item.ZJLX,
            CSZYMC: item.XCSZYMC,
            CJR: state.userInfo.userLoginName,
            CJSJ: comFun.getNowTime()
          })
        })
      }
      state.tpryList = res
      state.zjDialogVisible = false
    }
    onMounted(() => {
      let params = props.params
      if (params && params.operation !== 'add') {
        queryPwcqForm()
      } else {
        state.CQGZBS = comFun.newId()
        state.PWCQJLBS = comFun.newId()
        state.CQPC = 0
      }
    })
    return {
      ...toRefs(state),
      submitForm,
      validateForm,
      closeForm,
      More,
      openDialog,
      closeXmForm,
      delXmData,
      parentMethod,
      getRyxx,
      getZjxx
    }
  }
})
</script>

<style scoped>
.zdTitle {
  background-color: #E4E6F6;
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  font-weight: 600;
  margin-bottom: 20px;
}

:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: unset;
}
/deep/ .el-textarea.is-disabled .el-textarea__inner {
  background-color: unset;
}
</style>
