<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Close(){
	pageofficectrl.CloseWindow();
}

function increaseCount(value) {
	CallParentFunc({
		funcName: 'updateCount',
		paramJson: value,
		success: function (strRet) {
			alert("现在父窗口Count的值为：" + strRet);
		},
		error: function (strRet) {
			if (strRet.indexOf('parentlost') > -1) {
				alert('error: 父页面关闭或跳转刷新了，导致父页面函数没有调用成功！');
			}
			else console.error(strRet);
		}
	});
}

function increaseCountAndClose(value) {
	CallParentFunc({
		funcName: 'updateCount',
		paramJson: value,
		success: function (strRet) {
			alert("现在父窗口Count的值为：" + strRet);
		},
		error: function (strRet) {
			if (strRet.indexOf('parentlost') > -1) {
				alert('error: 父页面关闭或跳转刷新了，导致父页面函数没有调用成功！');
			}
			else console.error(strRet);
		}
	});
	pageofficectrl.CloseWindow();
}

function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("关闭", "Close", 21);
}

function openFile() {
	return request({
		url: '/CallParentFunction/Word',
		method: 'get',
	})
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit,Close };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<input type="button" value="设置父窗口Count的值加1" @click="increaseCount(1);" />
		<input type="button" value="设置父窗口Count的值加5，并关闭窗口" @click="increaseCountAndClose(5);" /><br>
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>