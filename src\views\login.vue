<template>
  <div class="wrapper vsui-layout vsui-theme vsui-style-theme-color-2">
    <video loop muted autoplay playsinline poster="/static/img/loginbg.jpg" class="background-video">
      <source src="/static/img/loginbg.png" type="video/mp4">
    </video>
    <vs-head></vs-head>

    <div class="video-shadow">

    </div>
    <div class="ms-login">
      <div class="ms-title">{{ runtimeCfg.app_project_name }}</div>
      <el-form label-width="0px" class="loginForm">
        <el-form-item prop="username">
          <el-input v-model="username" placeholder="请输入用户名" clearable></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="password" placeholder="请输入密码" clearable type="password"></el-input>
        </el-form-item>
        <div class="login-btn">
          <el-button size="normal" type="primary" @click="login">登录</el-button>
        </div>
        <div class="forgotPwd"></div>
        <span class="cbszc" @click="contractorRegisterVisible = true">承包商注册</span>
<!--        <span class="yhzc" @click="userRegisterVisible = true;" style="margin-left: 20px;">用户注册</span>-->
      </el-form>
    </div>
    <vs-bottom></vs-bottom>
  </div>
  <el-dialog
      title="承包商注册"
      v-model="contractorRegisterVisible"
      width="1000px"
      top="50px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      append-to-body
      z-index="1000"
  >
    <contractor-register @close="contractorRegisterVisible = false"/>
  </el-dialog>
  <el-dialog
      title="用户注册"
      v-model="userRegisterVisible"
      width="900px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
  >
    <user-register @close="userRegisterVisible = false"/>
  </el-dialog>

</template>


<script>
import {useRoute, useRouter} from "vue-router";
import vsHead from '../components/common/loginHeader.vue'
import vsBottom from '../components/common/loginBottom.vue'
import {vue, auth, runtimeCfg, mixin} from '../assets/core/index'
import {ElMessage} from 'element-plus';
import Constellation from '../lib/constellation.js';
import {ref} from "vue";
// 承包商注册
import ContractorRegister from '@views/cbs/cbszc/contractorRegister.vue';
// 用户注册
import UserRegister from '@views/cbs/cbszc/userRegister.vue';
import CryptoJS from "crypto-js";
import axiosUtil from "../lib/axiosUtil";

export default {
  components: {
    vsHead,
    vsBottom,
    ContractorRegister,
    UserRegister,
  },
  setup() {
    const {
      vsuiRouter,
    } = mixin();
    const route111 = useRoute();
    const router111 = useRouter();
    let username = vue.ref(''), password = vue.ref('');
    const initConstellation = function () {
      if (!document.querySelector('.constellation')) {
        return
      }
      const con = new Constellation(document.querySelector('.constellation'), {
        star: {
          color: 'rgba(255, 255, 255, .8)',
          width: 1.5,
          randomWidth: true
        },
        line: {
          color: 'rgba(255, 255, 255, .8)',
          width: 0.2
        },
        position: {
          x: 0, // This value will be overwritten at startup
          y: 0 // This value will be overwritten at startup
        },
        width: window.innerWidth,
        height: window.innerHeight,
        velocity: 0.3,
        length: 80,
        distance: (window.innerWidth < 1000) ? 40 : 100,
        radius: 250,
        stars: []
      })
      con.init()
    };
    const login = function () {
      console.log('this.username:' + username.value)
      if (username.value == '') {
        ElMessage({
          message: '请输入用户名！',
          type: 'error'
        })
        return
      }
      if (password.value == '') {
        ElMessage({
          message: '请输入密码',
          type: 'error'
        })
        return
      }
      auth.login(username.value, password.value).then(() => {
        let params = {
          username: username.value,
          type: 'success'
        }
        axiosUtil.get('/backend/common/dlgl/dlsbcl',params)
        vsuiRouter.push('/')
      }).catch(error => {
        let params = {
          username: username.value,
          type: 'error'
        }
        axiosUtil.get('/backend/common/dlgl/dlsbcl',params).then(res => {
            ElMessage.error(`登录过程出错，失败原因是：${res.data}`)
        })

        // ElMessage({
        //   message: `登录过程出错，失败原因是：${error.message}`,
        //   dangerouslyUseHTMLString: true,
        //   type: 'error'
        // })
      })
    };
    const contractorRegisterVisible = ref(false)
    const userRegisterVisible = ref(false)

    vue.onMounted(() => {
      //initConstellation()
    })

    return {
      runtimeCfg,
      username,
      password,
      initConstellation,
      login,
      vsuiRouter,
      contractorRegisterVisible,
      userRegisterVisible
    }
  },
}
</script>
<style src="../../node_modules/element-plus/theme-chalk/index.css" scoped>
</style>

<style scoped>

.wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  /*background-image: url(../../static/img/loginbg.png);
  background-repeat: no-repeat;
  background-size:100% 100%;
  */
}

.wrapper .ms-login {
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 10%;
  top: 30%;
  width: 380px;
  height: 360px;
  margin: -50px 0 0 -200px;
  border-radius: 5px;
  background: #fff;
  opacity: 0.8;
}

.wrapper .ms-login .ms-title {
  width: 90%;
  position: relative;
  margin: 25px auto 30px auto;
  text-align: left;
  font-size: 20px;
  color: #000;
}

.wrapper .ms-login .loginForm {
  position: relative;
  flex: 1;
  width: 90%;
  margin: 20px auto 20px auto;
}


.wrapper .ms-login .loginForm .forgotPwd {
  float: right;
}

.loginForm .el-form-item {
  height: 32px;
}

.loginForm .el-form-item .el-form-item__content > * {
  height: 100%;
}

.login-btn {
  text-align: right;
}

.wrapper .constellation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.wrapper .video-shadow {
  position: absolute;
  top: 0px;
  width: 100%;;
  height: 100%;
  background: #000;
  opacity: 0.5;
}

.wrapper .background-video {
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: -100;
  min-width: 100%;
  min-height: 100%;
  width: 100%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.cbszc, .yhzc {
  color: #409EFF;
  position: absolute;
  cursor: pointer;
}

.cbszc {
  left: 0;
  bottom: 0;
}

.yhzc {
  right: 0;
  bottom: 0;
}
</style>
