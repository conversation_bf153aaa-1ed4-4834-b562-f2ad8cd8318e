import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"


// 承包商管理-通用接口-未定义名称
// @method getCommonTest
// @type get
// @return url
//getCommonTest: `/cbsxx/common/test`,

// eslint-disable-next-line
export function getCommonTest(params) {
    return axiosUtil.get(`${baseUrl}/cbsxx/common/test`, params)
}
// 承包商管理-通用接口-附件表查询
// @method getCommonSelectFjb
// @type get
// @return url
//getCommonSelectFjb: `/cbsxx/common/selectFjb`,

// eslint-disable-next-line
export function getCommonSelectFjb(params) {
    return axiosUtil.get(`${baseUrl}/cbsxx/common/selectFjb`, params)
}
// 承包商管理-通用接口-代码表查询
// @method getCommonSelectDMB
// @type get
// @return url
//getCommonSelectDMB: `/cbsxx/common/selectDMB`,

// eslint-disable-next-line
export function getCommonSelectDMB(params) {
    return axiosUtil.get(`${baseUrl}/cbsxx/common/selectDMB`, params)
}
// 承包商管理-通用接口-代办已办工作流程
// @method getCommonGetTasksList
// @type get
// @return url
//getCommonGetTasksList: `/cbsxx/common/getTasksList`,

// eslint-disable-next-line
export function getCommonGetTasksList(params) {
    return axiosUtil.get(`${baseUrl}/cbsxx/common/getTasksList`, params)
}
// 承包商管理-通用接口-查询当前登录人所在单位
// @method getCommonSelectOrganByUserId
// @type get
// @return url
//getCommonSelectOrganByUserId: `/cbsxx/common/selectOrganByUserId`,

// eslint-disable-next-line
export function getCommonSelectOrganByUserId(params) {
    return axiosUtil.get(`${baseUrl}/cbsxx/common/selectOrganByUserId`, params)
}
// 承包商管理-通用接口-条件查询用户信息
// @method getCommonGetUserByParam
// @type get
// @return url
//getCommonGetUserByParam: `/cbsxx/common/getUserByParam`,

// eslint-disable-next-line
export function getCommonGetUserByParam(params) {
    return axiosUtil.get(`${baseUrl}/cbsxx/common/getUserByParam`, params)
}




