<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("保存", "Save", 1);
	pageofficectrl.AddCustomToolButton("打印设置", "PrintSet", 0);
	pageofficectrl.AddCustomToolButton("打印", "PrintFile", 6);
	pageofficectrl.AddCustomToolButton("全屏/还原", "IsFullScreen", 4);
	pageofficectrl.AddCustomToolButton("-", "", 0);
	pageofficectrl.AddCustomToolButton("关闭", "Close", 21);
}
function Save() {
	pageofficectrl.SaveFilePage = "/DisableCopyOut/save";
	pageofficectrl.WebSave();
}

function PrintSet() {
	pageofficectrl.ShowDialog(5);
}

function PrintFile() {
	pageofficectrl.ShowDialog(4);
}

function Close() {
	pageofficectrl.CloseWindow();
}

function IsFullScreen() {
	pageofficectrl.FullScreen = !pageofficectrl.FullScreen;
}

function openFile() {
	// 发起GET请求到后端Controller的路由
	return request({
		url: '/DisableCopyOut/Word',
		method: 'get',
	});
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit,Save,PrintSet,PrintFile,Close,IsFullScreen };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
	<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
</template>
