<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'
const poHtmlCode = ref('');
const templateName = ref("temp2008.docx");

function handleChange() {
	pageofficectrl.Enabled = !pageofficectrl.Enabled;
}

function taoHong() {
	let tname = templateName.value;
	//套红前判断是否修改了正文文件，如果修改了正文文件，提示用户不能直接套红，否则会丢失刚修改的内容
	if (pageofficectrl.IsDirty) {
		let flag = confirm("正文已被修改，如果继续套红会导致刚编辑的内容丢失，是否继续套红？")
		if (flag) {
			openFile(tname).then(response => {
				poHtmlCode.value = response;
				pageofficectrl.Reload();
			});
		} else {
			return false;
		}
	}
	openFile(tname).then(response => {
		poHtmlCode.value = response;
		pageofficectrl.Reload();
	});

}

//保存并关闭
function saveAndClose() {
	pageofficectrl.SaveFilePage = "/TaoHong/save?fileName=zhengshi.docx";
	pageofficectrl.WebSave();
	pageofficectrl.CloseWindow();
}

function openFile(tname) {
	return request({
		url: '/TaoHong/taoHong?mb=' + tname,
		method: 'get',
	})
}
onMounted(() => {
	// 请求后端打开文件
	openFile("").then(response => {
		poHtmlCode.value = response;
	});;

})
</script>

<template>
	<div class="Word">
		<div id="content">
			<div id="textcontent" style="width: auto; height: 800px;">
				<div class="flow4">
					<span style="width: 100px;"> </span><strong>文档主题：</strong>
					<span style="color: Red;">测试文件</span>
					<el-form id="form1">
						<strong>模板列表：</strong>
						<el-select v-model="templateName" @visible-change="handleChange()" style='width: 240px;'>
							<el-option value="temp2008.docx" label="红头文件一"></el-option>
							<el-option value="temp2009.docx" label="红头文件二"></el-option>
							<el-option value="temp2010.docx" label="红头文件三"></el-option>
						</el-select>
						<el-button type="primary" @click="taoHong">一键套红</el-button>
						<el-button @click="saveAndClose">保存关闭</el-button>
					</el-form>
				</div>
				<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
				<div style="width:auto; height:840px;" v-html="poHtmlCode"></div>
			</div>
		</div>
	</div>
</template>