<template>
    <div style="height: calc(100% - 40px);">
        <el-form
                class="lui-page"
                :model="state"
                ref="vForm"
                label-width="0"
                :inline="false"
                size="default"
                style="height: 100%"
                :disabled="!editable"
        >
            <el-table
                    highlight-current-row
                    size="default"
                    ref="table"
                    height="100%"
                    fit
                    class="lui-table"
                    :border="false"
                    :data="state.tableData"
            >
                <EleProTableColumn
                        v-for="prop in state.tableColumn"
                        :col="prop"
                        :key="prop.columnKey"
                >
                  <template #info="{ row }">
                    <el-tooltip :content="row[prop.prop]" placement="bottom" effect="light">
                      <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </template>
                    <template #fileList="{ row, $index }">
                        <vsfileupload
                            :maxSize="10"
                                @getFileList="getFileList"
                                :index="$index"
                                :ref="addRefs($index)"
                                :editable="false"
                                :busId="row.CLID"
                                :key="row.CLID"
                                ywlb="CLFJ"
                                busType="dwxx"
                                :limit="100"
                        ></vsfileupload>
                    </template>
                    <template #opration="{ row, $index }">
                        <div>
                          <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
                            <el-button class="lui-table-button" @click="editRow(row, $index)">查看</el-button>
<!--                            <el-button class="lui-table-button" @click="copyRow(row, $index)">复制</el-button>-->
                            <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
                            <el-button v-if="row.SHZT != 1 && row.EXTENSION?.SJLY!=='MBSC'" class="lui-table-button" @click="deleteRow(row, $index)"
                            >删除
                            </el-button
                            >
                        </div>
                    </template>
                </EleProTableColumn>
            </el-table>
        </el-form>

        <el-dialog
                custom-class="lui-dialog"
                title="查看车辆信息"
                v-model="editVisible"
                width="1200px"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                destroy-on-close
                @close="
              () => {}
            "
        >
            <clEdit :editData="editData" @close="editVisible = false" :editable="false"
                    @updateData="updateData"
            />
        </el-dialog>

    </div>

  <el-dialog
      custom-class="lui-dialog"
      title="信息项选择"
      v-model="state.chooseVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      top="50px"
      width="1200px"
      @close="() => {}"
  >
    <clxxXz
        :key="editIndex"
        :currentRow="currentRow"
        @updateChooseData="updateChooseData"
        @updateEditData="updateEditData"
        @close="state.chooseVisible = false"
        :TYXYDM="TYXYDM"
    />
  </el-dialog>
</template>
<script setup>
    import {defineProps, reactive, watch, ref} from "vue";
    import {ElMessage, ElMessageBox} from "element-plus";
    import EleProTableColumn from "@src/components/ele-pro-table-column";

    import {v4 as uuidv4} from "uuid";
    import vsfileupload from "@src/views/components/vsfileupload.vue";
    import clEdit from "./clEdit.vue";
    import clxxXz from "./clxx_xz";
    import axiosUtil from "../../../../../lib/axiosUtil";
    import {InfoFilled} from "@element-plus/icons-vue";

    const getFileList = (res) => {
        state.tableData[res.index].fileList = res.fileList;
    };
    const props = defineProps({
        defaultData: {
            type: Array,
            defaultData: () => [],
        },
        proDetails: {
            type: Array,
            defaultData: () => [],
        },
        // 结果表数据，比对用
        resultTableData: {
            type: Object,
            default: () => null,
        },
        // 是否查看模式
        editable: {
            type: Boolean,
            default: true
        },
      TYXYDM: {
        type: String,
        default: ''
      }
    });
    const state = reactive({
      chooseVisible:false,
        tableData: [{}],
        tableColumn: [
            {
                label: "序号",
                type: "index",
                width: 55,
                align: "center",
            },
            {
                label: "专业名称",
                prop: "ZYMC",
                align: "center",
                showOverflowTooltip: true,
                width: 100,
            },
          {
            label: "业务要求",
            prop: "YWYQ",
            align: "center",
            showOverflowTooltip: true,
            width: 80,
            slot: "info"
          },
          {
            label: "录入资料说明",
            prop: "LRZLSM",
            align: "center",
            showOverflowTooltip: true,
            width: 80,
            slot: "info"
          },
            {
            label: "信息项",
            prop: "XXXMC",
            align: "center",
            showOverflowTooltip: true,
            width: 150,
            },

            {
                label: "车辆名称",
                prop: "CLMC",
                align: "center",
                width: 150,
            },
            // {
            //     label: "基本要求",
            //     prop: "JBYQ",
            //     align: "center",
            //     width: 150,
            // },
            {
                label: "车型",
                prop: "CLLXMC",
                align: "center",
                width: 120,
            },
            {
                label: "吨(座)位",
                prop: "DZW",
                align: "center",
                width: 120,
            },
            {
                label: "车牌号",
                prop: "CPH",
                align: "center",
                width: 120,
            },
            {
                label: "品牌型号",
                prop: "PPXH",
                align: "center",
                width: 120,
            },
            {
                label: "规格型号",
                prop: "GGXH",
                align: "center",
                width: 120,
            },
            {
                label: "购置时间",
                prop: "GZSJ",
                align: "center",
                width: 150,
            },
            {
                label: "备注",
                prop: "BZ",
                align: "left",
                headerAlign: "center",
                width: 150,
            },
            {
                label: "附件",
                prop: "fileList",
                headerAlign: "center",
                align: "left",
                slot: "fileList",
                width: 200,
            },
            {
                label: "操作",
                align: "center",
                width: 200,
                fixed: "right",
                slot: "opration",
                hide: !props.editable
            },
        ],
        // 弹窗
        editVisible: false,
        editData: {},
    });

    const currentRow = ref({});
    const refs = ref([]);
    const addRefs=(id)=> {
      return (el) => {
        refs.value[id] = el;
      };
    }
    const updateChooseData = (val) => {
      changeData(currentRow.value,val,editIndex.value,false)
    };

    const changeData = (oldRow,newRow,index,visible) => {
      let params={
        newId: oldRow.CLID,
        oldId: newRow.CLZSJID,
        cover: true
      }

      axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
        if(oldRow.EXTENSION?.SJLY==='MBSC'){
          if(!newRow.EXTENSION){
            newRow.EXTENSION={
              SJLY: 'MBSC'
            }
          }else {
            newRow.EXTENSION.SJLY='MBSC'
          }
        }

        oldRow.EXTENSION=newRow.EXTENSION
        oldRow.CLZSJID=newRow.CLZSJID
        oldRow.CPH=newRow.CPH
        oldRow.CLMC=newRow.CLMC
        oldRow.SCCJ=newRow.SCCJ
        oldRow.PPXH=newRow.PPXH
        oldRow.CLLXMC=newRow.CLLXMC
        oldRow.DZW=newRow.DZW
        oldRow.GGXH=newRow.GGXH
        oldRow.DJH=newRow.DJH
        oldRow.FDJBH=newRow.FDJBH
        oldRow.NJRQ=newRow.NJRQ
        oldRow.YXQJSRQ=newRow.YXQJSRQ
        oldRow.NJJG=newRow.NJJG
        oldRow.GDSYNX=newRow.GDSYNX
        oldRow.GZSJ=newRow.GZSJ
        oldRow.BDBH=newRow.BDBH
        oldRow.BDJG=newRow.BDJG
        oldRow.BZ=newRow.BZ
        oldRow.CLLXDM=newRow.CLLXDM
        refs.value[editIndex.value].loadFileList()
        state.chooseVisible = visible;
      })
    }


    const updateEditData = (row) => {
      state.tableData.forEach((item,index)=>{
        if(item.CLZSJID===row.CLZSJID){
          changeData(item,row,index,true)
        }
      })
    }

    const chooseRow = (row, index) => {
      currentRow.value=row;
      editIndex.value = index;
      state.chooseVisible = true;
    };

    const changePro = (row) => {
        row.ZYMC = props.proDetails.filter((x) => x.ZYBM == row.ZYBM)[0].ZYMC;
    };
    watch(
        () => props.defaultData,
        (val) => {
            if (val) {
                val.forEach((x) => {
                    const UUID = uuidv4().replace(/-/g, "");
                    x.CLID = x.CLID || UUID;
                    x.CLWYBS = x.CLWYBS || UUID;
                });
            }
            state.tableData = val;
        },
        {
            immediate: true,
        }
    );

    const editVisible = ref(false);
    const editIndex = ref(0);
    const editData = ref({});
    const editRow = (row, index) => {
        editData.value = row;
        editIndex.value = index;
        editVisible.value = true;
    };

    // 保存
    const updateData = (val) => {
        console.log(val);
        state.tableData.splice(editIndex.value, 1, val);
        editVisible.value = false;
    };

    const copyRow = (row, index) => {
        const UUID = uuidv4().replace(/-/g, "");
        state.tableData.splice(index, 0, {...row, CLID: UUID, CLWYBS: UUID,SHZT:''});
    };
    const insertRow = (row, index) => {
        const UUID = uuidv4().replace(/-/g, "");
        state.tableData.splice(index + 1, 0, {
            CLID: UUID,
            CLWYBS: UUID,
            ZYMC: row.ZYMC,
            ZYBM: row.ZYBM,
            XXXMC: row.XXXMC,
            MBMXID: row.MBMXID,
          YWYQ: row.YWYQ,
          LRZLSM: row.LRZLSM,
            XXX: "",
            XMMC: "",
            JCQK: ""
        });
    };

    const deleteRow = (row, index) => {
        ElMessageBox.confirm("是否删除此条数据？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(() => {
                state.tableData.splice(index, 1);
                ElMessage({
                    message: "删除成功!",
                    type: "success",
                });
            })
            .catch(() => {
            });
    };
    const vForm = ref(null);
    const validateForm = () => {
        return new Promise((resolve, reject) => {
          if(state.tableData.find(item=>(item.SFBT=='1' && !item.CLZSJID))){
            reject({mgs:[{message:'请完善车辆信息！'}]})
          }else {
            resolve(true)
          }
        })
    };
    defineExpose({
        validateForm,
    });
</script>
<style scoped>
    :deep(.el-table-fixed-column--right) {
        background-color: rgba(255, 255, 255, 1) !important;
    }

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-form-item__error) {
        top: 50%;
        transform: translateY(-50%);
        left: 40px;
    }
</style>
