<script setup>
import { onMounted } from 'vue'

function test(num) {
      pohtmldialog.CallParentJSFunc({
        funcName: "updateCount",
        paramJson: num,
        success: function (strRet) {
          document.getElementById("msg").innerText =
            "success: 父窗口updateCount函数返回值 :" + strRet;
        },
      });
    }

    function  test2() {
      pageofficectrl.word.SetTextToSelection("--来自非模态对话框的文本--");
    } 

onMounted(() => {
 //获取父页面pageofficectrl.ShowHtmlModelessDialog()方法第二个参数
 document.getElementById("span1").innerText = window.external.UserParams;

})
</script>

<template>
  <div class="Model" style="background-color:white;">
    <h3>这是一个非模态网页对话框</h3>
    <p>1. 此窗口弹出之后，用户仍然可以操作父窗口和编辑Word文件。</p>
    <p>
      2. 父窗口执行ShowHtmlModelessDialog时传递过来的参数：<span
        id="span1"
        style="color: orange; background-color: white"
      ></span>
    </p>
    <p>3. 点击下面按钮可以与父窗口进行交互操作。</p>

    <ul>
      <li>
        通过CallParentJSFunc调用父窗口js函数updateCount：<br />
        <input type="button" value="Count 加 1" @click="test(1)" />
        <span id="msg" style="color: green; background-color: white"></span>
      </li>
      <li>
        直接调用父窗口pageofficectrl对象：(<span style="color: red"
          >点击后，注意word内容变化</span
        >)<br />
        <input type="button" value="插入文本到Word" @click="test2()" />
      </li>
    </ul>

    <div style="text-align: center">
      <img src="../HtmlDialog/working.gif" style="width: 100px" />
    </div>
  </div>
</template>
