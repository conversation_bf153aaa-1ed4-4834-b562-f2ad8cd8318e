<template>
  <div v-loading="loading">
    <cczxPane ref="cczxPane" :params="params" v-model="formData"/>
    <div class="bottom-button" >
      <el-button size="default" type="warning" @click="dialogVisible=true" v-if="params.type==='JL'">查重文件查看</el-button>
      <el-button size="default" type="success" @click="downloadCCBG" :loading="reporting" v-if="formData.CCSL===formData.CGSL">查重报告下载</el-button>
      <el-button size="default" @click="closeForm">返回</el-button>
    </div>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="查重文件查看"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <bsccwjView v-if="dialogVisible" :params="params" :formData="formData" @close="dialogVisible=false"/>
      </div>
    </el-dialog>


<!--    <bsccbgHtml :key="formData.CCWJID" v-if="!editable && formData.CCSL &&  formData.CGSL &&formData.CCSL===formData.CGSL"-->
<!--                ref="bsccbgHtml" style="visibility: unset;position: absolute" :params="params" @close="closeForm"/>-->

<!--    <div style="position: fixed;left: 0;right: 0;top: 0;bottom: 100px;z-index: 99999;background-color: white;overflow: auto">-->
<!--      <canvas id="myCanvas"/>-->
<!--    </div>-->

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogCCBGVisible"
        v-model="dialogCCBGVisible"
        title="标书查重对比报告预览"
        @closed="closeForm"
        z-index="1000"
        top="1vh"
        width="1000px">
      <div>
        <bsccbgHtml v-if="dialogCCBGVisible" ref="bsccbgHtml" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import cczxPane from "@views/bscc/xmbsccgl/cczxPane";
import axiosUtil from "@lib/axiosUtil";
import bsccwjView from "@views/bscc/ccjggl/bsccwjView";
import bsccbgHtml from "@views/bscc/xmbsccgl/bsccbg/bsccbgHtml";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {cczxPane,bsccwjView,bsccbgHtml},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: false,
      editable: props.params.editable,
      CCWJID: props.params.id,
      formData: {},
      dialogVisible: false,
      dialogCCBGVisible: false,

      reporting: false
    })

    const getFormData = () => {
      let params={
        CCWJID: state.CCWJID
      }
      state.loading=true
      axiosUtil.get('/backend/bscc/bsccgl/selectXmccwjglById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const instance = getCurrentInstance()

    const downloadCCBG = () => {
      // state.reporting=true
      // instance.proxy.$refs['bsccbgHtml'].pdfFunc().then(res=>{
      //   ElMessage.success('已下载报告')
      //   state.reporting=false
      // }).catch(msg=>{
      //   state.reporting=false
      //   ElMessage.warning(msg)
      // })
      state.dialogCCBGVisible=true

    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      downloadCCBG,
      closeForm

    }
  }

})
</script>

<style scoped>
.bottom-button{
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
