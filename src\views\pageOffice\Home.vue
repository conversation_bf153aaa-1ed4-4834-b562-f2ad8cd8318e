<script setup>
// import Index from "../components/Index.vue";
</script>

<template>
  <header class="header">
    <div id="main-nav">
      <nav
        class="navbar navbar-inverse container"
        role="navigation"
        style="margin: 0 auto"
      >
        <div class="navbar-header">
          <button
            type="button"
            id="nav-toggle"
            class="navbar-toggle"
            data-toggle="collapse"
            data-target="#main-nav"
          >
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <!-- <a href="#" class="navbar-brand scroll-top logo"
            ><img src="../assets/images/logo.png" alt="skytouch"
          /></a> -->
        </div>
        <!--/.navbar-header-->
        <div class="collapse navbar-collapse">
          <ul class="nav navbar-nav" id="mainNav" style="height: 78px">
            <li class="active">
              <!-- <a href="#home" class="scroll-link"
                ><img src="../assets/images/home.png"
              /></a> -->
            </li>
            <li style="height: 0; width: 0; visibility: hidden">
              <a href="#describe" class="scroll-link isotopeWrapper"
                >功能描述</a
              >
            </li>
            <li><a href="#jichu" class="scroll-link">基础功能</a></li>
            <li><a href="#gaoji" class="scroll-link">高级功能</a></li>
            <li><a href="#zonghe" class="scroll-link">综合演示</a></li>
            <li><a href="#qita" class="scroll-link">其他技巧</a></li>
            <li>
              <a
                href="https://www.zhuozhengsoft.com"
                target="_blank"
                class="scroll-link"
                >卓正官网</a
              >
            </li>
          </ul>
        </div>

        <!--/.navbar-collapse-->
      </nav>
      <!--/.navbar-->
    </div>
    <!--/.container-->
  </header>

  <!-- <Index></Index> -->

</template>

<style scoped>
</style>
