<template>
  <el-form ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="FBSMC">
          <el-input style="width:100%;" placeholder="请输入分包商名称" v-model="listQuery.FBSMC" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>


      <el-col :span="4" class="grid-cell">
        <div class="static-content-item" v-show="true" style="display: flex;padding-left: 10px;text-align: right;">
          <el-button ref="button91277" @click="getDataList" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
          <el-button ref="button9527" type="primary" class="lui-button-add" @click="openCheckFbs" :loading="CBSLoading">
            <el-icon>
              <Plus/>
            </el-icon>
            新增
          </el-button>
        </div>
      </el-col>
    </el-row>

    <el-row ref="grid71868" :gutter="12" v-show="true">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper" v-show="true">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
            <el-table-column v-if="true" prop="CBSDWQC" label="承包商名称" :fixed="false" align="left"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column v-if="true" prop="CJRXM" label="创建人" :fixed="false" align="center" header-align="center"
                             :show-overflow-tooltip="true" width="150"></el-table-column>
            <el-table-column v-if="true" prop="CJSJ" label="创建时间" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200"></el-table-column>
            <el-table-column v-if="true" prop="CZ" label="操作" align="center" width="150">
              <template #default="scope">
                <el-button size="small" class="lui-table-button" type="primary" @click="delRow(scope.row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>

    <el-dialog
        custom-class="lui-dialog"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="添加分包商"
        @closed="closeForm"
        top="1vh"
        z-index="1000"
        width="1200px">
      <div>
        <cbsChoose :CBSWYBS="CBSWYBS"/>
      </div>
    </el-dialog>

  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "../../../lib/vsAuth";
import {Search, Upload, Plus} from '@element-plus/icons-vue'
import cbsChoose from "./cbsChoose";
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "../../../lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {Search, Upload, Plus, cbsChoose},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        page: 1,
        size: 10,
        FBSMC: '',
      },
      tableData: [],
      total: 0,
      rules: {},
      dialogVisible: false,
      params: {},

      CBSWYBS: null,
      CBSLoading: false
    })

    const getDataList = () => {
      let params = {
        ...state.listQuery,
        CBSWYBS: state.CBSWYBS
      }
      axiosUtil.get('/backend/sccbsgl/fbsgl/selectFbsByCbsbs', params).then(res => {
        state.tableData = res.data.list
        state.total = res.data.total
      })
    }

    const getCBSWYBS = () => {
      let params = {
        orgnaId: state.userInfo.orgnaId
      }
      state.CBSLoading = true
      axiosUtil.get('/backend/sccbsgl/fbsgl/getLoginUserCbsbs', params).then(res => {
        state.CBSWYBS = res.data
        state.CBSLoading = false
        getDataList()
      })
    }

    const delRow = (row) => {
      ElMessageBox.confirm("确定删除此数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        let params={
          CBSWYBS: state.CBSWYBS,
          FBSWYBS: row.CBSWYBS
        }
        axiosUtil.del('/backend/sccbsgl/fbsgl/delCbsFbsGx', {params: params}).then((result) => {
          ElMessage.success("删除成功");
          getDataList();
        }).catch((err) => {
          ElMessage.error("删除失败");
        });
      }).catch(() => {
        // catch error
      });
    }

    const openCheckFbs = () => {
      if (!state.CBSWYBS) {
        ElMessage.error('仅允许承包商操作')
        return
      }

      state.dialogVisible = true
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getCBSWYBS()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      closeForm,
      openCheckFbs,
      delRow
    }
  }

})
</script>

<style scoped>

</style>
