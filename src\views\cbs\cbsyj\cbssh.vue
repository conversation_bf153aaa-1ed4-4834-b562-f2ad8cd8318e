<!-- 待办、已办列表 -->
<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px" size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="formData.processInstanceName" placeholder="请输入任务名称"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-button type="primary" @click="onSearch"><el-icon>
            <Search />
          </el-icon>查询</el-button>
      </el-col>
    </el-row>
    <div>
      <el-table style="height:calc(100vh - 400px);" :data="tableData" class="lui-table">
        <el-table-column type="index" :index="indexMethod" width="60" label="序号" align="center">
        </el-table-column>
        <el-table-column prop="PROCESSINSTANCENAME" label="工作事项" header-align="center" align="left">
          <template #default="scope">
            <el-button type="link" @click="onAudit(scope.row)">
              {{ scope.row.PROCESSINSTANCENAME }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="业务环节" prop="TASKNAME" header-align="center" align="center"
          width="200"></el-table-column>
        <el-table-column label="发送人" prop="SENDUSERNAME" header-align="center" align="center"
          width="200"></el-table-column>
        <el-table-column v-if="status == '1'" label="接收时间" prop="CREATEDTIME" header-align="center" align="center"
          width="200"></el-table-column>
        <el-table-column v-if="status == '3'" label="办理时间" prop="FINISHEDTIME" header-align="center" align="center"
          width="200"></el-table-column>
        <el-table-column label="操作" header-align="center" align="center" width="200">
          <template #default="scope">
            <el-button type="link" @click="onMonitor(scope.row)">[跟踪]</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" v-model:current-page="pageNum"
        v-model:current-size="pageSize" :total="totalPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, prev, pager, next, sizes" background class="lui-pagination" />
    </div>

    <el-dialog z-index="1000" :title="dialogTitle" v-model="showDialog" width="80%" top="35px" @close="onAuditClose"
      :close-on-click-modal="false" custom-class="lui-dialog">
      <!-- 审批 -->
      <audit-frame v-if="showDialog && lx == 'audit'" :row="row" :status="status" @handleClose="handleClose"></audit-frame>
      <!-- 跟踪 -->
      <monitor-form v-if="showDialog && lx == 'monitor'" :model="model" :queryParams="queryParams"></monitor-form>
    </el-dialog>

    <el-dialog custom-class="lui-dialog" :close-on-click-modal="false" v-if="dialogVisible" v-model="dialogVisible"
      title="审批记录" @closed="closeDialog" z-index="1200" top="5vh" width="1200px">
      <Csspjl v-if="dialogVisible" :params="params" @close="dialogVisible = false" />
    </el-dialog>
  </el-form>
</template>
<script>
import { defineComponent, defineAsyncComponent, toRefs, reactive, nextTick, watch, ref, onMounted } from 'vue'
import api from "../../../api/lc/index.js";
import MonitorForm from "@views/workflow/MonitorForm3.vue";
import AuditFrame from "@views/workflow/AuditFrame3.vue";
import Opinion from '@views/workflow/opinion.vue';
import { ElLoading, ElMessageBox, ElMessage } from "element-plus";
import axios from "axios";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "../../../lib/axiosUtil";
import { Search, Upload, Plus, RefreshRight, IceTea } from '@element-plus/icons-vue'
import Csspjl from './csspjl.vue';
import vsflow from "@views/vsflow/index.js";

export default defineComponent({
  name: 'TaskList',
  components: { Opinion, MonitorForm, AuditFrame, Search, Csspjl },
  props: {
    status: String,
    workflowid: String,//流程ID
    dbyb: String,
    activeName: String,
    taskNum: Number,
  },
  setup(props, { emit }) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      formData: {
        loginName: "",
        status: "1",
        processInstanceName: "",
      },
      status: props.status,
      pageSize: 10,
      pageNum: 1,
      totalPage: 0,
      tableHeight: "100%",
      tableData: [],
      showDialog: false,
      dialogTitle: "",
      queryParams: {},
      otherParams: {},
      lx: "",
      dialogVisible: false,
      params: {},
      row: {},
    })
    const busiCom = ref(null);
    const busiUrl = ref(null);

    watch(
      () => props.activeName,
      (val) => {
        if (val) {
          state.formData.status = state.status = props.activeName === '0' ? '1' : '3'
          onSearch();
        }
      }
    );

    // 获取待办/已办
    const onSearch = async () => {
      let resultMap = {};

      // 获取待办
      if (props.activeName == '0') {
        resultMap = await vsflow.getTaskList(
          state.formData.loginName,
          1,
          state.pageNum,
          state.pageSize,
          state.formData.processInstanceName
        );
      }
      // 获取已办
      else {
        resultMap = await vsflow.getTaskList(
          state.formData.loginName,
          9,
          state.pageNum,
          state.pageSize,
          state.formData.processInstanceName
        );
      }
      if (resultMap) {
        state.totalPage = resultMap.total;
        state.tableData = resultMap.rows;
      }
    }

    const onSizeChange = (val) => {
      state.pageSize = val;
      onSearch();
    }

    const onCurrentChange = (val) => {
      state.pageNum = val;
      onSearch();
    }

    // 跟踪
    const onMonitor = (row) => {
      state.showDialog = false;
      nextTick(() => {
        state.lx = "monitor";
        state.dialogTitle = row.PROCESSINSTANCENAME + "-业务跟踪";
        state.queryParams = {
          processInstanceId: row.PROCESSINSTANCEID,
        };
        state.showDialog = true;
      });
    }

    // 调起审核页面
    const onAudit = async (row) => {
      state.dialogTitle = `${row.PROCESSINSTANCENAME}-${state.status === "1" ? "办理" : "查看"}`;
      state.lx = "audit";
      state.row = row;
      state.showDialog = true;
    }

    /**
     * 审核后刷新数据
     */
    const onAuditClose = () => {
      if (state.lx == "audit" && state.status != "3") {
        setTimeout(() => {
          onSearch();
        }, 2000);
        state.lx = ""; // 重置状态
      }
    }

    const handleClose = () => {
      state.dialogVisible = false;
      state.showDialog = false;
    }

    const indexMethod = (index) => {
      return (state.pageNum - 1) * state.pageSize + index + 1
    }

    onMounted(async () => {
      state.formData.loginName = state.userInfo.userLoginName;
      if (props.activeName) {
        state.formData.status = state.status = props.activeName === '0' ? '1' : '3'
      } else {
        state.formData.status = state.status = '1'
      }
      onSearch();
    })

    return {
      ...toRefs(state),
      busiCom,
      busiUrl,
      indexMethod,
      onSearch,
      onAuditClose,
      onAudit,
      onSizeChange,
      onCurrentChange,
      onMonitor,
      handleClose
    }
  }
})
</script>
<style scoped>
.root>>>.el-dialog__body {
  padding: 10px;
}
</style>
