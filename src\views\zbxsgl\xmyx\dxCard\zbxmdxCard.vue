<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="180px"
             size="default" v-loading="loading" @submit.prevent>
      <el-collapse v-model="activeNames">
        <el-collapse-item title="基础信息" name="1">
          <template #title>
            <div style="display: flex;align-items: center;margin-left: 20px">
              <div style="width: 10px;height: 10px;background-color: #2A96F9;border-radius: 10px"></div>
              <div style="color: black;font-weight: bold;margin-left: 10px">基础信息</div>
            </div>
          </template>
          <el-row :gutter="0" class="grid-row">
            <el-col :span="16" class="grid-cell">
              <el-form-item label="所属单位：" prop="SSDWID" >
                <div style="margin-left: 10px">{{formData.SSDWMC}}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label="项目类别：" prop="XMLB" >
                <el-cascader v-model="formData.XMLB" :options="ZRZYTree" filterable :disabled="!editable"
                             :props="{label:'ZYMC',value:'ZYBM',emitPath: false}"
                             clearable />
              </el-form-item>
            </el-col>

            <el-col :span="16" class="grid-cell">
              <el-form-item label="采购项目名称：" prop="XMMC">
                <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="!editable">
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
              <el-form-item label="项目编号：" prop="XMBH" >
                <el-input v-model="formData.XMBH" type="text" placeholder="请输入" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
              <el-form-item label="选商方式：" prop="XSFS" >
                <el-select v-model="formData.XSFS" class="full-width-input" clearable disabled>
                  <el-option v-for="(item, index) in XSFSOptions" :key="index" :label="item.DMMC"
                             :value="item.DMXX"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label="选商申请时间：" prop="XSSJ" >
                <div style="margin-left: 10px">{{ formData.XSSJ }}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item title="开标前准备" name="2">
          <template #title>
            <div style="display: flex;align-items: center;margin-left: 20px">
              <div style="width: 10px;height: 10px;background-color: #2A96F9;border-radius: 10px"></div>
              <div style="color: black;font-weight: bold;margin-left: 10px">开标前准备</div>
            </div>
          </template>
          <el-row :gutter="0" class="grid-row">
            <el-col :span="12" class="grid-cell">
              <el-form-item label="采购申请：" prop="XSSQ" >
                <div style="margin-left: 10px">
                  <el-button size="small" v-if="formData.FASHZT==='2'" link type="primary"
                             @click="workEdit(formData,'XSFA',false,'view')">{{ formData.FACJSJ }}</el-button>
                  <!-- 审批完成有效 -->
                  <el-button size="small" v-if="formData.FASHZT==='1'" link type="primary"
                             @click="workEdit(formData,'XSFA',false,'view')">审批中</el-button><!-- 审批中有效 -->
                  <el-button size="small" v-if="formData.FASHZT==='0' && !onlyView" link type="primary"
                             @click="workEdit(formData,'XSSQ',false,'view')">已保存</el-button><!-- 已保存有效 -->
                </div>
              </el-form-item>
            </el-col>

           <el-col :span="12" class="grid-cell">
             <el-form-item label="招标文件：" prop="XSWJ" >
               <div style="margin-left: 10px">
                 <el-button size="small" v-if="formData.SFDMTZ==='1'" link type="primary">/</el-button>
                 <el-button size="small" v-else-if="(!formData.WJID || formData.FASHZT==='0' || formData.FASHZT==='1') && formData.YWZT==='1'" link type="primary">--</el-button><!-- 未发起 -->
                 <el-button size="small" v-else-if="formData.WJSHZT==='2'" link type="primary"
                            @click="workEdit(formData,'XSWJ',false,'view')">{{ formData.WJCJSJ }}</el-button>
                 <!-- 审批完成有效 -->
                 <el-button size="small" v-else-if="formData.WJSHZT==='1'" link type="primary"
                            @click="workEdit(formData,'XSWJ',false,'view')">审批中</el-button><!-- 审批中有效 -->
                 <el-button size="small" v-else-if="formData.WJSHZT==='0' && formData.FASHZT==='2' && formData.YWZT==='1' && !onlyView" link type="primary"
                            @click="workEdit(formData,'XSWJ',true,'edit')">{{formData.LSWJID ? '变更中' : '待提交'}}</el-button><!-- 已保存有效 -->
               </div>
             </el-form-item>
           </el-col>

           <el-col :span="12" class="grid-cell">
             <el-form-item label="采购公告发布：" prop="XSGG" >
               <div style="margin-left: 10px">
                 <el-button size="small" v-if="formData.SFDMTZ==='1'" link type="primary">/</el-button>
                 <el-button size="small" v-else-if="!formData.GGID && formData.WJSHZT==='2' && formData.YWZT==='1' && !onlyView"
                            link type="primary"
                            @click="workEdit(formData,'XSGG',true,'add')">未发起</el-button><!-- 未发起 -->
                 <el-button size="small" v-else-if="formData.GGSHZT==='2'" link type="primary"
                            @click="workEdit(formData,'XSGG',false,'view')">{{ formData.GGCJSJ }}</el-button>
                 <!-- 审批完成有效 -->
                 <el-button size="small" v-else-if="formData.GGSHZT==='1'" link type="primary"
                            @click="workEdit(formData,'XSGG',false,'view') && formData.YWZT==='1'">审批中</el-button>
                 <!-- 审批中有效 -->
                 <el-button size="small" v-else-if="formData.GGSHZT==='0'" link type="primary"
                            @click="workEdit(formData,'XSGG',true,'edit') && formData.YWZT==='1' && !onlyView">已保存</el-button>
                 <!-- 已保存有效 -->

                 <el-button size="small" v-else-if="!formData.GGID && formData.WJSHZT!=='2' && formData.YWZT==='1'" link
                            type="primary">--</el-button>
               </div>
             </el-form-item>
           </el-col>

           <el-col :span="12" class="grid-cell">
             <el-form-item label="回函：" prop="XSHH" >
               <div style="margin-left: 10px">
                 <el-button size="small" v-if="formData.XSFS==='GKZB' || formData.SFDMTZ==='1'" link type="primary">/</el-button>
                 <el-button size="small" v-else-if="formData.HHSHZT!=='2' && formData.GGSHZT==='2' && formData.YWZT==='1' && !onlyView" link
                            type="primary" @click="workEdit(formData,'XSHH',true,'edit')">确认</el-button><!-- 已发布公告可确认回函 -->
                 <el-button size="small" v-else-if="formData.HHSHZT==='2'" link type="primary"
                            @click="workEdit(formData,'XSHH',false,'view')">查看</el-button><!-- 已确认回函可查看回函 -->
                 <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>
               </div>
             </el-form-item>
           </el-col>

           <el-col :span="12" class="grid-cell">
             <el-form-item label="答疑列表:" prop="DYLB" >
               <div style="margin-left: 10px">
                 <el-button size="small" link type="primary" @click="workEdit(formData,'DYLB',false,'view')">查看</el-button>
               </div>
             </el-form-item>
           </el-col>

           <el-col :span="12" class="grid-cell">
             <el-form-item label="招标文件领取情况:" prop="WJLQ" >
               <div style="margin-left: 10px">
                 <el-button size="small" v-if="formData.XSFS==='GKZB' || formData.SFDMTZ==='1'" link type="primary">/</el-button>
                 <el-button size="small" v-else-if="formData.HHSHZT==='2'" link type="primary" @click="workEdit(formData,'WJLQ',false,'view')">查看</el-button>
                 <!-- 已确认回函可查看招标文件领取情况 -->
                 <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>
               </div>
             </el-form-item>
           </el-col>

           <el-col :span="12" class="grid-cell">
             <el-form-item label="投标文件上传:" prop="TBWJ" >
               <div style="margin-left: 10px">
                 <el-button size="small" v-if="formData.XSFS==='GKZB' || formData.SFDMTZ==='1'" link type="primary">/</el-button>
                 <el-button size="small" v-else-if="!formData.TBSHZT && formData.HHSHZT==='2' && formData.YWZT==='1' && !onlyView" link
                            type="primary" @click="workEdit(formData,'TBWJ',true,'edit')">未完成</el-button><!-- 已发布公告可查看招标文件递交情况 -->
                 <el-button size="small" v-else-if="formData.TBSHZT==='0' && formData.YWZT==='1' && !onlyView"
                            link type="primary" @click="workEdit(formData,'TBWJ',true,'edit')">待提交</el-button>
                 <el-button size="small" v-else-if="formData.TBSHZT==='2'" link type="primary"
                            @click="workEdit(formData,'TBWJ',false,'view')">查看</el-button>
                 <!-- 已发布公告可查看招标文件递交情况 -->
                 <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>
               </div>
             </el-form-item>
           </el-col>

            <el-col :span="12" class="grid-cell">
              <el-form-item label="预定会议:" prop="HY" >
                <div style="margin-left: 10px">
                  <el-button size="small" v-if="formData.SFDMTZ==='1'" link type="primary">/</el-button>
                  <el-button size="small" v-else-if="formData.FASHZT==='2'&&!formData.HYSHZT && formData.YWZT==='1' && !onlyView"
                             link type="primary" @click="workEdit(formData,'YDHY',true,'add')">待处理</el-button>
                  <!-- 未组会 -->
                  <el-button size="small" v-else-if="formData.HYSHZT==='0' && formData.YWZT==='1' && !onlyView"
                             link type="primary" @click="workEdit(formData,'YDHY',true,'edit')">待提交</el-button>
                  <!-- 未组会 -->
                  <el-button size="small" v-else-if="formData.HYSHZT==='2'"
                             link type="primary" @click="workEdit(formData,'YDHY',false,'view')">已完成</el-button>
                  <!-- 已组会 -->
                  <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12" class="grid-cell">
              <el-form-item label="组建评标委员会:" prop="PWCQ" >
                <div style="margin-left: 10px">
                  <el-button size="small" v-if="formData.SFDMTZ==='1'" link type="primary">/</el-button>
                  <el-button size="small"
                             v-else-if="formData.HYSHZT==='2' && !formData.PWCQSHZT && formData.YWZT==='1' && !onlyView" link
                             type="primary" @click="workEdit(formData,'PWCQ',true,'edit')">待处理</el-button><!-- 抽取评委 -->
                  <el-button size="small" v-else-if="formData.PWCQSHZT==='0' && formData.YWZT==='1' && !onlyView"
                             link type="primary" @click="workEdit(formData,'PWCQ',true,'edit')">待提交</el-button>
                  <el-button size="small" v-else-if="formData.PWCQSHZT==='1' && !onlyView" link type="primary"
                             @click="workEdit(formData,'PWCQ',false,'view')">审核中</el-button><!-- 抽取评委 -->
                  <!-- 未组会 -->
                  <el-button size="small" v-else-if="formData.PWCQSHZT==='2'" link type="primary"
                             @click="workEdit(formData,'PWCQ',false,'view')">查看</el-button><!-- 抽取评委 -->
                  <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>

        <el-collapse-item title="开标后工作" name="3">
          <template #title>
            <div style="display: flex;align-items: center;margin-left: 20px">
              <div style="width: 10px;height: 10px;background-color: #2A96F9;border-radius: 10px"></div>
              <div style="color: black;font-weight: bold;margin-left: 10px">开标后工作</div>
            </div>
          </template>
          <el-row :gutter="0" class="grid-row">
            <el-col :span="12" class="grid-cell">
              <el-form-item label="评审资料及评标报告上传:" prop="ZBJG" >
                <div style="margin-left: 10px">
                  <el-button size="small" v-if="formData.SFDMTZ==='1'" link type="primary">/</el-button>
                  <el-button size="small"
                             v-else-if="formData.PWCQSHZT==='2' && !formData.JGSHZT && formData.YWZT==='1' && !onlyView"
                             link type="primary"
                             @click="workEdit(formData,'ZBJG',true,'add')">待处理</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.JGSHZT==='0' && formData.YWZT==='1' && !onlyView" link
                             type="primary"
                             @click="workEdit(formData,'ZBJG',true,'edit')">待提交</el-button><!-- 抽取评委 -->
                  <el-button size="small" v-else-if="formData.JGSHZT==='2'" link type="primary"
                             @click="workEdit(formData,'ZBJG',false,'view')">查看</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12" class="grid-cell">
              <el-form-item label="中标候选人公示:" prop="ZBGS" >
                <div style="margin-left: 10px">
                  <el-button size="small" v-if="formData.SFDMTZ==='1'" link type="primary">/</el-button>
                  <el-button size="small"
                             v-else-if="!formData.JGGSSHZT && formData.JGSHZT==='2' && formData.YWZT==='1' && !onlyView" link
                             type="primary"
                             @click="workEdit(formData,'ZBGS',true,'add')">待处理</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.JGGSSHZT==='0' && formData.YWZT==='1' && !onlyView" link
                             type="primary"
                             @click="workEdit(formData,'ZBGS',true,'edit')">待提交</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.JGGSSHZT==='2'" link type="primary"
                             @click="workEdit(formData,'ZBGS',false,'view')">查看</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>

                  <el-button size="small" v-if="formData.JGGSSHZT==='2'" link type="primary"
                             @click="workEdit(formData,'GSYL',false,'view')">预览</el-button><!--  -->
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12" class="grid-cell">
              <el-form-item label="中标结果:" prop="ZBQR" >
                <div style="margin-left: 10px">
                  <el-button size="small"
                             v-if="((formData.JGGSSHZT==='2' && (formData.FASHZT==='2' || (formData.GGSHZT==='2' && formData.XSFS==='GKZB')))
                                 || (formData.FASHZT==='2' && formData.SFDMTZ==='1')) && !formData.ZBQRSHZT && formData.YWZT==='1' && !onlyView"
                             link type="primary"
                             @click="workEdit(formData,'ZBQR',true,'add')">待处理</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.ZBQRSHZT==='0' && formData.YWZT==='1'" link
                             type="primary"
                             @click="workEdit(formData,'ZBQR',true,'edit')">待提交</el-button><!-- 抽取评委 -->
                  <el-button size="small" v-else-if="formData.ZBQRSHZT==='2'" link type="primary"
                             @click="workEdit(formData,'ZBQR',false,'view')">查看</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12" class="grid-cell">
              <el-form-item label="中标通知书发放:" prop="ZBTZ" >
                <div style="margin-left: 10px">
                  <el-button size="small" v-if="formData.SFDMTZ==='1'" link type="primary">/</el-button>
                  <el-button size="small" v-else-if="!formData.TZSHZT&&formData.ZBQRSHZT==='2' && formData.YWZT==='1' && !onlyView"
                             link type="primary"
                             @click="workEdit(formData,'ZBTZ',true,'add')">待处理</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.TZSHZT==='0'&& formData.YWZT==='1' && !onlyView" link
                             type="primary"
                             @click="workEdit(formData,'ZBTZ',true,'edit')">待提交</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.TZSHZT==='2'" link type="primary"
                             @click="workEdit(formData,'ZBTZ',false,'view')">查看</el-button><!--  -->
                  <el-button size="small" v-else-if="formData.YWZT==='1'" link type="primary">--</el-button>

                  <el-button size="small" v-if="formData.TZSHZT==='2' && formData.ZBJGGSID" link type="primary"
                             @click="workEdit(formData,'TGYL',false,'view')">公告</el-button><!--  -->
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item title="其它" name="4" v-if="!onlyView">
          <template #title>
            <div style="display: flex;align-items: center;margin-left: 20px">
              <div style="width: 10px;height: 10px;background-color: #2A96F9;border-radius: 10px"></div>
              <div style="color: black;font-weight: bold;margin-left: 10px">其它</div>
            </div>
          </template>
          <el-row :gutter="0" class="grid-row">
            <el-col :span="24" class="grid-cell">
              <el-form-item label="运行流程调整:" prop="YXLCTZ" >
                <div style="margin-left: 10px">
                  <el-button size="small" v-if="formData.YWZT==='1'" link type="primary"
                             @click="changeYxlc">办理</el-button>
                  <div v-else>已终止</div>
                </div>

              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>


    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    onlyView: {
      type: Boolean,
      default: false
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      JLID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '0'
      },
      rules: {

      },

      ZRZYTree: [],
      SSDWOptions: [],
      XSFSOptions: [],

      activeNames: ['1','2','3']
    })

    const getFormData = () => {
      let params={
        JLID: state.JLID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/xmyx/selectXmyxById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const workEdit = (row, busType, editable, operation) => {
      emit('workEdit',row, busType, editable, operation)
    }

    const changeYxlc = () => {
      emit('changeYxlc',state.formData)
    }






    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const closeForm = () => {
      emit('close')
    }

    const getZrzyList = () => {
      let params={
      }
      axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
        state.ZRZYTree = comFun.treeData(res.data || [],'ZYBM','FZYBM','children','0')
      });
    }

    onMounted(() => {
      getFormData()
      getZrzyList()
      // getDMBData("SSDW", "SSDWOptions")
      getDMBData("XSFS", "XSFSOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      workEdit,
      changeYxlc,
      getFormData
    }
  }

})
</script>

<style scoped>

</style>
