<template>
   <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-tabs v-model="activeName">
        <el-tab-pane label="问题列表" name="wtlb">
          <el-row :gutter="20" class="lui-search-form">
            <el-col :span="24" :offset="18" class="grid-cell">
              <div class="static-content-item" style="display: flex;">
                <el-button ref="button91277" v-if="pageType === 'JF'" @click="editDyxx" type="primary">问题答复</el-button>
                <el-button ref="button91279" v-if="pageType === 'YF'" @click="editWtxx('edit','')" type="primary">提问</el-button>
                <el-button ref="button91278" @click="closePage" type="primary">关闭</el-button>
              </div>
            </el-col>
          </el-row>
          <el-row ref="grid71868" :gutter="12">
            <el-col :span="24" class="grid-cell">
              <div class="container-wrapper">
                <el-table ref="datatable91634" :data="wtTableData" height="calc(100vh - 430px)" v-loading="loading" @selection-change="handleSelectionChange"
                          :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table" 
                          :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                  <el-table-column type="selection" :selectable="checkSelectable"  width="55"/>
                  <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
                  <el-table-column prop="WT" label="问题" align="center" header-align="center" :show-overflow-tooltip="true" min-width="100"></el-table-column>
                  <el-table-column prop="DFZTMC" label="状态" align="center" header-align="center" :show-overflow-tooltip="true" min-width="40"></el-table-column>
                  <el-table-column prop="CZ" label="操作" align="center" min-width="40" fixed="right">
                    <template #default="scope">
                      <el-button size="small" v-if="scope.row.DFZT=='0' && pageType === 'JF'" class="lui-table-button" type="primary" @click="editDyxxRow('edit',scope.row)">答复</el-button>
                      <el-button size="small" v-else class="lui-table-button" type="primary" @click="editWtxx('view',scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="答疑列表" name="dylb">
          <el-row :gutter="20" class="lui-search-form">
            <el-col :span="24" :offset="18" class="grid-cell">
              <div class="static-content-item" style="display: flex;">
                <el-button ref="button91277" @click="closePage" type="primary">关闭</el-button>
              </div>
            </el-col>
          </el-row>
          <el-row ref="grid71868" :gutter="12">
            <el-col :span="24" class="grid-cell">
              <div class="container-wrapper">
                <el-table ref="datatable91634" :data="dyTableData" height="calc(100vh - 430px)" v-loading="loading"
                          :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table" 
                          :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                  <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
                  <el-table-column prop="HFBT" label="回复标题" align="center" header-align="center" :show-overflow-tooltip="true" min-width="100"></el-table-column>
                  <el-table-column prop="CZ" label="操作" align="center" min-width="40" fixed="right">
                    <template #default="scope">
                      <el-button size="small" class="lui-table-button" type="primary" @click="editDyxxRow('view',scope.row)">查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="wtEditVisible"
        v-model="wtEditVisible"
        :title="wtEditTitle"
        @closed="closeForm"
        z-index="1000"
        width="1100px">
      <div>
        <xbdyglWtEdit v-if="wtEditVisible" :params="params" @closeWtForm="closeWtForm"/>
      </div>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dyEditVisible"
        v-model="dyEditVisible"
        :title="dyEditTitle"
        @closed="closeForm"
        z-index="1000"
        width="1100px">
      <div>
        <xbdyglDyEdit v-if="dyEditVisible" :params="params" @closeDyForm="closeDyForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import xbdyglWtEdit from "./xbdyglWtEdit.vue"
import xbdyglDyEdit from "./xbdyglDyEdit.vue"
import comFun from "@lib/comFun";
export default defineComponent({
  components: {xbdyglWtEdit,xbdyglDyEdit},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
        wtTableData: [],
        dyTableData: [],
        formData: {
            faid: props.params.faid,
        },
        params: {},
        wtEditTitle: '', 
        wtEditVisible: false,  //问题
        dyEditTitle: '',
        dyEditVisible: false, //答疑
        loading: false,
        total: 0,
        activeName: 'wtlb',
        checkList: [],
        wtids: '',
        pageType: props.params.pageType,
    });

    const search = (() =>{
        state.formData.page = 1;
        loadData();
    });

    const loadData = (() =>{
        state.loading = true;
        axiosUtil.get('/backend/xsgl/xbdygl/queryXbWtlbList', state.formData).then((res) => {
            state.wtTableData = res.data.list
            state.total = res.data.total
        });
        state.loading = true;
        axiosUtil.get('/backend/xsgl/xbdygl/queryXbDylbList', state.formData).then((res) => {
            state.loading = false;
            state.dyTableData = res.data.list
            state.total = res.data.total
        });
    })

    //编辑
    const checkSelectable = ((row) =>{
        if(row.DFZT == '1'){
            return false;
        }else{
            return true;
        }
    })

    //批量答复
    const editDyxx = (() =>{
      if(state.checkList.length === 0){
          ElMessage({message: '请选择至少一条问题回复！',type: 'error',})
          return false;
      }
      //将问题ID拼接成一行
      state.wtids='';
      for(let i=0;i<state.checkList.length;i++){
          if(!!!state.wtids){
              state.wtids = state.checkList[i].WTID
          }else{
              state.wtids += ','+state.checkList[i].WTID
          }
      }

      state.params = {
          wtid: state.wtids,
          dyid: comFun.newId(),
          faid: state.formData.faid,
          type: "edit",
          dyEditTitle: '问题答复',
          editable: true,
      };
      state.dyEditVisible = true;
    })

    //编辑
    const editDyxxRow = ((type,row) =>{
      let dyid = row.DYID;
      if(!!!dyid){
          dyid = comFun.newId();
      }
      state.params = {
          wtid: row.WTID,
          dyid: dyid,
          faid: state.formData.faid,
          type: type,
      };
      
      if(type === 'view'){
          state.params.dyEditTitle = '问题答疑查看';
          state.params.editable = false;
      }else{
          state.params.dyEditTitle = '问题答复';
          state.params.editable = true;
      }
      state.dyEditVisible = true;
    })

    //查看
    const editWtxx = ((type, row) =>{
        let wtid = ""
        if(!!row){
            wtid = row.WTID;
        }else{
            wtid = comFun.newId();

        }

      state.params = {
          wtid: wtid,
          faid: state.formData.faid,
          type: type,
      };
      if(type === 'view'){
          state.params.wtEditTitle = '问题查看';
          state.params.editable = false;
      }else{
          state.params.wtEditTitle = '问题编制';
          state.params.editable = true;
      }
      state.wtEditVisible = true;
    })

    const indexMethod = (index) => {
        return index+1;
    }
    
    const handleSelectionChange = (value) => {
        state.checkList=value;
    }

    const closeWtForm = ((isFresh) =>{
        if(isFresh){
            loadData();
        }
        state.wtEditVisible = false;
    })

    const closeDyForm = ((isFresh) =>{
        if(isFresh){
            loadData();
        }
        state.dyEditVisible = false;
    })

    const closePage = (() =>{
        emit('close')
    })
    
    
    onMounted(() => {
        search();
    })

    return {
        ...toRefs(state),
        search,
        indexMethod,
        handleSelectionChange,
        checkSelectable,
        editDyxx,
        editDyxxRow,
        editWtxx,
        closeWtForm,
        closeDyForm,
        closePage,
    }
  }

})
</script>

<style scoped>

</style>
