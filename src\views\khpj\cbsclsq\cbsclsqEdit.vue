<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="200px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="12" class="grid-cell">
          <el-form-item label="处理对象类型：" prop="CLDXLX">
            <el-select v-model="formData.CLDXLX" class="full-width-input"
                       :disabled="!editable" @change="getCldxRes({})"
                       clearable>
              <el-option v-for="(item, index) in CLDXLXOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="企业（队伍/人员）名称：" prop="CLDXMC">
            <el-input v-model="formData.CLDXMC" type="text" placeholder="请选择" clearable :disabled="true">
              <template #append v-if="editable">
                <el-button @click="chooseCldx">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="grid-cell">
          <el-form-item label="专业名称：" prop="CLZYBM">
            <el-select v-model="formData.CLZYBM" class="full-width-input"
                       :disabled="!editable" @change="(value)=>formData.CLZYMC=ZYXXOption.find(item=>item.ZYBM===value)?.ZYMC"
                       clearable>
              <el-option v-for="(item, index) in ZYXXOption" :key="index" :label="item.ZYMC"
                         :value="item.ZYBM" :disabled="item.disabled"></el-option>
            </el-select>

          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="处罚原因类型：" prop="CFYYLX">
            <el-select v-model="formData.CFYYLX" class="full-width-input"
                       :disabled="!editable"
                       clearable>
              <el-option v-for="(item, index) in CFYYLXOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="处罚说明：" prop="CFSM">
            <el-input v-model="formData.CFSM" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="grid-cell">
          <el-form-item label="处理方式：" prop="CLFS">
            <el-select v-model="formData.CLFS" class="full-width-input"
                       :disabled="!editable"
                       clearable>
              <el-option v-for="(item, index) in CLFSOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="处理期限：" prop="CLQX">
            <el-select v-model="formData.CLQX" class="full-width-input"
                       :disabled="!editable"
                       clearable>
              <el-option v-for="(item, index) in CLQXOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="处理期限开始：" prop="CLKSRQ">
            <el-date-picker @change="getCLqxjs"
                v-model="formData.CLKSRQ"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="处理期限结束：" prop="CLJSRQ">
            <el-date-picker
                v-model="formData.CLJSRQ"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="主管单位：" prop="ZGDWID">
            <el-cascader v-model="formData.ZGDWID" :options="orgList" filterable
                         :props="{checkStrictly: true,label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false}"
                         clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="提报单位：" prop="JFBZ">
            <div style="margin-left: 10px">{{formData.CJDWMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="提报人：" prop="JFBZ">
            <div style="margin-left: 10px">{{formData.CJRXM}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="提报时间：" prop="JFBZ">
            <div style="margin-left: 10px">{{formData.CJSJ}}</div>
          </el-form-item>
        </el-col>


        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="处罚相关资料：" prop="CFZL">
            <vsfileupload style="margin-left: 10px" :busId="params.id"
                          :key="params.id"
                          :editable="editable" ywlb="cfxgzl"/>
          </el-form-item>
        </el-col>
      </el-row>



      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogCLDXVisible"
        v-model="dialogCLDXVisible"
        :title="title"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <cldxChoose :CLDXLX="formData.CLDXLX" @close="dialogCLDXVisible=false" @submit="getCldxRes"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import cldxChoose from "@views/khpj/cbsclsq/cldxChoose";

export default defineComponent({
  name: '',
  components: {vsfileupload,cldxChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      CBSCLID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJDWMC: vsAuth.getAuthInfo().permission.orgnaName,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        ZGDWID: null
      },
      rules: {
        CLDXLX: [{
          required: true,
          message: '字段值不可为空',
        }],
        CFYYLX: [{
          required: true,
          message: '字段值不可为空',
        }],
        CLDXMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        CFSM: [{
          required: true,
          message: '字段值不可为空',
        }],
        CLFS: [{
          required: true,
          message: '字段值不可为空',
        }],
        CLQX: [{
          required: true,
          message: '字段值不可为空',
        }],
        CLKSRQ: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      CLDXLXOptions: [],
      CFYYLXOptions: [],
      CLFSOptions: [],
      CLQXOptions: [],
      ZYXXOption: [],

      dialogCLDXVisible: false,
      title: '',

      orgList: []
    })

    watch(()=>state.formData.TYXYDM,(value)=>{
      if(value){
        getZyxxList()
      }
    })

    const getFormData = () => {
      let params={
        CBSCLID: state.CBSCLID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/cbsclsq/selectCbsclsqById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        CBSCLID: state.CBSCLID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),

      }
      if(type==='submit'){
        params.SHZT='1'
      }
      console.log(params)
      state.loading=true
      axiosUtil.post('/backend/sckhpj/cbsclsq/saveCbsclsqForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getCldxRes = (value) => {
      state.formData.CLDXID=value.CLDXID
      state.formData.QYMC=value.QYMC
      state.formData.TYXYDM=value.TYXYDM
      state.formData.DWMC=value.DWMC
      state.formData.DWBM=value.DWBM
      state.formData.RYXM=value.RYXM
      state.formData.SFZH=value.SFZH
      getCLDXMC()
      state.dialogCLDXVisible=false

    }

    const getCLDXMC = () => {
      if(state.formData.CLDXLX==='QY'){
        state.formData.CLDXMC=state.formData.QYMC
      }else if(state.formData.CLDXLX==='DW'){
        state.formData.CLDXMC=state.formData.DWMC
      }else if(state.formData.CLDXLX==='RY'){
        state.formData.CLDXMC=state.formData.RYXM
      }
    }

    const chooseCldx = () => {
      if(!state.formData.CLDXLX){
        ElMessage.warning('请选择处理对象类型')
        return
      }
      state.title=state.CLDXLXOptions.find(item=>item.DMXX===state.formData.CLDXLX)?.DMMC+'选择'
      state.dialogCLDXVisible=true
    }

    const getCLqxjs = (value) => {
      if(state.formData.CLQX){
        let CLQX=state.CLQXOptions.find(item=>item.DMXX===state.formData.CLQX) || {}
        if(CLQX.BYZD1){
          let type=CLQX.BYZD1.substring(0,1)
          let num=Number(CLQX.BYZD1.substring(1))
          let date=new Date(value)
          if(type==='M'){
            date.setMonth(date.getMonth()+num)
          }
          state.formData.CLJSRQ=comFun.getStringDay(date)
        }
      }
    }

    const getOrgList = () => {
      let params={}
      axiosUtil.get('/backend/common/selectpccbsZzjg', params).then((res) => {
        let data=res.data || []
        state.orgList = comFun.treeData(data,'ORGNA_ID','PORGNA_ID','children','0')
      });
    }

    const getZyxxList = () => {
      let params={
        TYXYDM:state.formData.TYXYDM
      }
      axiosUtil.get('/backend/sckhpj/cbsclsq/selectCbszyByTyxydm',params).then(res=>{
        state.ZYXXOption=res.data || []
      })
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      getOrgList()
      getDMBData('CLDXLX', 'CLDXLXOptions')
      getDMBData('CFYYLX', 'CFYYLXOptions')
      getDMBData('CLFS', 'CLFSOptions')
      getDMBData('CLQX', 'CLQXOptions')
    })

    return {
      ...toRefs(state),
      closeForm,
      chooseCldx,
      getCldxRes,
      getCLqxjs,
      saveData,
    }
  }

})
</script>

<style scoped>

</style>
