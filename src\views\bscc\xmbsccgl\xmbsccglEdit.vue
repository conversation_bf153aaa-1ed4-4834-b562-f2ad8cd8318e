<template>
  <div v-loading="loading">
    <ccwjPane ref="ccwjPane" :params="params" v-model="formData" v-if="step==='1'"/>
    <cczxPane ref="cczxPane" :params="params" v-model="formData" v-if="step==='2'"/>


    <div class="bottom-button" v-if="step==='1'">
      <el-button size="default" type="success" @click="saveData('save')" v-if="editable">保存</el-button>
      <el-button size="default" type="primary" @click="saveData('submit')">下一步</el-button>
      <el-button size="default" @click="closeForm">返回</el-button>
    </div>

    <div class="bottom-button" v-if="step==='2'">
      <el-button size="default" type="warning" @click="step='1'">上一步</el-button>
      <el-button size="default" type="primary" @click="checkAll" :loading="checking" v-if="formData.CCSL!==formData.CGSL">全部查重</el-button>
      <el-progress style="width: 300px;margin-left: 10px;margin-right: 10px" color="#409EFF" v-if="checking"
                   :percentage="progress" :stroke-width="15" :text-inside="true"
                   striped striped-flow/>
      <timeStatistic :value="CCKSSJ" v-if="CCKSSJ && checking"/>
      <el-button size="default" type="success" @click="downloadCCBG" :loading="reporting" v-if="formData.CCSL===formData.CGSL">查重报告下载</el-button>
    </div>
  </div>

<!--  <bsccbgHtml :key="formData.CCWJID"  v-if="!editable && formData.CCSL &&  formData.CGSL &&formData.CCSL===formData.CGSL"-->
<!--              ref="bsccbgHtml" style="visibility: unset;position: absolute" :params="params" @close="closeForm"/>-->


  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="true"
      v-if="dialogBGVisible"
      v-model="dialogBGVisible"
      title="标书查重对比报告预览"
      z-index="1000"
      top="1vh"
      width="1000px">
    <div>
      <bsccbgHtml v-if="dialogBGVisible" ref="bsccbgHtml" :params="params" @close="closeForm"/>
    </div>
  </el-dialog>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, onUnmounted} from "vue";
import vsAuth from "@lib/vsAuth";
import ccwjPane from "@views/bscc/xmbsccgl/ccwjPane";
import cczxPane from "@views/bscc/xmbsccgl/cczxPane";
import comFun from "@lib/comFun";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import bsccbgHtml from "@views/bscc/xmbsccgl/bsccbg/bsccbgHtml";
import timeStatistic from "@views/components/timeStatistic";

export default defineComponent({
  name: '',
  components: {ccwjPane,cczxPane,bsccbgHtml,timeStatistic},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      CCWJID: props.params.id,
      step: '1',
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJDWMC: vsAuth.getAuthInfo().permission.orgnaName,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
      },
      reporting: false,

      checking: false,

      progress: 0,


      intervalId: null,
      dialogBGVisible: false,

      CCKSSJ: ''
    })

    const getFormData = () => {
      let params={
        CCWJID: state.CCWJID
      }
      state.loading=true
      axiosUtil.get('/backend/bscc/bsccgl/selectXmccwjglById', params).then((res) => {
        state.formData=res.data
        if(state.formData.SHZT==='1'){
          state.step='2'
        }
        if(state.formData.ZXSL>0){
          initProgress()
        }
        state.CCKSSJ=state.formData.CCKSSJ
        state.loading=false
      })
    }


    const saveData = (type) => {
      if (!props.params.editable){
        state.step='2'
        return
      }
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }
    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        CCWJID: state.CCWJID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        CCQDList: instance.proxy.$refs['ccwjPane'].getCCQDList()
      }
      if(type==='submit'){
        params.SHZT='1'
      }
      state.loading=true
      axiosUtil.post('/backend/bscc/bsccgl/saveXmccwjglForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        if(type==='submit'){
          props.params.editable=false
          props.params.operation='view'
          state.editable=false
          state.step='2'
          getFormData()
        }else {
          closeForm()
        }
        state.loading=false
      })
      console.log(params)
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['ccwjPane'].validateForm().then(res=>{
          resolve(true)
        }).catch(msg=>{
          ElMessage.error(msg)
          resolve(false)
        })
      })
    }

    const downloadCCBG = () => {
      state.dialogBGVisible=true
      // state.reporting=true
      // instance.proxy.$refs['bsccbgHtml'].pdfFunc().then(res=>{
      //   ElMessage.success('已下载报告')
      //   state.reporting=false
      // }).catch(msg=>{
      //   state.reporting=false
      //   ElMessage.warning(msg)
      // })
    }
    
    const checkAll = () => {
      let params={
        CCWJID: state.CCWJID,
        CJRZH: state.userInfo.userLoginName,
        CJRXM: state.userInfo.userName,
        CJDWID: state.userInfo.orgnaId,
        CJDWMC: state.userInfo.orgnaName,
        CJSJ: comFun.getNowTime(),
      }
      state.checking=true
      axiosUtil.get('/backend/bscc/bsccgl/executeQbcc',params).then(res=>{
        ElMessage.success('已发起查重任务')
        state.CCKSSJ=comFun.getNowTime()
        initProgress()
      })
      
    }

    const initProgress = () => {
      state.checking=true
      state.progress=0
      state.intervalId=setInterval(getProgress, 5000);
    }

    const getProgress = () => {
      let params={
        CCWJID: state.CCWJID
      }
      axiosUtil.get("/backend/bscc/bsccgl/getBsccjd",params).then(res=>{
        if(res.data){
          state.progress=res.data
        }
        if(state.progress>=100){
          clearInterval(state.intervalId)
          state.checking=false
          getFormData()
          instance.proxy.$refs['cczxPane'].getDataList()
        }
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
    })

    onUnmounted(()=>{
      if(state.intervalId){
        clearInterval(state.intervalId)
      }
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      downloadCCBG,
      checkAll

    }
  }

})
</script>

<style scoped>
.bottom-button{
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
