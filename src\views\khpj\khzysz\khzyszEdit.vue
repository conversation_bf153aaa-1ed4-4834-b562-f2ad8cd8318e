<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="12" class="grid-cell">
          <el-form-item label="考核专业名称：" prop="ZYMC">
            <el-input v-model="formData.ZYMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="考核专业编码：" prop="ZYBM">
            <el-input v-model="formData.ZYBM" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="备注：" prop="BZ">
            <el-input v-model="formData.BZ"  :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="创建时间：" prop="CJSJ">
            <el-input v-model="formData.CJSJ" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="创建人：" prop="CJRMC">
            <el-input v-model="formData.CJRXM" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="margin-top: 10px;margin-bottom: 10px">
        <el-button ref="button9527" type="primary" @click="">关联准入专业</el-button>
        <el-button ref="button9527" @click="">关联项目专业</el-button>
      </div>

      <el-row :gutter="10">
        <el-col :span="11" style="padding: 10px;border: 1px solid #cbcfd5">
          <div style="margin-bottom: 10px;border-bottom: 1px solid #cbcfd5;padding-bottom: 5px;font-size: 16px;font-weight: bold">
            待选业务
          </div>
          <div style="display: flex;padding-bottom: 10px;text-align: right;gap: 10px">
            <el-input ref="input45296" placeholder="大类业务" v-model="data1.listQuery.DLYW" style="width: 100px;" type="text" clearable>
            </el-input>
            <el-input ref="input45296" placeholder="中类业务" v-model="data1.listQuery.ZLYW" style="width: 100px;" type="text" clearable>
            </el-input>
            <el-input ref="input45296" placeholder="小类业务" v-model="data1.listQuery.XLYW" style="width: 100px;" type="text" clearable>
            </el-input>
            <el-button ref="button91277" @click="getDataList1" type="primary">
              查询
            </el-button>
          </div>
          <el-table ref="table1" :data="data1.tableData" height="calc(300px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false" v-loading="data1.loading"
                    @selection-change="(val)=>handleSelectionChange(val,'data1')"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="selection" width="55" :selectable="selectable"/>
            <el-table-column prop="DLYW" label="大类业务" align="center" min-width="90" :show-overflow-tooltip="true"/>
            <el-table-column prop="ZLYW" label="中类业务" align="center" min-width="100" :show-overflow-tooltip="true"/>
            <el-table-column prop="XLYW" label="小类业务" align="center" min-width="120" :show-overflow-tooltip="true"/>
          </el-table>
        </el-col>
        <el-col :span="2">
          <div style="width: 100px;display: flex;align-items: center;height: 100%" v-if="editable">
            <el-button type="primary" @click="toLeft">&lt;</el-button>
            <el-button type="primary" @click="toRight">></el-button>
          </div>
        </el-col>
        <el-col :span="11" style="padding: 10px;border: 1px solid #cbcfd5">
          <div style="margin-bottom: 10px;border-bottom: 1px solid #cbcfd5;padding-bottom: 5px;font-size: 16px;font-weight: bold">
            已选业务
          </div>
          <div style="display: flex;padding-bottom: 10px;text-align: right;gap: 10px">
            <el-input ref="input45296" placeholder="大类业务" v-model="data2.listQuery.DLYW" style="width: 100px;" type="text" clearable>
            </el-input>
            <el-input ref="input45296" placeholder="中类业务" v-model="data2.listQuery.ZLYW" style="width: 100px;" type="text" clearable>
            </el-input>
            <el-input ref="input45296" placeholder="小类业务" v-model="data2.listQuery.XLYW" style="width: 100px;" type="text" clearable>
            </el-input>
            <el-button ref="button91277" @click="getDataList2" type="primary">
              查询
            </el-button>
          </div>
          <el-table ref="datatable91634" :data="showTable" height="calc(300px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false"
                    @selection-change="(val)=>handleSelectionChange(val,'data2')"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="selection" width="55"/>
            <el-table-column prop="DLYW" label="大类业务" align="center" min-width="90" :show-overflow-tooltip="true"/>
            <el-table-column prop="ZLYW" label="中类业务" align="center" min-width="100" :show-overflow-tooltip="true"/>
            <el-table-column prop="XLYW" label="小类业务" align="center" min-width="120" :show-overflow-tooltip="true"/>
          </el-table>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()" v-if="editable">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

  </div>
</template>

<script>

import {computed, defineComponent, getCurrentInstance, onMounted, reactive, toRefs} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      PJZYID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime()
      },
      data1: {
        tableData: [],
        listQuery: {

        },
        total: 0,
        checkList: [],
        loading: false
      },
      data2: {
        tableData: [],
        listQuery: {
        },
        total: 0,
        checkList: [],
        filterData:{

        }
      },
      rules:{
        ZYMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZYBM: [{
          required: true,
          validator: async (rule, value, callback) => {
            if (!value) {
              callback(new Error('字段值不可为空'))
            } else {
              let isCF = await checkProp(value)
              if(Boolean(isCF.data)){
                callback()
              }else {
                callback(new Error('专业编码不能重复'))
              }
            }
          },
        }],
      }
    })

    const showTable=computed(()=>{
      return state.data2.tableData.filter(item => {
        if (state.data2.filterData.DLYW) {
          if (item.DLYW.indexOf(state.data2.filterData.DLYW) === -1) {
            return false
          }
        }

        if (state.data2.filterData.ZLYW) {
          if (item.ZLYW.indexOf(state.data2.filterData.ZLYW) === -1) {
            return false
          }
        }

        if (state.data2.filterData.XLYW) {
          if (item.XLYW.indexOf(state.data2.filterData.XLYW) === -1) {
            return false
          }
        }
        return true
      })
    })


    const getFormData = () => {
      let params={
        PJZYID: state.PJZYID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/khzysz/seletKhzyById',params).then(res=>{
        state.formData=res.data
        state.data2.tableData=res.data.GLZRZYList || []
        state.loading=false
      })

    }


    const getDataList1 = () => {
      let params = {
        ...state.data1.listQuery,
        ORGNA_ID: state.ORGNA_ID,
        PJZYID: state.PJZYID
      }
      state.data1.loading = true
      axiosUtil.get('/backend/sckhpj/khzysz/selectDxzrzy', params).then((res) => {
        state.data1.tableData = res.data || []
        state.data1.loading = false
      })
    }

    const getDataList2 = () => {
      state.data2.filterData={...state.data2.listQuery}
    }

    const checkProp = (value) => {
      let params={
        PJZYID: state.PJZYID,
        value: value,
      }
      return axiosUtil.get('/backend/sckhpj/khzysz/checkZybm', params)
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const submitForm = () => {
      let params={
        ...state.formData,
        PJZYID: state.PJZYID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        SHZT: '1',
        GLZRZYList: state.data2.tableData
      }
      state.loading=true
      axiosUtil.post('/backend/sckhpj/khzysz/saveKhzyForm',params).then(res=>{
        ElMessage.success('保存成功')
        closeForm()
        state.loading=false
      })
      console.log(params)

    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const toLeft = () => {
      state.data2.tableData = state.data2.tableData.filter(item => {
        return !state.data2.checkList.find(i => item.ZYID === i.ZYID)
      })
      state.data2.total=state.data2.tableData.length
      state.data2.checkList = []
    }

    const toRight = () => {
      state.data1.checkList.forEach(item => {
        if (!state.data2.tableData.find(i => item.ZYID === i.ZYID)) {
          state.data2.tableData.push(item)
        }
      })
      instance.proxy.$refs['table1'].clearSelection()
      state.data2.total=state.data2.tableData.length
    }

    const handleSelectionChange = (val, name) => {
      state[name].checkList = val
    }
    const selectable = (row, index) => {
      return !state.data2.tableData.find(item => item.ZYID === row.ZYID)
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      getDataList1()

    })

    return {
      ...toRefs(state),
      handleSelectionChange,
      selectable,
      toLeft,
      toRight,
      closeForm,
      saveData,
      getDataList1,
      getDataList2,
      showTable

    }
  }

})
</script>

<style scoped>
</style>
