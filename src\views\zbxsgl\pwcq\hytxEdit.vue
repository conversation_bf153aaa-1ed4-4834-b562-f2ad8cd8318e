<template>
    <el-form :model="formData" class="lui-card-form" ref="vForm" :rules="rules" label-position="left"
             label-width="140px" size="default" v-loading="loading">
        <el-row :gutter="0" class="grid-row">
            <div class="zdTitle">会议信息</div>
            <el-col :span="24" class="grid-cell">
                <el-form-item label="会议名称：" prop="HYMC">
                    <div style="margin-left: 10px">{{ formData.HYMC }}</div>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="会议时间：" prop="KBSJ">
                    <div style="margin-left: 10px">{{ formData.KBSJ }}</div>
                </el-form-item>
            </el-col>
            <el-col :span="16" class="grid-cell">
                <el-form-item label="会议地点：" prop="PBDD">
                    <div style="margin-left: 10px">{{ formData.PBDD }}</div>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="grid-cell no-border-bottom">
                <el-form-item label="项目信息：" prop="DPSXMList">
                    <el-table :data="formData.DPSXMList" height="120px" border :show-summary="false" size="default"
                              stripe
                              :highlight-current-row="true" :cell-style="{ padding: '3px 0 ' }" class="lui-table">
                        <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                        <el-table-column prop="XMMC" label="项目名称" align="center" :show-overflow-tooltip="true"
                                         min-width="160"></el-table-column>
                        <el-table-column prop="XMBH" label="项目编号" align="center" :show-overflow-tooltip="true"
                                         width="160"></el-table-column>
                        <el-table-column prop="SSDWMC" label="所属单位" align="center" :show-overflow-tooltip="true"
                                         min-width="160"></el-table-column>
                        <el-table-column prop="BDSL" label="标段数量" align="center" :show-overflow-tooltip="true"
                                         width="100"></el-table-column>
                    </el-table>
                </el-form-item>
            </el-col>

        </el-row>

        <el-row :gutter="0" class="grid-row" style="margin-top: 20px">
            <div class="zdTitle">专家信息</div>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="选商小组组长：" prop="ZBRDB">
                    <el-input v-model="formData.ZBRDB" type="text" placeholder="请选择"
                              clearable readonly>

                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="计划财务部代表：" prop="JDR">
                    <el-input v-model="formData.JDR" type="text" placeholder="请选择"
                              clearable readonly>

                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
                <el-form-item label="企管法律部代表：" prop="ZBGZRY">
                    <el-input v-model="formData.ZBGZRY" type="text" placeholder="请选择"
                              clearable readonly>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="grid-cell">
                <el-table :data="formData.CQZJList" height="260px" :border="true" :show-summary="false"
                          size="default" :stripe="false" :highlight-current-row="true"
                          :cell-style="{ padding: '0px 0 ' }" class="lui-table">
                    <el-table-column type="index" width="60" fixed="left" label="序号"
                                     align="center"></el-table-column>
                    <el-table-column prop="ZJBH" label="专家编号" align="center" width="160"></el-table-column>
                    <el-table-column prop="XM" label="专家名称" align="center" width="100"></el-table-column>
                    <el-table-column prop="SJH" label="手机号" align="center" min-width="100"></el-table-column>
                    <el-table-column prop="CSZYMC" label="专业" align="center" min-width="100"></el-table-column>
                    <el-table-column prop="SZDWMC" label="所属单位" align="center" min-width="100"></el-table-column>
                    <el-table-column prop="ZJLBMC" label="专家类别" align="center" width="90"></el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <div class="footer-button">
            <el-button type="primary" @click="confirmData()">确认</el-button>
            <el-button @click="closeFrom">关闭</el-button>
        </div>
        <el-dialog
                custom-class="lui-dialog"
                v-if="dialogVisible"
                v-model="dialogVisible"
                title="确认出席"
                width="900px">
            <div>
                <el-row :gutter="0" class="grid-row">
                    <el-col :span="12" class="grid-cell">
                        <el-form-item label="是否确认出席：" prop="SFQRCX">
                            <el-select v-model="confirmForm.SFQRCX" placeholder="请选择">
                                <el-option v-for="item in NFCXOptions" :key="item.value" :label="item.label"
                                           :value="item.value"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" class="grid-cell">
                        <el-form-item label="不出席原因：" prop="BCXYY" v-if="confirmForm.SFQRCX==='0'">
                            <el-select v-model="confirmForm.BCXYY" class="full-width-input" clearable>
                                <el-option v-for="(item, index) in BCXYYOptions" :key="index" :label="item.DMMC"
                                           :value="item.DMXX"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
            <template #footer>
                <div class="footer-button">
                    <el-button type="primary" @click="saveConfirmData">提交</el-button>
                    <el-button @click="dialogVisible=false">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </el-form>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
    import axiosUtil from "../../../lib/axiosUtil";
    import {ElMessage} from "element-plus";
    import vsAuth from "@lib/vsAuth";
    import comFun from "../../../lib/comFun";

    export default defineComponent({
        name: '',
        components: {},
        props: {
            params: {
                type: Object,
                required: true
            }
        },
        setup(props, {emit}) {
            const state = reactive({
                userInfo: vsAuth.getAuthInfo().permission,
                editable: props.params.editable,
                loading: false,
                PBPWBS: props.params.id,
                formData: {
                    KBSJ: null,
                    PBDD: null,
                    CQZJList: []
                },
                rules: {
                    KBSJ: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    PBDD: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                },
                zbrdbList: [],
                jdrList: [],
                zbgzryList: [],

                confirmForm: {
                    PBRY: [],
                    SFQRCX: '1',
                    BCXYY: ''
                },
                BCXYYOptions: [],
                dialogVisible: false,
                NFCXOptions: [{value: '1', label: '是'}, {value: '0', label: '否'}],//能否出席下拉框
            })
            watch(() => state.zbrdbList, (newVal) => {
                let res = []
                if (newVal) {
                    newVal.forEach(item => {
                        res.push(item.XM)
                    })
                }
                state.formData.ZBRDB = res.join(',')

            })
            watch(() => state.jdrList, (newVal) => {
                let res = []
                if (newVal) {
                    newVal.forEach(item => {
                        res.push(item.XM)
                    })
                }
                state.formData.JDR = res.join(',')
            })
            watch(() => state.zbgzryList, (newVal) => {
                let res = []
                if (newVal) {
                    newVal.forEach(item => {
                        res.push(item.XM)
                    })
                }
                state.formData.ZBGZRY = res.join(',')
            })

            const getFormData = () => {
                let params = {
                  PBPWBS: state.PBPWBS
                }
                state.loading = true
                axiosUtil.get('/backend/xsgl/zjpbpw/selectPbhyxxBypbpw', params).then((res) => {
                    state.formData = res.data || {};
                    let zbrdbList = [];
                    let jdrList = [];
                    let zbgzryList = [];
                    state.formData.QTRYList.forEach(item => {
                        if (item.SFZBRDB === '1') {
                            zbrdbList.push(item);
                        } else if (item.SFJDRY === '1') {
                            jdrList.push(item);
                        } else if (item.SFZBGZRY === '1') {
                            zbgzryList.push(item);
                        }
                    });
                    state.zbrdbList = zbrdbList;
                    state.jdrList = jdrList;
                    state.zbgzryList = zbgzryList;
                    state.loading = false;
                })
            };
            const instance = getCurrentInstance();

            const confirmData = () => {
                state.dialogVisible = true;
            };

            const saveConfirmData = () => {
                if (state.confirmForm.SFQRCX === '0' && !state.confirmForm.BCXYY) {
                    ElMessage.error('不出席会议需要填写原因');
                    return
                }
                let params = {
                    ...state.confirmForm,
                    PBPWBS: state.PBPWBS,
                    XMMC: state.formData.XMMC,
                    SHR: state.userInfo.userLoginName,
                    RYBS: state.userInfo.userId,
                    SHRQ: comFun.getNowTime()
                }
                console.error(params)
                axiosUtil.post('/backend/xsgl/zjpbpw/saveHycxQrxx', params).then(res => {
                    if (res.message === 'success') {
                        emit('closeMsg');
                    }
                })
            };

            const getDMBData = async (DMLBID, resList) => {
                let params = {
                    DMLBID
                };
                const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
                state[resList] = res.data;
            };

            const closeFrom = () => {
                emit('close');
            };

            onMounted(() => {
                getDMBData("BCXYY", "BCXYYOptions");
                getFormData();
            });

            return {
                ...toRefs(state),
                saveConfirmData,
                closeFrom,
                getDMBData,
              confirmData

            }
        }

    })
</script>

<style scoped>
    .zdTitle {
        background-color: #E4E6F6;
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding-left: 10px;
        font-weight: 600;
    }

    .footer-button {
        width: 100%;
        text-align: center;
    }
</style>
