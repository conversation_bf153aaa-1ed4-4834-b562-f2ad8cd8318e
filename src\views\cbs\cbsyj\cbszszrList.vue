<template>
  <div
    class="zhyy-list-container"
    style="width: 100%; margin-top: 5px"
    v-loading="loading"
  >
    <div class="zhyy-list-main">
      <el-row class="zhyy-list-searchArea">
        <el-col :span="16" style="display: inline-block; text-align: left">
          <el-input
            v-model="state.searchForm.cbsdwqc"
            placeholder="搜索工作名称"
            style="width: 300px"
            clearable
          ></el-input>
          <el-button
            size="mini"
            type="primary"
            style="margin-left: 10px"
            @click="searchList()"
            >查询</el-button
          >
          <el-button type="primary" size="mini" @click="handleAdd">发起</el-button>
        </el-col>
      </el-row>
      <!--数据表格-->
      <el-row>
        <el-table
          class="customer-no-border-table"
          :data="tableData"
          width="100%"
          :height="pageHeight"
        >
          <el-table-column
            width="40"
            prop="order"
            header-align="center"
            align="center"
            label=""
          >
            <template #default="scope">
              <div class="gzxqsj">
                <span>{{ scope.row.PX }}</span>
              </div>
              <div>
                <span>&#12288;</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column header-align="left" align="left" show-overflow-tooltip>
            <template #header>
              <div>
                <span style="border-left: 3px solid #409eff; padding-left: 8px"
                  >工作详情</span
                >
              </div>
            </template>
            <template #default="scope">
              <div class="gzxqsj">
                <span v-if="scope.row.SHZT == '0' || scope.row.SHZT == '9'"
                  >{{ scope.row.CJSJ }}创建了【承包商正式引进申请】<label
                    >待提交</label
                  ></span
                >
                <span v-if="scope.row.SHZT == '1'"
                  >{{ scope.row.CJSJ }}创建了【承包商商正式引进申请】<label
                    >已提交</label
                  ></span
                >
                <span v-if="scope.row.SHZT == '2'"
                  >{{ scope.row.CJSJ }}创建了【承包商正式引进申请】<label
                    >已退回</label
                  ></span
                >
                <span v-if="scope.row.SHZT == '3'"
                  >{{ scope.row.CJSJ }}创建了【承包商正式引进申请】<label
                    >审核通过</label
                  ></span
                >
              </div>
              <div class="nav-zrzs">
                <span>【{{ scope.row.YJYWMC }}】{{ scope.row.CBSDWQC }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="" width="250px" header-align="center" align="center">
            <template #default="scope">
              <div class="kbzwf">
                <span> &#12288;</span>
              </div>
              <div>
                <el-button
                  v-if="scope.row.SHZT == '0'"
                  @click="lcView(scope.row, scope.$index)"
                  type="text"
                  size="small"
                  >[编辑]</el-button
                >
                <el-button
                  v-if="scope.row.SHZT == '0'"
                  @click="deleteRow(scope.row, scope.$index)"
                  type="text"
                  size="small"
                  >[删除]</el-button
                >
                <el-button
                  v-if="
                    scope.row.SHZT != '0' &&
                    scope.row.SHZT != '2' &&
                    scope.row.SHZT != '9'
                  "
                  @click="viewRow(scope.row, scope.$index)"
                  type="text"
                  size="small"
                  >[请查看]</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <!--分页-->
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="state.searchForm.pageNum"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="state.searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper "
          :total="total"
        ></el-pagination>
      </div>
      <el-dialog
        width="80%"
        title="承包商正式引进"
        v-model="state.lcdialogVisible"
        :close-on-click-modal="false"
        :fullscreen="true"
      >
        <audit-frame
          v-if="state.lcdialogVisible"
          :model="state.model"
          :processParams="state.processParams"
          :businessParams="otherParams"
          @close="onClose"
        ></audit-frame>
      </el-dialog>

      <!-- 弹出框区域 -->
      <el-dialog
        title="承包商正式引进"
        v-if="state.dialogViewVisible"
        v-model="state.dialogViewVisible"
        width="80%"
        top="3vh"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <div>
          <cbszrAdd
            @onClose="onClose"
            :model="state.model"
            :key="id"
            :cbsbs="id"
            :LX="dwlx"
            :params="otherParams"
            :mblx="mblx"
          ></cbszrAdd>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script setup>
import {
  vue,
  vsuiapi,
  mixin,
  eventDefine,
  eventBus,
  runtimeCfg,
  axios,
} from "@src/assets/core";
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import axiosUtil from '@src/lib/axiosUtil.js';
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import util from "@lib/comFun.js";
import cbszrAdd from "./index.vue";
import { useRouter } from "vue-router";
const router = useRouter();

const state = reactive({
  loading: true,
  queryDataOnLoad: true,
  ywlxdm: router?.query?.ywlxdm,
  zrlxdm: router?.query?.zrlxdm,
  model: "cbszr",
  appUser: {},
  id: "",
  searchForm: {
    ywlxdm: router?.query?.ywlxdm,
    cjr: "",
    cbsdwqc: "",
    pageNum: 1,
    pageSize: 10,
  },
  dialogVisible: false,
  dialogViewVisible: false,
  lcdialogVisible: false,
  tableData: [],
  total: 0,
  //流程发起参数--
  //流程参数
  processParams: {
    activityId: "new",
    processId: "DWZHGL_ABLNBDWZRLC",
    businessId: "",
    processInstanceName: "ABC类油田部门推荐单位引进流程",
  },
  //其他参数
  otherParams: {},
  //流程发起参数

  showDialog: false,
  queryParams: {},
  dwlx: "",
  fqButton: true,
  mblx: null,
  LX: "",
});
//变量
const pageHeight = computed(() => "calc(100vh - 300px)");

const lcView = async (row, index) => {
  console.log(555555, row.CBSDWQC);
  if (row.CBSDWQC.includes("(") || row.CBSDWQC.includes(")")) {
    ElMessage({
      type: "warning",
      message:
        "您的单位全称中包含（、）等特殊字符串，可能会造成流程发起失败，请联系运维人员修改企业全程",
    });
  }

  let allParam = {};
  state.otherParams = {
    id: row.CBSBS,
    cbsbs: row.CBSBS,
    type: state.ywlxdm, //承包商正式引进
    zsType: "ZS",
    editable: true,
    pageFlag: "view",
    appUser: state.appUser,
    zrlxdm: state.zrlxdm,
    mblx: row.mblx,
    LX: state.LX,
  };
  state.processParams.businessId = row.CBSBS;
  state.processParams.processInstanceName = row.CBSDWQC + "ABC类油田部门推荐单位引进流程";
  allParam.otherParams = state.otherParams;
  allParam.processParams = state.processParams;
  let params = {
    routePath: "/lcgz" + row.CBSBS,
    name: row.CBSDWQC,
    param: allParam,
  };

  let route = [
    {
      path: "/lcgz" + row.CBSBS,
      name: "lcgz",
      component: () => import("@views/workflow/AuditFrame.vue"),
      meta: { title: "流程页面", permission: false },
    },
  ];
  router.addRoute(route);
  eventBus.$emit("moduleclickedforLc", params);
};
// 返回跳转 有问题
const goBackToZc = () => {
  state.dialogVisible = false;
  router.push({
    name: "cbszcsq",
  });
};
const onAuditClose = () => {
  if (state.LX == "audit" && state.status != "3") {
    setTimeout(() => {
      onSearch();
    }, 2000);
  }
};
const onMonitor = (id) => {
  state.showDialog = false;
  nextTick(() => {
    state.LX = "monitor";
    state.queryParams = {
      id: id,
    };
    state.showDialog = true;
  });
};
const goBack = () => {
  state.state.lcdialogVisible = false;
};
const onClose = () => {
  state.dialogVisible = false;
  state.dialogViewVisible = false;
  state.state.lcdialogVisible = false;
  state.getDataList();
};
const handleAdd = () => {
  state.dialogVisible = false;
  state.otherParams = {
    id: util.newId(),
    type: state.ywlxdm, //承包商正式引进
    zsType: "ZS",
    editable: true,
    pageFlag: "add",
    appUser: state.appUser,
    zrlxdm: state.zrlxdm,
  };
  state.dialogVisible = true;
};
// 编辑方法
const editRow = (row, index) => {
  state.mblx = null;
  state.id = row.CBSBS;
  state.otherParams = {
    id: row.CBSBS,
    type: state.ywlxdm, //承包商正式引进
    editable: true,
    dwEditable: true,
    pageFlag: "edit",
    appUser: state.appUser,
    zrlxdm: state.zrlxdm,
  };
  state.dialogViewVisible = true;
};
// 创建方法
const addRow = (row) => {
  state.dialogVisible = false;
  let allParam = [];
  state.id = row.CBSBS;
  state.mblx = row.mblx;
  state.otherParams = {
    id: row.CBSBS,
    cbsbs: row.CBSBS,
    type: state.ywlxdm, //承包商正式引进
    zsType: "ZS",
    editable: true,
    pageFlag: "gl",
    appUser: state.appUser,
    zrlxdm: state.zrlxdm,
    mblx: row.mblx,
    LX: state.LX,
  };

  row.processId = state.processParams.processId;
  state.processParams.businessId = row.CBSBS;
  state.processParams.processInstanceName = row.CBSDWQC + "ABC类油田部门推荐单位引进流程";
  allParam.otherParams = state.otherParams;
  allParam.processParams = state.processParams;
  let params = {
    routePath: "/lcgz" + row.CBSBS,
    name: row.CBSDWQC,
    param: allParam,
  };

  let route = [
    {
      path: "/lcgz" + row.CBSBS,
      name: "lcgz",
      component: () => import("@views/workflow/AuditFrame"),
      meta: { title: "流程页面", permission: false },
    },
  ];
  router.addRoute(route);
  eventBus.$emit("moduleclickedforLc", params);
};
// 查看方法
const viewRow = (row, index) => {
  state.mblx = null;
  state.id = row.CBSBS;
  state.otherParams = {
    id: row.CBSBS,
    type: state.ywlxdm, //承包商正式引进
    editable: false,
    dwEditable: false,
    pageFlag: "view",
    appUser: state.appUser,
    activityId: "new",
  };
  state.dialogViewVisible = true;
};
const deleteRow = (row, index) => {
  ElMessageBox("您确实要删除信息?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      state.loading = true;
      try {
        let deleteResult = await util.delete(
          "/sldwgl/cbsJbxx/deleteCbszr",
          { cbsbs: row.CBSBS, cbsbm: row.CBSBM },
          state.model
        );
        getDataList();
      } catch (err) {
        console.log(err);
        state.loading = false;
        ElMessage.error("删除失败！");
      }
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消!",
      });
    });
};
const handleClose = () => {
  state.dialogVisible = false;
  state.dialogViewVisible = false;
  getDataList();
};
const handleSizeChange = (val) => {
  state.searchForm.pageNum = 1;
  state.searchForm.pageSize = val;
  getDataList();
};
const handleCurrentChange = (val) => {
  state.searchForm.pageNum = val;
  getDataList();
};
const indexMethod = (index) => {
  return index + state.pageSize * (state.currentPage - 1) + 1;
};
// 条件查询， 先把页数改为1
const searchList = () => {
  state.searchForm.pageNum = 1;
  getDataList();
};
const getDataList = async () => {
  state.loading = true;
  try {
    const result = await axiosUtil.get(
      "/sldwgl/cbsJbxx/queryDataPage",
      state.searchForm,
      state.model
    );
    const { data: pageData } = result;

    state.tableData = pageData.rows;
    state.total = pageData.total;
    state.loading = false;
  } catch (err) {
    state.loading = false;
    ElMessage.error("查询失败");
  }
};
const getAppUser = async () => {
  // state.appUser = await util.getAppUser();
  // state.searchForm.cjr = state.appUser.userLoginName;
};
const queryDwlx = async () => {
  let param = {
    user: state.appUser.userLoginName,
  };
  const result = await axiosUtil.get("/sldwgl/cbsJbxx/queryDwlx", param);
  const { data: pageData } = result;
  if (pageData.length > 0) {
    state.LX = pageData[0].LX;
    state.dwlx = pageData[0].LX;

    if (state.dwlx == "WB") {
      /* let dwList = util.getObjectResult(await util.postString('/sldwgl/dwdj/queryDwgl', {cbsbs: state.appUser.orgnaId}))
          if (dwList.length == 0) {
            state.fqButton = false;
            ElMessage.error("目前没有队伍信息，请先去创建队伍信息");
          }*/
    }
  } else {
    ElMessage.error("单位类型查询失败");
  }
};
// 退回
const returnRow = (row) => {
  ElMessageBox("是否确认撤回此条数据", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    let params = {
      old_cbsbs: row.CBSBS,
      cbsbm: row.CBSBM,
      // new_type: 'ZS',
      shzt: "0",
    };
    let result = await util.get("/sldwgl/cbsJbxx/saveToSH", params);
    getDataList();
  });
};

onMounted(() => {
  getAppUser().then((val) => {
    getDataList();
    queryDwlx();
  });
});
</script>
<style>
.pagination {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 45px;
}
.el-table .cell,
.el-table--border td:first-child .cell,
.el-table--border th:first-child .cell {
  padding-left: 0px;
}
</style>
<style scoped>
.el-breadcrumb {
  font-size: 22px;
  color: #2080da;
}
.gzxqsj {
  font-size: 14px;
  /* background: #cfeefc; */
  font-weight: 600;
  padding-right: 0;
  padding-left: 0;
  padding-top: 0px;
}
.gzxqsj label {
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
  padding-left: 12px;
}
.nav-zrzs {
  font-size: 14px;
  color: #909399;
}
*/deep/.el-table .cell {
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  line-height: 31px;
  padding: 0px;
  padding-left: 0;
}
.kbzwf {
  /* background: #cfeefc; */
  /* font-weight: 600; */
  padding-right: 0;
  padding-left: 0;
  padding-top: 0px;
}
*/deep/.el-table--small td,
.el-table--small th {
  padding: 0px 0;
}
/*/deep/.el-table td, .el-table th.is-leaf,.el-table--border, .el-table--group {
  border-color: #cfeefc;
}*/
/*/deep/.customer-no-border-table thead tr th.is-leaf{
    border: 0px solid #EBEEF5;
    border-right: none;
  }*/
/*/deep/ .customer-no-border-table thead tr th:nth-last-of-type(2){
    border-right: 0px solid #EBEEF5;
  }*/
*/deep/.el-dialog.is-fullscreen {
  overflow-x: hidden;
}
</style>
