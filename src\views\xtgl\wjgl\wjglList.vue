<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0" size="default">
      <el-row>
        <el-col :span="24">
          <div class="card-header">
               <span v-for="(item,index) in getResNameList(checkNode)">
                  <span class="resName" @click="resNameClick(item.DMXX)">{{ item.DMMC }}</span>
                  <span style="margin-right: 3px;margin-left: 3px"
                        v-if="getResNameList(checkNode).length-1!==index">></span>
                </span>
          </div>
        </el-col>
      </el-row>
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="8" class="grid-cell">
          <div class="card-body">
            <div style="height: 100%">
              <el-tree
                  ref="resTree"
                  :expand-on-click-node="false"
                  :data="resTreeList"
                  :default-expand-all="true"
                  node-key="DMXX"
                  :props="{label:'DMMC',children: 'children'}"
                  @node-click="checkRes"/>

            </div>
          </div>
        </el-col>
        <el-col :span="16" class="grid-cell">
          <div class="static-content-item" style="display: flex;height: 40px">
            <el-upload
                v-if="editable!=='N'"
                name="files"
                :action="uploadUrl"
                :with-credentials="true"
                :multiple="true"
                :data="fileUploadData"
                :on-success="onSuccess"
                :show-file-list="false">
              <el-button ref="button9527" type="primary" class="lui-button-add"
                         :disabled="checkNode.DMXX==='0'">
                <el-icon>
                  <Plus/>
                </el-icon>
                上传
              </el-button>
            </el-upload>

          </div>
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="childResList" height="calc(100vh - 290px)" v-loading="loading"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column v-if="true" prop="FILENAME" label="文件名称" align="left"
                               :show-overflow-tooltip="true" min-width="250">
                <template #default="{row}">
                  <el-button ref="button9527" type="primary" size="small" link @click="download(row)">{{ row.FILENAME }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column v-if="true" prop="FILE_TYPE" label="文件类型" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column v-if="true" prop="FILE_SIZE" label="文件大小" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column v-if="editable!=='N'" prop="CZ" label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="delRow(scope.row)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getFileList" @current-change="getFileList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import {Plus} from '@element-plus/icons-vue'
import comFun from "../../../lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";

export default defineComponent({
  name: '',
  components: {Plus},
  props: {
    editable:{
      type: String,
      default: 'Y'
    },
    WJLX:{
      type: String,
      default: null
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      childResList: [],
      resList: [],
      resTreeList: [],
      listQuery: {
        page: 1,
        size: 10
      },
      total: 0,
      loading: false,
      params: {},
      rootNode: {
        DMMC: '根目录',
        DMXX: '0',
      },
      checkNode: {},
      dialogVisible: false,
      fileUploadData: {
        busType: 'dwxx',
        busId: comFun.newId(),
        standbyField0: ''
      },
      uploadUrl: '/backend/minio/upload',
    })
    const instance = getCurrentInstance()
    const getResList = (pid) => {
      let params = {
        DMLBID: "WJMB"
      }
      if(props.WJLX){
        params.DMXX=props.WJLX
      }
      axiosUtil.get('/backend/common/wjgl/selectWjflList', params).then((res) => {
        let data = res.data || []
        state.resList = JSON.parse(JSON.stringify(data))
        state.resTreeList = treeData(data, 'DMXX', 'FDMXX', 'children', '0')
      });
    }

    const getFileList = () => {
      if (state.checkNode.DMXX === '0') {
        state.childResList = []
        state.total = 0
        return
      }
      let params = {
        ...state.listQuery,
        YWLB: state.checkNode.DMXX
      }
      state.loading = true
      axiosUtil.get('/backend/common/wjgl/selectWjPage', params).then(res => {
        state.loading = false
        state.childResList = res.data.list
        state.total = res.data.total
      })


    }

    const checkRes = (node) => {
      state.checkNode = node
      state.listQuery.page = 1
      state.listQuery.size = 10
      state.fileUploadData.standbyField0 = node.DMXX
      getFileList()
    }

    const delRow = (row) => {
      ElMessageBox.confirm('确定删除该文件?', '警告', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        axiosUtil.post('/backend/minio/del', {id: row.ID, delFlag: "1"}).then(res => {
          ElMessage.success('删除成功')
          getFileList()
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }

    const download = (row) => {
      const url = '/backend/common/fileupload/download?id=' + row.DOWNLOADID
      window.open(url)
    }

    const getResNameList = (node) => {
      let res = []
      const findPNode = (cNode) => {
        res.unshift({
          DMXX: cNode.DMXX,
          DMMC: cNode.DMMC
        })
        if (cNode.FDMXX !== state.rootNode.DMXX) {
          let pNode = state.resList.find(item => item.DMXX === cNode.FDMXX)
          if (pNode) {
            findPNode(pNode)
          }
        } else {
          res.unshift({
            DMXX: state.rootNode.DMXX,
            DMMC: state.rootNode.DMMC
          })
        }
      }
      findPNode(node)
      return res
    }

    const resNameClick = (DMXX) => {
      if(DMXX===state.rootNode.DMXX){
        checkRes(state.rootNode)
      }else {
        checkRes(state.resList.find(item=>item.DMXX===DMXX))
      }
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      state.dialogVisible = false
      getResList()
      getResList(state.checkNode.RES_ID)
    }

    const onSuccess = (res) => {
      ElMessage.success('上传成功')
      getFileList()
    }

    /**
     * 数据转树型结构
     * @param data
     * @param id id名称
     * @param parentId 父id名称
     * @param childName 生成的子节点名称
     * @param root 根节点的值
     * @returns {*}
     */
    const treeData = (data, id, parentId, childName, root) => {
      let cloneData = data
      return cloneData.filter((father) => {
        father[childName] = cloneData.filter((child) => {
          return father[id] === child[parentId]
        })
        return father[parentId] === root
      })
    }

    onMounted(() => {
      state.checkNode = state.rootNode
      getResList()
    })

    return {
      ...toRefs(state),
      indexMethod,
      checkRes,
      getResNameList,
      closeForm,
      delRow,
      download,
      resNameClick,
      onSuccess,
      getFileList

    }
  }

})
</script>

<style scoped>
.card-header {
  padding: 20px;
  box-shadow: rgba(0, 0, 0, 0.12) 0 0 12px;
  margin-bottom: 10px;
}

.card-body {
  box-shadow: rgba(0, 0, 0, 0.12) 0 0 12px;
  height: calc(100vh - 220px);
  padding: 10px;
  overflow: auto;
}

.resName {
  cursor: pointer;
}

.resName:hover {
  color: #409EFF;
}
</style>
