<template>
  <div style="height: 100%;">
    <el-form v-model="formData" inline>
      <el-form-item label="被授权委托人姓名">
        <el-input v-model="formData.wtr" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="身份证号">
        <el-input v-model="formData.sfzh" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="手机">
        <el-input v-model="formData.sj" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input v-model="formData.yx" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="固定电话">
        <el-input v-model="formData.gddh" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <el-collapse v-model="activeNames">
      <el-collapse-item title="相关附件" name="1">
        <el-table
          highlight-current-row
          size="small"
          ref="table"
          fit
          height="300px"
          :border="false"
          :data="tableData"
        >
          <EleProTableColumn
            v-for="prop in tableColumn"
            :col="prop"
            :key="prop.columnKey"
          >
            <template #WJMC="{ row }">
              <el-button type="text" @click="viewZS(row)"></el-button>
            </template>
            <template #opration="{ row, $index }">
              <div>
                <el-button type="text" @click="insertRow(row, $index)">上传</el-button>
              </div>
            </template>
          </EleProTableColumn>
        </el-table>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script setup>
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import { axios } from "@src/assets/core/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
const formData = ref({
  wtr: "",
  sfzh: "",
  sj: "",
  yx: "",
  gddh: "",
});
const activeNames = ref('1')
const tableColumn = ref([
  {
      label: "类型",
      prop: "LX",
      align: "center",
      showOverflowTooltip: true,
      width: 200,
    },
    {
      label: "文件名称",
      prop: "WJMC",
      align: "center",
      showOverflowTooltip: true,
      slot: "WJMC"
      
    },
    {
      label: "操作",
      align: "center",
      width: 150,
      fixed: "right",
      slot: "opration",
    },
])
const tableData = ref([]);
const insertRow = (row,index) => {

}
</script>
