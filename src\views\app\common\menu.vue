<template>
  <div class="container">
    <router-view/>
<!--    <div class="menu_bar">-->
<!--      <div :class="{menu_item: true,menu_item_click:checkBar===item.path}"-->
<!--           @click=""-->
<!--           v-for="(item,index) in menuList" :key="index">-->
<!--        <component :is="item.icon" :style="`height: ${item.size || 25}px;width: ${item.size || 25}px`"/>-->
<!--        <div>{{item.name}}</div>-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted, ref,
}
  from 'vue'
import {HomeFilled,View,Message,MessageBox,Camera} from '@element-plus/icons-vue'
import {useRoute} from "vue-router";
import {ElMessage} from "element-plus";
import vsAuth from "@lib/vsAuth";


export default defineComponent({
  name: '',
  components: {HomeFilled,View,Message,MessageBox,Camera},
  props: {},
  setup() {
    const route = useRoute()
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      checkBar:'/app/index',
      menuList:[
        {name:'首页',path:'/app/index',icon:'HomeFilled'},
        {name:'流程办理',path:'/app/task',icon:'View'},
        {name:'',path:'/app/xcjc',icon:'Camera',size: 40},
        {name:'消息提醒',path:'/app/msg',icon:'Message'},
        {name:'资源库',path:'/app/zyk',icon:'MessageBox'},
      ]
    })




    onMounted(() => {
      state.checkBar=route.path
    })

    return {
      ...toRefs(state),
    }
  }
})

</script>

<style scoped>
.container {

}
.menu_bar{
  background-color: white;
  box-shadow: 1px 1px 4px 1px rgba(140, 147, 157, 0.58);
  height: 60px;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
}
.menu_item{
  flex: 1;
  text-align: center;
  color: #8c939d;
  font-size: 14px;
}
.menu_item_click{
  color: #F56C6C;
}

</style>
