<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
          <el-input v-model="listQuery.qymc" placeholder="请输入企业名称"></el-input>
      </el-col>
      <el-col :span="5" class="grid-cell">
          <el-input v-model="listQuery.dwmc" placeholder="请输入队伍名称"></el-input>
      </el-col>
      <el-col :span="10" class="grid-cell">
          <el-button type="primary" @click="processSearch">查询</el-button>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          highlight-current-row
          ref="table"
          size="default"
          height="calc(100vh - 250px)"
          border
          :data="tableData"
          v-loading="listLoading"
      >
        <el-table-column type="index" width="60" :index="indexMethod" label="序号" align="center"/>
        <el-table-column label="单位全称" min-width="150" header-align="center" align="left">
          <template #default="{ row }">
              <el-button type="text" @click="viewCbsxx(row)">
                {{ row.CBSDWQC }}
              </el-button>
          </template>
        </el-table-column>
        <el-table-column label="队伍全称" min-width="150" prop="DWMC" header-align="center" align="left">
          <template #default="{ row }">
            <el-button type="text" @click="viewDwxx(row)">
              {{ row.DWMC }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="资质等级" min-width="100" header-align="center" align="left">
        </el-table-column>
        <el-table-column label="投标范围" min-width="150" prop="FWFW" header-align="center" align="left">
        </el-table-column>
        <el-table-column label="投标资格证号" min-width="100" header-align="center" align="left">
        </el-table-column>
        <el-table-column label="队伍状态" min-width="80" prop="DWZTMC" header-align="center" align="center">
        </el-table-column>
        <el-table-column label="征信" min-width="60" header-align="center" align="center">
          <template #default="{ row }">
              <el-button class="lui-table-button" @click="view(row,'ZX')">
                查看
              </el-button>
          </template>
        </el-table-column>
        <el-table-column label="风险" min-width="60" header-align="center" align="center">
          <template #default="{ row }">
              <el-button class="lui-table-button" @click="view(row,'FX')">
                查看
              </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
  </el-form>
  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="false"
      v-if="showDialog"
      v-model="showDialog"
      :title="title"
      top="5vh"
      width="80%">
    <cbszxView v-if="dialogType === 'ZX'" :params="dialogParam"></cbszxView>
    <cbsfxList v-if="dialogType === 'FX'" :params="dialogParam"></cbsfxList>
  </el-dialog>
</template>
<script>
import {defineComponent, onMounted, reactive, toRefs} from "vue";
import {getTeamreslutGetProDetails} from '@src/api/sccbsgl.js'
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import TabFun from "@src/lib/tabFun"
import cbsjbxxIndex from "@views/cbs/cbsyj/index.vue"
import dwjbxx from "@views/cbs/cbsyj/yrsqxxIndex.vue"
import cbszxView from "./cbszxView.vue";
import cbsfxList from "./cbsfxList.vue";
export default defineComponent({
  name: 'cbsdacxList',
  components: {cbszxView,cbsfxList},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery:{
        page: 1,
        size: 10,
        qymc: '',
        dwmc: '',
      },
      listLoading: false,
      tableData: [],
      total: 0,
      showDialog: false,
      dialogType: '',
      dialogParam: {},
      title: '',
    })
    onMounted(() => {
      initListData();
    })

    // 征信和风险
    const view = (row,type) => {
      if(type === 'ZX'){
        state.title = '征信';
      }else if(type === 'FX'){
        state.title = '风险';
      }
      state.dialogType = type;
      state.dialogParam = {
        CBSDWQC: row.CBSDWQC
      };
      state.showDialog = true;
    }

    // 初始化数据
    const initListData = () => {
      const params = {
        ...state.listQuery,
      }
      state.listLoading=true
      axiosUtil.get('/backend/sccbsgl/report/queryCbsdaList', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
        state.listLoading=false
      });
    }
    // 查看承包商信息
    const viewCbsxx = ({XNYWID, XNDWLX, EXTENSION,CBSDWQC}) => {
      let ex = JSON.parse(EXTENSION)
      getTeamreslutGetProDetails({dwid: XNYWID, dwlx: XNDWLX}).then(({data}) => {
        TabFun.addTabByCustomName(
            CBSDWQC,
            'cbsjbxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
            cbsjbxxIndex,
            {
              uuId: XNYWID, //队伍业务ID
              MBID: ex?ex.MBID:'', //模板ID
              MBLX: "QY", //模板类型、
              ZYFLDM: data.ZYFLDM, //专业分类代码
              YWLXDM: "BG", //业务类型代码
              editable: false,//是否查看,
              isVIewJgxx: true,
              ifShowLscz: true
            },
            {}
        );
      })
    }
    // 查看队伍信息
    const viewDwxx = ({XNYWID, XNDWLX, EXTENSION,CBSDWQC, DWLX,DWYWID,DWMC}) => {
      if(DWLX == 'CBS' || DWLX == 'XN'){
        let ex = JSON.parse(EXTENSION)
        getTeamreslutGetProDetails({dwid:XNYWID, dwlx: XNDWLX}).then(res=>{
          TabFun.addTabByCustomName(
                CBSDWQC,
                'cbsjbxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
                cbsjbxxIndex,
                {
                    uuId : XNYWID, //队伍业务ID
                    MBID: ex.MBID, //模板ID
                    MBLX: "QY", //模板类型、
                    ZYFLDM: res.data.ZYFLDM, //专业分类代码
                    YWLXDM: "BG", //业务类型代码
                    editable: false,//是否查看
                    isVIewJgxx: true,
                    ifShowLscz: true
                },
                {}
            );
        })
      }else if(DWLX == 'DW'){
        TabFun.addTabByCustomName(
              DWMC,
              'dwxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
              dwjbxx, {
              DWYWID: DWYWID,
              YWLXDM: 'BG',
              JGDWYWID: DWYWID,//结果表队伍业务ID
              editable: false,//是否查看
              isVIewJgxx: true,
              ifShowLscz: true
          }, {});

      }
    }
    const handleSizeChange = (val) => {
      state.listQuery.page = 1;
      state.listQuery.size = val;
      initListData();
    };
    const handleCurrentChange = (val) => {
      state.listQuery.page = val;
      initListData();
    };
    const indexMethod = (index) => {
      return index + state.listQuery.size * (state.listQuery.page - 1) + 1;
    };
    // 条件查询， 先把页数改为1
    const processSearch = () => {
      state.listQuery.page = 1;
      initListData();
    };

    return {
      ...toRefs(state),
      viewCbsxx,
      viewDwxx,
      handleSizeChange,
      handleCurrentChange,
      indexMethod,
      processSearch,
      view
    }
  }

})
</script>

<style scoped>

</style>
