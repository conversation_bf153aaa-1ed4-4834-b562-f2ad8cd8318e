<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue';
import { POBrowser } from 'js-pageoffice';

const searchKey = ref(''); // 定义搜索框的响应式引用
const tableData = ref([]); // 定义表格数据的响应式引用

const fetchData = async () => {
  try {
    const response = await request({
			url: '/SaveAndSearch/index',
			method: 'get',
		});
		tableData.value = response;
  } catch (error) {
    console.error('There has been a problem with your fetch operation:', error);
  }
};

function handleChange(){
  if (searchKey.value != "" && searchKey.value != null) {
        let searchDataJson = [];//定义一个搜索到的数据的数组
        for (let k = 0; k < tableData.value.length; k++) {
            if (tableData.value[k].content.toLocaleLowerCase().indexOf(searchKey.value.toLocaleLowerCase()) > -1) {// content:当前文件的纯文本内容
                searchDataJson.push(tableData.value[k]);
            }
        }
        tableData.value = searchDataJson;
    } 
}

const handleEdit = (row) => {
  let myParams={};
  myParams.id=row.id;
  myParams.fileName=row.fileName;
  myParams.key=searchKey.value;

	POBrowser.openWindow("Word", 'width=1150px;height=900px;',JSON.stringify(myParams));
};


onMounted(() => {
  fetchData();

});
</script>

<style>
/* 添加必要的样式来美化表格和按钮 */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh; /* 确保容器可以填充整个视口的高度 */
}

.hot-search-keywords{
  font-size:12px;
  margin: 0 0 15px 0;

}
.el-table {
  border-collapse: collapse;
}

.el-table th,
.el-table td {
  border: 1px solid #ebeef5;
}

.el-button {
  margin-top: 10px; /* 给按钮留出一些垂直空间 */
}
.el-input {
  margin-top: 30px; 
}
.title{
  font-size: x-large;
  margin-top: 10px; 
}

</style>

<template>
  <div class="container" style="width: 881px">
    <h3 style="margin-top: 25px">演示：根据关键字搜索文件</h3>
			<div style="width:600px;margin-top: 15px; font-size:14px;">			
			</div>
    <el-input v-model="searchKey" placeholder="请输入关键字进行搜索" clearable suffix-icon="el-icon-search" @input="handleChange()">
  </el-input>
  <div class="hot-search-keywords">热门搜索关键字：软件、网站、字体
          <!-- <a
            v-for="keyword in hotKeys"
            :key="keyword"
            :href="'#'"
            @click.prevent="selectKeyword(keyword)">
            {{ keyword }}
          </a> -->
    </div> 
    <el-table :data="tableData"  border>
      <el-table-column prop="fileName" label="文档名称" width="700" />
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

