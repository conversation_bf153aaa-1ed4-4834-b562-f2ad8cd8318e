/* 左侧树样式 */
.vsui-content .vsui-siderbar >>>.vsui-siderbar-main-nav{
    background-color: #2A96F9;
}
 
/*  重写框架颜色 */
.vsui-multiplex .vsui-theme .vsui-content .vsui-siderbar, .vsui-multiplex .vsui-theme .vsui-header .vsui-header-logo{
    --sidebar-main-font-color: #fff !important;
    --sidebar-main-font-color-active: #fff !important;
    --sidebar-main-font-color-hover: #fff !important;
    --sidebar-main-font-color-active-hover: #fff !important;
    --sidebar-main-background-color: #2A96F9 !important;
    --sidebar-main-background-color-hover: #176dbb !important;
    --sidebar-main-background-color-active: #176dbb !important;
    --sidebar-main-background-color-active-hover: #176dbb !important;
    --sidebar-main-item-border-active: #4079E0 !important;
    --sidebar-box-shadow: 1px 0px 2px #978b8b;
    --sidebar-border-color1: var(--sidebar-main-background-color);
    font-weight: bold;
    font-family: 微软雅黑;
    /*font-size: 16px !important;*/
}


.vsui-multiplex .vsui-theme .vsui-header .vsui-header-other{

    --header-background-color: #2A96F9 !important;

}


.vsui-multiplex .vsui-layout .vsui-header .vsui-header-other .vsui-header-nav{
    width: calc(100vw - 850px) !important;
}

.vsui-multiplex .vsui-theme .vsui-header .vsui-header-other .vsui-header-nav .vsui-header-el-menu-scoped>.vsui-el-menu-item-scoped{
    font-weight: bold !important;
    font-family: 微软雅黑;
    border-width: 4px !important;
    /*font-size: 16px !important;*/
}