<template>
  <div v-if="col.hide"></div>
  <el-table-column
    v-else-if="col.children && col.children.length"
    v-show="!col.hide"
    :type="col.type"
    :index="col.index || index"
    :column-key="col.columnKey"
    :label="col.label"
    :prop="col.prop"
    :width="col.width"
    :min-width="col.minWidth"
    :fixed="col.fixed"
    :render-header="col.renderHeader"
    :sortable="col.sortable"
    :sort-method="col.sortMethod"
    :sort-by="col.sortBy"
    :sort-orders="col.sortOrders"
    :resizable="col.resizable"
    :formatter="col.formatter"
    :show-overflow-tooltip="col.showOverflowTooltip"
    :align="col.align"
    :header-align="col.headerAlign"
    :class-name="col.className"
    :label-class-name="col.labelClassName"
    :selectable="col.selectable"
    :reserve-selection="col.reserveSelection"
    :filters="col.filters"
    :filter-placement="col.filterPlacement"
    :filter-multiple="col.filterMultiple"
    :filter-method="col.filterMethod"
    :filtered-value="col.filteredValue"
  >
    <ele-pro-table-column
      v-for="child in col.children"
      :key="child.key"
      :col="child"
      :index="index"
    >
      <template
        v-for="name in Object.keys($slots)"
        #[name]="{ row, column, $index }"
      >
        <slot
          :name="name"
          v-bind:row="row"
          v-bind:column="column"
          v-bind:$index="$index"
        />
      </template>
    </ele-pro-table-column>
  </el-table-column>
  <el-table-column
    v-else-if="col.slot && col.headerSlot"
    v-show="!col.hide"
    :type="col.type"
    :index="col.index || index"
    :column-key="col.columnKey"
    :label="col.label"
    :prop="col.prop"
    :width="col.width"
    :min-width="col.minWidth"
    :fixed="col.fixed"
    :render-header="col.renderHeader"
    :sortable="col.sortable"
    :sort-method="col.sortMethod"
    :sort-by="col.sortBy"
    :sort-orders="col.sortOrders"
    :resizable="col.resizable"
    :formatter="col.formatter"
    :show-overflow-tooltip="col.showOverflowTooltip"
    :align="col.align"
    :header-align="col.headerAlign"
    :class-name="col.className"
    :label-class-name="col.labelClassName"
    :selectable="col.selectable"
    :reserve-selection="col.reserveSelection"
    :filters="col.filters"
    :filter-placement="col.filterPlacement"
    :filter-multiple="col.filterMultiple"
    :filter-method="col.filterMethod"
    :filtered-value="col.filteredValue"
  >
    <template #default="{ row, column, $index }">
      <slot
        :name="col.slot"
        v-bind:row="row"
        v-bind:column="column"
        v-bind:$index="$index"
      />
    </template>
    <template #header="{ column, $index }">
      <slot
        :name="col.headerSlot"
        v-bind:column="column"
        v-bind:$index="$index"
      />
    </template>
  </el-table-column>
  <el-table-column
    v-else-if="col.slot"
    v-show="!col.hide"
    :type="col.type"
    :index="col.index || index"
    :column-key="col.columnKey"
    :label="col.label"
    :prop="col.prop"
    :width="col.width"
    :min-width="col.minWidth"
    :fixed="col.fixed"
    :render-header="col.renderHeader"
    :sortable="col.sortable"
    :sort-method="col.sortMethod"
    :sort-by="col.sortBy"
    :sort-orders="col.sortOrders"
    :resizable="col.resizable"
    :formatter="col.formatter"
    :show-overflow-tooltip="col.showOverflowTooltip"
    :align="col.align"
    :header-align="col.headerAlign"
    :class-name="col.className"
    :label-class-name="col.labelClassName"
    :selectable="col.selectable"
    :reserve-selection="col.reserveSelection"
    :filters="col.filters"
    :filter-placement="col.filterPlacement"
    :filter-multiple="col.filterMultiple"
    :filter-method="col.filterMethod"
    :filtered-value="col.filteredValue"
  >
    <template #default="{ row, column, $index }">
      <slot
        :name="col.slot"
        v-bind:row="row"
        v-bind:column="column"
        v-bind:$index="$index"
      />
    </template>
  </el-table-column>
  <el-table-column
    v-else-if="col.headerSlot"
    v-show="!col.hide"
    :type="col.type"
    :index="col.index || index"
    :column-key="col.columnKey"
    :label="col.label"
    :prop="col.prop"
    :width="col.width"
    :min-width="col.minWidth"
    :fixed="col.fixed"
    :render-header="col.renderHeader"
    :sortable="col.sortable"
    :sort-method="col.sortMethod"
    :sort-by="col.sortBy"
    :sort-orders="col.sortOrders"
    :resizable="col.resizable"
    :formatter="col.formatter"
    :show-overflow-tooltip="col.showOverflowTooltip"
    :align="col.align"
    :header-align="col.headerAlign"
    :class-name="col.className"
    :label-class-name="col.labelClassName"
    :selectable="col.selectable"
    :reserve-selection="col.reserveSelection"
    :filters="col.filters"
    :filter-placement="col.filterPlacement"
    :filter-multiple="col.filterMultiple"
    :filter-method="col.filterMethod"
    :filtered-value="col.filteredValue"
  >
    <template #header="{ column, $index }">
      <slot
        :name="col.headerSlot"
        v-bind:column="column"
        v-bind:$index="$index"
      />
    </template>
  </el-table-column>
  <el-table-column
    v-else
    v-show="!col.hide"
    :type="col.type"
    :index="col.index || index"
    :column-key="col.columnKey"
    :label="col.label"
    :prop="col.prop"
    :width="col.width"
    :min-width="col.minWidth"
    :fixed="col.fixed"
    :render-header="col.renderHeader"
    :sortable="col.sortable"
    :sort-method="col.sortMethod"
    :sort-by="col.sortBy"
    :sort-orders="col.sortOrders"
    :resizable="col.resizable"
    :formatter="col.formatter"
    :show-overflow-tooltip="col.showOverflowTooltip"
    :align="col.align"
    :header-align="col.headerAlign"
    :class-name="col.className"
    :label-class-name="col.labelClassName"
    :selectable="col.selectable"
    :reserve-selection="col.reserveSelection"
    :filters="col.filters"
    :filter-placement="col.filterPlacement"
    :filter-multiple="col.filterMultiple"
    :filter-method="col.filterMethod"
    :filtered-value="col.filteredValue"
  >
  </el-table-column>
</template>

<script>
export default {
  name: 'EleProTableColumn',
  props: {
    // 列配置参数
    col: Object,
    // 设置index列的起始索引
    index: Number,
  },
}
</script>
