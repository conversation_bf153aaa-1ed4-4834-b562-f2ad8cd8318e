<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function SaveAs() {
  pageofficectrl.ShowDialog(3);
}

function PrintSet() {
  pageofficectrl.ShowDialog(5);
}

function PrintFile() {
  pageofficectrl.ShowDialog(4);
}

function IsFullScreen() {
  pageofficectrl.FullScreen = !pageofficectrl.FullScreen;
}

function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
  pageofficectrl.AddCustomToolButton("另存到本地", "SaveAs()", 5);
  pageofficectrl.AddCustomToolButton("页面设置", "PrintSet()", 0);
  pageofficectrl.AddCustomToolButton("打印", "PrintFile", 6);
  pageofficectrl.AddCustomToolButton("全屏/还原", "IsFullScreen()", 4);
  pageofficectrl.OfficeToolbars = false;
}

function openFile() {
  return request({
    url: '/TaoHong/readOnly',
    method: 'get',
  })
}
onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit, PrintFile, PrintSet, IsFullScreen, SaveAs };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <div class="Word">
    <div class="flow4">
      <span style="width: 100px"> </span><strong>文档主题：</strong>
      <span style="color: Red">测试文件</span>
    </div>
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:870px;" v-html="poHtmlCode"></div>
  </div>
</template>
