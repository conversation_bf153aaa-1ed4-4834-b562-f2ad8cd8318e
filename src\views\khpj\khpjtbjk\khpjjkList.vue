<template>
    <div class="container">
        <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
                 size="default">
            <el-row :gutter="20" class="lui-search-form">
                <el-col :span="6" class="grid-cell">
                    <el-form-item label="" prop="KHZQ">
                        <el-select v-model="listQuery.KHZQ" class="full-width-input"
                                   placeholder="请选择考核周期"
                                   clearable>
                            <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC"
                                       :value="item.PJZQID" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="4" class="grid-cell">
                    <div class="static-content-item" style="display: flex;">
                        <el-button ref="button91277" @click="getDataList" type="primary">
                            <el-icon>
                                <Search/>
                            </el-icon>
                            查询
                        </el-button>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <div class="container-wrapper">
                        <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 280px)"
                                  :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"
                                             :index="indexMethod"/>
                            <el-table-column prop="ZQMC" label="考核周期" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="ORGNA_TWO_NAME" label="填报单位" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="ZYMC" label="考核专业" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="SHZTMC" label="当前状态" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                        </el-table>
                        <el-pagination background v-model:current-page="listQuery.page"
                                       v-model:page-size="listQuery.size"
                                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                                       class="lui-pagination"
                                       @size-change="getDataList" @current-change="getDataList" :total="total">
                        </el-pagination>
                    </div>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import {Plus, Search, Upload} from "@element-plus/icons-vue";
    import axiosUtil from "@lib/axiosUtil";
    import comFun from "@lib/comFun";
    import {ElMessage, ElMessageBox} from "element-plus";

    export default defineComponent({
        name: '',
        components: {Search, Upload, Plus},
        props: {},
        setup(props, {emit}) {
            const state = reactive({
                listQuery: {
                    page: 1,
                    size: 10,
                    KHZQ: '',
                },
                tableData: [],
                total: 0,
                params: {},
                dialogVisible: false,
                KHSJOptions: [],
            })

            const getDataList = () => {
                const params = {
                    ...state.listQuery,
                }
                axiosUtil.get('/backend/sckhpj/khpjjk/queryKhpjjkList', params).then((res) => {
                    state.tableData = res.data.list
                    state.total = res.data.total
                });
            }

            const indexMethod = (index) => {
                return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
            }
            const closeForm = () => {
                state.dialogVisible = false
                getDataList()
            }

            const getKhsjList = () => {
                axiosUtil.get('/backend/sckhpj/khmxtb/selectKhqjList', null).then(res => {
                    state.KHSJOptions = res.data || []
                })
            }

            onMounted(() => {
                getKhsjList();
                getDataList()
            })

            return {
                ...toRefs(state),
                getDataList,
                indexMethod,
                closeForm,
                getKhsjList,
            }
        }

    })
</script>

<style scoped>

</style>
