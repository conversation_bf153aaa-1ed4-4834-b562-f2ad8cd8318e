<!--
 * @Description: 江汉市场管理门户-考核评价弹窗
 * @Author: WLF (<EMAIL>)
 * @Date: 2024-11-14 08:49:04
 * @LastEditTime: 2025-01-13 17:56:20
 * @FilePath: \jhscgl-xieyun-front\src\views\WebPortal\khtj_list.vue
 * @copyright : Copyright (c) 2024 wlf
-->
<template>
    <el-form ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0" size="default"
        @submit.prevent>
        <el-row :gutter="20" class="lui-search-form">
            <el-col :span="6" class="grid-cell">
                <el-form-item label="" v-show="true" prop="ZYMC">
                    <el-input style="width:100%;" placeholder="请输入问题描述" v-model="listQuery.WTMS" type="text" clearable>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="4" class="grid-cell">
                <div class="static-content-item" v-show="true"
                    style="display: flex;padding-left: 10px;text-align: right;">
                    <el-button ref="button91277" @click="getDataList" type="primary">
                        <el-icon>
                            <Search />
                        </el-icon>
                        查询
                    </el-button>
                </div>
            </el-col>

        </el-row>
        <el-table :data="tableData" height="calc(100vh - 300px)" class="lui-table" :border="true" :show-summary="false"
            size="default" :stripe="false" :highlight-current-row="true" :cell-style="{ padding: '10px 0 ' }">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod" />
            <el-table-column prop="XMMC" label="项目名称" align="left" header-align="center" :show-overflow-tooltip="true" min-width="300">
            </el-table-column>
            <el-table-column prop="CBSDWQC" label="单位名称" align="left" header-align="center" :show-overflow-tooltip="true" min-width="300"></el-table-column>

            <el-table-column prop="DWMC" label="队伍名称" align="left" header-align="center" :show-overflow-tooltip="true" min-width="300"></el-table-column>
            
            <el-table-column prop="WTFXSJ" label="问题发现时间" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="WTMS" label="问题描述" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <!-- <el-table-column prop="PFBZ" label="评分标准" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="JFBZ" label="记分标准" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column> -->
              <el-table-column prop="JFFZ" label="记分值" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
        </el-table>
    </el-form>
</template>

<script>
import {
    defineComponent,
    toRefs,
    reactive,
    onMounted,
    onUnmounted
}
    from 'vue'
import TabFun from "@src/lib/tabFun";
// 请求方法 get,post,put,del,downloadFile
import vsAuth from "@src/lib/vsAuth";
import { ElMessage, ElMessageBox } from "element-plus";
import comFun from "@src/lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import { Search, Upload, Plus } from '@element-plus/icons-vue'
import { mixin } from "@src/assets/core/index";
const { vsuiRoute, vsuiEventbus } = mixin();
export default defineComponent({
    name: '',
    components: { Search, Plus, Upload },
    props: {
        DXMPJID: String
    },
    setup(props, { emit }) {
        const state = reactive({
            showKhtjDialog:false,
            listQuery: {
                page: 1,
                size: 10,
                DXMPJID: props.DXMPJID,
                WTMS: '',
            },
            tableData: [],
            total: 0,
        })

        const jumpKhtj = (row) => {
            state.DXMPJID = row.DXMPJID
            state.showKhtjDialog = true
        }


        const getDataList = () => {
            let params = {
                ...state.listQuery
            }
            axiosUtil.get('/backend/webProtal/queryKhpjXmWt', params).then((res) => {
                state.tableData = res.data
            });
        }

        const indexMethod = (index) => {
            return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
        }

        const closeDialog = () => {
            
        }



        onMounted(() => {
            getDataList();
        })

        onUnmounted(() => {
            
        });

        return {
            ...toRefs(state),
            indexMethod,
            getDataList,
            closeDialog
        }
    }
})

</script>

<style scoped>
.container {
    padding: 8px 5px;
}
</style>