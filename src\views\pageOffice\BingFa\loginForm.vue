<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

// 定义响应式数据
const loginForm = ref({
  username: '',
  password: '123456'
});

// 获取路由对象
const router = useRouter();

// 定义处理登录的方法
const handleLogin = () => {
  // 处理登录逻辑
  if (loginForm.value.username !== null && loginForm.value.password !== null) {
   router.push({ name: 'list', params: { username: loginForm.value.username } });
  } else {
    alert('用户名或密码错误');
  }
};
</script>
<template>
  <div>
    <el-form :model="loginForm" label-width="100px" class="login-form">
      <div style="font-weight: bold; margin-bottom: 10px; text-align: center;">登录</div>
      <el-form-item label="用户名" label-width="100px">
        <el-select v-model="loginForm.username" placeholder="请选择用户名" style="width: 100%;">
          <el-option label="张三" value="张三"></el-option>
          <el-option label="李四" value="李四"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="密码" label-width="100px">
        <el-input type="password" v-model="loginForm.password" placeholder="请输入密码" style="width: 100%;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleLogin" style="width: 100%;">登录</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.login-form {
  width: 400px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
</style>