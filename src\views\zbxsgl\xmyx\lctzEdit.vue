<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="目标流程：" prop="ZZHJ">
            <el-radio-group v-model="formData.ZZHJ" :disabled="!editable"
                            style="display: flex;flex-direction: column;gap: 20px">
              <el-radio :label="item.DMXX" style="width: 150px;margin-right: 0"
                        v-for="(item,index) in ZZHJOptions" :key="index">
                <div style="display: flex">
                  <div style="width: 150px">{{item.DMMC}}</div>
                  <div >{{item.BYZD1}}</div>
                </div>
              </el-radio>

            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="调整原因：" prop="ZZYY">
            <el-input v-model="formData.ZZYY" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="附件资料：" prop="FJZL">
            <vsfileupload
                style="margin-left: 10px"
                :editable="editable"
                :busId="params.id"
                :key="params.id"
                ywlb="FJZL"
                busType="FJZL"
                :limit="10"
            ></vsfileupload>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData('submit')" v-if="editable">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";


export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      JLID: props.params.id,
      formData: {
        ZZHJ: null,
        ZZYY: null
      },
      rules: {
        ZZHJ: [{
          required: true,
          message: '请选择',
        }],
        ZZYY: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      ZZHJOptions: []
    })

    const getFormData = () => {
      let params={
        JLID: state.JLID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/xmyx/selectXmyxById', params).then((res) => {
        state.formData=res.data
        getDMBData("ZZHJ", "ZZHJOptions")
        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        JLID: state.JLID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      if(type==='submit'){
        params.YWZT='9'
      }
      state.loading=true
      axiosUtil.post('/backend/xsgl/xmyx/saveXmyxlctzForm',params).then(res=>{
        if (res.data.ZT==='1'){
          ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
          closeForm()
        }else {
          ElMessage.error(res.data.msg)
        }
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
      if(DMLBID==='ZZHJ'){
        if(state.formData.XSFS==='DJTP'){
          state[resList]=state[resList].filter(item=>item.DMXX==='XSSQBG')
        }else {
          state[resList]=state[resList].filter(item=>item.DMXX!=='WJBG')
        }
      }
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData
    }
  }

})
</script>

<style scoped>

</style>
