<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="140px"
           size="default" v-loading="loading" @submit.prevent>
    <el-row :gutter="0" class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="父级机构名称：" prop="PORGNA_NAME">
          <el-input v-model="formData.PORGNA_NAME" type="text" placeholder="请输入" clearable :disabled="true">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="父级机构代码：" prop="PORGNA_ID">
          <el-input v-model="formData.PORGNA_ID" type="text" placeholder="请输入" clearable :disabled="true">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
        <el-form-item label="机构层级：" prop="ORGNA_FLOOR_NAME">
          <el-input v-model="formData.ORGNA_FLOOR_NAME" type="text" placeholder="请输入" clearable :disabled="true">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="16" class="grid-cell">
        <el-form-item label="机构名称：" prop="ORGNA_NAME">
          <el-input @change="changeOrgName" v-model="formData.ORGNA_NAME" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell no-border-bottom">
        <el-form-item label="排序号：" prop="ORGNA_ORDER">
          <el-input v-model="formData.ORGNA_ORDER" type="text" placeholder="请输入" clearable :disabled="!editable"
          @input="formData.ORGNA_ORDER=formData.ORGNA_ORDER.replace(/[^\d]/g, '')">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell no-border-bottom" v-if="formData.PORGNA_ID==='00450388d2154c2584e9c62b4534a83b'">
        <el-form-item label="机构编码：" prop="ORGNA_CODE">
          <el-input v-model="formData.ORGNA_CODE" type="text" placeholder="请输入机构编码" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
        <div style="color: #F56C6C">若该组织机构为承包商，组织机构编码为承包商统一信用代码</div>
      </el-col>
    </el-row>

    <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
      <el-button type="success" @click="saveData()" v-if="editable">保存</el-button>
      <el-button @click="closeForm">返回</el-button>
    </div>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "../../../lib/vsAuth";
import axiosUtil from "../../../lib/axiosUtil";
import comFun from "../../../lib/comFun";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      editable: props.params.editable,
      orgId: props.params.id,
      formData:{
        ORGNA_NAME:null,
        PORGNA_ID:null,
        PORGNA_NAME: null,
        ORGNA_FLOOR_ID: null,
        ORGNA_FLOOR_NAME: null,
        ORGNA_ORDER: null,
      },
      rules: {
        ORGNA_NAME: [{
          required: true,
          message: '字段值不可为空',
        }],
        ORGNA_ORDER: [{
          required: true,
          message: '字段值不可为空',
        }],
        ORGNA_CODE: [{
          required: true,
          validator: async (rule, value, callback) => {
            if (!value) {
              callback(new Error('字段值不可为空'))
            } else {
              let isCF = await checkProp(value,'ORGNA_CODE')
              if(Boolean(isCF.data)){
                callback()
              }else {
                callback(new Error('机构编码不能重复'))
              }
            }
          },
        }],
      },
      loading: false,

    })

    const getFormData = () => {
      let params = {
        orgId: state.orgId
      }
      state.loading = true
      axiosUtil.get('/backend/common/getOrgInfoById', params).then((res) => {
        state.formData = res.data
        state.formData.PORGNA_NAME=props.params.pNode.ORGNA_NAME
        state.loading = false
      });
    }

    const checkProp = (value,prop) => {
      let params={
        orgId: state.orgId,
        value: value,
        prop: prop
      }
      return axiosUtil.get('/backend/common/checkProp', params)
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const submitForm = () => {
      let params
      if(props.params.operation === 'add'){
        params={
          ORGNA_ID: props.params.id,
          ORGNA_CODE: state.formData.ORGNA_CODE || props.params.id,
          ...state.formData,
          ENABLED: '1',
          CREATOR: state.userInfo.userId,
          CREATE_TIME: comFun.getNowTime(),
          ORGNA_SRC_TYPE:'1',
          ORGNA_DESC:'1',
          ZT: 'add'
        }
      }else {
        params={
          ...state.formData
        }
        console.log(props.params.pNode)
      }
      axiosUtil.post('/backend/common/saveOrgInfo', params).then((res) => {
        ElMessage.success('保存成功')
      });
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const changeOrgName = (value) => {
      let floorList=state.formData.ORGNA_FLOOR_NAME.split(',')
      floorList[floorList.length-1]=value
      state.formData.ORGNA_FLOOR_NAME=floorList.join(',')
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      }else {
        state.formData.ORGNA_FLOOR_ID=props.params.pNode.ORGNA_FLOOR_ID+','+props.params.id
        state.formData.ORGNA_FLOOR_NAME=props.params.pNode.ORGNA_FLOOR_NAME+','
        state.formData.PORGNA_ID=props.params.pNode.ORGNA_ID
        state.formData.PORGNA_NAME=props.params.pNode.ORGNA_NAME
      }
    })

    return {
      ...toRefs(state),
      saveData,
      changeOrgName,
      closeForm

    }
  }

})
</script>

<style scoped>
:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}
</style>
