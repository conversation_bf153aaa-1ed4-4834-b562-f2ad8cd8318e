<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMMC">
            <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMMC">
            <el-input ref="input45296" placeholder="请输入创建时间" v-model="listQuery.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMMC">
            <el-input ref="input45296" placeholder="请输入创建人" v-model="listQuery.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col> -->

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="XMMC" label="项目名称" align="center" :show-overflow-tooltip="true" min-width="160">
                <template #default="scope">
                    <span>
                      <el-button size="small" link type="primary"
                                 @click="openXmyxdh(scope.row)">{{ scope.row.XMMC }}
                      </el-button>
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="ZZFSMC" label="选商组织方式" align="center" :show-overflow-tooltip="true" width="160">
              </el-table-column>
              <el-table-column prop="XSSQ" label="选商方案" align="center" :show-overflow-tooltip="true" width="120">
                <template #default="scope">
                    <span>
                      <el-button size="small" v-if="scope.row.FASHZT==='2'" link type="primary"
                                 @click="workEdit(scope.row,'XSFA',false,'view')">{{ scope.row.FACJSJ }}</el-button>
                      <!-- 审批完成有效 -->
                      <el-button size="small" v-if="scope.row.FASHZT==='1'" link type="primary"
                                 @click="workEdit(scope.row,'XSFA',false,'view')">审批中</el-button><!-- 审批中有效 -->
                      <el-button size="small" v-if="scope.row.FASHZT==='0'" link type="primary"
                                 @click="workEdit(scope.row,'XSSQ',false,'view')">已保存</el-button><!-- 已保存有效 -->
                    </span>
                </template>
              </el-table-column>
<!--              <el-table-column prop="XSWJ" label="选商文件" align="center" :show-overflow-tooltip="true" width="120">-->
<!--                <template #default="scope">-->
<!--                    <span>-->
<!--                      <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>-->
<!--                      <el-button size="small" v-else-if="(!scope.row.WJID || scope.row.FASHZT==='0' || scope.row.FASHZT==='1') && scope.row.YWZT==='1'" link type="primary">&#45;&#45;</el-button>&lt;!&ndash; 未发起 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.WJSHZT==='2'" link type="primary"-->
<!--                                 @click="workEdit(scope.row,'XSWJ',false,'view')">{{ scope.row.WJCJSJ }}</el-button>-->
<!--                      &lt;!&ndash; 审批完成有效 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.WJSHZT==='1'" link type="primary"-->
<!--                                 @click="workEdit(scope.row,'XSWJ',false,'view')">审批中</el-button>&lt;!&ndash; 审批中有效 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.WJSHZT==='0' && scope.row.FASHZT==='2' && scope.row.YWZT==='1'" link type="primary"-->
<!--                                 @click="workEdit(scope.row,'XSWJ',true,'edit')">{{scope.row.LSWJID ? '变更中' : '待提交'}}</el-button>&lt;!&ndash; 已保存有效 &ndash;&gt;-->
<!--                    </span>-->
<!--                </template>-->
<!--              </el-table-column>-->

<!--              <el-table-column prop="XSGG" label="选商通知发布" align="center" :show-overflow-tooltip="true" width="160">-->
<!--                <template #default="scope">-->
<!--                    <span>-->
<!--                      <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>-->
<!--                      <el-button size="small" v-else-if="!scope.row.GGID && scope.row.WJSHZT==='2' && scope.row.YWZT==='1'"-->
<!--                                 link type="primary"-->
<!--                                 @click="workEdit(scope.row,'XSGG',true,'add')">未发起</el-button>&lt;!&ndash; 未发起 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.GGSHZT==='2'" link type="primary"-->
<!--                                 @click="workEdit(scope.row,'XSGG',false,'view')">{{ scope.row.GGCJSJ }}</el-button>-->
<!--                      &lt;!&ndash; 审批完成有效 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.GGSHZT==='1'" link type="primary"-->
<!--                                 @click="workEdit(scope.row,'XSGG',false,'view') && scope.row.YWZT==='1'">审批中</el-button>-->
<!--                      &lt;!&ndash; 审批中有效 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.GGSHZT==='0'" link type="primary"-->
<!--                                 @click="workEdit(scope.row,'XSGG',true,'edit') && scope.row.YWZT==='1'">已保存</el-button>-->
<!--                      &lt;!&ndash; 已保存有效 &ndash;&gt;-->

<!--                      <el-button size="small" v-else-if="!scope.row.GGID && scope.row.WJSHZT!=='2' && scope.row.YWZT==='1'" link-->
<!--                                 type="primary">&#45;&#45;</el-button>-->
<!--                    </span>-->
<!--                </template>-->
<!--              </el-table-column>-->

<!--              <el-table-column prop="XSHH" label="回函" align="center" :show-overflow-tooltip="true" width="120">-->
<!--                <template #default="scope">-->
<!--                    <span>-->
<!--                         <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>-->
<!--                      <el-button size="small"-->
<!--                                 v-else-if="scope.row.HHSHZT!=='2' && scope.row.GGSHZT==='2' && scope.row.YWZT==='1'" link-->
<!--                                 type="primary"-->
<!--                                 @click="workEdit(scope.row,'XSHH',true,'edit')">确认</el-button>&lt;!&ndash; 已发布公告可确认回函 &ndash;&gt;-->
<!--                        <el-button size="small" v-else-if="scope.row.HHSHZT==='2'" link type="primary"-->
<!--                                   @click="workEdit(scope.row,'XSHH',false,'view')">查看</el-button>&lt;!&ndash; 已确认回函可查看回函 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">&#45;&#45;</el-button>-->
<!--                    </span>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--              <el-table-column prop="WJLQ" label="采购文件领取情况" align="center" :show-overflow-tooltip="true"-->
<!--                               width="120">-->
<!--                <template #default="scope">-->
<!--                    <span>-->
<!--                       <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>-->
<!--                      <el-button size="small" v-else-if="scope.row.HHSHZT==='2'" link type="primary"-->
<!--                                 @click="workEdit(scope.row,'WJLQ',false,'view')">查看</el-button>-->
<!--                      &lt;!&ndash; 已确认回函可查看招标文件领取情况 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">&#45;&#45;</el-button>-->
<!--                    </span>-->
<!--                </template>-->
<!--              </el-table-column>-->
              <el-table-column prop="HY" label="预定会议" align="center" :show-overflow-tooltip="true" width="120">
                <template #default="scope">
                    <span>
                       <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>
                      <el-button size="small" v-else-if="scope.row.FASHZT==='2'&&!scope.row.HYSHZT && scope.row.YWZT==='1'"
                                 link type="primary" @click="workEdit(scope.row,'YDHY',true,'add')">待处理</el-button>
                      <!-- 未组会 -->
                      <el-button size="small" v-else-if="scope.row.HYSHZT==='0' && scope.row.YWZT==='1'"
                                 link type="primary" @click="workEdit(scope.row,'YDHY',true,'edit')">待提交</el-button>
                      <!-- 未组会 -->
                      <el-button size="small" v-else-if="scope.row.HYSHZT==='2'"
                                 link type="primary" @click="workEdit(scope.row,'YDHY',false,'view')">已完成</el-button>
                      <!-- 已组会 -->
                       <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">--</el-button>
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="PWCQ" label="组建评标委员会" align="center" :show-overflow-tooltip="true" width="120">
                <template #default="scope">
                    <span>
                        <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>
                      <el-button size="small"
                                 v-else-if="scope.row.HYSHZT==='2' && !scope.row.PWCQSHZT && scope.row.YWZT==='1' " link
                                 type="primary"
                                 @click="workEdit(scope.row,'PWCQ',true,'edit')">待处理</el-button><!-- 抽取评委 -->
                      <el-button size="small" v-else-if="scope.row.PWCQSHZT==='0' && scope.row.YWZT==='1'"
                                 link type="primary" @click="workEdit(scope.row,'PWCQ',true,'edit')">待提交</el-button>
                      <el-button size="small" v-else-if="scope.row.PWCQSHZT==='1'" link type="primary"
                                 @click="workEdit(scope.row,'PWCQ',false,'view')">审核中</el-button><!-- 抽取评委 -->
                      <!-- 未组会 -->
                      <el-button size="small" v-else-if="scope.row.PWCQSHZT==='2'" link type="primary"
                                 @click="workEdit(scope.row,'PWCQ',false,'view')">查看</el-button><!-- 抽取评委 -->
                      <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">--</el-button>
                    </span>
                </template>
              </el-table-column>
<!--              <el-table-column prop="TBWJ" label="响应文件上传" align="center" :show-overflow-tooltip="true" width="120">-->
<!--                <template #default="scope">-->
<!--                    <span>-->
<!--                        <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>-->
<!--                       <el-button size="small" v-else-if="scope.row.XSFS==='GKZB'" link type="primary">/</el-button>-->
<!--                      <el-button size="small" v-else-if="!scope.row.TBSHZT && scope.row.HHSHZT==='2' && scope.row.YWZT==='1'" link-->
<!--                                 type="primary"-->
<!--                                 @click="workEdit(scope.row,'TBWJ',true,'edit')">未完成</el-button>&lt;!&ndash; 已发布公告可查看招标文件递交情况 &ndash;&gt;-->
<!--                         <el-button size="small" v-else-if="scope.row.TBSHZT==='0' && scope.row.YWZT==='1'"-->
<!--                                    link type="primary" @click="workEdit(scope.row,'TBWJ',true,'edit')">待提交</el-button>-->
<!--                       <el-button size="small" v-else-if="scope.row.TBSHZT==='2'" link type="primary"-->
<!--                                  @click="workEdit(scope.row,'TBWJ',false,'view')">查看</el-button>-->
<!--                      &lt;!&ndash; 已发布公告可查看招标文件递交情况 &ndash;&gt;-->
<!--                      <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">&#45;&#45;</el-button>-->
<!--                    </span>-->
<!--                </template>-->
<!--              </el-table-column>-->
              <el-table-column prop="ZBJG" label="评审资料及评标报告上传" align="center" :show-overflow-tooltip="true" width="120">
                <template #default="scope">
                    <span>
                       <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>
                      <el-button size="small"
                                 v-else-if="scope.row.PWCQSHZT==='2' && !scope.row.JGSHZT && scope.row.YWZT==='1'"
                                 link type="primary"
                                 @click="workEdit(scope.row,'ZBJG',true,'add')">待处理</el-button><!--  -->
                        <el-button size="small" v-else-if="scope.row.JGSHZT==='0' && scope.row.YWZT==='1'" link
                                   type="primary"
                                   @click="workEdit(scope.row,'ZBJG',true,'edit')">待提交</el-button><!-- 抽取评委 -->
                      <el-button size="small" v-else-if="scope.row.JGSHZT==='2'" link type="primary"
                                 @click="workEdit(scope.row,'ZBJG',false,'view')">查看</el-button><!--  -->
                       <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">--</el-button>
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="ZBGS" label="成交候选人公示" align="center" :show-overflow-tooltip="true" width="120">
                <template #default="scope">
                    <span>
                        <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>
                      <el-button size="small"
                                 v-else-if="!scope.row.JGGSSHZT && scope.row.JGSHZT==='2' && scope.row.YWZT==='1'" link
                                 type="primary"
                                 @click="workEdit(scope.row,'ZBGS',true,'add')">待处理</el-button><!--  -->
                      <el-button size="small" v-else-if="scope.row.JGGSSHZT==='0' && scope.row.YWZT==='1'" link
                                 type="primary"
                                 @click="workEdit(scope.row,'ZBGS',true,'edit')">待提交</el-button><!--  -->
                      <el-button size="small" v-else-if="scope.row.JGGSSHZT==='2'" link type="primary"
                                 @click="workEdit(scope.row,'ZBGS',false,'view')">查看</el-button><!--  -->
                       <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">--</el-button>

                        <el-button size="small" v-if="scope.row.JGGSSHZT==='2'" link type="primary"
                                   @click="workEdit(scope.row,'GSYL',false,'view')">预览</el-button><!--  -->
                    </span>
                </template>
              </el-table-column>

              <el-table-column prop="ZBQR" label="成交结果" align="center" :show-overflow-tooltip="true" width="120">
                <template #default="scope">
                    <span>
                     <el-button size="small"
                                v-if="((scope.row.JGGSSHZT==='2' && (scope.row.FASHZT==='2' || (scope.row.GGSHZT==='2' && scope.row.XSFS==='GKZB')))
                                 || (scope.row.FASHZT==='2' && scope.row.SFDMTZ==='1')) && !scope.row.ZBQRSHZT && scope.row.YWZT==='1'"
                                link type="primary"
                                @click="workEdit(scope.row,'ZBQR',true,'add')">待处理</el-button><!--  -->
                        <el-button size="small" v-else-if="scope.row.ZBQRSHZT==='0' && scope.row.YWZT==='1'" link
                                   type="primary"
                                   @click="workEdit(scope.row,'ZBQR',true,'edit')">待提交</el-button><!-- 抽取评委 -->
                      <el-button size="small" v-else-if="scope.row.ZBQRSHZT==='2'" link type="primary"
                                 @click="workEdit(scope.row,'ZBQR',false,'view')">查看</el-button><!--  -->
                       <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">--</el-button>
                    </span>
                </template>
              </el-table-column>

              <el-table-column prop="ZBTZ" label="成交通知书发放" align="center" :show-overflow-tooltip="true" width="120">
                <template #default="scope">
                    <span>
                         <el-button size="small" v-if="scope.row.SFDMTZ==='1'" link type="primary">/</el-button>
                      <el-button size="small" v-else-if="!scope.row.TZSHZT&&scope.row.ZBQRSHZT==='2' && scope.row.YWZT==='1'"
                                 link type="primary"
                                 @click="workEdit(scope.row,'ZBTZ',true,'add')">待处理</el-button><!--  -->
                        <el-button size="small" v-else-if="scope.row.TZSHZT==='0'&& scope.row.YWZT==='1'" link
                                   type="primary"
                                   @click="workEdit(scope.row,'ZBTZ',true,'edit')">待提交</el-button><!--  -->
                      <el-button size="small" v-else-if="scope.row.TZSHZT==='2'" link type="primary"
                                 @click="workEdit(scope.row,'ZBTZ',false,'view')">查看</el-button><!--  -->
                       <el-button size="small" v-else-if="scope.row.YWZT==='1'" link type="primary">--</el-button>

                        <el-button size="small" v-if="scope.row.TZSHZT==='2' && scope.row.ZBJGGSID" link type="primary"
                                   @click="workEdit(scope.row,'TGYL',false,'view')">公告</el-button><!--  -->
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="CZ" label="操作" align="center"
                               :show-overflow-tooltip="true" width="160">
                <template #default="scope">
                    <span v-if="scope.row.YWZT==='1'">
                      <el-button size="small" class="lui-table-button" type="primary" @click="changeYxlc(scope.row)">运行流程调整
                      </el-button>
                    </span>
                  <span v-else>已终止</span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="dialogTitle"
        @closed="closeForm"
        z-index="1200"
        top="5vh"
        :width="dialogWidth">
      <div>
        <component :is="pageComponent[busType]" v-if="pageComponent[busType] && dialogVisible" :params="params"
                   @close="closeForm"/>
      </div>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogYXDHVisible"
        v-model="dialogYXDHVisible"
        title="项目运行导航卡片"
        @closed="getDataList"
        z-index="1000"
        top="5vh"
        :width="dialogWidth">
      <div>
        <fzbxmdxCard ref="YXCard" v-if="dialogYXDHVisible" :params="params"
                    @workEdit="workEdit" @changeYxlc="changeYxlc"/>
      </div>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogLCTZVisible"
        v-model="dialogLCTZVisible"
        title="项目运行流程调整"
        @closed="closeLc"
        z-index="1100"
        top="5vh"
        :width="dialogWidth">
      <div>
        <lctzEdit v-if="dialogLCTZVisible" :params="params" @close="closeLc"/>
      </div>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogXSFSVisible"
        v-model="dialogXSFSVisible"
        title="选择选商方式"
        z-index="1000"
        width="500px">
      <div style="padding: 20px">
        <el-radio-group v-model="XSFS" size="default" style="display: flex;flex-direction: column;gap: 20px">
          <el-radio style="width: 150px;margin-right: 0" :label="item.DMXX" v-for="(item,index) in XSFSOptions" :key="index">{{item.DMMC}}</el-radio>
        </el-radio-group>

        <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
          <el-button size="default" type="primary" @click="addData">确定</el-button>
          <el-button size="default" @click="dialogXSFSVisible=false">返回</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import lctzEdit from "@views/zbxsgl/xmyx/lctzEdit";
import fzbxmdxCard from "@views/zbxsgl/xmyx/dxCard/fzbxmdxCard";

import xssqglEdit from "@views/zbxsgl/xssqgl/xssqglEdit";
import zbfaEdit from "@views/zbxsgl/xssqgl/zbfaEdit";
import xswjEdit from "@views/zbxsgl/xssqgl/xswjEdit";
import cgxmglEdit from "@views/zbxsgl/cgxmgl/cgxmglEdit";
import xstzfbEdit from "@views/zbxsgl/xstzfb/xstzfbEdit";
import xstzfbView from "@views/zbxsgl/xstzfb/xstzfbView";
import hhqkqrEdit from "@views/zbxsgl/hhqkqr/hhqkqrEdit";
import zbwjlqqkView from "@views/zbxsgl/zbwjlq/zbwjlqqkView";
import tbwjqrEdit from "@views/zbxsgl/tbwjdj/tbwjqrEdit";
import ydhyEdit from "@views/zbxsgl/ydhy/ydhyEdit";
import pwcqglEdit from "@views/zbxsgl/pwcq/pwcqglEdit";
import dbsqglEdit from "@views/zbxsgl/dbsqgl/dbsqglEdit";
import zbjggsEdit from "@views/zbxsgl/zbjggs/zbjggsEdit";
import zbjggsView from "@views/zbxsgl/zbjggs/zbjggsView";
import zbtzsqfEdit from "@views/zbxsgl/zbtzsqf/zbtzsqfEdit";
import zbtzggView from "@views/zbxsgl/zbtzsqf/zbtzggView";
import zbjgqrEdit from "@views/zbxsgl/zbjgqr/zbjgqrEdit";
import vsAuth from "@lib/vsAuth";




export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,lctzEdit,fzbxmdxCard},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 20,
        XSFS: 'JB,JJ,GKJB,GKJJ'
      },
      busType: "",//业务环节
      dialogTitle: '',//业务名称
      dialogWidth: '1400px',
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,

      XSFSOptions: [],

      pageComponent: {

        XSSQ: markRaw(xssqglEdit),
        XSFA: markRaw(zbfaEdit),
        XSWJ: markRaw(xswjEdit),
        XSGG: markRaw(xstzfbEdit),
        XSBM: markRaw(xstzfbView),
        XSHH: markRaw(hhqkqrEdit),
        WJLQ: markRaw(zbwjlqqkView),
        TBWJ: markRaw(tbwjqrEdit),
        YDHY: markRaw(ydhyEdit),
        PWCQ: markRaw(pwcqglEdit),
        ZBJG: markRaw(dbsqglEdit),
        ZBGS: markRaw(zbjggsEdit),
        GSYL: markRaw(zbjggsView),
        ZBQR: markRaw(zbjgqrEdit),
        ZBTZ: markRaw(zbtzsqfEdit),
        TGYL: markRaw(zbtzggView),
      },

      dialogLCTZVisible: false,

      XSFS: '',
      FARow: null,
      dialogXSFSVisible: false,
      dialogYXDHVisible: false


    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        orgnaId: vsAuth.getAuthInfo().permission.orgnaId,
      }
      axiosUtil.get('/backend/xsgl/xmyx/selectXmyxJlPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }
    // 编辑
    const workEdit = (row, busType, editable, operation) => {
      state.busType = busType;
      state.params = {editable: editable, id: '', operation: operation}
      if (busType === 'XSSQ') {
        state.params.id = operation === 'add' ? comFun.newId() : row.FAID
        state.params.XMID=row.XMID
        state.params.XMMC=row.XMMC
        state.params.activeTab = 'ZBFA'
        state.dialogTitle = row.XMMC + '-选商申请'
      }


      if (busType === 'XSFA') {
        state.params.id = row.FAID
        state.dialogTitle = row.XMMC + '-选商申请方案'
      }

      if (busType === 'XSWJ') {
        state.params.id = operation === 'add' ? comFun.newId() : row.WJID
        state.dialogTitle = row.XMMC + '-选商文件'
      }

      if (busType === 'XSGG') {
        state.params.id = operation === 'add' ? comFun.newId() : row.GGID
        state.params.WJID = row.WJID
        state.dialogTitle = row.XMMC + '-选商通知'
      }

      if (busType === 'XSBM') {
        state.params.id = row.GGID
        state.dialogTitle = row.XMMC + '-报名参与'
      }

      if (busType === 'XSHH') {
        state.params.id = row.GGID
        state.dialogTitle = row.XMMC + '-回函确认'
      }

      if (busType === 'WJLQ') {
        state.params.id = row.GGID
        state.dialogTitle = row.XMMC + '-招标文件领取情况'
      }

      if (busType === 'TBWJ') {
        state.params.id = row.GGID
        state.dialogTitle = row.XMMC + '-响应文件上传情况'
      }

      if (busType === 'YDHY') {
        state.params.id = operation === 'add' ? comFun.newId() : row.PBHYBS
        state.dialogTitle = row.XMMC + '-评标会议预定'
      }

      if (busType === 'PWCQ') {
        state.params.id = row.PBHYBS
        state.dialogTitle = row.XMMC + '-评委抽取'
      }

      if (busType === 'ZBJG') {
        state.params.id = operation === 'add' ? comFun.newId() : row.XSJGID
        state.params.FAID = row.FAID
        state.params.GGID = row.GGID
        state.dialogTitle = row.XMMC + '-评审资料及评标报告上传'
      }

      if (busType === 'ZBGS') {
        state.params.id = operation === 'add' ? comFun.newId() : row.JGGSID
        state.params.FAID = row.FAID
        state.params.XSJGID = row.XSJGID
        state.dialogTitle = row.XMMC + '-成交候选人公示'
      }

      if (busType === 'GSYL') {
        state.params.id = row.JGGSID
        state.dialogTitle = row.XMMC + '-成交候选人公示预览'
      }

      if(busType === 'ZBQR'){
        state.params.id = row.XSJGID || comFun.newId()
        state.params.tableEdit= !row.JGGSID
        if(operation!=='view'){
          state.params.operation= row.XSJGID ? 'edit' : 'add'
        }
        state.params.FAID = row.FAID
        state.params.GGID = row.GGID
        state.dialogTitle = row.XMMC + '-成交结果'
      }

      if (busType === 'ZBTZ') {
        state.params.id = operation === 'add' ? comFun.newId() : row.ZBTZID
        state.params.FAID = row.FAID
        state.params.XSJGID = row.XSJGID
        state.dialogTitle = row.XMMC + '-成交通知书签发'
      }

      if (busType === 'TGYL') {
        state.params.id = row.ZBJGGSID
        state.dialogTitle = row.XMMC + '-成交结果公告预览'
      }

      state.dialogVisible = true;
    }

    const openXmyxdh = (row) => {
      state.params = {editable: false, id: row.JLID, operation: 'view'}
      state.dialogYXDHVisible = true
    }

    const changeYxlc = (row) => {
      state.params = {editable: true, id: row.JLID, operation: 'edit'}
      state.dialogLCTZVisible = true
    }

    const addNewFa = (row) => {
      state.FARow=row
      state.dialogXSFSVisible=true
    }
    const addData = () => {
      let row=state.FARow
      if(!state.XSFS){
        ElMessage.warning('请选择选商方式')
        return
      }
      state.dialogXSFSVisible=false
      state.busType='XSSQ'
      let SQLX=state.XSFSOptions.find(item=>item.DMXX===state.XSFS).BYZD1
      state.params = {editable: true, id: comFun.newId(), operation: 'add',XSFS: state.XSFS,SQLX: SQLX}
      state.params.XMID=row.XMID
      state.dialogVisible = true
    }

    const arraySpanMethod = ({ row, column, rowIndex, columnIndex}) => {
      //合并项目
      // if(columnIndex===1){
      //   if(rowIndex>0 && state.tableData[rowIndex-1].XMID===row.XMID){
      //     return [0,0]
      //   }else {
      //     let span=1
      //     let i=rowIndex+1
      //     while (i<state.tableData.length && state.tableData[i].XMID===row.XMID){
      //       span++
      //       i++
      //     }
      //     return [span,1]
      //   }
      // }

      //合并组织方式
      if(columnIndex===2){
        if(rowIndex>0 && state.tableData[rowIndex-1].XMID===row.XMID && state.tableData[rowIndex-1].ZZFSMC===row.ZZFSMC){
          return [0,0]
        }else {
          let span=1
          let i=rowIndex+1
          while (i<state.tableData.length && state.tableData[i].XMID===row.XMID && state.tableData[i].ZZFSMC===row.ZZFSMC){
            span++
            i++
          }
          return [span,1]
        }
      }

      //合并选商清单
      if(columnIndex===3){
        if(rowIndex>0 && state.tableData[rowIndex-1].FAID===row.FAID){
          return [0,0]
        }else {
          let span=1
          let i=rowIndex+1
          while (i<state.tableData.length && state.tableData[i].FAID===row.FAID){
            span++
            i++
          }
          return [span,1]
        }
      }

      //合并招标文件---投标
      if([4,5,6,7,8].includes(columnIndex)){
        if(rowIndex>0 && state.tableData[rowIndex-1].WJID===row.WJID){
          return [0,0]
        }else {
          let span=1
          let i=rowIndex+1
          while (i<state.tableData.length && state.tableData[i].WJID===row.WJID){
            span++
            i++
          }
          return [span,1]
        }
      }

      //合并会议
      if([8,9].includes(columnIndex)){
        if(rowIndex>0 && state.tableData[rowIndex-1].WJID===row.WJID && state.tableData[rowIndex-1].PBHYBS===row.PBHYBS){
          return [0,0]
        }else {
          let span=1
          let i=rowIndex+1
          while (i<state.tableData.length && state.tableData[i].WJID===row.WJID && state.tableData[i].PBHYBS===row.PBHYBS){
            span++
            i++
          }
          return [span,1]
        }
      }
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const instance = getCurrentInstance()
    const closeForm = () => {
      state.dialogVisible = false
      state.dialogLCTZVisible=false
      if(instance.proxy.$refs['YXCard']){
        instance.proxy.$refs['YXCard'].getFormData()
      }
      getDataList()
    }

    const closeLc = () => {
      state.dialogVisible = false
      state.dialogLCTZVisible=false
      state.dialogXSFSVisible=false
      getDataList()
    }

    onMounted(() => {
      getDMBData("XSFS", "XSFSOptions")
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      workEdit,
      closeForm,
      changeYxlc,
      arraySpanMethod,
      addData,
      addNewFa,
      openXmyxdh,
      closeLc
    }
  }

})
</script>

<style scoped>

</style>
