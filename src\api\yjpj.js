import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"


// 业绩评价-评价模版管理-保存评价周期
// @method postPjmbglSavePjzq
// @type post
// @return url
//postPjmbglSavePjzq: `/yjpj/pjmbgl/savePjzq`,

// eslint-disable-next-line
export function postPjmbglSavePjzq(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/pjmbgl/savePjzq`, params)
}
// 业绩评价-评价模版管理-保存评价模版及评价专业
// @method postPjmbglSavePjmb
// @type post
// @return url
//postPjmbglSavePjmb: `/yjpj/pjmbgl/savePjmb`,

// eslint-disable-next-line
export function postPjmbglSavePjmb(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/pjmbgl/savePjmb`, params)
}
// 业绩评价-评价模版管理-查询评价专业
// @method getPjmbglSelectZylx
// @type get
// @return url
//getPjmbglSelectZylx: `/yjpj/pjmbgl/selectZylx`,

// eslint-disable-next-line
export function getPjmbglSelectZylx(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/pjmbgl/selectZylx`, params)
}
// 业绩评价-评价模版管理-查询评分标准
// @method getPjmbglSelectPjmbPfbzById
// @type get
// @return url
//getPjmbglSelectPjmbPfbzById: `/yjpj/pjmbgl/selectPjmbPfbzById`,

// eslint-disable-next-line
export function getPjmbglSelectPjmbPfbzById(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/pjmbgl/selectPjmbPfbzById`, params)
}
// 业绩评价-评价模版管理-复制评价模板
// @method postPjmbglCopyPjmb
// @type post
// @return url
//postPjmbglCopyPjmb: `/yjpj/pjmbgl/copyPjmb`,

// eslint-disable-next-line
export function postPjmbglCopyPjmb(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/pjmbgl/copyPjmb`, params)
}
// 业绩评价-评价模版管理-查询评价模板及评分标准
// @method getPjmbglSelectPjmbById
// @type get
// @return url
//getPjmbglSelectPjmbById: `/yjpj/pjmbgl/selectPjmbById`,

// eslint-disable-next-line
export function getPjmbglSelectPjmbById(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/pjmbgl/selectPjmbById`, params)
}
// 业绩评价-评价模版管理-保存评价模板明细及评分标准
// @method postPjmbglSavePjmbmx
// @type post
// @return url
//postPjmbglSavePjmbmx: `/yjpj/pjmbgl/savePjmbmx`,

// eslint-disable-next-line
export function postPjmbglSavePjmbmx(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/pjmbgl/savePjmbmx`, params)
}
// 业绩评价-评价模版管理-查询评价模板明细及评分标准
// @method getPjmbglSelectPjmbmxByid
// @type get
// @return url
//getPjmbglSelectPjmbmxByid: `/yjpj/pjmbgl/selectPjmbmxByid`,

// eslint-disable-next-line
export function getPjmbglSelectPjmbmxByid(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/pjmbgl/selectPjmbmxByid`, params)
}
// 业绩评价-评价模版管理-查询评价周期
// @method getPjmbglSelectPjzq
// @type get
// @return url
//getPjmbglSelectPjzq: `/yjpj/pjmbgl/selectPjzq`,

// eslint-disable-next-line
export function getPjmbglSelectPjzq(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/pjmbgl/selectPjzq`, params)
}
// 业绩评价-评价模版管理-查询评价模版
// @method getPjmbglSelectPjmb
// @type get
// @return url
//getPjmbglSelectPjmb: `/yjpj/pjmbgl/selectPjmb`,

// eslint-disable-next-line
export function getPjmbglSelectPjmb(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/pjmbgl/selectPjmb`, params)
}
// 业绩评价-评价模版管理-查询评价模板明细
// @method getPjmbglSelectPjmbmx
// @type get
// @return url
//getPjmbglSelectPjmbmx: `/yjpj/pjmbgl/selectPjmbmx`,

// eslint-disable-next-line
export function getPjmbglSelectPjmbmx(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/pjmbgl/selectPjmbmx`, params)
}
// 业绩评价-华东业绩评价-查询评价活动
// @method getHdyjpjSelectPjhd
// @type get
// @return url
//getHdyjpjSelectPjhd: `/yjpj/hdyjpj/selectPjhd`,

// eslint-disable-next-line
export function getHdyjpjSelectPjhd(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/selectPjhd`, params)
}
// 业绩评价-华东业绩评价-考核汇总评级保存
// @method postHdyjpjSavePjhz
// @type post
// @return url
//postHdyjpjSavePjhz: `/yjpj/hdyjpj/savePjhz`,

// eslint-disable-next-line
export function postHdyjpjSavePjhz(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/hdyjpj/savePjhz`, params)
}
// 业绩评价-华东业绩评价-查询单项目评价结果
// @method getHdyjpjSelectDxmPjjg
// @type get
// @return url
//getHdyjpjSelectDxmPjjg: `/yjpj/hdyjpj/selectDxmPjjg`,

// eslint-disable-next-line
export function getHdyjpjSelectDxmPjjg(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/selectDxmPjjg`, params)
}
// 业绩评价-华东业绩评价-保存评价活动
// @method postHdyjpjSavePjhd
// @type post
// @return url
//postHdyjpjSavePjhd: `/yjpj/hdyjpj/savePjhd`,

// eslint-disable-next-line
export function postHdyjpjSavePjhd(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/hdyjpj/savePjhd`, params)
}
// 业绩评价-华东业绩评价-查询承包商考核信息
// @method getHdyjpjSelectCbskhxx
// @type get
// @return url
//getHdyjpjSelectCbskhxx: `/yjpj/hdyjpj/selectCbskhxx`,

// eslint-disable-next-line
export function getHdyjpjSelectCbskhxx(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/selectCbskhxx`, params)
}
// 业绩评价-华东业绩评价-季度考核列表
// @method getHdyjpjJdkhList
// @type get
// @return url
//getHdyjpjJdkhList: `/yjpj/hdyjpj/jdkhList`,

// eslint-disable-next-line
export function getHdyjpjJdkhList(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/jdkhList`, params)
}
// 业绩评价-华东业绩评价-考核汇总_运算
// @method getHdyjpjKhhzys
// @type get
// @return url
//getHdyjpjKhhzys: `/yjpj/hdyjpj/khhzys`,

// eslint-disable-next-line
export function getHdyjpjKhhzys(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/khhzys`, params)
}
// 业绩评价-华东业绩评价-查询队伍列表分页
// @method getHdyjpjSelectDwList
// @type get
// @return url
//getHdyjpjSelectDwList: `/yjpj/hdyjpj/selectDwList`,

// eslint-disable-next-line
export function getHdyjpjSelectDwList(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/selectDwList`, params)
}
// 业绩评价-华东业绩评价-查询评价模板id
// @method getHdyjpjSelectPjmb
// @type get
// @return url
//getHdyjpjSelectPjmb: `/yjpj/hdyjpj/selectPjmb`,

// eslint-disable-next-line
export function getHdyjpjSelectPjmb(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/selectPjmb`, params)
}
// 业绩评价-华东业绩评价-保存队伍考核评价信息
// @method postHdyjpjSaveDxmplmx
// @type post
// @return url
//postHdyjpjSaveDxmplmx: `/yjpj/hdyjpj/saveDxmplmx`,

// eslint-disable-next-line
export function postHdyjpjSaveDxmplmx(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/hdyjpj/saveDxmplmx`, params)
}
// 业绩评价-华东业绩评价-查询队伍列表分页2
// @method getHdyjpjSelectDwListii
// @type get
// @return url
//getHdyjpjSelectDwListii: `/yjpj/hdyjpj/selectDwListii`,

// eslint-disable-next-line
export function getHdyjpjSelectDwListii(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/selectDwListii`, params)
}
// 业绩评价-华东业绩评价-更新评价活动状态
// @method postHdyjpjUpdatePjhdzt
// @type post
// @return url
//postHdyjpjUpdatePjhdzt: `/yjpj/hdyjpj/updatePjhdzt`,

// eslint-disable-next-line
export function postHdyjpjUpdatePjhdzt(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/hdyjpj/updatePjhdzt`, params)
}
// 业绩评价-华东业绩评价-保存评价活动状态
// @method postHdyjpjSavePjhdZt
// @type post
// @return url
//postHdyjpjSavePjhdZt: `/yjpj/hdyjpj/savePjhdZt`,

// eslint-disable-next-line
export function postHdyjpjSavePjhdZt(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/hdyjpj/savePjhdZt`, params)
}
// 业绩评价-华东业绩评价-发起和审核列表
// @method getHdyjpjShList
// @type get
// @return url
//getHdyjpjShList: `/yjpj/hdyjpj/shList`,

// eslint-disable-next-line
export function getHdyjpjShList(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/shList`, params)
}
// 业绩评价-华东业绩评价-承包商考核列表
// @method getHdyjpjCbskhList
// @type get
// @return url
//getHdyjpjCbskhList: `/yjpj/hdyjpj/cbskhList`,

// eslint-disable-next-line
export function getHdyjpjCbskhList(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/cbskhList`, params)
}
// 业绩评价-华东业绩评价-保存评价活动2
// @method postHdyjpjSavePjhdii
// @type post
// @return url
//postHdyjpjSavePjhdii: `/yjpj/hdyjpj/savePjhdii`,

// eslint-disable-next-line
export function postHdyjpjSavePjhdii(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/hdyjpj/savePjhdii`, params)
}
// 业绩评价-华东业绩评价-考核汇总列表
// @method getHdyjpjKhhzList
// @type get
// @return url
//getHdyjpjKhhzList: `/yjpj/hdyjpj/khhzList`,

// eslint-disable-next-line
export function getHdyjpjKhhzList(params) {
    return axiosUtil.get(`${baseUrl}/yjpj/hdyjpj/khhzList`, params)
}
// 业绩评价-华东业绩评价-保存单项目评价
// @method postHdyjpjSaveDxmpj
// @type post
// @return url
//postHdyjpjSaveDxmpj: `/yjpj/hdyjpj/saveDxmpj`,

// eslint-disable-next-line
export function postHdyjpjSaveDxmpj(params) {
    return axiosUtil.post(`${baseUrl}/yjpj/hdyjpj/saveDxmpj`, params)
}




