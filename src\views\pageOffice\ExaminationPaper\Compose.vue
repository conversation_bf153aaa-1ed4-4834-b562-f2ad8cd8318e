<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('')

const fetchData = async () => {
	try {
		const response = await request({
			url: '/ExaminationPaper/Compose?idlist=' + pageofficectrl.WindowParams,//pageofficectrl.WindowParams:获取POBrowser.openWindow方法第三个参数的值
			method: 'get',
		});
		poHtmlCode.value = response;
	} catch (error) {
		console.error('There has been a problem with your axios operation:', error);
	}
};

function OnPageOfficeCtrlInit() {
	pageofficectrl.CustomToolbar = false;//隐藏自定义工具栏
}

onMounted(() => {
	fetchData();
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
	<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
</template>
