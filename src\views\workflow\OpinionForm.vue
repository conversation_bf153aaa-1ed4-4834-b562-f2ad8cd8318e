<!-- 审核意见页面 -->
<template>
  <div class="root" ref="root">
    <!-- <div style="display: flex">
      <label style="margin-left:10px">是否发送短信:</label>
      <el-switch style="display: block;margin-left: 20px"  active-value="1"  active-color="#13ce66" inactive-color="#ff4949" inactive-value="0" v-model="sffsdx"></el-switch>
    </div> -->
    <el-row style="padding:12px" v-if="showBlrXz">
      <el-col :span="24" style="text-align: center;">
        <el-radio-group @change="changeBLFS" v-model="blfs" :load="changeBLFS('BR')">
          <el-radio label="BR">本人办理</el-radio>
          <el-radio label="QT">其他人员审核</el-radio>
        </el-radio-group>
      </el-col>
      <el-col :span="24" style="text-align: center;" v-if="blfs==='QT'">
        <el-select v-model="nextPerformer" multiple placeholder="请选择办理人" style="width:330px">
          <el-option
              v-for="item in blrArray"
              :key="item.USER_LOGINNAME"
              :label="item.USER_NAME"
              :value="item.USER_LOGINNAME"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-input style="margin-top: 20px" v-model="shyj" type="textarea" :rows="5" placeholder="请输入审核意见"></el-input>
    <div style="text-align: center; height: 40px; padding: 10px">
      <el-button type="primary" @click="onConfirm">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>
  </div>
</template>
<script>
import axiosUtil from "../../lib/axiosUtil";

let util = null;
import {ElLoading, ElMessage} from "element-plus";
import vsAuth from "@src/lib/vsAuth";

export default {
  name: "OpinionForm",
  props: {
    apprValue: String,
    params: Object,
  },
  data() {
    return {
      userInfo: vsAuth.getAuthInfo().permission,
      shyj: "",
      sffsdx: '1',
      blrArray: [],
      showBlrXz: false,
      nextPerformer: [],
      blfs:'BR'
    };
  },
  computed: {},
  watch: {},
  methods: {
    getBlrList(roleCode, userLoginName, type) {
      axiosUtil.post('/backend/workFlow/findUserListByRole', {
        roleCode: roleCode,
        userLoginName: userLoginName,
        type: type
      }).then((res) => {
        this.blrArray = res.data
      });
    },
    getTbmBlrList(roleCode, userLoginName, type) {
      axiosUtil.post('/backend/workFlow/findTbmUserList', {
        roleCode: roleCode,
        userLoginName: userLoginName,
        type: type
      }).then((res) => {
        this.blrArray = res.data
      });
    },
    changeBLFS(value){
      if(value==='BR'){
        this.nextPerformer=[this.userInfo.userLoginName]
      }else {
        this.nextPerformer=[]
      }
    },
    onConfirm() {
      if(this.apprValue==='0'&& !this.shyj){
        ElMessage({message: "请输入退回意见！", type: 'error', customClass: 'message_index'})
        return
      }
      if (this.showBlrXz) {
        if (this.nextPerformer && this.nextPerformer.length > 0) {
          if(this.params.processId === 'ZTB_XSSQLC'&& this.blfs==='BR'){
            axiosUtil.get('/backend/gcyztb/zbsqgl/changeSHLC',{XMID: this.params.businessId}).then(()=>{
              this.$emit("confirm", this.shyj, this.sffsdx, this.nextPerformer)
            })
          }else {
            this.$emit("confirm", this.shyj, this.sffsdx, this.nextPerformer)
          }
        } else {
          ElMessage({message: "请选择下节点办理人", type: 'error', customClass: 'message_index'})
        }
      } else {
        this.$emit("confirm", this.shyj, this.sffsdx, this.nextPerformer);
      }
    },
    onCancel() {
      this.$emit("close");
    },
  },
  components: {},
  created() {
  },
  mounted() {
    if (this.apprValue != '0') {
      this.shyj = '同意'
      // if(this.params.processId==='ZTB_XSSQLC'&&this.params.activityId==='4'){
      //   this.getBlrList('userInfo',this.userInfo.userLoginName,'ZC')
      //   this.showBlrXz=true;
      // }
      // if (this.params.processId === 'ZTB_XSSQLC' && this.params.activityId === '3') {
      //   this.getTbmBlrList('userInfo', this.userInfo.userLoginName, 'ZC')
      //   this.showBlrXz = true;
      // }
    }
  },
};
</script>
<style>
.root {
  padding: 10px;
}

.message_index {
  z-index: 9999 !important;
}
</style>