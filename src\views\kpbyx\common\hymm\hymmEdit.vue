<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent v-loading="loading">
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;color: #2A96F9;
      border-bottom: 3px solid #2A96F9;text-align: center;padding-bottom: 2px;padding-left: 20px;padding-right: 20px;">
        设置评标会密码
      </div>

      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="输入密码：" prop="JE">
            <el-input v-model="formData.HYMM" type="password" placeholder="请输入" :disabled="!editable" show-password v-if="editable">
            </el-input>
            <div v-else>已设置</div>
          </el-form-item>
        </el-col>
      </el-row>


      <div style="width: 100%;margin-top: 200px;margin-bottom: 10px;justify-content: center;display: flex;"
           v-if="editable">
        <el-button size="default" type="primary" @click="saveData('submit')">确定</el-button>
      </div>


    </el-form>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: props.parentForm.MM_WCZT!=='1',
      formData: {
        PBHYBS: props.params.PBHYBS,
        HYMM: ''
      },
      rules: {},
      loading: false
    })

    const saveData = () => {
      if (!state.formData.HYMM) {
        ElMessage.warning('请输入密码')
        return
      }
      state.loading = true
      let params = {
        ...state.formData,
      }
      axiosUtil.post('/backend/kpbyx/szpbmm/saveHyMM', params).then(res => {
        ElMessage.success('设置成功')
        emit('saveFromData', {MM_WCSJ: comFun.getNowTime(),MM_WCZT: '1'}, {})
        nextTick(() => {
          emit('nextStep','已设置会议密码，启评标会议')
        })
        state.loading = false
      })
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      saveData

    }
  }

})
</script>

<style scoped>

</style>
