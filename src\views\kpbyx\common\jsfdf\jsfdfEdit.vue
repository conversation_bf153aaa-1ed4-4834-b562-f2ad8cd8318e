<template>
  <div style="width: 100%" v-loading="loading">
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
      label-width="160px" size="default" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;
        border-bottom: 3px solid #2A96F9;padding-left: 10px;padding-right: 10px;text-align: center;padding-bottom: 2px">
        技术分打分
      </div>

      <!-- 当数据只有1行时，显示完整表格 -->
      <el-table
        v-if="formData.PSXXList && formData.PSXXList.length === 1"
        :data="formData.PSXXList"
        border
        class="lui-table"
        :highlight-current-row="true"
        size="default"
        :cell-style="{ padding: '10px 0' }"
        height="calc(100vh - 535px)">
        <el-table-column type="index" width="50" fixed="left" label="" align="center"/>
        <el-table-column prop="PJYS" header-align="center" align="center" width="150" label="评分因素" fixed="left"></el-table-column>
        <el-table-column prop="PJFZ" header-align="center" align="center" width="150" label="分值" fixed="left"></el-table-column>
        <el-table-column prop="SFGDFZ" header-align="center" align="center" width="120" label="是否固定分值" fixed="left"></el-table-column>
        <el-table-column prop="PJBZ" header-align="center" align="center" label="评分标准" fixed="left" min-width="200"></el-table-column>
        <el-table-column v-for="item in formData.TBRList" :key="'single-' + item.TBBSID" :property="item.TBBSID" header-align="center" align="center" :label="item.DWMC" width="150">
          <template #default="{row}">
            <el-input v-on:input="row[item.TBBSID] = row[item.TBBSID].replace(/[^\d.]/g, ''), countDf(item.TBBSID)"
                v-if="editable && row.type!=='HJ'" v-model="row[item.TBBSID]"></el-input>
            <span v-else>{{row[item.TBBSID]}}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 当数据有多行时，使用固定行的布局 -->
      <template v-if="formData.PSXXList && formData.PSXXList.length > 1">
        <!-- 固定第一行表格 -->
        <el-table
          :data="[formData.PSXXList[0]]"
          border
          class="lui-table fixed-first-row"
          :highlight-current-row="false"
          size="default"
          :cell-style="{ padding: '10px 0', backgroundColor: '#f5f7fa' }"
          :show-header="true"
          :height="40">
          <el-table-column type="index" width="50" fixed="left" label="" align="center"/>
          <el-table-column prop="PJYS" header-align="center" align="center" width="150" label="评分因素" fixed="left"></el-table-column>
          <el-table-column prop="PJFZ" header-align="center" align="center" width="150" label="分值" fixed="left"></el-table-column>
          <el-table-column prop="SFGDFZ" header-align="center" align="center" width="120" label="是否固定分值" fixed="left"></el-table-column>
          <el-table-column prop="PJBZ" header-align="center" align="center" label="评分标准" fixed="left" min-width="200"></el-table-column>
          <el-table-column v-for="item in formData.TBRList" :key="'first-' + item.TBBSID" :property="item.TBBSID" header-align="center" align="center" :label="item.DWMC" width="150">
            <template #default="{row}">
              <el-input v-on:input="row[item.TBBSID] = row[item.TBBSID].replace(/[^\d.]/g, ''), countDf(item.TBBSID)"
                  v-if="editable && row.type!=='HJ'" v-model="row[item.TBBSID]"></el-input>
              <span v-else>{{row[item.TBBSID]}}</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 主体表格（中间可滚动部分） -->
        <el-table
          v-if="formData.PSXXList.length > 2"
          :data="formData.PSXXList.slice(1, -1)"
          border
          class="lui-table main-table"
          :highlight-current-row="true"
          size="default"
          :cell-style="{ padding: '10px 0' }"
          :height="'calc(100vh - 655px)'"
          :show-header="false">
          <el-table-column type="index" width="50" fixed="left" label="" align="center">
            <template #default="{$index}">
              {{ $index + 2 }}
            </template>
          </el-table-column>
          <el-table-column prop="PJYS" header-align="center" align="center" width="150" label="评分因素" fixed="left"></el-table-column>
          <el-table-column prop="PJFZ" header-align="center" align="center" width="150" label="分值" fixed="left"></el-table-column>
          <el-table-column prop="SFGDFZ" header-align="center" align="center" width="120" label="是否固定分值" fixed="left"></el-table-column>
          <el-table-column prop="PJBZ" header-align="center" align="center" label="评分标准" fixed="left" min-width="200"></el-table-column>
          <el-table-column v-for="item in formData.TBRList" :key="'main-' + item.TBBSID" :property="item.TBBSID" header-align="center" align="center" :label="item.DWMC" width="150">
            <template #default="{row}">
              <el-input v-on:input="row[item.TBBSID] = row[item.TBBSID].replace(/[^\d.]/g, ''), countDf(item.TBBSID)"
                  v-if="editable && row.type!=='HJ'" v-model="row[item.TBBSID]"></el-input>
              <span v-else>{{row[item.TBBSID]}}</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 固定最后一行表格 -->
        <el-table
          :data="[formData.PSXXList[formData.PSXXList.length - 1]]"
          border
          class="lui-table fixed-last-row"
          :highlight-current-row="false"
          size="default"
          :cell-style="{ padding: '10px 0', backgroundColor: '#f0f9ff', fontWeight: 'bold' }"
          :show-header="false"
          :height="40">
          <el-table-column type="index" width="50" fixed="left" label="" align="center">
            <template #default="">
              {{ '' }}
            </template>
          </el-table-column>
          <el-table-column prop="PJYS" header-align="center" align="center" width="150" label="评分因素" fixed="left"></el-table-column>
          <el-table-column prop="PJFZ" header-align="center" align="center" width="150" label="分值" fixed="left"></el-table-column>
          <el-table-column prop="SFGDFZ" header-align="center" align="center" width="120" label="是否固定分值" fixed="left"></el-table-column>
          <el-table-column prop="PJBZ" header-align="center" align="center" label="评分标准" fixed="left" min-width="200"></el-table-column>
          <el-table-column v-for="item in formData.TBRList" :key="'last-' + item.TBBSID" :property="item.TBBSID" header-align="center" align="center" :label="item.DWMC" width="150">
            <template #default="{row}">
              <el-input v-on:input="row[item.TBBSID] = row[item.TBBSID].replace(/[^\d.]/g, ''), countDf(item.TBBSID)"
                  v-if="editable && row.type!=='HJ'" v-model="row[item.TBBSID]"></el-input>
              <span v-else>{{row[item.TBBSID]}}</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-form>

    <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;" v-if="editable">
      <el-button size="default" type="primary" @click="saveData('submit')">确认</el-button>
      <el-button size="default" @click="getFormData">刷新</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: false,
      // editable: props.parentForm.JSDF_WCZT !== '1',
      editable: true,
      formData: {
        PSXXList: [
          {PJBZ: '测试标准', type: 'SJ'},
          {PJBZ: '测试标准1', type: 'SJ'},
          {PJBZ: '测试标准2', type: 'SJ'},
          {PJBZ: '测试标准3', type: 'SJ'},
          {PJBZ: '测试标准4', type: 'SJ'},
          {PJBZ: '测试标准5', type: 'SJ'},
          {PJBZ: '测试标准6', type: 'SJ'},
          {PJBZ: '测试标准7', type: 'SJ'},
          {PJBZ: '测试标准8', type: 'SJ'},
          {PJBZ: '测试标准9', type: 'SJ'},
          {PJBZ: '测试标准10', type: 'SJ'},
        ],
        TBRList: [
          {TBBSID: '123', DWMC: '测试队伍'},
          {TBBSID: '1231', DWMC: '测试队伍1'},
          {TBBSID: '1232', DWMC: '测试队伍2'}
        ]
      },
      rules: {}
    })

    const getFormData = () => {
      computedRes()
    }


    const computedRes = () => {
      let HJRow = {PJYS: '技术分得分合计', PJFZ: '100', type: 'HJ'}

      let pushRow = state.formData.PSXXList.find(item => item.type === 'HJ')
      if (pushRow) {
        Object.assign(pushRow, HJRow)
      } else {
        state.formData.PSXXList.push(HJRow)
      }

      state.formData.TBRList.forEach(item => {
        countDf(item.TBBSID)
      })
    }

    const saveData = (type) => {

    }

    const viewRow = () => {

    }

    const countDf = (val) => {
      let sum=0
      state.formData.PSXXList.forEach(item=>{
        if(item.type==='SJ'){
          sum+=parseFloat(item[val] || 0)
        }
      })
      let HJRow = state.formData.PSXXList.find(item => item.type === 'HJ')
      if (HJRow) {
        HJRow[val] = sum
      }
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      getFormData,
      saveData,
      viewRow,
      countDf
    }
  }
})

</script>

<style scoped>
.button-PS {
  width: 80px;
  height: 30px;
  border: 1px solid #8c939d;
  border-radius: 5px;
  cursor: pointer;
  line-height: 30px;
}

.color-HG {
  background-color: #99cdfd;
  color: black;
}

.color-BHG {
  background-color: #fff3dc;
  color: #bb7b00;
}

.greenStyle {
  color: #5eb416;
}

.redStyle {
  color: #ff0000;
}

.blueStyle {
  color: #09d7ff;
}

/* 固定表格样式 */
.fixed-first-row {
  position: relative;
  z-index: 10;
  margin-bottom: 0;
}

.fixed-first-row :deep(.el-table) {
  border-bottom: none;
}

.fixed-first-row :deep(.el-table__header-wrapper) {
  background-color: #f5f7fa;
}

.fixed-first-row :deep(.el-table__body-wrapper) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.fixed-first-row :deep(.el-table__row) {
  background-color: #f5f7fa !important;
}

.fixed-first-row :deep(.el-table__header th) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.main-table {
  margin-top: 0;
  margin-bottom: 0;
  overflow: auto;
}

.main-table :deep(.el-table) {
  border-top: none;
  border-bottom: none;
}

.main-table :deep(.el-table__body-wrapper) {
  border-top: none;
  border-bottom: none;
}

.fixed-last-row {
  position: relative;
  z-index: 10;
  margin-top: 0;
}

.fixed-last-row :deep(.el-table) {
  border-top: none;
}

.fixed-last-row :deep(.el-table__body-wrapper) {
  background-color: #f0f9ff;
  border-top: 1px solid #ebeef5;
}

.fixed-last-row :deep(.el-table__row) {
  background-color: #f0f9ff !important;
  font-weight: bold;
}

/* 确保固定列的样式 */
:deep(.el-table__fixed-left) {
  z-index: 11;
}

:deep(.el-table__fixed-right) {
  z-index: 11;
}

/* 表格边框优化 */
.lui-table {
  border-collapse: separate;
  border-spacing: 0;
}

.lui-table :deep(.el-table__cell) {
  border-right: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  padding: 10px 0 !important;
}

/* 确保表格内容可见 */
.lui-table :deep(.el-table__body) {
  background-color: white;
}

.lui-table :deep(.el-table__row) {
  background-color: white;
}

.lui-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 修复表格连接处的边框 */
.fixed-first-row :deep(.el-table__body tr:last-child td) {
  border-bottom: 1px solid #ebeef5;
}

.main-table :deep(.el-table__body tr:first-child td) {
  border-top: none;
}

.main-table :deep(.el-table__body tr:last-child td) {
  border-bottom: 1px solid #ebeef5;
}

.fixed-last-row :deep(.el-table__body tr:first-child td) {
  border-top: none;
}

/* 固定列阴影效果 */
:deep(.el-table__fixed-left::before) {
  box-shadow: 1px 0 8px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式优化 */
.main-table :deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.main-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

.main-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

.main-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

.main-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .fixed-first-row,
  .main-table,
  .fixed-last-row {
    font-size: 12px;
  }
}

</style>
