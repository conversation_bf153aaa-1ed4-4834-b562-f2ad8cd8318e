<template>
  <div style="width: 100%" v-loading="loading">
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
      label-width="160px" size="default" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;
        border-bottom: 3px solid #2A96F9;padding-left: 10px;padding-right: 10px;text-align: center;padding-bottom: 2px">
        技术分打分
      </div>

      <!-- 统一表格，固定表头和最后一行 -->
      <el-table
        :data="formData.PSXXList"
        border
        class="lui-table fixed-header-last-row-table"
        :highlight-current-row="true"
        size="default"
        :cell-style="getCellStyle"
        height="calc(100vh - 535px)">
        <el-table-column type="index" width="50" fixed="left" label="" align="center"/>
        <el-table-column prop="PJYS" header-align="center" align="center" width="150" label="评分因素" fixed="left"></el-table-column>
        <el-table-column prop="PJFZ" header-align="center" align="center" width="150" label="分值" fixed="left"></el-table-column>
        <el-table-column prop="SFGDFZ" header-align="center" align="center" width="120" label="是否固定分值" fixed="left"></el-table-column>
        <el-table-column prop="PJBZ" header-align="center" align="center" label="评分标准" fixed="left" min-width="200"></el-table-column>
        <el-table-column v-for="item in formData.TBRList" :key="item.TBBSID" :property="item.TBBSID" header-align="center" align="center" :label="item.DWMC" width="150">
          <template #default="{row, $index}">
            <el-input v-on:input="row[item.TBBSID] = row[item.TBBSID].replace(/[^\d.]/g, ''), countDf(item.TBBSID)"
                v-if="editable && row.type!=='HJ'" v-model="row[item.TBBSID]"></el-input>
            <span v-else :class="{ 'total-text': $index === formData.PSXXList.length - 1 }">{{row[item.TBBSID]}}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;" v-if="editable">
      <el-button size="default" type="primary" @click="saveData('submit')">保存</el-button>
      <el-button size="default" @click="close"> 关闭 </el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: false,
      // editable: props.parentForm.JSDF_WCZT !== '1',
      editable: true,
      formData: {
        PSXXList: [
          {PJBZ: '测试标准', type: 'SJ'},
          {PJBZ: '测试标准1', type: 'SJ'},
          {PJBZ: '测试标准2', type: 'SJ'},
          {PJBZ: '测试标准3', type: 'SJ'},
          {PJBZ: '测试标准4', type: 'SJ'},
          {PJBZ: '测试标准5', type: 'SJ'},
          {PJBZ: '测试标准6', type: 'SJ'},
          {PJBZ: '测试标准7', type: 'SJ'},
          {PJBZ: '测试标准8', type: 'SJ'},
          {PJBZ: '测试标准9', type: 'SJ'},
          {PJBZ: '测试标准10', type: 'SJ'},
        ],
        TBRList: [
          {TBBSID: '123', DWMC: '测试队伍'},
          {TBBSID: '1231', DWMC: '测试队伍1'},
          {TBBSID: '1232', DWMC: '测试队伍2'}
        ]
      },
      rules: {}
    })

    const getFormData = () => {
      computedRes()
    }

    const computedRes = () => {
      let HJRow = {PJYS: '技术分得分合计', PJFZ: '100', type: 'HJ'}

      let pushRow = state.formData.PSXXList.find(item => item.type === 'HJ')
      if (pushRow) {
        Object.assign(pushRow, HJRow)
      } else {
        state.formData.PSXXList.push(HJRow)
      }

      state.formData.TBRList.forEach(item => {
        countDf(item.TBBSID)
      })
    }

    const close = () => {
      emit('closeDialog');
    }

    const saveData = (type) => {
      console.log("111111111111111111111111111111111111", state.formData.TBRList)
    }

    const viewRow = () => {

    }

    const countDf = (val) => {
      let sum=0
      state.formData.PSXXList.forEach(item=>{
        if(item.type==='SJ'){
          sum+=parseFloat(item[val] || 0)
        }
      })
      let HJRow = state.formData.PSXXList.find(item => item.type === 'HJ')
      if (HJRow) {
        HJRow[val] = sum
      }
    }

    // 获取单元格样式
    const getCellStyle = ({ row, rowIndex }) => {
      const baseStyle = { padding: '10px 0' }

      // 最后一行（合计行）的样式
      if (rowIndex === state.formData.PSXXList.length - 1) {
        return {
          ...baseStyle,
          backgroundColor: '#f0f9ff',
          fontWeight: 'bold'
        }
      }

      return baseStyle
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      getFormData,
      saveData,
      viewRow,
      countDf,
      getCellStyle,
      close
    }
  }
})

</script>

<style scoped>
.button-PS {
  width: 80px;
  height: 30px;
  border: 1px solid #8c939d;
  border-radius: 5px;
  cursor: pointer;
  line-height: 30px;
}

.color-HG {
  background-color: #99cdfd;
  color: black;
}

.color-BHG {
  background-color: #fff3dc;
  color: #bb7b00;
}

.greenStyle {
  color: #5eb416;
}

.redStyle {
  color: #ff0000;
}

.blueStyle {
  color: #09d7ff;
}

/* 合计行文本样式 */
.total-text {
  font-weight: bold;
  color: #409eff;
}

/* 固定表头和最后一行的表格样式 */
.fixed-header-last-row-table {
  position: relative;
}

/* 固定表头 */
.fixed-header-last-row-table :deep(.el-table__header-wrapper) {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f5f7fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.fixed-header-last-row-table :deep(.el-table__header th) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

/* 固定最后一行 */
.fixed-header-last-row-table :deep(.el-table__body tr:last-child) {
  position: sticky;
  bottom: 0;
  z-index: 9;
  background-color: #f0f9ff;
  font-weight: bold;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.fixed-header-last-row-table :deep(.el-table__body tr:last-child td) {
  background-color: #f0f9ff !important;
  font-weight: bold;
  border-top: 2px solid #409eff;
}

/* 确保固定列的样式 */
:deep(.el-table__fixed-left) {
  z-index: 11;
}

:deep(.el-table__fixed-right) {
  z-index: 11;
}

/* 表格边框优化 */
.lui-table {
  border-collapse: separate;
  border-spacing: 0;
}

.lui-table :deep(.el-table__cell) {
  border-right: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  padding: 10px 0 !important;
}

/* 确保表格内容可见 */
.lui-table :deep(.el-table__body) {
  background-color: white;
}

.lui-table :deep(.el-table__row) {
  background-color: white;
}

.lui-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 修复表格连接处的边框 */
.fixed-first-row :deep(.el-table__body tr:last-child td) {
  border-bottom: 1px solid #ebeef5;
}

.main-table :deep(.el-table__body tr:first-child td) {
  border-top: none;
}

.main-table :deep(.el-table__body tr:last-child td) {
  border-bottom: 1px solid #ebeef5;
}

.fixed-last-row :deep(.el-table__body tr:first-child td) {
  border-top: none;
}

/* 固定列阴影效果 */
:deep(.el-table__fixed-left::before) {
  box-shadow: 1px 0 8px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式优化 */
.main-table :deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.main-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

.main-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

.main-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

.main-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .fixed-first-row,
  .main-table,
  .fixed-last-row {
    font-size: 12px;
  }
}

</style>
