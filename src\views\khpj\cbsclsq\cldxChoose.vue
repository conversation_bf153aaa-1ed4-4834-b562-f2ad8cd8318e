<template>
  <div>
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="QYMC">
            <el-input ref="input45296" placeholder="请输入企业名称" v-model="listQuery.QYMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell" v-if="CLDXLX==='DW'">
          <el-form-item label="" prop="DWMC">
            <el-input ref="input45296" placeholder="请输入队伍名称" v-model="listQuery.DWMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell" v-if="CLDXLX==='RY'">
          <el-form-item label="" prop="XM">
            <el-input ref="input45296" placeholder="请输入姓名" v-model="listQuery.XM" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="400px" @current-change="handleCurrentChange"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="QYMC" label="承包商名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="TYXYDM" label="统一信用代码" align="center" v-if="['DW','QY'].includes(CLDXLX)"
                               :show-overflow-tooltip="true" width="200"></el-table-column>
              <el-table-column prop="DWMC" label="队伍名称" align="center" v-if="CLDXLX==='DW'"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="RYXM" label="人员姓名" align="center" v-if="CLDXLX==='RY'"
                               :show-overflow-tooltip="true" width="110"></el-table-column>
              <el-table-column prop="SFZH" label="身份证号" align="center" v-if="CLDXLX==='RY'"
                               :show-overflow-tooltip="true" width="150"></el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    CLDXLX: {
      type: String,
      required: true
    },
    LX: {
      type: String,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      checkRow: null,
    })

    const getDataList = () => {
      let params={
        ...state.listQuery,
        CLDXLX: props.CLDXLX,
        LX: props.LX
      }
      axiosUtil.get('/backend/sckhpj/cbsclsq/selectDxcldX',params).then(res=>{
        state.tableData = res.data.list || []
        state.total = res.data.total
      })

    }

    const saveData = () => {
      if(state.checkRow){
        emit('submit',state.checkRow)
      }else {
        ElMessage.warning('请选择处理对象')
      }
    }

    const handleCurrentChange = (val) => {
      state.checkRow=val
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      closeForm,
      handleCurrentChange,
      saveData

    }
  }

})
</script>

<style scoped>

</style>
