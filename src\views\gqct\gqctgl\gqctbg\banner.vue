<template>
  <div class="banner">
    <div class="banner-icon">
      <img class="banner-img" src="@static/img/login_logo.png">
      <div style="text-align: center">
        <div style="font-size: 3mm;font-family: 宋体">中国石化</div>
        <div style="font-size: 2mm">SINOPEC</div>
      </div>
    </div>
    <div class="banner-text">
      西北油田市场管理信息系统
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, onMounted} from "vue";


export default defineComponent({
  name: '',
  components: {},
  props: {},
  setup(props, {emit}) {
    const state = reactive({})
    onMounted(() => {

    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>
.banner{
  height: 12mm;
  width: 210mm;
  /*background-color: #8c939d;*/
  display: flex;
  align-items: center;
  color: black;
  border: 1px solid #e0e3e8;
  border-bottom: 1px solid black;
}
.banner-icon{
  display: flex;
  align-items: center;
  gap: 1mm;
  font-weight: bolder;
  background-color: rgb(226, 230, 234);
  margin-left: 10%;
  padding-right: 2mm;
}
.banner-img{
  width: 8mm;
  height: 8mm;
}
.banner-text{
  font-size: 4mm;
  height: 8mm;
  line-height: 8mm;
  width: 75%;
  text-align: center;
  background-color: #e3dfd9;
  border: 1px solid #cec0a5;
}
</style>
