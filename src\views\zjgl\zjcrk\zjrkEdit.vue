<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="left" label-width="150px"
           size="default" v-loading="loading">
    <el-collapse v-model="openCollapse">
      <jbxx-card v-model:form-data="formData" :params="params"/>
      <el-collapse-item title="专业信息" name="1">
        <zyxx-card v-model:form-data="formData" :params="params"/>
      </el-collapse-item>
      <el-collapse-item title="扩展信息" name="2">
        <kzxx-card v-model:form-data="formData" :params="params"/>
      </el-collapse-item>
    </el-collapse>
     <div style="display:flex;justify-content: center;gap: 20px;margin-top: 20px" v-if="editable">
      <div class="static-content-item">
        <el-button type="success" @click="saveForm('save')">暂存</el-button>
      </div>
      <div class="static-content-item">
        <el-button type="primary" @click="saveForm('submit')">提交</el-button>
      </div>
      <div class="static-content-item">
        <el-button @click="closePage">返回</el-button>
      </div>
    </div>




    <el-dialog z-index="1000" custom-class="lui-dialog" v-model="inputPasswordShow" title="设置密码" width="300px"
               v-if="inputPasswordShow">
      <div>
        <el-input v-model="formData.DZQMMM" type="password"
                  placeholder="请输入密码"
                  show-password clearable></el-input>
      </div>
      <template #footer>
        <el-button type="primary" @click="inputPasswordShow = false">
          关闭
        </el-button>
      </template>
    </el-dialog>

  </el-form>

</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted, watch
}
  from 'vue'
import axiosUtil from "../../../lib/axiosUtil";
import {Plus} from '@element-plus/icons-vue'
import comFun from "../../../lib/comFun";
import vsAuth from "../../../lib/vsAuth";
import {ElMessage, ElMessageBox} from 'element-plus'


import KzxxCard from "@views/zjgl/zjcrk/compoents/kzxxCard";
import LlxxCard from "@views/zjgl/zjcrk/compoents/llxxCard";
import zyxxCard from "@views/zjgl/zjcrk/compoents/zyxxCard";
import jbxxCard from "@views/zjgl/zjcrk/compoents/jbxxCard";

export default defineComponent({
  components: {LlxxCard, KzxxCard,zyxxCard,jbxxCard},
  props: {
    params: {
        type: Object,
        required: true
    },
    value: {
        type: Object,
        default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: false,
      openCollapse: ['1', '2'],
      userInfo: vsAuth.getAuthInfo().permission,
      Plus: Plus,
      loading: false,
      GLZJID: null,
      formData: {
        XM: null,
        XB: null,
        SFZH: null,
        SJH: null,
        GZDW: null,
        XL: null,

        DZQMMM: null,
        XCSZYBM: null,
        CSZYGZSJ: null,
        ZJLX: 'NB',
        SBZYMX: [],

        ZJLB: null,
        ZJJB: null,

        RKZJBXX: {},

        tableData1: [],
        tableData2: [{LX: '专家工作经历证明', WJMC: ''}],

      },
      rules: {
        XM: [{
          required: true,
          message: '字段值不可为空',
        }],
        XB: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZJLB: [{
          required: true,
          message: '字段值不可为空',
        }],
        GZDW: [{
          required: true,
          message: '字段值不可为空',
        }],
        "RKZJBXX.SBZY": [{
          required: true,
          message: '字段值不可为空',
        }],
        // 'RKZJBXX.CSNY': [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        XL: [{
          required: false,
          message: '字段值不可为空',
        }],
        RYZH: [{
          required: true,
          validator: async (rule, value, callback) => {
            if (!value) {
              callback(new Error('字段值不可为空'))
            } else {
              let isCF = await checkZh(value)
              if(Boolean(isCF.data)){
                callback()
              }else {
                callback(new Error('该账号已经准入，不能重复准入'))
              }
            }
          },
        }],
        // CSZYGZSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJLX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // SBZY: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJLB: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        ZJJB: [{
          required: true,
          message: '字段值不可为空',
        }],
        QY: [{
          required: true,
          message: '字段值不可为空',
        }],
        // ZJBH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZC: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BGDH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZZDH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // DZYX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // TXDZ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // MQSZD: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BYYX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // SXZY: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BYSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // CJGZSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZYZG: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZYZGZS: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZZZT: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // XZZW: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // pictureupload90411: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // pictureupload40039: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
      },
      // XBOptions: [],
      // XLOptions: [],
      // XCSZYOptions: [],
      // ZJLXOptions: [],
      // ZJLBOptions: [],
      // ZJJBOptions: [],
      // ZCOptions: [],
      // ZZZTOptions: [],
      // ZYZCOptions: [],

      fileTableData: [],

      inputPasswordShow: false

    })
    const getFormDate = (GLZJMXID) => {
      state.loading = true
      axiosUtil.get('/backend/zjgl/zjrkgl/selectRkzj', {GLZJMXID}).then((res) => {
        if (res.data && res.data.length > 0) {
          state.formData = {...state.formData, ...res.data[0]}
        }
        state.loading = false
      })
    }

    const checkZh = (value) => {
      let params={
        GLZJMXID: props.params.id,
        value: value,
      }
      return axiosUtil.get('/backend/zjgl/zjrkgl/checkZh', params)
    }



    const instance = getCurrentInstance()

    const validateForm = (type) => {
                return new Promise((resolve, reject) => {
                    if (type === 'submit') {
                        instance.proxy.$refs['vForm'].validate(valid => {
                            if (valid) {
                                resolve(true)
                                //TODO: 提交表单
                            } else {
                                reject('请完善页面信息')
                            }
                        })
                    } else if (type === 'save') {
                        resolve(true)
                    }
                })
            }

//工作流的保存
            const saveData = (val) => {
                console.log('流程变量', props.value)
                let type = val === '1' ? 'submit' : 'save'
                return new Promise(async (resolve, reject) => {
                    if (props.value.activityId === '1' || props.value.activityId === 'new') {
                        let res
                        try {
                            res = await validateForm(type)
                        } catch (resMsg) {
                            ElMessage.error(resMsg)
                        }
                        if (res) {
                            if (props.value.activityId === '1') {
                                type = 'submit'
                            }
                            state.loading = true
                            let res2 = await submitForm(type)
                            if (res2) {
                                resolve(true)
                            } else {
                                reject('保存失败')
                                return;
                            }
                        } else {
                            reject('校验未通过')
                            return;
                        }
                    }
                    let data = {
                        ...props.value,
                        businessId: props.value.businessId || props.params.id,
                        processInstanceName: state.formData.XM + '-专家入库申请',
                        conditionStr: "lczx=1",
                    };
                    state.loading = false
                    emit("update:value", data);
                    resolve(true)
                })
            }

    const closePage = () => {
      emit('closeDialog')
    }

    const submitForm = (type) => {
          return new Promise((resolve, reject) => {
              let params
              if (props.params.operation === 'add') {
                params = {
                  GLZJID: state.GLZJID,
                  SQSJ: comFun.getNowTime(),
                  CJR: state.userInfo.userLoginName,
                  CJSJ: comFun.getNowTime(),
                  HDFL: 'RK',
                  MX: {
                    ...state.formData,
                    GLZJID: state.GLZJID,
                    ZJBS: comFun.newId()
                  }
                }
              } else if (props.params.operation === 'edit') {
                params = {
                  GLZJID: state.formData.GLZJID,
                  XGR: state.userInfo.userLoginName,
                  XGSJ: comFun.getNowTime(),
                  MX: {
                    ...state.formData,
                  }
                }
              }
              params.SHZT = type === 'submit' ? '1' : '0'
              params.MX.ZJZT = type === 'submit' ? '1' : '0'
              params.MX.ZJBH = params.MX.RKZJBXX.ZJBH
              params.MX.ZC = params.MX.RKZJBXX.ZC
              axiosUtil.post('/backend/zjgl/zjrkgl/saveRkzj', params).then(res => {
                if (res.message === 'success') {
                    resolve(true)
                }
              })
          })
      }

    const saveForm = (type) => {
        let params
        if (props.params.operation === 'add') {
          params = {
            GLZJID: state.GLZJID,
            SQSJ: comFun.getNowTime(),
            CJR: state.userInfo.userLoginName,
            CJSJ: comFun.getNowTime(),
            HDFL: 'RK',
            MX: {
              ...state.formData,
              GLZJID: state.GLZJID,
              ZJBS: comFun.newId()
            }
          }
        } else if (props.params.operation === 'edit') {
          params = {
            GLZJID: state.formData.GLZJID,
            XGR: state.userInfo.userLoginName,
            XGSJ: comFun.getNowTime(),
            MX: {
              ...state.formData,
            }
          }
        }
        params.SHZT = type === 'submit' ? '2' : '0'
        params.MX.ZJZT = type === 'submit' ? '1' : '0'
        params.MX.ZJBH = params.MX.RKZJBXX.ZJBH
        params.MX.ZC = params.MX.RKZJBXX.ZC
        axiosUtil.post('/backend/zjgl/zjrkgl/saveRkzj', params).then(res => {
          if (res.message === 'success') {
            ElMessage({
              message: '保存成功！',
              customClass: "myMessageClass",
              type: 'success',
            })
              if(type === 'submit') {
                  closePage();
              }
          }
        })
    }

    const setPassword = () => {
      state.inputPasswordShow = true
    }
    const resetForm = () => {
      instance.proxy.$refs['vForm'].resetFields()
    }
    const addLLXX = () => {
      state.formData.tableData1.push({
        ZJLLID: comFun.newId(),
        WJID: props.params.id,
        QZRQ: null,
        GZDW: null,
        ZW: null,
        GZJJ: null
      })
    }
    const deleteRow = (index, data) => {
      data.splice(index, 1);
      // RealDelete(data)//对tableData中的数据删除一行
    }
    const closeDialog = () => {
      state.dialogVisible = false
      state.dialogDWXZVisible = false
    }


    const getZJBH = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/generateZJBH', {}).then((res) => {
        state.formData.RKZJBXX.ZJBH = res.data[0].NEWBH
      })
    }
    onMounted(() => {
      // getZYData()
      // getDMBData('XB', 'XBOptions')
      // getDMBData('XL', 'XLOptions')
      // getDMBData('ZJLX', 'ZJLXOptions')
      // getDMBData('ZJLB', 'ZJLBOptions')
      // getDMBData('ZJJB', 'ZJJBOptions')
      // getDMBData('ZC', 'ZCOptions')
      // getDMBData('ZZZT', 'ZZZTOptions')
      // getDMBData('ZYZC', 'ZYZCOptions')

      state.formData.GLZJMXID = props.params.id
      state.editable = props.params.editable
      if (props.params.operation !== 'add') {
        getFormDate(props.params.id)
      } else if (props.params.operation === 'add') {
        state.GLZJID = comFun.newId()
        getZJBH()
      }
    })
    watch(() => state.formData.SBZYMX, (newValue) => {
      let inputShow = []
      newValue.forEach(item => {
        if (item.SFZZY === '1') {
          inputShow.push(item.ZYMC + '(主专业)')
        } else {
          inputShow.push(item.ZYMC)
        }

      })
      state.formData.RKZJBXX.SBZY = inputShow.join('、')
    })
    return {
      ...toRefs(state),
      validateForm,
      submitForm,
      resetForm,
      addLLXX,
      deleteRow,
      closePage,
      closeDialog,
      setPassword,
      saveData,
      saveForm
    }
  }
})

</script>


<style scoped>
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: unset;
}

/deep/ .el-collapse-item__header {
  background-color: #dcdfef !important;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
  font-size: 16px;
  color: black !important;
}
</style>
