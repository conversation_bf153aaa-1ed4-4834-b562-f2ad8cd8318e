<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0" size="default">
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="8" class="grid-cell">
          <div class="card">
            <div class="card-header">
               <span v-for="(item,index) in getResNameList(checkNode.RES_FLOOR_ID)">
                  <span class="resName" @click="resNameClick(index)">{{item}}</span>
                  <span style="margin-right: 3px;margin-left: 3px"
                        v-if="getResNameList(checkNode.RES_FLOOR_ID).length-1!==index">></span>
                </span>
            </div>
            <div class="card-body">
              <div style="height: 100%">
                <el-tree
                    ref="resTree"
                    :expand-on-click-node="false"
                    :data="resTreeList"
                    node-key="RES_ID"
                    :props="{label:'RES_NAME',children: 'children'}"
                    @node-click="checkRes"/>

              </div>
            </div>
          </div>


        </el-col>
        <el-col :span="16" class="grid-cell">
          <div class="static-content-item" style="display: flex;height: 40px">
<!--            <el-button ref="button91277" @click="getDataList" type="primary"><el-icon><Search/></el-icon>查询</el-button>-->
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData"><el-icon><Plus/></el-icon>添加</el-button>
            <el-button ref="button9527" type="primary" @click="saveSort" v-if="sorting"><el-icon><Finished/></el-icon>完成</el-button>
            <el-button ref="button9527" type="primary" @click="reloadPXH" v-else><el-icon><Operation/></el-icon>排序</el-button>
          </div>
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="childResList" height="calc(100vh - 180px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column v-if="true" prop="RES_NAME" label="资源名称" align="left"
                               :show-overflow-tooltip="true" min-width="150"></el-table-column>
              <el-table-column v-if="true" prop="RES_PVALUE" label="资源路径" align="left"
                               :show-overflow-tooltip="true" min-width="150"></el-table-column>
              <el-table-column v-if="sorting" prop="YD" label="移动" align="center"
                               :show-overflow-tooltip="true" width="100">
                <template #default="{row,$index}">
                  <el-icon style="cursor: pointer" @click="moveDown($index)" v-if="$index+1!==childResList.length"><SortDown/></el-icon>
                  <el-icon style="cursor: pointer" @click="moveUp($index)" v-if="$index>0"><SortUp/></el-icon>
                </template>

              </el-table-column>
              <el-table-column v-if="true" prop="RES_ORDER" label="排序号" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column v-if="true" prop="CZ" label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button  size="small" class="lui-table-button"  type="primary" @click="editRow(scope.row)">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>

      <el-dialog
          custom-class="lui-dialog"
          :close-on-click-modal="false"
          v-if="dialogVisible"
          v-model="dialogVisible"
          title="菜单编辑"
          @closed="closeForm"
          z-index="1000"
          width="1200px">
        <div>
          <resEdit v-if="dialogVisible" :params="params" @close="closeForm"></resEdit>
        </div>
      </el-dialog>


    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import {Plus, Operation, SortUp, SortDown,Finished} from '@element-plus/icons-vue'
import resEdit from "./resEdit";
import comFun from "../../../lib/comFun";
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {Plus,resEdit,Operation,SortUp,SortDown,Finished},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      childResList:[],
      resList:[],
      resTreeList:[],
      listQuery: {
        page: 1,
        size: 10
      },
      total: 0,
      params:{},
      rootNode:{
        RES_ID:'0',
        RES_NAME: '根节点',
        RES_FLOOR_NUM: '0',
        RES_FLOOR_ID:'0'
      },
      checkNode:{},
      dialogVisible: false,

      sorting: false

    })
    const instance = getCurrentInstance()

    const getResList = (pid) => {
      let params={
        pid
      }
      axiosUtil.get('/backend/common/cdgl/selectResListByPid', params).then((res) => {
        let data=res.data || []
        if(pid){
          state.sorting=false
          state.childResList=data
        }else {
          state.resList=JSON.parse(JSON.stringify(data))
          state.resTreeList = treeData(data,'RES_ID','RES_PID','children',state.rootNode.RES_ID)
          nextTick(()=>{
            instance.proxy.$refs['resTree'].setCurrentKey(state.checkNode.RES_ID,true)
          })
        }
      });
    }
    const checkRes = (node) => {
      state.checkNode=node
      getResList(node.RES_ID)
    }

    const addData = () => {
      state.params={
        editable: true,
        id: comFun.newId(),
        operation: 'add',
        pNode: state.checkNode,
        PXH: state.childResList.length+1
      }
      state.dialogVisible=true
    }

    const editRow = (row) => {
      state.params={
        editable: true,
        id: row.RES_ID,
        operation: 'edit',
        pNode: state.checkNode
      }
      state.dialogVisible=true
    }

    const getResNameList = (name) => {
      let res=[]
      if(name){
        res=name.split(',')
      }
      for (let i = 0; i < res.length; i++) {
        if(res[i]===state.rootNode.RES_ID){
          res[i]=state.rootNode.RES_NAME
        }else {
          let node=state.resList.find(item=>item.RES_ID===res[i])
          if(node){
            res[i]=node.RES_NAME
          }
        }
      }
      return res
    }

    const resNameClick = (index) => {
      let nodeId=state.checkNode.RES_FLOOR_ID.split(',')[index]
      if(nodeId===state.rootNode.RES_ID){
        state.checkNode=state.rootNode
      }else {
        state.checkNode=state.resList.find(item=>item.RES_ID===nodeId)
      }
      getResList(state.checkNode.RES_ID)
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      state.dialogVisible=false
      getResList()
      getResList(state.checkNode.RES_ID)
    }

    /**
     * 数据转树型结构
     * @param data
     * @param id id名称
     * @param parentId 父id名称
     * @param childName 生成的子节点名称
     * @param root 根节点的值
     * @returns {*}
     */
    const treeData = (data, id, parentId, childName, root) => {
      let cloneData = data
      return cloneData.filter((father) => {
        father[childName] = cloneData.filter((child) => {
          return father[id] === child[parentId]
        })
        return father[parentId] === root
      })
    }

    const saveSort = () => {
      let params={
        PXXXList: state.childResList.map(item=>{
          return{
            RES_ID: item.RES_ID,
            RES_ORDER: item.RES_ORDER
          }
        })
      }
      axiosUtil.post('/backend/common/cdgl/saveZypxxx',params).then(res=>{
        ElMessage.success('保存成功')
        getResList(state.checkNode.RES_ID)
      })

    }

    const moveUp = (index) => {
      let temp = state.childResList[index]
      state.childResList[index] = state.childResList[index - 1]
      state.childResList[index - 1] = temp
      reloadPXH()
    }

    const moveDown = (index) => {
      let temp = state.childResList[index]
      state.childResList[index] = state.childResList[index + 1]
      state.childResList[index + 1] = temp
      reloadPXH()
    }

    const reloadPXH = () => {
      state.sorting=true
      state.childResList.forEach((item,index)=>{
        item.RES_ORDER=index+1
      })
    }

    onMounted(() => {
      state.checkNode=state.rootNode
      getResList()
      getResList(state.rootNode.RES_ID)
    })

    return {
      ...toRefs(state),
      indexMethod,
      checkRes,
      getResNameList,
      closeForm,
      addData,
      editRow,
      resNameClick,
      saveSort,
      moveUp,
      moveDown,
      reloadPXH

    }
  }

})
</script>

<style scoped>
.card{
  box-shadow: rgba(0, 0, 0, 0.12) 0 0 12px;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
}

.card-header{
  padding: 20px;
  border-bottom: 1px solid #eaeaf1;
}
.card-body{
  flex: 1;
  padding: 10px;
  overflow: auto;
}

.resName{
  cursor: pointer;
}
.resName:hover{
  color: #409EFF;
}
</style>
