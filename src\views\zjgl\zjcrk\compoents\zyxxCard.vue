<template>
  <el-row :gutter="0" class="grid-row">
    <el-col :span="8" class="grid-cell">
      <el-form-item label="现从事专业：" prop="XCSZYBM">
        <el-cascader
            @change="changeCSZY"
            clearable
            v-model="formData.XCSZYBM"
            :options="XCSZYOptions"
            :disabled="!editable"
            :show-all-levels="false"
            :props="{expandTrigger: 'hover',label:'<PERSON>Y<PERSON>',value:'<PERSON>Y<PERSON>',emitPath:false}"
        />
      </el-form-item>
    </el-col>
    <el-col :span="16" class="grid-cell">
      <el-form-item label="从事专业工作时间：" prop="CSZYGZSJ" label-width="150px">
        <el-date-picker v-model="formData.CSZYGZSJ" type="date" class="full-width-input" format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" :disabled="!editable" clearable
                        :editable="false"></el-date-picker>
      </el-form-item>
    </el-col>

<!--    <el-col :span="8" class="grid-cell">-->
<!--      <el-form-item label="专家类型：" prop="ZJLX">-->
<!--        <el-radio-group v-model="formData.ZJLX" :disabled="!editable">-->
<!--          <el-radio v-for="(item, index) in ZJLXOptions" :key="index" :label="item.DMXX"-->
<!--                    style="{display: inline}">{{ item.DMMC }}-->
<!--          </el-radio>-->
<!--        </el-radio-group>-->
<!--      </el-form-item>-->
<!--    </el-col>-->

    <!--          <el-col :span="8" class="grid-cell" v-if="formData.ZJLB==='JS'">-->
    <!--            <el-form-item label="专家级别：" prop="ZJJB">-->
    <!--              <el-select v-model="formData.ZJJB" class="full-width-input" :disabled="!editable" clearable>-->
    <!--                <el-option v-for="(item, index) in ZYZCOptions" :key="index" :label="item.DMMC"-->
    <!--                           :value="item.DMXX" :disabled="item.disabled"></el-option>-->
    <!--              </el-select>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <el-col :span="24" class="grid-cell">
      <el-form-item label="申报专业：" prop="RKZJBXX.SBZY">
        <el-input v-model="formData.RKZJBXX.SBZY" :disabled="true" type="text" clearable>
          <template #append v-if="editable">
            <el-button @click="dialogVisible=true" plain :disabled="!editable">选择</el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="专家类别：" prop="ZJLB">
        <el-select v-model="formData.ZJLB" class="full-width-input" :disabled="!editable" clearable>
          <el-option v-for="(item, index) in ZJLBOptions" :key="index" :label="item.DMMC"
                     :value="item.DMXX" :disabled="item.disabled"></el-option>
        </el-select>
      </el-form-item>
    </el-col>

    <el-col :span="8" class="grid-cell">
      <el-form-item label="专家区域：" prop="QY">
        <el-select v-model="formData.QY" class="full-width-input" :disabled="!editable" clearable>
          <el-option v-for="(item, index) in ZJQYOptions" :key="index" :label="item.DMMC"
                     :value="item.DMXX" :disabled="item.disabled"></el-option>
        </el-select>
      </el-form-item>
    </el-col>
    <!--          <el-col :span="8" class="grid-cell">-->
    <!--            <el-form-item label="专家级别：" prop="ZJJB" >-->
    <!--              <el-select v-model="formData.ZJJB" class="full-width-input" :disabled="!editable" clearable>-->
    <!--                <el-option v-for="(item, index) in ZJJBOptions" :key="index" :label="item.DMMC"-->
    <!--                           :value="item.DMXX" :disabled="item.disabled"></el-option>-->
    <!--              </el-select>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--          <el-col :span="8" class="grid-cell">-->
    <!--            &nbsp;-->
    <!--          </el-col>-->
  </el-row>

  <el-dialog z-index="1000" custom-class="lui-dialog" v-model="dialogVisible" title="申报专业选择" width="800px"
             class="dialogClass"
             append-to-body>
    <zyxz v-if="dialogVisible" @close="dialogVisible=false" @parentMethod="getCheck"></zyxz>
  </el-dialog>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import Zyxz from "@views/zjgl/zjcrk/common/zyxz";


export default defineComponent({
  name: '',
  components: {Zyxz},
  props: {
    formData:{
      required:true,
      type: Object
    },
    params:{
      type: Object
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: props.params.editable,
      XCSZYOptions: [],
      ZJLXOptions: [],
      ZJLBOptions: [],
      ZJQYOptions: [],
      dialogVisible: false
    })

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }
    const getZYData = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy', {}).then((res) => {
        state.XCSZYOptions = comFun.treeData(res.data, 'ZYBM', 'FZYBM', 'children', '0')
      });
    }
    const getCheck = (e, ZZY) => {
      console.log(e, ZZY)
      let res = []
      e.forEach(item => {
        res.push({
          ZJXGZYBS: comFun.newId(),
          WJID: props.params.id,
          SFZZY: item.ZYBM === ZZY ? '1' : '0',
          ZYBM: item.ZYBM,
          ZYMC: item.ZYMC
        })
      })
      props.formData.SBZYMX = res
      state.dialogVisible = false

    }

    const changeCSZY = () => {
      props.formData.XCSZYMC=state.XCSZYOptions.find(item=>item.ZYBM===props.formData.XCSZYBM)?.ZYMC
    }

    onMounted(() => {
      getZYData()
      getDMBData('ZJLX', 'ZJLXOptions')
      getDMBData('ZJLB', 'ZJLBOptions')
      getDMBData('ZJQY', 'ZJQYOptions')
    })

    return {
      ...toRefs(state),
      getCheck,
      changeCSZY

    }
  }

})
</script>

<style scoped>

</style>
