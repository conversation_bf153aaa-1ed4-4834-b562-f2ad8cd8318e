<template>
  <el-form :model="formData" ref="vForm" class="lui-card-form" label-position="right" label-width="165px"
           size="default" @submit.prevent>
    <el-row :gutter="0" class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="标题：" prop="BT">
          <el-input
              v-model="formData.BT"
              placeholder="请输入"
              :disabled="isView"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="grid-cell">
        <el-form-item label="内容：" prop="GZRWNR" :rules="[{ required: true }]">
          <newQuillEditor v-model="formData.GZRWNR" :editable="true"/>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
        <el-form-item label="相关附件：">
          <vsfileupload style="margin-left: 10px" :key="XXFBID" :editable="pageFlag!='view'" :busId="XXFBID" :showTip="false" :maxSize="maxSize"
                        :accept="accept"></vsfileupload>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
        <el-form-item label="信息类型：" prop="GZLX" :rules="[{ required: true }]">
          <el-select v-model="formData.GZLX" class="full-width-input"
                     clearable>
            <el-option v-for="(item, index) in GZLXOptions" :key="index" :label="item.DMMC"
                       :value="item.DMXX" :disabled="item.disabled"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="发布时间：">
          <el-date-picker
              :disabled="true"
              v-model="formData.WHSJ"
              type="date"
              placeholder="选择"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="发布单位：">
          <el-input v-model="formData.ORGNA_NAME" :disabled="true"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="!isView" style="width:100%;text-align: center;margin-top: 20px">
      <el-col :span="24">
        <el-button type="success" @click="save">保存</el-button>
        <el-button type="warning" @click="closeForm">返回</el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted
}
  from 'vue'
import axiosUtil from "@src/lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import {ElMessage, ElMessageBox} from 'element-plus';
import Vsfileupload from "../../components/vsfileupload";

import Editor from '../Editor/index.vue';
import comFun from '@src/lib/comFun.js';
import newQuillEditor from "@views/components/newQuillEditor";


export default defineComponent({
  components: {Editor, Vsfileupload,newQuillEditor},
  props: {
    params: {
      type: Object,
      required: true
    },
    XXFBID: {
      type: String,
      required: true
    },
    GZLX: {
      type: String,
      required: true
    },
    pageFlag: {
      type: String,
      required: true
    }

  },

  setup(props, context) {
    const state = reactive({
      accept: '.pdf,.PDF,.jpj,.jpej,.png,.PNG,.JPG,.JPEG,.gif,.GIF,.xls,.xlsx,.doc,.docx',
      maxSize: '2048',
      userInfo: vsAuth.getAuthInfo().permission,
      formData: {
        BT: "",
        GZRWNR: "",
        ZT: "0",
        XXFBID: props.XXFBID,
        WHR: vsAuth.getAuthInfo().permission.userId,
        WHSJ: null,
        ORGNA_NAME: vsAuth.getAuthInfo().permission.orgnaName,
        SFXYFK: "0",
        QSBM: null,
      },

      formSend: {
        XXFBID: "", //人员id
        currentPage: 1,
        pageSize: 10,
      },
      isView: false,
      qsbmArr: [], //签收部门
      ywbmArr: [], //业务部门
      ejdwArr: [], //二级单位
      checkAllYwbm: false,
      checkAllEj: false,
      // 富文本相关---------------
      indexFwb: 0,
      viewAble: false,
      emailForm: {
        msg: ""
      },
      fileUploadData: {
        busType: "demo",
        busId: props.XXFBID,
        standbyField0: "file_fwb_zp",
      },
      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显示
      content: null,
      GZLXOptions:[],
      header: {
        // token: sessionStorage.token
      }, // 有的图片服务器要求请求头需要有token
    })


    const emailForm = reactive({
      test_msg: ''
    })
    const getMsg = (val) => {
      state.formData.GZRWNR = val
    }
    // 关闭窗口
    const closeForm = () => {
      context.emit("closeForm")
    }

    // 获取组织结构数据
    const getDictData = async () => {
      var res = await axiosUtil.get('/backend/ztb/dict_organzation')
      state.TJDWList = res.data
    }

    // 查询
    const getFormData = () => {
      let params = {
        XXFBID: props.XXFBID
      }
      axiosUtil.get('/backend/xxfb/common/selectGzxfList', params).then((res) => {
        if (res.data.list.length > 0) {
          state.formData = {...res.data.list[0]}
          state.XSFS = res.data.list[0].XSFS
        }
      });

    }

    //获取Vue实例对象
    const instance = getCurrentInstance()
    const save = async () => {
      state.formData.WHSJ = comFun.getNowTime();
      axiosUtil.post('/backend/xxfb/common/saveXxfb', state.formData).then(res => {
        console.log('保存结果', res);
        if (res.message == "success") {
          ElMessage({message: '保存成功', type: 'success',})
          closeForm();
        }
      })

    }

    // 关闭专业选择
    const closeZYDialog = () => {
      state.ZYDialogVisible = false
    }

    // 获取码表数据
    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      var res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }

    // 获取代码表字段
    const getDMBDataBY = async (DMLBID, resList, BYZD1) => {
      let params = {
        DMLBID
      }
      var res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data.filter((item) => {
        if (item.BYZD1 == BYZD1) {
          return item
        }
      })
    }


    // 专业查询
    const getZyData = () => {
      axiosUtil.get('/backend/yjpj/pjmbgl/selectZylx').then((res) => {
        state.ysZyList = res.data;
        var treeData = transData(res.data, 'ZYBM', 'FZYBM', 'children');
        state.zyOptions = treeData
      });
    }


    /**
     * json格式转树状结构
     * @param   {json}      json数据
     * @param   {String}    id的字符串
     * @param   {String}    父id的字符串
     * @param   {String}    children的字符串
     * @return  {Array}     数组
     */
    const transData = (a, idStr, pidStr, childrenStr) => {
      var r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
      for (; i < len; i++) {
        a[i]['isparent'] = false;
        hash[a[i][id]] = a[i];
      }
      for (; j < len; j++) {
        var aVal = a[j], hashVP = hash[aVal[pid]];
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = []);
          hashVP[children].push(aVal);
        } else {
          r.push(aVal);
        }
      }
      //this.setIsParent(r);
      return r;
    }


    onMounted(() => {
      let params = props.params
      console.log(params)
      if (props.pageFlag == 'edit' || props.pageFlag == 'view') {
        getFormData();
      }
      state.formData.WHSJ = comFun.getNowTime();
      // getDictData();
      getDMBData("GZLX", "GZLXOptions");
      // getZyData();
    })

    return {
      ...toRefs(state),
      getFormData,
      getMsg,
      closeForm,
      closeZYDialog,
      save,
    }
  }
})
</script>

<style scoped>

</style>