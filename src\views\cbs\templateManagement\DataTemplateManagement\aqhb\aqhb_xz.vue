<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        class="lui-page"
        :model="state"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        v-loading="state.loading"
    >
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="userName">
            <el-input ref="input45296" placeholder="名称" @input="getDataList"
                      v-model="state.listQuery.MC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="1" class="grid-cell" style="margin-left: auto">
          <el-button type="primary" class="lui-button-add" @click="addRow"><el-icon><Plus/></el-icon>添加</el-button>
        </el-col>
      </el-row>
      <el-table
          highlight-current-row
          size="default"
          ref="table"
          height="50vh"
          fit
          class="lui-table"
          :border="false"
          :data="state.tableData"
      >
        <EleProTableColumn
            v-for="prop in state.tableColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #fileList="{ row, $index }">
            <vsfileupload
                :maxSize="10"
                @getFileList="getFileList"
                :index="$index"
                :editable="row.edit+''==='true'"
                :busId="row.AQHBZSJID"
                :key="row.AQHBZSJID"
                ywlb="ZDFJ"
                busType="dwxx"
                :limit="100"
            ></vsfileupload>
          </template>
          <template #input="{ row, $index }">
            <el-form-item
                v-if="$index > -1"
                :prop="`tableData.${$index}.${prop.prop}`"
                :rules="[
                {
                  required: prop.required,
                  message: `请输入${prop.label}`,
                  trigger: 'blur',
                },
                {
                  max: prop.maxlength,
                  message: `最多输入${prop.maxlength}个字符`,
                  trigger: 'change',
                },
              ]"
            >
              <el-input
                  v-if="row.edit"
                  v-model="row[prop.prop]"
                  :maxlength="prop.maxlength"
                  placeholder="请输入"
              ></el-input>
              <div v-else>{{ row[prop.prop] }}</div>
            </el-form-item>
          </template>
          <template #opration="{ row, $index }">
            <div v-if="row.edit">
              <el-button class="lui-table-button" @click="saveRow(row,$index)" :loading="state.saving.has('S'+$index)">
                <el-icon><Select/></el-icon>保存
              </el-button>

            </div>
            <div v-else>
              <el-button class="lui-table-button" @click="chooseData(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="editRow(row, $index)">编辑</el-button>
              <el-button class="lui-table-button" @click="delRow(row, $index)">删除</el-button>
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-form>
  </div>
</template>
<script setup>
import { defineProps, reactive, watch, ref ,defineEmits, onMounted} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";

import { v4 as uuidv4 } from "uuid";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import axiosUtil from "../../../../../lib/axiosUtil";
import {auth} from "../../../../../assets/core";
import { Search, Select, Plus} from '@element-plus/icons-vue'
import comFun from "../../../../../lib/comFun";

const getFileList = (res) => {
  state.tableData[res.index].fileList = res.fileList;
};
const props = defineProps({
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  }
});

const addRow = () => {
  const userinfo = auth.getPermission();
  let userParams={
    CJRZH: props.TYXYDM,
    CJRXM: userinfo.userName,
    CJDWID: userinfo.orgnaId,
    CJSJ: comFun.getNowTime(),
    SHZT: '1',
  }
  let params={
    edit: true,
    AQHBZSJID: comFun.newId(),
    ...userParams,
  }
  state.tableData.push(params)
}

const editRow = (row) => {
  row.edit=true
}

const saveRow = (row,index) => {
  let fields=['ZDMC']
  let props=[]
  fields.forEach(item=>{
    props.push(`tableData.${index}.${item}`)
  })
  vForm.value.validateField(props).then(res=>{
    state.saving.add('S'+index)
    let params={
      ...row,
      XGRZH: userinfo.userLoginName,
      XGSJ: comFun.getNowTime(),
    }
    axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveAqhbXxx',{tableData: [params]}).then(r=>{
      row.edit=false
      state.saving.delete('S'+index)
      ElMessage.success('保存成功')
      emit('updateEditData',row)
    })

  }).catch(msg=>{
    console.log(msg)
  })
  const userinfo = auth.getPermission();

}

const delRow = (row, index) => {
  ElMessageBox.confirm(
      '确定删除该条数据?',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(() => {
    let params={
      AQHBZSJID: row.AQHBZSJID,
      SHZT: '2'
    }
    axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveAqhbXxx',{tableData: [params]}).then(r=>{
      state.tableData.splice(index,1)
      ElMessage.success('删除成功')
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除',
    })
  })
}

const state = reactive({
  tableData: [],
  listQuery:{},
  saving: new Set(),
  loading: false,
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    {
      label: "名称",
      prop: "MC",
      align: "center",
      // width: 250,
      maxlength: 128,
      slot: "input",
    },
    {
      label: "附件",
      prop: "fileList",
      headerAlign: "center",
      align: "left",
      slot: "fileList",
      required: true,
      // width: 200,
    },
    {
      label: "操作",
      align: "center",
      width: 150,
      fixed: "right",
      slot: "opration",
      hide: !props.editable
    },
  ],
});
const emit=defineEmits(['updateChooseData','updateEditData'])
const chooseData = (row, index) => {
  emit('updateChooseData',row)
};

const getDataList = () => {
  const userinfo = auth.getPermission();
  let params = {
    ...state.listQuery,
    orgId: userinfo.orgnaId,
    TYXYDM: props.TYXYDM,
  }
  state.loading = true
  axiosUtil.get('/backend/sccbsgl/cbsyj/ytxxx/selectAqhbXxx', params).then((res) => {
    state.tableData = res.data || []
    state.loading = false
  })
}

onMounted(()=>{
  getDataList()
})
const vForm = ref(null);
const validateForm = () => {
  return vForm.value.validate();
};
defineExpose({
  validateForm,
});
</script>
<style scoped>
:deep(.el-table-fixed-column--right) {
  background-color: rgba(255, 255, 255, 1) !important;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form-item__error) {
  top: 50%;
  transform: translateY(-50%);
  left: 40px;
}
</style>
