<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CBSMC">
            <el-input ref="input45296" placeholder="请输入承包商名称" v-model="listQuery.CBSMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="DWMC" label="承包商名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="ZYFLMC" label="准入专业" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                  <span v-if="!scope.row.STATUS">
                    <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑
                    </el-button>
                  </span>
                  <span v-else>
                    <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                    </el-button>
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import vsAuth from "@lib/vsAuth";
import cbsblEdit from "@views/cbs/cbsbl/cbsblEdit.vue"
import TabFun from "@lib/tabFun";
import {getTeamreslutGetProDetails} from "@src/api/sccbsgl";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        userLoginName: vsAuth.getAuthInfo().permission.userLoginName
        // ==='tanjunhao' ? null : vsAuth.getAuthInfo().permission.userLoginName
      }
      axiosUtil.get('/backend/cbsxx/cbsbl/selectCbsblPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const viewRow = (row) => {
      const {DWYWID, DWLX,ZYFLDM, EXTENSION,DWMC,MBLX} = row;
      let ex = JSON.parse(EXTENSION)
      TabFun.addTabByCustomName(
          DWMC,
          'cbsjbxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
          cbsblEdit,
          {
            uuId: DWYWID, //队伍业务ID
            MBID: ex?ex.MBID:'', //模板ID
            MBLX: MBLX, //模板类型、
            ZYFLDM: ZYFLDM, //专业分类代码
            YWLXDM: "ZR", //业务类型代码
            editable: false,//是否查看,
            isVIewJgxx: false,
            from:'CBSBL'
          },
          {}
      );
    }


    const editRow = (row) => {
      const {DWYWID, DWLX,ZYFLDM, EXTENSION,DWMC,MBLX} = row;
      let ex = JSON.parse(EXTENSION)
      TabFun.addTabByCustomName(
          DWMC,
          'cbsjbxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
          cbsblEdit,
          {
            uuId: DWYWID, //队伍业务ID
            MBID: ex?ex.MBID:'', //模板ID
            MBLX: MBLX, //模板类型、
            ZYFLDM: ZYFLDM, //专业分类代码
            YWLXDM: "ZR", //业务类型代码
            editable: true,//是否查看,
            isVIewJgxx: false,
            from:'CBSBL'
          },
          {}
      );
    }



    const indexMethod = (index) => {
      console.error()
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      getDataList()
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      closeForm,
      viewRow,
      editRow

    }
  }

})
</script>

<style scoped>

</style>
