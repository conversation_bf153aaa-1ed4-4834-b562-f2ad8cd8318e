<template>
  <el-form :model="formData" :rules="rules" ref="vForm" label-position="right" label-width="180px" size="default" class="lui-card-form">
    <!-- 综合评分法部分 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <div style="font-size:32px;text-align:center;color:red;margin-bottom:30px;">评分方法设置</div>
      <div style="font-weight:bold;margin-bottom:10px;font-size: 18px;">一、综合评分法</div>
      <div style="margin-bottom:5px;padding: 10px; border: 1px solid #56AEFF;">1、本次采购采用综合评分法，其中《技术标》评分满分100分，权重
        <el-input @input="val => onInputNumber(val, 'PFSF_JSBQZ')" @blur="() => onBlurNumberInput('PFSF_JSBQZ')" v-model="formData.PFSF_JSBQZ" style="width:60px;display:inline-block;" size="small" />%，价格评分基础得分
        <el-input @input="val => onInputNumber(val, 'PFSF_JGJCDF')" @blur="() => onBlurNumberInput('PFSF_JGJCDF')" v-model="formData.PFSF_JGJCDF" style="width:60px;display:inline-block;" size="small" />分，综合得分=《技术标》评分*权重+价格评分。</div>
      <div style="margin-bottom:5px;padding: 10px; border: 1px solid #56AEFF;">2、《技术标》评分结果为评审小组各成员有效评分结果的平均值，当评审小组成员人数大于等于11人时，去掉1个最高分值和1个最低分值后计算平均值。</div>
      <div style="margin-bottom:5px;padding: 10px; border: 1px solid #56AEFF;">
        <div style="margin-bottom:10px;">3、评审基准价M值的计算方法：</div>
        <div style="margin-left:20px;margin-bottom:5px;">（1）在甲方标底B值上浮
          <el-input @input="val => onInputNumber(val, 'PFSF_BDBZSF')" @blur="() => onBlurNumberInput('PFSF_BDBZSF')" v-model="formData.PFSF_BDBZSF" style="width:60px;display:inline-block;" size="small" />%~下浮
          <el-input @input="val => onInputNumber(val, 'PFSF_BDBZXF')" @blur="() => onBlurNumberInput('PFSF_BDBZXF')" v-model="formData.PFSF_BDBZXF" style="width:60px;display:inline-block;" size="small" />%范围内（均含边界，四舍五入保留小数点后两位）的投标报价参与平均值A值计算。参与A值计算的投标报价的平均值为A值，M值为A值与B值的平均值。（适用于有标底的项目）</div>
        <div style="margin-left:20px;margin-bottom:5px;">（2）各投标单位有效报价的平均值为M值。</div>
        <div style="margin-left:20px;margin-bottom:5px;">（3）各投标单位有效报价的最低报价为M值。</div>
        <div style="margin-left:20px;margin-bottom:5px;">（4）甲方的标底B值为M值。</div>
      </div>
      <div style="margin-bottom:5px;padding: 10px; border: 1px solid #56AEFF;">
        <div style="margin-bottom:10px;">4、价格得分的计算方法：</div>
        <div style="margin-left:20px;margin-bottom:5px;">（1）投标报价等于M值时，价格评分等于基础得分*
          <el-input @input="val => onInputNumber(val, 'PFSF_JCDFQZ')" @blur="() => onBlurNumberInput('PFSF_JCDFQZ')" v-model="formData.PFSF_JCDFQZ" style="width:60px;display:inline-block;" size="small" /> 。投标报价同M值相比：每高于M值1个百分点扣
          <el-input @input="val => onInputNumber(val, 'PFSF_BJKF')" @blur="() => onBlurNumberInput('PFSF_BJKF')" v-model="formData.PFSF_BJKF" style="width:60px;display:inline-block;" size="small" />分、每低于M值1个百分点加
          <el-input @input="val => onInputNumber(val, 'PFSF_BJJF')" @blur="() => onBlurNumberInput('PFSF_BJJF')" v-model="formData.PFSF_BJJF" style="width:60px;display:inline-block;" size="small" />分。评分结果以基础得分封顶，以零分为最小分值。</div>
        <div style="margin-left:20px;margin-bottom:5px;">（2）投标报价等于M值时，价格评分等于基础得分。投标报价同M值相比：在上浮
          <el-input @input="val => onInputNumber(val, 'PFSF_BJSF')" @blur="() => onBlurNumberInput('PFSF_BJSF')" v-model="formData.PFSF_BJSF" style="width:60px;display:inline-block;" size="small" />%~下浮
          <el-input @input="val => onInputNumber(val, 'PFSF_BJXF')" @blur="() => onBlurNumberInput('PFSF_BJXF')" v-model="formData.PFSF_BJXF" style="width:60px;display:inline-block;" size="small" />%范围内（均含边界）的投标报价，每高于M值1个百分点扣
          <el-input @input="val => onInputNumber(val, 'PFSF_BJFDNKF')" @blur="() => onBlurNumberInput('PFSF_BJFDNKF')" v-model="formData.PFSF_BJFDNKF" style="width:60px;display:inline-block;" size="small" />分、每低于M值1个百分点加
          <el-input @input="val => onInputNumber(val, 'PFSF_BJFDNJF')" @blur="() => onBlurNumberInput('PFSF_BJFDNJF')" v-model="formData.PFSF_BJFDNJF" style="width:60px;display:inline-block;" size="small" />分：在M值上浮%～下浮%范围外的投标报价，每高于M值1个百分点扣
          <el-input @input="val => onInputNumber(val, 'PFSF_BJFDWKF')" @blur="() => onBlurNumberInput('PFSF_BJFDWKF')" v-model="formData.PFSF_BJFDWKF" style="width:60px;display:inline-block;" size="small" />分、每低于M值1个百分点扣
          <el-input @input="val => onInputNumber(val, 'PFSF_BJFDWJF')" @blur="() => onBlurNumberInput('PFSF_BJFDWJF')" v-model="formData.PFSF_BJFDWJF" style="width:60px;display:inline-block;" size="small" />分。评分结果以零分为最小值上不封顶。</div>
        <div style="margin-left:20px;margin-bottom:5px;">（3）（M值/投标人有效投标报价）*价格评分基础得分。</div>
        <div style="margin-left:20px;margin-bottom:5px;">（4）经评审的有效投标报价=评标基准价时，得100分。经评审的有效投标报价-评标基准价＞0时，投标报价得分=100-（（有效投标报价-评标基准价）/评标基准价*100*
          <el-input @input="val => onInputNumber(val, 'PFSF_XJZJQZ')" @blur="() => onBlurNumberInput('PFSF_XJZJQZ')" v-model="formData.PFSF_XJZJQZ" style="width:60px;display:inline-block;" size="small" />）。经评审的有效投标报价-评标基准价＜0时，投标报价得分=100-（（评标基准价-有效投标报价）/评标基准价*100*
          <el-input @input="val => onInputNumber(val, 'PFSF_DJZJQZ')" @blur="() => onBlurNumberInput('PFSF_DJZJQZ')" v-model="formData.PFSF_DJZJQZ" style="width:60px;display:inline-block;" size="small" />）。分值按内插法计算，结算保留小数后二位，本项最低得分0分。</div>
      </div>
      <div style="margin-bottom:5px;padding: 10px; border: 1px solid #56AEFF;">5、分值计算按插入法，评分结果按四舍五入保留小数点后两位。</div>
    </el-card>
    <!-- 定标办法部分 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <div style="font-weight:bold;margin-bottom:10px;font-size: 18px;">二、定标办法：</div>
      <el-form-item label="" prop="PFSF_DBBF">
        <el-input type="textarea" :rows="6" v-model="formData.PFSF_DBBF" placeholder="请输入定标办法内容" />
      </el-form-item>
    </el-card>
    <!-- 流标及招标结束部分 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <div style="font-weight:bold;margin-bottom:10px;font-size: 18px;">三、流标及招标结束：</div>
      <el-form-item label="" prop="PFSF_LBZBJS">
        <el-input type="textarea" :rows="6" v-model="formData.PFSF_LBZBJS" placeholder="请输入流标及招标结束内容" />
      </el-form-item>
    </el-card>
  </el-form>
  <el-row style="line-height: 50px;">
    <el-col :span="24" style="text-align: center; position: fixed; bottom: 9px; left: 0; right: 0; z-index: 100; background-color: white;">
      <el-button size="default" type="primary" @click="confirmData">确认</el-button>
      <el-button size="default" @click="close">关闭</el-button>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axiosUtil from "@lib/axiosUtil";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
});
const emit = defineEmits(["closeDialogpsbf","reload"]);
const formData = ref({});
const rules = ref({});

onMounted(() => {
  formData.value = Object.assign({}, props.data);

  if(formData.value.PFSF_DBBF == null){
    formData.value.PFSF_DBBF = '1、技术分得分最高的投标单位为第一中标顺序人。\n2、若技术分得分相等，则去掉一个最高分去掉一个最低分取平均值为第一中标顺序人。\n3、如第一中标顺序人放弃中标的标段，第二中标顺序人递补中标，依次类推。\n4、如出现其他情况，则由招标小组确定处理方案。';
  }
  if(formData.value.PFSF_LBZBJS == null){
    formData.value.PFSF_LBZBJS = '如一个标段的所有投标文件均为无效标，则宣布流标。';
  }
});

// 确认
const vForm = ref(null);
const confirmData = () => {
  if(!formData.value.PFSF_JSBQZ || !formData.value.PFSF_JGJCDF || !formData.value.PFSF_BDBZSF || 
     !formData.value.PFSF_BDBZXF || !formData.value.PFSF_JCDFQZ || !formData.value.PFSF_BJKF || 
     !formData.value.PFSF_BJJF || !formData.value.PFSF_BJSF || !formData.value.PFSF_BJXF || 
     !formData.value.PFSF_BJFDNKF || !formData.value.PFSF_BJFDNJF || !formData.value.PFSF_BJFDWKF || 
     !formData.value.PFSF_BJFDWJF || !formData.value.PFSF_XJZJQZ || !formData.value.PFSF_DJZJQZ || 
     !formData.value.PFSF_DBBF || !formData.value.PFSF_LBZBJS){
    ElMessage.warning('还有未填写项!');
    return;
  }

  emit('confirmDialogData', formData.value);
}

const close = () => {
  emit('closeDialogpsbf');
}

const onInputNumber = (val, key) => {
  let newVal = val === null || val === undefined ? '' : String(val);

  // 输入空字符串或仅一个小数点
  if (newVal === '' || newVal === '.') {
    formData.value[key] = '';
    return;
  }

  // 只保留可选的负号、数字和最多一个小数点，移除其他无效字符
  let sign = newVal.startsWith('-') ? '-' : '';
  let numStr = newVal.replace(/[^\d.]/g, ''); // 只保留数字和小数点

  // 处理多个小数点，只保留第一个
  const firstDotIndex = numStr.indexOf('.');
  if (firstDotIndex !== -1) {
    numStr = numStr.slice(0, firstDotIndex + 1) + numStr.slice(firstDotIndex + 1).replace(/\./g, '');
  }
  
  newVal = sign + numStr;

  // 处理如 '-' 这种 numStr 为空的情况
  if (numStr === '') {
      formData.value[key] = newVal;
      return;
  }

  // 限制小数点后最多两位
  if (numStr.includes('.')) {
    const parts = numStr.split('.');
    if (parts[1] && parts[1].length > 2) {
      parts[1] = parts[1].slice(0, 2);
      numStr = parts.join('.');
      newVal = sign + numStr;
    }
  }

  const num = parseFloat(newVal);

  // 限制数值范围在 -100 到 100 之间
  if (!isNaN(num)) {
    if (num > 100) {
      newVal = '100';
    } else if (num < -100) {
      newVal = '-100';
    }
  }

  formData.value[key] = newVal;
};

const onBlurNumberInput = (key) => {
  if (formData.value[key] === '-') {
    formData.value[key] = '';
  }
}

defineExpose({});

</script>

<style scoped>
.lui-card-form {
  height: calc(100vh - 160px);
  overflow-y: auto;
  padding-bottom: 80px; /* 新增，避免底部fixed按钮遮挡内容 */
}

::v-deep .el-input__inner{
  border: 1px solid gray;
  text-align: center;
}

::v-deep(.el-form-item__content) {
  margin-left: 0 !important;
}

::v-deep .el-input__wrapper{
  box-shadow: none;
}

</style>