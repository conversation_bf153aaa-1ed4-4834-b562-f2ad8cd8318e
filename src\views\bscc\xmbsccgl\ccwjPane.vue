<template>
  <div>
    <el-form :model="modelValue" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="项目名称：" prop="XMID">
            <el-input v-model="modelValue.XMMC" type="text" placeholder="请选择" clearable :disabled="true">
              <template #append v-if="editable">
                <el-button @click="chooseCcxm">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="标题：" prop="BT">
            <el-input v-model="modelValue.BT" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="创建人：" prop="CJRXM">
            <div style="margin-left: 10px">{{ modelValue.CJRXM }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="创建时间：" prop="CJSJ">
            <div style="margin-left: 10px">{{ modelValue.CJSJ }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="招标文件：" prop="ZBWJ">
            <vsfileupload ref="vsfileupload" style="margin-left: 10px;min-width: 200px" :busId="modelValue.WJID"
                          :key="modelValue.WJID" v-model:files="ZBfileList"
                          :editable="true" ywlb="ZBWJ"/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="对比标书文件：" prop="ZBWJ">
            <div style="display: flex;gap: 10px;align-items: center;margin-bottom: 10px;margin-top: 10px">
              <vsfileupload ref="dbvsfileupload" style="margin-left: 10px;" :busId="params.id"
                            :key="params.id" v-model:files="DBfileList" :show-file-list="false" :limit="20"
                            accept=".pdf" :noTableButton="true"
                            :editable="editable" ywlb="dbwj"/>
              <el-button type="warning" @click="getXmzbWj" v-if="editable">提取</el-button>
              <div v-if="editable">提示：请上传非扫描版PDF文件，单个文件件大小不超过200M，最多上传文件不超过20个。</div>
            </div>
            <el-table ref="datatable91634" :data="DBfileList" height="200px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
              <el-table-column prop="name" label="文件名称" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="160">
                <template #default="{row}">
                  <el-button link type="primary" size="large" @click="previewFile(row)">{{row.name}}</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="150" fixed="right" v-if="editable">
                <template #default="{row}">
                  <el-button size="small" class="lui-table-button" type="primary" @click="deleteFile(row)">删除
                  </el-button>
                </template>
              </el-table-column>

            </el-table>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogCCXMVisible"
        v-model="dialogCCXMVisible"
        title="查重项目选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <ccxmChoose @close="dialogCCXMVisible=false" @submit="getCcxmRes"/>
      </div>
    </el-dialog>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsfileupload from "@views/components/vsfileupload";
import ccxmChoose from "@views/bscc/xmbsccgl/ccxmChoose";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import vsAuth from "@lib/vsAuth";


export default defineComponent({
  name: '',
  components: {vsfileupload,ccxmChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: props.params.editable,
      userInfo: vsAuth.getAuthInfo().permission,
      rules: {
        XMID: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      DBfileList: [],
      ZBfileList: [],

      dialogCCXMVisible: false
    })

    const chooseCcxm = () => {
      state.dialogCCXMVisible=true
    }

    const getCcxmRes = (value) => {
      props.modelValue.XMID=value.XMID
      props.modelValue.GGID=value.GGID
      props.modelValue.JLID=value.JLID
      props.modelValue.WJID=value.WJID
      props.modelValue.XMMC=value.XMMC
      props.modelValue.BT=value.XMMC+'查重'
      state.dialogCCXMVisible=false
    }

    const instance = getCurrentInstance()
    const getXmzbWj = () => {
      if(!props.modelValue.XMID){
        ElMessage.warning('请选择查重项目')
        return
      }
      if(!props.modelValue.GGID){
        ElMessage.warning('当前未进行到投标环节，无法提取')
        return
      }
      let params={
        GGID: props.modelValue.GGID,
        CCWJID: props.params.id
      }
      axiosUtil.get('/backend/bscc/bsccgl/extractXmzbwj',params).then(res=>{
        instance.proxy.$refs['dbvsfileupload'].loadFileList()
        ElMessage.success('提取成功')
      })
    }

    const deleteFile = (row) => {
      instance.proxy.$refs['dbvsfileupload'].handleRemove(row)
      instance.proxy.$refs['dbvsfileupload'].loadFileList()
    }

    const previewFile = (row) => {
      instance.proxy.$refs['dbvsfileupload'].handlePreview(row)
    }

    const validateForm = () => {
      return new Promise((resolve, reject) => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            if(state.DBfileList.length<2){
              reject('请上传或提取至少两个对比标书文件')
            }else if(state.DBfileList.find(item=>!item.mongoDBId)){
              reject('请等待文件上传完成')
            }else {
              resolve(true)
            }
          } else {
            reject('请完善页面信息')
          }
        })
      })
    }

    const getCCQDList = () => {
      let CCQDList=[]

      for (let i = 0; i < state.DBfileList.length; i++) {
        for (let j = i+1; j < state.DBfileList.length; j++) {
          CCQDList.push({
            CCQDID: comFun.newId(),
            CCWJID: props.params.id,
            AWJID: state.DBfileList[i].id,
            AWJMC: state.DBfileList[i].name,
            BWJID: state.DBfileList[j].id,
            BWJMC: state.DBfileList[j].name,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
          })
        }
      }

      return CCQDList
    }


    onMounted(() => {

    })

    return {
      ...toRefs(state),
      chooseCcxm,
      getCcxmRes,
      getXmzbWj,
      deleteFile,
      previewFile,
      validateForm,
      getCCQDList

    }
  }

})
</script>

<style scoped>

</style>
