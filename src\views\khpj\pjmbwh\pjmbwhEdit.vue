<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 120px;text-align: center;padding-bottom: 2px">
        基础信息
      </div>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="模板名称：" prop="MBMC">
            <el-input v-model="formData.MBMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="记分方式：" prop="JFFS">
            <el-select v-model="formData.JFFS" class="full-width-input"
                       :disabled="!editable"
                       clearable>
              <el-option v-for="(item, index) in JFFSOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="记分层级：" prop="JFCJ">
            <el-select v-model="formData.JFCJ" class="full-width-input"
                       :disabled="!editable" @change="formData.XXZBList=[]"
                       clearable>
              <el-option v-for="(item, index) in JFCJOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="创建时间：" prop="CJSJ">
            <div style="margin-left: 10px">{{formData.CJSJ}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="更新时间：" prop="XGSJ">
            <div style="margin-left: 10px">{{formData.XGSJ}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="版本号：" prop="BBH">
            <el-input v-model="formData.BBH" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="模板描述：" prop="MBMS">
            <el-input v-model="formData.MBMS" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

      </el-row>

      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 120px;text-align: center;padding-bottom: 2px">
        详细指标配置
      </div>

      <div>
        <div style="text-align: right;padding-bottom: 10px">
          <el-button ref="button91277" @click="addRow" type="primary" v-if="editable">添加</el-button>
        </div>
        <el-table ref="datatable91634" :data="formData.XXZBList" height="calc(300px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :span-method="arraySpanMethod"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
          <el-table-column prop="ZBLBMC" label="评价类别" align="center"
                           :show-overflow-tooltip="true" width="150">
          </el-table-column>
          <el-table-column prop="PJNR" label="评价内容" align="center"
                           :show-overflow-tooltip="true" min-width="150">
          </el-table-column>
          <el-table-column prop="ZBMC" label="指标名称" align="center"
                           :show-overflow-tooltip="true" width="150">
          </el-table-column>
          <el-table-column prop="ZBJF" label="指标记分" align="center"
                           :show-overflow-tooltip="true" width="100">
          </el-table-column>
          <el-table-column prop="PFBZXQ" label="评分标准" align="center" v-if="formData.JFCJ==='BZ'"
                           :show-overflow-tooltip="true" min-width="200">
          </el-table-column>
          <el-table-column prop="BZJF" label="标准记分" align="center" v-if="formData.JFCJ==='BZ'"
                           :show-overflow-tooltip="true" width="100">
          </el-table-column>
          <el-table-column prop="CZ" label="操作" align="center" width="120" v-if="editable">
            <template #default="scope">
              <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.$index)">删除</el-button>
            </template>
          </el-table-column>

        </el-table>
      </div>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()" v-if="editable">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogXZZBVisible"
        v-model="dialogXZZBVisible"
        title="选择指标"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <pjzbChoose :JFCJ="formData.JFCJ" :JFFS="formData.JFFS" v-if="dialogXZZBVisible" :params="XZZBParams" @close="dialogXZZBVisible=false" @submit="getXzzbRes"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import pjzbChoose from "@views/khpj/pjmbwh/pjzbChoose";
import axiosUtil from "@lib/axiosUtil";


export default defineComponent({
  name: '',
  components: {pjzbChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      PJMBID: props.params.id,
      formData: {
        XXZBList: [],
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SFQY: '0',
      },
      rules: {
        MBMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        MBMS: [{
          required: true,
          message: '字段值不可为空',
        }],
        JFFS: [{
          required: true,
          message: '字段值不可为空',
        }],
        JFCJ: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      JFFSOptions: [],
      JFCJOptions: [],

      XZZBParams: {},
      dialogXZZBVisible: false,


    })

    const getFormData = () => {
      let params={
        PJMBID: state.PJMBID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/pjmbwh/selectPjmbById',params).then(res=>{
        state.formData=res.data
        state.formData.XGSJ=comFun.getNowTime()
        state.loading=false
      })
    }

    const arraySpanMethod = ({ row, column, rowIndex, columnIndex}) => {
      if(columnIndex===1){
        if(rowIndex>0 && state.formData.XXZBList[rowIndex-1].ZBLB===row.ZBLB){
          return [0,1]
        }else {
          let span=1
          let rIndex=rowIndex+1
          while (state.formData.XXZBList.length-1>=rIndex && state.formData.XXZBList[rIndex].ZBLB === row.ZBLB){
            span++
            rIndex++
          }
          return [span,1]
        }
      }

      if(columnIndex===2){
        if(rowIndex>0 && state.formData.XXZBList[rowIndex-1].PJNR===row.PJNR){
          return [0,1]
        }else {
          let span=1
          let rIndex=rowIndex+1
          while (state.formData.XXZBList.length-1>=rIndex && state.formData.XXZBList[rIndex].PJNR === row.PJNR){
            span++
            rIndex++
          }
          return [span,1]
        }
      }

      if(columnIndex===3){
        if(rowIndex>0 && state.formData.XXZBList[rowIndex-1].ZBMC===row.ZBMC){
          return [0,1]
        }else {
          let span=1
          let rIndex=rowIndex+1
          while (state.formData.XXZBList.length-1>=rIndex && state.formData.XXZBList[rIndex].ZBMC === row.ZBMC){
            span++
            rIndex++
          }
          return [span,1]
        }
      }

      if(columnIndex===4){
        if(rowIndex>0 && state.formData.XXZBList[rowIndex-1].ZBJF===row.ZBJF){
          return [0,1]
        }else {
          let span=1
          let rIndex=rowIndex+1
          while (state.formData.XXZBList.length-1>=rIndex && state.formData.XXZBList[rIndex].ZBJF === row.ZBJF){
            span++
            rIndex++
          }
          return [span,1]
        }
      }


    }


    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const submitForm = () => {
      let params={
        ...state.formData,
        PJMBID: state.PJMBID,
        XGRZH: state.userInfo.userLoginName,
        SHZT: '1',
      }

      params.XXZBList.forEach((item,index)=>item.PXH=index+1)

      state.loading=true
      axiosUtil.post('/backend/sckhpj/pjmbwh/savePjmbForm',params).then(res=>{
        ElMessage.success('保存成功')
        closeForm()
        state.loading=false
      })

    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            if(state.formData.XXZBList.length===0){
              ElMessage.error('请添加详细指标')
              resolve(false)
              return
            }
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const addRow = () => {
      if(!state.formData.JFCJ){
        ElMessage.warning('请选择记分层级')
        return
      }
      if(!state.formData.JFFS){
        ElMessage.warning('请选择记分方式')
        return
      }
      state.dialogXZZBVisible=true
    }

    const deleteRow = (index) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.formData.XXZBList.splice(index,1)
        ElMessage.success({
          message: '删除成功!'
        });
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const closeForm = () => {
      emit('close')
    }

    const getXzzbRes = (value) => {
      console.error(value)
      value.forEach(item=>{
        let pushRow=[]
        let PJMBMXID=comFun.newId()
        item.BZMXList.forEach(ii=>{
          pushRow.push({
            ...item,
            PFBZID: comFun.newId(),
            PJMBMXID: PJMBMXID,
            PJMBID: state.PJMBID,
            PFBZXQ: ii.PFBZXQ,
            BZJF: ii.BZJF
          })
        })




        state.formData.XXZBList.push(...pushRow)


      })
      state.dialogXZZBVisible=false
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
      })
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      getDMBData('JFFS', 'JFFSOptions')
      getDMBData('JFCJ', 'JFCJOptions')
    })

    return {
      ...toRefs(state),
      getFormData,
      saveData,
      closeForm,
      addRow,
      deleteRow,
      getXzzbRes,
      arraySpanMethod
    }
  }

})
</script>

<style scoped>

</style>
