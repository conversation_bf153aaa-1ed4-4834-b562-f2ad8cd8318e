<template>
  <div style="height: calc(100% - 40px)">
    <el-form style="height: 100%;" size="default" class="lui-page" v-loading="loading">
      <el-table :data="tableData" height="calc(100% - 80px)" border size="default" class="lui-table">
        <el-table-column label="队伍名称" prop="unitName" width="200" show-overflow-tooltip header-align="center"
                         align=""></el-table-column>
        <el-table-column label="队伍编码" prop="dwbm" width="200" header-align="center"
                         align=""></el-table-column>
        <el-table-column label="申请服务范围" show-overflow-tooltip prop="specialName"
                         header-align="center" align=""></el-table-column>
        <el-table-column v-for="(item,index) in templates" :key="index" :label="item.SJMBMC"
                         :prop="item.SJMBBM" header-align="center" align="center" width="100">
          <template v-slot="scope">
            <div v-if="scope.row.templates.filter(x=>x.SJMBBM == item.SJMBBM).length > 0">
              <el-button type="success" v-if="scope.row[item.SJMBBM]" :icon="Check" circle
                         style="width: 30px;height: 30px"/>
              <el-button type="danger" v-else :icon="Close" circle
                         style="width: 30px;height: 30px"/>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" align="center" fixed="right" width="150">
          <template v-slot="scope">
            <div>
              <el-button v-if="editable" class="lui-table-button" @click="editRow(scope.row)">录入信息</el-button>
              <el-button v-if="!editable" class="lui-table-button" @click="viewRow(scope.row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, onUnmounted} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {Check, Close} from '@element-plus/icons-vue'
import tabFun from "@lib/tabFun";
import {mixin} from "@core";

export default defineComponent({
  name: '',
  components: {},
  props: {
    DWYWID: {
      type: String,
      defaultData: () => '',
    },
    LSDWYWID: {
      type: String,
      defaultData: () => '',
    },
    BGJL: {
      type: Object,
      required: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    editable: {
      type: String,
      defaultData: () => false,
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      tableData: [],
      templates: [],
      loading: false,
      LSDWList: []
    })


    const getDataList = () => {
      const params = {
        DWYWID: props.DWYWID
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/cbsyj/teamlist', params).then((res) => {
        let resData = res.data || []
        resData.forEach(x => {
          x.unitName = x.DWMC;
          x.specialName = x.SQFWFW;
          x.dwbm = x.DWBM
        })
        state.tableData = resData
        getLSDWList()
        state.loading = false
      });
    }

    const getLSDWList = () => {
      const params = {
        DWYWID: props.LSDWYWID
      }
      axiosUtil.get('/backend/sccbsgl/cbsyj/teamlist', params).then((res) => {
        state.LSDWList = res.data || []
        state.loading = false
      });
    }

    const getMbxxList = () => {
      const params = {
        DWYWID: props.DWYWID,
        MBLX: 'DW',
        BGLX: props.BGJL?.BGLX
      }
      axiosUtil.get('/backend/sccbsgl/dwxxbg/selecBqListById', params).then(res => {
        state.templates = res.data.tabList || []
      })
    }

    const editRow = (row) => {
      let bgdw = state.LSDWList.find(item => item.DWWYBS = row.DWWYBS)
      if(props.BGJL.BGJLID && bgdw.DWYWID){
        tabFun.addTabByRoutePath(
            '队伍变更信息',
            '/contractors/dwxxEdit',
            {
              params: {
                id: row.DWYWID,
                lsId: bgdw.DWYWID,
                BGJL: props.BGJL,
                YWLXDM: props.YWLXDM,
                editable: true,
                operation: 'edit'
              }
            },
            null
        )
      }

    }

    const viewRow = (row) => {
      let bgdw = state.LSDWList.find(item => item.DWWYBS = row.DWWYBS)
      tabFun.addTabByRoutePath(
          '队伍变更信息',
          '/contractors/dwxxEdit',
          {
            params: {
              id: row.DWYWID,
              lsId: bgdw.DWYWID,
              BGJL: props.BGJL,
              YWLXDM: props.YWLXDM,
              editable: false,
              operation: 'view'
            }
          },
          null
      )
    }
    const {vsuiEventbus} = mixin()

    onMounted(() => {
      vsuiEventbus.on('reloadTableData',getDataList);

      getDataList()
      getMbxxList()
    })

    onUnmounted(()=>{
      // timer.value = null;
      vsuiEventbus.off('reloadTableData',getDataList);
    })

    return {
      ...toRefs(state),
      Check, Close,
      editRow,
      viewRow

    }
  }

})
</script>

<style scoped>

</style>
