<!-- 选择队伍 -->
<template>
    <el-container>
        <outerBox title="选择队伍">
            <template v-slot:content>
                <div class='out-box-content'>
                    <el-table :data="tableData" height="calc(100% - 100px)" border>
                        <el-table-column label="队伍名称" prop="teamName" header-align="center" align="center"></el-table-column>
                        <el-table-column label="服务范围" prop="serveField" header-align="center" align="left"></el-table-column>
                        <el-table-column label="队伍类别" prop="teamType" header-align="center" align="left"></el-table-column>
                        <el-table-column label="推荐单位" prop="recommendUnit" header-align="center" align="left"></el-table-column>
                        <el-table-column label="引进类型" prop="introduceType" header-align="center" align="left"></el-table-column>
                        <el-table-column label="操作" header-align="center" align="center" width="100">
                            <template #default="scope">
                                <div>
                                    <el-button type="text" @click="handleFqzx(scope.row, scope.$index)" :disabled="isDisabled">发起增项</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-row class="btn" align="center" justify="center">
                        <el-button @click="handleReturn">返回</el-button>
                    </el-row>
                </div>
            </template>
        </outerBox>
    </el-container>
</template>

<script setup>
    import outerBox from '../../../components/common/outerBox.vue'
    import { vue } from '../../../../src/assets/core'

    let tableData = vue.reactive([])

    /**查询队伍 */
    const queryTableData = () =>{
        tableData.length = 0
        tableData.push(...[])
        // TODO 查询队伍信息
    }
    /**发起增项 */
    const handleFqzx = (row, index) =>{
        // TODO 发起增项
    }
    /**返回 */
    const handleReturn = () =>{
        // TODO 返回操作
    }
    vue.onMounted(() =>{
        queryTableData()
    })
</script>

<style scoped>
.el-container{
    height: calc(100% - 10px);
}
.out-box-content{
    height: 100%;
}
</style>