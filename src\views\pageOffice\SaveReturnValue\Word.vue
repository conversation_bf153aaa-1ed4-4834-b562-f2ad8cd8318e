<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
  pageofficectrl.AddCustomToolButton("保存", "Save", 1);
  pageofficectrl.AddCustomToolButton("关闭", "Close", 21);
  pageofficectrl.Caption = "Word文档保存后获取返回值";
}

function Save() {
  pageofficectrl.SaveFilePage = "/SaveReturnValue/save";
  //在这里写您保存前的代码
  pageofficectrl.WebSave();
  //在这里写您保存后的代码，比如判断保存结果pageofficectrl.CustomSaveResult
  alert("保存成功，返回值为：" + pageofficectrl.CustomSaveResult);
}
function Close() {
  pageofficectrl.CloseWindow();
}

function AfterDocumentOpened() {
  // PageOffice的文档打开后事件回调函数
}

function openFile() {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/SaveReturnValue/Word',
    method: 'get',
  });
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit, Save, Close, AfterDocumentOpened };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
  <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
</template>
