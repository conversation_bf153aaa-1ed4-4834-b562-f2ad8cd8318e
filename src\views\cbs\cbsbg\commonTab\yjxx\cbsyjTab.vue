<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        :model="tableData"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        class="lui-page"
        :disabled="!editable">
<!--      <div style="color: red">-->
<!--        {{ BGXX }}-->
<!--      </div>-->
      <el-row :gutter="20" style="margin-bottom: 20px" v-if="editable">
        <el-col :span="24">
          <el-button style="float: right" ref="button9527" type="primary" @click="addData">
            增加
          </el-button>
        </el-col>
      </el-row>
      <el-table ref="dataTable" :data="[...tableData,...getDelRow()]" height="500px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column v-for="(item,index) in tableColumn" :key="index"
                         :prop="item.prop" :label="item.label" :align="item.align" :width="item.width"
                         :min-width="item.minWidth"
                         :show-overflow-tooltip="item.showOverflowTooltip">

          <template #default="{row,$index}" v-if="item.slot==='formatChange'">
            <div :class="{dataChange : isChangeT(row.YJWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.YJWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.YJWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span> {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else>
            <div :class="{dataChange : isChangeT(row.YJWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.YJWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.YJWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span>{{ row[item.prop] }}</span>
            </div>
          </template>

        </el-table-column>

        <el-table-column prop="CZ" label="操作" align="center" width="300" fixed="right" v-if="editable">
          <template #default="{row,$index}">
            <div v-if="tableRowClassName({row})!=='warning-row'">
              <el-button class="lui-table-button" @click="editRow(row)">编辑</el-button>
              <el-button class="lui-table-button" @click="viewRow(row)">查看</el-button>
              <el-button class="lui-table-button" @click="deleteRow($index)">删除</el-button>
            </div>
            <div v-else>
              已删除
            </div>
          </template>
        </el-table-column>


      </el-table>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="业绩信息"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <cbsyjEdit v-if="dialogVisible" :currentRow="currentRow" @close="closeForm" @submit="getSubmit"/>
      </div>
    </el-dialog>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, ref} from "vue";
import vsfileupload from "@views/components/vsfileupload";
import {InfoFilled} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import yjxxEdit from "@views/cbs/templateManagement/DataTemplateManagement/yjxx/yjxxEdit.vue"
import yjxxXz from "@views/cbs/templateManagement/DataTemplateManagement/yjxx/yjxx_xz.vue"
import comFun from "@lib/comFun";
import cbsyjEdit from "@views/cbs/templateManagement/DataTemplateManagement/cbsyj/cbsyjEdit";


export default defineComponent({
  name: '',
  components: {vsfileupload, InfoFilled, yjxxEdit, yjxxXz,cbsyjEdit},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    TYXYDM: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      currentRow: {},
      dialogVisible: false,

      tableData: [],
      tableColumn: [
        {
          label: "项目名称",
          prop: "XMMC",
          align: "center",
          minWidth: 150,
        },
        {
          label: "建设单位",
          prop: "JSDW",
          align: "center",
          minWidth: 150,
        },
        {
          label: "合同名称",
          prop: "HTMC",
          align: "center",
          width: 150,
        },
        {
          label: "工程范围",
          prop: "GCFW",
          align: "center",
          width: 150,
        },
        {
          label: "合同开始日期",
          prop: "HTRQKS",
          align: "center",
          width: 150,
          slot: 'formatChange',
        },
        {
          label: "合同结束日期",
          prop: "HTRQJS",
          align: "center",
          width: 150,
          slot: 'formatChange',
        },
      ],
    })

    watch(() => props.defaultData, val => {
      if (val) {
        val.forEach(x => {
          const UUID = comFun.newId()
          x.DWYJID = x.DWYJID || UUID;
          x.YJWYBS = x.YJWYBS || UUID;
        })
        state.tableData = val
      }
    }, {immediate: true})


    watch(() => state.tableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])
    const initBgInfo = () => {
      if (state.tableData && props.resultTableData) {
        let res = []
        let BGQBS = props.resultTableData.map(i => i.YJWYBS)
        let BGHBS = state.tableData.map(i => i.YJWYBS)
        let allBS = new Set([...BGQBS, ...BGHBS])
        allBS.forEach(item => {
          if (BGQBS.includes(item) && BGHBS.includes(item)) {
            let BGQXX = props.resultTableData.find(i => i.YJWYBS === item)
            let BGHXX = state.tableData.find(i => i.YJWYBS === item)
            let isBg = false
            let dbsj = []
            let checkProp = ['XMMC', 'JSDW', 'HTMC', 'GCFW', 'HTRQKS', 'HTRQJS']
            checkProp.forEach(ii => {
              if ((BGQXX[ii] || '') !== (BGHXX[ii] || '')) {
                dbsj.push({
                  BGQ: BGQXX[ii] || '',
                  BGH: BGHXX[ii] || '',
                  ZDMC: ii
                })
                isBg = true

              }
            })
            if (isBg) {
              res.push({
                YWLX: 'YJXX',
                BGZT: 'BG',
                WYBS: item,
                BGXQ: dbsj
              })
            }

          } else if (BGQBS.includes(item) && !BGHBS.includes(item)) {
            res.push({
              YWLX: 'YJXX',
              BGZT: 'SC',
              WYBS: item,
            })
          } else if (!BGQBS.includes(item) && BGHBS.includes(item)) {
            res.push({
              YWLX: 'YJXX',
              BGZT: 'XZ',
              WYBS: item,
            })
          }
        })
        BGXX.value = res
      }

    }

    const getDelRow = () => {
      if (props.resultTableData && state.tableData) {
        return props.resultTableData.filter(item => !state.tableData.find(ii => ii.YJWYBS === item.YJWYBS))
      } else {
        return []
      }
    }

    const tableRowClassName = ({row, index}) => {
      let info = BGXX.value.find(ii => ii.WYBS === row.YJWYBS) || {}
      if (info.BGZT === 'XZ') {
        return "success-row"
      } else if (info.BGZT === 'SC') {
        return "warning-row"
      }

    }

    const isChangeT = (YJWYBS, prop) => {
      let info = BGXX.value.find(item => item.WYBS === YJWYBS && item.BGZT === 'BG')
      if (info) {
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if (res) {
          res.BGMS = `${res.BGQ} 变更为 ${res.BGH}`
          return res
        }
      }
      return null
    }
    // 新增
    const addData = () => {
      state.currentRow = {editable: true, id: comFun.newId(), operation: 'add',YJWYBS: comFun.newId()}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.currentRow = {...row,editable: true, id: row.CBSYJID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.currentRow = {...row,editable: false, id: row.CBSYJID, operation: 'view'}
      state.dialogVisible = true
    }

    const deleteRow = (index) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.tableData.splice(index, 1)
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const getSubmit = (value,isContinue) => {
      if(value.operation==='add'){
        state.tableData.push(value)
      }else {
        let row=state.tableData.find(x=>x.CBSYJID===value.CBSYJID)
        Object.assign(row,value)
      }

      if(isContinue){
        state.currentRow={editable: true, id: comFun.newId(), operation: 'add'}
      }else {
        state.dialogVisible=false
      }
    }

    const closeForm = () => {
      state.dialogVisible=false
    }




    onMounted(() => {

    })

    return {
      ...toRefs(state),
      tableRowClassName,
      isChangeT,
      getDelRow,
      BGXX,
      addData,
      editRow,
      viewRow,
      deleteRow,
      getSubmit,
      closeForm

    }
  }

})
</script>

<style scoped>

</style>
