<template>
<div class="Word">
	当前用户：{{userName}}
  <div style="width:auto; height:700px;" v-html="poHtmlCode" >
  </div>
</div>
</template>

<script>
  import axios from 'axios';
  export default{
    name: 'Word',
    data(){
      return {
        message: ' ',
        poHtmlCode: '',
		userName:''
      }
    },
    created: function(){
		var userid = window.external.UserParams
      //由于vue中的axios拦截器给请求加token都得是ajax请求，所以这里必须是axios方式去请求后台打开文件的controller
      axios.post("/api/ConcurrencyCtrl/Word?userid="+userid).then((response) => {
        this.poHtmlCode = response.data.pageoffice;
		this.userName = response.data.userName;

      }).catch(function (err) {
        console.log(err)
      })
    },
    methods:{
      //控件中的一些常用方法都在这里调用，比如保存，打印等等
		Save() {
			pageofficectrl.WebSave();
		},

		//文档关闭前先提示用户是否保存
		BeforeBrowserClosed() {
			if (pageofficectrl.IsDirty) {
				if (confirm("提示：文档已被修改，是否继续关闭放弃保存 ？")) {
					return true;

				} else {

					return false;
				}
			}
		}
    },
    mounted: function(){
      // 将vue中的方法赋值给window
	  window.Save = this.Save;
      window.BeforeBrowserClosed = this.BeforeBrowserClosed;
    }
}
</script>
