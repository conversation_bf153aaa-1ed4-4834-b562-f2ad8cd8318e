<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="200px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="12" class="grid-cell">
          <el-form-item label="项目名称：" prop="XMMC">
            <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="子项目名称：" prop="ZXMMC">
            <el-input v-model="formData.ZXMMC" type="text" placeholder="请选择" clearable :disabled="!editable">

            </el-input>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="grid-cell">
          <el-form-item label="企业名称：" prop="CBSDWQC">
            <el-input v-model="formData.CBSDWQC" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="队伍名称：" prop="DWMC">
            <el-input v-model="formData.DWMC" type="text" placeholder="请输入" clearable :disabled="true">
              <template #append v-if="editable">
                <el-button @click="dialogDWXZVisible=true">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="项目所属单位：" prop="JSDWID">
            <el-select v-model="formData.JSDWID" class="full-width-input"
                       :disabled="!editable" @change="(value)=>formData.JSDWMC=EJDWOptions.find(item=>item.ORGNA_ID===value)?.ORGNA_NAME"
                       clearable>
              <el-option v-for="(item, index) in EJDWOptions" :key="index" :label="item.ORGNA_NAME"
                         :value="item.ORGNA_ID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="项目专业名称：" prop="XMZYBM">
            <el-cascader v-model="formData.XMZYBM" :options="XMZYTree" filterable
                         @change="(value)=>formData.XMZYMC=XMZYList.find(item=>item.ZYBM===value)?.ZYMC"
                         :props="{checkStrictly: false,label:'ZYMC',value:'ZYBM',emitPath: false}"
                         clearable :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="合同期限开始：" prop="HTQXKS">
            <el-date-picker
                v-model="formData.HTQXKS"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="合同期限结束：" prop="HTQXJS">
            <el-date-picker
                v-model="formData.HTQXJS"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="招标文件：" prop="ZBWJ">
            <vsfileupload style="margin-left: 10px" :busId="params.id"
                          :key="params.id"
                          :editable="editable" ywlb="zbwj"/>
          </el-form-item>
        </el-col>
      </el-row>


      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogDWXZVisible"
        v-model="dialogDWXZVisible"
        title="队伍选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <dwChoose v-if="dialogDWXZVisible" @close="dialogDWXZVisible=false" @submit="getDWXZRes"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import dwChoose from "@views/khpj/xmwtsb/dwChoose";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";


export default defineComponent({
  name: '',
  components: {dwChoose,vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      XMID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
      },
      rules: {
        XMMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        DWMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        JSDWID: [{
          required: true,
          message: '字段值不可为空',
        }],
        XMZYBM: [{
          required: true,
          message: '字段值不可为空',
        }],
        HTQXKS: [{
          required: true,
          message: '字段值不可为空',
        }],
        HTQXJS: [{
          required: true,
          message: '字段值不可为空',
        }],
        
      },
      EJDWOptions: [],
      XMZYTree: [],
      XMZYList: [],

      dialogDWXZVisible: false
    })


    const getFormData = () => {
      let params={
        XMID: state.XMID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/pjxmgl/selectPjxmById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        XMID: state.XMID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      if(type==='submit'){
        params.SHZT='1'
      }
      state.loading=true
      axiosUtil.post('/backend/sckhpj/pjxmgl/savePjxmForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }


    const getEjdwList = () => {
      axiosUtil.get('/backend/common/selectEjdwList',null).then(res=>{
        state.EJDWOptions=res.data || []
      })
    }

    const getZyxxTree = () => {
      axiosUtil.get('/backend/sckhpj/pjxmgl/selectXmzyList',null).then(res=>{
        let resData=res.data || []
        state.XMZYList=resData
        state.XMZYTree=comFun.treeData(resData,'ZYBM','FZYBM','children','0')
      })
    }

    const getDWXZRes = (value) => {
      state.formData.DWMC=value.DWMC
      state.formData.CBSDWQC=value.CBSDWQC
      state.formData.DWWYBS=value.DWWYBS
      state.formData.CBSWYBS=value.CBSWYBS
      state.dialogDWXZVisible=false
    }
    const getUserTwoOrg = () =>{
      axiosUtil.get('/backend/common/selectOrganByUserId',{USER_ID: vsAuth.getAuthInfo().permission.userId}).then(res=>{
        console.log(res,'resresresrser');
        if(res.data && !state.formData.JSDWID){
          state.formData.JSDWID = res.data[0].ORGNA_TWO_ID
          state.formData.JSDWMC = res.data[0].ORGNA_TWO_NAME
        }
      })
    }
    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      getEjdwList()
      if(props.params.operation == 'add'){
        getUserTwoOrg()
      }
      getZyxxTree()
    })

    return {
      ...toRefs(state),
      getDWXZRes,
      closeForm,
      saveData

    }
  }

})
</script>

<style scoped>

</style>
