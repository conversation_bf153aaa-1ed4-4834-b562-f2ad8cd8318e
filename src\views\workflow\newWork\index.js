/**
 * 流程API
 */
import axios from 'axios';

export default {
    //流程引擎地址(正式)
    wfmethod : "http://localhost:8888/vsflow/rest/workflow/",


    //获取待办列表
    getDbTasksListUrl: "/vsflow/rest/workflow/getWithoutFinishedDataForPagianted",
    //获取已办列表
    getYbTasksListUrl: "/vsflow/rest/workflow/getMyFinishedTaskForPagianted",

    //获取待办列表
    getTasksListUrl: "/backend/wfCallback/selectTaskList",
    //获取监控信息
    getTasksMonitorUrl: "/vsflow/rest/wfClient/monitorTasks",
    //获取审核按钮信息
    getApproveValueUrl: "/vsflow/rest/wfClient/getApproveValue",
    //获取流程审核页面
    getTaskInfoUrl: "/vsflow/rest/wfClient/getTaskInfo",
    //创建任务
    createTaskUrl: "/vsflow/rest/workflow/start",
    //结束任务
    finishTaskUrl: "/vsflow/rest/workflow/complete",
    //终止任务
    terminatedUrl: "/vsflow/rest/workflow/terminatedProcessInstance",

    //流程实例列表
    processInstanceList: "/vsflow/rest/workflow/processInstance/list",
    //删除流程实例
    deleteProcessInstance: "/vsflow/rest/workflow/deleteProcessInstance",
    
    //撤回任务
    repealTaskUrl: "/vsflow/rest/workflow/repealedTask",
    //获取跟踪数据
    getMonitorListUrl:'/backend/wfCallback/selectTaskMonitor',
    monitorInfo:'/vsflow/rest/workflow/monitorInfo',
    //流程节点回调地址
    processTaskCallBackUrl:'/backend/wfCallback/processTaskCallBack',
    //发起流程时 流程节点回调地址
    createTaskCallBackUrl:'/backend/wfCallback/createTaskCallBackUrl',
    //获取流程图数据
    getLctDataListUrl:'/backend/wfCallback/selectLctData',
    //终止流程回调地址
    terminateTaskCallBackUrl:'/backend/wfCallback/terminateTaskCallBack',

    //应用code
    appId:'AUTHM',


    //流程信息
    processData:{
        //测试流程
        test:{
            processId:'17471164351859520',
            busiUrl: 'test',
            examUrl: {},
        },
        // //正式招投标资格申请
        // CBSZR_TBZGSQ:{
        //     processId:'17362512437082122',
        //     busiUrl: 'TBZGSQ',
        //     examUrl: {},
        // },
        // CBSZR_DWHB:{
        //     processId:'17367383939694445',
        //     busiUrl: 'dwhb',
        //     examUrl: {},
        // }
    },
    //映射关系 processId查询twfprocesses表获得
    processMap:{
        '17471164351859520' : 'test',
        // '17362512437082122' : 'CBSZR_TBZGSQ',
        // '17367383939694445' : 'CBSZR_DWHB'
    },
    /**
     * 创建任务
     * @param processId / 流程id /（必填）
     * @param processName / 流程名称 / （必填）
     * @param founder / 发起人 / （必填）
     * @param processInstanceId / 流程实例id，用于在外部生成实例id的情况 / （非必填）
     * @param nextPerformers / 指定下节点处理人，多个用“;”分隔 / （非必填）
     * @param toActivityId / 强制指定的下一节点 / （非必填）
     * @param tzbl 跳转变量，流程图中使用shbl参数
     */
    async createTask(processId, processName, founder, processInstanceId, nextPerformers, toActivityId, tzbl) {
        //返回数据
        let resultData = {
            success:true,
            message:''
        };

        //创建任务参数
        let params = {
            processId:processId,
            processName:processName,
            founder:founder
        }
        //判断流程实例
        if(processInstanceId){
            params.processInstanceId = processInstanceId;
        }
        //判断指定办理人
        if(nextPerformers){
            params.nextPerformers = nextPerformers;
        }
        //判断指定节点
        if(toActivityId){
            params.toActivityId = toActivityId;
        }
        //判断节点跳转
        if(tzbl){
            params.shbl = tzbl;
        }else{
            params.shbl = 1;
        }
        //发起任务
        await axios({
            method: "post",
            url: this.createTaskUrl,
            params: params
        }).then((resp) => {
            if(resp.data && resp.data.meta && resp.data.meta.success){
                resultData.message = '提交成功！';
                // this.createTaskCallBack(processId,processInstanceId);
            }else{
                resultData.success = false;
                resultData.message = '提交失败';
            }
        }).catch((error) => {
            resultData.success = false;
        });

        return resultData;
    },




    
    /**
     * 完成任务
     * @param taskId / 任务ID /（必填）
     * @param performer / 当前任务执行者 / （必填）
     * @param suggestflg / 是否同意，0 否 1是 / （非必填）
     * @param result / 审批意见内容 / （非必填）
     * @param nextPerformers / 指定下节点处理人，多个用“;”分隔 / （非必填）
     * @param toActivityId / 强制指定的下一节点 / （非必填）
     * @param tzbl 跳转变量，流程图中使用shbl参数
     * @param dbid 待办id，从统一待办进入办理时，根据待办id获取办理信息 / (非必填，送审稿用)
     * @param sfcxzlc 是否重新走流程，1是;0否
     */
     async finishTask(taskId,performer,suggestflg,result,nextPerformers,toActivityId,tzbl,dbid,sfcxzlc){
        //返回数据
        let resultData = {
            success:true,
            message:''
        };

        //创建任务参数
        let params = {
            taskId:taskId,
            performer:performer
        }
        //判断是否同意
        if(suggestflg){
            params.suggestflg = suggestflg;
        }
        //判断审批内容
        if(result){
            params.result = result;
        }
        //判断指定办理人
        if(nextPerformers){
            params.nextPerformers = nextPerformers;
        }
        //判断指定节点
        if(toActivityId){
            params.toActivityId = toActivityId;
        }
        //判断节点跳转
        if(tzbl){
            params.shbl = tzbl;
        }else{
            params.shbl = 1;
        }

        params.sfcxzlc = sfcxzlc;
            
        //完成任务
        await axios({
            method: "post",
            url: this.finishTaskUrl,
            params: params
        }).then((resp) => {
            if(resp.data && resp.data.meta && resp.data.meta.success){
                resultData.message = '办理成功！';
                // this.processTaskCallBack(taskId,tzbl,dbid);
            }else{
                resultData.success = false;
                resultData.message = '办理失败';
            }
        }).catch((error) => {
            resultData.success = false;
            // if (error.indexOf('请求被中断') !== -1) {
            //     resultData.message = '办理失败';
            // } else {
            //     resultData.message = '';
            // }
        });

        return resultData;

    },





     /**
     * 获取待办列表
     * @param status 状态 1 待办 9 已办
     * @param page / 页号 / （必填）
     * @param rows / 每页行数 / （必填）
     * @param userId / 用户编码 / （非必填）
     * @param processInstanceName / 实例名称 / （非必填）
     * @param lclx / 流程类型 / （非必填）
     */
    async getTaskList(status,page,rows,userId,processInstanceName, lclx){

        let resultMap = { };

        //创建任务参数
        let params = {
            appId:this.appId,
            status:status,
            page:page,
            rows:rows,
        }
        //判断用户编码
        if(userId){
            params.yhid = userId;
        }
        //判断流程实例名称
        if(processInstanceName){
            params.processInstanceName = processInstanceName;
        }
        //判断流程类型
        if(lclx){
            params.lclx = lclx;
        }

        
        await axios({
            method: "post",
            url: this.getTasksListUrl,
            data: params
        }).then((resp) => {
            if(resp.data && resp.data.data && resp.data.data){

                resultMap = resp.data.data;
            }
        });

        return resultMap;
    },



    
    /**
     * 获取流程图数据
     * @param processInstanceId / 流程实例ID / （必填）
     */
    async getMonitorList(processInstanceId){

        let resultMap = [];

        //创建任务参数
        let params = {
            processInstanceId:processInstanceId,
        }

        
        //流程跟踪
        await axios({
            method: "post",
            url: this.getMonitorListUrl,
            params: params
        }).then((resp) => {
            if(resp.data && resp.data.data && resp.data.data){
                resultMap = resp.data.data;
            }
        });
        
        return resultMap;
    },

    async getLctDataList(processInstanceId){

        let resultMapList = [];

        //创建任务参数
        let params = {
            processInstanceId:processInstanceId,
        }

        
        //流程跟踪
        await axios({
            method: "post",
            url: this.getLctDataListUrl,
            params: params
        }).then((resp) => {
            if(resp.data && resp.data.data && resp.data.data){
                resultMapList = resp.data.data;
            }
        });

        return resultMapList;
    },

    createTaskCallBack(processId,processInstanceId){
        let resultMap = [];

        //创建任务参数
        let params = {
            processId:processId,
            processInstanceId:processInstanceId,
        }

        
        //流程跟踪
        axios({
            method: "post",
            url: this.createTaskCallBackUrl,
            params: params
        }).then((resp) => {
            if(resp.data && resp.data.data && resp.data.data){
                resultMap = resp.data.data;
            }
        });

        return resultMap;
    },


    /**
     * 流程节点回调（用于完成任务时回调，发起任务暂无操作）
     * @param taskId / 待办ID
     * @param dbid / 从统一待办进入办理时，统一待办id
     */
     processTaskCallBack(taskId,tzbl,dbid){

        let resultMap = [];

        //创建任务参数
        let params = {
            taskId:taskId,
            tzbl:'',
            dbid:'',
        }
        //判断用户编码
        if(tzbl){
            params.tzbl = tzbl;
        }
        //判断待办id
        if(dbid){
            params.dbid = dbid;
        }

        
        //流程跟踪
        axios({
            method: "post",
            url: this.processTaskCallBackUrl,
            params: params
        }).then((resp) => {
            if(resp.data && resp.data.data && resp.data.data){
                resultMap = resp.data.data;
            }
        });

        return resultMap;
    },
    
    /**
     * 终止任务
     * @param processInstanceId / 流程实例ID / （必填）
     * @param oprNo  / 用户ID / （必填）
     * @param oprName  / 用户名称 / （必填）
     */
    async terminatedTask(processInstanceId, userId, userName){
        let resultMap = {
            success:true,
            message:''
        };

        //创建任务参数
        let params = {
            processInstanceId: processInstanceId,
            oprNo: userId,
            oprName: userName,
        }

        //终止任务
        await axios({
            method: "get",
            url: this.terminatedUrl,
            params: params
        }).then((resp) => {
            if(resp.data && resp.data.meta && resp.data.meta.success){
                resultMap.message = '办理成功！';
                // this.terminateTaskCallBack(processInstanceId);
            }else{
                resultMap.success = false;
                resultMap.message = '办理失败';
            }
        });
        return resultMap;
    },

    /**
     * 终止任务回调函数
     * @param processInstanceId / 流程实例ID / （必填）
     */
    terminateTaskCallBack(processInstanceId){
        let resultMap = [];
        //创建任务参数
        let params = {
            processInstanceId:processInstanceId,
        }
        axios({
            method: "post",
            url: this.terminateTaskCallBackUrl,
            params: params
        }).then((resp) => {
            if(resp.data && resp.data.data && resp.data.data){
                resultMap = resp.data.data;
            }
        });

        return resultMap;
    },


}