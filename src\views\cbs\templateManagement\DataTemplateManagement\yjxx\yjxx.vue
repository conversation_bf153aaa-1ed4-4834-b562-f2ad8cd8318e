<template>
  <div style="height: calc(100% - 40px);">
        <el-form :disabled="!editable" style="height: 100%;" class="lui-page">
            <el-table
                    highlight-current-row
                    size="default"
                    ref="table"
                    class="lui-table"
                    fit
                    height="100%"
                    :border="false"
                    :data="state.tableData"
            >
                <EleProTableColumn
                        v-for="prop in state.tableColumn"
                        :col="prop"
                        :key="prop.columnKey"
                >
                  <template #info="{ row }">
                    <el-tooltip :content="row[prop.prop]" placement="bottom" effect="light">
                      <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </template>
                    <template #select="{ row }">
                        <el-select
                                size="normal" v-model="row[prop.prop]" clearable placeholder="请选择"
                                @change="changePro(row)"
                        >
                            <el-option
                                    v-for="(item, index) in proDetails" :key="index" :value="item.ZYBM" :label="item.ZYMC"
                            ></el-option>
                        </el-select>
                    </template>
                    <template #input="{ row }">
                        <el-input v-model="row[prop.prop]" placeholder="请输入"></el-input>
                    </template>
                    <template #format="{ row }">
                        {{ row[prop.prop]?row[prop.prop].replace(" 00:00:00", ""): '' }}
                    </template>
                    <template #titleinput="{ row }">
                        <el-input v-if="row.SHZT != 1" v-model="row[prop.prop]" placeholder="请输入"></el-input>
                        <span v-else>{{ row[prop.prop] }}</span>
                    </template>
                    <template #opration="{ row, $index }">
                        <div>
                          <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
                            <el-button class="lui-table-button" @click="editRow(row, $index)">查看</el-button>
<!--                            <el-button class="lui-table-button" @click="copyRow(row, $index)">复制</el-button>-->
                            <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
                            <el-button v-if="row.SHZT != 1 && row.EXTENSION?.SJLY!=='MBSC'" class="lui-table-button" @click="deleteRow(row,$index)">删除</el-button>
                        </div>
                    </template>
                </EleProTableColumn>
            </el-table>
        </el-form>
        <el-dialog
                title="业绩信息查看"
                v-model="editVisible"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :destroy-on-close="true"
                top="100px"
                width="1200px"
                @close="()=>{}">
            <yjxxEdit :editData="editData" @updateData="updateData" @close="editVisible = false" :editable="false"/>
        </el-dialog>
    </div>

  <el-dialog
      custom-class="lui-dialog"
      title="信息项选择"
      v-model="state.chooseVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      top="50px"
      width="1200px"
      @close="() => {}"
  >
    <yjxxXz
        :key="editIndex"
        :currentRow="currentRow"
        @updateChooseData="updateChooseData"
        @updateEditData="updateEditData"
        @close="state.chooseVisible = false"
        :TYXYDM="TYXYDM"
    />
  </el-dialog>
</template>
<script setup>
import {
    reactive,
    computed,
    onMounted,
    ref,
    defineProps,
    nextTick,
    defineEmits,
    getCurrentInstance,
    watch,
} from "vue";
import {axios} from "@src/assets/core/index";
import {ElMessage, ElMessageBox, ElLoading} from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import yjxxEdit from './yjxxEdit.vue'
import {v4 as uuidv4} from "uuid";
import axiosUtil from "../../../../../lib/axiosUtil";
import yjxxXz from "./yjxx_xz.vue";
import {InfoFilled} from "@element-plus/icons-vue";

const state = reactive({
  chooseVisible:false,
    tableData: [{}],
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 55,
            align: "center",
        },
        {
            label: "专业名称",
            prop: "ZYMC",
            align: "center",
            showOverflowTooltip: true,
          width: 100,
            // slot: "select"
        },
      {
        label: "业务要求",
        prop: "YWYQ",
        align: "center",
        showOverflowTooltip: true,
        width: 80,
        slot: "info"
      },
      {
        label: "录入资料说明",
        prop: "LRZLSM",
        align: "center",
        showOverflowTooltip: true,
        width: 80,
        slot: "info"
      },
        {
            label: "信息项",
            prop: "XXXMC",
            align: "center",
            showOverflowTooltip: true,
            width: 150,
        },
        {
            label: "项目名称",
            prop: "XMMC",
            align: "center",
        },
        {
            label: "甲方单位",
            prop: "JSDW",
            align: "center",
        },
        {
            label: "合同开始日期",
            prop: "HTRQKS",
            align: "center",
            width: 150,
            slot: 'format'
        },
        {
            label: "合同结束日期",
            prop: "HTRQJS",
            align: "center",
            width: 150,
            slot: 'format'
        },
        {
            label: "合同金额(万元)",
            prop: "HTJE",
            align: "center",
            width: 150,
        },
        {
            label: "操作",
            align: "center",
            width: 250,
            fixed: "right",
            slot: "opration",
          hide: !props.editable
        },
    ],
});

const currentRow = ref({});
const refs = ref([]);

const updateChooseData = (val) => {
  changeData(currentRow.value,val,editIndex.value,false)
};

const changeData = (oldRow,newRow,index,visible) => {
  let params={
    newId: oldRow.DWYJID,
    oldId: newRow.YJZSJID,
    cover: true
  }

  axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
    if(oldRow.EXTENSION?.SJLY==='MBSC'){
      if(!newRow.EXTENSION){
        newRow.EXTENSION={
          SJLY: 'MBSC'
        }
      }else {
        newRow.EXTENSION.SJLY='MBSC'
      }
    }
    oldRow.EXTENSION=newRow.EXTENSION
    oldRow.YJZSJID=newRow.YJZSJID
    oldRow.XMMC=newRow.XMMC
    oldRow.JSDW=newRow.JSDW
    oldRow.HTJE=newRow.HTJE
    oldRow.HTRQKS=newRow.HTRQKS
    oldRow.HTRQJS=newRow.HTRQJS
    oldRow.GCFW=newRow.GCFW
    state.chooseVisible = visible;
  })
}


const updateEditData = (row) => {
  state.tableData.forEach((item,index)=>{
    if(item.YJZSJID===row.YJZSJID){
      changeData(item,row,index,true)
    }
  })
}

const chooseRow = (row, index) => {
  currentRow.value=row;
  editIndex.value = index;
  state.chooseVisible = true;
};

const props = defineProps({
    defaultData: {
        type: Array,
        defaultData: () => [],
    },
    proDetails: {
        type: Array,
        defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
        type: Object,
        default: () => null,
    },
    // 是否查看模式
    editable: {
        type: Boolean,
        default: true
    },
  TYXYDM: {
    type: String,
    default: ''
  }
});
const changePro = (row) => {
    row.ZYMC = props.proDetails.filter(x => x.ZYBM == row.ZYBM)[0].ZYMC
}
watch(() => props.defaultData, val => {
    if(val){
        val.forEach(x=> {
            const UUID = uuidv4().replace(/-/g, '');
            x.DWYJID = x.DWYJID || UUID;
            x.YJWYBS = x.YJWYBS || UUID;
        })
    }
    state.tableData = val;
}, {
    immediate: true
})
const copyRow = (row, index) => {
    const UUID = uuidv4().replace(/-/g, '');
    state.tableData.splice(index, 0, {...row, DWYJID: UUID, YJWYBS: UUID,SHZT:''});
};
const insertRow = (row, index) => {
    const UUID = uuidv4().replace(/-/g, '');
    state.tableData.splice(index + 1, 0, {
        DWYJID: UUID,
        YJWYBS: UUID,
        ZYMC: row.ZYMC,
        ZYBM: row.ZYBM,
        XXXMC: row.XXXMC,
        MBMXID: row.MBMXID,
      YWYQ: row.YWYQ,
      LRZLSM: row.LRZLSM,
        XXX: null,
        ZZXXMC: null,
        ZZDJ: null,
        YXKSRQ: null,
        YXJSRQ: null,
        FZBM: null,
        FJ: null,
    });
};
// 查看持证状况
const viewZS = (row) => {
    console.log(row);
};
const info = ref({});
const editVisible = ref(false);
const editIndex = ref(0);
const editData = ref({});
const editRow = (row, index) => {
    editIndex.value = index;
    editData.value = row;
    editVisible.value = true;
};
const updateData = (val, isAdd) => {
    console.log(val, isAdd);
    if (isAdd) {
        state.tableData.splice(editIndex.value, 1, val);
        const UUID = uuidv4().replace(/-/g, '');
        insertRow({
            DWYJID: UUID,
            YJWYBS: UUID,
            ZYMC: val.ZYMC,
            ZYBM: val.ZYBM,
            XXXMC: val.XXXMC,
            MBMXID: val.MBMXID,
        }, editIndex.value);
        editRow(state.tableData[editIndex.value + 1], editIndex.value + 1);
    } else {
        state.tableData.splice(editIndex.value, 1, val);
        editVisible.value = false;
    }
};
const deleteRow = (row, index) => {
    ElMessageBox.confirm('是否删除此条数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        state.tableData.splice(index, 1)
        ElMessage({
            message: '删除成功!',
            type: 'success'
        })

    }).catch(() => {

    })
}
const validateForm = () => {
        return new Promise((resolve, reject) => {
          if(state.tableData.find(item=>(item.SFBT=='1' && !item.YJZSJID))){
            reject({mgs:[{message:'请完善业绩信息！'}]})
          }else {
            resolve(true)
          }
        })
};
defineExpose({
  validateForm,
});

</script>
<style scoped>
>>> .el-table-fixed-column--right{
    background-color: rgba(255, 255, 255, 1) !important;
}
</style>
