<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="180px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="招标单位：" prop="ZGDWID">
            <div style="margin-left: 10px">{{ formData.XMXX.SSDWMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{ formData.XMXX.XMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMXX.XMBH }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="评标时间：" prop="KBSJ">
            <div style="margin-left: 10px">{{ formData.XMXX.PBRQ }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="评标地点：" prop="PBDD">
            <div style="margin-left: 10px">{{ formData.XMXX.PBDD }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="中标情况：" prop="ZBQK">
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-table ref="datatable91634" :data="formData.TZMXList" height="300px"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="BDMC" label="标段名称" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="BDNRSM" label="标段内容说明" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="TJZBSX" label="排序" align="center"
                             :show-overflow-tooltip="true" width="120"></el-table-column>
            <el-table-column prop="DWMC" label="中标单位名称" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="TJZB" label="推荐中标" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <div v-if="row.TJZB==='1'">是</div>
                <div v-if="row.TJZB==='0'">否</div>
              </template>
            </el-table-column>
            <el-table-column prop="SFZB" label="是否中标" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <div v-if="row.SFZB==='1'">是</div>
                <div v-if="row.SFZB==='0'">否</div>
              </template>
            </el-table-column>

            <el-table-column prop="BJ" label="中标价" align="center"
                             :show-overflow-tooltip="true" width="100">
              <template #default="{row,$index}">
                <div v-if="row.SFZB==='1'">{{row.BJ}}</div>
              </template>
                             </el-table-column>
            <el-table-column prop="XMJL" label="项目负责人" align="center"
                             :show-overflow-tooltip="true" width="120"></el-table-column>
            <el-table-column prop="FWQX" label="服务期限" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <el-form-item label="" :prop="`TZMXList.${$index}.FWQX`" label-width="0" :rules="row.SFZB==='1' ? rules.FWQX : null">
                  <el-input v-model="row.FWQX" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="YQHTQDSJ" label="要求合同签订时间" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <el-form-item label="" :prop="`TZMXList.${$index}.YQHTQDSJ`" label-width="0" :rules="row.SFZB==='1' ? rules.YQHTQDSJ : null">
                  <el-input v-model="row.YQHTQDSJ" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="YQHTQDDD" label="要求合同签订地点" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <el-form-item label="" :prop="`TZMXList.${$index}.YQHTQDDD`" label-width="0" :rules="row.SFZB==='1' ? rules.YQHTQDDD : null">
                  <el-input v-model="row.YQHTQDDD" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="BZ" label="备注" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <el-form-item label="" :prop="`TZMXList.${$index}.GQ`" label-width="0">
                  <el-input v-model="row.BZ" type="text" placeholder="请输入" :disabled="!editable"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="CZ" label="操作" align="center" fixed="right"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row,$index}">
                <el-button size="small" class="lui-table-button" type="primary"
                           @click="viewTzs(row)">预览通知书
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="同时发布中标结果公告：" prop="SFFBGG">
            <el-button style="margin-top: 4px;margin-left: 10px" type="primary" @click="dialogYLGGVisible=true">预览公告</el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogYLGGVisible"
        v-model="dialogYLGGVisible"
        :title="`${GJC[formData.XMXX.XSFS][0]}结果公告预览`"
        z-index="1000"
        width="1200px">
      <div style="padding: 20px">
        <div style="width: 100%;text-align: center">
          <h2>{{ formData.XMXX.XMMC }}{{GJC[formData.XMXX.XSFS][0]}}结果公示</h2>
          <h4>发布日期：{{ formData.CJSJ }}</h4>
        </div>

        <el-row :gutter="0" class="grid-row">
          <el-col :span="14" class="grid-cell" :offset="2">
            <h3 style="margin-left: 10px">{{GJC[formData.XMXX.XSFS][1]}}项目名称：{{ formData.XMXX.XMMC }}</h3>
          </el-col>

          <el-col :span="8" class="grid-cell">
            <h3 style="margin-left: 10px">{{GJC[formData.XMXX.XSFS][1]}}项目编号：{{ formData.XMXX.XMBH }}</h3>
          </el-col>

        </el-row>

        <div style="padding: 20px 100px;min-height: 300px;overflow: auto">
          <div class="ql-editor" v-html="makeJggg()"></div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogZBTZVisible"
        v-model="dialogZBTZVisible"
        :title="`${GJC[formData.XMXX.XSFS][0]}通知书预览`"
        z-index="1000"
        width="800px">
      <div>
        <zbtzsView :params="ZTParams"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import zbtzsView from "@views/zbxsgl/zbtzsqf/zbtzsView";


export default defineComponent({
  name: '',
  components: {zbtzsView},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      ZBTZID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        SFFBGG: '1',

        XMXX: {
          XSFS: 'GKZB'
        },

        TZMXList: [],
      },
      rules: {
        FWQX: [{
          required: true,
          message: '请输入',
        }],
        YQHTQDSJ: [{
          required: true,
          message: '请输入',
        }],
        YQHTQDDD: [{
          required: true,
          message: '请输入',
        }],
      },

      GJC: {
        JB: ['成交','采购','响应'],
        JJ: ['成交','采购','响应'],
        GKJB: ['成交','采购','响应'],
        GKJJ: ['成交','采购','响应'],
        GKZB: ['中标','招标','投标'],
        YQZB: ['中标','招标','投标'],
      },


      dialogZBTZVisible: false,
      ZTParams:{},

      dialogYLGGVisible: false,
    })

    const getFormData = () => {
      let params={
        ZBTZID: state.ZBTZID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/zbtzqf/selectZbtzqfById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        ZBTZID: state.ZBTZID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      if(type==='submit'){
        params.SHZT='2'
        if(params.SFFBGG==='1'){
          params.TZGGXX={
            JGGSID: comFun.newId(),
            FAID: params.FAID,
            TZLX: 'ZBGS',
            GGNR: makeJggg(),
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '2',
            YWZT: '1',
          }
        }
        params.XXTX = {
          YWLX: '中标结果通知',
          YWMC: '关于 ' + params.XMXX.XMMC + ' 的中标结果通知',
          BUSINESSID: state.ZBTZID,
          FSR: state.userInfo.userName,
          FSRZH: state.userInfo.userLoginName,
          SHZT: '0',
          JSSJ: comFun.getNowTime(),
          BYZD1: 'zbjgtz'
        }

      }
      state.loading=true
      axiosUtil.post('/backend/xsgl/zbtzqf/saveZbtzqfForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }


    const makeJggg = () => {
      let res=''

      let time=comFun.getNowDate().split('-')

      let zbrxx=`<p class="ql-align-right">
      <strong style="color: rgb(136, 136, 136);">
        <span class="ql-cursor">﻿</span>
    ${state.GJC[state.formData.XMXX.XSFS][1]}人：${state.formData.XMXX.SSDWMC}
    ${time[0]+'年'+time[1]+'月'+time[2]+'日'}
      </strong>
      <strong style="color: rgb(187, 187, 187);">
  </strong></p>
    <p></p><p><br></p>`

      state.formData.TZMXList.forEach(item=>{
        if(item.SFZB==='1'){
          res+=`<p>
      <strong style="color: rgb(136, 136, 136);">
      ${state.GJC[state.formData.XMXX.XSFS][1]}人已接受${item.DWMC}所递交的${item.BDMC}一标段${state.GJC[state.formData.XMXX.XSFS][2]}文件，确定${item.DWMC}为${state.GJC[state.formData.XMXX.XSFS][0]}人。
      </strong>
    </p>`
        }
      })
      res+=zbrxx
      return res
    }


    const getXmxx = () => {
      let params = {
        FAID: state.formData.FAID,
        XSJGID: state.formData.XSJGID
      }
      state.loading=true
      axiosUtil.get('/backend/xsgl/zbtzqf/selectZbjgByFaid', params).then(res => {
        state.formData.XMXX = res.data
        state.formData.TZMXList=res.data.TZMXList
        state.formData.TZMXList.forEach(item=>{
          Object.assign(item,{
            ZBTZMXID: comFun.newId(),
            ZBTZID: state.ZBTZID,
            CJRZH: state.userInfo.userLoginName,
            CJRXM: state.userInfo.userName,
            CJDWID: state.userInfo.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '0',
            YWZT: '1',
          })
        })
        state.loading=false
      })
    }

    const viewTzs = (row) => {
      axiosUtil.get('/backend/common/queryOrgByOrgId', {ORGNA_ID: state.formData.CJDWID}).then(res => {
        state.ZTParams={
          ...row,
          ...state.formData.XMXX,
          SSDW_TWO_NAME: res.data[0].ORGNA_TWO_NAME,
          BDZBR: state.formData.TZMXList.filter(item=>item.SFZB==='1' && item.FABDID===row.FABDID).map(item=>item.DWMC).join('、')
        }
        state.dialogZBTZVisible=true
      })
    }


    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      } else {
        state.formData.FAID = props.params.FAID
        state.formData.XSJGID = props.params.XSJGID
        getXmxx()
      }
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      makeJggg,
      viewTzs
    }
  }

})
</script>

<style scoped>
@import '@vueup/vue-quill/dist/vue-quill.snow.css';
</style>
