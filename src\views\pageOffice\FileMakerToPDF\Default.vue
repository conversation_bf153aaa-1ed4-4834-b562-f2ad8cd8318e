<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue';
import { filemakerctrl } from 'js-pageoffice';

const titleText = ref('');
const isButtonDisabled = ref(false);
const progressBarWidth = ref('0%');
const progressBarText = ref('0%');

onMounted(async () => {
  try {
    const response = await request({
      url: '/index',
      method: 'get',
    });
    titleText.value = response;
  } catch (error) {
    console.error('Failed to fetch title:', error);
  }
});

function ConvertFile() {
  document.getElementById("Button1").disabled = true;//设置按钮为不可用状态
  // 设置用于保存文件的服务器端controller地址,该地址需从"/"开始，指向服务器端根目录
  /** 如果想要给SaveFilePage传递参数，建议使用new URLSearchParams方式，例如：
* let saveFileUrl = "/FileMaker/save";
* let paramValue = new URLSearchParams({id:1,name:"张三"});
* filemakerctrl.SaveFilePage = `${saveFileUrl}?${paramValue.toString()}`;
*/
  filemakerctrl.SaveFilePage = "/FileMakerToPDF/save";

  filemakerctrl.CallFileMaker({
    // url：指向服务器端FileMakerCtrl打开文件的controller地址，该地址需从"/"开始，指向服务器端根目录  
    url: "/FileMakerToPDF/FileMakerToPDF",
    success: (res) => {
      console.log(res);
      console.log("completed successfully.");
      setProgress(100);
      isButtonDisabled.value = false;//设置按钮为可用状态
    },
    progress: (pos) => {
      console.log("running " + pos + "%");
      setProgress(pos);
    },
    error: (msg) => {
      console.log("error occurred: " + msg);
      isButtonDisabled.value = false;//设置按钮为可用状态
    },
  });
}

function setProgress(percent) {
  progressBarWidth.value = `${percent}%`;
  progressBarText.value = `${percent}%`;
}
</script>

<template>
  <div class="Word">
    <div style="text-align: center;">
      <h3 style="margin: 20px;">演示：动态填充数据到Word模板生成PDF文件的效果</h3>
      <div style="width: 650px; margin: 0 auto; font-size: 14px;">
        <p style="text-align: left;">
          演示内容：<br />
          &nbsp;&nbsp;&nbsp;&nbsp;本示例演示了调用FileMaker对象在客户端动态转换Word为PDF自动上传到服务器上，却不在线打开文件的功能，模拟了直接在服务器端转换生成PDF文件的效果。
        </p>
        <p style="text-align: left;">
          操作说明：<br />
          &nbsp;&nbsp;&nbsp;&nbsp;1. 点击“生成PDF文件”按钮，执行动态将荣誉证书模板“template.doc”转换为份PDF格式的荣誉证书文件。<br />
          &nbsp;&nbsp;&nbsp;&nbsp;2. 生成完毕后，即可在“FileMakerPDF/doc”目录下看到生成的PDF文件：zhengshu.pdf。
        </p>
      </div>
      <input id="Button1" type="button" value="生成PDF文件" @click="ConvertFile" :disabled="isButtonDisabled" />
      <div id="progressBarContainer">
        <div :style="{ width: progressBarWidth, height: '20px', backgroundColor: '#76b900', borderRadius: '5px', textAlign: 'center', lineHeight: '20px', color: 'white' }">
          {{ progressBarText }}
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
h3 {
  display: block;
  font-size: 1.17em;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  font-weight: bold;
}

#progressBarContainer {
  width: 500px;
  background-color: #e0e0e0;
  border-radius: 5px;
  padding: 3px;
  margin: 10px auto;
}

#progressBar {
  height: 20px;
  width: 0%;
  background-color: #76b900;
  border-radius: 5px;
  text-align: center;
  line-height: 20px;
  /* 使文字垂直居中 */
  color: white;
}
</style>