<!-- 采购项目管理--编辑页 -->
<template>
  <div>
    <el-form class="lui-card-form" :model="formData" ref="vForm" :rules="rules" label-position="right" label-width="110px"
             size="default" status-icon>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目类别:" prop="XMLB" >
            <el-select v-model="formData.XMLB" class="full-width-input" clearable :disabled="!editable">
              <el-option v-for="(item, index) in XMLBOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="所属单位:" prop="SSDW" >
            <el-select v-model="formData.SSDW" class="full-width-input" clearable :disabled="!editable">
              <el-option v-for="(item, index) in SSDWOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目编码:" prop="XMBM" >
            <el-input v-model="formData.XMBM" type="text" placeholder="请输入" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label=" 项目名称:" prop="XMMC" >
            <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目额度:" prop="XMED" >
            <el-input v-model="formData.XMED" type="text" placeholder="请输入" clearable :disabled="!editable">
            <template #append>万元</template>
          </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="创建人:" prop="CJRMC" >
            <el-input v-model="formData.CJRMC" type="text" placeholder="请输入" clearable disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="创建时间:" prop="CJSJ" >
            <el-input v-model="formData.CJSJ" type="text" placeholder="请输入" clearable disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div style="width: 100%;display: flex;justify-content: center;margin-top: 20px">
        <el-row :gutter="12" style="width: 300px;">
          <el-col  v-if="editable" :span="8">
            <el-button type="success" @click="validateForm('save')">暂存</el-button>
          </el-col>
          <el-col  v-if="editable" :span="8">
            <el-button type="primary" @click="validateForm('submit')">提交</el-button>
          </el-col>
          <el-col :span="8">
            <el-button @click="closeForm()">返回</el-button>
          </el-col>
        </el-row>
      </div>
    </el-form>

  </div>
  </template>

  <script>
  import {
    defineComponent,
    toRefs,
    reactive,
    getCurrentInstance,
    onMounted
  }
    from 'vue'
  import axiosUtil from "../../../lib/axiosUtil";
  import vsAuth from "../../../lib/vsAuth.js";
  import {ElMessage} from 'element-plus'
  import comFun from "../../../lib/comFun";
  import Vsfileupload from "../../components/vsfileupload";
  export default defineComponent({
    components: {Vsfileupload},
    props: {
      params: {
        type: Object,
        required: true
      }
    },

    setup(props, context) {
      const state = reactive({
        editable:props.params.editable,
        operation:props.params.operation,
        id:props.params.id,

        userInfo: vsAuth.getAuthInfo().permission,
        formData: {
          XMLB: null,
          SSDW: null,
          XMBM: null,
          XMMC: null,
          XMED:null,
          CJRMC: vsAuth.getAuthInfo().permission.userName,
          CJSJ: comFun.getNowTime(),
        },
        rules: {
           XMLB: [{
            required: true,
            message: '字段值不可为空',
          }],
          SSDW: [{
            required: true,
            message: '字段值不可为空',
          }],
          XMBM: [{
            required: true,
            message: '字段值不可为空',
          }],
           XMMC: [{
            required: true,
            message: '字段值不可为空',
          }],
           XMED: [{
            required: true,
            message: '字段值不可为空',
          }],
           CJRMC: [{
            required: true,
            message: '字段值不可为空',
          }],
           CJSJ: [{
            required: true,
            message: '字段值不可为空',
          }],
        },
        XMLBOptions: [],
        SSDWOptions: [],
      })
      const instance = getCurrentInstance()
      const submitForm = (type) => {
        let params = {
          ...state.formData
        }
        if (state.operation != 'add') {
          params.XGR = state.userInfo.userLoginName
          params.XGSJ = comFun.getNowTime();
        } else {
          params.CGXMBS = props.params.id
          params.CJR = state.userInfo.userLoginName
          params.CJSJ = comFun.getNowTime();
        }
        if(type=='submit'){
          params.SHR = state.userInfo.userLoginName
          params.SHRQ = comFun.getNowTime();
        }
        params.tableData = state.ckzjMxData
        return new Promise(resolve => {
          axiosUtil.post('/backend/zjgl/cgxmgl/saveCgxmData', params).then((res) => {
            if (res.message === 'success') {
              resolve(true)
              ElMessage({
                message: `${type === 'submit' ? '提交' : '保存'}成功`,
                type: 'success',
              })
               if (type == 'submit') {
                 context.emit("closeForm")
               }
            } else {
              resolve(false)
            }
          });
        })
      }
      const download = () => {

      }
      const closeForm = () =>{
        context.emit("closeForm")
      }
      const getDMBData = async (DMLBID, resList) => {
          let params = {
          DMLBID
          }
          var res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
          state[resList] = res.data
      }
      const validateForm = async (type) => {
        return new Promise(resolve => {
          console.log(state.formData)
          if (type === 'submit') {
            console.log(state.formData)
            instance.proxy.$refs['vForm'].validate(valid => {
              if (valid) {
                state.formData.SHZT = '1'
                //TODO: 提交表单
                resolve(submitForm(type))
              } else {
                resolve(false)
              }
            })
          } else {
            state.formData.SHZT = '0'
            resolve(submitForm(type))
          }
        })
      }
      const queryCgxmForm = () => {
        let params = {
          CGXMBS: state.id
        }
        axiosUtil.get('/backend/zjgl/cgxmgl/queryCgxmForm', params).then((res) => {
          if (res.data.length > 0) {
            state.formData = {...res.data[0]}
          }
        });
      }


      onMounted(() => {
        let params = props.params
        if (params && params.operation != 'add') {
         queryCgxmForm()
        }
        getDMBData("XMLB", "XMLBOptions")
        // getDMBData("SSDW", "SSDWOptions")
      })
      return {
        ...toRefs(state),
        submitForm,
        validateForm,
        closeForm,
        getDMBData,queryCgxmForm
      }
    }
  })
  </script>

  <style scoped>
  :deep(.required .el-form-item__label):before {
    content: "*";
    color: #F56C6C;
    margin-right: 4px;
  }

  :deep(.is-required .el-form-item__label):before {
    content: "*";
    color: #F56C6C;
    margin-right: 4px;
  }

  :deep(.el-input.is-disabled .el-input__wrapper){
    background-color: white;
  }

  :deep(.el-textarea.is-disabled .el-textarea__inner){
    background-color: white;
  }




  .title {
    font-weight: bold;
  }
  .fjtsClass{
    font-size: 12px;
    margin-left: 10px;
  }
  </style>
