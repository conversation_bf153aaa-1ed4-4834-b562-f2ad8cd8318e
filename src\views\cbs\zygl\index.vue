<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="name">
          <el-select
              size="normal"
              v-model="queryParams.ZYJB"
              clearable
              placeholder="请选择专业级别"
          >
            <el-option
                v-for="(item, index) in levelOptions"
                :key="index"
                :value="item.value"
                :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="name">
          <el-input
              size="normal"
              v-model="queryParams.ZYMC"
              clearable
              placeholder="请输入专业名称"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="" prop="name">
          <el-input
              size="normal"
              v-model="queryParams.ZYBM"
              clearable
              placeholder="请输入专业编码"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell" style="display: flex;gap: 10px;text-align: right;">
        <el-button type="primary" @click="getTableData">
          <el-icon><Search/></el-icon>查询
        </el-button>
        <el-button type="primary" @click="exportData"><el-icon><Upload/></el-icon>导出</el-button>
        <el-button type="primary" @click="editVisible = true" class="lui-button-add">
          <el-icon><Plus/></el-icon>新增
        </el-button>
      </el-col>
    </el-row>


    <div class="table">

      <el-row>
        <el-table
            class="lui-table"
            :data="tableData"
            row-key="ZYBM"
            :style="{width: '100%'}" :border="true" :show-summary="false" size="default"
            height="calc(100% - 10px)"
            border
            :highlight-current-row="true" :cell-style="{padding: '8px 0 '}">
          <el-table-column
              label="序号"
              type="index"
              header-align="center"
              align="center"
              width="60"
          ></el-table-column>
          <el-table-column
              label="专业名称"
              prop="ZYMC"
              header-align="center"
              align=""
              min-width="200"
          ></el-table-column>
          <el-table-column
              label="专业级别"
              prop="ZYJB"
              header-align="center"
              align="center"
          ></el-table-column>
          <el-table-column
              label="专业分类"
              prop="ZYFL"
              header-align="center"
              align=""
          ></el-table-column>
          <el-table-column
              label="专业编码"
              prop="ZYBM"
              header-align="center"
              align="center"
          ></el-table-column>
          <el-table-column
              label="排序码"
              prop="PXH"
              header-align="center"
              align="center"
          ></el-table-column>

<!--          <el-table-column-->
<!--              label="是否自营"-->
<!--              prop="SFZY"-->
<!--              header-align="center"-->
<!--              align="center"-->
<!--          >-->
<!--            <template #default="{ row }">-->
<!--              <div>-->
<!--                {{ row.SFZY ? ["非自营", "自营"][Number(row.SFZY)] : "&#45;&#45;" }}-->
<!--              </div>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column
              label="范围描述"
              prop="ZYMS"
              header-align="center"
              align=""
          ></el-table-column>
          <el-table-column
              label="专业状态"
              prop="YXBZ"
              header-align="center"
              align=""
          >
            <template #default="{ row }">
              <div>
                {{ row.YXBZ==0 ? "无效":"有效" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
              label="操作"
              header-align="center"
              align="center"
              width="150"
              fixed="right"
          >
            <template v-slot="scope">
              <el-button class="lui-table-button" @click="handleEdit(scope.row, scope.$index)"
              >编辑</el-button
              >
              <el-button
                  class="lui-table-button"
                  @click="add(scope.row)">新增</el-button>
              <!-- <el-button
                type="text"
                @click="handleDelete(scope.row, scope.$index)"
                >停用</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row, scope.$index)"
                >启用</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </div>
  </el-form>
  <el-dialog
      custom-class="lui-dialog"
    title="专业编辑"
    v-model="editVisible"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    @close="
      () => {
        editData = null;
        getTableData();
      }
    "
  >
    <zyEdit :editData="editData" @close="editVisible = false" />
    <!-- <span slot="footer">
      <el-button @click=" = false">Cancel</el-button>
      <el-button type="primary" @click="">OK</el-button>
    </span> -->
  </el-dialog>
</template>

<script setup>
import outerBox from "@components/common/outerBox";
import zyEdit from "./zyEdit.vue";
import { ref, reactive, getCurrentInstance, onMounted } from "vue";
import axiosUtil from "@lib/axiosUtil";
import { ElMessage, ElMessageBox } from "element-plus";
import { getZyglZyglList } from "@src/api/sccbsgl";
import cascaderTree from "@src/lib/cascaderTree.js";
import {getZyglExport} from "../../../api/sccbsgl";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
const _this = getCurrentInstance();
let levelOptions = reactive([
  { label: "全部", value: "" },
  { label: "大类", value: "1" },
  { label: "中类", value: "2" },
]);
let zymcOptions = reactive([]);
const queryParams = reactive({
  ZYJB: "",
  ZYMC: "",
  ZYBM: "",
});
let tableData = ref([]);
const getTableData = () => {
  getZyglZyglList({ ...queryParams }).then(({ data }) => {
    tableData.value = new cascaderTree(data, "ZYBM", "FZYBM").init();
  });
};


const exportData = ()=>{
  let column = [[
    { field: 'ZYMC', title: '专业名称'},
    { field: 'ZYJB', title: '专业级别'},
    { field: 'ZYBM', title: '专业编码'},
    { field: 'PXH', title: '排序码'},
    { field: 'ZYFL', title: '专业分类'},
    { field: 'SFZY', title: '是否自营'},
    { field: 'ZYMS', title: '范围描述'},
    { field: 'YXBZ', title: '专业状态'},
  ]]
  let params = {
    title: "专业信息",
    name: "专业信息",
    params: queryParams,
    url: '/excel/zyxxExport',
  }
  params.column = column
  axiosUtil.exportExcel('/backend/excelExport/magicExcel', params)
}


onMounted(() => {
  getTableData();
});
const editVisible = ref(false);
const editData = ref(null);
const handleEdit = (row, index) => {
  editData.value = { ...row };
  editVisible.value = true;
};
const add = (row) => {
  editData.value = {
    ZYMC: "",
    FZYBM: row.ZYBM,
    FZYMC: row.ZYMC,
    ZYJB: Number(row.ZYJB)+1,
    ZYBM: "",
    ZYCJ: "",
    SFZY: "1",
    ZYFL: "",
    FWMS: "",
    tableData: [
      { qxjb: "管理部门", fzdw: "", fzr: "" },
      { qxjb: "油田监督部门", fzdw: "", fzr: "" },
    ],
  };
  editVisible.value = true;
}
const handleDelete = (row, index) => {
  ElMessageBox.confirm("是否删除当前数据？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ElMessage({
        message: "已删除",
        type: "success",
      });
    })
    .catch(() => {
      ElMessage({
        message: "已取消",
        type: "info",
      });
    });
};
</script>

<style scoped>
.container {
  height: 100%;
  width: 100%;
}
.outer-box-content {
  padding: 50px 120px;
  height: calc(100% - 80px);
}
.search {
  padding: 15px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border: 1px solid #dddddd;
}
.search > span {
  margin: 0 10px;
}
.search > span > .el-select,
.el-input {
  width: 200px;
}
.table {
  height: calc(100% - 110px);
}
.table > .el-row:first-child {
  height: 35px;
}
.table > .el-row:first-child .el-button {
  height: 30px;
}
.table > .el-row:last-child {
  height: calc(100% - 35px);
}
.table-col-btn > .el-button {
  margin-left: 0;
  padding: 0;
}
.table-col-btn > .el-button:last-child {
  margin-left: 5px;
}
.pagenation {
  height: 30px;
  display: flex;
  align-items: center;
  margin-top: 10px;
}
</style>
