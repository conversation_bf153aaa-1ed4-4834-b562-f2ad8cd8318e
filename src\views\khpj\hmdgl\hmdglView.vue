<template>
    <div>
      <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
               label-width="200px"
               size="default" v-loading="loading" @submit.prevent>
        <el-row>
          <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='CZYKXZ'">
            <el-form-item label="企业（队伍/人员）名称：" prop="CLDXMC">
              <el-input v-model="formData.CLDXMC" type="text" placeholder="请选择" clearable :disabled="true">
                <template #append v-if="editable">
                  <el-button @click="chooseCldx">选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
  
          <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='CZYKXZ' && formData.CLDXLX==='DW'">
            <el-form-item label="编号：" prop="DWBM">
              <el-input v-model="formData.DWBM" type="text" placeholder="请输入" clearable :disabled="true">
              </el-input>
            </el-form-item>
          </el-col>
  
  
          <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='SGLR'">
            <el-form-item label="企业名称：" prop="QYMC">
              <el-input v-model="formData.QYMC" type="text" placeholder="请输入" clearable disabled>
              </el-input>
            </el-form-item>
          </el-col>
  
          <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='SGLR'">
            <el-form-item label="统一信用代码：" prop="TYXYDM">
              <el-input v-model="formData.TYXYDM" type="text" placeholder="请输入" clearable disabled>
              </el-input>
            </el-form-item>
          </el-col>
  
          <el-col :span="24" class="grid-cell" v-if="formData.WHFS==='SGLR' && formData.CLDXLX==='DW'">
            <el-form-item label="队伍名称：" prop="DWMC">
              <el-input v-model="formData.DWMC" type="text" placeholder="请输入" clearable disabled>
              </el-input>
            </el-form-item>
          </el-col>
  
          <el-col :span="12" class="grid-cell" v-if="formData.WHFS==='SGLR' && formData.CLDXLX==='RY'">
            <el-form-item label="人员姓名：" prop="RYXM">
              <el-input v-model="formData.RYXM" type="text" placeholder="请输入" clearable disabled>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell no-border-bottom">
            <el-form-item label="纳入时间：" prop="HMDYY">
              <el-input v-model="formData.CJSJ" type="text" placeholder="请输入" clearable disabled>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell no-border-bottom">
            <el-form-item label="纳入黑名单原因：" prop="HMDYY">
              <el-input v-model="formData.HMDYY" :rows="3"
                        type="textarea" clearable
                        disabled/>
            </el-form-item>
          </el-col>
  
          <el-col :span="24" class="grid-cell no-border-bottom">
            <el-form-item label="相关附件：" prop="XGFJ">
              <vsfileupload style="margin-left: 10px" :busId="params.id"
                            :key="params.id"
                            :editable="false" ywlb="hmdfj"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
  
    </div>
  </template>
  
  <script>
  
  import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
  import axiosUtil from "@lib/axiosUtil";
  import vsfileupload from "@views/components/vsfileupload";
  
  
  export default defineComponent({
    name: '',
    components: {vsfileupload},
    props: {
      params: {
        type: Object,
        required: true
      },
    },
    setup(props, {emit}) {
      const state = reactive({
        loading: false,
        formData: {}
  
      })

      const getFormData = () => {
        state.loading=true
        axiosUtil.get('/backend/sckhpj/hmdgl/selectHmdById', {HMDID: props.params.HMDID}).then((res) => {
          state.formData=res.data
          state.loading=false
        })
      }

      onMounted(() => {
        getFormData()
      })
  
      return {
        ...toRefs(state),
        getFormData
  
      }
    }
  
  })
  </script>
  
  <style scoped>
  
  </style>
  