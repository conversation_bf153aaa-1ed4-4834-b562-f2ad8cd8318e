<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="userName">
            <el-input ref="input45296" placeholder="请输入角色名称" v-model="listQuery.roleName" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="userLoginName">
            <el-input ref="input45296" placeholder="请输入角色编码" v-model="listQuery.roleCode" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>


        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary"><el-icon><Search/></el-icon>查询</el-button>
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData"><el-icon><Plus/></el-icon>新建</el-button>
          </div>
        </el-col>

      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column v-if="true" prop="ROLE_NAME" label="角色名称" align="center"
                               :show-overflow-tooltip="true" width="260"></el-table-column>
              <el-table-column v-if="true" prop="ROLE_CODE" label="应用角色编号" align="center"
                               :show-overflow-tooltip="true" width="210"></el-table-column>
              <el-table-column v-if="true" prop="ROLE_DESC" label="角色描述" align="left"
                               :show-overflow-tooltip="true" min-width="150"></el-table-column>
              <el-table-column v-if="true" prop="CZ" label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button  size="small" class="lui-table-button"  type="primary" @click="editRow(scope.row)">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes" class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
      <el-dialog
          custom-class="lui-dialog"
          :close-on-click-modal="false"
          v-if="dialogVisible"
          v-model="dialogVisible"
          title="账号编辑"
          @closed="closeForm"
          z-index="1000"
          width="1200px">
        <div>
          <roleEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
        </div>
      </el-dialog>


    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import comFun from "../../../lib/comFun";
import roleEdit from "./roleEdit";
export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,roleEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      dialogVisible: false,
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params:{}
    })
    const getDataList = () => {
      const params={
        ...state.listQuery,
      }
      axiosUtil.get('/backend/common/jspz/selectRolePage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }
    // 新增
    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.params = {editable: true, id: row.ROLE_ID, operation: 'edit'}
      state.dialogVisible = true
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible=false
      getDataList()
    }
    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      addData,
      closeForm

    }
  }

})
</script>

<style scoped>

</style>
