<template>
  <div style="width: 100%" v-loading="loading">
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;
      border-bottom: 3px solid #2A96F9;padding-left: 10px;padding-right: 10px;text-align: center;padding-bottom: 2px">
        资格初审
      </div>

      <el-table :data="formData.PSXXList" border class="lui-table" :highlight-current-row="true" size="default"
                :cell-style="{ padding: '10px 0 ' }" height="calc(100vh - 580px)">
        <el-table-column type="index" width="50" fixed="left" label="" align="center"/>
        <el-table-column property="PJBZ" header-align="center" align="left"
                         label="初审内容"></el-table-column>
        <el-table-column v-for="item in formData.TBRList" :key="item"
                         header-align="center" align="center" :label="item.DWMC" width="220">
          <template #default="{row}">
            <div v-if="row.type === 'HJ'" style="display: flex;justify-content: center;align-items: center">
              <div class="greenStyle" v-if="row[item.DWWYBS] === '1' && row.type === 'HJ'">通过
              </div>
              <div class="redStyle" v-if="row[item.DWWYBS] === '0' && row.type === 'HJ'">不通过
              </div>
            </div>
              <div v-else style="display: flex;justify-content: center;align-items: center" >
                  <div  class="button-PS color-HG" v-if="row[`${row.PBBZMXBS}_${item.DWWYBS}`] === '1'" @click="changeZT(row,item.DWWYBS)">合格
                  </div>
                  <div class="button-PS color-BHG" v-if="row[`${row.PBBZMXBS}_${item.DWWYBS}`] === '0'" @click="changeZT(row,item.DWWYBS)">不合格
                  </div>
              </div>
          </template>
        </el-table-column>

      </el-table>

    </el-form>


    <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;"
         v-if="editable">
      <el-button size="default" type="primary" @click="saveData('save')">保存</el-button>
      <el-button size="default" type="primary" @click="saveData('submit')">提交</el-button>
      <el-button size="default" @click="getFormData">刷新</el-button>
    </div>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";

export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: false,
      editable: false,
      formData: {
        form:{},
        PSXXList: [],
        TBRList: []
      },
      rules: {}
    })

    const getFormData = () => {
        let params={
            jlid: props.params.JLID,
            kbjlid: props.params.KBJLID,
            pbpwbs: props.parentForm.RYXX.PBPWBS
        }
        state.loading=true
        axiosUtil.get('/backend/kpbyx/zgcs/queryPwzgcs', params).then((res) => {
            state.editable = res.data.form.SHZT === '0';
            state.formData = res.data;
            state.loading=false;
            computedRes(false);
        })

    }


    const computedRes = (isChange) => {
      let HJRow = {PJBZ: '初审结果', type: 'HJ'};
      for(let m=0;m<state.formData.TBRList.length;m++){
          let isHg = true;
          for(let n=0;n<state.formData.PSXXList.length;n++){
              if(state.formData.PSXXList[n].type !== 'HJ'){
                  if(state.formData.PSXXList[n][state.formData.PSXXList[n].PBBZMXBS + "_" + state.formData.TBRList[m].DWWYBS] === '0'){
                      isHg = false;
                  }
              }
          }
          HJRow[state.formData.TBRList[m].DWWYBS] = isHg ? '1' : '0';
      }
      if(isChange){
          state.formData.PSXXList[state.formData.PSXXList.length - 1] = HJRow;
      }else{
          state.formData.PSXXList.push(HJRow)
      }

    }

    const saveData = (type) => {
        if(state.formData.PSXXList.length < 2){
            ElMessage.warning('没有需要保存的数据！');
            return;
        }
        if(type === 'submit'){
            ElMessageBox.confirm('你确定要提交吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                submitForm(type);
            }).catch(() => {

            });
        }else{
            submitForm(type);
        }


    }

    const submitForm = (type) => {
        axiosUtil.post('/backend/kpbyx/zgcs/savePwzgcs', {
            PWPSBSBS: state.formData.form.PWPSBSBS || comFun.newId(),
            WJID: state.formData.form.WJID,
            PBPWBS: props.parentForm.RYXX.PBPWBS,
            PBBZBS: state.formData.form.PBBZBS,
            PSLXDM: state.formData.form.PSLXDM,
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
            SHZT: type==='submit' ? '1' : '0',
            tbrList: state.formData.TBRList,
            psList: state.formData.PSXXList,
            KPBYXID: props.params.KPBYXID
        }).then(res=>{
            if(res.data.success){
                ElMessage.success('保存成功！');
                getFormData();
            }else{
                ElMessage.error('保存失败！');
            }
        })
    }

    const changeZT = (row,DWWYBS) => {
        if(!state.editable){
            return;
        }
        row[row.PBBZMXBS + '_' + DWWYBS] = row[row.PBBZMXBS + '_' + DWWYBS] === '1' ? '0' : '1';
        computedRes(true);
    }

    onMounted(() => {
      getFormData()

    })

    return {
      ...toRefs(state),
      getFormData,
      saveData,
      changeZT

    }
  }

})
</script>

<style scoped>
.button-PS {
  width: 80px;
  height: 30px;
  border: 1px solid #8c939d;
  border-radius: 5px;
  cursor: pointer;
  line-height: 30px;
}

.color-HG {
  background-color: #99cdfd;
  color: black;
}

.color-BHG {
  background-color: #fff3dc;
  color: #bb7b00;
}

.greenStyle {
  color: #5eb416;
}

.redStyle {
  color: #ff0000;
}
</style>
