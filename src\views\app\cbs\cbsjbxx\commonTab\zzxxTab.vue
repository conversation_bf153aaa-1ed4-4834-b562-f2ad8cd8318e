<template>
  <div style="font-size: 14px;">
    <div style="margin-bottom: 10px;border: 1px solid #cbcbcb;padding: 5px"
         v-for="(item,index) in tableData" :key="index">
      <div style="font-weight: bold;color: #409EFF">{{item['ZYMC']}}</div>
      <div v-for="(iitem,iindex) in item.SJXXList" :key="iindex">
        <appRow label="信息项：" label-width="110px">{{iitem['XXXMC']}}</appRow>
        <appRow label="资质信息名称：" label-width="110px">{{iitem['ZSMC']}}</appRow>
        <appRow label="证书编号：" label-width="110px">{{iitem['ZSBH']}}</appRow>
        <appRow label="证书到期日期：" label-width="110px">{{iitem['YXQJS'] ? iitem['YXQJS'].replace(" 00:00:00", "") : ''}}</appRow>
        <appRow label="资质等级：" label-width="110px">{{iitem['ZSDJ']}}</appRow>
        <appRow label="资质附件：" label-width="110px">
          <vsfileupload
              :editable="false"
              :busId="iitem.ZSYWID"
              :key="iitem.ZSYWID"
              ywlb="DWZTBGFJ"
              busType="dwxx"
              :limit="100"
          ></vsfileupload>
        </appRow>
        <div style="width: 100%;border-bottom: 1px dashed #c4c4c4;margin-top: 10px;margin-bottom: 10px"
             v-if="iindex+1!==item.SJXXList.length"></div>
      </div>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import appRow from "@views/app/common/appRow";
import vsfileupload from "@views/components/vsfileupload";

export default defineComponent({
  name: '',
  components: {appRow,vsfileupload},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
  },



  setup(props, {emit}) {
    const state = reactive({
      tableData:[]
    })
    watch(()=>props.defaultData,()=>{
      if(props.defaultData){
        let res=[]
        props.defaultData.forEach(item=>{
          let ZYXX=res.find(ii=>item.ZYBM===ii.ZYBM)
          if(ZYXX){
            ZYXX.SJXXList.push(item)
          }else {
            res.push({
              ZYBM: item.ZYBM,
              ZYMC: item.ZYMC,
              SJXXList: [item]
            })
          }
        })
        console.log(res)
        state.tableData=res
      }
    },{immediate: true,deep: true})


    onMounted(() => {

    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>

</style>
