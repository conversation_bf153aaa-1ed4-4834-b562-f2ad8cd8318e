<template>
    <el-form ref="vForm" :model="formData" :rules="rules" label-position="left" size="default">
        <el-row :gutter="20" style="width: 100%;margin: 10px 0 ">
            <el-col :span="24" class="grid-cell" style="text-align: right">
                <el-button size="default" type="primary" @click="addData"> 新增 </el-button>
            </el-col>
        </el-row>
        <el-table 
            ref="dataTable" 
            :data="tableData" 
            height="calc(100vh - 180px)" 
            class="lui-table"
            border
            stripe
            size="default" 
            highlight-current-row>
            <el-table-column type="index" align="center" width="60">
            </el-table-column>
            <el-table-column label="排序号" header-align="center" align="center" width="200">
                <template #default="scope">
                    <el-input-number v-model="scope.row.PXH" :min="1" :max="100" :controls="false" />
                </template>
            </el-table-column>
            <el-table-column label="初审内容" header-align="center" align="left" >
                <template #default="scope">
                    <el-input v-model="scope.row.PJBZ" placeholder="请输入" clearable >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" header-align="center" align="center" width="200">
                <template #default="scope">
                    <el-button size="mini" type="text" @click="deleteRow(scope.$index)"> 删除 </el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-form>
    <el-row style="line-height: 50px;">
        <el-col :span="24" style="width: 100%;text-align: center;position: fixed;bottom: 20px;">
            <el-button size="default" type="primary" @click="saveData"> 保存 </el-button>
            <el-button size="default" @click="close"> 关闭 </el-button>
        </el-col>
    </el-row>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import vsAuth from "../../../lib/vsAuth";
const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
});
const emit = defineEmits(["closeDialog","reload"]);
const listQuery = ref({
    wjid: '',
    pslxdm: 'ZGCS'
});
const tableData = ref([]);
const rules = ref({});

onMounted(() => {
    listQuery.value.wjid = Object.assign({}, props.data).WJID;
    initData();
});

const initData = () => {
    axiosUtil.get('/backend/xsgl/xswj/queryPfbzList', listQuery.value).then((res) => {
        tableData.value = res.data.list;
    });
}

const addData = () => {
    tableData.value.push({
        PBBZMXBS: comFun.newId()
    });
}

const deleteRow = (index) => {
    tableData.value.splice(index,1)
}

const saveData = () => {
    for(let i=0;i<tableData.value.length;i++){
        if(!tableData.value[i].PXH || !tableData.value[i].PJBZ){
            ElMessage.warning('请将信息填写完整！');
            return false;
        }
    }
    let params = {
        WJID: listQuery.value.wjid,
        PBBZBS: comFun.newId(),
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        PSLXDM: listQuery.value.pslxdm,
        rows: tableData.value
    }
    axiosUtil.post('/backend/xsgl/xswj/savePfbz', params).then(res=>{
        if(res.data.success){
            ElMessage.success('保存成功！');
        }else{
            ElMessage.error('保存失败！');
        }
    })
}

const close = () => {
    emit('closeDialog');
}

defineExpose({});
</script>

<style scoped>
::v-deep .el-form-item--default {
    margin-bottom: 0px;
}
::v-deep .zdyform .el-form-item--default {
    margin-bottom: 6px;
}
::v-deep .el-form-item--default .el-form-item__content {
    line-height: 23px;
}
</style>