<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="12" class="grid-cell">
          <el-form-item label="合同序号：" prop="HTXH">
            <div style="margin-left: 10px">{{ formData.HTXH }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="合同编号：" prop="HTBH">
            <div style="margin-left: 10px">{{ formData.HTBH }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="合同名称：" prop="HTMC">
            <div style="margin-left: 10px">{{ formData.HTMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="合同类别：" prop="HTLB">
            <div style="margin-left: 10px">{{ formData.HTLB }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="计划金额(元)：" prop="JHJE">
            <div style="margin-left: 10px">{{ formData.JHJE }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="资金流向：" prop="ZJLX">
            <div style="margin-left: 10px">{{ formData.ZJLX }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="选商方式：" prop="XSFS">
            <div style="margin-left: 10px">{{ formData.XSFS }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMBH }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="主办部门：" prop="ZBBM">
            <div style="margin-left: 10px">{{ formData.ZBBM }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="经办人：" prop="JBR">
            <div style="margin-left: 10px">{{ formData.JBR }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="签订时间：" prop="QDSJ">
            <div style="margin-left: 10px">{{ formData.QDSJ }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="履行期限：" prop="LXQX">
            <div style="margin-left: 10px">{{ formData.LXQX }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="相对人：" prop="XDR">
            <div style="margin-left: 10px">{{ formData.XDR }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item label="收款执行人：" prop="SKZXR">
            <div style="margin-left: 10px">{{ formData.SKZXR }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
     <div style="width: 100%;margin-bottom: 10px;justify-content: center;display: flex">
        <el-button @click="closeForm">返回</el-button>
      </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
export default defineComponent({
  name: '',
  components: {axiosUtil,vsfileupload},
  props: {
    params: {
      type: Object,
      default: {},
    },
  },
  setup(props, {emit}) {
    const state = reactive({
        formData: {
            XMMC: '',
            XMBH: '',
            XMLB: '',
            JSDW: '',
        },
        fileList: [],
    })

    const queryDygd = (() =>{
        // axiosUtil.get('/backend/xsgl/dygl/queryDygd', props.params).then((res) => {
        //     state.formData = res.data;
        // });
    })
   
   const closeForm = (() =>{
       emit('close');
   })

    onMounted(() => {
    //   queryDygd();
    })

    return {
      ...toRefs(state),
      queryDygd,
      closeForm,
    }
  }

})
</script>

<style scoped>
</style>
