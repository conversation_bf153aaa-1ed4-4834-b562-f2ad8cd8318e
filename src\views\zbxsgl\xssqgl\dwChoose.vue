<template>
    <div>
        <el-form ref="vForm" class="lui-page" label-position="left" label-width="120px"
                 size="default">
            <el-row >
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="专业类别">
                        <el-cascader v-model="listQuery.ZYBM" :options="ZRZYTree" filterable
                                     :props="{label:'ZYMC',value:'ZYBM',emitPath: false}" @change="gclbChange"
                                     clearable/>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="单位/队伍名称" prop="DWMC">
                        <el-input ref="input45296" v-model="listQuery.DWMC" type="text" clearable style="width: 80%">
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" class="grid-cell">
                    <div class="static-content-item" style="display: flex;">
                        <el-button ref="button91277" @click="getDataList" type="primary">
                            查询
                        </el-button>
                      <el-button type="primary" @click="saveData()">确定</el-button>
                      <el-button @click="closeForm">返回</el-button>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <div class="container-wrapper">
                        <el-table ref="datatable91634" :data="tableData" height="400px"
                                  :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                                  @selection-change="handleSelectionChange"
                                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                            <el-table-column type="selection" width="55"/>
                            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                            <el-table-column prop="CBSDWQC" label="单位名称" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="DWMC" label="队伍名称" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                          <el-table-column prop="ZYMC" label="专业类别" align="center"
                                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
                          <el-table-column prop="TBLX" label="投标类型" align="center"
                                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
                        </el-table>
                        <el-pagination background v-model:current-page="listQuery.page"
                                       v-model:page-size="listQuery.size"
                                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                                       class="lui-pagination"
                                       @size-change="getDataList" @current-change="getDataList" :total="total">
                        </el-pagination>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="6" class="grid-cell">
                    <div style="cursor:pointer; color: #3c85fb" @click="openZttpdw">暂停投标的队伍>>></div>
                </el-col>
                <el-col :span="6" class="grid-cell">
                    <div style="cursor:pointer; color: #3c85fb">关键资料将要到期的队伍>>></div>
                </el-col>

            </el-row>

        </el-form>


        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="zttpdwDialog"
                v-model="zttpdwDialog"
                title="暂停投标的队伍"
                z-index="1000"
                top="6vh"
                width="900px">
            <div>
                <cbsViewList @close="zttpdwDialog=false" :params="cbsParams"/>
            </div>
        </el-dialog>

    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import axiosUtil from "@lib/axiosUtil";
    import {ElMessage} from "element-plus";
    import comFun from "@lib/comFun";

    export default defineComponent({
        name: '',
        components: {},
        props: {
            params: {
                type: Object,
                required: true
            },
        },
        setup(props, {emit}) {
            const state = reactive({
                listQuery: {
                    page: 1,
                    size: 10,
                },
                tableData: [],
                total: 0,
                checkList: [],

                FMLBOptions: [],
                FMQDXWOptions: [],
                ZRZYTree: [],
                zttpdwDialog: false,
            });

            const getDataList = () => {
                let params = {
                    ...state.listQuery,
                };
                axiosUtil.get('/backend/xsgl/xssqgl/selectDxyqdwPage', params).then(res => {
                    state.tableData = res.data.list || [];
                    state.total = res.data.total;
                })

            };

            const saveData = () => {
                if (state.checkList.length === 0) {
                    ElMessage.warning('请选择邀请单位');
                    return;
                }
                emit('submit', state.checkList);

            };

            const getZrzyList = () => {
                let params = {};
                axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
                    state.zrzyList = [...res.data];
                    state.ZRZYTree = comFun.treeData(res.data || [], 'ZYBM', 'FZYBM', 'children', '0');
                });
            };

            const handleSelectionChange = (value) => {
                state.checkList = value;
            };

            const closeForm = () => {
                emit('close');
            };

            const openZttpdw = () => {
                state.zttpdwDialog = true;
            };

            onMounted(() => {
                getZrzyList();
                getDataList();
            });

            return {
                ...toRefs(state),
                getDataList,
                closeForm,
                saveData,
                getZrzyList,
                handleSelectionChange

            }
        }

    })
</script>

<style scoped>

</style>
