<!--
 * @Description: 江汉市场管理门户-除首页剩余页面
 * @Author: WLF (<EMAIL>)
 * @Date: 2024-11-14 08:49:04
 * @LastEditTime: 2025-01-13 17:53:02
 * @FilePath: \jhscgl-xieyun-front\src\views\WebPortal\otherPage.vue
 * @copyright : Copyright (c) 2024 wlf
-->
<template>
    <div>
        <el-row :gutter="20">
            <el-col :span="6">
                <el-menu :default-active="activeIndex" class="my-el-menu" @select="handleSelect"
                    active-text-color="#ffffff">
                    <el-menu-item v-for="item in pageParams.menuList" :index="item.itemIndex" :key="item.itemIndex">
                        <el-icon>
                            <Menu />
                        </el-icon>
                        <template #title>{{ item.menuName }}</template>
                    </el-menu-item>
                </el-menu>
            </el-col>
            <el-col :span="18">
                <div style="height: 700px;margin-top:5px;border-top: 3px solid #b81515;">
                    <el-card class="box-card" shadow="never">
                        <template #header>
                            <div class="card-header">
                                <span class="tabs-lable">
                                    <span style="cursor: pointer;">{{ lable }}</span>
                                </span>
                                <el-button class="button" link>{{ pathLable }}</el-button>
                            </div>
                        </template>
                        <template v-if="['2','4','8'].includes(pageParams.activeIndex)">
                            <el-row justify="center">
                                <el-input placeholder="请输入标题" v-model="listQuery.BT" type="text" style="width: 300px" clearable></el-input>
                                <el-button style="margin-left: 5px" type="primary" @click="handleSelect(activeIndex)">查询</el-button>
                            </el-row>
                            <el-table :data="tableData" style="width: 100%" :show-header="false" height="555px" :cell-style="{ padding: '10px 0 10px 0' }" empty-text="&nbsp">
                                <el-table-column prop="name" show-overflow-tooltip>
                                    <template #default="{ row }">
                                        <el-tag size="mini" v-if="pageParams.activeIndex == '4'"
                                            style="border-radius: 40px;font-size: 8px;height: 18px;width: 40px;">
                                            <span :style="[row.GGZT == '1' ? 'color:#409eff' : 'color: #b81515']">
                                                {{ row.GGZT == '1' ? '公告中' : (row.GGZT == '9' ? '终止' : '公告截止') }}
                                            </span>
                                        </el-tag>
                                        <el-button link @click="openDialog(row)" style="font-size: 16px;">{{ row.BT }}</el-button>
                                        <img src="@static/img/webPortal/new.png" v-if="row.ISNEW == '1' && pageParams.activeIndex == '4'"
                                            style="width: 30px; height: 15px; margin-right: 5px;">
                                    </template>
                                </el-table-column>
                                <el-table-column prop="WHSJ" width="130"></el-table-column>
                            </el-table>
                            <el-pagination background v-model:current-page="listQuery.page"
                                v-model:page-size="listQuery.size" :page-sizes="[10, 20, 50, 100]"
                                layout="->,total, prev, pager, next, sizes" class="lui-pagination" @size-change="sizeChange"
                                @current-change="currentChange" :total="total">
                            </el-pagination>
                        </template>
                        <template v-if="pageParams.activeIndex == '3'">
                            <div v-if="listQuery.GZLX == 'CBSZYC'">
                                <el-table :data="tableData" style="width: 100%" :show-header="true" height="605px" :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                                    <el-table-column prop="ZYMC" label="专业" header-align="center" align="center"></el-table-column>
                                    <el-table-column prop="DW_COUNT" label="合计" header-align="center" align="center" width="80"></el-table-column>
                                    <el-table-column prop="CBS" label="民营企业" header-align="center" align="center" width="150" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="SHN" label="石化内" header-align="center" align="center" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="QTGYQY" label="其他国有企业" header-align="center" align="center" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="SYDW" label="事业单位" header-align="center" align="center" show-overflow-tooltip></el-table-column>
                                </el-table>
                            </div>
                            <div v-if="listQuery.GZLX == 'PJGS'">
                                <el-row style="margin-top: 10px;">
                                    <el-col :span="5">
                                        <el-select v-model="listQuery.PJZQ" style="width:100%;" class="full-width-input" placeholder="请选择考核周期" clearable @change="pjzqChange">
                                            <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC" :value="item.PJZQID"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="5">
                                        <el-input style="width:100%;" placeholder="请输入专业名称" v-model="listQuery.ZYMC" type="text" clearable>
                                        </el-input>
                                    </el-col>
                                    <el-button style="margin-left: 5px" type="primary" @click="handleSelect(activeIndex)">查询</el-button>
                                </el-row>
                                <el-table :data="tableData" style="width: 100%" :show-header="true" height="530px" :cell-style="{ padding: '10px 0 10px 0' }" empty-text="&nbsp">
                                    <el-table-column prop="ZYMC" label="考核专业" header-align="center" align="left">
                                        <template #default="{ row }">
                                            <el-button type="text" @click="jumpKhtj(row)">{{ row.ZYMC }}</el-button>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="DW_COUNT" label="队伍数量" header-align="center" align="center" width="100"></el-table-column>
                                </el-table>
                                <el-pagination background v-model:current-page="listQuery.page"
                                    v-model:page-size="listQuery.size" :page-sizes="[10, 20, 50, 100]"
                                    layout="->,total, prev, pager, next, sizes" class="lui-pagination" @size-change="sizeChange"
                                    @current-change="currentChange" :total="total">
                                </el-pagination>
                            </div>
                            <div v-if="listQuery.GZLX == 'HMD'">
                                <el-row justify="center">
                                    <el-input placeholder="请输入企业名称" v-model="listQuery.BT" type="text" style="width: 300px" clearable></el-input>
                                    <el-button style="margin-left: 5px" type="primary" @click="handleSelect(activeIndex)">查询</el-button>
                                </el-row>
                                <el-table :data="tableData" style="width: 100%" :show-header="true" height="555px" :cell-style="{ padding: '10px 0 10px 0' }" empty-text="&nbsp">
                                    <el-table-column prop="CLDXMC" label="企业（队伍/人员）名称" header-align="center" align="left" min-width="300" show-overflow-tooltip>
                                        <template #default="{ row }">
                                            <el-button type="text" @click="openHmdDialog(row)">{{ row.CLDXMC }}</el-button>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="CJSJ" label="纳入时间" header-align="center" align="center" width="250" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="CLDXLXMC" label="类型" header-align="center" align="center" width="150" show-overflow-tooltip></el-table-column>
                                </el-table>
                                <el-pagination background v-model:current-page="listQuery.page"
                                    v-model:page-size="listQuery.size" :page-sizes="[10, 20, 50, 100]"
                                    layout="->,total, prev, pager, next, sizes" class="lui-pagination" @size-change="sizeChange"
                                    @current-change="currentChange" :total="total">
                                </el-pagination>
                            </div>
                            <div v-if="listQuery.GZLX == 'CQYJ'">
                                <el-row justify="center">
                                    <el-input placeholder="请输入企业名称" v-model="listQuery.BT" type="text" style="width: 300px" clearable></el-input>
                                    <el-button style="margin-left: 5px" type="primary" @click="handleSelect(activeIndex)">查询</el-button>
                                </el-row>
                                <el-table :data="tableData" style="width: 100%" :show-header="true" height="555px" :cell-style="{ padding: '10px 0 10px 0' }" empty-text="&nbsp">
                                    <el-table-column prop="CBSDWQC" label="企业名称" header-align="center" align="left" width="300" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="DWMC" label="队伍名称" header-align="center" align="left" width="300" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="YJYY" label="预警说明" header-align="center" align="left" show-overflow-tooltip></el-table-column>
                                </el-table>
                                <el-pagination background v-model:current-page="listQuery.page"
                                    v-model:page-size="listQuery.size" :page-sizes="[10, 20, 50, 100]"
                                    layout="->,total, prev, pager, next, sizes" class="lui-pagination" @size-change="sizeChange"
                                    @current-change="currentChange" :total="total">
                                </el-pagination>
                            </div>
                        </template>
                        <template v-if="pageParams.activeIndex == '5'">
                            <el-row justify="center">
                                <el-input placeholder="请输入姓名" v-model="listQuery.BT" type="text" style="width: 300px" clearable></el-input>
                                <el-button style="margin-left: 5px" type="primary" @click="handleSelect(activeIndex)">查询</el-button>
                            </el-row>
                            <el-table :data="tableData" style="width: 100%" :show-header="true" height="555px" :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                                <el-table-column prop="XM" label="姓名" header-align="center" align="center"></el-table-column>
                                <el-table-column prop="ZJLBMC" label="专家类别" header-align="center" align="center" width="80"></el-table-column>
                                <el-table-column prop="ZC" label="职称" header-align="center" align="left" width="150" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="XCSZYMC" label="专业" header-align="center" align="left" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="GZDWMC" label="工作单位" header-align="center" align="left" show-overflow-tooltip></el-table-column>
                            </el-table>
                            <el-pagination background v-model:current-page="listQuery.page"
                                v-model:page-size="listQuery.size" :page-sizes="[10, 20, 50, 100]"
                                layout="->,total, prev, pager, next, sizes" class="lui-pagination" @size-change="sizeChange"
                                @current-change="currentChange" :total="total">
                            </el-pagination>
                        </template>
                    </el-card>
                </div>
            </el-col>
        </el-row>
        <el-dialog v-if="showTzggDialog" v-model="showTzggDialog" :title="title" @closed="closeDialog" top="1vh"
            z-index="1000" width="80%">
            <xxfbView v-if="showTzggDialog" :XXFBID="XXFBID" pageFlag="view" @closeForm="closeDialog"></xxfbView>
        </el-dialog>

        <el-dialog v-if="showKhtjDialog" v-model="showKhtjDialog" title="考核统计表" @closed="closeDialog" top="1vh"
            z-index="1000" width="80%">
            <khtj_list v-if="showKhtjDialog" :PJZQ="listQuery.PJZQ" :PJZYID="PJZYID"  @closeForm="closeDialog"></khtj_list>
        </el-dialog>

        <el-dialog custom-class="lui-dialog" :close-on-click-modal="false" v-if="dialogVisible" v-model="dialogVisible"
            :title="dialogTitle" @closed="closeDialog" z-index="1200" top="5vh" width="1200px">
            <div>
                <component :is="pageComponent[busType]" v-if="pageComponent[busType] && dialogVisible" :params="params"
                    @close="closeDialog" />
            </div>
        </el-dialog>
    </div>
</template>

<script>

import { defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount,markRaw } from "vue";
import axiosUtil from "@lib/axiosUtil";
import xxfbView from "../xxfb/common/xxfbView"
import xstzfbView from "@views/zbxsgl/xstzfb/xstzfbView";
import zbjggsView from "@views/zbxsgl/zbjggs/zbjggsView";
import zbtzggView from "@views/zbxsgl/zbtzsqf/zbtzggView";
import { Menu } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from "element-plus";
import vsAuth from "@lib/vsAuth";
import djtpgsView from "@views/zbxsgl/djtpgs/djtpgsView";
import hmdView from "@views/khpj/hmdgl/hmdglView.vue";
import khtj_list from "./khtj_list.vue";
export default defineComponent({
    name: '',
    components: { Menu,xxfbView,khtj_list},
    props: {
        pageParams: Object
    },
    setup(props, { emit }) {
        const state = reactive({
            activeIndex: '1',
            pageParams: props.pageParams,
            lable: '',
            pathLable: '',
            pathUrl:'',
            listQuery: {
                page: 1,
                size: 10,
                GZLX: '',
                flag: '',
                BT: '',
                ZYMC:'',
                PJZQ:'',
                orgid: vsAuth.getAuthInfo().permission.orgnaId,
                orgnaId: vsAuth.getAuthInfo().permission.orgnaId
            },
            total: 0,
            tableData: [],
            // 政策法规弹窗
            showTzggDialog: false,
            title: '',
            XXFBID: '',

            // 公告弹窗
            dialogVisible: false,
            dialogTitle: '',
            busType:'',
            params: {},
            pageComponent: {
                XSGG: markRaw(xstzfbView),
                CQGG: markRaw(xstzfbView),
                CGGG: markRaw(xstzfbView),
                JGGS: markRaw(zbjggsView),
                ZBGS: markRaw(zbtzggView),
                GG: markRaw(djtpgsView),
                HMD: markRaw(hmdView)
            },
            // 评价统计弹窗
            KHSJOptions: [],
            showKhtjDialog: false,
            PJZYID:''
        })
        const handleSelect = (key, keyPath) => {
            state.activeIndex=key
            state.lable = state.pageParams.menuList.find(val => val.itemIndex == key).menuName
            state.pathLable = state.pageParams.menuList.find(val => val.itemIndex == key).pathName
            state.listQuery.GZLX = state.pageParams.menuList.find(val => val.itemIndex == key).LX
            state.listQuery.flag = state.pageParams.menuList.find(val => val.itemIndex == key).flag
            state.pathUrl = state.pageParams.menuList.find(val => val.itemIndex == key).url
            loadTableData(state.pageParams.menuList.find(val => val.itemIndex == key).url)
        }
        const loadTableData = (url) => {
            let params = {
                ...state.listQuery
            }
            if (params.orgnaId) {
                if(state.listQuery.GZLX =='CBSZYC'){
                    params.orgnaId = null
                }
                axiosUtil.get('/backend/' + url, params).then((res) => {
                    if(state.listQuery.flag){
                        state.tableData = res.data.list?.map(item=>{
                            return {
                                ...item,WHSJ: item[state.listQuery.GZLX+'WCSJ']?.substring(0,10),
                                BT: item.XMMC+'-'+ state.pageParams.menuList.find(val => val.itemIndex == state.activeIndex).menuName
                            }
                        })
                    }else{
                        if(res.data.list){
                            state.tableData = res.data?.list
                            state.total = res.data.total
                        }else{
                            state.tableData = res.data.length ? res.data : []
                        }
                    }
                })
            }
        }
        const currentChange = (v) => {
            state.listQuery.page = v
            loadTableData(state.pathUrl)
        }
        const sizeChange = (v) => {
            state.listQuery.size = v
            loadTableData(state.pathUrl)
        }
        const closeDialog = () => {
            state.showTzggDialog = false
            state.dialogVisible = false
        }
        const openDialog = (row) => {
            if(props.pageParams.activeIndex == '2' || props.pageParams.activeIndex == '5'){
                state.title = row.GZLXMC
                state.XXFBID = row.XXFBID
                state.showTzggDialog = true
            }else{
                state.busType = row.GGLX
                state.params = {editable: false, id: '',operation: 'view'}
                if(['XSGG','CQGG','CGGG'].includes(state.busType)){
                    state.params.id = row.GGID
                    state.params.operation='msg'
                } 
                if(['JGGS'].includes(state.busType)){
                    state.params.id = row.JGGSID
                }
                if(['ZBGS'].includes(state.busType)){
                    state.params.id = row.ZBJGGSID
                }
                if (['GG'].includes(state.busType)) {
                    state.params.id = row.GGID
                }
                state.dialogVisible = true
            }

        }
        const jumpKhtj = (row) => {
            state.PJZYID = row.PJZYID
            state.showKhtjDialog = true
        }
        const getKhsjList = () => {
            axiosUtil.get('/backend/sckhpj/khmxtb/selectKhqjList', null).then(res => {
                state.KHSJOptions = res.data || []
                state.listQuery.PJZQ = state.KHSJOptions[0].PJZQID
            })
        }
        const openHmdDialog = (row) =>{
            state.dialogTitle = '黑名单查看'
            state.busType = 'HMD'
            state.params = {HMDID: row.HMDID,id: row.HMDID}
            state.dialogVisible = true
        }
        onMounted(() => {
            getKhsjList()
        })

        return {
            ...toRefs(state),
            handleSelect,
            closeDialog,
            openDialog,
            loadTableData,
            currentChange,
            sizeChange,
            openHmdDialog,
            jumpKhtj,
            getKhsjList
        }
    }

})
</script>

<style scoped>
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 10px;
}

.tabs-lable {
    font-family: "黑体", "Heiti", sans-serif;
    font-weight: bold;
    font-size: 18px;
}

.box-card {
    height: 99%;
}

.image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    /* 根据需要调整高度 */
}

.my-el-menu {
    width: 100%;
    height: 700px;
    margin-top: 5px;
    border-top: 3px solid #b81515;
}

/* 设置选中项的背景色 */
.my-el-menu .is-active {
    background-color: #b81515;
    /* 使用你想要的颜色代码 */
}
</style>