

/**
 *
 * dev认证为便于程序开发时不具备后端、认证与权限信息的情况下，自行组织用户信息与用户权限菜单。
 *
 */
const DEV = {
  //这里配置key来源自static\js\runtime\config.js文件的"__rest_base_path__"节点，
  '/restful/api': {
    // target: 'http://localhost:8088',
    target: 'http://************:8888',
    changeOrigin: true,
    pathRewrite: {
      '^/restful/api': '/apidata'
    }
  },
}

const SECURITY = {
  '/vseaf-service': {
    // target: 'http://************:8888',
    target: 'http://************:8888',
    changeOrigin: false,
    pathRewrite: {
      '^/vseaf-service': '/'
    }
  },
}

const CAS = {
  '/vseaf-service': {
    // target: 'http://************:8888',
    target: 'http://************:8888',
    changeOrigin: false,
    pathRewrite: {
      '^/vseaf-service': '/'
    }
  },
}

/**
 * sima认证请修改build\dev\dev.server.option.custom.js文件host节点为域名，同时下面的target节点也要使用域名
 * 如vseaf4.4框架输出如下：
 * ----------------------------------------------------------
 *	Application vseaf-4.4 is running! Access URLs:
 *	Local: 		  http://************:8888/backend/
 *	External: 	http://************:8888/backend
 * ----------------------------------------------------------
 *
 */
const SIAM = {

  //这里代理到后端siam登录地址，"http://ip:port{context-path}login/siam",context-path为application.properties文件内配置项，默认或留空为"/"
  '/login/siam': {
    // target:'http://************:8888',
    target: 'http://************:8888',
    changeOrigin: false,
    pathRewrite: {
      '^/login/siam': 'login/siam'
    }
  },

  //这里代理到后端工程框架地址，"http://ip:port{context-path}",context-path为application.properties文件内配置项，默认或留空为"/"
  '/vseaf-service': {
    // target:'http://************:8888',
    target: 'http://************:8888',
    changeOrigin: false,
    pathRewrite: {
      '^/vseaf-service': '/'
    }
  },

  /**
   * 这里配置key来源自static\js\runtime\config.js文件的"__rest_base_path__"节点，
   * 用来代理业务的REST接口请求到后端工程接口地址，"htt://ip:port{context-path}",context-path为application.properties文件内配置项，默认或留空为"/"
   */
  '/backend': {
    target: 'http://localhost:8890',
    //target:'http://************:8888',
    changeOrigin: false,
    pathRewrite: {
      '^/backend': ''
    }
  },
  '/scgl': {
    target: 'http://localhost:8890',
    //target:'http://************:8888',
    changeOrigin: false,
    pathRewrite: {
      '^/scgl': ''
    }
  },


  /**
  * 流程配置
  */
  '/hdscglpt/workflow': {
    target: 'http://************:8093',
    changeOrigin: false,
    pathRewrite: {
      '^/hdscglpt/workflow': '/jhsc'
    }
  },


  '/vsflow': {
    target: 'http://*************:8888',
    changeOrigin: true, 
    pathRewrite: {
      '^/vsflow': '/vsflow'
    }
  },


  //这里代理到后端siam的SSO地址，"http://ip:port{context-path}SSO",context-path为application.properties文件内配置项，默认或留空为"/"
  '/SSO': {
    target: 'http://localhost:8888',
    //target:'http://************:8888',
    changeOrigin: false,
    pathRewrite: {
      '^/SSO': '/SSO'
    }
  },
}

module.exports = {
  DEV,
  SECURITY,
  CAS,
  SIAM,
}
