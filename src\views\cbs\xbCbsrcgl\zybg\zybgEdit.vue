<template>
  <el-form class="lui-card-form" :model="formData" ref="saveFormRef" :rules="rules" label-width="120px" status-icon
    size="default">
    <el-row>
      <div class="button-box">
        <el-button type="primary" @click="handleSubmit(saveFormRef)">提交</el-button>
        <el-button type="info" @click="handleClose()">关闭</el-button>
      </div>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="队伍名称">
          <el-input v-model="formData.dwmc" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="原投标专业">
          <el-input v-model="formData.dwmc" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="申请投标类型">
          <el-input v-model="formData.cbsdwqc" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="申请投标专业">
          <el-input v-if="formData.dwlx == 'DW'" v-model="formData.dwmc" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="投标资格范围">
          <el-input :rows="2" type="textarea" v-model="formData.dwmc" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="准入证号">
          <el-input v-model="formData.zrzh" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="有效期">
          <span style="padding-left: 11px;">{{ formData.yxqks.split(' ')[0] }} - {{ formData.yxqjs.split(' ')[0]
          }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="12" class="grid-cell">
        <el-form-item label="当前状态">
          <el-select v-model="formData.dwzt" disabled>
            <el-option v-for="item in dqztOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="grid-cell">
        <el-form-item label="变更状态" prop="rgtzzt" required>
          <el-select v-model="formData.rgtzzt" clearable>
            <el-option v-for="item in bgztOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="变更原因" prop="bgyy" class="custom-label" required>
          <el-input type="textarea" :rows="2" placeholder="请输入变更原因" v-model="formData.bgyy"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="相关附件">
          <vsfileupload class="custom-upload" v-if="formData.dwywid" :key="formData.dwywid" :busId="formData.dwywid"
            :busType="'xbcbsrcgl'" :ywlb="'xbcbsrcgl'" :editable="formData.shzt != '1'"></vsfileupload>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div>
    <el-dialog title="选择专业" v-model="dialogVisible" v-if="dialogVisible" append-to-body>
      <xzzy />
    </el-dialog>
  </div>
</template>

<script setup>
import xzzy from "@src/views/cbs/cbsyj/xzzy/xzzy.vue";
</script>