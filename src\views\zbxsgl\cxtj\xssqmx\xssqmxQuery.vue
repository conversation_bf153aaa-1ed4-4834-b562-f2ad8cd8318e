<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMLB">
            <el-input ref="input45296" placeholder="请输入所属单位" v-model="listQuery.SSDW" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMLB">
            <el-cascader v-model="listQuery.XMLB" :options="ZRZYTree" filterable placeholder="请选择项目类别"
                         :props="{label:'ZYMC',value:'ZYBM',emitPath: false}"
                         clearable />
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMMC">
            <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMLB">
            <el-select ref="select14540" placeholder="请选择选商方式" v-model="listQuery.XSFS" class="full-width-input"
                       clearable>
              <el-option v-for="(item, index) in XSFSOptions" :key="index" :label="item.DMMC" :value="item.DMXX"
                         :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell" style="margin-right: 150px">
          <el-form-item label="" prop="XMMC">
            <div style="display: flex;align-items: center;gap: 10px">
              <el-date-picker
                  v-model="listQuery.KSSJ"
                  type="date"
                  clearable
                  style="width: 150px"
                  placeholder="请选择开始时间"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
              <div>-</div>
              <el-date-picker
                  v-model="listQuery.JSSJ"
                  type="date"
                  clearable
                  style="width: 150px"
                  placeholder="请选择结束时间"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="SFWC">
            <el-select ref="select14540" placeholder="是否完成" v-model="listQuery.SFWC" class="full-width-input"
                       clearable>
              <el-option v-for="(item, index) in SFWCOptions" :key="index" :label="item.DMMC" :value="item.DMXX"
                         :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="XMMC" label="项目名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="SSDWMC" label="所属单位" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="XMLBMC" label="项目类别" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="XSFSMC" label="选商方式" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="CJSJ" label="提交时间" :formatter="timeFormatter" align="center"
                               :show-overflow-tooltip="true" width="150"></el-table-column>
              <el-table-column prop="XSSQWCSJ" label="选商申请完成时间" :formatter="timeFormatter" align="center"
                               :show-overflow-tooltip="true" width="150"></el-table-column>
              <el-table-column prop="HYCJSJ" label="预定会议完成时间" :formatter="timeFormatter" align="center"
                               :show-overflow-tooltip="true" width="150"></el-table-column>
              <el-table-column prop="JGGSWCSJ" label="中标（成交）申请完成时间" :formatter="timeFormatter" align="center"
                               :show-overflow-tooltip="true" width="150"></el-table-column>
              <el-table-column prop="ZBTZWCSJ" label="中标（成交）通知书签发时间" :formatter="timeFormatter" align="center"
                               :show-overflow-tooltip="true" width="150"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="150" fixed="right">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="title[params.XSFS]"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <xssqmxView v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import xssqmxView from "@views/zbxsgl/cxtj/xssqmx/xssqmxView";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,xssqmxView},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,

      XSFSOptions: [],
      SSDWOptions: [],
      SFWCOptions: [{DMXX: '1', DMMC: '是'}, {DMXX: '0', DMMC: '否'}],

      ZRZYTree: [],

      title: {
        GKZB: '申请详情查看-招标',
        YQZB: '申请详情查看-招标',
        JB: '申请详情查看-非招标',
        JJ: '申请详情查看-非招标',
        GKJB: '申请详情查看-非招标',
        GKJJ: '申请详情查看-非招标',
        DJTP: '申请详情查看-独家谈判'
      }
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/xsgl/xssqcx/selectXssqPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }


    const viewRow = (row) => {
      state.params = {editable: false, id: row.JLID, operation: 'view',XSFS: row.XSFS}
      state.dialogVisible = true
    }


    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    const timeFormatter = (row, column, cellValue, index) => {
      if (cellValue) {
        return cellValue.substring(0, 10)
      }
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const getZrzyList = () => {
      let params={
      }
      axiosUtil.get('/backend/cbsxx/common/selectCbszyList', params).then((res) => {
        state.ZRZYTree = comFun.treeData(res.data || [],'ZYBM','FZYBM','children','0')
      });
    }


    onMounted(() => {
      getDataList()
      getZrzyList()
      getDMBData("XSFS", "XSFSOptions")
      // getDMBData("SSDW", "SSDWOptions")
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      closeForm,
      viewRow,
      timeFormatter

    }
  }

})
</script>

<style scoped>

</style>
