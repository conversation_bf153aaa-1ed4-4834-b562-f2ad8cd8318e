<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function locateBookMark() {
	//获取书签名称
	var bkName = document.getElementById("txtBkName").value;
	//将光标定位到书签所在的位置
	pageofficectrl.word.LocateDataRegion(bkName);
}

function OnPageOfficeCtrlInit() {
	pageofficectrl.AddCustomToolButton("定位光标到指定书签", "locateBookMark", 5);
}

function openFile() {
	return request({
		url: '/WordLocateBKMK/Word',
		method: 'get',
	})
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit, locateBookMark };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<div style=" font-size:small; color:Red;">
			<label>关键代码：看js函数：</label>
			<label>function locateBookMark()</label>
			<br />
			<label>将光标定位到书签前，请先在文本框中输入要定位到的书签名称（可点击Office工具栏上的“插入”→“书签”，来查看当前Word文档中所有的书签名称）！</label><br />
			<label>书签名称：</label><input id="txtBkName" type="text" value="PO_seal" />
		</div>
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
