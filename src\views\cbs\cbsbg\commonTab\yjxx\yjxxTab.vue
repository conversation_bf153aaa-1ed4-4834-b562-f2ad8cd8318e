<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        :model="tableData"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        class="lui-page"
        :disabled="!editable">
<!--      <div style="color: red">-->
<!--        {{ BGXX }}-->
<!--      </div>-->
      <el-table ref="dataTable" :data="[...tableData,...getDelRow()]" height="500px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column v-for="(item,index) in tableColumn" :key="index"
                         :prop="item.prop" :label="item.label" :align="item.align" :width="item.width"
                         :min-width="item.minWidth"
                         :show-overflow-tooltip="item.showOverflowTooltip">
          <template #default="{row,$index}" v-if="item.slot==='format'">
            {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='change'">
            <div :class="{dataChange : isChangeT(row.YJWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.YJWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.YJWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span>{{ row[item.prop] }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='formatChange'">
            <div :class="{dataChange : isChangeT(row.YJWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.YJWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.YJWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span> {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='info'">
            <el-tooltip :content="row[item.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="CZ" label="操作" align="center" width="300" fixed="right" v-if="editable">
          <template #default="{row,$index}">
            <div v-if="tableRowClassName({row})!=='warning-row'">
              <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="viewRow(row, $index)">查看</el-button>
              <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button v-if="row.SHZT != 1" class="lui-table-button" @click="deleteRow(row,$index)">删除</el-button>
            </div>
            <div v-else>
              已删除
            </div>
          </template>
        </el-table-column>


      </el-table>

    </el-form>

    <el-dialog
        title="业绩信息查看"
        v-model="editVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        top="100px"
        width="1200px">
      <yjxxEdit :editData="editData" @close="editVisible = false" :editable="false"/>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        title="信息项选择"
        v-model="chooseVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        top="50px"
        width="1200px"
    >
      <yjxxXz
          :key="editIndex"
          :currentRow="currentRow"
          @updateChooseData="updateChooseData"
          @updateEditData="updateEditData"
          @close="chooseVisible = false"
          :TYXYDM="TYXYDM"
      />
    </el-dialog>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, ref} from "vue";
import vsfileupload from "@views/components/vsfileupload";
import {InfoFilled} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import yjxxEdit from "@views/cbs/templateManagement/DataTemplateManagement/yjxx/yjxxEdit.vue"
import yjxxXz from "@views/cbs/templateManagement/DataTemplateManagement/yjxx/yjxx_xz.vue"
import comFun from "@lib/comFun";


export default defineComponent({
  name: '',
  components: {vsfileupload, InfoFilled, yjxxEdit, yjxxXz},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    TYXYDM: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      chooseVisible: false,
      tableData: [],
      tableColumn: [
        {
          label: "专业名称",
          prop: "ZYMC",
          align: "center",
          showOverflowTooltip: true,
          width: 100,
          // slot: "select"
        },
        {
          label: "业务要求",
          prop: "YWYQ",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "录入资料说明",
          prop: "LRZLSM",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "信息项",
          prop: "XXXMC",
          align: "center",
          showOverflowTooltip: true,
          width: 150,
        },
        {
          label: "项目名称",
          prop: "XMMC",
          align: "center",
          minWidth: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "甲方单位",
          prop: "JSDW",
          align: "center",
          minWidth: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "合同开始日期",
          prop: "HTRQKS",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'formatChange' : 'format',
        },
        {
          label: "合同结束日期",
          prop: "HTRQJS",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'formatChange' : 'format',
        },
        {
          label: "合同金额(万元)",
          prop: "HTJE",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
      ],
    })

    watch(() => props.defaultData, val => {
      if (val) {
        val.forEach(x => {
          const UUID = comFun.newId()
          x.DWYJID = x.DWYJID || UUID;
          x.YJWYBS = x.YJWYBS || UUID;
        })
      }
      state.tableData = val
    }, {immediate: true})


    const insertRow = (row, index) => {
      const UUID = comFun.newId();
      state.tableData.splice(index + 1, 0, {
        DWYJID: UUID,
        YJWYBS: UUID,
        ZYMC: row.ZYMC,
        ZYBM: row.ZYBM,
        XXXMC: row.XXXMC,
        MBMXID: row.MBMXID,
        YWYQ: row.YWYQ,
        LRZLSM: row.LRZLSM,
        XXX: null,
        ZZXXMC: null,
        ZZDJ: null,
        YXKSRQ: null,
        YXJSRQ: null,
        FZBM: null,
        FJ: null,
      });
    };

    const currentRow = ref({});

    const updateChooseData = (val) => {
      changeData(currentRow.value, val, editIndex.value, false)
    };

    const changeData = (oldRow, newRow, index, visible) => {
      let params = {
        newId: oldRow.DWYJID,
        oldId: newRow.YJZSJID,
        cover: true
      }

      axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
        oldRow.YJZSJID = newRow.YJZSJID
        oldRow.XMMC = newRow.XMMC
        oldRow.JSDW = newRow.JSDW
        oldRow.HTJE = newRow.HTJE
        oldRow.HTRQKS = newRow.HTRQKS
        oldRow.HTRQJS = newRow.HTRQJS
        oldRow.GCFW = newRow.GCFW
        state.chooseVisible = visible;
      })
    }
    watch(() => state.tableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])
    const initBgInfo = () => {
      if (state.tableData && props.resultTableData) {
        let res = []
        let BGQBS = props.resultTableData.map(i => i.YJWYBS)
        let BGHBS = state.tableData.map(i => i.YJWYBS)
        let allBS = new Set([...BGQBS, ...BGHBS])
        allBS.forEach(item => {
          if (BGQBS.includes(item) && BGHBS.includes(item)) {
            let BGQXX = props.resultTableData.find(i => i.YJWYBS === item)
            let BGHXX = state.tableData.find(i => i.YJWYBS === item)
            let isBg = false
            let dbsj = []
            let checkProp = ['XMMC', 'JSDW', 'HTJE', 'HTRQKS', 'HTRQJS']
            checkProp.forEach(ii => {
              if ((BGQXX[ii] || '') !== (BGHXX[ii] || '')) {
                dbsj.push({
                  BGQ: BGQXX[ii] || '',
                  BGH: BGHXX[ii] || '',
                  ZDMC: ii
                })
                isBg = true

              }
            })
            if (isBg) {
              res.push({
                YWLX: 'YJXX',
                BGZT: 'BG',
                WYBS: item,
                BGXQ: dbsj
              })
            }

          } else if (BGQBS.includes(item) && !BGHBS.includes(item)) {
            res.push({
              YWLX: 'YJXX',
              BGZT: 'SC',
              WYBS: item,
            })
          } else if (!BGQBS.includes(item) && BGHBS.includes(item)) {
            res.push({
              YWLX: 'YJXX',
              BGZT: 'XZ',
              WYBS: item,
            })
          }
        })
        BGXX.value = res
      }

    }

    const getDelRow = () => {
      if (props.resultTableData && state.tableData) {
        return props.resultTableData.filter(item => !state.tableData.find(ii => ii.YJWYBS === item.YJWYBS))
      } else {
        return []
      }
    }

    const tableRowClassName = ({row, index}) => {
      let info = BGXX.value.find(ii => ii.WYBS === row.YJWYBS) || {}
      if (info.BGZT === 'XZ') {
        return "success-row"
      } else if (info.BGZT === 'SC') {
        return "warning-row"
      }

    }

    const isChangeT = (YJWYBS, prop) => {
      let info = BGXX.value.find(item => item.WYBS === YJWYBS && item.BGZT === 'BG')
      if (info) {
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if (res) {
          res.BGMS = `${res.BGQ} 变更为 ${res.BGH}`
          return res
        }
      }
      return null
    }

    const updateEditData = (row) => {
      state.tableData.forEach((item, index) => {
        if (item.YJZSJID === row.YJZSJID) {
          changeData(item, row, index, true)
        }
      })
    }

    const chooseRow = (row, index) => {
      currentRow.value = row;
      editIndex.value = index;
      state.chooseVisible = true;
    };

    const editVisible = ref(false);
    const editIndex = ref(0);
    const editData = ref({});

    const viewRow = (row, index) => {
      editIndex.value = index;
      editData.value = row;
      editVisible.value = true;
    };
    const deleteRow = (row, index) => {
      ElMessageBox.confirm('是否删除此条数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.tableData.splice(index, 1)
        ElMessage({
          message: '删除成功!',
          type: 'success'
        })
      }).catch(() => {
      })
    }

    onMounted(() => {

    })

    return {
      ...toRefs(state),
      viewRow,
      deleteRow,
      chooseRow,
      insertRow,
      updateEditData,
      editIndex,
      currentRow,
      updateChooseData,
      editVisible,
      editData,
      tableRowClassName,
      isChangeT,
      getDelRow,
      BGXX

    }
  }

})
</script>

<style scoped>

</style>
