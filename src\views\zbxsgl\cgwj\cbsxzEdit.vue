<template>
    <el-form ref="vForm" :model="formData" :rules="rules" label-position="left" size="default">
        <el-table 
            ref="dataTable" 
            :data="tableData" 
            height="calc(100vh - 180px)" 
            class="lui-table"
            border
            stripe
            size="default" 
            :span-method="arraySpanMethod"
            highlight-current-row>
            <el-table-column prop="tkh" label="条款号" align="center" width="200"></el-table-column>
            <el-table-column label="条款名称" header-align="center" align="left" width="300">
                <template #default="scope">
                    <span><span v-if="scope.row.tkmcStar" style="color: red;">*</span>
                        {{scope.row.tkmc}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="编列内容" header-align="center" align="left">
                <template #default="scope">
                    <!-- 只有一个属性 -->
                    <template v-if="scope.row.props">
                        <el-form-item :prop="scope.row.props">
                            <el-input v-if="scope.row.type=='text'" v-model="formData[scope.row.props]" placeholder="请输入" clearable >
                            </el-input>
                            <span v-else>{{formData[scope.row.props]}}</span>
                        </el-form-item>
                    </template>
                    <!-- 自定义 -->
                    <template v-else>
                        <div v-if="scope.row.num === 1" class="zdyform">
                            <el-form-item label="名称：" prop="SSDWMC" label-width="90px">
                                {{formData.SSDWMC}}
                            </el-form-item>
                            <el-form-item label="地址：" prop="CGR_DZ" label-width="90px">
                                <el-input v-model="formData.CGR_DZ" placeholder="请输入" clearable >
                                </el-input>
                            </el-form-item>
                            <el-form-item label="联系人：" prop="CGR_LXR" label-width="90px">
                                <el-input v-model="formData.CGR_LXR" placeholder="请输入" clearable >
                                </el-input>
                            </el-form-item>
                            <el-form-item label="电话：" prop="CGR_LXDH" label-width="90px">
                                <el-input v-model="formData.CGR_LXDH" placeholder="请输入" clearable >
                                </el-input>
                            </el-form-item>
                        </div>
                        <div v-if="scope.row.num === 14" class="zdyform">
                            <div v-for="(item,index) in formData.bdList" :key="index">
                                {{item.BDMC}}
                                <template v-if="item.SFAECF === '1'">
                                    <el-form-item  v-for="iindex in Number(item.ZBRGS)" :key="iindex" :label="'第' + iindex + '中标人的份额比例：'" label-width="185px">
                                        <el-select size="normal" v-model="item.gzlList[iindex-1].GXYSF" placeholder="请选择" style="width:100px" >
                                            <el-option v-for="(item, index) in blfwList" :key="index" :value="item.DMXX" :label="item.DMMC" >
                                            </el-option>
                                        </el-select>
                                        <el-input-number v-model="item.gzlList[iindex-1].FEBL" :min="0" :max="100" :controls="false" />
                                        <span style="margin-left: 8px;">%</span>
                                    </el-form-item>
                                </template>
                                <template v-else>
                                    <el-form-item  v-for="iindex in Number(item.ZBRGS)" :key="iindex" :label="'第' + iindex + '中标人工作量内容：'" label-width="185px">
                                        <el-input v-model="item.gzlList[iindex-1].GZLNR" placeholder="请输入" clearable >
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </div>
                        </div>
                    </template>
                </template>
            </el-table-column>
        </el-table>
    </el-form>
    <el-row style="line-height: 50px;">
        <el-col :span="24" style="width: 100%;text-align: center;position: fixed;bottom: 20px;">
            <el-button size="default" type="primary" @click="saveData"> 保存 </el-button>
            <el-button size="default" @click="close"> 关闭 </el-button>
        </el-col>
    </el-row>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import VSAuth from "@src/lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
});
const emit = defineEmits(["closeDialog","reload"]);
const formData = ref({});
// tkh为合并单元格标记，tkmcStar为条款名称红星提示
const tableData = ref([
    { num: 1, tkh:'1.1.1', tkmc: '采购人', tkmcStar: true },
    { num: 2, tkh:'1.1.2', tkmc: '招标代理机构', tkmcStar: false, props: 'ZBDLJG', type:'text'},
    { num: 3, tkh:'1.1.3', tkmc: '采购项目名称', tkmcStar: true, props: 'XMMC' },
    { num: 4, tkh:'1.1.4', tkmc: '采购项目单位编号', tkmcStar: false, props: 'XMBH' },
    { num: 5, tkh:'1.1.5', tkmc: '建设地点', tkmcStar: true, props: 'SSDD' },
    { num: 6, tkh:'1.1.5', tkmc: '签约地点', tkmcStar: false, props: 'QYDD', type:'text'},
    { num: 7, tkh:'1.1.6', tkmc: '项目概述', tkmcStar: true, props: 'XMGS', type:'text' },
    { num: 8, tkh:'1.2.1', tkmc: '资金来源', tkmcStar: true, props: 'ZJLYMC' },
    { num: 9, tkh:'1.2.2', tkmc: '出资比例', tkmcStar: false, props: 'CZBL', type:'text' },
    { num: 10, tkh:'1.2.3', tkmc: '资金落实情况', tkmcStar: false, props: 'ZJLSQK', type:'text' },
    { num: 11, tkh:'1.3.1', tkmc: '项目总金额', tkmcStar: true, props: 'XMZJE', type:'text' },
    { num: 12, tkh:'1.3.1', tkmc: '农民工费用', tkmcStar: false, props: 'NMGFY', type:'text' },
    { num: 13, tkh:'1.3.2', tkmc: '采购范围', tkmcStar: true, props: 'CGFW', type:'text' },
    { num: 14, tkh:'1.3.3', tkmc: '工作量分配', tkmcStar: true },
    { num: 15, tkh:'1.3.3', tkmc: '标段划分', tkmcStar: true, props: 'BDHF', type:'text'  },
    { num: 16, tkh:'1.3.4', tkmc: '计划工期或服务期', tkmcStar: true, props: 'JHGQHFWQ', type:'text' },
    { num: 17, tkh:'1.3.4', tkmc: '质保期结束日期', tkmcStar: false, props: 'ZBQJSRQ', type:'text' },
    
]);
const rules = ref({
    SSDWMC: [{ required: true }],
    CGR_DZ: [{ required: true, message: "请输入地址", trigger: "blur" }],
    CGR_LXR: [{ required: true, message: "请输入联系人", trigger: "blur" }],
    CGR_LXDH: [{ required: true, message: "请输入电话", trigger: "blur" }],
    XMZJE: [{ required: true, message: "请输入项目总金额", trigger: "blur" }],
    CGFW: [{ required: true, message: "请输入采购范围", trigger: "blur" }],
    JHGQHFWQ: [{ required: true, message: "请输入计划工期或服务期", trigger: "blur" }],
    
});
const blfwList = ref([
    {DMXX: '01', DMMC: '>'}
]);

onMounted(() => {
    formData.value = Object.assign({}, props.data);
    // 是否框架项目
    if(formData.value.SFKJXM === '1'){
        tableData.value = tableData.value.filter(item => item.num !== 15);
    }else{
        tableData.value = tableData.value.filter(item => item.num !== 14);
    }
});

const vForm = ref(null);
const saveData = () => {
    vForm.value.validate()
    .then((result) => {
        formData.value.XGRZH = VSAuth.getAuthInfo().permission.userLoginName;
        axiosUtil.post('/backend/xsgl/xswj/saveCbsxz', formData.value).then(res=>{
            if(res.data.success){
                emit('reload');
                ElMessage.success('保存成功！');
            }else{
                ElMessage.error('保存失败！');
            }
        })
    }).catch((err) => {

    });
}

const close = () => {
    emit('closeDialog');
}
const arraySpanMethod = ({row,column,rowIndex,columnIndex}) => {
    if(columnIndex === 0){
        let nameSpan = getSpanNumber(tableData.value,"tkh");
        return {
            rowspan: nameSpan[rowIndex],
            colspan: 1,
        };
    }
    
}

// 合并单元格
const getSpanNumber = (data, prop) => {
    //data要处理的数组，prop要合并的属性，比如name
    //数组的长度，有时候后台可能返回个null而不是[]
    let length = Array.isArray(data) ? data.length : 0;
    if (length > 0) {
        //用于标识位置
        let position = 0;
        //用于对比的数据
        let temp = data[0][prop];
        //要返回的结果
        let result = [1];
        //假设数据是AABCC，我们的目标就是返回20120
        for (let i = 1; i < length; i++) {
            if (data[i][prop] == temp) {
                //标识位置的数据加一
                result[position] += 1;
                //当前位置添0
                result[i] = 0;
            } else {
                //不相同时，修改标识位置，该位置设为1，修改对比值
                position = i;
                result[i] = 1;
                temp = data[i][prop];
            }
        }
        //返回结果
        return result;
    } else {
        return [0];
    }
}
defineExpose({});
</script>

<style scoped>
::v-deep .el-form-item--default {
    margin-bottom: 0px;
}
::v-deep .zdyform .el-form-item--default {
    margin-bottom: 6px;
}
::v-deep .el-form-item--default .el-form-item__content {
    line-height: 23px;
}
</style>