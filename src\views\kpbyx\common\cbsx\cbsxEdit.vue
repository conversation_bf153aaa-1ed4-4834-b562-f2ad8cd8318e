<template>
  <div>
    <el-form :model="tableData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;color: #2A96F9;
      border-bottom: 3px solid #2A96F9;text-align: center;padding-bottom: 2px;padding-left: 20px;padding-right: 20px;">
        唱标顺序
      </div>
      <div style="width: 100%;margin-bottom: 10px;justify-content: right;display: flex">
        <el-button type="primary" @click="shuffleTable" v-if="editable">随机排序</el-button>
      </div>

      <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 560px)"
                :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column prop="DWMC" label="投标单位名称" align="center"
                         :show-overflow-tooltip="true" min-width="160"></el-table-column>
        <el-table-column prop="CB_SX" label="唱标顺序" align="center" min-width="160">
          <template #default="{row}">
            {{row.CB_SX || '未排序'}}
          </template>
        </el-table-column>

      </el-table>
      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="getTableData">刷新</el-button>
      </div>
    </el-form>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import vsAuth from "@lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.parentForm.CB_WCZT !== '1',
      role: props.fromParams.role,
      KPBYXID: props.params.KPBYXID,
      JLID: props.params.JLID,
      tableData: [],
      rules: {}
    })

    const getTableData = () => {
      let params={
        JLID: state.JLID,
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/cbsx/selectCbsxList', params).then((res) => {
        state.tableData=res.data || []
        state.loading=false
      })
    }

    const saveData = (type) => {
      if(state.tableData.find(item=>!item.CB_SX)){
        ElMessage.warning('请完成唱标顺序')
        return
      }
      ElMessageBox.confirm('确定提交并公布唱标顺序?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params={
          CBXXList: state.tableData.map(item=>{
            return{
              ...item,
              CB_CJSJ: comFun.getNowTime(),
              CB_ZT: '1',
            }
          })
        }
        state.loading=true
        axiosUtil.post('/backend/kpbyx/cbsx/saveCbpxxx',params).then(res=>{
          ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
          state.loading=true
          emit('saveFromData', {CB_WCZT: '1',CB_WCSJ: comFun.getNowTime(),CB_SFGS: '1'},{})
          nextTick(()=>{
            emit('nextStep','唱标顺序已公布')
          })
        })
      }).catch(() => {})
    }

    const shuffleTable = () => {
      let array = state.tableData
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]]; // 交换位置
      }
      array.forEach((item, index) => item.CB_SX = index + 1)
      ElMessage.success('已随机排序')
    }

    onMounted(() => {
      getTableData()
    })

    return {
      ...toRefs(state),
      shuffleTable,
      saveData,
      getTableData

    }
  }

})
</script>

<style scoped>

</style>
