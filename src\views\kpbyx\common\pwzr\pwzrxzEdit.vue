<template>
  <div style="width: 100%" v-loading="loading">
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 160px;text-align: center;padding-bottom: 2px">
        选择评委主任
      </div>
      <el-row ref="grid71868" :gutter="12" style="width: 100%">
        <el-col :span="24">
          <el-table ref="datatable91634" :data="formData.PWQDList" height="300px"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="ZJLBMC" label="人员类型" align="center"
                             :show-overflow-tooltip="true" width="160">
              <template #default="{row}">
                <div v-if="row.SFZBRDB==='1'">
                  需求单位代表
                </div>
                <div v-else>
                  {{row.ZJLBMC}}专家
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="XM" label="姓名" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="PWJS" label="评委角色" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="{row}">
                评委
              </template>
            </el-table-column>
            <el-table-column prop="DPSL" label="评委主任得票数" align="center"
                             :show-overflow-tooltip="true" width="120">
            </el-table-column>

            <el-table-column prop="SFPWZR" label="是否评委主任" align="center"
                             :show-overflow-tooltip="true" width="100">
              <template #default="{row}">
                <div style="color: #6ad297" v-if="row.SFQDWPBZR==='1'">是</div>
                <div v-else>否</div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;margin-bottom: 10px;justify-content: center;display: flex;"
           v-if="!formData.PWQDList.find(item=>item.SFQDWPBZR)">
        <el-button size="default" type="primary" @click="saveData('submit')" v-if="formData.SHZT!=='1'">提交</el-button>
        <el-button size="default" @click="getFormData">刷新</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "@lib/comFun";
import vsAuth from "@lib/vsAuth";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      formData: {
        PWQDList: [],
      },
      loading:false,
      rules: {}
    })

    const getFormData = () => {
      let params={
        PBHYBS: props.params.PBHYBS,
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/szpwzr/selectXzpwzrDpxx', params).then((res) => {
        state.formData=res.data
        state.loading=false
        if(state.formData.PWQDList.find(item=>item.SFQDWPBZR) && props.params.SFDQHJ){
          emit('nextStep',`当前已有评标主任，等待投标文件解密`)
        }
      })
    }


    const saveData = () => {
      let maxDPPW=state.formData.PWQDList.reduce((max, b) => {
        return b.DPSL > max.DPSL ? b : max
      })

      ElMessageBox.confirm(`确认选定【${maxDPPW.XM}】为评标主任?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params={
          PBPWBS: maxDPPW.PBPWBS,
          SFQDWPBZR: '1',
          QDWPBZRSJ: comFun.getNowTime(),
        }
        axiosUtil.post('/backend/kpbyx/szpwzr/savePwzrjg',params).then(res=>{
          ElMessage.success('提交成功')
          getFormData()
          nextTick(()=>{
            emit('nextStep',`已选定【${maxDPPW.XM}】为评标主任，等待投标文件解密`)
          })
        })

      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      saveData,
      getFormData

    }
  }

})
</script>

<style scoped>

</style>
