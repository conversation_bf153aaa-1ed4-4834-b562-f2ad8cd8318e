<!--
 * @Description: 江汉市场管理门户-首页
 * @Author: WLF (<EMAIL>)
 * @Date: 2024-11-14 08:49:04
 * @LastEditTime: 2025-01-13 17:19:40
 * @FilePath: \jhscgl-xieyun-front\src\views\WebPortal\homePage.vue
 * @copyright : Copyright (c) 2024 wlf
-->
<template>
    <div>
        <div style="height: 320px;margin-top:5px;border-top: 3px solid #b81515;">
            <el-card class="box-card" shadow="never">
                <template #header>
                    <div class="card-header">
                        <span class="tabs-lable-ttgg">
                            <span :class="{ active: activeTab === 'TZGG' }" style="cursor: pointer;"
                                @click="loadTtgzData('TZGG')">通知公告</span>
                            |
                            <span :class="{ active: activeTab === 'ZCFG' }" style="cursor: pointer;"
                                @click="loadTtgzData('ZCFG')">政策法规</span>
                            |
                            <span :class="{ active: activeTab === 'WGTB' }" style="cursor: pointer;"
                                @click="loadTtgzData('WGTB')">违规通报</span>
                        </span>
                        <el-button class="button" type="primary" link @click="imgClick('2', '1')">更多</el-button>
                    </div>
                </template>
                <el-row>
                    <el-col :span="12">
                        <el-table :data="tzggTableData" style="width: 95%" :show-header="false" height="221px"
                            :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                            <el-table-column prop="name" show-overflow-tooltip>
                                <template #default="{ row }">
                                    <div class="tableColumn">
                                        <img src="@static/img/webPortal/icon.png"
                                            style="width: 15px; height: 15px; margin-right: 5px;">
                                        <div @click="openTzggDialog(row)" class="tableTitle">
                                            {{ row.BT }}
                                        </div>
                                        <img src="@static/img/webPortal/new.png" v-if="row.ISNEW == '1'"
                                        style="width: 30px; height: 15px; margin-right: 5px;">
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="WHSJ" width="130">
                                <template #default="{ row }">
                                    <div class="tableDate">
                                        {{ row.WHSJ }}
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-col>
                    <el-col :span="12">
                        <el-table :data="zcfgTableData" style="width: 100%" :show-header="false" height="221px"
                            :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                            <el-table-column prop="name" show-overflow-tooltip>
                                <template #default="{ row }">
                                    <div class="tableColumn">
                                        <img src="@static/img/webPortal/icon.png"
                                            style="width: 15px; height: 15px; margin-right: 5px;">
                                        <div @click="openTzggDialog(row)" class="tableTitle">
                                            {{ row.BT }}
                                        </div>
                                        <img src="@static/img/webPortal/new.png" v-if="row.ISNEW == '1'"
                                        style="width: 30px; height: 15px; margin-right: 5px;">
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="WHSJ" width="130">
                                <template #default="{ row }">
                                    <div class="tableDate">
                                        {{ row.WHSJ }}
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-col>
                </el-row>

            </el-card>
        </div>
        <div style="height: 642px;margin-top:5px;border-top: 3px solid #b81515;">
            <el-card class="box-card" shadow="never">
                <template #header>
                    <div class="card-header">
                        <span class="tabs-lable">承包商专区</span>
                        <el-button class="button" type="primary" link @click="imgClick('3', '1')">更多</el-button>
                    </div>
                </template>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('3', '1')"
                            @mouseenter="onMouseEnter('CBSZYC', 'cbs')" :class="{ imgHover: isHover }"
                            @mouseleave="onMouseLeave" src="../../../static/img/webPortal/承包商资源库.png"
                            fit="cover"></el-image>
                    </el-col>
                    <el-col :span="6">
                        <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('3', '2')"
                            @mouseenter="onMouseEnter('PJGS', 'cbs')" :class="{ imgHover: isHover }"
                            @mouseleave="onMouseLeave" src="../../../static/img/webPortal/考核评价公示.png"
                            fit="cover"></el-image>
                    </el-col>
                    <el-col :span="6">
                        <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('3', '3')"
                            @mouseenter="onMouseEnter('HMD', 'cbs')" :class="{ imgHover: isHover }"
                            @mouseleave="onMouseLeave" src="../../../static/img/webPortal/黑名单公示.png"
                            fit="cover"></el-image>
                    </el-col>
                    <el-col :span="6">
                        <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('3', '4')"
                            @mouseenter="onMouseEnter('CQYJ', 'cbs')" :class="{ imgHover: isHover }"
                            @mouseleave="onMouseLeave" src="../../../static/img/webPortal/超期预警.png"
                            fit="cover"></el-image>
                    </el-col>
                </el-row>
                <template v-if="cbsTableFlag == 'CBSZYC'">
                    <el-row>
                        <el-col :span="12">
                            <el-table :data="cbsTableData1" style="width: 95%" :show-header="false" height="661px"
                                :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                                <el-table-column prop="name" show-overflow-tooltip>
                                    <template #default="{ row }">
                                        <div class="tableColumn">
                                            <img src="@static/img/webPortal/icon.png"
                                                style="width: 15px; height: 15px; margin-right: 5px;">
                                            <div class="zjTableTitle" style="cursor: pointer;" @click="jumpCbsTj(row)">
                                                {{ row.ZYMC }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="DW_COUNT" width="130">
                                    <template #default="{ row }">
                                        <div class="tableDate">
                                            {{ row.DW_COUNT }}
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-col>
                        <el-col :span="12">
                            <el-table :data="cbsTableData2" style="width: 100%" :show-header="false" height="661px"
                                :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                                <el-table-column prop="name" show-overflow-tooltip>
                                    <template #default="{ row }">
                                        <div class="tableColumn">
                                            <img src="@static/img/webPortal/icon.png"
                                                style="width: 15px; height: 15px; margin-right: 5px;">
                                            <div class="zjTableTitle" style="cursor: pointer;" @click="jumpCbsTj(row)">
                                                {{ row.ZYMC }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="DW_COUNT" width="130">
                                    <template #default="{ row }">
                                        <div class="tableDate">
                                            {{ row.DW_COUNT }}
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-col>
                    </el-row>
                </template>
                
                <template v-if="cbsTableFlag == 'PJGS'">
                    <el-row style="margin-top: 10px;">
                        <el-select v-model="PJZQ" class="full-width-input" placeholder="请选择考核周期" clearable @change="pjzqChange">
                            <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC" :value="item.PJZQID"></el-option>
                        </el-select>
                    </el-row>
                     <el-row>
                        <el-col :span="12">
                            <el-table :data="cbsTableData1" style="width: 95%" :show-header="false" height="661px"
                                :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                                <el-table-column prop="name" show-overflow-tooltip>
                                    <template #default="{ row }">
                                        <div class="tableColumn">
                                            <img src="@static/img/webPortal/icon.png"
                                                style="width: 15px; height: 15px; margin-right: 5px;">
                                            <div class="zjTableTitle" style="cursor: pointer;" @click="jumpKhtj(row)">
                                                {{ row.ZYMC }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="DW_COUNT" width="130">
                                    <template #default="{ row }">
                                        <div class="tableDate">
                                            {{ row.DW_COUNT }}
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-col>
                        <el-col :span="12">
                            <el-table :data="cbsTableData2" style="width: 100%" :show-header="false" height="661px"
                                :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                                <el-table-column prop="name" show-overflow-tooltip>
                                    <template #default="{ row }">
                                        <div class="tableColumn">
                                            <img src="@static/img/webPortal/icon.png"
                                                style="width: 15px; height: 15px; margin-right: 5px;">
                                            <div class="zjTableTitle" style="cursor: pointer;" @click="jumpKhtj(row)">
                                                {{ row.ZYMC }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="DW_COUNT" width="130">
                                    <template #default="{ row }">
                                        <div class="tableDate">
                                            {{ row.DW_COUNT }}
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-col>
                    </el-row>
                    <!-- <el-row style="margin-top: 10px;">
                        <el-select v-model="PJZQ" class="full-width-input" placeholder="请选择考核周期" clearable @change="pjzqChange">
                            <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC" :value="item.PJZQID"></el-option>
                        </el-select>
                    </el-row>
                    <el-table :data="cbsTableData1" style="width: 100%" :show-header="true" height="661px"
                        :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                        <el-table-column label="考核专业" header-align="center" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="zjTableTitle">
                                    {{ row.PJZYMC }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="合计" width="130">
                            <template #default="{ row }">
                                <div class="tableDate">
                                    {{ row.HJ }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="A档" width="130">
                            <template #default="{ row }">
                                <div class="tableDate">
                                    {{ row.A }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="B档" width="130">
                            <template #default="{ row }">
                                <div class="tableDate">
                                    {{ row.B }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="C档" width="130">
                            <template #default="{ row }">
                                <div class="tableDate">
                                    {{ row.C }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="D档" width="130">
                            <template #default="{ row }">
                                <div class="tableDate">
                                    {{ row.D }}
                                </div>
                            </template>
                        </el-table-column>
                    </el-table> -->
                </template>
                <template v-if="cbsTableFlag == 'HMD'">
                    <el-table :data="cbsTableData1" style="width: 100%" :show-header="true" height="661px"
                        :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                        <el-table-column min-width="300" label="企业（队伍/人员）名称" header-align="center" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="zjTableTitle">
                                    <el-button style="font-size: 16px;" type="text" @click="openHmdDialog(row)">{{ row.CLDXMC }}</el-button>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="纳入时间" header-align="center" align="center" width="280" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="zjTableTitle">
                                    {{ row.CJSJ }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="类型" header-align="center" align="center" width="230">
                            <template #default="{ row }">
                                <div class="zjTableTitle">
                                    {{ row.CLDXLXMC }}
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
                <template v-if="cbsTableFlag == 'CQYJ'">
                    <el-table :data="cbsTableData1" style="width: 100%" :show-header="true" height="661px"
                        :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                        <el-table-column width="300" label="企业名称" header-align="center" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="zjTableTitle">
                                    {{ row.CBSDWQC }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="队伍名称" width="300" header-align="center" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="zjTableTitle">
                                    {{ row.DWMC }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="预警说明" header-align="center" align="left" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="zjTableTitle">
                                    {{ row.YJYY }}
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>

            </el-card>
        </div>
        <div style="height: 835px;margin-top:5px;border-top: 3px solid #b81515;">
            <el-card class="box-card" shadow="never">
                <template #header>
                    <div class="card-header">
                        <span class="tabs-lable-ttgg">
                            <span :class="{ active: xsTab === 'ZB' }" style="cursor: pointer;"
                                @click="loadXszqData('ZB')">招标专区</span>
                            |
                            <span :class="{ active: xsTab === 'FZB' }" style="cursor: pointer;"
                                @click="loadXszqData('FZB')">非招标专区</span>
                        </span>
                        <el-button class="button" type="primary" link @click="imgClick('4', '1')">更多</el-button>
                    </div>
                </template>
                <div  v-if="xsTab == 'ZB'">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('4', '1')"
                                @mouseenter="onMouseEnter('ZBGG', 'zb')" :class="{ imgHover: isHover }"
                                @mouseleave="onMouseLeave" src="../../../static/img/webPortal/招标公告.png"
                                fit="cover"></el-image>
                        </el-col>
                        <el-col :span="6">
                            <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('4', '2')"
                                @mouseenter="onMouseEnter('CQGG', 'zb')" :class="{ imgHover: isHover }"
                                @mouseleave="onMouseLeave" src="../../../static/img/webPortal/澄清公告.png"
                                fit="cover"></el-image>
                        </el-col>
                        <el-col :span="6">
                            <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('4', '3')"
                                @mouseenter="onMouseEnter('ZBGS', 'zb')" :class="{ imgHover: isHover }"
                                @mouseleave="onMouseLeave" src="../../../static/img/webPortal/中标候选人公示.png"
                                fit="cover"></el-image>
                        </el-col>
                        <el-col :span="6">
                            <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('4', '4')"
                                @mouseenter="onMouseEnter('ZBJG', 'zb')" :class="{ imgHover: isHover }"
                                @mouseleave="onMouseLeave" src="../../../static/img/webPortal/中标结果公示.png"
                                fit="cover"></el-image>
                        </el-col>
                    </el-row>
                    <el-table :data="zbzqTableData" style="width: 100%" :show-header="false" height="661px" :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                        <el-table-column prop="name" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="tableColumn">
                                    <el-tag size="mini"
                                            style="border-radius: 40px;font-size: 8px;height: 18px;width: 40px;">
                                            <span :style="[row.GGZT == '1' ? 'color:#409eff' : 'color: #b81515']">
                                                {{row.TITLE}} {{ row.GGZT == '1' ? '中' : (row.GGZT == '9' ? '终止' : '截止') }}
                                            </span>
                                        </el-tag>
                                    <div @click="openGgDialog(row)" class="tableTitle">
                                        
                                        {{ row.BT }}
                                        <img src="@static/img/webPortal/new.png" v-if="row.ISNEW == '1'"
                                            style="width: 30px; height: 15px; margin-right: 5px;">
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="WHSJ" width="130">
                            <template #default="{ row }">
                                        <div class="tableDate">
                                            {{ row.WHSJ }}
                                        </div>
                                    </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div v-if="xsTab == 'FZB'">
                    <el-row :gutter="20" justify="space-evenly">
                        <el-col :span="7">
                            <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('4', '5')"
                                @mouseenter="onMouseEnter('CGGG', 'fzb')" :class="{ imgHover: isHover }"
                                @mouseleave="onMouseLeave" src="../../../static/img/webPortal/采购公告.png"
                                fit="cover"></el-image>
                        </el-col>
                        <el-col :span="7">
                            <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('4', '6')"
                                @mouseenter="onMouseEnter('CJGS', 'fzb')" :class="{ imgHover: isHover }"
                                @mouseleave="onMouseLeave" src="../../../static/img/webPortal/成交候选人公示.png"
                                fit="cover"></el-image>
                        </el-col>
                        <el-col :span="7">
                            <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('4', '7')"
                                @mouseenter="onMouseEnter('CJJG', 'fzb')" :class="{ imgHover: isHover }"
                                @mouseleave="onMouseLeave" src="../../../static/img/webPortal/成交结果公示.png"
                                fit="cover"></el-image>
                        </el-col>
                        <!-- <el-col :span="6">
                            <el-image style="width: 100%;height: 70px;cursor: pointer;" @click="imgClick('4', '8')"
                                @mouseenter="onMouseEnter('TPGS', 'djtp')" :class="{ imgHover: isHover }"
                                @mouseleave="onMouseLeave" src="../../../static/img/webPortal/独家谈判公示.png"
                                fit="cover"></el-image>
                        </el-col> -->
                    </el-row>
                    <el-table :data="fzbzqTableData" style="width: 100%" :show-header="false" height="661px" :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                        <el-table-column prop="name" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="tableColumn">
                                    <el-tag size="mini"
                                            style="border-radius: 40px;font-size: 8px;height: 18px;width: 40px;">
                                            <span :style="[row.GGZT == '1' ? 'color:#409eff' : 'color: #b81515']">
                                                {{row.TITLE}} {{ row.GGZT == '1' ? '中' : (row.GGZT == '9' ? '终止' : '截止') }}
                                            </span>
                                        </el-tag>
                                    <div @click="openGgDialog(row)" class="tableTitle">
                                        
                                        {{ row.BT }}
                                        <img src="@static/img/webPortal/new.png" v-if="row.ISNEW == '1'"
                                            style="width: 30px; height: 15px; margin-right: 5px;">
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="WHSJ" width="130">
                            <template #default="{ row }">
                                        <div class="tableDate">
                                            {{ row.WHSJ }}
                                        </div>
                                    </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-card>
        </div>
        <!-- <div style="height: 735px;margin-top:5px;border-top: 3px solid #b81515;">
            <el-card class="box-card" shadow="never">
                <template #header>
                    <div class="card-header">
                        <span class="tabs-lable">评标专家专区</span>
                        <el-button class="button" type="primary" link @click="imgClick('5', '1')">更多</el-button>
                    </div>
                </template>
                <el-table :data="zjTableData" style="width: 100%" :show-header="false" height="661px" :cell-style="{ padding: '10px' }" empty-text="&nbsp">
                    <el-table-column show-overflow-tooltip width="150">
                        <template #default="{ row }">
                            <div class="tableColumn">
                                <img src="@static/img/webPortal/icon.png"
                                            style="width: 15px; height: 15px; margin-right: 5px;">
                                <div class="zjTableTitle">
                                    {{ row.XM }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="200" align="center" show-overflow-tooltip>
                        <template #default="{ row }">
                            <div class="zjTableTitle">
                                {{ row.ZJLBMC }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="300" align="left" show-overflow-tooltip>
                        <template #default="{ row }">
                            <div class="zjTableTitle">
                                {{ row.ZC }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="300" align="left" show-overflow-tooltip>
                        <template #default="{ row }">
                            <div class="zjTableTitle">
                                {{ row.XCSZYMC }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="300" align="left" show-overflow-tooltip>
                        <template #default="{ row }">
                            <div class="zjTableTitle">
                                {{ row.GZDWMC }}
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div> -->
        <el-row :gutter="20" style="margin-top: 10px;">
            <el-col :span="6">
                <el-image style="width: 100%;height: 100px;cursor: pointer;" @click="imgClick('8', '1')"
                    src="../../../static/img/webPortal/业务指南.png" fit="cover"></el-image>
            </el-col>
            <el-col :span="6">
                <el-image style="width: 100%;height: 100px;cursor: pointer;" @click="imgClick('8', '2')"
                    src="../../../static/img/webPortal/用户手册.png" fit="cover"></el-image>
            </el-col>
            <el-col :span="6">
                <el-image style="width: 100%;height: 100px;cursor: pointer;" @click="imgClick('8', '3')"
                    src="../../../static/img/webPortal/常见问题.png" fit="cover"></el-image>
            </el-col>
            <el-col :span="6">
                <el-image style="width: 100%;height: 100px;cursor: pointer;" @click="imgClick('8', '4')"
                    src="../../../static/img/webPortal/资料下载.png" fit="cover"></el-image>
            </el-col>
        </el-row>
        <el-dialog v-if="showTzggDialog" v-model="showTzggDialog" :title="title" @closed="closeDialog" top="1vh"
            z-index="1000" width="80%">
            <xxfbView v-if="showTzggDialog" :XXFBID="XXFBID" pageFlag="view" @closeForm="closeDialog"></xxfbView>
        </el-dialog>

        <el-dialog v-if="showKhtjDialog" v-model="showKhtjDialog" title="考核统计表" @closed="closeDialog" top="1vh"
            z-index="1000" width="80%">
            <khtj_list v-if="showKhtjDialog" :PJZQ="PJZQ" :PJZYID="PJZYID"  @closeForm="closeDialog"></khtj_list>
        </el-dialog>

        <el-dialog custom-class="lui-dialog" :close-on-click-modal="false" v-if="dialogVisible" v-model="dialogVisible"
            :title="dialogTitle" @closed="closeDialog" z-index="1200" top="5vh" width="1200px">
            <div>
                <component :is="pageComponent[busType]" v-if="pageComponent[busType] && dialogVisible" :params="params"
                    @close="closeDialog" />
            </div>
        </el-dialog>
    </div>
</template>

<script>

import { defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw } from "vue";
import axiosUtil from "@lib/axiosUtil";
import vsAuth from "@lib/vsAuth";
import xxfbView from "../xxfb/common/xxfbView"
import xstzfbView from "@views/zbxsgl/xstzfb/xstzfbView";
import zbjggsView from "@views/zbxsgl/zbjggs/zbjggsView";
import zbtzggView from "@views/zbxsgl/zbtzsqf/zbtzggView";
import djtpgsView from "@views/zbxsgl/djtpgs/djtpgsView";
import hmdView from "@views/khpj/hmdgl/hmdglView.vue";
import khtj_list from "./khtj_list.vue";
import { ElMessage, ElMessageBox } from "element-plus";
export default defineComponent({
    name: '',
    components: { xxfbView,khtj_list},
    props: {},
    setup(props, { emit }) {
        const state = reactive({
            activeIndex: '1',
            activeTab: 'TZGG',
            xsTab: 'ZB',
            cbsTableFlag: 'CBSZYC',
            tzggTableData: [],
            zcfgTableData: [],
            zbzqTableData: [],
            fzbzqTableData: [],
            zjTableData: [],
            cbsTableData1: [],
            cbsTableData2: [],
            allOtherTableData: [],
            userInfo: vsAuth.getAuthInfo().permission,
            isHover: false, // 跟踪鼠标是否悬浮在图片上
            // 政策法规弹窗
            showTzggDialog: false,
            title: '',
            XXFBID: '',

            // 公告弹窗
            dialogVisible: false,
            dialogTitle: '',
            busType: '',
            params: {},
            pageComponent: {
                XSGG: markRaw(xstzfbView),
                CQGG: markRaw(xstzfbView),
                CGGG: markRaw(xstzfbView),
                JGGS: markRaw(zbjggsView),
                ZBGS: markRaw(zbtzggView),
                GG: markRaw(djtpgsView),
                HMD: markRaw(hmdView)
            },
            KHSJOptions: [],
            PJZQ: '',
            showKhtjDialog: false,
            PJZYID:''
        })
        const imgClick = (menuInd, itemInd) => {
            /**
             * menuInd ： header菜单下标
             * itemInd ： header菜单中子菜单下标
             */
            emit('imgClick', menuInd, itemInd)
        }
        const loadTtgzData = (gzlx = 'TZGG') => {
            state.activeTab = gzlx
            axiosUtil.get('/backend/webProtal/queryXxfb', { page: 1, size: 10, GZLX: gzlx }).then((res) => {
                state.tzggTableData = res.data.list ? res.data.list.slice(0, 5) : []
                state.zcfgTableData = res.data.list ? res.data.list.slice(5, 10) : []
            });
        }
        const loadXszqData = (val) =>{
            state.xsTab = val
        }
        const loadAllOtherData = () => {
            axiosUtil.get('/backend/webProtal/queryHomeAllOtherData', {orgnaId: state.userInfo.orgnaId}).then((res) => {
                state.allOtherTableData = res.data
                state.zbzqTableData = state.allOtherTableData.find(e => e.GZLX == 'ZBGG')?.tableData
                state.fzbzqTableData = state.allOtherTableData.find(e => e.GZLX == 'CGGG')?.tableData
                state.zjTableData = state.allOtherTableData.find(e => e.GZLX == 'ZJ')?.tableData
                state.cbsTableData1 = state.allOtherTableData.find(e => e.GZLX == 'CBSZYC')?.tableData.slice(0, 11)
                state.cbsTableData2 = state.allOtherTableData.find(e => e.GZLX == 'CBSZYC')?.tableData.slice(11, 22)
            });
        }

        const onMouseEnter = (type, flag) => {
            state.isHover = true
            let list = state.allOtherTableData.find(e => e.GZLX == type)?.tableData
            if (flag == 'zb') {
                state.zbzqTableData = list
            } else if(flag == 'fzb') {
                state.fzbzqTableData = list
            }else{
                state.cbsTableFlag = type
                if(type == 'CBSZYC'){
                    state.cbsTableData1 = list?.slice(0, 11)
                    state.cbsTableData2 = list?.slice(11, 22)
                }else if(type == 'PJGS'){
                    state.cbsTableData1 = list?.slice(0, 10)
                    state.cbsTableData2 = list?.slice(10, 20)
                }else{
                    state.cbsTableData1 = list
                }
            }
            if(type == 'PJGS'){
                pjzqChange(state.PJZQ)
            }
        }

        const onMouseLeave = () => {
            state.isHover = false;
        }

        const openTzggDialog = (row) => {
            state.title = state.activeTab == 'TZGG' ? '通知公告' : '政策法规'
            state.XXFBID = row.XXFBID
            state.showTzggDialog = true
        }

        const closeDialog = () => {
            state.showTzggDialog = false
            state.dialogVisible = false
            state.showKhtjDialog = false
        }

        const jumpCbsTj = (row) => {
            window.location.href='/query-analysis/dwtjhz?ZYMC=' + row.ZYMC + '&ZYBM=' + row.ZYBM
        }

        const jumpKhtj = (row) => {
            state.PJZYID = row.PJZYID
            state.showKhtjDialog = true
        }
        
        const openGgDialog = (row) => {
            state.busType = row.GGLX
            state.params = { editable: false, id: '', operation: 'view' }
            if (['XSGG', 'CQGG', 'CGGG'].includes(state.busType)) {
                state.params.id = row.GGID
                state.params.operation = 'msg'
            }
            if (['JGGS'].includes(state.busType)) {
                state.params.id = row.JGGSID
            }
            if (['ZBGS'].includes(state.busType)) {
                state.params.id = row.ZBJGGSID
            }
            if (['GG'].includes(state.busType)) {
                state.params.id = row.GGID
            }
            state.dialogVisible = true

        }
        const pjzqChange = (val) => {
            axiosUtil.get('/backend/webProtal/queryKhpjZyfl', {PJZQ: val,page: 1, size: 25}).then(res => {
                // state.cbsTableData1 = res.data.list || []
                state.cbsTableData1 = res.data.list?.slice(0, 10)
                state.cbsTableData2 = res.data.list?.slice(10, 20)
            })
        }
        const getKhsjList = () => {
            axiosUtil.get('/backend/sckhpj/khmxtb/selectKhqjList', null).then(res => {
                state.KHSJOptions = res.data || []
                state.PJZQ = state.KHSJOptions[0].PJZQID
            })
        }
        const openHmdDialog = (row) =>{
            state.busType = 'HMD'
            state.dialogTitle = '黑名单查看'
            state.params = {HMDID: row.HMDID,id: row.HMDID}
            state.dialogVisible = true
        }
        onMounted(() => {
            if(state.userInfo.orgnaId){
                loadTtgzData()
                loadAllOtherData()
            }
            getKhsjList()
        })

        return {
            ...toRefs(state),
            imgClick,
            loadTtgzData,
            loadXszqData,
            onMouseEnter,
            onMouseLeave,
            openTzggDialog,
            closeDialog,
            openGgDialog,
            loadAllOtherData,
            getKhsjList,
            pjzqChange,
            openHmdDialog,
            jumpCbsTj,
            jumpKhtj
        }
    }

})
</script>

<style scoped>
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 10px;
}

.tabs-lable-ttgg {
    font-family: "黑体", "Heiti", sans-serif;
    font-size: 18px;
    font-weight: bold;
}

.tabs-lable {
    font-family: "黑体", "Heiti", sans-serif;
    font-weight: bold;
    font-size: 22px;
    color: #b81515;
}

.box-card {
    height: 99%;
}

.image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    /* 根据需要调整高度 */
}

.active {
    font-size: 22px;
    color: #b81515;
    /* 激活状态的颜色 */

}

.imgHover:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

.tableTitle {
    font-size: 16px;
    color: black;
    cursor: pointer;
}

.tableTitle:hover {
    border-bottom: 1px solid #b81515;
    /* 鼠标悬浮时显示下划线 */
}
.zjTableTitle {
    font-size: 16px;
    color: black;
}
.tableDate {
    font-size: 14px;
    color: #8c8c8c;
}

.tableColumn {
    display: flex;
    /* 使用flexbox布局 */
    align-items: center;
    /* 垂直居中对齐 */
}
</style>
