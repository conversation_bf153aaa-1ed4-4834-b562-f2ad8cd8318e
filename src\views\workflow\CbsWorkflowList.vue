<!-- 待办、已办列表 -->
<template>
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0" size="default">
        <el-row :gutter="20" class="lui-search-form">
            <el-col :span="6" class="grid-cell">
                <el-form-item label="">
                    <el-input v-model="listQuery.ssummary" placeholder="请输入任务名称" clearable></el-input>
                </el-form-item>
            </el-col>
            <!-- <el-col :span="6" class="grid-cell">
          <el-form-item label="">
            <el-select ref="select14540" v-model="listQuery.LCLB" placeholder="请选择流程类别" class="full-width-input" style="width: 100% " clearable>
              <el-option v-for="(item, index) in LCLBList" :key="index" :label="item.NAME"
                         :value="item.PROCESSID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="grid-cell">
          <el-form-item label="">
            <el-select ref="select14540" v-model="listQuery.RWZT" placeholder="请选择流程状态" class="full-width-input" style="width: 100% " clearable>
              <el-option v-for="(item, index) in LCZTList" :key="index" :label="item.label"
                         :value="item.value" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="grid-cell">
          <el-form-item label="">
            <el-input v-model="listQuery.TJDW" placeholder="请输入发起单位" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="grid-cell">
          <el-form-item label="">
            <div style="display: flex;gap: 5px">
              <el-date-picker style="width: 150px;" v-model="listQuery.KSSJ" format="YYYY-MM-DD"
                              value-format="YYYY-MM-DD" type="date" placeholder="发起时间开始"/>
              <div>至</div>
              <el-date-picker style="width: 150px;" v-model="listQuery.JSSJ" format="YYYY-MM-DD"
                              value-format="YYYY-MM-DD" type="date" placeholder="发起时间结束"/>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="grid-cell">
          <el-form-item label="">
          </el-form-item>
        </el-col> -->
            <el-col :span="6" class="grid-cell">
                <el-button type="primary" @click="onSearch"><el-icon>
                        <Search />
                    </el-icon>查询</el-button>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="24" class="grid-cell">
                <div class="container-wrapper">
                    <el-table style="height:calc(100vh - 280px);" :data="tableData" class="lui-table" border>
                        <el-table-column type="index" :index="indexMethod" width="50" label="序号" align="center">
                        </el-table-column>
                        <el-table-column prop="PROCESSINSTANCENAME" label="任务名称" header-align="center" align="left"
                            min-width='200' :show-overflow-tooltip="true">
                            <template #default="scope">
                                <el-button type="text" @click="onAudit(scope.row)">
                                    {{ scope.row.PROCESSINSTANCENAME }}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column label="流程状态" prop="TASK_STATU" align="center" width="100"></el-table-column>
                        <el-table-column label="当前节点" prop="TASK_POINT" header-align="center" align="left"
                            :show-overflow-tooltip="true"></el-table-column>
                        <el-table-column label="流程类型" prop="LCLX" header-align="center" align="center" width="200"
                            :show-overflow-tooltip="true">
                            <!--              <template #default="scope">-->
                            <!--                <span v-if="scope.row.WORKFLOWID=='CBSGL_CBSZR'">承包商准入</span>-->
                            <!--                <span v-else-if="scope.row.WORKFLOWID=='CBSGL_FWSZR'">服务商准入</span>-->
                            <!--                <span v-else-if="scope.row.WORKFLOWID=='ZTB_CGSQ'">招投标</span>-->
                            <!--                <span v-else-if="scope.row.WORKFLOWID=='YJPJ_CBSPJ'">承包商考核</span>-->
                            <!--                <span v-else>/</span>-->
                            <!--              </template>-->
                        </el-table-column>
                        <el-table-column label="发起人" prop="OWNER_NAME" header-align="center" align="center"
                            :show-overflow-tooltip="true"></el-table-column>
                        <el-table-column label="当前办理人" prop="RECI_NAME" header-align="center" align="center"
                            :show-overflow-tooltip="true"></el-table-column>
                        <el-table-column label="接收时间" prop="RECEIVERTIME" header-align="center" align="center"
                            width="150"></el-table-column>
                        <el-table-column label="操作" header-align="center" align="center" width="150">
                            <template #default="scope">
                                <el-button type="text" @click="onMonitor(scope.row)">[跟踪]</el-button>
                                <el-button type="text" @click="onEnd(scope.row)">[直接结束]</el-button>
                                <!-- <el-button type="text" @click="deleteTask(scope.row,'deleteTask')">[清理流程]</el-button>
                  <el-button type="text" @click="deleteTask(scope.row,'deleteAll')">[完全删除]</el-button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :current-page="pageNum"
                        :page-size="pageSize" :total="totalPage" :page-sizes="[10, 20, 30, 40, 50]"
                        layout="total, prev, pager, next, sizes" background class="lui-pagination"></el-pagination>
                </div>
            </el-col>
        </el-row>
        <el-dialog custom-class="lui-dialog" z-index="1000" :title="dialogTitle" v-model="showDialog" width="80%"
            top="35px" @close="onAuditClose" :close-on-click-modal="false">
            <audit-frame v-if="showDialog && lx == 'audit'" :model="model" :processParams="queryParams"
                :businessParams="otherParams" @close="showDialog = false"></audit-frame>
            <monitor-form v-if="showDialog && lx == 'monitor'" :model="model" :queryParams="queryParams"></monitor-form>
        </el-dialog>
    </el-form>
</template>
<script>
import {
    defineComponent,
    toRefs,
    reactive,
    nextTick,
    ref,
    getCurrentInstance,
    onMounted
}
    from 'vue'
import api from "../../api/lc";
import AuditFrame from "./AuditFrame.vue";
import MonitorForm from "./MonitorForm.vue";
import { ElLoading, ElMessageBox, ElMessage } from "element-plus";
import axios from "axios";
import vsAuth from "@lib/vsAuth";
import axiosUtil from '@src/lib/axiosUtil.js';

import { Search, Upload, Plus, RefreshRight } from '@element-plus/icons-vue'

export default defineComponent({
    name: 'TaskList',
    components: { AuditFrame, MonitorForm, Search, Plus, Upload, RefreshRight },
    props: {
        status: String,
        workflowid: String,//流程ID
        dbyb: String,
    },
    setup(props, { emit }) {
        const state = reactive({
            userInfo: vsAuth.getAuthInfo().permission,
            listQuery: {
                loginName: "",
                RWZT: '1',
                LCLB: '17337319287684954364927'
            },
            status: props.status,
            pageSize: 10,
            pageNum: 1,
            totalPage: 0,
            tableHeight: "100%",
            tableData: [],
            showDialog: false,
            dialogTitle: "",
            queryParams: {},
            otherParams: {},
            lx: "",
            LCZTList: [
                {
                    label: '审核中',
                    value: '1'
                },
                {
                    label: '已审核',
                    value: '3'
                },
            ],
            LCLBList: [],
        })
        const onSearch = () => {
            let ld = ElLoading.service({ target: "#taskDiv", text: "正在加载数据，请稍后...", });
            let queryParams = {
                ...state.listQuery,
                page: state.pageNum,
                size: state.pageSize,
                workflowid: state.workflowid,
            };

            axiosUtil.get('/backend/workFlow/getLcTasks', queryParams)
                .then((res) => {
                    state.totalPage = res.data.total;
                    state.tableData = res.data.list;
                    ld.close();
                })
                .catch((error) => {
                    ld.close();
                });
        }

        const onSizeChange = (val) => {
            state.pageSize = val;
            onSearch();
        }
        const onCurrentChange = (val) => {
            state.pageNum = val;
            onSearch();
        }
        const onEnd = (row) => {
            
            let params = {
                businessId: row.ID,
                processId: row.WORKFLOWID
            }
            ElMessageBox.confirm('确定结束该流程?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                axiosUtil.post('/backend/workFlow/updateBusinessStatus', params).then(res => {
                    if (res.data.status) {
                        ElMessage({
                            message: '已将' + row.PROCESSINSTANCENAME + '流程结束！',
                            type: 'success'
                        })
                        onSearch()
                    }
                })
            }).catch(() => {
                ElMessage({ message: '已取消', type: 'success', })
            });
        }
        const onMonitor = (row) => {
            state.showDialog = false;
            nextTick(() => {
                state.lx = "monitor";
                state.dialogTitle = row.PROCESSINSTANCENAME + "-业务跟踪";
                state.queryParams = {
                    processId: row.WORKFLOWID,
                    processInstanceId: row.WORKFLOWINSTANCEID,
                    id: row.id,
                };
                state.showDialog = true;
            });
        }

        const onAudit = (row) => {
            state.showDialog = false;
            nextTick(() => {
                state.lx = "audit";
                state.dialogTitle = row.PROCESSINSTANCENAME + "-监控";
                state.queryParams = {
                    processId: row.WORKFLOWID,
                    activityId: row.ACTIVITYID,
                    engineType: row.ENGINETYPE,
                    type: row.MKID,
                    taskId: row.TASKID,
                    processInstanceId: row.WORKFLOWINSTANCEID,
                    businessId: row.ID,
                    Processversion: row.PROCESSVERSION,
                    processInstanceName: row.PROCESSINSTANCENAME,
                    status: '3',//审批按钮不可查看
                    byzd10: row.BYZD10   //该参数配置的是角色代码，用于查询下节点办理人
                };
                // var pageFlag='SH';
                // var editable=false;
                // if((row.WORKFLOWID=='YJPJ_CBSPJ'||row.ACTIVITYID=='1')&&state.listQuery.status == "1"){
                //     pageFlag='edit';
                //     editable=true;
                // }
                state.otherParams = {
                    id: row.ID,
                    editable: false,
                    operation: 'view',
                }
                state.showDialog = true;
            });
        }

        const recall = (row) => {//撤回
            state.queryParams = {
                processId: row.WORKFLOWID,
                activityId: row.ACTIVITYID,
                engineType: row.ENGINETYPE,
                type: row.MKID,
                taskId: row.TASKID,
                processInstanceId: row.WORKFLOWINSTANCEID,
                businessId: row.ID,
                Processversion: row.PROCESSVERSION,
                processInstanceName: row.PROCESSINSTANCENAME,
                status: state.status,
                byzd10: row.BYZD10   //该参数配置的是角色代码，用于查询下节点办理人
            };
            ElMessageBox.confirm('确定撤回该流程?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                axios({
                    method: "post",
                    url: api.repealTask(),
                    data: "varJson=" + JSON.stringify(state.queryParams),
                })
                    .then((res) => {
                        console.log(res)
                        if (res.status == '200' && res.data.result == '1') {
                            ElMessage({ message: '撤回成功!', type: 'success', });
                            onSearch();
                        } else {
                            ElMessage({ message: result.data.info, type: 'error', });
                        }
                    })
                    .catch((error) => {
                        ElMessage({ message: '撤回失败！', type: 'warning', })
                    });
            }).catch(() => {
                ElMessage({ message: '已取消', type: 'info', })
            });
        }

        const deleteTask = (row, flag) => {//删除流程
            var message = "您是否确定要清理该流程，流程清理后业务数据将恢复到未提交状态，流程清理后无法恢复，请谨慎操作！";
            if (flag == 'deleteAll') {
                message = "您是否确定要完全删除该业务的流程数据和业务数据，删除后无法恢复，请谨慎操作！";
            }
            var queryParams = {
                processId: row.WORKFLOWID,
                processInstanceId: row.WORKFLOWINSTANCEID,
                businessId: row.ID,
                flag: flag,
            };
            ElMessageBox.confirm(message, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                axiosUtil.post('/backend/workFlow/deleteTask', queryParams)
                    .then((res) => {
                        onSearch();
                    })
                    .catch((error) => {
                        ElMessage({ message: '删除失败', type: 'error', })
                    });
            }).catch(() => {
                ElMessage({ message: '已取消', type: 'info', })
            });
        }




        /**
     * 审核后刷新数据
     */
        const onAuditClose = () => {
            if (state.lx == "audit" && state.status != "3") {
                setTimeout(() => {
                    onSearch();
                }, 2000)
            }
        }

        const indexMethod = (index) => {
            return (state.pageNum - 1) * state.pageSize + index + 1
        }

        // 流程类别 
        const getDictData = async () => {
            var res = await axiosUtil.get('/backend/workFlow/dict_process')
            state.LCLBList = res.data
        }

        onMounted(async () => {
            onSearch();
            getDictData();
        })

        return {
            ...toRefs(state),
            indexMethod,
            onSearch,
            onAuditClose,
            recall,
            onAudit,
            onSizeChange,
            onCurrentChange,
            onMonitor,
            getDictData,
            deleteTask,
            onEnd
        }
    }
})
</script>
<style scoped>
.root>>>.el-dialog__body {
    padding: 10px;
}
</style>