<template>
  <div class="context" v-loading="loading">
    <div class="banner">
      <el-icon :size="25" @click="goBack"><ArrowLeftBold /></el-icon>
      <div style="font-weight: bold;font-size: 16px;color: #409EFF">队伍信息查看</div>
      <div></div>
    </div>
    <!--    {{formData}}-->
    <div class="info-card">
      <div style="flex: 1;font-weight: bold">
        <appRow label="队伍名称：">12313</appRow>
        <appRow label="队伍编号：">12313</appRow>
        <appRow label="发证日期：">12313</appRow>
        <appRow label="服务专家：">12313</appRow>
      </div>

      <div class="qrcode">
        <QrcodeVue :size="100" :value="qrcodeData" level="H"/>
      </div>

    </div>

    <div style="padding: 10px;display: flex;align-items: center;gap: 10px;color: #409EFF;font-weight: bold">
      <el-icon :size="30"><OfficeBuilding /></el-icon>
      队伍基本信息
    </div>

    <div class="info-context">
      <el-tabs v-model="ActiveTab" tab-position="left" >
        <el-tab-pane :name="item.SJMBBM" :label="item.SJMBMC"
                     v-for="(item,index) in compList" :key="item.SJMBBM">
          <component
              class="comp-context"
              v-if="formData[item.SJMBBM]"
              v-model="formData"
              :ref="(el) => setRefMap(el, item.SJMBBM)"
              :is="ComponentDic[item.SJMBBM]"
              :defaultData="formData[item.SJMBBM]"
              :row="formData[item.SJMBBM]"
              :DWYWID="DWYWID"
              :TYXYDM="formData?.JBXX?.TYXYDM"
              :BGJL="bgInfo"
              YWLXDM="BG"
              :editable="false"
              :resultTableData="resultFormData?.[item.SJMBBM]"
          ></component>
        </el-tab-pane>
      </el-tabs>
    </div>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, markRaw} from "vue";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import appRow from "@views/app/common/appRow";
import QrcodeVue from 'qrcode.vue'
import {OfficeBuilding,ArrowLeftBold} from "@element-plus/icons-vue";
import {useRouter} from "vue-router";

import jbxxTab from "@views/app/cbs/cbsjbxx/commonTab/jbxxTab";
import zzxxTab from "@views/app/cbs/cbsjbxx/commonTab/zzxxTab";
import ryxxTab from "@views/app/cbs/cbsjbxx/commonTab/ryxxTab";
import sbxxTab from "@views/app/cbs/cbsjbxx/commonTab/sbxxTab";
import yjxxTab from "@views/app/cbs/cbsjbxx/commonTab/yjxxTab";
import tdxxTab from "@views/app/cbs/cbsjbxx/commonTab/tdxxTab";
import zdxxTab from "@views/app/cbs/cbsjbxx/commonTab/zdxxTab";
import dwxxTab from "@views/app/cbs/cbsjbxx/commonTab/dwxxTab";
import clxxTab from "@views/app/cbs/cbsjbxx/commonTab/clxxTab";
import {router} from "@core";

export default defineComponent({
  name: '',
  components: {appRow,QrcodeVue,OfficeBuilding,ArrowLeftBold},
  props: {},
  setup(props, {emit}) {
    const { currentRoute } = useRouter()
    const route = currentRoute.value
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: false,
      DWYWID: route.query.id,
      ActiveTab: 'JBXX',
      formData: {},
      resultFormData: {},
      ComponentDic: {
        JBXX: markRaw(jbxxTab),
        ZZXX: markRaw(zzxxTab),
        RYXX: markRaw(ryxxTab),
        SBXX: markRaw(sbxxTab),
        YJXX: markRaw(yjxxTab),
        TDXX: markRaw(tdxxTab),
        ZDXX: markRaw(zdxxTab),
        DWXX: markRaw(dwxxTab),
        CLXX: markRaw(clxxTab),
      },
      refMap: {},

      compList: [],
      MBMXList: [],

      bgInfo: {},

      qrcodeData: '123sadkhkl1klzxjdkljaslkhdjklh21'

    })

    const getFormData = (DWYWID, resForm, isLs) => {
      let params = {
        DWYWID: DWYWID
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/cbsyj/getTeamResultInfo', params).then((res) => {
        let resData = state.MBMXList.reduce((t, i) => {
          t[i.SJMBBM] ? t[i.SJMBBM].push(i) : (t[i.SJMBBM] = [i]);
          return t;
        }, {})
        Object.entries(res.data || {}).forEach(([key, value]) => {
          if (value) {
            //排除null
            if (Array.isArray(value)) {
              //list的话且不为空数组的时赋值
              if (value.length || isLs) resData[key] = value;
            } else {
              resData[key] = value;
            }
          }
        })

        state[resForm] = resData
        state.loading = false
      })
    }


    const getTabList = () => {
      let params = {
        DWYWID: state.DWYWID,
        MBLX: 'DW',
      }
      state.loading = true
      axiosUtil.get('/backend/sccbsgl/report/selecBqListByJgId', params).then((res) => {
        let tab = res.data.tabList || []
        state.MBMXList = res.data.MBMXList || []
        state.MBMXList = state.MBMXList.map((i) => ({
          ...i,
          ZYMC: i.ZYFLMC,
          ZYBM: i.ZYFLDM,
          SHZT: "0",
        }))
        let MBLX = res.data.MBLX || []
        state.compList = [...tab]
        if(state.compList.length>0){
          state.ActiveTab=state.compList[0].SJMBBM
        }
        // if (MBLX.map(i => i.MBLX).includes('DW')) {
        //   state.compList.push({SJMBMC: "队伍信息", SJMBBM: "DWXX"})
        // }
        // } else {
        //   state.compList.push({SJMBMC: "引入申请信息", SJMBBM: "YRSQXX"})
        // }
        getFormData(state.DWYWID, 'formData')
      })
    }



    const setRefMap = (el, name) => {
      state.refMap[name] = el
    }

    const goBack = () => {
      router.back();
    }

    onMounted(() => {
      getTabList()
    })

    return {
      ...toRefs(state),
      setRefMap,
      goBack
    }
  }

})
</script>

<style scoped>
.context{
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #e1e1e1;
  overflow: auto;
}
.info-card{
  padding: 10px;
  display: flex;
  border-top: 1px solid #8c939d;
  border-bottom: 1px solid #8c939d;
  background-color: white;
}
.info-context{
  border: 1px solid #8c939d;
  min-height: 500px;
  margin: 10px;
  background-color: white;
}
.comp-context{
  height: calc(100vh - 250px);
  overflow-y: auto;
}
.banner{
  height: 40px;
  background-color: white;
  align-items: center;
  justify-content: space-between;
  display: flex;
}
</style>
