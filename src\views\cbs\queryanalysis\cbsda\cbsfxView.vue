<template>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="190px" size="default" v-loading="loading" @submit.prevent>
        <el-row class="grid-row">
            <el-col :span="24" class="grid-cell">
                <el-table 
                    :data="tableData" 
                    class="lui-table"
                    border
                    stripe
                    height="calc(100vh - 300px)"
                    size="default" 
                    highlight-current-row>
                    <el-table-column type="index" header-align="center" align="center" width="60">
                    </el-table-column>
                    <el-table-column v-for="(item,index) in columns" :key="index" :prop="item.prop" :label="item.label" header-align="center" align="left" min-width="150">
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
const props = defineProps({
    varDetail: {
        type: Array,
        default: () => ([])
    },
});
const emit = defineEmits([]);
const columns = ref([]);
const tableData = ref([]);
onMounted(() => {
    for(let i=0;i<props.varDetail[0].length;i++){
        columns.value.push(
            {label:props.varDetail[0][i].key,prop:"prop" + i}
        );
    }
    if(props.varDetail.length > 1){
        for(let m=0;m<props.varDetail.length;m++){
            let row = {};
            for(let i=0;i<props.varDetail[m].length;i++){
                row[columns.value[i].prop] = props.varDetail[m][i].value
            }
            tableData.value.push(row);
        }
    }
});
defineExpose({});
</script>

<style scoped>
</style>