<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="LCID">
            <el-select v-model="listQuery.LCID" class="full-width-input" placeholder="请选择流程" clearable @change="getDataList">
              <el-option v-for="(item, index) in LCOptions" :key="index" :label="item.BZ"
                         :value="item.PROCESSID"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="BUSINESSNAME" label="节点名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="ROLE_CODE" label="角色编码" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="ROLE_NAME" label="角色名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="ROLE_DESC" label="角色描述" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>


              <el-table-column prop="CZ" label="操作" align="center" width="180" fixed="right">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">配置人员
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="角色人员配置"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <lcjspzEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import lcjspzEdit from "@views/xtgl/lcjspz/lcjspzEdit";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus, lcjspzEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,

      LCOptions: []
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/common/lcjspz/selectLcjsPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    const editRow = (row) => {
      state.params = {editable: true, id: row.ROLE_ID, operation: 'edit'}
      state.dialogVisible = true
    }

    const getLcxx = () => {
      axiosUtil.get('/backend/common/lcjspz/selectLcList', null).then((res) => {
        state.LCOptions = res.data || []
      });
    }


    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getDataList()
      getLcxx()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      closeForm,

    }
  }

})
</script>

<style scoped>

</style>
