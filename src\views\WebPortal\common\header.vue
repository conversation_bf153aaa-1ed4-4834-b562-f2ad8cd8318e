<!--
 * @Description: 江汉市场管理门户-头部
 * @Author: WLF (<EMAIL>)
 * @Date: 2024-11-14 08:49:04
 * @LastEditTime: 2024-12-14 08:54:08
 * @FilePath: \jhscgl-xieyun-front\src\views\WebPortal\common\header.vue
 * @copyright : Copyright (c) 2024 wlf
-->
<template>
    <div>
        <div class="header">
            <div class="loginButton">
                <span>您好，</span>
                <span v-if="loginName">{{ loginName }}<span class="into-dashboard" @click="goDashboard">进入工作办理</span></span>
                <span v-else @click="login">请[登录]</span>
            </div>
        </div>
        <div class="container">
            <div class="body">
                <el-menu :default-active="activeIndex" background-color="#b81515" text-color="#fff" class="my-el-menu"
                    active-text-color="#f19823" mode="horizontal" @select="handleSelect">
                    <el-menu-item index="1"><span class="menuLable">首页</span></el-menu-item>
                    <el-menu-item index="2"><span class="menuLable">信息动态</span></el-menu-item>
                    <el-menu-item index="3"><span class="menuLable">承包商专区</span></el-menu-item>
                    <el-menu-item index="4"><span class="menuLable">选商专区</span></el-menu-item>
                    <el-menu-item index="5"><span class="menuLable">评标专家库</span></el-menu-item>
                    <!--<el-menu-item index="6"><span class="menuLable">股权穿透</span></el-menu-item>
                    <el-menu-item index="7"><span class="menuLable">标书查重</span></el-menu-item>-->
                    <el-menu-item index="8"><span class="menuLable">帮助中心</span></el-menu-item>
                </el-menu>
            </div>
        </div>
    </div>
</template>

<script>

import { defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount } from "vue";
import vsAuth from "@lib/vsAuth";
export default defineComponent({
    name: '',
    components: {},
    props: {
        modelValue: {
            type: String
        }
    },
    setup(props, { emit }) {
        const state = reactive({
            activeIndex: '1',
            loginName: vsAuth.getAuthInfo().permission.userName
        })
        const handleSelect = (key, keyPath) => {
            emit('update:modelValue', key)
            emit('resetItemInd')
        }
        const login = () => {
            window.location.href='/loginxieyun'
        }

      const goDashboard =()=>{
        window.location.href='/dashboard'
      }

        onMounted(() => {

        })

        return {
            ...toRefs(state),
            handleSelect,
            login,
          goDashboard
        }
    }

})
</script>

<style scoped>
.header {
    height: 350px;
    background-size: 100% 100%;
    background-position: center;
    background-image: url('@static/img/webPortal/bannerXbyt.png');
    /* width: 100%; */
    width: 1300px;
    margin: 0 auto;
    /* 确保 header 宽度覆盖整个屏幕 */
    position: relative;
    /* 设置定位，以便 z-index 生效 */
    z-index: 1;
    /* 设置 z-index 值 */
}

.loginButton {
    float: right;
    min-width: 170px;
    height: 51px;
    position: absolute;
    right: 10px;
    top: 20px;
}

.container {
    width: 100%;
    min-width: 1300px;
    height: auto;
    overflow: hidden;
    margin: 0 auto;
    /* 实现水平居中 */
    position: relative;
    /* 设置定位，以便 z-index 生效 */
    z-index: 2;
    /* 设置 z-index 值，确保高于 header */
    margin-top: -190px;
}

.body {
    width: 1300px;
    height: auto;
    margin: 0 auto;
}

.menuLable {
    font-family: "黑体", "Heiti", sans-serif;
    font-weight: bold;
    font-size: 20px;
}

.el-menu--horizontal>.el-menu-item {
    margin: 0 0 0 37px;
}
.into-dashboard{
  cursor: pointer;
  border-bottom: 1px solid;
  margin-left: 10px;
}
.into-dashboard:hover{
  font-weight: bold;
  color: #2A96F9;
}


</style>
