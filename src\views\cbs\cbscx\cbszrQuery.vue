<template>
  <el-form ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" v-show="true" prop="SSDWMC">
          <el-input style="width:100%;" placeholder="请输入企业名称" v-model="listQuery.DWMC" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="listQuery.DWXTGS" clearable placeholder="选择单位系统归属">
            <el-option v-for="item in DWXTGSOptions" :key="item.DMXX" :label="item.DMMC"
                       :value="item.DMXX"></el-option>
          </el-select>
        </el-form-item>
      </el-col>

<!--      <el-col :span="5" class="grid-cell">-->
<!--        <el-form-item label="">-->
<!--          <el-select v-model="listQuery.QYLX" clearable placeholder="选择企业类型">-->
<!--            <el-option v-for="item in QYLXOptions" :key="item.DMXX" :label="item.DMMC"-->
<!--                       :value="item.DMXX"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--      </el-col>-->

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="listQuery.ZT" clearable placeholder="选择提交状态">
            <el-option v-for="item in ZTOptions" :key="item.DMXX" :label="item.DMMC"
                       :value="item.DMXX"></el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="4" class="grid-cell">
        <div class="static-content-item" v-show="true" style="display: flex;padding-left: 10px;text-align: right;">
          <el-button ref="button91277" @click="getDataList" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
        </div>
      </el-col>

    </el-row>

    <el-row ref="grid71868" :gutter="12" v-show="true">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper" v-show="true">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
            <el-table-column prop="CBSDWQC" label="企业名称" align="center" :show-overflow-tooltip="true" min-width="200"/>
            <el-table-column prop="TYXYDM" label="统一信用代码" align="center" :show-overflow-tooltip="true" min-width="150"/>
<!--            <el-table-column prop="QYLXMC" label="企业类型" align="center" :show-overflow-tooltip="true" width="100"/>-->
            <el-table-column prop="DWXTGSMC" label="单位系统归属" align="center" :show-overflow-tooltip="true" width="100"/>
            <el-table-column prop="ZCDZ" label="注册地址" align="center" :show-overflow-tooltip="true" min-width="100"/>
            <el-table-column prop="LXRXM" label="联系人" align="center" :show-overflow-tooltip="true" width="80"/>
            <el-table-column prop="LXRSJH" label="联系人手机" align="center" :show-overflow-tooltip="true" width="140"/>
            <el-table-column prop="DWZTMC" label="状态" align="center" :show-overflow-tooltip="true" width="70">
              <template #default="{row}">
                <el-tag v-if="row.CBSSHZT==='1'" type="primary" effect="light">提交</el-tag>
                <el-tag v-else-if="row.CBSSHZT==='2'" type="primary" effect="light">审批完成</el-tag>
                <el-tag v-else type="success" effect="light">保存</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="TJDWMC" label="推荐单位" align="center" :show-overflow-tooltip="true" min-width="100"/>
            <el-table-column prop="CZ" label="操作" align="center" width="100">
              <template #default="scope">
                <el-button size="small" class="lui-table-button" type="primary" @click="viewData(scope.row)">查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, onUnmounted} from "vue";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import axiosUtil from "../../../lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth";
import {mixin} from "@src/assets/core/index";
import TabFun from "../../../lib/tabFun";
import cbsjbxxIndex from "@views/cbs/cbsyj/index.vue"
import dwjbxx from "@views/cbs/cbsyj/yrsqxxIndex.vue"
import comFun from "@lib/comFun";
const {vsuiRoute, vsuiEventbus} = mixin();

export default defineComponent({
  name: '',
  components: {Search, Plus, Upload},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        page: 1,
        size: 10,
        DWMC: '',
        DWXTGS: null,
        QYLX: null
      },
      isAdmin:false,
      tableData: [],
      total: 0,
      rules: {},
      dialogVisible: false,
      params: {},

      DWXTGSOptions:[],
      QYLXOptions:[],
      ZTOptions:[{DMXX: '0',DMMC: '保存'},{DMXX: '1',DMMC: '提交'}]
    })

    const getDataList = () => {
      let params = {
        ...state.listQuery,
      }
      if(!state.isAdmin){
          params.TJDWID=state.userInfo.orgnaId
      }
      axiosUtil.get('/backend/sccbsgl/report/selectCbsywsjPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    //查看
    const viewData = (row) => {
      let editable = false;
      if (row.DWLX === 'DW') {
        TabFun.addTabByCustomName(
            row.CBSDWQC,
            'dwxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
            dwjbxx,
            {
          DWYWID: row.DWYWID,
          YWLXDM: 'BG',
          backPath: '/contractors/zrfqIndex',
          JGDWYWID: row.JGDWYWID,
          editable: editable,
          from:'YWBMBA'
        },);
        return false;
      }
      const {DWYWID: uuId, MBID, MBLX, ZYFLDM, YWLXDM, EXTENSION} = row;
      TabFun.addTabByCustomName(
          row.CBSDWQC+'1',
          'dwxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
          cbsjbxxIndex,
          {
            uuId,
            MBID,
            MBLX,
            ZYFLDM,
            YWLXDM: YWLXDM ?? 'ZR',
            JGDWYWID: row.JGDWYWID,
            EXTENSION,
            editable: editable,
            from:'YWBMBA'
          },
          {}
      );
      // vsuiEventbus.emit("reloadCbsjbxx", {
      //   uuId,
      //   MBID,
      //   MBLX,
      //   ZYFLDM,
      //   YWLXDM: YWLXDM ?? 'ZR',
      //   JGDWYWID: row.JGDWYWID,
      //   EXTENSION,
      //   editable: editable
      // });
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/common/selectDMB', params)
      state[resList] = res.data
    }

    onMounted(() => {
      let roleList=vsAuth.getAuthInfo().permission.roleList;
      if(roleList&&roleList.length>0){
        for(let i=0;i<roleList.length;i++){
          if(roleList[i].roleCode=='SCGL_SCGLY'){//市场管理员
            state.isAdmin=true;
            break;
          }
        }
      }
      getDataList();
      getDMBData('DWXTGS', 'DWXTGSOptions')
      getDMBData('QYLX', 'QYLXOptions')
      vsuiEventbus.on("reloadBaList", getDataList);
    })
    onUnmounted(() => {
      vsuiEventbus.off("reloadBaList", getDataList);
    });

    return {
      ...toRefs(state),
      viewData,
      indexMethod,
      closeForm,
      getDataList

    }
  }

})
</script>

<style scoped>

</style>
