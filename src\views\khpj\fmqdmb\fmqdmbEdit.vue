<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 120px;text-align: center;padding-bottom: 2px">
        基础信息
      </div>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="模板名称：" prop="MBMC">
            <el-input v-model="formData.MBMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="创建时间：" prop="CJSJ">
            <div style="margin-left: 10px">{{formData.CJSJ}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="更新时间：" prop="XGSJ">
            <div style="margin-left: 10px">{{formData.XGSJ}}</div>
          </el-form-item>
        </el-col>


        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="模板描述：" prop="MBMS">
            <el-input v-model="formData.MBMS" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 120px;text-align: center;padding-bottom: 2px">
        详细指标配置
      </div>

      <div>
        <div style="text-align: right;padding-bottom: 10px">
          <el-button ref="button91277" @click="addRow" type="primary" v-if="editable">添加</el-button>
        </div>
        <el-table ref="datatable91634" :data="formData.XXZBList" height="calc(300px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
          <el-table-column prop="LBBM" label="类别名称" align="center"
                           :show-overflow-tooltip="true" width="180">
            <template #default="{row,$index}">
              <el-form-item label="" :prop="`XXZBList.${$index}.LBBM`" label-width="0" :rules="tableRules.LBBM">
                <el-select v-model="row.LBBM" class="full-width-input"
                           :disabled="!editable" @change="(value)=>row.LBMC=FMLBOptions.find(item=>item.DMXX===value)?.DMMC"
                           clearable>
                  <el-option v-for="(item, index) in FMLBOptions" :key="index" :label="item.DMMC"
                             :value="item.DMXX" :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="FMQDXW" label="负面清单行为" align="center"
                           :show-overflow-tooltip="true" min-width="180">
            <template #default="{row,$index}">
              <el-form-item label="" :prop="`XXZBList.${$index}.FMQDXW`" label-width="0" :rules="tableRules.FMQDXW">
                <el-input v-model="row.FMQDXW" type="text" placeholder="请输入" clearable :disabled="!editable">
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
<!--          <el-table-column prop="CLFS" label="处理方式" align="center"-->
<!--                           :show-overflow-tooltip="true" width="250">-->
<!--            <template #default="{row,$index}">-->
<!--              <el-form-item label="" :prop="`XXZBList.${$index}.CLFS`" label-width="0" :rules="tableRules.CLFS">-->
<!--                <el-select v-model="row.CLFS" class="full-width-input"-->
<!--                           :disabled="!editable"-->
<!--                           clearable>-->
<!--                  <el-option v-for="(item, index) in CLFSOptions" :key="index" :label="item.DMMC"-->
<!--                             :value="item.DMMC" :disabled="item.disabled"></el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column prop="SFYX" label="是否有效" align="center"
                           :show-overflow-tooltip="true" width="120">
            <template #default="{row,$index}">
              <el-form-item label="" :prop="`XXZBList.${$index}.SFYX`" label-width="0" :rules="tableRules.SFYX">
                <el-select v-model="row.SFYX" class="full-width-input"
                           :disabled="!editable"
                           clearable>
                  <el-option v-for="(item, index) in SFYXOptions" :key="index" :label="item.DMMC"
                             :value="item.DMXX"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="CZ" label="操作" align="center" width="120" v-if="editable">
            <template #default="scope">
              <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData()" v-if="editable">确定</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      MBID: props.params.id,
      formData: {
        XXZBList: [],
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SFQY: '0',
      },
      rules: {
        MBMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        MBMS: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      tableRules:{
        LBBM: [{
          required: true,
          message: '请选择',
        }],
        FMQDXW: [{
          required: true,
          message: '请选择',
        }],
        CLFS: [{
          required: true,
          message: '请选择',
        }],
        SFYX: [{
          required: true,
          message: '请选择',
        }],
      },

      FMLBOptions: [],
      FMQDXWOptions: [],
      CLFSOptions: [],
      SFYXOptions: [{DMMC: '是',DMXX: '1'},{DMMC: '否',DMXX: '0'}],

    })
    const getFormData = () => {
      let params={
        MBID: state.MBID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/fmqdmbgl/selectFmqdmbById',params).then(res=>{
        state.formData=res.data
        state.formData.XGSJ=comFun.getNowTime()
        state.loading=false
      })
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const submitForm = () => {
      let params={
        ...state.formData,
        MBID: state.MBID,
        XGRZH: state.userInfo.userLoginName,
        SHZT: '1',
      }

      params.XXZBList.forEach((item,index)=>item.PXH=index+1)

      state.loading=true
      axiosUtil.post('/backend/sckhpj/fmqdmbgl/saveFmqdmbForm',params).then(res=>{
        ElMessage.success('保存成功')
        closeForm()
        state.loading=false
      })

    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            if(state.formData.XXZBList.length===0){
              ElMessage.error('请添加详细指标')
              resolve(false)
              return
            }
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const addRow = () => {
      state.formData.XXZBList.push({
        MXID: comFun.newId(),
        MBID: state.MBID,
      })
    }

    const deleteRow = (index) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.formData.XXZBList.splice(index,1)
        ElMessage.success({
          message: '删除成功!'
        });
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      getDMBData('FMLB', 'FMLBOptions')
      // getDMBData('FMQDXW', 'FMQDXWOptions')
      // getDMBData('CLFS', 'CLFSOptions')
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm,
      addRow,
      deleteRow

    }
  }

})
</script>

<style scoped>

</style>
