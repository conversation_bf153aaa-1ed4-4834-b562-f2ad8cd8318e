<template>
    <div class="out-box">
        <el-row>
            <el-col :span="20" class="out-box-title">
                <span>{{ prop.title }}</span>
            </el-col>
            <el-col :span="4" style="line-height: 50px;">
                <slot name="button"></slot>
            </el-col>
        </el-row>
        <slot name="content"></slot>
    </div>
</template>

<script setup>
    import {defineProps} from 'vue';
    const prop = defineProps({
        title: String
    });

</script>

<style scoped>

    .out-box{
        height: 100%;
        width: 100%;
        border: 2px solid #dbdbdb;
        background-color: #fff;
    }
    .out-box-title{
        padding-left: 70px;
        font-size: 20px;
    }
    .out-box-title > span{
        border-bottom: 4px solid #1684fc;
        display: inline-block;
        padding: 10px 40px;
    }
</style>