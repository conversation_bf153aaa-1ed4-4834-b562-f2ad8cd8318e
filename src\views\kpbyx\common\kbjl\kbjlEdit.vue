<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;display: inline-block;color: #2A96F9;
      border-bottom: 3px solid #2A96F9;text-align: center;padding-bottom: 2px;padding-left: 20px;padding-right: 20px;">
        开标记录表
      </div>

      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="开标地点：" prop="KBDD">
            <el-input v-model="formData.KBDD" type="text" placeholder="请输入" clearable :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="开标时间：" prop="KBSJ">
            <el-date-picker
                v-model="formData.KBSJ"
                :disabled="true"
                type="datetime"
                clearable
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col v-if="formData.PSBF_XMLB === 'SYGC_QT' || formData.PSBF_XMLB === 'SYGC_GDBJ'" :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="复核人：" prop="FHR">
            <signImageUpload v-if="params.KBJLID" :editable="editable" :busId="params.KBJLID"
                             busType="FHRQZ" ywlb="FHRQZ" v-model:signFile="FHRSignFile"/>
          </el-form-item>
        </el-col>

        <el-col v-if="formData.PSBF_XMLB === 'SYGC_QT' || formData.PSBF_XMLB === 'SYGC_GDBJ'" :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="计算人：" prop="JSR">
            <signImageUpload v-if="params.KBJLID" :editable="editable" :busId="params.KBJLID"
                             busType="JSRQZ" ywlb="JSRQZ" v-model:signFile="JSRSignFile"/>
          </el-form-item>
        </el-col>

        <el-col v-if="formData.PSBF_XMLB === 'SYGC_QT'" :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="" prop="JSR" label-width="calc(50% + 160px)" label-position="left">
            <template #label>
              <div style="color: #c03c29">
                *石油工程及其他类 浮动率招标   填写注意: (例)上浮5% 写成 0.05, 下浮5%写成-0.05。
              </div>
            </template>
            <!-- <el-input v-model="formData.FDZBL" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input> -->
          </el-form-item>
        </el-col>

        <el-col v-if="formData.PSBF_XMLB === 'SYGC_GDBJ'" :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="" prop="JSR" label-width="calc(50% + 160px)" label-position="left">
            <template #label>
              <div style="color: #c03c29">
                *固定标价 填写注意 ：实际没有报价，所以填写 "1"
              </div>
            </template>
            <!-- <el-input v-model="formData.FDZBL" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input> -->
          </el-form-item>
        </el-col>

        <el-col v-if="formData.PSBF_XMLB === 'GCJS'" :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="监督人：" prop="JDR">
            <signImageUpload v-if="params.KBJLID" :editable="editable" :busId="params.KBJLID"
                             busType="JDRQZ" ywlb="JDRQZ" v-model:signFile="JDRSignFile"/>
          </el-form-item>
        </el-col>

        <el-col v-if="formData.PSBF_XMLB === 'GCJS'" :span="12" class="grid-cell no-border-bottom">
          <el-form-item label="投标人代表：" prop="TBRDB">
            <signImageUpload v-if="params.KBJLID" :editable="editable" :busId="params.KBJLID"
                             busType="TBRDBQZ" ywlb="TBRDBQZ" v-model:signFile="TBRDBSignFile"/>
          </el-form-item>
        </el-col>

        <el-col v-if="formData.PSBF_XMLB === 'GCJS'" :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="工作人员：" prop="GZRY">
            <signImageUpload v-if="params.KBJLID" :editable="editable" :busId="params.KBJLID"
                             busType="GZRYQZ" ywlb="GZRYQZ" v-model:signFile="GZRYSignFile"/>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- <el-table ref="datatable91634" :data="formData.TBXXList" height="calc(250px)"
                :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column prop="BDMC" label="标段名称" align="center"
                         :show-overflow-tooltip="true" min-width="160"></el-table-column>
        <el-table-column prop="BDJ" label="标底价" align="center"
                         :show-overflow-tooltip="true" width="100"></el-table-column>
        <el-table-column prop="YS" label="预算" align="center"
                         :show-overflow-tooltip="true" width="100"></el-table-column>
        <el-table-column prop="ZGXJ" label="最高限价" align="center"
                         :show-overflow-tooltip="true" width="100"></el-table-column>
        <el-table-column prop="BZ" label="备注" align="center"
                         :show-overflow-tooltip="true" width="100"></el-table-column>
      </el-table> -->

      <el-table ref="datatable91634" :data="TBXXList" height="calc(250px)" :span-method="arraySpanMethod"
                :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column prop="BDMC" label="标段名称" align="center"
                         :show-overflow-tooltip="true" min-width="160"></el-table-column>
        <el-table-column prop="DWMC" label="队伍名称" align="center"
                         :show-overflow-tooltip="true" min-width="160"></el-table-column>
        <el-table-column prop="BJ" label="报价" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input-number v-if="editable" v-model="scope.row.BJ" :controls="false" style="width: 100%">
              </el-input-number>
              <span v-else>{{scope.row.BJ}}</span>
            </template>
        </el-table-column>
        <el-table-column prop="TBBZJ" label="投标保证金" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input-number v-if="editable" v-model="scope.row.TBBZJ" :controls="false" style="width: 100%">
              </el-input-number>
              <span v-else>{{scope.row.TBBZJ}}</span>
            </template>
        </el-table-column>
        <el-table-column prop="GQ" label="工期（天）" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input-number v-if="editable" v-model="scope.row.GQ" :controls="false" style="width: 100%">
              </el-input-number>
              <span v-else>{{scope.row.GQ}}</span>
            </template>
        </el-table-column>
        <el-table-column prop="XMJL" label="项目经理" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input v-if="editable" v-model="scope.row.XMJL" style="width: 100%">
              </el-input>
              <span v-else>{{scope.row.XMJL}}</span>
            </template>
        </el-table-column>
        <el-table-column prop="SFZH" label="身份证号" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input v-if="editable" v-model="scope.row.SFZH" style="width: 100%">
              </el-input>
              <span v-else>{{scope.row.SFZH}}</span>
            </template>
        </el-table-column>
        <el-table-column prop="ZGZS" label="资格证书名称" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input v-if="editable" v-model="scope.row.ZGZS" :controls="false" style="width: 100%">
              </el-input>
              <span v-else>{{scope.row.ZGZS}}</span>
            </template>
        </el-table-column>

        <el-table-column prop="YS" label="预算" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input-number v-if="editable" v-model="scope.row.YS" :controls="false" style="width: 100%">
              </el-input-number>
              <span v-else>{{scope.row.YS}}</span>
            </template>
        </el-table-column>
        <el-table-column prop="BDJ" label="标底" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input-number v-if="editable" v-model="scope.row.BDJ" :controls="false" style="width: 100%">
              </el-input-number>
              <span v-else>{{scope.row.BDJ}}</span>
            </template>
        </el-table-column>>
          <el-table-column prop="ZGXJ" label="最高限价" align="center"
                           :show-overflow-tooltip="true" min-width="100">
              <template #default="scope">
                  <el-input-number v-if="editable" v-model="scope.row.ZGXJ" :controls="false" style="width: 100%">
                  </el-input-number>
                  <span v-else>{{scope.row.ZGXJ}}</span>
              </template>
          </el-table-column>>
        <el-table-column prop="BZ" label="备注" align="center"
                         :show-overflow-tooltip="true" min-width="100">
            <template #default="scope">
              <el-input v-if="editable" v-model="scope.row.BZ" :controls="false" style="width: 100%">
              </el-input>
              <span v-else>{{scope.row.BZ}}</span>
            </template>
        </el-table-column>
      </el-table>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="getFormData">刷新</el-button>
      </div>

    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, nextTick} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import signImageUpload from "@views/components/signImageUpload";


export default defineComponent({
  name: '',
  components: {signImageUpload},
  props: {
    params: {
      type: Object,
      required: true
    },
    fromParams: {
      type: Object,
      required: true
    },
    parentForm: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.parentForm.KBJL_WCZT!=='1',
      role: props.fromParams.role,
      KPBYXID: props.params.KPBYXID,
      JLID: props.params.JLID,
      KBJLID: props.params.KBJLID,
      formData:{
        KBJLID: comFun.newId(),
      },
      TBXXList: [],
      rules: {

      },
      FHRSignFile: null,
      JSRSignFile: null,
      JDRSignFile: null,
      TBRDBSignFile: null,
      GZRYSignFile: null

    })

    const getFormData = () => {
      let params={
        jlid: state.JLID,
        kbjlid: state.KBJLID
      }
      state.loading=true
      axiosUtil.get('/backend/kpbyx/kbjl/queryKbjl', params).then((res) => {
        state.formData = res.data.form || {};
        state.TBXXList = res.data.list || [];
        state.loading=false
      })
    }

    const saveData = (type) => {
      for(let i=0; i<state.TBXXList.length; i++){
        if(i > 0 && state.TBXXList[i].FABDID === state.TBXXList[i-1].FABDID){
            state.TBXXList[i].YS =  state.TBXXList[i-1].YS;
            state.TBXXList[i].BDJ =  state.TBXXList[i-1].BDJ;
            state.TBXXList[i].ZGXJ =  state.TBXXList[i-1].ZGXJ;
        }
      }
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      axiosUtil.post('/backend/kpbyx/kbjl/updateGysTbbj', {bjList: state.TBXXList}).then(res=>{
          if(res.data.success){
            ElMessage.success('保存成功！');
            if(type === 'submit'){
              emit('saveFromData', {KBJL_WCSJ: comFun.getNowTime(),KBJL_WCZT: '1'}, {})
              nextTick(() => {
                state.editable=false
                emit('nextStep', `已完成开标记录`)
              })
            }
          }else{
              ElMessage.error('保存失败！');
          }
      })
      return;
      



      // let params={
      //
      // }
      // state.loading=true
      // axiosUtil.post('/backend/kpbyx/tbrqd/saveTbrqzxx',params).then(res=>{
      //   ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
      //   emit('saveFromData', {TBRQD_WCSJ: comFun.getNowTime(),TBRQD_WCZT: '1'}, {})
      //   nextTick(() => {
      //     state.editable=false
      //     emit('nextStep', `已完成投标人签到`)
      //   })
      //   state.loading=false
      // })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    onMounted(() => {
      getFormData();
    })

    const arraySpanMethod = ({row,column,rowIndex,columnIndex}) => {
        if(columnIndex === 1 || columnIndex === 9 || columnIndex === 10 || columnIndex === 11){
            let nameSpan = getSpanNumber(state.TBXXList,"FABDID");
            return {
                rowspan: nameSpan[rowIndex],
                colspan: 1,
            };
        }
        
    }
    // 合并单元格
    const getSpanNumber = (data, prop) => {
        //data要处理的数组，prop要合并的属性，比如name
        //数组的长度，有时候后台可能返回个null而不是[]
        let length = Array.isArray(data) ? data.length : 0;
        if (length > 0) {
            //用于标识位置
            let position = 0;
            //用于对比的数据
            let temp = data[0][prop];
            //要返回的结果
            let result = [1];
            //假设数据是AABCC，我们的目标就是返回20120
            for (let i = 1; i < length; i++) {
                if (data[i][prop] == temp) {
                    //标识位置的数据加一
                    result[position] += 1;
                    //当前位置添0
                    result[i] = 0;
                } else {
                    //不相同时，修改标识位置，该位置设为1，修改对比值
                    position = i;
                    result[i] = 1;
                    temp = data[i][prop];
                }
            }
            //返回结果
            return result;
        } else {
            return [0];
        }
    }

    return {
      ...toRefs(state),
      getFormData,
      saveData,
      arraySpanMethod

    }
  }

})
</script>

<style scoped>

</style>
