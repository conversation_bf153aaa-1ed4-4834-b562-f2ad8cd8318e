<!--
 * @Description: 江汉市场管理门户-尾部
 * @Author: WLF (<EMAIL>)
 * @Date: 2024-11-14 08:49:04
 * @LastEditTime: 2024-11-23 17:06:07
 * @FilePath: \jhscgl-xieyun-front\src\views\WebPortal\common\footer.vue
 * @copyright : Copyright (c) 2024 wlf
-->
<template>
    <div>
        <div class="footer">
            <div class="label">西北油田@版权所有</div>
        </div>
    </div>
</template>

<script>

import { defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount } from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, Upload } from "@element-plus/icons-vue";
export default defineComponent({
    name: '',
    components: { Search, Upload, Plus },
    props: {},
    setup(props, { emit }) {
        const state = reactive({


        })


        onMounted(() => {

        })

        return {
            ...toRefs(state),
        }
    }

})
</script>

<style scoped>
.footer{
    background-color: #b81515;
    width: 1300px;
    height: 30px;
    margin: 0 auto;
    display: flex; /* 设置为 Flex 容器 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
}
.label{
    font-family: "黑体", "Heiti", sans-serif;
    font-weight: bold;
    font-size: 14px;
    color: white;
    margin: 0 auto;
    width: 150px;
}
</style>
