<!-- 评委抽取 -评标编辑页 -->
<template>
  <div>
    <el-form :model="formData" ref="vForm1" :rules="rules" label-position="right" label-width="110px" size="default"
             status-icon>
      <el-row :gutter="12">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="会议名称:" prop="HYMC">
            <el-input v-model="formData.HYMC" type="text" placeholder="请输入"
                      :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="评审时间:" prop="KBSJ">
            <el-date-picker v-model="formData.KBSJ" type="date" placeholder="请选择"
                            :disabled="!editable" value-format="YYYY-MM-DD"/>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="评审地点:" label-width="110px" prop="PBDD">
            <el-input v-model="formData.PBDD" type="text" placeholder="请输入"
                      :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="16" class="grid-cell">
          <el-form-item label=" 说明:" label-width="100px" prop="BZ">
            <el-input v-model="formData.BZ" type="textarea" placeholder="请输入" clearable
                      :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row>
      <div class="zdTitle">待评审项目</div>
      <el-row>
        <el-col :span="23">
          <el-table :data="formData.dpsxmData" height="160px" :border="true" :show-summary="false" size="default"
                    :stripe="false" :highlight-current-row="true" :cell-style="{ padding: '3px 0 ' }">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"></el-table-column>
            <el-table-column prop="XMMC" label="项目名称" align="center" min-width="200"></el-table-column>
            <el-table-column prop="XMBM" label="项目编号" align="center" min-width="200"></el-table-column>
            <el-table-column prop="XMLB" label="项目类别" align="center" min-width="200"></el-table-column>
            <el-table-column prop="SSDW" label="所属单位" align="center" min-width="200"></el-table-column>
            <el-table-column prop="XMED" label="项目额度" align="center" min-width="200"></el-table-column>
            <el-table-column prop="CZ" label="操作" align="center" min-width="100" v-if="editable">
              <template #default="scope">
                <el-button plain size="small" text type="primary"
                           @click="delXmData(scope.row,scope.$index)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="1">
          <el-button type="primary" size="small" @click="openDialog()"
                     v-if="editable">选择
          </el-button>
        </el-col>
      </el-row>
    </el-row>
    <el-form :model="formData" ref="vForm2" :rules="rules" label-position="right" label-width="110px" size="default"
             status-icon>
      <el-row>
        <div class="zdTitle">抽取规则</div>
        <el-col :span="8">
          <el-form-item label=" 总人数:" label-width="100px" prop="PWZRS">
            <el-input style="width: 240px;" v-model="formData.PWZRS" type="text" placeholder="请输入" clearable
                      :disabled="!editable"></el-input>
          </el-form-item>
          <el-form-item label=" 抽取人数:" label-width="100px" prop="CQRS">
            <el-input style="width: 240px;" v-model="formData.CQRS" type="text" placeholder="请输入" clearable
                      :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label=" 随机抽取条件:" label-width="120px" prop="SM">
            <el-table :data="formData.cqtjData" height="260px" :border="true" :show-summary="false"
                      size="default" :stripe="false" :highlight-current-row="true"
                      :cell-style="{ padding: '0px 0 ' }">
              <el-table-column type="index" width="60" fixed="left" label="序号"
                               align="center"></el-table-column>
              <el-table-column prop="KEY" label="条件" align="center" min-width="160">
                <template #default="scope">
                  <el-select v-model="scope.row.KEY" placeholder="请选择" clearable v-if="editable"
                             @change="changeCQTJ(scope.row)">
                    <el-option v-for="item in tjOptions" :key="item.DMXX" :label="item.DMMC"
                               :value="item.DMXX" :disabled="disabledTj(item.DMXX)">
                    </el-option>
                  </el-select>
                  <span v-else>{{ scope.row.KEY }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="LOIGC" label="逻辑" align="center" width="80"/>
              <el-table-column prop="VALUE" label="条件值" align="center" min-width="100">
                <template #default="scope">
                  <div v-if="editable">
                    <el-cascader
                        clearable
                        v-model="scope.row.VALUE"
                        :options="XCSZYOptions"
                        v-if="scope.row.KEY==='JSZYFW'"
                        :show-all-levels="false"
                        :props="{expandTrigger: 'hover',label:'ZYMC',value:'ZYBM',emitPath:false,multiple: true,}"
                    />
                    <el-input v-model="scope.row.VALUE" placeholder="请输入" clearable v-else>
                    </el-input>
                  </div>

                  <span v-else>{{ scope.row.VALUE }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="80" v-if="editable">
                <template #default="scope">
                  <el-button plain size="small" text type="primary"
                             @click="delTjData(scope.row, scope.$index)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <el-button type="primary" size="small" :icon="Plus" @click="addTjData()"
                     v-if="editable"></el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-form :model="formData" ref="vForm3" :rules="rules" label-position="right" label-width="110px" size="default"
             status-icon>
      <el-row>
        <div class="zdTitle">专家抽取</div>
        <el-col :span="24">
          <el-row>
            <el-col :span="18">
              <el-form-item label=" 提示信息:" label-width="100px" prop="ZRS">
                总人数为{{ zrs }}人，其中招标人{{ zbrdbList.length }}人，经济专家{{ jjzjrs }}人，技术专家{{ jszjrs }}人，其他{{ qtrs }}人。
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="sjcqRy" v-if="editable">随机抽取</el-button>
              <el-button type="primary" @click="openZJXZDialog" v-if="editable">指定</el-button>
              <el-button type="primary">查看回执</el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label=" 招标人代表:" label-width="100px" prop="ZBRDB">
                <el-input style="width: 240px;" v-model="formData.ZBRDB" type="text" placeholder="请输入"
                          clearable :disabled="true">
                  <template #append v-if="editable">
                    <el-button :icon="More" @click="openCheckRy('zbrdbList')"/>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" 监督人:" label-width="100px" prop="JDR">
                <el-input style="width: 240px;" v-model="formData.JDR" type="text" placeholder="请输入"
                          clearable :disabled="true">
                  <template #append v-if="editable">
                    <el-button :icon="More" @click="openCheckRy('jdrList')"/>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" 招标工作人员:" label-width="110px" prop="ZBGZRY">
                <el-input style="width: 240px;" v-model="formData.ZBGZRY" type="text" placeholder="请输入"
                          clearable :disabled="true">
                  <template #append v-if="editable">
                    <el-button :icon="More" @click="openCheckRy('zbgzryList')"/>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-table :data="formData.zjcqData" height="260px" :border="true" :show-summary="false"
                      size="default" :stripe="false" :highlight-current-row="true"
                      :cell-style="{ padding: '0px 0 ' }">
              <el-table-column type="index" width="60" fixed="left" label="序号"
                               align="center"></el-table-column>
              <el-table-column prop="ZJBH" label="专家编号" align="center" min-width="300"></el-table-column>
              <el-table-column prop="SJH" label="手机号" align="center" min-width="100"></el-table-column>
              <el-table-column prop="CSZYMC" label="专业" align="center" min-width="100"></el-table-column>
              <el-table-column prop="SZDWMC" label="所属单位" align="center" min-width="100"></el-table-column>
              <el-table-column prop="ZJLBMC" label="专家类别" align="center" min-width="100"></el-table-column>
              <el-table-column prop="SFQRCX" label="能否出席" align="center" min-width="100">
                <template #default="{row,$index}">
                  <el-form-item :prop="`zjcqData.${$index}.SFQRCX`" label-width="0" :rules="rules.SFQRCX"
                                style="margin-bottom: 0">
                    <el-select v-model="row.SFQRCX" placeholder="请选择" :disabled="!editable" clearable>
                      <el-option v-for="item in nfcxOptions" :key="item.value" :label="item.label"
                                 :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="BCXYY" label="原因说明" align="center" min-width="100">
                <template #default="{row,$index}">
                  <el-form-item :prop="`zjcqData.${$index}.BCXYY`" label-width="0" style="margin-bottom: 0">
                    <el-input v-model="row.BCXYY" type="text" placeholder="请输入" clearable
                              :disabled="!editable"/>
                  </el-form-item>
                </template>
              </el-table-column>
              <!--              <el-table-column prop="CZ" label="操作" align="center" min-width="100">-->
              <!--                <template #default="scope">-->
              <!--                  <el-button plain size="small" text type="primary"-->
              <!--                             @click="delZjcqData(scope.row.XMBS)">删除-->
              <!--                  </el-button>-->
              <!--                </template>-->
              <!--              </el-table-column>-->
            </el-table>
          </el-row>
        </el-col>

      </el-row>
    </el-form>
    <div style="width: 100%;display: flex;justify-content: center;">
      <el-row :gutter="12" style="width: 300px;">
        <el-col v-if="editable" :span="8">
          <el-button type="success" @click="validateForm('save')">暂存</el-button>
        </el-col>
        <el-col v-if="editable" :span="8">
          <el-button type="primary" @click="validateForm('submit')">提交</el-button>
        </el-col>
        <el-col :span="8">
          <el-button @click="closeForm()">返回</el-button>
        </el-col>
      </el-row>
    </div>
    <el-dialog
        v-if="xmdialogVisible"
        v-model="xmdialogVisible"
        title="待评审项目选择"
        @closed="closeXmForm"
        top="2vh"
        width="70%">
      <div>
        <cgxmxzForm :xmParams="xmParams" @closeXmForm="closeXmForm" @parentMethod="parentMethod"/>
      </div>
    </el-dialog>
    <el-dialog
        v-if="ryDialogVisible"
        v-model="ryDialogVisible"
        title="人员选择"
        top="2vh"
        width="900px">
      <div>
        <ryxz @getData="getRyxx"></ryxz>
      </div>
    </el-dialog>
    <el-dialog
        v-if="zjDialogVisible"
        v-model="zjDialogVisible"
        title="专家选择"
        top="2vh"
        width="900px">
      <div>
        <zjxz @getData="getZjxx" :fitterList="fitterList"></zjxz>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onMounted,
  watch, computed
}
  from 'vue'
import axiosUtil from "../../../lib/axiosUtil";
import vsAuth from "../../../lib/vsAuth.js";
import {ElMessage} from 'element-plus'
import {Plus, More} from '@element-plus/icons-vue'
import comFun from "../../../lib/comFun";
import Vsfileupload from "../../components/vsfileupload";
import cgxmxzForm from "./cgxmxz.vue"
import Ryxz from "../zjcrk/common/ryxz";
import Zjxz from "../zjcrk/common/zjxz";

export default defineComponent({
  components: {Vsfileupload, cgxmxzForm,Ryxz,Zjxz},
  props: {
    params: {
      type: Object,
      required: true
    }
  },

  setup(props, context) {
    const state = reactive({
      editable: props.params.editable,
      operation: props.params.operation,
      id: props.params.id,
      xmdialogVisible: false,
      ryDialogVisible: false,
      zjDialogVisible: false,
      listName: null,
      userInfo: vsAuth.getAuthInfo().permission,
      CQGZBS: null,
      PWCQJLBS: null,
      CQPC: null,
      formData: {
        HYMC: null,
        KBSJ: null,
        PBDD: null,
        BZ: null,
        PWZRS: null,
        CQRS: null,
        ZBRDB: null,
        JDR: null,
        ZBGZRY: null,
        dpsxmData: [],
        cqtjData: [],
        zjcqData: [],
      },
      xmParams: null,
      rules: {
        HYMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        KBSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        PBDD: [{
          required: true,
          message: '字段值不可为空',
        }],
        BZ: [{
          required: true,
          message: '字段值不可为空',
        }],
        PWZRS: [{
          required: true,
          message: '字段值不可为空',
        }],
        CQRS: [{
          required: true,
          message: '字段值不可为空',
        }],
        SFQRCX: [{
          required: true,
          message: '字段值不可为空',
        }],
        BCXYY: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZBRDB: [{
          required: true,
          message: '字段值不可为空',
        }],
        JDR: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZBGZRY: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      tjOptions: [],//条件下拉框
      tjzOptions: [],//条件值下拉框
      nfcxOptions: [
        {
          value: '1',
          label: '是'
        },
        {
          value: '0',
          label: '否'
        }
      ],//能否出席下拉框
      ljOptions: [],//逻辑下拉框

      zbrdbList: [],
      jdrList: [],
      zbgzryList: [],
      XCSZYOptions: [],

      fitterList:[]
    })
    watch(() => state.zbrdbList, (newVal) => {
      let res = []
      if (newVal) {
        newVal.forEach(item => {
          res.push(item.XM)
        })
      }
      state.formData.ZBRDB = res.join(',')
    })
    watch(() => state.jdrList, (newVal) => {
      let res = []
      if (newVal) {
        newVal.forEach(item => {
          res.push(item.XM)
        })
      }
      state.formData.JDR = res.join(',')
    })
    watch(() => state.zbgzryList, (newVal) => {
      let res = []
      if (newVal) {
        newVal.forEach(item => {
          res.push(item.XM)
        })
      }
      state.formData.ZBGZRY = res.join(',')
    })
    const cxrs = computed(() => {
      if (state.formData.zjcqData) {
        return state.formData.zjcqData.filter(item => {
          return item.SFQRCX === '1'
        }).length
      } else {
        return 0
      }
    })
    const jjzjrs = computed(() => {
      if (state.formData.zjcqData) {
        return state.formData.zjcqData.filter(item => {
          return item.ZJLB === 'JJ' && item.SFQRCX === '1'
        }).length
      } else {
        return 0
      }
    })
    const jszjrs = computed(() => {
      if (state.formData.zjcqData) {
        return state.formData.zjcqData.filter(item => {
          return item.ZJLB === 'JS' && item.SFQRCX === '1'
        }).length
      } else {
        return 0
      }
    })
    const zrs = computed(() => {
      return cxrs.value + state.zbrdbList.length + state.jdrList.length + state.zbgzryList.length
    })
    const qtrs = computed(() => {
      return zrs.value - state.zbrdbList.length - jjzjrs.value - jszjrs.value
    })

    const instance = getCurrentInstance()
    const submitForm = (type) => {
      let params = JSON.parse(JSON.stringify(state.formData))
      let zjcqData = []
      zjcqData.push(...state.zbrdbList, ...state.jdrList, ...state.zbgzryList, ...params.zjcqData)
      params.zjcqData = zjcqData
      params.cqtjData.forEach(item => {
        if (item.VALUE instanceof Array) {
          item.VALUE = item.VALUE.join(',')
        }
      })
      if (state.operation !== 'add') {
        params.XGR = state.userInfo.userLoginName
        params.XGSJ = comFun.getNowTime();
      } else {
        params.PBHYBS = props.params.id;
        params.CJR = state.userInfo.userLoginName
        params.CJSJ = comFun.getNowTime();
        params.HYLX = 'ZB'
      }
      if (type === 'submit') {
        params.SHR = state.userInfo.userLoginName
        params.SHRQ = comFun.getNowTime();
      }
      params.CQGZBS = state.CQGZBS
      return new Promise(resolve => {
        axiosUtil.post('/backend/zjgl/pwcqgl/savePwcqglData', params).then((res) => {
          if (res.message === 'success') {
            resolve(true)
            if (type) {
              ElMessage({
                message: `${type === 'submit' ? '提交' : '保存'}成功`,
                type: 'success',
              })
            }
            if (type === 'submit') {
              context.emit("closePbForm")
            }
          } else {
            resolve(false)
          }
        });
      })
    }
    /**
     * 随机抽取
     * @returns {Promise<unknown>}
     */
    const sjcqRy = () => {
      return new Promise(resolve => {
        if (state.formData.cqtjData.length === 0) {
          ElMessage({
            message: `请先输入随机抽取条件`,
            type: 'error',
          })
          resolve(false)
          return
        }
        if (state.formData.cqtjData.some((val) => !val.KEY || !val.VALUE || (val.VALUE instanceof Array && val.VALUE.length === 0))) {
          ElMessage({
            type: "error",
            message: "条件、逻辑、条件值不能为空！！",
          });
          resolve(false)
          return;
        }
        instance.proxy.$refs['vForm2'].validate(valid2 => {
          instance.proxy.$refs['vForm3'].validate(valid3 => {
            if (valid2) {
              if (valid3) {
                let YCXRS = state.formData.zjcqData.filter(item => {
                  return item.SFQRCX === '1'
                }).length
                let params = JSON.parse(JSON.stringify({
                  CQGZBS: state.CQGZBS,
                  PBHYBS: props.params.id,
                  PWZRS: state.formData.PWZRS,
                  CQRS: state.formData.CQRS,
                  tableData: state.formData.cqtjData,

                  PWCQJLBS: state.PWCQJLBS,
                  CQPC: parseInt(state.CQPC) + 1,
                  CQSJ: comFun.getNowTime()

                }))
                params.tableData.forEach(item => {
                  if (item.VALUE instanceof Array) {
                    item.VALUE = item.VALUE.join(',')
                  }
                })
                //页面保存
                validateForm('save').then(() => {
                  //抽取规则保存&&评委抽取记录保存
                  axiosUtil.post('/backend/zjgl/pwcqgl/saveCqgzCqjlData', params).then((res) => {
                    if (res.message === 'success') {
                      let CQGZ = {
                        TSGZ: [],
                        PTGZ: []
                      }
                      state.formData.cqtjData.forEach(item => {
                        if (item.KEY === 'JSZYFW') {
                          CQGZ.TSGZ.push(item)
                        } else {
                          CQGZ.PTGZ.push(item)
                        }
                      })
                      let CQParams = {
                        // BCCQRS: state.formData.CQRS-YCXRS,//本次抽取人数
                        PBHYBS: props.params.id,
                        PWCQJLBS: state.PWCQJLBS,
                        ZJCQFS: 'SJCQ',
                        CJR: state.userInfo.userLoginName,
                        CJSJ: comFun.getNowTime(),
                        CQGZ
                      }
                      sjcqzj(CQParams)
                      resolve(true)
                    } else {
                      resolve(false)
                    }
                  });
                })
              } else {
                ElMessage({
                  message: `请先完善已经抽取结果`,
                  type: 'error',
                })
                resolve(false)
              }
            }
          })
        })
      })
    }
    //抽取专家
    const sjcqzj = (params) => {
      console.log('QC____', params)
      axiosUtil.post('/backend/zjgl/pwcqgl/savaPwzjJS', params).then(res => {
        if (res.message === 'success') {
          ElMessage({
            message: res.data.reString,
            type: 'success',
          })
        }
        queryZjcqData()
      })
    }
    const closeForm = () => {
      context.emit("closePbForm")
    }
    const closeXmForm = () => {
      state.xmdialogVisible = false
      //查询
    }
    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      var res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }
    const validateForm = (type) => {
      return new Promise(resolve => {
        if (type === 'submit') {
          if (state.formData.dpsxmData.length === 0) {
            ElMessage({
              message: `请选择待评审项目`,
              type: 'error',
            })
            resolve(false)
            return
          }
          if (state.formData.zjcqData.length === 0) {
            ElMessage({
              message: `请抽取专家`,
              type: 'error',
            })
            resolve(false)
            return
          }
          instance.proxy.$refs['vForm1'].validate(valid1 => {
            if (valid1) {
              instance.proxy.$refs['vForm2'].validate(valid2 => {
                if (valid2) {
                  instance.proxy.$refs['vForm3'].validate(valid3 => {
                    if (valid3) {
                      state.formData.SHZT = '1'
                      //TODO: 提交表单
                      resolve(submitForm(type))
                    } else {
                      ElMessage({
                        message: `专家抽取有未填项`,
                        type: 'error',
                      })
                      resolve(false)
                    }
                  })
                } else {
                  ElMessage({
                    message: `抽取规则有未填项`,
                    type: 'error',
                  })
                  resolve(false)
                }
              })
            } else {
              resolve(false)
            }
          })
        } else {
          state.formData.SHZT = '0'
          resolve(submitForm(type))
        }
      })
    }
    const openDialog = () => {
      //state.xmParams.tableData = state.formData.dpsxmData
      state.xmdialogVisible = true
    }
    // 抽取条件表格 删除
    const delTjData = (row, i) => {
      state.formData.cqtjData.splice(i, 1)
      //真正删除
      // RealDeleteTj(row)
    }
    //待评审项目 删除
    const delXmData = (row, i) => {
      state.formData.dpsxmData.splice(i, 1)
      //真正删除
      // RealDeleteXm(row)
    }
    const RealDeleteTj = (row) => {
      axiosUtil.post('/backend/zjgl/pwcqgl/deleteCqtj', row).then((res) => {
        if (res.message === 'success') {
          ElMessage({
            message: `删除成功`,
            type: 'success',
          })
          queryCqtjData();
        }

      });
    }
    const RealDeleteXm = (row) => {
      axiosUtil.post('/backend/zjgl/pwcqgl/deleteDpsxm', row).then((res) => {
        if (res.message === 'success') {
          ElMessage({
            message: `删除成功`,
            type: 'success',
          })
          queryDpsxmData();
        }

      });
    }
    // 抽取条件表格 新增
    const addTjData = (row, i) => {
      state.formData.cqtjData.push({
        CQGZMXBS: comFun.newId(),
        CQGZBS: state.CQGZBS,
        KEY: '',
        LOIGC: '',
        VALUE: ''
      })
    }
    const parentMethod = (val) => {
      let data = val
      data.forEach(item => {
        item.ID = comFun.newId();
        item.PBHYBS = props.params.id;
        item.PSDXBS = item.CGXMBS
      })
      state.formData.dpsxmData = data
      state.xmdialogVisible = false
    }
    const queryPwcqForm = () => {
      return new Promise(resolve => {
        let params = {
          PBHYBS: state.id
        }
        axiosUtil.get('/backend/zjgl/pwcqgl/queryPwcqForm', params).then((res) => {
          if (res.data.length > 0) {
            state.formData = {...res.data[0]}
            state.CQGZBS = state.formData.CQGZBS
            if (state.CQGZBS == null) {
              state.CQGZBS = comFun.newId()
            }
            state.PWCQJLBS = state.formData.PWCQJLBS
            if (state.PWCQJLBS == null) {
              state.PWCQJLBS = comFun.newId()
            }
            state.CQPC = state.formData.CQPC
            if (state.CQPC == null) {
              state.CQPC = 0
            }
          }
          queryDpsxmData()
          queryCqtjData()
          queryZjcqData()
          resolve(true)
        });
      })
    }
    const queryDpsxmData = () => {
      let params = {
        PBHYBS: state.id
      }
      axiosUtil.get('/backend/zjgl/pwcqgl/queryDpsxmData', params).then((res) => {
        state.formData.dpsxmData = res.data
      });
    }
    const queryCqtjData = () => {
      let params = {
        PBHYBS: state.id
      }
      axiosUtil.get('/backend/zjgl/pwcqgl/queryCqtjData', params).then((res) => {
        state.formData.cqtjData = res.data
        if (state.formData.cqtjData) {
          state.formData.cqtjData.forEach(item => {
            if (item.KEY === 'JSZYFW') {
              item.VALUE = item.VALUE.split(',')
            }
          })
        }
      });
    }
    const getZYData = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy', {}).then((res) => {
        state.XCSZYOptions = comFun.treeData(res.data, 'ZYBM', 'FZYBM', 'children', '0')
      });
    }
    const queryZjcqData = () => {
      let params = {
        PBHYBS: state.id
      }
      axiosUtil.get('/backend/zjgl/pwcqgl/queryZjcqData', params).then((res) => {
        if (res.data) {
          let zbrdbList = []
          let jdrList = []
          let zbgzryList = []
          let zjcqData = []
          res.data.forEach(item => {
            if (item.SFZBRDB === '1') {
              zbrdbList.push(item)
            } else if (item.SFJDRY === '1') {
              jdrList.push(item)
            } else if (item.SFZBGZRY === '1') {
              zbgzryList.push(item)
            } else {
              zjcqData.push(item)
            }
          })
          state.zbrdbList = zbrdbList
          state.jdrList = jdrList
          state.zbgzryList = zbgzryList
          state.formData.zjcqData = zjcqData
        }
      });
    }
    const openCheckRy = (listName) => {
      state.listName = listName
      state.ryDialogVisible = true
    }
    const getRyxx = (value) => {
      let zd = {}
      if (state.listName === 'zbrdbList') {
        zd = {SFZBRDB: '1'}
      } else if (state.listName === 'jdrList') {
        zd = {SFJDRY: '1'}
      } else if (state.listName === 'zbgzryList') {
        zd = {SFZBGZRY: '1'}
      }
      let res = []
      if (value) {
        value.forEach(item => {
          res.push({
            XM: item.USER_NAME,
            PBPWBS: comFun.newId(),
            PBHYBS: props.params.id,
            RYBS: item.USER_ID,
            ZJCQFS: 'ZD',
            SJH: item.USER_MOBILE,
            SZDWMC: item.ORGNA_NAME,
            CJR: state.userInfo.userLoginName,
            CJSJ: comFun.getNowTime(),
            ...zd
          })
        })
      }
      state[state.listName] = res
      state.ryDialogVisible = false
    }
    const getZjxx = (value) => {
      let res = []
      if (value) {
        value.forEach(item => {
          res.push({
            XM: item.XM,
            PBPWBS: comFun.newId(),
            PBHYBS: props.params.id,
            RYBS: item.ZJBS,
            ZJCQFS: 'ZD',
            SJH: item.RKZJBXX.BGDH,
            SZDWMC: item.GZDW,
            ZJLB: item.ZJLB,
            ZJLX: item.ZJLX,
            CSZYMC: item.XCSZYMC,
            CJR: state.userInfo.userLoginName,
            CJSJ: comFun.getNowTime()
          })
        })
      }
      state.formData.zjcqData.push(...res)
      state.zjDialogVisible = false
    }
    const openZJXZDialog = () => {
      let res=[]
      state.formData.zjcqData.forEach(item=>{
        res.push(item.RYBS)
      })
      state.fitterList=res
      state.zjDialogVisible=true
    }
    const changeCQTJ = (row) => {
      state.tjOptions.forEach(item => {
        if (item.DMXX === row.KEY) {
          row.LOIGC = item.BYZD1
        }
      })
      row.VALUE = null
    }
    const disabledTj = (value) => {
      let yxtj = []
      state.formData.cqtjData.forEach(item => {
        yxtj.push(item.KEY)
      })
      return yxtj.includes(value)
    }
    onMounted(() => {
      let params = props.params
      if (params && params.operation !== 'add') {
        queryPwcqForm()
      } else {
        state.CQGZBS = comFun.newId()
        state.PWCQJLBS = comFun.newId()
        state.CQPC = 0
      }
      getZYData()
      getDMBData("CQTJ", "tjOptions")
      getDMBData("CQLJ", "ljOptions")

    })
    return {
      ...toRefs(state),
      cxrs,
      jjzjrs,
      jszjrs,
      zrs,
      qtrs,
      submitForm,
      validateForm,
      closeForm,
      Plus,
      More,
      delTjData,
      addTjData,
      closeXmForm,
      openDialog,
      sjcqRy,
      getDMBData,
      parentMethod,
      delXmData,
      queryDpsxmData,
      queryCqtjData,
      RealDeleteXm,
      RealDeleteTj,
      openCheckRy,
      getRyxx,
      getZjxx,
      changeCQTJ,
      disabledTj,
      openZJXZDialog
    }
  }
})
</script>

<style scoped>
.zdTitle {
  background-color: #E4E6F6;
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  font-weight: 600;
  margin-bottom: 20px;
}

:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: unset;
}

/deep/ .el-textarea.is-disabled .el-textarea__inner {
  background-color: unset;
}
</style>
