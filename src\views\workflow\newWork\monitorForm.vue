<template>
  <div v-loading="loading" style="padding: 10px">
    <el-row :gutter="10" style="padding: 20px">
      <el-col :span="8">
        <span>业务类型：{{ fromData.NAME }}</span>
      </el-col>
      <el-col :span="8">
        <span> 发起人：{{ fromData.OWNERNAME }} </span>
      </el-col>
      <el-col :span="8">
        <span> 发起时间：{{ fromData.CREATEDTIME }} </span>
      </el-col>
      <el-col :span="8">
        <span>流程状态：</span>
        <span v-if="fromData.STATUS === 1">运行中</span>
        <span v-else-if="fromData.STATUS === 9">结束</span>
        <span v-else-if="fromData.STATUS === 2">终止</span>
      </el-col>

      <el-col :span="8">
        <span> 结束时间：{{ fromData.FINISHEDTIME }} </span>
      </el-col>

    </el-row>
    <el-table ref="datatable91634" :data="fromData.tableData" height="calc(100vh - 330px)"
              :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
              :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
      <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
      <el-table-column prop="PROCESSINSTANCENAME" label="任务名称" header-align="center" align="left" min-width="200">
      </el-table-column>
      <el-table-column label="业务环节" prop="TASKNAME" header-align="center" align="center" width="180">
        <template #default="scope">
          {{scope.row.TASKNAME || '已结束'}}
        </template>
      </el-table-column>
      <el-table-column label="处理状态" prop="STATUS" header-align="center" align="center" width="100">
        <template #default="scope">
          <el-tag type="primary" v-if="scope.row.STATUS===1">处理中</el-tag>
          <el-tag type="success" v-if="scope.row.STATUS===9">已完成</el-tag>
          <el-tag type="warning" v-if="scope.row.STATUS===7">已撤回</el-tag>
          <el-tag type="danger" v-if="scope.row.STATUS===2">已终止</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发送人" prop="SENDUSERNAME" header-align="center" width="100" align="center"></el-table-column>
      <el-table-column label="办理人" prop="CANDIDATESNAME" header-align="center" width="100" align="center"></el-table-column>
      <el-table-column label="接收时间" prop="CREATEDTIME" header-align="center" align="center" width="180"></el-table-column>
      <el-table-column label="处理时间" prop="FINISHEDTIME" header-align="center" align="center" width="180"></el-table-column>
      <el-table-column label="审核结果" prop="SUGGESTFLG" header-align="center" align="center" width="100">
        <template #default="scope">
          <div v-if="scope.row.SUGGESTFLG==='1'">通过</div>
          <div v-if="scope.row.SUGGESTFLG==='0'">驳回</div>
        </template>
      </el-table-column>
      <el-table-column label="审核意见" prop="RESULT" header-align="center" align="center" min-width="180"></el-table-column>
    </el-table>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import newWork from "@views/workflow/newWork/index";


export default defineComponent({
  name: '',
  components: {},
  props: {
    monitorParams: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: false,
      fromData:{
        tableData: [],
      }
    })

    const getFormData = () => {
      state.loading=true
      let params={
        PROCESSINSTANCEID: props.monitorParams.PROCESSINSTANCEID
      }
      axiosUtil.get('/backend/workFlow/wf5/monitorInfo',params).then(res=>{
        state.fromData=res.data
        state.loading=false
      })
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>

</style>
