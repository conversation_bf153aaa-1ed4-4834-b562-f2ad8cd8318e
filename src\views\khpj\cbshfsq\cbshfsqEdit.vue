<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="200px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">

        <el-col :span="24" class="grid-cell">
          <el-form-item label="企业（队伍/人员）名称：" prop="CLDXMC">
            <div style="margin-left: 10px">{{ formData.CLXX.CLDXMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="处罚说明：" prop="CFSM">
            <el-input v-model="formData.CLXX.CFSM" :rows="3"
                      type="textarea" clearable
                      :disabled="true"/>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="grid-cell">
          <el-form-item label="专业名称：" prop="ZYMC">
            <div style="margin-left: 10px">{{ formData.CLXX.ZYMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="处理方式：" prop="CLFS">
            <div style="margin-left: 10px">{{ formData.CLXX.CLFSMC }}</div>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="grid-cell">
          <el-form-item label="处理期限开始：" prop="CLKSRQ">
            <el-date-picker
                v-model="formData.CLXX.CLKSRQ"
                :disabled="true"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="处理期限结束：" prop="CLJSRQ">
            <el-date-picker
                v-model="formData.CLXX.CLJSRQ"
                :disabled="true"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>




        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="处罚相关资料：" prop="CFZL">
            <vsfileupload style="margin-left: 10px" :busId="params.id"
                          :key="params.id"
                          :editable="false" ywlb="cfxgzl"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider />

      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="恢复申请说明：" prop="HFSQSM">
            <el-input v-model="formData.HFSQSM" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="相关资料：" prop="FJZL">
            <vsfileupload style="margin-left: 10px" v-model:files="FJZLFiles" :busId="HFSQID"
                          :key="HFSQID"
                          :editable="editable" ywlb="hfzl"/>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="提报人：" prop="TBR">
            <div style="margin-left: 10px">{{formData.CJRXM}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="提报时间：" prop="TBSJ">
            <div style="margin-left: 10px">{{formData.CJSJ}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="联系人：" prop="LXR">
            <el-input v-model="formData.LXR" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="联系电话：" prop="LXDH">
            <el-input v-model="formData.LXDH" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsfileupload from "@views/components/vsfileupload";
import vsAuth from "@lib/vsAuth";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      CBSCLID: props.params.id,
      formData: {
        CLXX: {}
      },
      rules: {
        HFSQSM: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXR: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXDH: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      HFSQID: null,
      FJZLFiles: []

    })


    const getFormData = () => {
      let params={
        CBSCLID: state.CBSCLID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/cbshfsq/selecthfsqById', params).then((res) => {
        state.formData=res.data
        if(!state.formData.SHZT){
          state.formData={
            ...state.formData,
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '0'
          }
        }
        state.HFSQID=state.formData.HFSQID || comFun.newId()

        state.loading=false
      })
    }


    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }
    }

    const submitForm = (type) => {
      let params= {
        ...state.formData,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        HFSQID: state.HFSQID,
        CBSCLID: state.CBSCLID
      }
      if(type==='submit'){
        params.SHZT='1'
      }
      state.loading=true
      axiosUtil.post('/backend/sckhpj/cbshfsq/saveCbshfsqForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })

    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm,
    }
  }

})
</script>

<style scoped>

</style>
