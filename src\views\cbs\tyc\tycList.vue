<template>
  <div style="height: calc(100% - 40px);">
    <el-form ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="150px"
             size="default" @submit.prevent style="height: 100%">
      <el-row ref="grid70953">
        <!--      <el-col :span="12" class="grid-cell">-->
        <!--        <el-form-item label="准入专业：" prop="ZRZY">-->
        <!--          <el-cascader-->
        <!--              clearable-->
        <!--              v-model="listQuery.ZRZY"-->
        <!--              :options="XCSZYOptions"-->
        <!--              :show-all-levels="false"-->
        <!--              collapse-tags-->
        <!--              :props="{expandTrigger: 'hover',label:'ZYMC',value:'ZYBM',emitPath:false,multiple:true}"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <!--      </el-col>-->

        <!--      <el-col :span="12" class="grid-cell">-->
        <!--        <el-form-item label="单位名称：" prop="DWMC">-->
        <!--          <el-input ref="input45296" v-model="listQuery.DWMC" type="text" clearable>-->
        <!--          </el-input>-->
        <!--        </el-form-item>-->
        <!--      </el-col>-->
        <!--    </el-row>-->

        <el-col :span="24">
          <div class="static-content-item" style="display: flex;justify-content: right;margin-bottom: 10px">
            <el-button ref="button91277" @click="rePullData" type="success">重新获取风险信息</el-button>
            <el-button ref="button9527" type="primary" @click="openWindow">天眼查官网</el-button>
          </div>
        </el-col>

      </el-row>

      <div class="container-wrapper" style="height: calc(100% - 50px)">
        <el-table ref="datatable91634" :data="tableData" height="calc(100% - 80px)" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
          <el-table-column v-if="true" prop="FXLXMC" label="风险分类" :fixed="false" align="center"
                           :show-overflow-tooltip="true" width="160"></el-table-column>
          <el-table-column v-if="true" prop="FXSM" label="风险名称" :fixed="false" align="center"
                           :show-overflow-tooltip="true" min-width="120"></el-table-column>
          <el-table-column v-if="true" prop="FXDJ" label="风险等级" :fixed="false" align="center"
                           :show-overflow-tooltip="true" width="160"></el-table-column>
          <el-table-column v-if="true" prop="YJSJ" label="预警时间" :fixed="false" align="center"
                           :show-overflow-tooltip="true" width="150"></el-table-column>
        </el-table>
        <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                       :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                       class="lui-pagination"
                       @size-change="getDataList" @current-change="getDataList" :total="total">
        </el-pagination>
      </div>
    </el-form>
  </div>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
}
  from 'vue'

// 请求方法 get,post,put,del,downloadFile
import vsAuth from "@src/lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "../../../lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {},
  props: {
    TYXYDM: {
      type: String,
    },
    fxxxsl:{
      type: Number,
    },
    params:{}
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        page: 1,
        size: 10,
      },
      total: 0,
      tableData: [],
      rules: {},
    })

    const rePullData = (XMID) => {
      ElMessageBox.confirm(
          '确定重新获取风险信息?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        let params={
          TYXYDM: props.TYXYDM,

        }
        axiosUtil.get('/backend/sccbsgl/zcgl/rePullTyc', params).then((res) => {
          getDataList()
          ElMessage({
            message: '获取成功',
            customClass: "myMessageClass",
            type: 'success',
          })
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消',
        })
      })
    }


    const getDataList = () => {
      let params={
        TYXYDM: props.TYXYDM
      }
      axiosUtil.get('/backend/sccbsgl/zcgl/selectTycList', params).then((res) => {
        state.tableData = res.data.list
        state.total=res.data.total
        emit('update:fxxxsl',state.total)

      });
    }


    const closeForm = () => {
      emit('closeDialog')
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const openWindow = () => {
      window.open('https://www.tianyancha.com/')
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      closeForm,
      indexMethod,
      rePullData,
      openWindow
    }
  }
})

</script>

<style scoped>

</style>
