<template>
  <el-form :model="formData" ref="vForm" :rules="vFormRules" :label-width="150" label-position="left"
           size="default" class="lui-card-form">
    <el-row :gutter="0" class="grid-row" style="margin-bottom:16px;">
      <el-col :span="24" class="grid-cell" v-if="formData.XXXMC">
        <el-form-item label="设备类型">
          <el-input v-model="formData.XXXMC" placeholder="请输入" disabled></el-input>
          <!-- <el-select
              v-model="formData.SBLX"
              v-tooltip="{
                newValue: formData.SBLX,
                oldValue: resultTableData?.SBLX,
                label: personTypeOptions.find((i) => i.DMXX == resultTableData?.SBLX)?.DMMC,
              }"
              value-key=""
              placeholder="请选择设备类型"
              clearable
              filterable
          >
            <el-option
                v-for="item in personTypeOptions"
                :key="item.DMXX"
                :label="item.DMMC"
                :value="item.DMXX"
            >
            </el-option>
          </el-select> -->
        </el-form-item>
      </el-col>

      <el-col :span="8" class="grid-cell">
        <el-form-item label="设备名称" prop="SBMC">
          <el-input v-model="formData.SBMC" placeholder="请输入" :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="规格型号" prop="GGXH">
          <el-input v-model="formData.GGXH" placeholder="请输入" :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="生产厂家" prop="SCCJ">
          <el-input v-model="formData.SCCJ" placeholder="请输入" :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="8" class="grid-cell">
        <el-form-item label="品牌" prop="EXTENSION.PP">
          <el-input v-model="formData.EXTENSION.PP" :disabled="!editable" placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="数量" prop="SL">
          <el-input
              type="number"
              :disabled="!editable"
              v-model.number="formData.SL"
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="出厂日期" prop="TCRQ">
          <el-date-picker
              v-model="formData.TCRQ"
              type="date"
              :disabled="!editable"
              value-format="YYYY-MM-DD"
              placeholder="出厂日期"
              style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>

<!--      <el-col :span="8" class="grid-cell">-->
<!--        <el-form-item label="设备能力" prop="SBNL">-->
<!--          <el-input v-model="formData.SBNL" placeholder="请输入"></el-input>-->
<!--        </el-form-item>-->
<!--      </el-col>-->
<!--      <el-col :span="8" class="grid-cell">-->
<!--        <el-form-item label="完好情况" prop="WHQK">-->
<!--          <el-input v-model="formData.WHQK" placeholder="请输入"></el-input>-->
<!--        </el-form-item>-->
<!--      </el-col>-->
      <el-col :span="8" class="grid-cell">
        <el-form-item label="检验检测日期" prop="SCDXRQ">
          <el-date-picker
              v-model="formData.SCDXRQ"
              type="date"
              :disabled="!editable"
              value-format="YYYY-MM-DD"
              placeholder="检验检测日期"
              style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>

      <el-col :span="8" class="grid-cell">
        <el-form-item label="有效期截止日期" prop="EXTENSION.YXQJZRQ">
          <el-date-picker
              v-model="formData.EXTENSION.YXQJZRQ"
              type="date"
              style="width: 100%"
              :disabled="!editable"
              value-format="YYYY-MM-DD"
              placeholder="有效期截止日期"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="检测结论" prop="EXTENSION.JCJL">
          <el-input v-model="formData.EXTENSION.JCJL" placeholder="请输入" :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="检测单位" prop="EXTENSION.JCDW">
          <el-input v-model="formData.EXTENSION.JCDW" placeholder="请输入" :disabled="!editable"></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="grid-cell">
        <el-form-item label="设备能力" prop="SBNL">
          <el-input v-model="formData.SBNL" type="textarea" placeholder="请输入" maxlength="200" :disabled="!editable" show-word-limit></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="grid-cell">
        <el-form-item label="备注" prop="BZSM">
          <el-input
              :disabled="!editable"
              type="textarea"
              v-model="formData.BZSM"
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="grid-cell">
        <el-form-item label="相关附件" prop="BZSM">
          <vsfileupload
              :maxSize="10"
              :editable="editable"
              :busId="formData.DWSBID"
              :key="formData.DWSBID"
              ywlb="xgfj"
              busType="dwxx"
              :limit="100"
          ></vsfileupload>
        </el-form-item>
      </el-col>
    </el-row>
    <div v-if="editable">
      <el-button type="primary" size="default" @click="continueAdd">继续添加</el-button>
      <el-button type="primary" size="default" @click="confirm()">确定</el-button>
      <el-button type="primary" size="default" @click="cancel">取消</el-button>
    </div>
  </el-form>
</template>
<script setup>
import { defineEmits, defineProps, onMounted, ref, watch } from "vue";
import { getCommonSelectDMB } from "@src/api/cbsxx.js";
import vsfileupload from "../../../../components/vsfileupload";

const getSbLxs = () => {
  getCommonSelectDMB({ DMLBID: "SBLX" }).then(({data}) => {
    if(!data) return;
    personTypeOptions.value = data;
  });
};

onMounted(() => {
  getSbLxs();
});
const defaultData = ref({
  SBLX: null,
  SBMC: null,
  GGXH: null,
  SCCJ: null,
  PP: null,
  SL: null,
  TCRQ: null,
  CCBH: null,
  GDSYNX: null,
  SCDXSJ: null,
  SBNL: null,
  WHQK: null,
  SCDXRQ: null,
  YXQJZRQ: null,
  JCJL: null,
  JCDW: null,
  BZSM: null,
  EXTENSION: {},
});
const formData = ref({
  SBLX: null,
  SBMC: null,
  GGXH: null,
  SCCJ: null,
  PP: null,
  SL: null,
  TCRQ: null,
  CCBH: null,
  GDSYNX: null,
  SCDXSJ: null,
  SBNL: null,
  WHQK: null,
  SCDXRQ: null,
  YXQJZRQ: null,
  JCJL: null,
  JCDW: null,
  BZSM: null,
  EXTENSION: {},
});
const personTypeOptions = ref([]);
const certificateTypeOptions = ref([
  {
    label: "HSE",
    value: "HSE",
  },
]);
const props = defineProps({
  editData: {
    type: Object,
    default: () => {},
  },
  // 结果表数据，比对用
  resultTableData: {
    type: Object,
    default: () => null,
  },
  editable:{
    type: Boolean,
  }
});
watch(
  () => props.editData,
  (val) => {
    // console.log('1111111111111111');
    formData.value = Object.assign(formData.value, { ...defaultData.value }, { ...val });
  },
  {
    immediate: true,
    // deep: true
  }
);
const updateFile = (row, index) => {};
const deleteRow = (row, index) => {};

const emits = defineEmits(["updateData", "close"]);
// 继续添加
const continueAdd = () => {
  confirm(true);
};
//确认
const confirm = (isAdd = false) => {
  validateForm()
    .then((result) => {
      emits(
        "updateData",
        {
          ...formData.value,
        },
        isAdd
      );
    })
    .catch((err) => {
      console.log(err);
    });
};
// 取消
const cancel = () => {
  emits("close");
};

const vForm = ref(null);
const vFormRules = ref({
  SBLX: [{ required: true, message: "请选择设备类型", trigger: "change" }],
  SBMC: [
    { required: true, message: "请输入设备名称", trigger: "blur" },
    { max: 128, message: "最多输入128个字符", trigger: "blur" },
  ],
  GGXH: [
    { required: true, message: "请输入规格型号", trigger: "blur" },
    { max: 128, message: "最多输入128个字符", trigger: "blur" },
  ],
  SCCJ: [
    { required: false, message: "请输入生产厂家", trigger: "blur" },
    { max: 128, message: "最多输入128个字符", trigger: "blur" },
  ],

  SL: [
    { required: true, message: "请输入数量", trigger: "blur" },
    {
      pattern: /^[0-9]{1,8}(.[0-9]{1,4})?$/,
      message: "请输入正常范围",
      trigger: "change",
    },
    { type: "number", message: "请输入数字" },
  ],
  TCRQ: [{ required: false, message: "请选择出厂日期", trigger: "blur" }],
  SBNL: [
    { required: false, message: "请输入设备能力", trigger: "blur" },
    { max: 2000, message: "最多输入2000个字符", trigger: "blur" },
  ],
  WHQK: [
    { required: false, message: "请输入完好情况", trigger: "blur" },
    { max: 600, message: "最多输入600个字符", trigger: "blur" },
  ],
  SCDXRQ: [{ required: false, message: "请选择检验检测日期", trigger: "blur" }],
  EXTENSION: {
    PP: [
      { required: false, message: "请输入数量", trigger: "blur" },
      { max: 10, message: "最多输入128个字符", trigger: "change" },
    ],
    YXQJZRQ: [{ required: false, message: "请选择有效期截止日期", trigger: "blur" }],
    JCJL: [
      { required: false, message: "请输入检测结论", trigger: "blur" },
      { max: 256, message: "最多输入256个字符", trigger: "blur" },
    ],
    JCDW: [
      { required: false, message: "请输入检测单位", trigger: "blur" },
      { max: 256, message: "最多输入256个字符", trigger: "blur" },
    ],
  },
  BZSM: [
    { required: false, message: "请输入备注", trigger: "blur" },
    { max: 1024, message: "最多输入1024个字符", trigger: "blur" },
  ],
});
const validateForm = () => {
  return vForm.value.validate();
};
defineExpose({
  validateForm,
});
</script>
