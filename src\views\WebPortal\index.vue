<!--
 * @Description: 江汉市场管理门户
 * @Author: WLF (<EMAIL>)
 * @Date: 2024-11-14 08:49:04
 * @LastEditTime: 2024-12-12 12:34:23
 * @FilePath: \jhscgl-xieyun-front\src\views\WebPortal\index.vue
 * @copyright : Copyright (c) 2024 wlf
-->
<template>
    <div style="background-color: #faede7;">
        <webPortalHeader v-model="activeIndex" ref="header" @resetItemInd="resetItemInd"></webPortalHeader>
        <div class="body">
            <webPortalHomePage v-if="activeIndex == '1'" @imgClick="imgClick"></webPortalHomePage>
            <webPortalOtherPage ref="otherPage" :pageParams="pageParams" v-else></webPortalOtherPage>
            <webPortalFooter></webPortalFooter>
        </div>
    </div>
</template>

<script>

import { defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, nextTick } from "vue";
import webPortalHeader from "@views/WebPortal/common/header.vue"
import webPortalFooter from "@views/WebPortal/common/footer.vue"
import webPortalHomePage from "@views/WebPortal/homePage.vue"
import webPortalOtherPage from "@views/WebPortal/otherPage.vue"
import axiosUtil from "@lib/axiosUtil";
import { ElMessage, ElMessageBox } from "element-plus";
export default defineComponent({
    name: '',
    components: {webPortalHeader,webPortalFooter,webPortalHomePage,webPortalOtherPage },
    props: {},
    setup(props, { emit }) {
        const state = reactive({
            activeIndex: '1',
            // 除首页外其他菜单信息
            pageList: [],
            itemInd: '',
            pageParams:{}
        })
        const instance = getCurrentInstance()
        watch(() => state.activeIndex, () => {
            if(!['1','5','6','7'].includes(state.activeIndex)){
                Object.assign(state.pageParams,state.pageList.find(val => val.activeIndex == state.activeIndex))
                nextTick(()=>{
                    instance.proxy.$refs['otherPage'].handleSelect(state.itemInd ? state.itemInd : '1')
                    instance.proxy.$refs['otherPage'].activeIndex = state.itemInd ? state.itemInd : '1'
                })
            }
            if(state.activeIndex == '5'){
                window.location.href='/zjgl/zjxxcxList'
            }
            if(state.activeIndex == '6'){
                window.location.href='/gqct/gqctglPage'
            }
            if(state.activeIndex == '7'){
                window.location.href='/bscc/xmbsccglList'
            }
            instance.proxy.$refs['header'].activeIndex = state.activeIndex
        }, {deep: true})

        const imgClick =(menuInd,itemInd) => {
            state.activeIndex = menuInd
            state.itemInd = itemInd
        }
        const resetItemInd = () => {
            state.itemInd = null
        }
        const loadPageList = () => {
            axiosUtil.get('/backend/webProtal/queryWebProtalTabData', {}).then((res) => {
               state.pageList = res.data
            })
        }
        onMounted(() => {
            loadPageList()
        })

        return {
            ...toRefs(state),
            imgClick,
            resetItemInd,
            loadPageList
        }
    }

})
</script>

<style scoped>
.body {
    width: 1300px;
    height: auto;
    margin: 0 auto;
    background-color: white;
    /* 实现水平居中 */
    position: relative;
    /* 设置定位，以便 z-index 生效 */
    z-index: 3;
}
</style>
<style>
::-webkit-scrollbar {
    width: 0px
}
</style>