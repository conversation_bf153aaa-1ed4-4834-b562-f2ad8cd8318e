<template>
  <div id="auditDiv">
    <el-main>
      <component ref="busiCom" :is="busiUrl" :params="queryParams" @handleClose="handleClose" />
    </el-main>
    <el-footer class="button-box" v-if="props.status == '1' && props.row.DESCRIPTION != 'new'">
      <el-button type="primary" @click="handleAudit(1, 1, 1)">同意</el-button>
      <el-button type="danger" @click="handleAudit(0, null, 0)">不同意并退回</el-button>
      <el-button type="danger" @click="handleAudit(0, 1, 1)">不同意并关闭</el-button>
    </el-footer>
  </div>
</template>

<script setup>
import { defineAsyncComponent, ref, onMounted, nextTick } from 'vue';
import vsflow from "@views/vsflow/index.js";

const props = defineProps({
  row: {
    type: Object,
    default: () => { }
  },
  status: {
    type: String,
    default: '1'
  },
});

const busiCom = ref(null);
const busiUrl = ref(null);
const queryParams = ref({});
const emit = defineEmits(['handleClose']); // 定义关闭事件

const handleAudit = async (suggestflg, tzbl, shbl) => {
  if (busiCom.value?.handleSubmit) {
    try {
      await busiCom.value.handleSubmit(suggestflg, tzbl, shbl); // 调用子组件方法
    } catch (error) {
      console.error("调用子组件方法失败:", error);
    }
  } else {
    console.error("子组件未加载或缺少 handleSubmit 方法");
  }
};

const loadPage = async () => {
  const processConfig = Object.values(vsflow.processData).find(
    (item) => item.processId === props.row.PROCESSID
  );
  queryParams.value = {
    processId: props.row.PROCESSID,
    activityId: props.row.ACTIVITYID,
    taskId: props.row.TASKID,
    processInstanceId: props.row.PROCESSINSTANCEID,
    businessId: props.row.PROCESSINSTANCEID,
    processInstanceName: props.row.PROCESSINSTANCENAME,
    status: props.status,
    activityType: props.row.ACTIVITYTYPE,
  };
  const url = props.row.DESCRIPTION == 'new' && props.status == '1' ? processConfig.processEditUrl : processConfig.processViewUrl;
  console.log("加载组件路径:", url);
  busiUrl.value = defineAsyncComponent(() => import(`@views/${url}.vue`));
  await nextTick();
}

const handleClose = () => {
  emit('handleClose');
}

onMounted(() => {
  loadPage();
})
</script>

<style scoped>
.button-box {
  display: flex;
  justify-content: center;
  width: 100%;
}
</style>