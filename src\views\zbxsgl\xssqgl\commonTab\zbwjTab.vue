<template>
  <div>
    <el-form :model="modelValue.XSWJ" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="190px"
             size="default" @submit.prevent>
      <el-collapse v-model="activeNames">
        <el-collapse-item title="联系方式" name="1">
          <template #title>
            <div style="display: flex;align-items: center;margin-left: 20px">
              <div style="margin-left: 10px">联系方式</div>
            </div>
          </template>
          <el-row :gutter="0" class="grid-row" style="flex: 1;">
            <el-col :span="8" class="grid-cell">
              <el-form-item label="招标人：" prop="ZBR" :class="{dataChange: isChangeT('ZBR')}">
                <template #label>
                  <div>
                    招标人：
                    <el-tooltip :content="isChangeT('ZBR')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZBR')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-input v-model="modelValue.XSWJ.ZBR" type="text" placeholder="请输入" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label="地址：" prop="ZBRDZ" :class="{dataChange: isChangeT('ZBRDZ')}">
                <template #label>
                  <div>
                    地址：
                    <el-tooltip :content="isChangeT('ZBRDZ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZBRDZ')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-input v-model="modelValue.XSWJ.ZBRDZ" type="text" placeholder="请输入" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label="邮编：" prop="ZBRYB" :class="{dataChange: isChangeT('ZBRYB')}">
                <template #label>
                  <div>
                    邮编：
                    <el-tooltip :content="isChangeT('ZBRYB')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZBRYB')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-input v-model="modelValue.XSWJ.ZBRYB" type="text" placeholder="请输入" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label="联系人：" prop="ZBRLXR" :class="{dataChange: isChangeT('ZBRLXR')}">
                <template #label>
                  <div>
                    联系人：
                    <el-tooltip :content="isChangeT('ZBRLXR')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZBRLXR')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-input v-model="modelValue.XSWJ.ZBRLXR" type="text" placeholder="请输入" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label="电话：" prop="ZBRDH" :class="{dataChange: isChangeT('ZBRDH')}">
                <template #label>
                  <div>
                    电话：
                    <el-tooltip :content="isChangeT('ZBRDH')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZBRDH')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-input v-model="modelValue.XSWJ.ZBRDH" type="text" placeholder="请输入" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="grid-cell">
              <el-form-item label="电子邮件：" prop="ZBRDZYJ" :class="{dataChange: isChangeT('ZBRDZYJ')}">
                <template #label>
                  <div>
                    电子邮件：
                    <el-tooltip :content="isChangeT('ZBRDZYJ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZBRDZYJ')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-input v-model="modelValue.XSWJ.ZBRDZYJ" type="text" placeholder="请输入" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

<!--          <div style="display: flex;gap: 10px">-->
<!--            <el-row :gutter="0" class="grid-row" style="flex: 1;">-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="招标人：" prop="ZBR">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBR" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="地址：" prop="ZBRDZ">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBRDZ" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="邮编：" prop="ZBRYB">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBRYB" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="联系人：" prop="ZBRLXR">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBRLXR" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="电话：" prop="ZBRDH">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBRDH" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="电子邮件：" prop="ZBRDZYJ">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBRDZYJ" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--            </el-row>-->

<!--            <div style="height: auto;width: 2px;background-color: #e3dfdf"></div>-->

<!--            <el-row :gutter="0" class="grid-row" style="flex: 1;">-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="招标服务机构：" prop="ZBJG">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBJG" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="地址：" prop="ZBJGDZ">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBJGDZ" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="邮编：" prop="ZBJGYB">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBJGYB" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="联系人：" prop="ZBJGLXR">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBJGLXR" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="电话：" prop="ZBJGDH">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBJGDH" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="电子邮件：" prop="ZBJGDZYJ">-->
<!--                  <el-input v-model="modelValue.XSWJ.ZBJGDZYJ" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--            </el-row>-->
<!--          </div>-->

        </el-collapse-item>
        <el-collapse-item title="招标文件" name="2">
          <template #title>
            <div style="display: flex;align-items: center;margin-left: 20px">
              <div style="margin-left: 10px">招标文件</div>
            </div>
          </template>
          <el-row :gutter="0" class="grid-row">
<!--            <el-col :span="16" class="grid-cell" v-if="modelValue.XSWJ.WJID===params.id">-->
<!--              <el-form-item label="招标项目名称：" prop="XMMC">-->
<!--                <div style="margin-left: 10px">{{ modelValue.XMXX.XMMC }}-->
<!--                  <el-button size="large" link @click="viewZbfa"-->
<!--                             type="primary">查看招标方案详情</el-button>-->
<!--                </div>-->
<!--              </el-form-item>-->
<!--            </el-col>-->

<!--            <el-col :span="8" class="grid-cell" v-if="modelValue.XSWJ.WJID===params.id">-->
<!--              <el-form-item label="招标项目编号：" prop="XMBH">-->
<!--                <div style="margin-left: 10px">{{ modelValue.XMXX.XMBH }}</div>-->
<!--              </el-form-item>-->
<!--            </el-col>-->

            <el-col :span="16" class="grid-cell">
              <el-form-item :label="`${modelValue.SQLX==='FZB' ? '采购文件': '招标文件'}获取时间：`" prop="ZBWJHQSJ" :class="{dataChange: isChangeT('ZBWJHQSJ')}">
                <template #label>
                  <div>
                    {{`${modelValue.SQLX==='FZB' ? '采购文件': '招标文件'}获取时间：`}}
                    <el-tooltip :content="isChangeT('ZBWJHQSJ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('ZBWJHQSJ')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <div style="display: flex;gap: 10px;align-items: center">
                  <el-date-picker
                      v-model="modelValue.XSWJ.ZBWJHQSJKS"
                      :disabled="!editable"
                      type="datetime"
                      clearable
                      style="width: 200px"
                      placeholder="请选择"
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm:ss"
                  ></el-date-picker>
                  <div>至</div>
                  <el-date-picker
                      v-model="modelValue.XSWJ.ZBWJHQSJJS"
                      :disabled="!editable"
                      type="datetime"
                      clearable
                      style="width: 200px"
                      placeholder="请选择"
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm:ss"
                  ></el-date-picker>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
              <el-form-item :label="`${modelValue.SQLX==='FZB' ? '响应文件': '招标文件'}递交截止时间：`" prop="TBWJDJJZSJ" :class="{dataChange: isChangeT('TBWJDJJZSJ')}">
                <template #label>
                  <div>
                    {{`${modelValue.SQLX==='FZB' ? '响应文件': '招标文件'}递交截止时间：`}}
                    <el-tooltip :content="isChangeT('TBWJDJJZSJ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('TBWJDJJZSJ')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-date-picker
                    v-model="modelValue.XSWJ.TBWJDJJZSJ"
                    :disabled="!editable"
                    type="datetime"
                    clearable
                    style="width: 100%"
                    placeholder="请选择"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="16" class="grid-cell">
              <el-form-item :label="`计划${modelValue.SQLX==='FZB' ? '评审（谈判）': '开标'}时间：`" prop="JHKBSJ" :class="{dataChange: isChangeT('JHKBSJ')}">
                <template #label>
                  <div>
                    {{`计划${modelValue.SQLX==='FZB' ? '评审（谈判）': '开标'}时间：`}}
                    <el-tooltip :content="isChangeT('JHKBSJ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('JHKBSJ')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-date-picker
                    v-model="modelValue.XSWJ.JHKBSJ"
                    :disabled="!editable"
                    type="datetime"
                    clearable
                    style="width: 100%"
                    placeholder="请选择"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="8" class="grid-cell">
              <el-form-item :label="`计划${modelValue.SQLX==='FZB' ? '评审（谈判）': '开标'}地点：`" prop="JHKBDD" :class="{dataChange: isChangeT('JHKBDD')}">
                <template #label>
                  <div>
                    {{`计划${modelValue.SQLX==='FZB' ? '评审（谈判）': '开标'}地点：`}}
                    <el-tooltip :content="isChangeT('JHKBDD')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('JHKBDD')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-input v-model="modelValue.XSWJ.JHKBDD" type="text" placeholder="请输入" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>

<!--            <el-col :span="8" class="grid-cell">-->
<!--              <el-form-item :label="`${modelValue.SQLX==='FZB' ? '评审': '评标'}模板：`" prop="PBMB" :class="{dataChange: isChangeT('PBMB')}">-->
<!--                <template #label>-->
<!--                  <div>-->
<!--                    {{`计划${modelValue.SQLX==='FZB' ? '评审': '评标'}模板：`}}-->
<!--                    <el-tooltip :content="isChangeT('PBMB')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('PBMB')">-->
<!--                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>-->
<!--                    </el-tooltip>-->
<!--                  </div>-->
<!--                </template>-->
<!--                <el-input v-model="modelValue.XSWJ.PBMB" type="text" placeholder="请输入" :disabled="!editable"></el-input>-->
<!--              </el-form-item>-->
<!--            </el-col>-->


<!--            <el-col :span="16" class="grid-cell" v-if="modelValue.SQLX==='FZB'">-->
<!--              <el-form-item label="报价时间：" prop="BJSJ" :class="{dataChange: isChangeT('BJSJ')}">-->
<!--                <template #label>-->
<!--                  <div>-->
<!--                    报价时间：-->
<!--                    <el-tooltip :content="isChangeT('BJSJ')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('BJSJ')">-->
<!--                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>-->
<!--                    </el-tooltip>-->
<!--                  </div>-->
<!--                </template>-->
<!--                <div style="display: flex;gap: 10px;align-items: center">-->
<!--                  <el-date-picker-->
<!--                      v-model="modelValue.XSWJ.BJSJKS"-->
<!--                      :disabled="!editable"-->
<!--                      type="datetime"-->
<!--                      clearable-->
<!--                      style="width: 200px"-->
<!--                      placeholder="请选择"-->
<!--                      format="YYYY-MM-DD HH:mm"-->
<!--                      value-format="YYYY-MM-DD HH:mm:ss"-->
<!--                  ></el-date-picker>-->
<!--                  <div>至</div>-->
<!--                  <el-date-picker-->
<!--                      v-model="modelValue.XSWJ.BJSJJS"-->
<!--                      :disabled="!editable"-->
<!--                      type="datetime"-->
<!--                      clearable-->
<!--                      style="width: 200px"-->
<!--                      placeholder="请选择"-->
<!--                      format="YYYY-MM-DD HH:mm"-->
<!--                      value-format="YYYY-MM-DD HH:mm:ss"-->
<!--                  ></el-date-picker>-->
<!--                </div>-->
<!--              </el-form-item>-->
<!--            </el-col>-->

<!--            <el-col :span="8" class="grid-cell" v-if="modelValue.SQLX==='FZB'">-->
<!--              <el-form-item label="报价轮次：" prop="BJLC" :class="{dataChange: isChangeT('BJLC')}">-->
<!--                <template #label>-->
<!--                  <div>-->
<!--                    报价轮次：-->
<!--                    <el-tooltip :content="isChangeT('BJLC')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('BJLC')">-->
<!--                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>-->
<!--                    </el-tooltip>-->
<!--                  </div>-->
<!--                </template>-->
<!--                <el-select v-model="modelValue.XSWJ.BJLC" class="full-width-input" clearable :disabled="!editable">-->
<!--                  <el-option v-for="(item, index) in BJLCOptions" :key="index" :label="item.DMMC"-->
<!--                             :value="item.DMXX"></el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->

            <el-col :span="24" class="grid-cell no-border-bottom" v-if="modelValue.XSWJ.LSWJID">
              <el-form-item label="变更说明：" prop="BGSM">
                <el-input v-model="modelValue.XSWJ.BGSM" :rows="3"
                          type="textarea" clearable
                          :disabled="!editable"/>
              </el-form-item>
            </el-col>

            <el-col :span="24" class="grid-cell no-border-bottom" v-else>
              <el-form-item label="备注：" prop="BZ">
                <el-input v-model="modelValue.XSWJ.BZ" :rows="3"
                          type="textarea" clearable
                          :disabled="!editable"/>
              </el-form-item>
            </el-col>

            <el-col :span="24" class="grid-cell no-border-bottom">
              <el-form-item :label="`${modelValue.XSWJ.LSWJID ? '变更后' : ''}招标文件：`" prop="ZBWJ">
                <vsfileupload
                    style="margin-left: 10px"
                    :editable="editable"
                    :busId="modelValue.XSWJ.WJID"
                    :key="modelValue.XSWJ.WJID"
                    ywlb="ZBWJ"
                    busType="ZBWJ"
                    :limit="100"
                    v-model:files="ZBWJFiles"
                ></vsfileupload>
              </el-form-item>
            </el-col>

            <el-col :span="24" class="grid-cell">
              <el-form-item label="是否网上评审：" prop="SFWSPS" :class="{dataChange: isChangeT('SFWSPS')}">
                <template #label>
                  <div>
                    是否网上评审
                    <el-tooltip :content="isChangeT('SFWSPS')?.BGMS" placement="bottom" effect="light" v-if="isChangeT('SFWSPS')">
                      <el-icon style="cursor: pointer;color: red"><InfoFilled/></el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <el-radio-group v-model="modelValue.XSWJ.SFWSPS" :disabled="!editable">
                  <el-radio label="1">是</el-radio>
                  <el-radio label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>



          </el-row>
        </el-collapse-item>
        <el-collapse-item title="评标规则设置" name="3" v-if="modelValue.XSWJ.SFWSPS==='1'">
          <template #title>
            <div style="display: flex;align-items: center;margin-left: 20px">
              <div style="margin-left: 10px">评标规则设置</div>
            </div>
          </template>
          <el-row :gutter="0" class="grid-row" style="flex: 1;">
            
            <el-table ref="datatable91634" :data="pbgzTableData" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" label="序号" align="center" width="80"/>
              <el-table-column prop="PSJD" label="评审阶段" header-align="center" align="left"/>
              <el-table-column prop="PSLX" label="审查/评审类型" header-align="center" align="left"/>
<!--              <el-table-column prop="checked" label="选择" header-align="center" align="center" width="60">-->
<!--                <template #default="scope">-->
<!--                  <el-checkbox v-model="scope.row.checked" :disabled="!editable"></el-checkbox>-->
<!--                </template>-->
<!--              </el-table-column>-->
              <el-table-column label="权重（%）" prop="qz" header-align="center" align="center" width="200">
                <template #default="{row}">
                  <div v-if="row.PSJD!=='初步评审'">
                    <el-input v-model="row.QZ" v-show="editable&&row.checked"></el-input>
                    <span v-text="row.QZ" v-show="!editable"></span>
                  </div>
                  <div v-else>{{ row.QZ }}</div>
                </template>
              </el-table-column>
              <el-table-column label="规则配置" align="center" width="100" v-if="editable">
                <template #default="{row}">
                  <el-button @click="editGzpz(row)" size="small" class="lui-table-button"
                            v-if="row.checked">
                    <span v-if="row.saveFlag" style="color: #00c000">配置</span>
                    <span v-if="!row.saveFlag">配置</span>
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="规则配置" align="center" width="200" v-if="!editable">
                <template #default="{row}">
                  <el-button @click="editGzpz(row)" class="lui-table-button" size="mini">查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

          </el-row>
        </el-collapse-item>

      </el-collapse>
    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="招标方案查看"
        @closed="dialogVisible=false"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <zbfaEdit v-if="dialogVisible" :params="faParams" @close="dialogVisible=false"/>
      </div>
    </el-dialog>

    <!-- 规则配置 弹窗 -->
    <el-dialog :title="dialogTitle" v-if="dialogVisible_pbgz" v-model="dialogVisible_pbgz" :append-to-body="true"
               custom-class="lui-dialog" width="1000px" top="10vh" :close-on-click-modal="false" z-index="1000">
      <XspsMb :key="xspsData" v-if="pbgzlx==='XSPS'" @handleClose2="handleClose2" @handleClose="handleClose_pbgz"
              :saveDataList="xspsData" :editable="editable" :PSLXDM="pbgzlx"/>
      <ZgpsMb :key="zgpsData" v-else-if="pbgzlx==='ZGPS'" @handleClose2="handleClose2" @handleClose="handleClose_pbgz"
              :saveDataList="zgpsData" :editable="editable" :PSLXDM="pbgzlx"/>
      <XyxpsMb :key="xyxpsData" v-else-if="pbgzlx==='XYXPS'" @handleClose2="handleClose2"
               @handleClose="handleClose_pbgz"
               :saveDataList="xyxpsData" :editable="editable" :PSLXDM="pbgzlx"/>
      <BjpsMb :key="bjpsData" v-else-if="pbgzlx==='BJPS'" @handleClose2="handleClose2" @handleClose="handleClose_pbgz"
              :bjpsData="bjpsData" :editable="editable"/>
      <ZhpsMb :key="zhpsData" v-else-if="pbgzlx==='ZHPS'" @handleClose2="handleClose2" @handleClose="handleClose_pbgz"
              :saveDataList="zhpsData" :editable="editable" :PSLXDM="pbgzlx"/>
      <JspsMb :key="jspsData" v-else-if="pbgzlx==='JSPS'" @handleClose2="handleClose2" @handleClose="handleClose_pbgz"
              :saveDataList="jspsData" :editable="editable" :PSLXDM="pbgzlx"/>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import vsfileupload from "@views/components/vsfileupload";
import {ElMessage} from "element-plus";
import {InfoFilled} from "@element-plus/icons-vue";
import zbfaEdit from "@views/zbxsgl/xssqgl/zbfaEdit";
import axiosUtil from "@lib/axiosUtil";

import XspsMb from "../mb/xspsMb";
import ZgpsMb from "../mb/zgpsMb";
import XyxpsMb from "../mb/xyxpsMb";
import BjpsMb from "../mb/bjpsMb";
import ZhpsMb from "../mb/zhpsMb";
import JspsMb from "../mb/jspsMb";

export default defineComponent({
  name: '',
  components: {vsfileupload,InfoFilled,zbfaEdit, XspsMb, ZgpsMb, XyxpsMb, BjpsMb, ZhpsMb, JspsMb},
  props: {
    params: {
      type: Object,
      required: true
    },
    modelValue: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      editable: props.params.editable,
      ID: props.params.id,
      rules: {
        ZBWJHQSJ: [{
          required: true,
          validator: (rule, value, callback) => {
            if(!props.modelValue.XSWJ.ZBWJHQSJKS){
              callback(new Error('开始时间不能为空'))
            }else if (!props.modelValue.XSWJ.ZBWJHQSJJS){
              callback(new Error('结束时间不能为空'))
            }else {
              callback()
            }
          }
        }],
        TBWJDJJZSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        JHKBSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        JHKBDD: [{
          required: true,
          message: '字段值不可为空',
        }],
        BGSM: [{
          required: true,
          message: '字段值不可为空',
        }],
        SFWSPS: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZBWJ: [{
          required: true,
          validator: async (rule, value, callback) => {
            if(state.ZBWJFiles.length===0){
              callback(new Error('招标文件不能为空'))
            }else {
              callback()
            }
          }
        }],
      },
      BJLCOptions: [],

      BGXX: [],

      dialogVisible: false,
      faParams: {},

      activeNames: ['1','2', '3'],
      ZBWJFiles: [],

      pbgzTableData: [
        {PSJD: '初步评审', PSLX: '形式评审', XZ: '1', QZ: '', LX: 'XSPS', checked: true},
        {PSJD: '初步评审', PSLX: '资格评审', XZ: '1', QZ: '', LX: 'ZGPS', checked: true},
        {PSJD: '初步评审', PSLX: '响应性评审', XZ: '1', QZ: '', LX: 'XYXPS', checked: true},

        {PSJD: '详细评审', PSLX: '商务部分', XZ: '1', QZ: '', LX: 'ZHPS', checked: true},
        {PSJD: '详细评审', PSLX: '技术部分', XZ: '1', QZ: '', LX: 'JSPS', checked: true},
        {PSJD: '详细评审', PSLX: '报价部分', XZ: '1', QZ: '', LX: 'BJPS', checked: true},
      ],
      dialogVisible_pbgz: false,
      pbgzlx: '',
      dialogTitle: '',
      pbgzlx2lxmc: {
        XSPS: '形式评审标准设置',
        ZGPS: '资格评审标准设置',
        XYXPS: '响应性评审标准设置',

        BJPS: '报价评审标准设置',
        ZHPS: '商务部分评审标准设置',
        JSPS: '技术评审标准设置'
      },
      xspsData: [],
      zgpsData: [],
      xyxpsData: [],
      zhpsData: [],
      jspsData: [],
      bjpsData: {
        BJFS: '01',
        GS_N1: 5,
        GS_N2: 8,
        JZJ_TOP_BFD: 1,
        JZJ_TOP_KF: 0.7,
        JZJ_TOP_JZDF: 75,
        JZJ_BUT_BFD: 1,
        JZJ_BUT_KF2: 0.5,
        JZJ_BUT_JZDF: 75,
      },//报价评审
    })


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise((resolve, reject) => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            reject(`请完善${props.modelValue.SQLX==='FZB' ? '采购文件': '招标文件'}信息`)
          }
        })
      })
    }

    watch(() => props.modelValue.XSWJ, () => {
      if (props.modelValue.XSWJ.LSWJID) {
        initBgInfo()
      }
    }, {deep: true})

    const initBgInfo = () => {
      const getObjValue = (obj,prop) => {
        let propNameList= prop.split('.')
        let value=obj;
        propNameList.forEach(name=>{
          value=value ? value[name] || '' : ''
        })
        return value
      }
      if (props.modelValue.XSWJ && props.modelValue.LSXSWJ){
        let res = []
        let checkProp = ['ZBWJHQSJKS','ZBWJHQSJJS','TBWJDJJZSJ','JHKBSJ','JHKBDD','ZBR','ZBRDZ','ZBRYB','ZBRLXR','ZBRDH','ZBRDZYJ']
        checkProp.forEach(ii => {
          if (getObjValue(props.modelValue.XSWJ,ii) !== getObjValue(props.modelValue.LSXSWJ,ii)) {
            let bgRow={
              BGQ: getObjValue(props.modelValue.LSXSWJ,ii),
              BGH: getObjValue(props.modelValue.XSWJ,ii),
              ZDMC: ii
            }
            bgRow.BGMS = `[${bgRow.BGQ || '空'}] 变更为 [${bgRow.BGH || '空'}]`
            res.push(bgRow)
          }
        })
        state.BGXX = res
      }
    }

    const isChangeT = (prop) => {
      if(prop==='ZBWJHQSJ'){
        let kssjbg=state.BGXX?.find(item => item.ZDMC === 'ZBWJHQSJKS')
        let jssjbg=state.BGXX?.find(item => item.ZDMC === 'ZBWJHQSJJS')

        if(kssjbg || jssjbg){
          return {
            BGMS: `[${(kssjbg ? kssjbg.BGQ : props.modelValue.XSWJ['ZBWJHQSJKS']) || '空'}至${(jssjbg ? jssjbg.BGQ : props.modelValue.XSWJ['ZBWJHQSJJS']) || '空'}] 变更为 [${(kssjbg ? kssjbg.BGH : props.modelValue.XSWJ['ZBWJHQSJKS']) || '空'}至${(jssjbg ? jssjbg.BGH : props.modelValue.XSWJ['ZBWJHQSJJS']) || '空'}]`
          }
        }
      }

      return state.BGXX?.find(item => item.ZDMC === prop)
    }

    const viewZbfa = () => {
      state.faParams={editable: false, id: props.modelValue.FAID, operation: 'view'}
      state.dialogVisible=true
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }
    const getZbsPbgz = (WJID) => {
      let params = {
        WJID
      };
      //正常地址
      axiosUtil.get('/backend/gcyztb/cgwjgl/selectPbbz', params).then((res) => {
        let pzData = res.data
        if (pzData.length > 0) {
          state.pbgzTableData = [];
          let pbgzData1 = {PSJD: '初步评审', PSLX: '形式评审', XZ: '1', QZ: '', LX: 'XSPS', checked: false};
          let pbgzData2 = {PSJD: '初步评审', PSLX: '资格评审', XZ: '1', QZ: '', LX: 'ZGPS', checked: false};
          let pbgzData3 = {PSJD: '初步评审', PSLX: '响应性评审', XZ: '1', QZ: '', LX: 'XYXPS', checked: false};
          let pbgzData4 = {PSJD: '详细评审', PSLX: '报价部分', XZ: '1', QZ: '', LX: 'BJPS', checked: false};
          let pbgzData5 = {PSJD: '详细评审', PSLX: '商务部分', XZ: '1', QZ: '', LX: 'ZHPS', checked: false};
          let pbgzData6 = {PSJD: '详细评审', PSLX: '技术部分', XZ: '1', QZ: '', LX: 'JSPS', checked: false};
          for (let i in pzData) {
            if (pzData[i]['PSLXDM'] == 'XSPS') {
              pbgzData1 = {PSJD: '初步评审', PSLX: '形式评审', XZ: '1', QZ: '', LX: 'XSPS', checked: true};
            } else if (pzData[i]['PSLXDM'] == 'ZGPS') {
              pbgzData2 = {PSJD: '初步评审', PSLX: '资格评审', XZ: '1', QZ: '', LX: 'ZGPS', checked: true}
            } else if (pzData[i]['PSLXDM'] == 'XYXPS') {
              pbgzData3 = {PSJD: '初步评审', PSLX: '响应性评审', XZ: '1', QZ: '', LX: 'XYXPS', checked: true}
            } else if (pzData[i]['PSLXDM'] == 'BJPS') {
              pbgzData4 = {
                PSJD: '详细评审',
                PSLX: '报价部分',
                XZ: '1',
                QZ: pzData[i]['QZB'],
                LX: 'BJPS',
                checked: true
              };
            } else if (pzData[i]['PSLXDM'] == 'ZHPS') {
              pbgzData5 = {
                PSJD: '详细评审',
                PSLX: '商务部分',
                XZ: '1',
                QZ: pzData[i]['QZB'],
                LX: 'ZHPS',
                checked: true
              };
            } else if (pzData[i]['PSLXDM'] == 'JSPS') {
              pbgzData6 = {
                PSJD: '详细评审',
                PSLX: '技术部分',
                XZ: '1',
                QZ: pzData[i]['QZB'],
                LX: 'JSPS',
                checked: true
              };
            }
          }
          state.pbgzTableData.push(pbgzData1);
          state.pbgzTableData.push(pbgzData2);
          state.pbgzTableData.push(pbgzData3);
          state.pbgzTableData.push(pbgzData4);
          state.pbgzTableData.push(pbgzData5);
          state.pbgzTableData.push(pbgzData6);
        }
        getPsgzmx(WJID)
      })

    }
    const getPsgzmx = (WJID) => {
      let params = {
        WJID
      };
      //正常地址
      axiosUtil.get('/backend/gcyztb/cgwjgl/selectCgwjMbsj', params).then(res => {
        let pageData = res.data
        state.xspsData = pageData.xspsData;
        state.zgpsData = pageData.zgpsData;
        state.xyxpsData = pageData.xyxpsData;
        state.zhpsData = pageData.zhpsData;
        state.jspsData = pageData.jspsData;
        if (pageData.bjpsData.BJPSBZBS) {
          state.bjpsData = {
            BJFS: pageData.bjpsData.PFFSDM,
            GS_N1: pageData.bjpsData.TBJS1,
            GS_N2: pageData.bjpsData.TBJS2,
            JZJ_TOP_BFD: pageData.bjpsData.JZJ_TOP_BFD,
            JZJ_TOP_KF: pageData.bjpsData.JZJ_TOP_KF,
            JZJ_TOP_JZDF: pageData.bjpsData.JZJ_TOP_JZDF,
            JZJ_BUT_BFD: pageData.bjpsData.JZJ_BUT_BFD,
            JZJ_BUT_KF2: pageData.bjpsData.JZJ_BUT_KF2,
            JZJ_BUT_JZDF: pageData.bjpsData.JZJ_BUT_JZDF,
          }
        }
        //回显是否保存了配置
        if (state.xspsData.length > 0) {
          state.pbgzTableData[0].saveFlag = true;
        }
        if (state.zgpsData.length > 0) {
          state.pbgzTableData[1].saveFlag = true;
        }
        if (state.xyxpsData.length > 0) {
          state.pbgzTableData[2].saveFlag = true;
        }
        if (pageData.bjpsData.BJPSBZBS) {
          state.pbgzTableData[5].saveFlag = true;
        }
        if (state.zhpsData.length > 0) {
          state.pbgzTableData[3].saveFlag = true;
        }
        if (state.jspsData.length > 0) {
          state.pbgzTableData[4].saveFlag = true;
        }
        state.randomKeys = Math.random();
      })
    }
    // 规则配置
    const editGzpz = (row) => {
      state.dialogVisible_pbgz = true;
      state.pbgzlx = row.LX;
      state.dialogTitle = state.pbgzlx2lxmc[row.LX];
    }
    // 关闭 配置规则
    const handleClose2 = () => {
      state.dialogVisible_pbgz = false
    }
    //关闭评标设置框
    const handleClose_pbgz = (avg, flag) => {
      //规则配置完成
      if ('XM' === flag) {
        state.XMZBSBS = avg.ZBSBS;
        if (avg.checked1) {
          queryPbbzData('XSPS');
        }
        if (avg.checked2) {
          queryPbbzData('ZGPS');
        }
        if (avg.checked3) {
          queryPbbzData('XYXPS');
        }
        if (avg.checked4) {
          queryPbbzData('ZHPS');
        }
        if (avg.checked5) {
          queryPbbzData('JSPS');
        }
      } else if ('XSPS' === flag) {
        state.XMZBSBS = '';
        state.xspsData = avg
      } else if ('ZGPS' === flag) {
        state.XMZBSBS = '';
        state.zgpsData = avg
      } else if ('XYXPS' === flag) {
        state.XMZBSBS = '';
        state.xyxpsData = avg
      } else if ('ZHPS' === flag) {
        state.XMZBSBS = '';
        state.zhpsData = avg
      } else if ('JSPS' === flag) {
        state.XMZBSBS = '';
        state.jspsData = avg
      } else if ('BJPS' === flag) {
        state.bjpsData = avg
        //回显是否保存了配置
        state.pbgzTableData[5].saveFlag = true;
      }
      //回显是否保存了配置
      if (state.xspsData.length > 0) {
        state.pbgzTableData[0].saveFlag = true;
      }
      if (state.zgpsData.length > 0) {
        state.pbgzTableData[1].saveFlag = true;
      }
      if (state.xyxpsData.length > 0) {
        state.pbgzTableData[2].saveFlag = true;
      }
      if (state.zhpsData.length > 0) {
        state.pbgzTableData[3].saveFlag = true;
      }
      if (state.jspsData.length > 0) {
        state.pbgzTableData[4].saveFlag = true;
      }
      state.randomKeys = Math.random();
      state.dialogVisible_pbgz = false;
    }
    const queryPbbzData = async (param) => {
      //state.tableData = [];
      let dm = '';
      if ('XSPS' === param) {
        dm = 'XSPS'
        state.xspsData = [];
      } else if ('ZGPS' === param) {
        dm = 'ZGPS'
        state.zgpsData = [];
      } else if ('XYXPS' === param) {
        dm = 'XYXPS'
        state.xyxpsData = [];
      } else if ('ZHPS' === param) {
        dm = 'ZHPS'
        state.zhpsData = [];
      } else if ('JSPS' === param) {
        dm = 'JSPS'
        state.jspsData = [];
      }
      let params = {
        WJID: props.params.id,
        PSLXDM: dm,
      }
      let result = await util.get('/backend/gcyztb/cgwjgl/getPbbzMxData', params);/////?????
      let data = result.data.data;
      if ('XSPS' === param) {
        state.xspsData = data;
      } else if ('ZGPS' === param) {
        state.zgpsData = data;
      } else if ('XYXPS' === param) {
        state.xyxpsData = data;
      } else if ('ZHPS' === param) {
        state.zhpsData = data;
      } else if ('JSPS' === param) {
        state.jspsData = data;
      }
    }


    onMounted(() => {
      
      getDMBData("BJLC", "BJLCOptions")
      getZbsPbgz(props.params.id)
    })

    return {
      ...toRefs(state),
      validateForm,
      isChangeT,
      viewZbfa,
      editGzpz,
      handleClose2,
      handleClose_pbgz


    }
  }

})
</script>

<style scoped>
:deep( .dataChange .el-radio){
  --el-radio-text-color: #F56C6C;
}
:deep( .dataChange  .el-radio__input.is-checked + .el-radio__label ){
  color: #F56C6C !important;
}
:deep( .dataChange .el-radio__input.is-checked .el-radio__inner){
  background: #F56C6C !important;
  border-color: #F56C6C !important;
}
:deep( .dataChange .el-input){
  --el-input-text-color : #F56C6C;
  --el-disabled-text-color: #F56C6C;
  color: #F56C6C !important;
}
:deep( .dataChange .el-textarea){
  --el-input-text-color : #F56C6C;
  --el-disabled-text-color: #F56C6C;
  color: #F56C6C !important;
}
:deep(.dataChange){
  color: #F56C6C;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
