<script setup>


</script>

<template>
	<div class="container">
		<div class="bingfa">
			<div>
				<h2>
					文件并发控制功能
				</h2>
				<h3>
					演示说明:
				</h3>
				<p style="text-indent: 2em;">
					本示例演示了如何自己开发文件并发控制功能，实现防止两个用户同时打开编辑同一份文件，造成文件保存时内容互相覆盖的问题。
					（如果您在线编辑的文件在流转中都是单用户操作的，就无需开发和使用并发控制功能，仅仅在一个处理节点中需要两个或两个以上的用户同时编辑同一个文件的时候才需要参考本示例的并发控制功能。）
				</p>
				<h3>
					操作步骤：
				</h3>
				<p>
					1. 选择一个用户，比如“<span style="color:black;font-weight:bold;">张三</span>”，登录后点击“编辑”按钮打开文件列表中的一个文件，比如“<span
						style="color:black;font-weight:bold;">产品简介</span>”；
				</p>
				<p>
					2. 重新打开一个新的浏览器窗口访问本页面，模拟另一个用户“<span
						style="color:black;font-weight:bold;">李四</span>”登录，然后同样编辑打开“<span
						style="color:black;font-weight:bold;">产品简介</span>”文件，查看文件并发控制的效果（提示文档已被其他用户打开）。
				</p>
			</div>
			<hr>
			<router-view></router-view>		
		</div>
	</div>
</template>

<style scoped>
.container {
	display: flex;
	justify-content: center;
	height: 100vh;
	/* 使容器高度占满整个视口 */
}

.bingfa {
	text-align: left;
	/* 保持文本左对齐 */
	max-width: 800px;
	/* 设置最大宽度，防止内容过宽 */
	margin: 25px 0px 0px 0px;
}

h2 {
	font-size: 18px;
	color: #00517d;
	font-weight: 700;
	text-align: center;
}

h3 {
	font-size: 14px;
	font-weight: 700;

}


</style>