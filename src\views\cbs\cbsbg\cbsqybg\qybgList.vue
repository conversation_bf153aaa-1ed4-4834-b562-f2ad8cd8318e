<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CLDXMC">
            <el-input ref="input45296" placeholder="请输入企业名称" v-model="listQuery.QYMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CLDXMC">
            <el-input ref="input45296" placeholder="请输入队伍名称" v-model="listQuery.DWMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>

            <el-button ref="button9527" type="primary" class="lui-button-add" :loading="initiating" @click="initiateChange">
              <el-icon>
                <Plus/>
              </el-icon>
              发起变更
            </el-button>
          </div>
        </el-col>

      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 180px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      @selection-change="handleSelectionChange"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="selection" width="55"/>
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="CBSDWQC" label="企业名称" align="center"
                               :show-overflow-tooltip="true" min-width="260"></el-table-column>
              <el-table-column prop="DWMC" label="队伍名称" align="center"
                               :show-overflow-tooltip="true" min-width="260"></el-table-column>
              <el-table-column prop="ZYMC" label="专业名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="YXQY" label="已选择服务区域" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import vsAuth from "@lib/vsAuth";
import {ElMessage} from "element-plus";
import TabFun from "@lib/tabFun";
import comFun from "@lib/comFun";
import {mixin} from "@core";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
      },
      tableData: [],
      total: 0,
      params: {},
      checkList: [],

      initiating: false
    })

    const getDataList = () => {
      state.checkList=[]
      const params = {
        ...state.listQuery,
        orgId: state.userInfo.orgnaId,
        TSZY: '1',
      }
      axiosUtil.get('/backend/sccbsgl/qyxxbg/selectDbgqyxxPage', params).then((res) => {
        state.tableData = res.data || []
      });
    }

    const {vsuiEventbus} = mixin()
    const initiateChange = () => {
      if(state.checkList.length===0){
        ElMessage.warning('请选择队伍专业')
        return
      }

      let QYBGID=comFun.newId()
      let params={
        CJR: state.userInfo.userLoginName,
        QYBGID: QYBGID,
        YWLX: 'QYBG',
        YWBT: state.userInfo.orgnaName+'-区域变更',
        CJRZH: state.userInfo.userLoginName,
        CJRXM: state.userInfo.userName,
        CJDWID: state.userInfo.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        BGMXList: state.checkList.map(item=>{
          return{
            ...item,
            QYBGID,
            QYBGMXID: comFun.newId(),
            BGQQYBM: item.YXQYMB,
            BGQQYMC: item.YXQY,
          }
        })
      }

      state.initiating=true

      axiosUtil.post('/backend/sccbsgl/qyxxbg/initiateQybg',params).then(res=>{
        ElMessage.success('发起成功，已保存草稿')
        state.initiating=false
        vsuiEventbus.emit('reloadBGTableData', {})
        TabFun.addTabByRoutePath("区域变更信息", "/contractors/qybgEdit", {
          params:{
            editable: true,
            id: QYBGID,
            operation: 'edit'
          }
        }, {});
      })
    }

    const handleSelectionChange = (value) => {
      state.checkList=value
    }

    onMounted(() => {
      getDataList()

    })

    return {
      ...toRefs(state),
      getDataList,
      handleSelectionChange,
      initiateChange

    }
  }

})
</script>

<style scoped>

</style>
