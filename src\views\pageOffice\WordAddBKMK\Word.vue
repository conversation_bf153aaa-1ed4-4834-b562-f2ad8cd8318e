<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function addBookMark() {
	var bkName = document.getElementById("txtBkName").value;
	var bkText = document.getElementById("txtBkText").value;
	pageofficectrl.word.AddDataRegion(bkName, bkText);
}

function delBookMark() {
	var bkName = document.getElementById("txtBkName").value;
	pageofficectrl.word.DeleteDataRegion(bkName);

}

function OnPageOfficeCtrlInit() {
	pageofficectrl.AddCustomToolButton("插入书签", "addBookMark", 5);
	pageofficectrl.AddCustomToolButton("删除书签", "delBookMark", 5);
}

function openFile() {
	return request({
		url: '/WordAddBKMK/Word',
		method: 'get',
	})
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit,addBookMark,delBookMark };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<div style=" font-size:small; color:Red;">
			<label>关键代码：看js函数：</label>
			<label>function addBookMark() 和 function delBookMark()</label>
			<br />
			<label>插入书签时，请先输入要插入的书签名称和文本；删除书签时，请先输入相应的书签名称！</label><br />
			<label>书签名称：</label><input id="txtBkName" type="text" value="test" />
			&nbsp;&nbsp;<label>书签文本：</label><input id="txtBkText" type="text" value="[测试]" />
		</div>
		<input id="Button1" type="button" @click="addBookMark();" value="插入书签" />
		<input id="Button2" type="button" @click="delBookMark()" value="删除书签" />
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
