<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="data.queryForm.dwmc" placeholder="请输入队伍名称" @keyup.enter="query"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.sqlb" placeholder="请选择引入类型" clearable>
            <el-option v-for="(item, index) in data.importTypes"
                       :key="index + 'bgyy'" :label="item.DMMC" :value="item.DMXX"/>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.dwzt" placeholder="请选择状态" clearable>
            <el-option v-for="(item, index) in data.teamStatus" :key="index + 'dwzt'"
                       :label="item.DMMC" :value="item.DMXX"/>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="10" class="grid-cell">
        <div style="display:flex;">
          <el-button @click="resetQuery"><el-icon><RefreshRight/></el-icon>重置</el-button>
          <el-button type="primary" @click="query"><el-icon><Search/></el-icon>查询</el-button>
          <el-button type="primary" @click="exportData"><el-icon><Upload/></el-icon>导出</el-button>
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          highlight-current-row
          size="default"
          height="calc(100vh - 250px)"
          ref="table"
          fit
          border
          :data="data.tableData"
          v-loading="tableLoading"
      >
        <EleProTableColumn
            v-for="prop in data.tableColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #view="{row}">
                        <span @click="goCbsXq(row)" style="cursor: pointer; color: #5F80C7;">
                            {{row[prop.prop]}}
                        </span>
          </template>
          <template #importType="{row,$index}">
            <div>
              {{data.importType[row.SQLBDM]}}
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
      <el-pagination
          background
          v-model:current-page="data.queryForm.page"
          v-model:page-size="data.queryForm.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="data.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
  </el-form>
</template>
<script setup>
import {onMounted, onUnmounted, reactive, useAttrs, watch, ref} from "vue";
import {runtimeCfg,eventBus, mixin} from '@src/assets/core/index';
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getReportDwmx,getReportDwmxExport,getBgGetChangedTeamById,getTeamreslutGetProDetails} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam, getCommonSelectDMB} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import tabFun from "@src/lib/tabFun";
import { Search, Upload, Plus,RefreshRight} from '@element-plus/icons-vue'
import axiosUtil from "@lib/axiosUtil";
const attrs = useAttrs();
const {zybm,qylx,qy} = attrs;
import cbsjbxxIndex from "@views/cbs/cbsyj/index.vue"
import dwjbxx from "@views/cbs/cbsyj/yrsqxxIndex.vue"
import comFun from "@lib/comFun";

const data = reactive({

    total: 0,
    queryForm: {
        dwmc: null,
        sqlb: null,
        dwzt: null,
        qylx: null,
        page: 1,
        size: 10
    },
    systemUnits:[],
    teamStatus:[],
    currentUser: {},
    tableData: [],
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 60,
            align: "center"
        },
        {
            label: "企业名称",
            prop: "CBSDWQC",
            align: "left",
            showOverflowTooltip: true,
        },
        {
            label: "队伍名称",
            prop: "DWMC",
            align: "left",
            showOverflowTooltip: true,
            slot: "view"
        },
        {
            label: "队伍编码",
            prop: "DWBM",
            align: "left",
            showOverflowTooltip: true,
        },
        {
            label: "服务范围",
            prop: "fwfw",
            align: "left",
            showOverflowTooltip: true,
            width: 300,
        },
        {
            label: "引进类型",
            prop: "SQLBDM",
            align: "center",
            width: 150,
            showOverflowTooltip: true,
            slot: 'importType'
        },
        {
            label: "初次引进日期",
            prop: "CJSJ",
            align: "center",
            showOverflowTooltip: true,
        },
        {
            label: "推荐单位",
            prop: "TJDWMC",
            align: "left",
            showOverflowTooltip: true,
        }
    ],
    entType: {},
    entUnit: {},
    importTypes: [],
    importType: {}
})

const resetQuery = () => {
    data.queryForm = {
        zybm: null,
        dwmc: null,
        sqlb: null,
        dwzt: null,
        page: 1,
        size: 10
    }
}

const exportData = ()=>{
  let column = [[
    { field: 'CBSDWQC', title: '企业名称'},
    { field: 'DWMC', title: '队伍名称'},
    { field: 'DWBM', title: '队伍编码'},
    { field: 'fwfw', title: '服务范围'},
    { field: 'SQLBDM', title: '引进类型'},
    { field: 'CJSJ', title: '初次引进日期'},
    { field: 'TJDW', title: '推荐单位'},
  ]]
  let params = {
    title: "队伍信息",
    name: "队伍信息",
    params: data.queryForm,
    url: '/excel/dwxxExport',
  }
  params.column = column
  axiosUtil.exportExcel('/backend/excelExport/magicExcel', params)
}
const tableLoading = ref(false);
const query = (o) => {
    if(o){
        data.queryForm.zybm = o.zybm;
        data.queryForm.qylx = o.qylx;
    }
    tableLoading.value = true;
    getReportDwmx(data.queryForm).then(res => {
        console.log(res)
        data.tableData = res.data.list
        data.total = res.data.total
    }).catch((err) => {
        console.log(err);
    }).finally(() => {
        tableLoading.value = false
    })

}
const {vsuiEventbus} = mixin()
const getUserInfo = () => {
    let user = VSAuth.getAuthInfo().permission
    getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
        data.currentUser = res.data;
    })
}

const handleSizeChange = (val) => {
    data.queryForm.size = val;
    query();
}
const handleCurrentChange = (val) => {
    data.queryForm.page = val;
    query();
}

const comBelong = ()=>{
    getCommonSelectDMB({DMLBID: 'DWXTGS'}).then(res=>{
        data.systemUnits = res.data;
        res.data.forEach(x=>{
            data.entUnit[x.DMXX] = x.DMMC
        })
    })
}

const getTeamStatus = ()=>{
    getCommonSelectDMB({DMLBID: 'DWZT'}).then(res=>{
        data.teamStatus = res.data
    })
}

const getImportTypes = ()=>{
    getCommonSelectDMB({DMLBID: 'YRLX'}).then(res=>{
        data.importTypes = res.data
        res.data.forEach(x=>{
            data.importType[x.DMXX] = x.DMMC
        })
    })
}

const getEntType = ()=>{
    getCommonSelectDMB({DMLBID: 'QYLX'}).then(res=>{
        res.data.forEach(x=>{
            data.entType[x.DMXX] = x.DMMC
        })
    })
}

const goCbsXq = ({DWYWID,DWLX,EXTENSION,CBSDWQC,DWMC}) => {
    if(DWLX == 'CBS'){
        let ex = JSON.parse(EXTENSION)
        getTeamreslutGetProDetails({dwid:DWYWID}).then(res=>{
            tabFun.addTabByCustomName(
                CBSDWQC,
                'cbsjbxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
                cbsjbxxIndex,
                {
                    uuId : DWYWID, //队伍业务ID
                    MBID: ex.MBID, //模板ID
                    MBLX: "QY", //模板类型、
                    ZYFLDM: res.data.ZYFLDM, //专业分类代码
                    YWLXDM: "BG", //业务类型代码
                    editable: false,//是否查看
                  isVIewJgxx: true
                },
                {}
            );
            // vsuiEventbus.emit("reloadCbsjbxx", {
            //     uuId : DWYWID, //队伍业务ID
            //     MBID: ex.MBID, //模板ID
            //     MBLX: "QY", //模板类型、
            //     ZYFLDM: res.data.ZYFLDM, //专业分类代码
            //     YWLXDM: "BG", //业务类型代码
            //     editable: false,//是否查看
            //   isVIewJgxx: true
            // });
        })
    }else if(DWLX == 'DW'){
        tabFun.addTabByCustomName(DWMC,
            'dwxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
            dwjbxx, {DWYWID: DWYWID,YWLXDM: 'BG',backPath: '/query-analysis/dwmx',
            JGDWYWID: DWYWID,//结果表队伍业务ID
            editable: false,//是否查看
          isVIewJgxx: true
        }, {});

    }
}


onMounted(() => {
    if(zybm){
        data.queryForm.zybm = zybm;
    }
    if(qylx){
        data.queryForm.qylx = qylx;
    }
  if(qy){
    data.queryForm.qy = qy;
  }
    getTeamStatus();
    getImportTypes();
    getUserInfo();
    query();
    vsuiEventbus.on('reloadTeamList',query);
})
onUnmounted(()=>{
    // timer.value = null;
    vsuiEventbus.off('reloadTeamList',query);
})
</script>

<style scoped>
.header {
    padding: 10px 10px 0;
    height: 50px;
    line-height: 50px;
}

.main {
    height: calc(100% - 155px);
    padding: 10px;
}
.footer {
    height: 100px;
    line-height: 100px;
}
</style>
