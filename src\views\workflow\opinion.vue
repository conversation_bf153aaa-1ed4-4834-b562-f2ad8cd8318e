<template>
  <div class="root" ref="root">
    <!-- <el-input v-model="shyj" type="textarea" :rows="5"
      :placeholder="flowdirection == '0' ? '请输入退回意见' : '请输入审核意见'"></el-input>
    <div style="text-align: center; height: 40px; padding: 10px">
      <el-button v-if="flowdirection == '1'" type="primary" @click="onConfirm">同意</el-button>
      <el-button v-else-if="flowdirection == '0'" type="danger" @click="onConfirm">退回</el-button>
      <el-button v-else type="primary" @click="onConfirm">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div> -->
    <component></component>
  </div>
</template>

<script setup>
import { defineAsyncComponent, ref, watch } from 'vue';
import vsflow from "@views/vsflow/index.js";

const currentComponent = ref(null);

// // 根据路径动态加载组件
// const openDialog = async (componentPath) => {
//   try {
//     currentComponent.value = defineAsyncComponent(() => import(/* @vite-ignore */ componentPath))
//     dialogVisible.value = true
//   } catch (e) {
//     console.error('组件加载失败:', e)
//   }
// }

</script>

<style scoped>
.root {
  padding: 10px;
}
</style>