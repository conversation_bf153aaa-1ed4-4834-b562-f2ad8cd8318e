<template>
  <el-form
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="140px"
      size="default"
      class="lui-card-form">
    <el-row class="grid-row" style="margin-bottom:16px;">
      <el-col :span="8" class="grid-cell">
        <el-form-item label="专业名称" prop="ZYMC">
          <el-input v-model="form.ZYMC" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="专业编码" prop="ZYBM">
          <el-input v-model="form.ZYBM" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="专业层级" prop="ZYCJBM">
          <!-- <el-input v-model="form.ZYCJBM" clearable placeholder="请输入"></el-input> -->
          <el-select v-model="form.ZYCJBM" clearable placeholder="请选择">
            <el-option
                v-for="(item, index) in [{label:'一级',value:'1'},{label:'二级',value:'2'},{label:'三级',value:'3'}]"
                :key="index"
                :label="item.label"
                :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <!-- <el-col :span="8" class="grid-cell">
        <el-form-item label="专业级别" prop="ZYJB">
          <el-input v-model="form.ZYJB" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col> -->

      <el-col :span="8" class="grid-cell">
        <el-form-item label="父专业名称" prop="FZYMC">
          <div style="margin-left: 10px">{{form.FZYMC || '根节点'}}</div>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="父专业编码" prop="FZYBM">
          <div style="margin-left: 10px">{{form.FZYBM}}</div>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="是否有效" prop="YXBZ">
          <el-select v-model="form.YXBZ" clearable placeholder="请选择">
            <el-option
                v-for="(item, index) in [{label:'有效',value:'1'},{label:'无效',value:'0'}]"
                :key="index"
                :label="item.label"
                :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="是否末级" prop="SFMJ">
          <el-select v-model="form.SFMJ" clearable placeholder="请选择">
            <el-option
                v-for="(item, index) in [{label:'是',value:'1'},{label:'否',value:'0'}]"
                :key="index"
                :label="item.label"
                :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>

<!--      <el-col :span="8" class="grid-cell">-->
<!--        <el-form-item label="是否自营" prop="SFZY">-->
<!--          <el-radio-group v-model="form.SFZY" clearable placeholder="请输入">-->
<!--            <el-radio label="1">是</el-radio>-->
<!--            <el-radio label="0">否</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
<!--      </el-col>-->
      <el-col :span="8" class="grid-cell">
        <el-form-item label="专业分类" prop="ZYFL">
          <el-select v-model="form.ZYFL" clearable placeholder="请选择">
            <el-option
                v-for="(item, index) in zyflOptions"
                :key="index"
                :label="item.DMMC"
                :value="item.DMXX"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="核查报告" prop="BYZD2">
          <el-select v-model="form.BYZD2" clearable placeholder="请选择">
            <el-option
                v-for="(item, index) in [{label:'是',value:'1'},{label:'否',value:'0'}]"
                :key="index"
                :label="item.label"
                :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="最低定员数" prop="byzd2">
          <el-input v-model="form.BYZD2" type="number" clearable placeholder="请输入">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="注册资金最低要求" prop="byzd3">
          <el-input v-model="form.BYZD3" type="number" clearable placeholder="请输入">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="注册最低年限" prop="byzd4">
          <el-input v-model="form.BYZD4" type="number" clearable placeholder="请输入">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="16" class="grid-cell">
        <el-form-item label="排序码" prop="pxm">
          <el-input v-model="form.PXH" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">
        <el-form-item label="范围描述" prop="FWMS">
          <el-input
              v-model="form.ZYMS"
              :rows="3"
              type="textarea"
              clearable
              placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0" class="grid-row">
      <el-collapse v-model="activeName" accordion>
        <el-collapse-item name="zyglqx">
          <template #title>
            <div>专业管理权限</div>
          </template>
          <div>
            <el-table :data="form.tableData" border class="lui-table">
              <el-table-column
                  label="序号"
                  type="index"
                  header-align="center"
                  align="center"
                  width="80"
              ></el-table-column>
              <el-table-column
                  label="权限级别"
                  prop="QXLBMC"
                  header-align="center"
                  align="left"
              ></el-table-column>
              <el-table-column
                  label="负责单位"
                  prop="FZDWID"
                  header-align="center"
                  align="left"
              >
                <template v-slot="scope">
                  <div>
                    <el-select
                        v-model="scope.row.FZDWID"
                        clearable
                        placeholder="请选择"
                    >
                      <el-option
                          v-for="(item, index) in fzdwOptions"
                          :key="index"
                          :label="item.DMMC"
                          :value="item.DMXX"
                      ></el-option>
                    </el-select>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                  label="分管领导"
                  prop="FGLD"
                  header-align="center"
                  align="left"
              >
                <template #default="{row}">
                  {{fzdwOptions.find(item=>item.DMXX===row.FZDWID)?.FGLD}}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-row>
    <el-row class="btn" align="center" justify="center">
<!--      <el-button type="primary" @click="temporaryStore">暂存</el-button>-->
      <el-button type="primary" @click="submit">提交</el-button>
      <el-button @click="handleReturn">返回</el-button>
    </el-row>
  </el-form>
</template>

<script setup>
import outerBox from "@components/common/outerBox";
import {
  ref,
  reactive,
  getCurrentInstance,
  onMounted,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import axiosUtil from "@lib/axiosUtil";
import { v4 as uuidv4 } from "uuid";
import { ElMessage, ElMessageBox } from "element-plus";
import { postZyglSaveZygl } from "@src/api/sccbsgl";
import { getCommonSelectDMB } from '@src/api/common.js'

const activeName = ref("zyglqx");
const props = defineProps({
  title: {
    type: String,
    default: "专业编辑",
  },
  editData: {
    type: Object,
    default: () => {},
  },
});
const emits = defineEmits(["close"]);
const defaultData = reactive({
  ZYMC: "",
  FZYBM: '0',
  ZYJB: 1 ,
  ZYBM: "",
  ZYCJ: "",
  SFZY: "1",
  ZYFL: "",
  FWMS: "",
  tableData: [
    { qxjb: "专业管理部门", fzdw: "", fzr: "" },
  ],
});
const form = ref({
  ZYMC: "",
  ZYBM: "",
  ZYCJ: "",
  SFZY: "1",
  ZYFL: "",
  ZYMS: "",
  ZYID: "",
  BYZD2:"",
  BYZD3:"",
  BYZD4:"",
  PXH:"",
  ZYBMC: "",
  tableData: [
    { qxjb: "专业管理部门", fzdw: "", fzr: "" },
  ],
});
watch(
  () => props.editData,
  (val) => {
    // console.log(val);
    // form.value.ZYID = uuidv4().replace(/-/g, '').toUpperCase(),
    form.value = val ? Object.assign(form.value, val) : defaultData;
  },
  {
    immediate: true,
  }
);
// 专业分类下拉数据
const zyflOptions = ref([]);
const getZyfl = () => {
  getCommonSelectDMB({
    DMLBID: "ZYFL"
  }).then(({data}) => {
    if(data) zyflOptions.value = data;
  }).catch((err) => {
    ElMessage.error('岗位信息获取失败')
  });
}
onMounted(() => {
  getZyfl(),
      getTableData()
      getFzdw()
});
const getTableData = () => {
  let params={
    ZYBM:form.value.ZYBM
  }
  axiosUtil.get('/backend/sccbsgl/cbsyj/selectZyglqx', params).then((res) => {
    getZYGLQXLB(res.data)
  })
}

// 表格数据
const getZYGLQXLB = (tableData) => {
  // return
  getCommonSelectDMB({
    DMLBID: "ZYGLQXLB"
  }).then(({data}) => {
    if(data) {
      let tableDataDefault = [];
      console.log(form.value);
      data.forEach(item=>{
        let filterXz = tableData.find(x=>x.QXLB == item.DMXX)
        console.log('filterXz',{...filterXz});
        let dwqx = {
          ID: uuidv4().replace(/-/g, '').toUpperCase(),
          ZYID: form.ZYID,
          ZYBM: form.ZYBM,
          QXLB: item.DMXX,
          QXLBMC: item.DMMC,
          FZRMC: filterXz?filterXz.FZRMC:"",
          FZDWID: filterXz?filterXz.FZDWID:"",
          YXBZ: "1",
          PXH: form.PXH
        }
        tableDataDefault.push(dwqx)
      })
      form.value.tableData = tableDataDefault;
    }
  }).catch((err) => {
    ElMessage.error('权限类别获取失败')
  });
}
// 负责单位下拉数据
const fzdwOptions = ref([]);
const getFzdw = () => {
  axiosUtil.get('/backend/sccbsgl/zygl/selectFzdwList', {}).then(({data}) => {
    if(data) fzdwOptions.value = data;
  }).catch((err) => {
    ElMessage.error('岗位信息获取失败')
  });
}
const rules = reactive({

});
/**查询专业管理权限 */
const queryZyglqx = () => {};
/**查询负责单位 */
const queryFzdw = () => {};
/**查询专业分类 */
const queryZyfl = () => {};
/**暂存 */
const temporaryStore = () => {
  ElMessageBox.confirm("是否暂存当前数据？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // TODO 暂存
      ElMessage({
        message: "已暂存",
        type: "success",
      });
    })
    .catch(() => {
      ElMessage({
        message: "已取消暂存",
        type: "info",
      });
    });
};
/**提交 */
const submit = () => {
  ElMessageBox.confirm("是否提交当前数据？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      form.value.tableData.forEach(item=>{
        let filterXz = fzdwOptions.value.filter(x=>x.DMXX == item.FZDWID)
        console.log(filterXz)
        let dwqx = {
          ID: uuidv4().replace(/-/g, '').toUpperCase(),
          ZYID: form.value.ZYID,
          ZYBM: form.value.ZYBM,
          QXLB: item.QXLB,
          FZDWMC: filterXz.length>0?filterXz[0].DMMC:"",
          FZDWID: item.FZDWID,
          YXBZ: "1",
          PXH: form.value.PXH
        }
        Object.assign(item,dwqx)
      })
      form.value.ZYJB=form.value.ZYCJBM;
      form.value.ZYBMC = form.value.ZYMC;
      // TODO 提交
      postZyglSaveZygl({...form.value})
        .then(() => {
          ElMessage({
            message: "已提交",
            type: "success",
          });
          emits('close')
        })
        .catch(() => {
          ElMessage({
            message: "提交失败",
            type: "error",
          });
        });
    })
    .catch((err) => {
      console.log(err)
      ElMessage({
        message: "已取消提交",
        type: "info",
      });
    });
};
/**返回 */
const handleReturn = () => {
  emits("close");
  // TODO 返回
};
onMounted(() => {});
</script>

<style scoped src="../style/index.css"></style>
<style scoped>
.el-collapse {
  width: 100%;
}
.el-collapse ::v-deep .el-collapse-item__header {
  background-color: #e3e6f6;
  padding-left: 15px;
}
.out-box-content{
  padding: 20px;
}
</style>
