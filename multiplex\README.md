
## 胜软@vsui/vue-multiplex框架核心组件说明文档


组件开发者：崔良

组件版本：@vsui/vue-multiplex@2.1.0

框架工程适用版本：>=vsui.vue@^2.1.0

***<font size="4px" color="red">使用时，请明确使用的版本号，请勿使用^2.1.0或~2.1.0之类模糊的版本！</font>***



效果图如下：

[![查看框架核心组件项目模板](
data:image/jpeg;base64,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
)](http://**********:30669/)




## 1：版本演化


### V2.1.0新特性
	
- 从rc1-rc7迭过程修复、升级的内容
- 依赖项重新编排，不再限定npm环境为6.14.15

### V2.1.0升级内容

- 升级编译依赖项，不再限定npm版本为6.x.x


### 定制说明

本版本前端框架核心库使用大量变量定义、事件通知、复杂的css选择器实现，因此定制化开发需要较强的动手能力，加之其他方面综合考虑后，核心库进行组件化，如果您想进行功能定制，请首先翻阅<a href="secdev">二次开发</a>是否能满足您的需求，

其次，您可以：

1：最终用户对界面布局与风格没有配置要求，且界面元素与框架界面相差较大，您可以使用低版本的框架（如：vsui@1.2.2）进行自行定制；

2：最终用户对界面布局与风格没有配置要求，且界面元素与客户要求无差别，仅在配色上存在差别，您首先通过配置实现界面布局，然后通过css样式覆盖实现之；

3：其他未尽事宜请联系框架开发者（<EMAIL>），由开发者甄别是否在不影响框架复杂配置与切换机制的前提下，能够融入您的界面需求；


## 2：开发说明

### 2.1：如何获取一个开发环境

框架以NPM组件的方式提供，并且提供项目开发模板，这使得项目开发模板的目录结构极其简单，开发模板中有如何启用/引入框架的基本代码，您只需要关注您的业务页面得开发。

框架核心组件无法直接运行，您需要一个vue工程。[vsui.vue@2.1.0示例工程]("http://***********/UE/vsui-vue/-/archive/V2.1.0/vsui-vue-V2.1.0.zip",示例模板)提供源码，手把手教你如何引入、使用框架。您也可以自己搭建一个工程，仿照示例工程引入框架核心库。


## 3：<a id="core">组件开放对象</a>

### 3.1：<a id="core-export">组件内核公开点</a>
```script
export {
    app, vue, vsuiapi, router, store, mixin, elementPlus, axios, console, auth, NameSpace, namespace, eventDefine, eventBus, version ,runtimeCfg
}
```

使用框架核心组件，意味着您需要使核心组件提供的周边组件，VUE3.x之后的版本，vue应用、VUE-Router、VUE都是通过CreateXXX创建所得，创建过程由框架核心库完成并导出到项目中，开发者通过调用API来完成项目内的扩展需要。



#### 3.1.1：<a id="core-export-app">app</a>

app公开了核心组件内部通过vue的API接口createApp构建的应用对象，此对象没有安装任何VUE插件,您仍需要在项目中调用use安装插件，例如：
```
import { app, router, store , elementPlus } from "@vsui/vue-multiplex";

 app
 .use(router)
 .use(store)
 .use(elementPlus, { size: 'small', zIndex: 3000 })
 .mount('#app');
 
```
<font size="2px" color="#666">

tips：*具体代码请查看示例工程*

</font>



#### 3.1.2：<a id="core-export-vue">vue</a>

这里完整导出vue,伪代码如下：
```
export * from 'vue';
```

您可以在工程中引入该组件使用vue,如：
```
//引入框架导出的vue
import{vue} from '@vsui/vue-multiplex';

const index=vue.ref(0);
```

也可以 使用
```
//引入vue
import{ref} from 'vue';

const index=ref(0);
```

<font size="2px" color="#666">

tips：*使用@vsui/vue-multiplex公开的vue对象可以借助开发工具的接口发现可以自动提示，原生引入vue需要您自行查找定义与API接口*

</font>

#### 3.1.3：<a id="core-export-vsuiapi">vsuiapi</a>



#### 3.1.4：<a id="core-export-router">router</a>

核心组件内构建router，注册内置拦截器，并完整导出vue-router，供外部扩展与使用伪代码如下：
```
const router = new createRouter({})
```
内置路由默认注册了4个默认路由地址：

* 核心库UI的挂载点，请参考<a href="core-ui">3.2:UI界面挂载点</a>；

* 默认首页面dashboard；

* 403页面；

* 404页面；

项目中注册router时，请通过VUE-Router的API接口[addRoute](https://router.vuejs.org/api/#addroute-1)进行扩展

#### 3.1.5：<a id="core-export-store">store</a>

核心组件内构建vuex，用于存储核心组件的内置状态，并完整导出vuex，供外部扩展与使用，

项目中注册存储模块时，请通过VUEX的API接口[registerModule](https://vuex.vuejs.org/api/#registermodule)进行扩展

#### 3.1.6：<a id="core-export-mixin">mixin</a>

非vue2中的混入，此库将常用对象进行封装，可以直接在组件中导入使用：
```
import { mixin} from '@vsui/vue-multiplex';
setup(){
	const {
		/**
		* 前端运行的虚拟目录，取值自runtimeCfg.app_public_path，引入资源时需要将此作为前缀
		*/
		basePath,
		/**
		* 当前路由信息 = useRoute()
		*/
		vsuiRoute,
		/**
		* 路由定义信息，@vsui/vue-multiplex导出的router对象
		*/
		vsuiRouter,
		/*
		* 核心组件认证对象
		*/
		vsuiAuth,
		/**
		* 状态定义信息，@vsui/vue-multiplex导出的vuex对象
		*/
		vsuiStore,
		/**
		* Axios实例
		*/
		vsuiAxios,
		/**
		* 全局事件总线,@vsui/vue-multiplex导出的eventBus对象
		*/
		vsuiEventbus
	} = mixin();
}
```
#### 3.1.7：<a id="core-export-elementplus">elementPlus</a>

核心组件默认引入Element-Plus的主题样式，语言、以及核心库对组件的主题自动适配。

开发者无需手动操作，这是内置能力。

#### 3.1.8：<a id="core-export-axios">axios</a>

核心库导出一个默认的axios操作实例。

#### 3.1.9：<a id="core-export-console">console</a>

核心库对控制台输出的控制，可以通过外置配置文件__app_log_open__设置日志开关，外置配置文件请参考<a src="#config">组件外置配置<a/>

#### 3.1.10：<a id="core-export-auth">auth</a>

核心组件提供的用户认证信息的初始化入口，auth对象提供一个createAuth初始化函数，函数接受一个配置，配置结构如下：
```
{
	/**
	 * 必须实现
	 * 当前登录用户名字符串
	 * 数据示例："admin",
	 * @return 返回用户名字符串
	 */
	getUserName:function(){
		return ""
	},
	/**
     * 必须实现
     * 当前登录用户名字符串
     * 数据示例："崔良",
	 * @return 返回用户真实名称字符串
     */
    getRealName () {
		return VSAuth.getAuthInfo().realName;
	  },
	/**
	 * 必须实现
	 * 用户的权限完整信息，JSON对象,结构自定义,框架中仅做保存，未使用，以备业务需要
	 * 数据示例:{权限JSON对象,结构自定义,框架中仅做保存，未使用，以备业务需要}
	 * @return 返回用户权限信息原示数据
	 */
	getPermission(){
		//数据转换操作
		return Object;
	},
	/**
	 *
	 * 必须实现
	 * 用户资源菜单为树状结构
	 * 结构如下：
	 *      [{
	 *           resId:"菜单唯一识别符,必选",
	 *           iconColor:"#000,代表菜单图标字体颜色，可选",
	 *           iconClass:"fa fa-bus 字体字库名称，代表菜单图标，可选",
	 *           resPath:"菜单url路径，第一级非必选，二级及以下菜单必选",
	 *           resName:"菜单名称文字",
	 *           children:[{
	 *				 resId:"菜单唯一识别符,必选",
	 *               iconColor:"#000,代表菜单图标字体颜色，可选",
	 *               iconClass:"fa fa-bus 字体字库名称，代表菜单图标，可选",
	 *               resPath:"菜单url路径，第一级非必选，二级及以下菜单必选",
	 *               resName:"菜单名称文字"},
	 *               {...},
	 *               {...}]
	 *          }],
	 * @return 模块树状菜单
	 */
	getModulesTree(){ 
		//数据转换操作
		return Array;
	},
	/**
	 * 非必须
	 * 为使用jwt等模式的登陆时预留
	 * 在REST无会话模式下使用的票据信息
	 * @return token
	 */
	getToken(){
		return "";
	},
}
```

<font size="2px" color="#666">

tips 1：*函数体return之前您可以转换您的数据格式为@vsui/vue-multiplex使用的数据；*

tips 2：*配置文件的文件名修改后会导致云平台的多云切换功能不可用。*

</font>


#### 3.1.11：<a id="core-export-NameSpace">NameSpace</a>

核心组件增加对命名空间的支持。命名空间用于隔离应用公开的变量，避免多个应用在集成时存在同名变量而丢失变量的问题，该库来源自VSNPM的[@vsui/lib-jsext](http://***********:7002/package/@vsui/lib-jsext)组件。

#### 3.1.12：<a id="core-export-namespace">namespace</a>

核心组件注册的命名空间，可以在window下直接访问，格式大致如：window.victorysoft.software.frame.vsui.vue.multiplex.v2_1_0_rc8，这是核心库的命名空间，请区别项目的命名空间。

#### 3.1.13：<a id="core-export-eventDefine">eventDefine</a>

事件是核心库的重要组成部分，也是组件化后需要考虑的对外消息通知点，优秀的事件定义描述将有效的帮助开发人员鉴别消息，此内容将另外展开描述，<a href="#core-events-eventDefine">点击查看详情</a>。

#### 3.1.14：<a id="core-export-eventBus">eventBus</a>

事件是核心库的重要组成部分，也是组件化后需要考虑的对外消息通知点，全面的事件触发机制将是开发者可以灵活订阅消息，充分利用观察者模式来处理相应操作。 此内容将另外展开描述，<a href="#core-events-eventBus">点击查看详情</a>。

全局事件总线。为了便于开发者进行二次开发，框架存在诸多的事件通知，eventBus是事件通知的一种，这种方式请见<a href="#core-export-console">eventDefine</a>对事件的订阅方式

#### 3.1.15：<a id="core-export-version">version</a>

vsui/vue-multiplex组件的版本信息描述。其描述信息如下：
```
{
    name:"@vsui/vue-multiplex",
    release:"发布版本",
    developer:"开发者",
    email:"邮件",
    company:"公司信息",
}
```

#### 3.1.16：<a id="core-export-runtimeCfg">runtimeCfg</a>

运行时配置作为@vsui/vue-multiplex核心库的重点特色，将另外展开描述，<a href="#config">点击查看详情</a>。


### 3.2：<a id="core-ui">UI界面挂载点</a>

框架核心库的界面UI默认挂在路由名为"@vsui/vue-multiplex",路径为"/"的节点上，UI的路由定义伪代码如下：
```
   {
     path: '/',
     name: "@vsui/vue-multiplex",
     component: @vsui/vue-multiplex UI组件,
   },
```
<font size="2px" color="#666">

tips 1：*您可以在工程中通过VUE-Router的API接口[addRoute(route: RouteRecordRaw): () => void](https://router.vuejs.org/api/#addroute-1)进行调整，*

tips 2：*模板工程中，通过使用VUE-Router的API接口[addRoute(parentName: string | symbol, route: RouteRecordRaw): () => void](https://router.vuejs.org/api/#addroute)将使用框架的路由页面注册到框架路由下*

</font>

### 3.3：<a id="core-events">组件内核事件</a>

组件事件通知分为事件定义与事件通知两部分

#### 3.3.1 <a id="core-events-eventDefine">事件定义</a>

核心组件开放的事件定义信息，所有事件定义如下：
```
{
 
    header:{
        menuItemClicked: "头部菜单点击事件，此时路由地址已变化，函数签名：({module:菜单对应的模块信息})",
    },
    sidebar:{
        main:{
          menuItemClicked: "侧边栏无子菜单项的主菜单项被点击后所触发的事件，框架对此事件进行了路由切换的操作，外部还可以监听进行其他处理，函数签名：({module:菜单对应的模块信息})",
          subMenuOpen: "侧边栏有子菜单项的主菜单项被展开时触发的事件，框架不响应此事件，仅传出事件，外部可以监听进行其他处理，函数签名：({module:菜单对应的模块信息,state:扩展信息})",
          subMenuClose: "侧边栏有子菜单项的主菜单项被关闭时触发的事件，框架不响应此事件，仅传出事件，外部可以监听进行其他处理，函数签名：({module:菜单对应的模块信息,state:扩展信息})",
        },
        sub:{
          menuItemClicked: "侧栏双菜单模式时，副菜单的最终极菜单项被点击后所触发的事件，框架对此事件进行了路由切换的操作，外部还可以监听进行其他处理",
          subMenuOpen: "侧边双菜单模式时，有子菜单项的副菜单项被展开时触发的事件，框架不响应此事件，仅传出事件，外部可以监听进行其他处理，函数签名：({module:菜单对应的模块信息})",
          subMenuClose: "侧边双菜单模式时，有子菜单项的副菜单项被关闭时触发的事件，框架不响应此事件，仅传出事件，外部可以监听进行其他处理，函数签名：({module:菜单对应的模块信息})",
        }  
    },
    content:{
        multipage:{
            changeCurrentTab: "当多页面模式时，切换多页面的选项卡时触发本事件，函数签名：({oldValue:原选项卡名称,newValue:新选项卡名称,allowChange:允许切换})",
        },
        singlepage:{

        },
        refreshCurrentRoute: "刷新当前工作区",
    },
 
      changedSideBarCollapse: "修改侧边栏区域展开合并设置，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；如启用，则自动增加样式名：vsui-style-layout-sidebar-collapse",
      changedSideBarClassName: "设置侧边栏风格样式，函数签名：({oldValue:旧模式字符串,newValue:新模式字符串})；取值：vsui-style-theme-sidebar-dark，vsui-style-theme-sidebar-light，切换风格则自动设置为相应状态的样式名",
      changedHeaderBarClassName: "设置头部风格样式，函数签名：({oldValue:旧模式字符串,newValue:新模式字符串})；取值：vsui-style-theme-header-dark,vsui-style-theme-header-light,vsui-style-theme-header-color，切换风格则自动设置为相应状态的样式名",
      changedControlClassName: "修改界面风格（主题）设置，函数签名：({oldValue:旧模式字符串,newValue:新模式字符串})；取值：vsui-style-theme-color-1[-8]，切换风格则自动设置为相应状态的样式名,",
      changedControlCustomColor: "当changedControlClassName为vsui-style-theme-color-custom时，由用户指定的主色调,",
      changedDarkMode: "修改黑暗模式设置，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；bool值：是/否，如启用，则自动增加样式名：vsui-style-theme-dark",
      changedNavigationMode: "修改导航模式，函数签名：({oldValue:旧模式字符串,newValue:新模式字符串})；取值：vsui-style-layout-nav-mode-side:左侧菜单布局（所有级别,侧边显示，向下展开）,vsui-style-layout-nav-mode-header:顶部菜单布局（所有级别，头部显示，向下展开）,vsui-style-layout-nav-mode-mixture:混合菜单布局（顶部显示一级菜单，侧边为二级及以下），切换模式则自动设置为相应状态的样式名",
      changedDoubleSidebar: "侧栏双排菜单模式，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；如启用，则自动增加样式名：vsui-style-layout-doublesidebar",
      changedBodyBespread: "修改内容区域铺满设置，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；如启用，则自动增加样式名：vsui-style-layout-bodybespread",
      changedFixedHeader: "修改固定顶栏区域设置，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；如启用，则自动增加样式名：vsui-style-layout-fixedheader",
      changedFixedSidebar: "修改固定侧边栏区域设置，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；如启用，则自动增加样式名：vsui-style-layout-fixedsidebar",
      changedFixedBody: "修改固定主题区域设置，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；如启用，则自动增加样式名：vsui-style-layout-fixedbody",
     
      changedLogoAuto: "",
      changeSidebarIconColorful: "修改侧边栏图标为彩色，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；",
      changeShowFooter: "修改显示全局页脚，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；如启用，则自动增加样式名：vsui-style-layout-showfooter",
      changeColorWeakMode: "修改色弱模式，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；如启用，则自动增加样式名：",
      changeMultiPageMode: "修改多页面模式，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值})；无论是否启用，页面都不含影响此模式的样式名称",
      changeTabsStyle: "修改（多页面模式时）选项卡的样式，函数签名：({oldValue:旧样式名,newValue:新样式名})；",
      changeMenuIconBGColorMode: "更改菜单栏按钮背景色为彩色/默认时触发，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值}),true为启用彩色模式,false为单色模式；",
      changeLogoAreaAutoWidth: "更改左上角Logo区域宽度布局模式时触发，函数签名：({oldValue:旧状态布尔值,newValue:新状态布尔值}),true为自动宽度,false为固定宽度；只在导航为vsui-style-layout-nav-mode-header模式时有效",
      changeLoadingAnimation: "更改页面加载动画时触发，函数签名：({oldValue:旧模式整数值,newValue:新模式整数值}),0:不使用；1:页面顶部NProgress实现；2:页面遮罩，ElementPlus v-loading实现",

      changeSideBarMainMenuDefaultWidth: "当修改侧边栏主菜单默认状态下的宽度时触发，函数签名：({oldValue:旧值,newValue:新值})",
 
      changeSideBarMainMenuCollapseWidth: "侧边栏在vsui-style-layout-nav-mode-side/vsui-style-layout-nav-mode-mixture状态+主菜单收缩模式，调整主菜单项的宽度时触发，函数签名：({oldValue:旧值,newValue:新值})",
      changeSideBarMainMenuDoubleWidth: "侧边栏在vsui-style-layout-nav-mode-side/vsui-style-layout-nav-mode-mixture状态+双菜单栏模式下，调整主菜单项的宽度时触发，函数签名：({oldValue:旧值,newValue:新值})",
      changeSideBarMainMenuDoubleHeight: "侧边栏在vsui-style-layout-nav-mode-side/vsui-style-layout-nav-mode-mixture状态+双菜单栏模式，调整主菜单项的高度时触发，函数签名：({oldValue:旧值,newValue:新值})",
      changeSideBarMainMenuDoubleIconSize: "侧边栏在vsui-style-layout-nav-mode-side/vsui-style-layout-nav-mode-mixture状态+双菜单栏模式，调整主菜单项的图标大小时触发，函数签名：({oldValue:旧值,newValue:新值})",
      changeSideBarMainMenuItemHeight: "当修改侧边栏主菜单菜单项高度时触发，函数签名：({oldValue:旧值,newValue:新值})",
      changeSideBarMainMenuItemIconTextMargin: "当修改侧边栏主菜单菜单项图标与文字间距时触发，函数签名：({oldValue:旧值,newValue:新值})",
      changeSideBarMainMenuItemIconSize: "当修改侧边栏主菜单菜单项图标大小时触发，函数签名：({oldValue:旧值,newValue:新值})",
      changeSideBarMainMenuItemFontSize: "当修改侧边栏主菜单菜单项文字大小时触发，函数签名：({oldValue:旧值,newValue:新值})",

      changeHeaderHeight: "当修改头部高度时触发，函数签名：({oldValue:旧值,newValue:新值})",
      changeHeaderMenuItemPadding: "当修改头部菜单的菜单项间距时触发，函数签名：({oldValue:旧值,newValue:新值})",
 
      haSubModulesWhenChangeToDoubleSidebar: "当界面修改为双菜单模式时，当前页面的显示存在双菜单并会显示时触发本事件,函数签名(当前页面的父模块对象)",
      changedDoubleSidebarData: "当界面为双菜单模式时，父模块选中状态改变时，会导致子菜单数据变更，函数签名(父模块对象，子菜单的数据列表)",
      closeVsuiSetting: "当关闭VSUI框架设置对话框时触发，函数签名：(配置信息的JSON对象)；",
 }
```


#### 3.3.2 <a id="core-events-eventBus">事件通知</a>

事件会以两种方式传出，一种是以原生window.CustomEvent自定义事件的方式传出，订阅事件代码如下：

```
	//这是某个js文件
	import {eventDefine} from "@vsui/vue-multiplex";
	
	1：通过浏览器自定义事件机制，适用于原生js：
	window.addEventListener(eventDefine.事件定义,function(eventArgs){
		let args=eventArgs.detail;//这里是事件参数
		///something
	});
	
 
```

，一种是借助全局事件总线eventBus在vue组件中使用，使用代码如下：
```
	//这是某个vue文件
	import { vue, eventBus, eventDefine } from "@vsui/vue-multiplex";
	export default {
		setup(){
			vue.onMounted(()=>{
				eventBus.on(eventDefine.事件定义,(eventArgObj)=>{
					console.log("接收到事件");
					let args1=eventArgObj[xxx];//这里是事件参数1
					let args2=eventArgObj[xxx];//这里是事件参数2
					//...
					///something
				 })
			});
		}
	}

```

两种事件通知机制的使用，可兼顾原生js文件与当前框架使用的vue技术栈。

#### 3.4：<a id="core-vsuiapi">vsuiapi</a>

API是UI框架提供的接口，因为组件化，不同于直接看到代码修改代码，因此特别提供的API接口函数，API接口基于界面组件，如多页面模式的添加选项卡接口，必须是在多页面模式下才有此接口，否则调用后会抛出"页面中不存在相应此事件的组件header,此调用无效"的错误

##### 3.4.1：<a id="core-vsuiapi-headerapi">头部API</a>
	
	openSetting：打开vsui.vue的界面设置抽屉框
	
	closeSetting：关闭vsui.vue的界面设置抽屉框
	
	
调用API的代码示例如下：
```
import{vsuiapi} from '@vsui/vue-multiplex';

export default {
    name:"dashboard",

    setup(){

        const click1 = ()=>{
            vsuiapi.header.openSetting();
        }
        const click2 = ()=>{
            vsuiapi.header.closeSetting();
        }

        return {
            click1,
            click2,
        }
    }
}

```
	
##### 3.4.2：<a id="core-vsuiapi-contentapi">工作区API</a>

因工作区有单页面模式与多页面模式，因此将从通用、单页面、多页面三个方面加以介绍

* 通用API：
	
		/**
		 * 刷新当前工作区的api，不区分单页面还是多页面，两者共用
		 */
		refreshCurrent()
		
调用通用API的代码示例如下：
```
import{vsuiapi} from '@vsui/vue-multiplex';

export default {
    name:"dashboard",

    setup(){

        const refresh = ()=>{
            vsuiapi.content.refreshCurrent();
        }
		return{
			refresh,
		}
	}
}
```
	
* 多页面模式API：
	
```
	
		

		/**
         * 添加一个新的tab页面,因此API用于用户自定义操作，该API只做增加选项卡的动作，不做跳转、激活等动作，如有其他动作，请结合其他API进行组合使用
         * @param {string} title 页面标题
         * @param {string} name 页面唯一字符串，用户自定义唯一识别符，如希望使用路由地址对应的组件，请使用addTabByRoutePath
         * @param {vue} componentInfo 组件对象
         * @param {object} componentProps 组件默认参数
         * @param {object} data 扩展数据，通过框架UI菜单操作默认传入的是菜单模块信息
         */
         addTabByCustomName : (title,name,componentInfo,componentProps,data) => warnLog_m(),
        
        /**
         * 添加一个新的tab页面,因添加的选项卡与路由定义由匹配关系，所以会影响title和面包屑，不做跳转、激活等动作，如有其他动作，请结合其他API进行组合使用
         * @param {string} title 页面标题
         * @param {string} routePath 路由地址(必须是路由中存在的地址)，如希望使用非路由中的组件，请使用addTabByCustomName
         * @param {object} componentProps 组件默认参数(此参数为undefined的情况下，会使用路由定义中的默认参数)
         * @param {object} data 扩展数据，通过框架UI菜单操作默认传入的是菜单模块信息
         */
         addTabByRoutePath : (title,routePath,componentProps,data) => warnLog_m(),
		
	    /**
         * 获取当前选中项的信息
         * @returns {index:索引,tabItem:调用addTab时传入的信息}
         */
        getCurrentTabInfo()
		
	    /**
         * 通过索引设置当前选中项
         * @param {*} index 
         */
        setCurrentTabByIndex(index)

        /**
        * 通过名称（默认是路由地址,如果是自己创建的tab，则传入自定义的名称）设置当前选中项
        * @param {*} path 通过框架UI操作默认是路由地址,如果是自己创建的tab，则传入自定义的名称
        */
        setCurrentTabByPath(path)

	    /**
         * 通过索引关闭指定的tab
         * @param {*} index 
         */
        closeTabByIndex(index)

        /**
         * 通过名称（默认是路由地址,如果是自己创建的tab，则传入自定义的名称）关闭指定的tab
         * @param {*} path 通过框架UI操作默认是路由地址,如果是自己创建的tab，则传入自定义的名称
         */
        closeTabByPath(path)

        /**
         * 获取tab的数量
         */
        getTabsLength:()
		
```
调用多页面模式API的代码示例如下：
```
import{vsuiapi} from '@vsui/vue-multiplex';

export default {
    name:"dashboard",

    setup(){

        const openNewTab1 = ()=>{
			let path="/vsui-sdk/vsui-api/content/example?type=props1";
            vsuiapi.content.multipage.addTabByRoutePath("你好",path,{name:"world2!"},{});
            vsuiapi.content.multipage.setCurrentTabByPath(path);
        }
		return {
            openNewTab1,
		}
	}
}
```



* 单页面模式API：
```
暂无API
```

## 4：<a id="config">组件外置配置</a>

运行时配置为编译后的项目提供了可配置与调整的能力。

运行时配置信息，具体内容请复制如下内容项目文件中，并使前端程序在运行时能够加载此文件内容。

前端模板工程对本组件的配置进行了扩展，并提供项目完整配置文件：static\js\runtime\config.js，无需创建。

```




     {

        __multiplex_style__:
        {
            /**
             * 是否收缩侧边栏，boolean
             * 如启用，则自动增加样式名：vsui-style-layout-sidebar-collapse
             * 
             */
            sideBarCollapse:false,
            /**
             * 侧边栏样式名称
             * 取值：
             * vsui-style-theme-sidebar-dark
             * vsui-style-theme-sidebar-light
             */
            sideBarClassName: 'vsui-style-theme-sidebar-dark',

            /**
             * 顶栏设置
             * 取值：
             * vsui-style-theme-header-dark
             * vsui-style-theme-header-light
             * vsui-style-theme-header-color
             * 
             */
            headerBarClassName: 'vsui-style-theme-header-dark',

            /**
             * 界面风格名称
             */
            controlClassName: 'vsui-style-theme-color-2',

            /**
             * 当界面风格名称为自定义：vsui-style-theme-color-custom 时，由用户指定的颜色
             */
            controlCustomColor:'#000',
            /**
             * 黑暗模式
             */
            darkMode: false,

            /**
             * 导航模式
             * 取值：
             * vsui-style-layout-nav-mode-side:左侧菜单布局（所有级别,向下展开）
             * vsui-style-layout-nav-mode-header:顶部菜单布局（所有级别，向下展开）
             * vsui-style-layout-nav-mode-mixture:混合菜单布局（顶部为一级，测边为二级及以下）
             */
            navigationMode: 'vsui-style-layout-nav-mode-mixture',

            /**
             * 侧栏双排菜单模式
             * 如启用，则自动增加样式名：vsui-style-layout-doublesidebar
             */
            doubleSidebar: false,

            /**
             * 内容区域铺满模式
             * 如启用，则自动增加样式名：vsui-style-layout-bodybespread
             */
            bodyBespread: false,

            /**
                * 是否固定顶栏区域
                * 如启用，则自动增加样式名：vsui-style-layout-fixedheader
                */
            fixedHeader: false,

            /**
                * 是否固定侧边栏区域
                * 如启用，则自动增加样式名：vsui-style-layout-fixedsidebar
                */
            fixedSidebar: false,

            /**
                * 是否固定主体区域
                * 如启用，则自动增加样式名：vsui-style-layout-fixedbody
                */
            fixedBody: false,

            /**
                * logo宽度自动
                */
            logoAuto: false,

            /**
                * 侧栏彩色图标
                */
            sidebarIconColorful: false,

            /**
                * 显示页脚，则自动增加样式名：vsui-style-layout-showfooter
                */
            showFooter: false,

            /**
                * 色弱模式
                */
            colorWeakMode: false,

            /**
                * 使用多页面模式
                */
            multiPageMode: true,

            /**
                 * 多标签页样式
                 */
            tabsStyle: '',

            /**
             * 启用菜单栏图标背景的彩色模式
             */
            menuIconBGColorMode: false,

            /**
             * logo区域宽度自动
             */
            logoAreaAutoWidth:true,

            /**
             * 加载动画
             */
            loadingAnimation:1,

            detail:{
                sidebar:{
                    sideBarMainMenuDefaultWidth:200,
                    sideBarMainMenuCollapseWidth:50,
                    sideBarMainMenuDoubleWidth:70,
                    sideBarMainMenuDoubleHeight:50,
                    sideBarMainMenuDoubleIconSize:18,
                    sideBarMainMenuItemHeight:50,
                    sideBarMainMenuItemIconTextMargin:4,
                    sideBarMainMenuItemIconSize:14,
                    sideBarMainMenuItemFontSize:14
                },
                header:{
                    headerUserInfoMenus:[
                        /*
						右上角用户下拉菜单内容项
						icon：图标，label：文字，event：点击后的事件名，会通过全局事件触发，请注意接收处理
						/*
						/*{icon:"",label:"修改密码",event:"changePasswd"},*/
                        {icon:"fa fa-user-o",label:"个人信息",event:"showuserinfo"},
                        {icon:"fa fa-cog",label:"主题设置",event:"styleSetting",divided:false},
                        {icon:"fa fa-sign-out",label:"退出登录",event:"logout",divided:true}
                    ],
                    headerHeight:48,
                    headerMenuItemPadding:20,
                }
            }
        },

        __multiplex_config__:{
            show:true,
            /**
             * 第一列配置项
             */
            "10000":{
                show:true
            },
            /**
             * 第二列配置
             */
            "20000":{
                show:false
            },
            "30000":{
                show:false
            },
            "40000":{
                show:false
            },
        },

        __multiplex_animate__:{
			/**工作区切入时的动画样式*/
            router_view_enter_active_class:"",
			/**工作区切出时的动画样式*/
            router_view_leave_active_class:""
        },
    
    
        /***************************************配置项均在此配置项之上，配置文件结束标记，以下配置项请勿删除***********************************************/
        ______finished______:""
    }
```


你也可以在项目中抽离配置信息放入本文件中，例如项目中提取一个配置项
```
	customConfig:{
		a:1,
		b:'123',
		c:{
			c1:"s",
		}
	}
```
您可以在配置中增加相应代码，如下：
```
window.__vsui_runtime_config__ = {
	...其他配置项
	
	/*******************************************************自定义配置项请添加到下方****************************************************************/
	__customConfig__:{
		a:1,
		b:'123',
		c:{
			c1:"s",
		}
	},
	/***************************************配置项均在此配置项之上，配置文件结束标记，以下配置项请勿删除***********************************************/
	______finished______:""
}
```
<font size="2px" color="#666">

tips 1：*配置内容您可以部分或者整体保存入数据库，通过接口返回来实现不同用户、机构、岗位配置不同；*

tips 2：*配置文件的文件名修改后会导致云平台的多云切换功能不可用。*

</font>



## 5：<a id="secdev">二次开发</a>

### 5.1：<a id="secdev-events">订阅框架事件来进行自定义处理</a>

在<a href="#core-events">3.3：组件内核事件</a>中已经对事件定义与使用进行了介绍，此处不再复述，请查看文档相应章节

### 5.2：<a id="secdev-vsuiapi">通过调用框架API函数</a>

在<a href="#core-vsuiapi">3.4：vsuiapi</a>中已经对API接口进行了介绍，此处不再复述，请查看文档相应章节

### 5.3：<a id="secdev-uiexpend">对界面UI进行扩展</a>

自@vsui/vue-multiplex@2.1.0-rc3 版本就对UI进行了扩展坞定义，开发者可以根据自己的需要对扩展坞进行自定义

扩展坞位置为红框所示如下：

[![查看框架核心组件项目模板](
data:image/jpeg;base64,/9j/4QAYRXhpZgAASUkqAAgAAAAAAAAAAAAAAP/sABFEdWNreQABAAQAAAAeAAD/4QMvaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLwA8P3hwYWNrZXQgYmVnaW49Iu+7vyIgaWQ9Ilc1TTBNcENlaGlIenJlU3pOVGN6a2M5ZCI/PiA8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIiB4OnhtcHRrPSJBZG9iZSBYTVAgQ29yZSA1LjYtYzE0NSA3OS4xNjM0OTksIDIwMTgvMDgvMTMtMTY6NDA6MjIgICAgICAgICI+IDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+IDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSIiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDQyAyMDE5IChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpFOEE2ODBCNUMxRTExMUVDODlEMUI4M0JFRThBOUM3QiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDpFOEE2ODBCNkMxRTExMUVDODlEMUI4M0JFRThBOUM3QiI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOkU4QTY4MEIzQzFFMTExRUM4OUQxQjgzQkVFOEE5QzdCIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOkU4QTY4MEI0QzFFMTExRUM4OUQxQjgzQkVFOEE5QzdCIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+/+4ADkFkb2JlAGTAAAAAAf/bAIQAEAsLCwwLEAwMEBcPDQ8XGxQQEBQbHxcXFxcXHx4XGhoaGhceHiMlJyUjHi8vMzMvL0BAQEBAQEBAQEBAQEBAQAERDw8RExEVEhIVFBEUERQaFBYWFBomGhocGhomMCMeHh4eIzArLicnJy4rNTUwMDU1QEA/QEBAQEBAQEBAQEBA/8AAEQgAnwPoAwEiAAIRAQMRAf/EALIAAQACAwEAAAAAAAAAAAAAAAADBQIEBgEBAQEBAQEBAQAAAAAAAAAAAAABAgMEBQYQAAEDAgIGBgYIAwYGAgMAAAEAAgMRBCESMUFRkRMFYdEiklQVcTJS0lMUgaGxQqIzkxZyI9PBgnMkVQbh8WKyQzWzNEQlVhEAAgECAwUEBwcEAgMAAAAAAAECEQMhElIxQVGRFGHRIhPwcYGhMpIEweFi0lOTJLFCIxVygjNjBf/aAAwDAQACEQMRAD8A6awt7u/gN1JfzxufJKMkeQNaGPcwAVYdQWx5Vcf6ldb4/wCmnIf/AFw/xZ//AJXqxJoCaVpqC5xinFN1xXFnqv3pxvXIxyJRnKKWSOxP1FDPBefMGzsr25nuGAOlc58bYog7RnIiJqdQCwdBzS0IdzG8nEDiG/MQPYWsJNBxGviqB0j6VhY39yyN87LywiN08zvjme7iMLqDI7tt9UCmhSz31zcQyQSX/LMkrSx1HurRwof/ACLng1XxV3Y4Hsy3IyUHG04qilLJ4q/3NeGnqN7yq4/1K63x/wBNPKrj/UrrfH/TWk3mNyLCxDJAwnPE+RoDxJwewHtLhodSqydzS6Fsx5lGY5ySA0OcGyZNDsNf04rqoxaTxx7WeG5duwnKDcaxk4/BHd7Db8quP9Sut8f9NPKrj/UrrfH/AE1G/mMrGQufIC97fVYAaPqcvEpXKwg4nVRZP5hPHM5jcsjMwyOINH9lnYjy/eNSVcke3mzPUXPw/JHuMvKrj/UrrfH/AE08quP9Sut8f9NR+a3GdzS2NgwOZ2akVXPbSTp7Or/nFNeyukDjI+PMW8aMPa3gMLQc2Uirq7NOtMke3mOoucY/JHuNnyq4/wBSut8f9NPKrj/UrrfH/TWpdcxvmz5GTNZFxMoeGVNK4evhSmlyvBiKpkj6NjqLnGPyR7iptjcRT8ws5Z33DIoo3sdJlzAyNfmFWgeyuYaTlHoXUN/9pzX/AAIf+2Vcs31R6F9D/wCZ8Nz/AJfazd7460Srbtt0VMXBNntTtTN0qS2BNzEAK1e3Cgdr2OwP0roXwxfKSl8UTX5X0/lsBpjl0DTRey7dVtpUrmOVcTmqlKleDQF6upRXpSpVta20jYbZzbSOeGcEzuOLsdBc8/ltCq5S0yvLAAzM7KG4gCuFOhYjNSbSWzt9hEzGpSpRFsor0pm11W7ysMMspdK2Atjq2V4Dg0VGY0d0Ka64TbljuNHK51s+kwDWtc414ZoKgFc3cpJxpu9NxCsqdqV6VvZLB4c+R7c4a2oY7JmkAGbLqo70LNg5c10bC5hDHOeH1OHajysdWtcK/wBieZ+GXIVK6pSpW6YuX6S4B1f5ga8kNb2u1GfvHRgsuFyxobmkDicocWvNMXUc6noTzFplyBoVO1KlT3fCrEIsgpE0PDDmAdV1e1rKgW06qtKFFSmbpRXPL5rBlpC2Z1sJA52cSNq+napmWbk8irlcsaURCmzdKZulWlu+0z3R/ll5kJiIAoG0PqZtNdiza+yzy8QxtIzOjzNGYYUb6taYLLu4tZHgKlRU7UzdK2pDWR7mcDIXEiuT+1TRywshDHOOcQyuNOGaGoyY5dPsrTngsK1BX1KVKItlFTtTN0qS3LRcRF9AwPaXF2LaVxzdCteYTWD7SdsL7YyEtycNtH/d9Vc5TyyjHK3m3rcQps3SlelWAmtczS0MBn7cjuyDCaBlG5gW6auovQ+xIDSWZ4oqB4FGy5nY1GpzaV9BTzHpYqV2bpSp2qxMtjOJonlsYc/K2YgB1AXvDhl+7obuqtW9fFJcF8IDYy1lGt0Dsio9O1WM23RxaBBUpUoi2UZulKnarzlt3ydtiDcxME9uO1maC6QnW3bX6loWz7Sssk7A2J7yWA45SDVjQAa/xaqLkrrrJOElkdPX6iVNLN0pm6VvMexhuPWaA1zgAYyA5xFMlQ76FsWz7T5aM0Bf2jISGmQ1LsCN2KsrlFXLXHcKlTm6UqVaxyWTYe06LjCjT2QCRiTU6MSqoYCisJ5q4NU4hCpSpRZROayVj3t4jGuBcz2gDiFspjm6Ur0q75pdcodYkWsbDNcAFpa0B0dNbth6FFIbAWzcrIxKI8CCA7MATTRrXGN5tJu3KNXSj7CVKnN0pXpW+51vka17iWuJFMwAc1uXQctRXGlVJdfImKUxhomDMSA0CpdhSn3qfUteZilleLBWVKVKIuhRUpUoiAVKVKIgFSlSiIBUpUoiAVKVKIgFSlSiIBUpUoiAVKVKIgFSlSiIBUpUoiAVKVKIgFSlSiIBUpUoiAVKVKIgFSlSiIBUpUoiAVKVKIgMJnOETiCQdv0r2O2MkjI2vfme4NFXHSTRYz/ku+j7VtWn/wByD/FZ/wBwXmnCMrtzMs2SzBqva59x3U5QsxcXlbuXK07FGhZftKXxZ/F7yftKXxh/F7y6GcuEZLSQdVFAXyUc0ucDUUrsqvmZ3wj8qOPU3tbKX9pS+MP4veT9pS+MP4veV9A+QsOGY1ridFVlKZclfVGGg1OlM74R5IdTe1s5/wDaUvjD+L3k/aUvjD+L3lfOMxmDM1ARjl+3oUcz5mvcGlxFNPo9ATO/w/Kh1N7Wyl/aUvjD+L3k/aUvjD+L3ld8SXE5jQY/ToGpeZ5uL6xy5qU1U27UzvhH5UOpva2Uv7Sl8Yfxe8n7Sl8Yfxe8roSTVHrY4A/8KJFPJmrJUAYGow0famd8I/Kh1N7Wyl/aUvjD+L3k/aUvjD+L3leufMIi3EP0tP8A0k6/QvbZzy52ZxOApX60zvhH5UOpva2cjzTk8nLQ0undIJA4tIJFC3oWsxxLGknEgVV9/uz1IPRJ9gVBFQsjDvVOUO9GFV7/AKTbF7M1tt07JU2HVzlOynN5mrlK+w3rbll5cRcdobHDqkldkBxpgoruzurN4ZcNy5vVcDVrvQVfc3MUr4OXcJ8sGUyyshbmIaBki1jDNj/dUV1OLr/bvEuMLiIhjw7BwmY7Ia9JWLf1k5XUnFZZSyrjiebNic/UpUrZtLQXDXuc/hhuAdhQGhd2hsWyeX22Vgc57CXlmYluZ1X5Gu2ZaYr2yuRTo9pqpW1KVK3vk7bgh5e6hGfDLnxDRlOrSdi1JWCKd8dc7WOLa6MwBorGalsKRucQ0kHEAlRsZI5jXGV9XAHVr+hTTCIMIjJdgSXOFNWinRtUTafLjMaDIKnoouNxKV1KVaK1KVMzjjXsO1ttW21SruRjXKpOlHxHDf8AFf8AV1Jw3/Ff9XUvXtj4dDg0aCNXoWaqs226NPYnhck9od2aSaa2tY24rYR8N/xX/V1I3O2XIXlwLa47aqRR/wD5A/gP2rM7cYODjmT8yK+JvB+0sLkpqalla8uT+GK2ewkqdqVK2oGtPL7pxAzNdHQkDNidRJqPoWqvQnVtaXT3VOB6CaovBpRUHWch/wDXD/Fn/wDmerJVzuQ8sc5zuG9pcS4hssjW1JqaNa8BeeQct9mX9aX31+djnSSpHBU2/cdLjsTnKee4s8nKnlp0r/2I38quY55n2htzHO8yubPEXua9wGbK5rhgaVWEnK+YzRuhkdZsZIC1z44HZwDgctX0qp/IOW+zL+tL76eQct9mX9aX31nI9K+Z9x1X1EVT/JJtUxdmNcP+xvwxRwxMhjFGRtDGDY1ooF65jHAhzQQdIIqq/wAg5b7Mv60vvp5By32Zf1pffW/Fpjz+487VhuruXcf/AFr85YNYxmDGho6BRegBoAaKAaAFXeQct9mX9aX308g5b7Mv60vvpWfCPzfcMtj9S5+2vzm9HBFE5zo2hrnmriNZXroYnnM5jXHaQCVoeQct9mX9aX308g5b7Mv60vvpWfCPzfcMtj9S5+2vzljQEUIw0UXqrfIOW+zL+tL76eQct9mX9aX30rPhH5vuGWx+pc/bX5yNv/s+af4EP2SrlmkZRjqXZ2/LrOyZL8uwgyj+YXOc8uAGAq8nas/L7Dw0Xcb1L1fSX1ZjJSjXM64e0XbkXOsatKEI4qj8MUtmJxNQlGbAu38vsfDRdxvUsTYWOdo+Xixr9xvUvV/sIaJczGY4uo2pUbV23l9j4aLuN6k8vsfDRdxvUnXw0S5jMcTXAiuB0iuB9KVC7by+x8NF3G9Sx+Qsc5Hy8Wgfcb09CdfDRLmMxxdRtSo2rtvL7Hw0Xcb1J5fY+Gi7jepOvholzGY4ioSoXb+X2Phou43qTy+x8NF3G9SdfDRLmMxxNRtSo2rtvL7Hw0Xcb1LF9hYhv/14tI+43b6E6+GiXMZji6jalRtXbeX2Phou43qTy+x8NF3G9SdfDRLmMxxNRtXrWuecrAXu9loJO4LtfL7Hw0Xcb1L1tjZsOZkEbXbWtAO8KdfHRLmMxxny9x8GTuO6k+XuPgydx3Uu1+Xh9n7VjJBEGEhuzWdqnX/gJmOL+XuD/wCCT9N3Uny9x8CT9N3Uux4MXs/Wete8GP2frPWr134S5jjvl7n4Mncd1J8vcfBk7jupdjwY/Z+s9acGMfd+s9add+H3jMcd8vc/Bk7jupYOa5hyvaWO2OBadxXZ8GL2frPWsXWlo85nwse7a5ocd5VX1y3w5DMcbUJUbV2IsbLM3/LxafYGwqXy+x8NF3G9SP6+GiXMZjiajalRtXbeX2Phou43qTy+x8NF3G9SdfDRLmMxxNRtSo2rtvL7Hw0Xcb1J5fY+Gi7jepOvholzGY4mo2pUbV23l9j4aLuN6k8vsfDRdxvUnXw0S5jMcRUJULt/L7Hw0Xcb1J5fY+Gi7jepOvholzGY4jDoTsnYu38vsfDRdxvUnl9j4aLuN6k/2ENEuYzHEVC9qNq7by+x8NF3G9SeX2Phou43qTr4aJcxmOJqNqVG1dt5fY+Gi7jepPL7Hw0Xcb1J18NEuYzHEVCYdC7fy+x8NF3G9SeX2Phou43qTr4aJcxmOIqEw6F2/l9j4aLuN6k8vsfDRdxvUnXw0S5jMcTUbUqNq7by+x8NF3G9SeX2Phou43qTr4aJcxmOJqNqVG1dt5fY+Gi7jepPL7Hw0Xcb1J18NEuYzHE1G1Kjau28vsfDRdxvUnl9j4aLuN6k6+GiXMZjiajalRtXbeX2Phou43qTy+x8NF3G9SdfDRLmMxxNRtSo2rtvL7Hw0Xcb1J5fY+Gi7jepOvholzGY4mo2pUbV23l9j4aLuN6k8vsfDRdxvUnXw0S5jMcTUbUqNq7by+x8NF3G9SeX2Phou43qTr4aJcxmOJqNqVG1dt5fY+Gi7jepPL7Hw0Xcb1J18NEuYzHE1G1Kjau28vsfDRdxvUnl9j4aLuN6k6+GiXMZjiajalRtXbeX2Phou43qTy+x8NF3G9SdfDRLmMxxNRtSo2rtvL7Hw0Xcb1J5fY+Gi7jepOvholzGY4mo2pUbV23l9j4aLuN6k8vsfDRdxvUnXw0S5jMcTUbUqNq7by+x8NF3G9SeX2Phou43qTr4aJcxmOJqNqVG1dt5fY+Gi7jepPL7Hw0Xcb1J18NEuYzHE1G1Kjau28vsfDRdxvUnl9j4aLuN6k6+GiXMZjiajalRtXbeX2Phou43qTy+x8NF3G9SdfDRLmMxxNRtSo2rtvL7Hw0Xcb1J5fY+Gi7jepOvholzGY4mo2pUbV23l9j4aLuN6k8vsfDRdxvUnXw0S5jMcLORwXY7PtUsU7Ipo5ag8NzXUqMcpqu0PLrAihtoiDqyN6lj5TyvwcH6bepcJfWPzJSjBNThGDUnT4W+H/I6q5bdtQnnWWUpeGj+JJfYV37qsvhv3t95P3VZfDfvb7ysfKeV+Dg/Tb1J5TyvwcH6bepefPH9KPzvuM/x+N3ku8rv3VZfDfvb7yfuqy+G/e33lY+U8r8HB+m3qTynlfg4P029SZ4/pR+d9w/j8bvJd5Xfuqy+G/e33k/dVl8N+9vvKx8p5X4OD9NvUnlPK/Bwfpt6kzx/Sj877h/H43eS7yu/dVl8N+9vvJ+6rL4b97feVj5TyvwcH6bepPKeV+Dg/Tb1Jnj+lH533D+Pxu8l3ld+6rL4b97feT91WXw372+8rHynlfg4P029SeU8r8HB+m3qTPH9KPzvuH8fjd5LvK791WXw372+8n7qsvhv3t95WPlPK/Bwfpt6k8p5X4OD9NvUmeP6UfnfcP4/G7yXec1zvm8HMWs4YyNjDvWIqS4dB6FXRkcNn8I+xdt5TyvwcH6bepZeX2Hhou43qXez9UoSq4USjlSi67XXeWVy2reSGb4s1ZHN2PPJbQUdGyU0DeITlflb6rS7GoFcFr3vMJLxxq1sUZdnMbMA59Mudx1mmC63y+x8NF3G9SeX2Phou43qXRfU2FLOrVJcTlVcDiMOhOz0Lt/L7Hw0Xcb1J5fY+Gi7jepdP9hDRLmXMcR2ehKhdv5fY+Gi7jepPL7Hw0Xcb1J18NEuYzHDvIyO9BUUc8Aja1zx6oBB9C73y+x8NF3G9SeX2Hhou43qXGf1lZqcFl8Li1JZvtR0hdgouM4yfiUllko/1TOD41tSmZtBoCy+Yg9sLuvL7Dw0Xcb1J5fYeGi7jepRfW3Fs8v9t/nK7ll7YXv3F+Q4X5iD2wsWyMfPVhBAZQn6V3nl9h4aLuN6k8vsPDRdxvUj+rlJxz5WoyUvDCjw7czCvWoqWWFysouPimmsezKjiMNK9qNq7by+x8NF3G9SeX2Phou43qXfr4aJczlmOJBFdKLtvL7Hw0Xcb1InXw0S5jMbC5XnH+5r+xvb+3hks2ttmRujEpfnBflrny/xYLqlC+0tJHOdJBG9zwA8uY0lwGitRivmmDSfzXhizzFh4zWPnIDi0Nko0Fp1DMa9rUCsIuZXYZxJGBzCXU7JYSWtkcWtqcQMo7Ss+HHQjI2hAaRQYgaAhjjLcpaMorQUFMcCgKeLm3MnunY+FjeC04tDic/3WmuFTXALY5be3k87o7hha2ji0luXFj8n/NbbbGzY4PZCxrhjmAxJ6dujWpGwQskdK1gEjhQuGmla/agJEREAREQHhqASBU6go4ZXS1JjdGBh2sCTr/5qVEBjJ6jvQsljJ6jvQVjxm7DuQEiwd67fpXnGbsO5YmRpc00NBXUgIuZsmfaObAHF5LcGmhpXHWNSr47a7fC+O4ZISX9mpxILD97EN7RxKt+M3YdycZuw7kBDYRmKN7HNc2jsA6laUGtunFbA9c+gfaVjxm7DuXnFbmJoaUA0elAQXsE8ksMsGD4Q9zcaNLjlAa4bHCvo0rU8vuZIX5m5XGNjAA7tmrGtc3PWlB9qs+M3YdycZuw7kBXS2148ve+tSwRHKc1WNkGNAdLm4ncobezuHRuY9r2Oe/MQ8ucAG5jXNmIx2BW/GbsO5OM3YdyA0+VQSwNkbIHCoZprpDe1Sq3pPV+kfaseM3Ydy8dK0igB0jV0oBcte+3lbH67mODdIxphoVRHZ3AeQ5sjyY8hLqjM40xJGjDDSrjjN2HcnGbsO5AVlpbXEbnuuoi4OBblYcxOI+9gdW1SzW4mi4MMT4i57SS9uYYHTi7Ci3uM3YdycZuw7kBDYQPgZIHU7T3EANDcKmmglTy+ofo+1ecZuw7lhNL/AC3ZWOe7SGigJx6cFd4MJgTDIG+sWkCla1p0YrWFvIbR2bO6ZzcgDnHRhSorRSfMz+Ek70fvJ8zP4STvR+8uyqlg47a/Eu8pqQ29yC4yMdiBlpUa644nrXtvBdNjeJWEVIdjR+GJ0GuK2vmZ/CSd6P3k+Zn8JJ3o/eWnck6/BjT+5bvaDK1ZIxhEgAJocAAMRjg3XVZM43Edn/K+5or/AHv7PrUfzM/hJO9H7yfMz+Ek70fvLDq234MfxLvBsD1m+n+wqdaUc8rpGh1u+NtalxLCNGxpqtnjN2HcuclR7vY6kJEUfGbsO5OM3YdyyCRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJEUfGbsO5OM3YdyAkRR8Zuw7k4zdh3ICRFHxm7DuTjN2HcgJFBLDK51Y5TGHYSDThtb7JWfGbsO5OM3YdyAzAAAA0DBFhxm7DuRAUvnt3T8uP8AF1p57d/Dj/F1qtGgegKKW5ZE/IWuJy5qtFQvpStWYqrjFG6It/Pbv4cf4utPPbv4cf4utVTJmva1wBAeCQDgcNqcdmGBx0YdAP8Aanl2dMRRFr57d/Dj/F1p57d/Dj/F1qqdNlp2HYitDQU3rNrszQ6lK6iitWXgooURZee3fw4/xdaee3fw4/xdarkV8i1oQoix89u/hx/i6089u/hx/i61XInkWtCFEWPnt38OP8XWnnt38OP8XWq5eBwdoNU8m1pQoizbzq6e4MLIwHkNNK6CabVbHSuZi/Nj/ib9q6c6SvL9VCMXHKqVTMs8REXmIEREAREQBERAEREAREQBERAE1YadSIdBoKnUEBzn+4Oc84suWyONmLeV5a2J8VwJJK5gTlZwscNKsrTmHMbkRy/IsFtI3MJGXAe6lKgBuRuNcNKpOex82ZZ3V9eWzZJJY2Na5kzWx2cZkaeE0OFXucQC9+vADAKztLbnDJZjHG2yt7t0hfA6RsrreVza/MQlooQ53rMOvEaSm4Fc7nfMprC4dC90Er7yaC3e9gld2XVETOE/s5GtdUuB6Fucv5nfycxmbPLxIizixW4tnxvcyNoDxHWVwBzEYGtehVJseXW9s9tGh8N7O1rQ8CWYxVYxrYnRSiRxzn7uHoWzyNk9petdcwBnNI4pGutC+GN8gdR4MLY4Y2v9UD18Ma0T09w9PebMfOOYyckvZ4reYzQ/NHjTZYsjWukyZPWzGMClFt2nN5HQyx30braSC0juHzscJiWyBzc4a1ukFpNKKoltb+eI8nkLre8uHSPEVtO5wiguJHSSSXYa1rOy12Vor2itvlXyfL5Z5L2e5ZzOygcbmOWVz2SwRAlssQODmGpI9kmhTd6bge3XMOcx2bnOuoPlzFmi5hbxGRzxQl02QSgNphhQ1xW3Z8xvntdNeSwR2QiNbos4JZKcuQlr5XgtdXoxwVLdWtzdvtY3iH5y7sOHwIw1pidI50lXR8KXIxodi7s4rc5URZycxc+ITyxQxMNkwNMz3xl4/LEUXZJcKOoR0pxHCnYS/OSf/wBNafow/wBVbdnPJPHNBDzm3u7twDonsjjPCAPaJjZIc1VX3Yhb5VY3UsM/NvnoZLiFgbmYCHvc0MArw2A0BOpWtlHdW9/PbSx8SBwMtrdhrW0a40dbyZQMWn1drfQgNW9dzWwhE1zzaIBxyRsbZ55JHnQyNjZaucVJy675jBbmfn0kcQnlZHasDMr25yQ0TZC9oc7DXQbVDe39ld845Vb2s7J54LmV00cZzOja2GRji8D1aE0xVlzSxHMOXXFkTlMzCI3ezI3tRu+hwBTdUb6HtxzGxtZHxXM7YnxxG4kDq9mIOyZidHrYDWVHHzjl0kJm4jmRiRkP82OSM55DRgyvaD2q6VWQWkl1y+/vuc2cr5r7hsks48ZWRQANbkxGOfNJpULoudXXLJ2ujnnitbi3nsW3LWx3crIXNfK14FBhTsk4lAXt1e28TpbYziG5bA+4BLS7JG3smUjQQ12pYtvYIOWR31zcCSERMkkugwtDw4D+ZkbUgGtaalUTi95hzK5u2WU8MD+Vz28RmaGOfK59cmStQdldK3+XsLuXWvKbu2lDHWDGzyOA4YOQRPhJrXP9Cen9Rv8ATsNu5v7O1dG24lDHSte+MYnMyJvEkdgDg1qG/sxHbSmUcO9c1ts6h/mOkbnYBhhVu1UnLeV8xmhuW37cktvaO5XaOccJG9rNceh/Y3LCP5+4h5Hanl9xC7l08PzckjQGN4UToyWHMczTpqE3+1cmN3MuJed8rhujaST0la4RvOVxjY93qsfKG5GuNdBKg5lzV8M0tvA9kLbZjX3l1I0yCPimkUUcTaF8j9mrDTVVDeVyMbc8svouYysnnkeDbFptpY5ZOIHOeR2SK9qpWzzaGS3v5ZHubHFNPbXltPLXgGW3bw3QTPaDkqAHNcRTcnAcSW25zcxOrcS/NQRyRw3ZfAbWe2fPThOcwktc01FdYrXFbfNnc3t47i9t7mCG3tYzI2GSIvMuRuZwkkzNy1pQZQq+G3k5nc3TG3VrNb3ckE92YHukfG2HKBCygynMWDtVGvs6FlzGaefmksd/YXlzy63cw20NvGHwzOoHGSYl7c1HYNbowrigLu3uHT2UV0Iy18sTZRCTQhzm5shJ3LR5Rf8AMLm75hb37Ion2roQyOIlwaJY+JRz3UzH6FuQ3bppWxm2mia+ETcSRoa1pcacJ2J7Y0kLV5fbzx835vNIwtinfbmF50PDIQ12X0HBOJOB7zC7uze23LLFzIp7hkk0tw9vEEUUVG9llW1c5zqYnBaEvOOYt5dOwkN5haXsdnK+JjTxGvcyj42SHKC5j9BOlSzyXIvbLnjLK4MfAltrm1yg3EQe4OY/hg44txodBWtLbyssb29v2QWsV9dfMSNvAXmGJjWsh/lMwfIclctejFPT39xfT3d542/52eZvs893kbA2WnBtOJVz3Mqe3ly9n01Ug5lzCTlNhcm6fFc3D5Iy2O1bO+RzXPp2AaNytZjRaT+S20HI7a9kt2w80lmhL5ntBcDcSNjo+I9mmQ/l6Aelb83/AOpHK23xt4IYLqQMNu1zY8jopKdh2YtJcdAqEf20HdU0Yecc4kOd13cC3leI7SRvLgTMctXYZsMQ6g6FuHmPNLi0tLfl8vzN1emR/wAzI0Wpjjt3sbIzh5ZO0cW1VNy26s4ZOWzSPibw7hz5CDOZWtIlxfG4ZGjEaFsWz7J8fJ33BldA7zBzXW3Fzmswof8AL9uiLd6bg+4tIebc25jdQusbYNZbNkF7A+XIx0he6KNol4bs2XIXYDWFjPzrmU9ty64trUs493wpWiVtHFjpY3RVc3Q7JWqi5X5bHy3l93NeCxdb3FyYJJXBjpIjM/iQv4unMKVriDQ6VpiG0n5fyYXEzwJrl8htDIWNdA6SeUzFgyu9X72oIt3rRHsfqLg3nPLyUusYYoorW4dHJnmxmEYyvY5vCdTE4EHUseacw5tA6xjAbbyT8YztifG8ARhuSklyGt14rZ/2/V/Lzc5S1l5NLcRNIoRFI7+VvYAVo/7hfI+6AgeGP5fZXN3KXMbI3tZWxMcHgt7RYdycPT1l7zUPPb90TXQ3Ty+bO2Cr7GjntDjQ7B2VZPveZngR208J5iLVkk9jO3Kx7nAVljkaR6prUAkU2LnhCyGCKe3k+atIXAwXTYYhDDLI08UXTuE4lgz9ktBGrSFeXVzx2ts4Z4Zom8vmmlu3hjWUlHBhyvFGsBIdWmoIERN5pzU3skbbiNzyxpED7ZwjZi5oeHfMYZjp06Krbur2QCFkvNrbll02NvzVu5sctJCKmhke002LnZLGQ2LrshjbaaBlqyZ2VrM7K/zHO+VFI3anYfxaF0txK118ySTJb8ttY2yXN3IG5J3yDLFG2Rwpkbpca6aBAaovHggu/wBy2hGkjhQio/VV6x7JGNkjcHxvAcxzTUOB0EFc7YxSTWF5ecrEcjm3880LMrTHdRA0MYcWnB33XDWuhhcHwxvDDEHNDuE4BrmVFcrgNBCbuXvBmiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgOWGgegKOW2jlfncXA0y4GgorRnJrxzGuBZRwBGJ1j0L3yS92x7z7q+lK5ZeEpRZuqKtsLWsaxpIDAQDWpodKcFmYEYU1bupWnkl7tj3n3U8kvdse8+6nmWdURVFXJAyUgvrhgFkxoY0NGgYBWXkl7tj3n3U8kvdse8+6iuWU21KNWKor0Vh5Je7Y9591PJL3bHvPuq+db1xFUV6Kw8kvdse8+6nkl7tj3n3U863riKor0Vh5Je7Y9591PJL3bHvPup51rXEVRoxfmx/xN+1dOdJVO3k12xzXkso0hxoToBrsVudK8v1U4yccrTonsMsIiLzECIiAIiIAiIgCIiAIiIAiIgC8JoCQK0FaL1eEAggioIoR0IDSm5hDw5hJAZWxBpeyrHh1aaq6jtCls71t2wvDHRkOLaOxqQKmhC8hhtpBnHEHEGMbnHQ05dGjBZM+St20a5rRHVxJOI0ip3UWE3tqqHeStZXGMJZ67f6+8xluIILgBsOZ49aRjWgsz46dOOtSG5tCWlz2kipYSKkbaGmCi/wAjdTZzR0jKEY9l7QA4O2ECqOPLXDM4soS6SpqASRRzvp/5K1e5x7CZIUScLlaeKi3krriGNzzhWrWuI06/W6G0K9dc2wa5zntysqCTvIULHctbRzHNAFW0qT62mtca/wDFeEcrpjw6ACICppQClBu0pV8YjJHTd+UlN1ZtHF4jBxKdoDEgCorQVwG1DdQU4rSHuwaKCjqHSMRXDTRY8bl76MzxuykxgVrQ6C1RmXloHzDWh5pmNKh1DVwOV1MTRG3ucQrcdF37Owzbd2hPG7Ic5oLXFvbLSAe12at061nHfWrwAZA19G5mHS0vGDfTqWoJeXuY2VkdA0EZHnKOyQKupm2KRjrJ7yxlu55JaXZRVowGV/rDRv1qZnxRt2oJPwXVywoeR8zhMzmthy1aXF2GY01dnpUkd9xWyOjZ+UAX5ntpTGtMmfYte1fbXF3lEEjZGh4Mjn5sNGNCtq5+Vt4XF4LQ4ZcrCQ59NWCJyarVFnC2pKCtyzSSwrXf2Mgl5nNE1hfbEF7cxBeBlFcK1CzbzSMy8JzHNfmy4luwGuJG1SSS2JzF0jQT/LLq0OFcBv0rB09lG5jGsLqgOYWgEdvs/eI2JV6iJQa/8Mk8dlft4EMXNzMJMkDi5gqBm9bGmFQsm83HDzvgeHHQwEEnfRSk8ukLbclpq2jRiBlYaUzDpUTWcrDCwBrQ7tCHORmDTlDgKqVlqWw1Szj/AIbixVNuzmRy88bGW0hzA5TXNt0/d1LaZfiQPLInlsebO6rcoLdVa66LSgk5fexZTbvY2J2VsYdUnulbIubMQUexzRKRxI3+sA5xbmdpwqEi203mTrsLct21SMbM1JOjxrt9TMYecMlZK/hHLEASWua71jQDUpTzKPIC2N7nPGDOzjjlI9basC3lkGd7XDM0B7gHGprVzHUH1LMCxODn4tGIc8/e0k7TjpVTepGZK1WqtXFGvbwNpmDAA3h1xLBQUP8AdwXq1mXcYkFvE1zhGQxzycMRpBOLlhFzS2mBLA6jSQ7MKUotZlxOPk3MWoum3nsNxFqM5lBICWNdUHLR1BjSurMo381Y0ZhEZBQGrHZq6a0wqaUUzR4lVi63TKzfXha11MwDqEOFRWhGg461ps5pE+7FoI3B5NK1bStK7a6FtTyGGJ0lAQ3E1OUU9JVqqV4GZW5xaUlRyxXbUyc1rhR4DhUGhFcRiDihAJBIBLcWk6QdGC03c0jjjY+ZmQyVytDg6lAD2tmlDzNmYMjic93YLqEADiaKE6VM0eJryLun21VMO03sztqxa1rfVaG0rSgA0mp0bVp+aw1d2HUYC55BaSKY6Aday8yho4mOQBhy6G4mlcO0rmjxDsXV/azZdHG8UexrhWoDmgiv0r0sY45nNaTSlSATQ6vQtN3M2MfGx8MjDIM3aoCBjqqsjzGIXDLcseHPIGbCmOjWopR49hPIuad1fYjb0YDADQF4WtcCHAEOFHAitRsKxmk4TC8tLqUwaKnErTk5sxkfEED340oC36sVXJLaSFqc/hVcaG+MAAMAMABoAXhYxzS1zWlpFC0gUpsoq+XnMcb2MMLiZAHes3Cu1TN5jGZnRFjm5cwDyW5TkBJ11AwUzRe80/p7qVXHCldq3G2ca1xBwIOsLwtaW5C0FlKZSAW09GharOYMcSHMOGWmQ565q9DdFKrCfmscLA8RukFMWj1ga5aUoq5R4hWLrdFF1N4AAANAAGAAwARanz47NIjRwBJJpSv0aUtuYC5kDGwua0g9skEVbqwTMq0qTyblG8uC24o20RFTmEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAZ2/5Ef8AA37FIo7f8iP+Bv2KRV7X6wRT3FvbR8W4lZDGCAXyODG1PS6i8jvLSUyCOeN5h/NDXtOSvtUOC0f9w8sn5py51pBweIXtd/mGl7KNOwa1qWPIrq18zMgtyL1rRGIGZHYAjtl4I1qcQXT7m3YHOfKxoYA55Lh2QdBK8hu7WckQyskLaZg1wNK6Fpnlv+SMTGRsuH4OkABLRXNSpHa+lasHKL5rXtuCyfPwy0vNA3LiQeG1pJ+pAXMc0UwLontkaDQlpDhXZgs1ocrtrm34zZ6GpaGODQ2oaMuAadFAt9AEREAREQGMn5bvQVEpZPy3egqJAEREAREQBERAEREAREQBERAEREATRii8IqCDoIoaYHFARW74XgcMFpaXNyOwIIPawqdaxdDZOc4uDS9+Yuxx1Bx06sqkiiZE1rWuJDRQZjXBYPtLd5c6ha57szi1xFTTKfoOtZphsR0Uo5m1KcVua2mFbKN9W9sl3CcGnMA4imLa6SAtZtxy+RxjMT8rWgguJ7VaZW0rpGagW4LO3AoAQM2agOFeoLBvL7dr8+ZxJbkxLdHdUaeGCOkZ21WsrlaYYkL5rGQte5sjKOcKULc1RWmnWX/WpGeXVIwY5tGlryWlnEqAzHRXHBSCytg4OYMmXU2gBPThinyVvhmq4tINSdYrq0aDRFFquCDnbpRSur2kcR5cGVGWMUzFjnUIBcDortavXRWJq0yUIDQSHmtCezrXrrC3NMlYyHNdUHHs7Dqqs22ls1wcB6pDmjNg120Dp1q0fBEc4bVO6axnsWMqJJH8QZRKCM1HGpFcKYqQXFtHK/LUlwOY5hm7I0NZp1LN1jE6nadgKCtHUGnDO0rN1pE4UxDQHjKKU/mesaUUSl2Fc7XGbrWtWaLW2MOa5hglroDh2Q4PGNCdlFtsMV9EJHxublJaATQ9OLDiCspbOCa3Fs8ExDQKr22tIbVuSEFrKk5a4VdSv2KpNPdT7RO5CUa1n5ifhbdfD66kZ+RjlMRoH5S51XE5Wgjs6cBU4BePj5e97RnYHg17LhmNe1QnTipHWcDnl5zZicwo4jK7DtN2HBets7ZlA1lADWlTpq132tCUfBGc8cHmu1pj6zTfNyx7ZO2WAYvdic7S4EgD0j0rIz8vkYZWMe7iEsk4QJcD62JafsU7LGFrXtBd/MwJ7IOmuGVoXosbegDgZAC4gPo7FwodSlJcIm/MtaruDw8Rpg2bJCwGVrpGh5OcB2Ip6g6OhZRviY4D5aUiEHhyOcKHLUgNxx0LadYwk1FW0aGANygAD+6vTZxGvaeHGoLg6mB6NGGpMr7Cu7BrbJ1XFmlDLy94lkfC+3yDEvcWkjQWto7WtnJy6hLiwZaVDnepmGilcNK9by63aSQZCTrLySK6aHpUgs7cEnKdYAqaNDtIbsrVFF8Iknctt+GV1e3vZrSzWDP5YzkFjSDFUjK06jt7OKn+Wtshko5jXAvcAcuka16+zgfWoIJrjXQT97HXipQzK0tBIroOmmFMKqpPGqRiVxUWSU676srobyxyuka6VwZShdQuaT2OyPQdKxdLYmICaGUGnDDswLuwNbmu2lbkdjDHmALiH4uBocRrGGCwHK7ThmOQOkYTmo81xwx0dCzllSmB182zVutxYrY8ae4gzcuE7JhHJxxlFRX7w7IOOKnkvLZrC3hSSMiGauWoBboqSdKzPL7UkEtJIIINSDVuipFEfYQvY5hc+j65qECuboAp0q0ljShl3LUnGruOmGL/AKGs+4sZrcPkZJwxSrW7QKAkjTgNKmZBbXsTJqy0B0k5XEtw7VPQshy214IhIcWA1HaIP1aa9KmhgjhGWOoaK0bqGY10Ik96RJ3IJf43cUlJ0q8MppkcsZcOZwiZHdl72ioJ0UJ9K8ZJYFxEbJI3OIcQCRmdgGj1sMfQtl9jC6bjVc11a9kgY1rsXrbSNocMz6OwGOj0YfWUo+CL5sKLxXG6L+4r5ZLJgbcPhldIxxaGhwc5n3hrprwSS7sIp43i3eJX5XggjDHpOHTRb8lhbygCUF4FMoJplAFABTUsPLLWrHHNWMBrcRjQ17WHa+lTLLdQ2r1mizO5vXxOlHu2iS5tTE6I5qNBLaktzZfZedK1WTcvnBjnhleIyO3J2ql2Glp0YLeFowE5XvaHVzAEDTpphh9CwHLrcOcauOYhxBIOI0Uwqq1JvcYjO1FPG4ntTTxrvNSUWcXCebebM0UZmo0tynXm016aqYCx4oBjkD3hzjgaDPg4Yfxqe6sLa7c107S7IKNFcKFetsoGta1ooWFpzYZjkwArTQmV1eCK7sHFVlczY1x4+nA1BNbOqX/MvDRmGcinZodRFDXaj/lJLdxfx3NZ2SQQ5w2t7BIUzOVWUZcWtdVwIxcSBU5sAelZOsI3Rujc9xa4lxwj0n+4pSW9IeZaqsspqjW3DlQ1p5rWNwj4Er5A0PzCgeRX1TWikgjtILgujE3ExGXFze0MxoPpWcnLYZHNc58gLRQZS1op6A2iz8vtOIJMgzDTo7WFO1rVo61ouwO5by0zXMYvNTe/aYO5pb8AzMa97QQKAUPaOUa0j5nC+PPkkBNaMAzOwLRXDD7ylFlbCoDaNIDctaDKNWCxFjC1rWtLxkJLaO0VxpsI9Kvi7DFfp6bJ7eO7gPn4M7YyHBznBtCKUzaCcdCxHMrcl1GvoytSW0rQ0wrpR3LbZxzOzFwpR1QDUazQY/SsvL4MznVfV1Se17VK6uhPH2D+P+PYDfwZQ4B5DjRlG+tgTUE+heeYQljXtZI4v9Vob2j9ay+SgNM4LwKZQdVBTUBpXgsIGtDWFzGtblblOI6a0rpxTxdg/wAH4zYBqAaEVFaHA/SvV4BQAVJoKVOk+lerRwCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgEEsQgjBe31G6xsUnGh9tu8KhaBkb/CPsXtBsXpdhVfiZaF7xofbbvCcaH227wqKg2JQbE6damKF7xofbbvCcaH227wqKg2JQbE6damKF7xofbbvCcaH227wqKg2JQbE6damKF7xofbbvCcaH227wqKg2JQbE6damKF7xofbbvCcaH227wqKg2JQbE6damKF2+WIscA9tSDrCxVO0DM30j7VcHSuVy2oNY1qGgiIuZAiIgCIiAIiIAiIgCIiAIiIAh0IvDoQE+VuwLTj5javcBlc1rq5XlooaAn06luVC0Y+Vxx5C2Q1YSQSAdII/tQErb22cSACCASKtPaAGY0WXzVvSpFBhiRhRxytNdhUMPLjBI58c2JAFSxtcAG6qY4L0csiDHRF7nRPOZ7TStRoodQ6NyAlF3ZnL/Mb2zRvSdCwffWwALAJGmtXAtDW0wxLiFHJy4veP5lWPpxy71nhp7IwAC9fyxjv/IXAikjXgOa8UAaHDDRRAev5jaxgcUFji3OG5amhrTRtotiF8c0TZWto1wqA4UP0rWdy1kjGtlle4tGVpqMB9NcelbNvCIIWQh2YMFATQYatCAxOk+koh0n0lEAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQFK31G/wj7FksW+o3+EfYsl9A0anMuN8qeDxM+Zv5Xr0UNubkC7MhmGA4Zk0V/6NKsUWHCrrVg1HCX5cvzPL3AAN0a/RUYKGJ81HF2dhoC0Ak10nCoVilSjj2g0InXBidxswc01o/NgKV1EFbUAcGEPFDp16xXWSpalFVGm8BERaAREQBvrN9I+1XB0qnb6zfSPtVwdK831G2PqIwiIuBAiIgCIiAIiIAiIgCIiAIiIAiLw6EBlw3eyE4bvZClVRb39058ZdKDnLqtLQGijXHT9CAsuG72QnDd7IWjbX00r3DiNDctczsoDatBboO8FZMvZZIZXMeGuaKDPQUcTiRmy1aNR1oDc4bvZCcN3shVz7+4NqKPMMofi5zQSWnNTsaqgVqvG3lzw3ET1c5zcpLW0y/epnyAYY7kBZcN3shOG72Qqy6v7kRwvZJRr6nM3Lm0DKCAX7arxl7dPjq55dNUgRNLWVAcQTUg+r9aAtMj9n1pkfs+tVt1fXMbaQSh0eVpEhbV+IxOOBJOpWlu8vgjeXZi5oJcNZ2oDBE1n0n7UQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQHvylto4TcOhPlLb4TdymXL83/3NfWN5fW8XyeW1bG6PiyOa+r8tc4H8WCuaXF8wdF8pbfCbuT5S2+E3ctQcwnL4eyHNdbid/DaXgkgmgfmAAwwwxXvnEJcRHG+RgaXmRoq3LQ5Xeg5SrmlxfMG18pbfCbuT5S2+E3ctU8zlqQIMuVj3PzPHZyZDqrWrX1UJ5tLle9phLRkpUuFM5p2j6R9imaXF8wWHylt8Ju5PlLb4Tdyg5feS3Qc6RhZgCG5SAAa07Z9b6At1M0uL5gh+UtvhN3J8pbfCbuUyJmlxfMEPylt8Ju5PlLb4TdylJIBIFTqG1RwzcUE5HMAw7YpjrFOhM0uL5gxfa24YSI2ggVBoilk/Ld6Cokbb2uoCIigCIiAIiIAiIgCIiAIiIAiIgCFEOhAZ8XoP1LzO2lMuGzBZcNvTvUbZLZ7srZWucMKBwrtQHpEROYxgk66Cq9LmEgllSNBNMF419u4kNkaS31gHA014pmgyl2cUbXMc2ihoUBlnFa5cduC8JY4ZXMqNhpRYufbNZxHSAMJpmLsK+lYiezIcRM2jPWObAIDMcNvqxga8AAhERABjBA0YBYyTWkTQ58oaHGgObSaVXjrizaaGUEnYa6PQgJA9oAAbQDQBReiQAUDaAaBgsOJbBnEMgDKZsxdQUOvFZhjHAOaag4gg4EIDBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERATrXl5fYTOe+a2ikfIAJHPja4uA0BxIxothEBEba3L2P4bc8QpG6gq0bAvPlbarTwmdhpa3sjBp0gdCmRAQy2ltMCJI2mtcaUONAcRjqWEfL7OJxeyOjiMpJJOBNdZWyiAwjiiiFI2hooBhsbgFmiIAiIgCIiAxk/Ld6ColLJ+W70FRIAiIgCIiAIiIAiIgCIiAIiIAiIgC8OheogJlVxcsuI+Hm4ZDC4kAkE5g4aadK3+3tK97e0oDRhsrmOQukY2QZcoaHANxbld92tDsWQs7jhvjlyy5wGhwOWjG6GmodXpOtbnb2lO3tKA0XcuuTDwS9koDswMnqitcA0Dp0rzyyYxgEswc1xjFQ05f+o1I+hb/b2lO3tKArpuWXUgblc1rg533iQGHLhoFTgvGctuY4uCMGEk5mPyvHaJArSmNcdysu3tKdvaUBX3HLJrg8QiNshDRrwc0UrsPQFYwNcyFjHABzWgEDQF529pXnb2lAeaz6T9qIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiA//2Q==
)](http://**********:30669/vsui-ui-extend/demo)

扩展方案可参考web components与vue3 teleport两种，前者基于原生web标准，后者使用vue3新功能，具体使用请见模板工程.

### 5.4 <a id="secdev-darkMode">黑暗模式的开发</a>

#### 5.4.1：黑暗模式的识别

##### *******：按需获取

一种用于JS代码中，可以通过判断运行时配置信息实现：
```
import {runtimeCfg} from '../../../assets/core';
export default {
    components:{},
    props:{},
    setup() {
        let isDarkMode=vue.ref(runtimeCfg.multiplex_style.darkMode);
        return {
            isDarkMode,
        }
    }
}
```
一种用于css代码中，开启黑暗模式会在body标签中增加独立的class名称"vsui-style-theme-dark"，您可以使用选择器对页面进行独立控制或处理
```html
<body class="vsui-style-theme-dark">
		<div id="app" class="vsui-multiplex">
			<div class="vsui-layout vsui-theme vsui-style-layout-nav-mode-side vsui-style-theme-dark vsui-style-theme-header-light vsui-style-theme-sidebar-dark vsui-style-theme-color-2 vsui-style-layout-sidebar-collapse vsui-style-layout-doublesidebar vsui-style-layout-fixedbody" >
			....
			</div>
		</div>	
</body>
```
您可以使用以下类似代码实现黑暗模式下的某元素样式控制
```css
.vsui-style-theme-dark .vsui-multiplex .vsui-theme ....到您的dom元素
{
	background:var(--element-ui-color-dark-mode-bg1) #这里使用了一个变量来设置背景色，您也可以使用明确的颜色值
}

```

##### *******：实时监听

这种方式通过@vsui/vue-multiplex事件机制实时将变更动作传出，您可以监听changedDarkMode的事件定义，举一种代码实现如下:
```
//这是某个vue文件
import { vue, eventBus, eventDefine } from '../../../assets/core';
export default {
		setup(){
			vue.onMounted(()=>{
				eventBus.on(eventDefine.changedDarkMode,(eventArgObj)=>{
					console.log("接收到事件");
					let args1=eventArgObj[xxx];//这里是事件参数1
					let args2=eventArgObj[xxx];//这里是事件参数2
					//...
					///something
				 })
			});
		}
	}

```
事件使用请见3.3.2

#### 5.4.2：如何开发支持黑暗模式切换的项目

* 推荐使用原生dom编写与布局有关的结构，如左右结构的工作区，上下结构的工作区，此内容不应设置背景色，文字颜色等，否则会影响主题色与黑暗模式的切换效果

* 使用Element-Plus进行内容区元素填充，如工作区中左侧内容为菜单、树、列表等，可使用Element-Plus对应组件填充，

* 框架以element-plus为UI控件库，黑暗模式与主题色切换是对Element-Plus的样式进行特殊适配得来，即这种模式适配是需要单独处理的，而非不劳而获

* 三方组件想要获得此能力，可能需要适配，适配的方案是通过css选择器判断是否是黑暗模式从而开启另一套适配样式来实现


#### 5.4.3：如何自定义黑暗模式的颜色

颜色适配、颜色变量数量、颜色防干扰、颜色反差等诸多因素的平衡是困难的，好在强大的@vsui/vue-multiplex框架核心库考虑到了这一点，提供了简单适配模式与专业适配模式两种模式

##### *******：简单适配模式：

@vsui/vue-multiplex框架核心库为黑暗模式定义了一套简单的配色，涉及背景色、文字颜色、边框颜色等，您可以通过声明一套新的定义覆盖之

```
body.vsui-style-theme-dark {
    color-scheme:dark;
    --element-ui-color-dark-mode-bg0: #000000;
    --element-ui-color-dark-mode-bg1: #141414;
    --element-ui-color-dark-mode-bg3: #1f1f1f;
    --element-ui-color-dark-mode-font0:#ffffff;
    --element-ui-color-dark-mode-font1:rgba(255, 255, 255, .85);;
    --element-ui-color-dark-mode-font2:rgba(255,255,255,.65);
    --element-ui-color-dark-mode-border0:#000000;
    --element-ui-color-dark-mode-border1:#141414;
}
```

##### *******：专业适配模式：

除了简单适配使用到的变量外，@vsui/vue-multiplex框架核心库为黑暗模式维持了一套与Element-plus适配色，涉及诸多细节，乃至每个Element-plus组件的dom控件，您可以通过声明一套新的定义覆盖之
```
body.vsui-style-theme-dark {
    color-scheme:dark;
    --el-color-primary:var(--element-ui-color-main,#409eff);
    --el-color-primary-light-3:var(--element-ui-color-assist-3,#79bbff);
    --el-color-primary-light-5:var(--element-ui-color-assist-5,#a0cfff);
    --el-color-primary-light-7:var(--element-ui-color-assist-7,#c6e2ff);
    --el-color-primary-light-8:var(--element-ui-color-assist-8,#d9ecff);
    --el-color-primary-light-9:var(--element-ui-color-assist-9,#ecf5ff);

    --el-color-primary-dark-2:var(--element-ui-color-main,#337ecc);
    --el-color-success:#67c23a;
    --el-color-success-light-3:#4e8e2f;
    --el-color-success-light-5:#3e6b27;
    --el-color-success-light-7:#2d481f;
    --el-color-success-light-8:#25371c;
    --el-color-success-light-9:#1c2518;
    --el-color-success-dark-2:#85ce61;
    --el-color-warning:#e6a23c;
    --el-color-warning-light-3:#a77730;
    --el-color-warning-light-5:#7d5b28;
    --el-color-warning-light-7:#533f20;
    --el-color-warning-light-8:#3e301c;
    --el-color-warning-light-9:#292218;
    --el-color-warning-dark-2:#ebb563;
    --el-color-danger:#f56c6c;
    --el-color-danger-light-3:#b25252;
    --el-color-danger-light-5:#854040;
    --el-color-danger-light-7:#582e2e;
    --el-color-danger-light-8:#412626;
    --el-color-danger-light-9:#2b1d1d;
    --el-color-danger-dark-2:#f78989;
    --el-color-error:#f56c6c;
    --el-color-error-light-3:#b25252;
    --el-color-error-light-5:#854040;
    --el-color-error-light-7:#582e2e;
    --el-color-error-light-8:#412626;
    --el-color-error-light-9:#2b1d1d;
    --el-color-error-dark-2:#f78989;
    --el-color-info:var(--element-ui-color-main,#909399);
    --el-color-info-light-3:var(--element-ui-color-assist-3,#b1b3b8);
    --el-color-info-light-5:var(--element-ui-color-assist-5,#c8c9cc);
    --el-color-info-light-7:var(--element-ui-color-assist-7,#dedfe0);
    --el-color-info-light-8:var(--element-ui-color-assist-8,#e9e9eb);
    --el-color-info-light-9:var(--element-ui-color-assist-9,#f4f4f5);
    --el-color-info-dark-2:var(--element-ui-color-main,#73767a);
    --el-box-shadow:0px 12px 32px 4px rgba(0, 0, 0, 0.36), 0px 8px 20px rgba(0, 0, 0, 0.72);
    --el-box-shadow-light:0px 0px 12px rgba(0, 0, 0, 0.72);
    --el-box-shadow-lighter:0px 0px 6px rgba(0, 0, 0, 0.72);
    --el-box-shadow-dark:0px 16px 48px 16px rgba(0, 0, 0, 0.72), 0px 12px 32px #000000, 0px 8px 16px -8px #000000;
    --el-bg-color-page:#0a0a0a;
    --el-bg-color:var(--element-ui-color-dark-mode-bg1);
    --el-bg-color-overlay:#1d1e1f;
    --el-text-color-primary:#E5EAF3;
    --el-text-color-regular:#CFD3DC;
    --el-text-color-secondary:var(--element-ui-color-assist-5,#909399);
    --el-text-color-placeholder:#8D9095;
    --el-text-color-disabled:#6C6E72;
    --el-border-color-darker:#636466;
    --el-border-color-dark:#58585B;
    --el-border-color: var(--element-ui-color-assist-5,#dcdfe6);
    --el-border-color-light:var(--element-ui-color-dark-mode-border0,#414243);
    --el-border-color-lighter:#363637;
    --el-border-color-extra-light:#2B2B2C;
    --el-fill-color-darker:#424243;
    --el-fill-color-dark:#39393A;
    --el-fill-color:#303030;
    --el-fill-color-light:#262727;
    --el-fill-color-lighter:#1D1D1D;
    --el-fill-color-extra-light:#191919;
    --el-fill-color-blank:var(--element-ui-color-dark-mode-bg1,transparent);
    --el-mask-color:rgba(0, 0, 0, 0.8);
    --el-mask-color-extra-light:rgba(0, 0, 0, 0.3)
}

```

专业级适配不仅包含以上信息，还包括在Element-plus样式定义文件对每个控件class定义的样式信息，您如果有很强的css能力，可以尝试一下从入门到放弃。

#### 5.4.4：黑暗模式免责声明

注黑暗模式是自@vsui/vue-multiplex@2.1.0-rc5增加的对Element-plus@2.2.2控件库的适配，未特殊说明仍是对Element-plus@2.2.2控件库的适配，如果在项目中使用其他版本的Element-plus可能会因为未做适配而出现问题，项目技术负责人等相关人员应对技术选型、版本区别有一定认识，该行为属于使用者的自主行为，且框架开发者对于这一问题提前尽到了告知义务，因此不承担任何责任。


## 6：<a id="version">版本正式发布</a>

### 6.1：发布历程

本组件历经8次重大迭代升级，在项目中历练，经历诸多大型项目的洗礼，继而发布成为正式版本，如有问题请及时反馈至框架核心库开发人员处。

### 6.2：后续升级说明

后续的升级将会涉及bug修改、功能升级等,如是bug修改,则会增加patch版本号，功能升级则会增加minor版本号，短期内不会调整major版本号。


## @vsui/vue-multiplex正式版发布，共勉