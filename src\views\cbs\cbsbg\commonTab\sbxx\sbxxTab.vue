<template>
  <div style="height: calc(100% - 40px);">
    <el-form
        :model="tableData"
        ref="vForm"
        label-width="0"
        :inline="false"
        size="default"
        style="height: 100%"
        class="lui-page"
        >
<!--                  <div style="color: red">-->
<!--                    {{ BGXX }}-->
<!--                  </div>-->
      <el-table ref="dataTable" :data="[...tableData,...getDelRow()]" height="500px" class="lui-table"
                :style="{width: '100%'}" :border="true" :show-summary="false" size="default" :stripe="false"
                :highlight-current-row="true" :cell-style="{padding: '8px 0 '}" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
        <el-table-column v-for="(item,index) in tableColumn" :key="index"
                         :prop="item.prop" :label="item.label" :align="item.align" :width="item.width"
                         :min-width="item.minWidth"
                         :show-overflow-tooltip="item.showOverflowTooltip">
          <template #default="{row,$index}" v-if="item.slot==='format'">
            {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='change'">
            <div :class="{dataChange : isChangeT(row.SBWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.SBWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.SBWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span>{{ row[item.prop] }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='formatChange'">
            <div :class="{dataChange : isChangeT(row.SBWYBS,item.prop)}">
              <el-tooltip :content="isChangeT(row.SBWYBS,item.prop)?.BGMS" placement="bottom" effect="light"
                          v-if="isChangeT(row.SBWYBS,item.prop)">
                <el-icon style="cursor: pointer">
                  <InfoFilled/>
                </el-icon>
              </el-tooltip>
              <span> {{ row[item.prop] ? row[item.prop].replace(" 00:00:00", "") : '' }}</span>
            </div>
          </template>

          <template #default="{row,$index}" v-else-if="item.slot==='info'">
            <el-tooltip :content="row[item.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
          <template #default="{row,$index}">
            <div v-if="tableRowClassName({row})!=='warning-row'">
              <el-button v-if="editable" class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="viewRow(row, $index)">查看</el-button>
              <el-button v-if="editable" class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
              <el-button v-if="editable" class="lui-table-button" @click="deleteRow(row,$index)">删除</el-button>
              <div v-else>
              </div>
            </div>
            <div v-else>
              已删除
            </div>
          </template>
        </el-table-column>

      </el-table>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        title="设备信息查看"
        v-model="editVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        top="100px"
        width="1200px">
      <sbxxEdit :editData="editData" :editable="false" @close="editVisible = false"/>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        title="信息项选择"
        v-model="chooseVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        top="50px"
        width="1200px">
      <sbxxXz
          :key="editIndex"
          :currentRow="currentRow"
          @updateChooseData="updateChooseData"
          @updateEditData="updateEditData"
          @close="chooseVisible = false"
          :TYXYDM="TYXYDM"
      />
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, ref} from "vue";
import {InfoFilled} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage, ElMessageBox} from "element-plus";
import sbxxXz from "@views/cbs/templateManagement/DataTemplateManagement/sbxx/sbxx_xz.vue";
import sbxxEdit from '@views/cbs/templateManagement/DataTemplateManagement/sbxx/sbxxEdit.vue'
import comFun from "@lib/comFun";

export default defineComponent({
  name: '',
  components: {InfoFilled,sbxxEdit,sbxxXz},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    YWLXDM: {
      type: String,
      defaultData: () => '',
    },
    TYXYDM: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      chooseVisible:false,
      tableData: [],
      tableColumn: [
        {
          label: "专业名称",
          prop: "ZYMC",
          align: "center",
          showOverflowTooltip: true,
          width: 100,
        },
        {
          label: "业务要求",
          prop: "YWYQ",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "录入资料说明",
          prop: "LRZLSM",
          align: "center",
          showOverflowTooltip: true,
          width: 80,
          slot: "info"
        },
        {
          label: "信息项",
          prop: "XXXMC",
          align: "center",
          showOverflowTooltip: true,
          width: 150,
        },
        {
          label: "设备名称",
          prop: "SBMC",
          align: "center",
          minWidth: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "规格型号",
          prop: "GGXH",
          align: "center",
          minWidth: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "生产厂家",
          prop: "SCCJ",
          align: "center",
          minWidth: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
        {
          label: "出厂日期",
          prop: "TCRQ",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'formatChange' : void 0,
        },
        {
          label: "数量",
          prop: "SL",
          align: "center",
          width: 150,
          slot: props.YWLXDM === 'BG' ? 'change' : void 0,
        },
      ],
    })

    watch(() => props.defaultData, val => {
      val?.forEach(x=> {
        const UUID = comFun.newId()
        x.DWSBID = x.DWSBID || UUID;
        x.SBWYBS = x.SBWYBS || UUID;
      })
      state.tableData = val
    },{
      immediate: true
    })

    const currentRow = ref({});
    const refs = ref([]);

    const updateChooseData = (val) => {
      changeData(currentRow.value,val,editIndex.value,false)
    };

    const changeData = (oldRow,newRow,index,visible) => {
      let params={
        newId: oldRow.DWSBID,
        oldId: newRow.SBZSJID,
        cover: true
      }

      axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
        oldRow.SBZSJID=newRow.SBZSJID
        oldRow.SBLX=newRow.SBLX
        oldRow.SBMC=newRow.SBMC
        oldRow.GGXH=newRow.GGXH
        oldRow.SCCJ=newRow.SCCJ
        oldRow.EXTENSION=newRow.EXTENSION
        oldRow.SL=newRow.SL
        oldRow.TCRQ=newRow.TCRQ
        oldRow.SCDXRQ=newRow.SCDXRQ
        oldRow.SBNL=newRow.SBNL
        oldRow.BZSM=newRow.BZSM
        state.chooseVisible = visible;
      })
    }

    watch(() => state.tableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    watch(() => props.resultTableData, () => {
      if (props.YWLXDM === 'BG') {
        initBgInfo()
      }
    }, {deep: true})

    const BGXX = ref([])
    const initBgInfo = () => {
      const getObjValue = (obj,prop) => {
        let propNameList= prop.split('.')
        let value=obj;
        propNameList.forEach(name=>{
          value=value ? value[name] || '' : ''
        })
        return value
      }


      if (state.tableData && props.resultTableData) {
        let res = []
        let BGQBS = props.resultTableData.map(i => i.SBWYBS)
        let BGHBS = state.tableData.map(i => i.SBWYBS)
        let allBS = new Set([...BGQBS, ...BGHBS])
        allBS.forEach(item => {
          if (BGQBS.includes(item) && BGHBS.includes(item)) {
            let BGQXX = props.resultTableData.find(i => i.SBWYBS === item)
            let BGHXX = state.tableData.find(i => i.SBWYBS === item)

            let isBg = false
            let dbsj = []
            let checkProp = ['SBLX', 'SBMC', 'GGXH', 'SCCJ','SL','TCRQ','SCDXRQ','SBNL','BZSM', 'EXTENSION.YXQJZRQ',
              'EXTENSION.JCJL','EXTENSION.JCDW','EXTENSION.PP']
            checkProp.forEach(ii => {
              if (getObjValue(BGQXX,ii) !== getObjValue(BGHXX,ii)) {
                dbsj.push({
                  BGQ: getObjValue(BGQXX,ii),
                  BGH: getObjValue(BGHXX,ii),
                  ZDMC: ii
                })
                isBg = true
              }
            })
            if (isBg) {
              res.push({
                YWLX: 'SBXX',
                BGZT: 'BG',
                WYBS: item,
                BGXQ: dbsj
              })
            }

          } else if (BGQBS.includes(item) && !BGHBS.includes(item)) {
            res.push({
              YWLX: 'SBXX',
              BGZT: 'SC',
              WYBS: item,
            })
          } else if (!BGQBS.includes(item) && BGHBS.includes(item)) {
            res.push({
              YWLX: 'SBXX',
              BGZT: 'XZ',
              WYBS: item,
            })
          }
        })
        BGXX.value = res
      }

    }

    const getDelRow = () => {
      if(props.resultTableData && state.tableData){
        return props.resultTableData.filter(item => !state.tableData.find(ii => ii.SBWYBS === item.SBWYBS))
      }else {
        return []
      }
    }

    const tableRowClassName = ({row,index}) => {
      let info=BGXX.value.find(ii=>ii.WYBS===row.SBWYBS) || {}
      if (info.BGZT==='XZ'){
        return "success-row"
      }else if(info.BGZT==='SC'){
        return "warning-row"
      }

    }

    const isChangeT = (SBWYBS, prop) => {
      let info = BGXX.value.find(item => item.WYBS === SBWYBS && item.BGZT === 'BG')
      if (info) {
        let res = info.BGXQ?.find(item => item.ZDMC === prop)
        if(res){
          res.BGMS = `${res.BGQ} 变更为 ${res.BGH}`
          return res
        }
      }
      return null
    }


    const updateEditData = (row) => {
      state.tableData.forEach((item,index)=>{
        if(item.SBZSJID===row.SBZSJID){
          changeData(item,row,index,true)
        }
      })
    }

    const chooseRow = (row, index) => {
      currentRow.value=row;
      editIndex.value = index;
      state.chooseVisible = true;
    }


    const insertRow = (row, index) => {
      const UUID = comFun.newId();
      state.tableData.splice(index + 1, 0, {
        DWSBID: UUID,
        SBWYBS: UUID,
        ZYMC: row.ZYMC,
        ZYBM: row.ZYBM,
        XXXMC: row.XXXMC,
        MBMXID: row.MBMXID,
        YWYQ: row.YWYQ,
        LRZLSM: row.LRZLSM,
        XXX: null,
        ZZXXMC: null,
        ZZDJ: null,
        YXKSRQ: null,
        YXJSRQ: null,
        FZBM: null,
        FJ: null,
        PP: null
      });
    }

    const editVisible = ref(false);
    const editIndex = ref(0);
    const editData = ref({});
    const editRow = (row, index) => {
      editIndex.value = index;
      editData.value = row;
      editVisible.value = true;
    };

    const viewRow = (row, index) => {
      editIndex.value = index;
      editData.value = row;
      editVisible.value = true;
    }

    const deleteRow = (row, index) => {
      ElMessageBox.confirm('是否删除此条数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.tableData.splice(index, 1)
        ElMessage({
          message: '删除成功!',
          type: 'success'
        })

      }).catch(() => {

      })
    }

    const validateForm = () => {
      return new Promise((resolve, reject) => {
        if(state.tableData.find(item=>(item.SFBT=='1' && !item.SBZSJID))){
          reject({mgs:[{message:'请完善设备信息！'}]})
        }else {
          for(let i=0;i<state.tableData.length;i++){
            let sbsl = state.tableData[i].SBSL;
            let xxxmc = state.tableData[i].XXXMC;
            let realrs = 0;
            for(let k=0;k<state.tableData.length;k++) {
              if(state.tableData[k].SBZSJID && state.tableData[k].MBMXID === state.tableData[i].MBMXID) {
                realrs++;
              }
            }
            if(realrs < sbsl){
              reject({mgs:[{message:xxxmc + '设备数量不满足' + sbsl + '要求'}]});
              return;
            }
          }
          resolve(true)
        }
      })
    };
    onMounted(() => {

    })

    return {
      ...toRefs(state),
      getDelRow,
      tableRowClassName,
      isChangeT,
      chooseRow,
      viewRow,
      insertRow,
      deleteRow,
      editVisible,
      editData,
      editIndex,
      currentRow,
      updateChooseData,
      updateEditData,
      BGXX,
      validateForm
    }
  }

})
</script>

<style scoped>

</style>
