<template>
  <div class="container">

    <div class="out-box-content">
      <el-row class="zhyy-list-searchArea">
        <el-tabs v-model="state.activeName" class="db_tab" @tab-click="handleClick" type="card">
          <el-tab-pane label="待办工作" name="0"></el-tab-pane>
          <el-tab-pane label="已办工作" name="1"></el-tab-pane>
        </el-tabs>
      </el-row>
      <el-row class="zhyy-list-searchArea">
        <ztbsh style="width: 100%;" LX="LD" :activeName="state.activeName" v-model:taskNum="state.ZtbManageLabel"/>
      </el-row>
    </div>
  </div>
</template>
<script setup>
import outerBox from "@src/components/common/outerBox.vue";
import zcsh from "./cbssh/zcsh.vue";
import cbssh from "./cbssh";
import vsAuth from "@lib/vsAuth";

import {
  vue,
  vsuiapi,
  mixin,
  eventDefine,
  eventBus,
  runtimeCfg,
  axios,
} from "@src/assets/core";
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import axiosUtil from "@src/lib/axiosUtil.js";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();
const loading = reactive(true);

import component from "../../component";
const state = reactive({
  isCbs:false,
  MC: "",
  TYPE: "",
  activeName: "0",
  activeChildName: '0',
  RegesterLabelTitle: 0,
  CbsManageLabel: 0,
  ZtbManageLabel: 0,
  ZjManageLabel: 0,
  PjManageLabel: 0,
  XxtxManageLabel: 0,
  HytxManageLabel: 0,
  cpgylManageLabel: 0,

});

onMounted(() => {
  console.log("用户信息",vsAuth.getAuthInfo().permission)
  let roleList=vsAuth.getAuthInfo().permission.roleList;
  if(roleList&&roleList.length>0){
    for(let i=0;i<roleList.length;i++){
      if(roleList[i].roleCode=='SCGL_CBSGL'){
        state.isCbs=true;
        break;
      }
    }
  }
});
const handleClick = () => {};
</script>
<style scoped>
.container{
  height: 100%;
}
.out-box-content{
  height: calc(100% - 55px);
}
.el-tabs{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-tabs__content){
  flex: 1;
}
.el-form-item {
  margin-bottom: unset;
}
:deep(.el-tabs__header) {
  margin-bottom: unset;
}
.zhyy-list-searchArea:last-child {
  /*height: calc(100% - 15px);*/
  margin: 0;
}
:deep(.el-tab-pane) {
  overflow-y: hidden;
}

:deep(.db_tab .el-tabs__item ){
  border-radius: 8px 8px 0 0;
  border-right: 1px solid var(--el-border-color-light);
  border-left: 1px solid var(--el-border-color-light);
  border-top: 1px solid var(--el-border-color-light);
  margin-right: 10px;
}
:deep(.el-tabs--card>.el-tabs__header .el-tabs__nav){
  border-top: none;
  border-right: none;
  border-right: none;
  border-radius: 8px 0 0 0;
}
:deep(.db_tab .el-tabs__item.is-active){
  background: #2A96F9;
  color: white;
}
</style>
