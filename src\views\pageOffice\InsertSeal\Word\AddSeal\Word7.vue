<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Save() {
	pageofficectrl.SaveFilePage = "/InsertSeal/Word/AddSeal/save?savePath=/InsertSeal/Word/AddSeal7/";
	pageofficectrl.WebSave();
}

function AddSealByPos() {
	try {
		//先定位到印章位置,再在印章位置上盖章
		pageofficectrl.zoomseal.LocateSealPosition("Seal1");
		/**
		 *第一个参数，必填项，标识印章名称（当存在重名的印章时，默认选取第一个印章）；
		 *第二个参数，可选项，标识是否保护文档，true：保护文档；false：不保护文档；
		 *第三个参数，可选项，标识盖章指定位置名称，须为英文或数字，不区分大小写
		 */
		var bRet = pageofficectrl.zoomseal.AddSealByName("李志签名", true, "Seal1"); //位置名称不区分大小写
		if (bRet) {
			alert("盖章成功！");
		} else {
			alert("盖章失败！");
		}
	} catch (e) {
	}
}

function OnPageOfficeCtrlInit() {
	pageofficectrl.AddCustomToolButton("保存", "Save()", 1);
	pageofficectrl.AddCustomToolButton("盖章到印章位置", "AddSealByPos()", 2);
}

function openFile() {
	// 发起GET请求到后端Controller的路由
	return request({
		url: '/InsertSeal/Word/AddSeal/Word7',
		method: 'get',
	});
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit, Save, AddSealByPos };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
