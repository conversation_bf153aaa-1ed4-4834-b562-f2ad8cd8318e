<script setup>
import request from '@/utils/request'
import { ref, onMounted } from 'vue'
import { POBrowser } from "js-pageoffice"

const titleText = ref('');

onMounted(async () => {
  try {
    const response = await request({
      url: '/index',
      method: 'get',
    });
    titleText.value = response;
  } catch (error) {
    console.error('Failed to fetch title:', error);
  }
});

function refreshDemo() {
	if (confirm("确认复位所有示例的文档吗？")) {
		refresh().then(response => {
			alert(response);
		});
	}
}
function open_pageoffice(vue_page_url) {
	POBrowser.openWindow(vue_page_url, 'width=1200px;height=800px;');
}

function refresh() {
	return request({
		url: '/InsertSeal/refresh',
		method: 'get',
	})
}

</script>
<template>
	<div class="Word">
		<div class="zz-content mc clearfix pd-28" align="center">
			<div class="demo mc">
				<h2 class="fs-16">
					盖章和(手写)签字专题
				</h2>
			</div>
			<div style="margin: 10px" align="center">
				<p>
					点击
					<a href="/dev-api/loginseal.zz" target="_blank">电子印章简易管理平台</a> 添加、删除印章。管理员:admin 密码:111111
					<span style="font-size:12px;color: red">(为了系统使用印章的安全性，强烈建议修改此登录密码！！)</span>
				</p>
				<p>
					点击右侧的“全部复位”连接，即可重新演示盖章和签字效果： <input type="button" value="全部复位" @click="refreshDemo()" />
				</p>
				<p style="color: red;">
					印章测试用户名：李志，密码：111111或123456
				</p>
			</div>
			<div style="margin: 10px" align="center">
				<h3>
					一、Word盖章（只支持Windows）
				</h3>
				<table style="border-collapse: separate; border-spacing: 20px;border: 1px solid #9CF " align="center"
					width="1200px">
					<tr>
						<th style="border-bottom: 1px solid #9CF ">功能演示</th>
						<th style="border-bottom: 1px solid #9CF ">文件目录</th>
					</tr>
					<tr>
						<td>
							1.常规盖章：
							<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word1')">Word1</a>
						</td>
						<td>
							(Word/AddSeal/Word1)
						</td>
					</tr>
					<tr>
						<td>
							2.无需输入用户名、密码盖章：
							<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word2')">Word2</a>
						</td>
						<td>
							(Word/AddSeal/Word2)
						</td>
					</tr>
					<tr>
					<td>
						3.无需输入用户名、密码，并且不弹出印章选择框盖章：
						<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word3')">Word3</a>
					</td>
					<td>
						(Word/AddSeal/Word3)
					</td>
				</tr>
				<tr>
					<td>
						4.编辑模版 - 在模版中添加盖章位置：
						<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word4')">Word4</a>
					</td>
					<td>
						(Word/AddSeal/Word4)
					</td>
				</tr>
				<tr>
					<td>
						5.常规指定位置盖章，加盖印章到模板中的指定位置：
						<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word5')">Word5</a>
					</td>
					<td>
						(Word/AddSeal/Word5)
					</td>
				</tr>
				<tr>
					<td>
						6.无需输入用户名、密码加盖印章到模板中的指定位置：
						<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word6')">Word6</a>
					</td>
					<td>
						(Word/AddSeal/Word6)
					</td>
				</tr>
				<tr>
					<td>7.无需输入用户名、密码，并且不弹出印章选择框加盖印章到模板中的指定位置：
						<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word7')">Word7</a>
					</td>
					<td>
						(Word/AddSeal/Word7)
					</td>
				</tr>
					<tr>
						<td>
							8.特殊盖章需求实现：盖章后印章不保护文档内容，用户仍可编辑修改，印章不会出现失效字样：
							<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word8')">Word8</a>
						</td>
						<td>
							(Word/AddSeal/Word8)
						</td>
					</tr>
					<tr>
					<td>
						9.加盖骑缝章：
						<a href="#" @click.prevent="open_pageoffice('Word/AddSeal/Word9')">Word9</a>
					</td>
					<td>
						(Word/AddSeal/Word9)
					</td>
				</tr>
				<tr>
					<td>
						删除印章：
						<a href="#" @click.prevent="open_pageoffice('Word/DeleteSeal/Word')">Word</a>
					</td>
					<td>
						(Word/DeleteSeal/Word)
					</td>
				</tr>
				</table>
				<div style="margin: 10px" align="center">
					<h3>二、Word(手写)签字（只支持Windows）</h3>
					<table style="border-collapse: separate; border-spacing: 20px;border: 1px solid #9CF" align="center"
						width="1200px">
						<tr>
							<th style="border-bottom: 1px solid #9CF ">功能演示</th>
							<th style="border-bottom: 1px solid #9CF ">文件目录</th>
						</tr>
						<tr>
							<td>
								1.常规(手写)签字：
								<a href="#" @click.prevent="open_pageoffice('Word/AddSign/Word1')">Word1</a>
							</td>
							<td>
								(Word/AddSign/Word1)
							</td>
						</tr>
						<tr>
							<td>
								2.无需输入用户名密码签字：
								<a href="#" @click.prevent="open_pageoffice('Word/AddSign/Word2')">Word2</a>
							</td>
							<td>
								(Word/AddSign/Word2)
							</td>
						</tr>
						<tr>
						<td>
							3.(手写)签字到文档指定位置：
							<a href="#" @click.prevent="open_pageoffice('Word/AddSign/Word3')">Word3</a>
						</td>
						<td>
							(Word/AddSign/Word3)
						</td>
					</tr>
					<tr>
						<td>
							4.无需输入用户名、密码，(手写)签字到模板中的指定位置（参考一、4示例在模板中添加签字位置）：
							<a href="#" @click.prevent="open_pageoffice('Word/AddSign/Word4')">Word4</a>
						</td>
						<td>
							(Word/AddSign/Word4)
						</td>
					</tr>
						<tr>
							<td>
								5.特殊(手写)签字需求实现：签批后(手写)签字不保护文档内容，用户仍可编辑修改，(手写)签字不会出现失效字样：
								<a href="#" @click.prevent="open_pageoffice('Word/AddSign/Word5')">Word5</a>
							</td>
							<td>
								(Word/AddSign/Word5)
							</td>
						</tr>
					</table>
				</div>
				<div style="margin: 10px" align="center">
					<h3>三、PDF盖章、签字<span style=" color:Red;">（专业版、企业版）</span></h3>
					<table style="border-collapse: separate; border-spacing: 20px;border: 1px solid #9CF" align="center"
						width="1200px">
						<tr>
							<th style="border-bottom: 1px solid #9CF; width:85%;">功能演示</th>
							<th style="border-bottom: 1px solid #9CF">文件目录</th>
						</tr>
						<tr>
							<td>
								1.常规盖章：
								<a href="#" @click.prevent="open_pageoffice('PDF/AddSeal/PDF1')">PDF1</a>
							</td>
							<td>
								(PDF/AddSeal/PDF1)
							</td>
						</tr>
						<tr>
						<td>
							2.无需输入用户名、密码盖章：
							<a href="#" @click.prevent="open_pageoffice('PDF/AddSeal/PDF2')">PDF2</a>
						</td>
						<td>
							(PDF/AddSeal/PDF2)
						</td>
					</tr>
						<tr>
						<td>
							3.加盖骑缝章：
							<a href="#" @click.prevent="open_pageoffice('PDF/AddSeal/PDF3')">PDF3</a>
						</td>
						<td>
							(PDF/AddSeal/PDF3)
						</td>
					</tr>
					<tr>
						<td>
							删除印章：
							<a href="#" @click.prevent="open_pageoffice('PDF/DeleteSeal/PDF')">PDF</a>
						</td>
						<td>
							(PDF/DeleteSeal/PDF)
						</td>
					</tr>
					<tr>
						<td>
							常规签字（只支持Windows）：
							<a href="#" @click.prevent="open_pageoffice('PDF/AddSign/PDF1')">PDF1</a>
						</td>
						<td>
							(PDF/AddSign/PDF1)
						</td>
					</tr>
					</table>
				</div>

			</div>
		</div>
	</div>
</template>
