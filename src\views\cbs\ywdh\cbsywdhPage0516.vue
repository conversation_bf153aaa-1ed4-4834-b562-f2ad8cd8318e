<template>
  <div class="page-context">
    <div class="row-context" v-for="(item,index) in pageInfo" :key="index">
      <div class="title1">
        {{item.title}}
      </div>
      <div class="line"></div>
      <el-row :gutter="0" style="margin-top: 10px">
        <el-col :span="6" :offset="ii%3===0 ? 0 : 3" v-for="(cItem,ii) in item.childCard" :key="ii" class="card-context">
          <div class="card">
            <div class="card-icon">
              <el-icon :size="25" color="#409EFF"><DocumentAdd /></el-icon>
            </div>
            <div>
              <div class="title2" @click="openPage(cItem)">{{cItem.title}}</div>
              <div class="describe">{{cItem.describe}}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, computed, markRaw} from "vue";

import {DocumentAdd} from '@element-plus/icons-vue'
import TabFun from "@lib/tabFun";
import zxzy from "@views/cbs/cbsyj/xzzy";
import cgxxList from "@views/cbs/cgxx/cgxxList.vue"
import {ElMessage} from "element-plus";
import {getCbsyjGetTeamInfoByOrgId, getCbszxCopyAddInfo} from "@src/api/sccbsgl";
import zxzyzx from "@views/cbs/cbsyj/xzzy-zx";
import bgdwzyList from "@views/cbs/cbsbg/bgdwzyxg/bgdwzyList";
// 承包商变更
import cbsbgList from "@views/cbs/cbsbg/cbsdwbg/cbsbgList.vue";
// 队伍变更
import dwbgList from "@views/cbs/cbsbg/cbsdwbg/dwbgList.vue";
import bgdwqtList from "@views/cbs/cbsbg/bgdwqtzy/bgdwqtList";
import vsAuth from "@lib/vsAuth";
import bgcgList from "@views/cbs/cbsbg/bgcgList";
import comFun from "@lib/comFun";
import qybgList from "@views/cbs/cbsbg/cbsqybg/qybgList";
// 承包商档案管理
// import cbsdacxList from "@views/cbs/queryanalysis/cbsda/cbsdacxList.vue";
// 队伍专业删除
// import dwzyscList from "@views/cbs/cbsbg/cbsdwbg/dwzyscList.vue";

export default defineComponent({
  name: '',
  components: {DocumentAdd},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      DWXX:{
      },
      pageInfo: [
        {
          title: '承包商资格申请',
          childCard: [
            {title: '正式投标资格申请', describe: '对承包商实现承包商按照业务专业线上提起准入申请',icon: '',name: 'zxzy',page: markRaw(zxzy),data:{},props: {}},
            {title: 'C类专业投标资格申请', describe: '对承包商实现承包商按照业务专业线上提起准入申请',icon: '',name: 'zxzy',page: markRaw(zxzy),data:{},props: {}},
            // {title: '公开招标登记', describe: '对公开招标中标队伍建立公开招标登记',icon: '',name: 'zxzy',page: markRaw(zxzy),data:{},props: {ZRLX: 'GKZB'}},
            {title: '临时准入登记', describe: '分公司临时准入和单位内临时准入登记',icon: '',name: 'zxzy',page: markRaw(zxzy),data:{},props: {ZRLX: 'LSZR'}},
            // {title: '高端承包商绿色通道', describe: '对省属以上科研院所、高等院校、战略合作企业建立绿色备案流程',icon: '',name: 'zxzy',page: markRaw(zxzy),data:{},props: {ZRLX: 'LSTD'}},
            {title: '准入草稿', describe: '承包商准入相关业务草稿',icon: '',name: 'cgxx',page: markRaw(cgxxList),data:{},props: {}},
            // {title: '承包商档案管理',icon: '',name: 'cbsdagl',page: markRaw(cbsdacxList)}
          ]
        },
        {
          title: '承包商信息变更',
          childCard: [                               
            {title: '承包商业务增顶', describe: '已经准入的承包商，如果需要增加准入业务类别，可通过增项管理进行申请',icon: '',method: 'cbsdwzx'},
            {title: '承包商区域变更', describe: '已经准入的承包商增加或更改服务区域',icon: '',name: 'qybg',page: markRaw(qybgList),data:{},props: {}},
            {title: '企业信息变更', describe: '已经准入石油工程、地面工程、承运商专业承包商的人员、设备信息',icon: '',name: 'cbsxxbg',page: markRaw(cbsbgList)},
            // {title: '队伍信息变更', describe: '已经准入的队伍石油工程、地面工程、承运商专业的人员、设备信息',icon: '',name: 'dwxxbg',page: markRaw(dwbgList)},
            // {title: '队伍专业删除',icon: '',name: 'dwzybg',page: markRaw(dwzyscList)}

          ]
        },
        {
          title: '承包商信息维护',
          childCard: [
            // {title: '企业信息维护', describe: '已经准入的承包商其他信息',icon: ''},
            {title: '队伍信息维护', describe: '已经准入的队伍其他信息',icon: '',name: 'dwxxwh',page: markRaw(bgdwqtList)},
            {title: '变更草稿', describe: '承队伍变更相关业务草稿',icon: '',name: 'bgcgxx',page: markRaw(bgcgList),data:{},props: {}},
          ]
        },

      ]
    })


    const cbsdwzx = () => {
      if (!state.DWXX.DWYWID) {
        ElMessage({
          message: "请先进行正式投标资格申请",
          type: "warning",
        });
        return false
      }
      // if (state.DWXX.ADWLX.includes("DW")) {
        TabFun.addTabByRoutePath("增项-选择队伍", "/contractors/addTeamList", {}, {});
      // } else {
      //   let zybms = state.DWXX.ZYBM.split(',')
      //   let newId = comFun.newId();
      //   getCbszxCopyAddInfo({
      //     dwywidNew: newId,
      //     dwywidResut: state.DWXX.DWYWID,
      //     newType: 'ZX'
      //   }).then(res => {
      //     if (state.DWXX.DWLX === 'CBS') {
      //       TabFun.addTabByCustomName("增项-专业选择", "zxzyzx", zxzyzx, {
      //         uuId: newId,
      //         dwlx: 'QY',
      //         zybms: zybms
      //       }, {});
      //     }
      //   })
      // }
    }


    const instance = getCurrentInstance()
    const openPage = (info) => {
      console.error(info)
      if(info.method && instance.proxy[info.method]){
        instance.proxy[info.method](info)
      }
      if(info.page){
        TabFun.addTabByCustomName(info.title, info.name, info.page, info.props, info.data);
      }
    }

    const getDwInfo = () => {
      getCbsyjGetTeamInfoByOrgId({orgId: vsAuth.getAuthInfo().permission.orgnaId}).then(res => {
        state.DWXX = res.data || {};
      })
    }

    onMounted(() => {
      getDwInfo()
    })

    return {
      ...toRefs(state),
      openPage,
      cbsdwzx,
      getDwInfo

    }
  }

})
</script>

<style scoped>
.page-context {
  background-color: white;
  height: 100%;
  font-family: 黑体;
  padding: 20px 50px;
  font-size: 0.9vw;
}
.row-context{
  margin-bottom: 10px;
}
.title1{
  /*margin-left: calc((100% - 1000px) / 6);*/
  font-size: 1vw;
  font-weight: bold;
}
.line{
  width: 100%;
  border-bottom: 1px solid #cad1da;
  padding-bottom: 10px;
  /*margin: 10px calc((100% - 1000px) / 6) 0;*/
}
.title2{
  display: inline-block;
  color: #409EFF;
  font-weight: bold;
  cursor: pointer;
  border-bottom: 1px solid transparent;

}
.title2:hover{
  border-bottom: 1px solid #409EFF;
}
.describe{
  font-size: 1vw;
  /*font-weight: bold;*/
  margin-top: 10px;
}
.card-context{
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  cursor: pointer;
}


.card{
  display: flex;
  /*align-items: center;*/
  gap: 8px;
  width: 100%;
  padding: 0.5vw;
  border: 1px solid #bec7d5;
  min-height: 5vw;
}
.card:hover{
  border: 1px solid #409EFF;
}
.card-icon{
  display: flex;
  align-items: center;
}
</style>
