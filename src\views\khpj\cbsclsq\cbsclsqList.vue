<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CLDXMC">
            <el-input ref="input45296" placeholder="请输入企业（队伍/人员）名称" v-model="listQuery.CLDXMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CFYY">
            <el-select v-model="listQuery.CFYY" class="full-width-input"
                       placeholder="请选择处罚原因"
                       clearable>
              <el-option v-for="(item, index) in CFYYLXOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CLFS">
            <el-select v-model="listQuery.CLFS" class="full-width-input"
                       placeholder="请选择处理方式"
                       clearable>
              <el-option v-for="(item, index) in CLFSOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="SHZT">
            <el-select v-model="listQuery.SHZT" class="full-width-input"
                       placeholder="请选择审核状态"
                       clearable>
              <el-option v-for="(item, index) in SHZTOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData">
              <el-icon>
                <Plus/>
              </el-icon>
              新建
            </el-button>
          </div>
        </el-col>
      </el-row>
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 280px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="CLDXMC" label="企业（队伍/人员）名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CLZYMC" label="专业名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CFYYLXMC" label="处罚原因类型" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CLFSMC" label="处理方式" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CJSJ" label="提报时间" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CJDWMC" label="提报单位" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CJRXM" label="提报人" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                  <span v-if="scope.row.SHZT==='0'">
                    <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑
                    </el-button>
                    <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.row)">删除
                    </el-button>
                  </span>
                  <span v-else>
                    <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                    </el-button>
                  </span>

                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>


      <el-dialog
          custom-class="lui-dialog"
          :close-on-click-modal="false"
          v-if="dialogVisible"
          v-model="dialogVisible"
          title="承包商处理申请"
          @closed="closeForm"
          z-index="1000"
          top="5vh"
          width="1200px">
        <div>
          <cbsclsqEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
        </div>
      </el-dialog>

    </el-form>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import cbsclsqEdit from "@views/khpj/cbsclsq/cbsclsqEdit";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,cbsclsqEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,
      CFYYLXOptions: [],
      CLFSOptions: [],
      SHZTOptions: [{DMXX: '0',DMMC: '保存'},{DMXX: '1',DMMC: '提交'}],
    })


    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      axiosUtil.get('/backend/sckhpj/cbsclsq/selectCbsclsqPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    // 新增
    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.params = {editable: true, id: row.CBSCLID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.params = {editable: false, id: row.CBSCLID, operation: 'view'}
      state.dialogVisible = true
    }

    const deleteRow = (row) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.del('/backend/sckhpj/cbsclsq/delCbsclsq?CBSCLID=' + row.CBSCLID, null).then(res => {
          ElMessage.success({
            message: '删除成功!'
          });
          getDataList()
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
      })
    }

    onMounted(() => {
      getDataList()
      getDMBData('CFYYLX', 'CFYYLXOptions')
      getDMBData('CLFS', 'CLFSOptions')
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      addData,
      viewRow,
      closeForm,
      deleteRow
    }
  }

})
</script>

<style scoped>

</style>
