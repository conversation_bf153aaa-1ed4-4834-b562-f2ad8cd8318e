<template>
    <div class="content">
        <VuePdfApp :page-scale="'page-fit'"
    :theme="'light'"
    :style="`width: 100%; height: 100vh;`"
    :pdf="pdfSrc" />

    </div>
</template>
<script>
    import {
        defineComponent,
        ref,
        toRefs,
        reactive,
        onMounted,
        getCurrentInstance,
    }
        from 'vue'

    import axiosUtil from "@lib/axiosUtil";
    import VuePdfApp from 'vue3-pdf-app'
    import 'vue3-pdf-app/dist/icons/main.css'
    import vsAuth from "../../lib/vsAuth";
    import {ElMessage} from "element-plus";

    export default defineComponent({
        name: '',
        components: { VuePdfApp },
        props: {
            src:String,
        },
        setup(props, context) {
            const state = reactive({
                pdfSrc:props.src,
            })

            const instance = getCurrentInstance()

            // 清空画板
            const handleReset = () => {
                instance.refs.esign.reset();
            };

            //
            const handleGenerate = () => {
                instance.refs.esign.generate().then(res => {
                    state.imgSrc = res;
                    saveqm();
                }).catch(err => {
                    alert('请签字！') // 画布没有签字时会执行这里 'Not Signned'
                })
            };

            const saveqm = () => {
                let other = {
                    busType: state.busType,
                    busId: state.busId,
                    standbyField0: state.standbyField0,
                }
                let param = {
                    QRR: state.busId,
                    qmbs: state.imgSrc,
                    other: other
                };
                if (state.imgSrc == '' || state.imgSrc == null) {
                    state.$message.error("请先进行签名")
                    return;
                }
                state.disabled = true;
                //先去数据库中查询，看是否写入到了数据库中
                axiosUtil.post('/backend/component/saveqm', param).then((res) => {
                    if (res.meta.message == 'ok') {
                        let param = {
                            PDRYID: state.busId,
                            QZZT: '3'
                        }
                        context.emit("saveQmData", param)
                    }
                })
            }

            onMounted(async () => {
                
            })

            return {
                ...toRefs(state),
                handleReset,
                handleGenerate,
                saveqm,
            }
        }
    })
</script>

<style scoped>
    .content {
        width: 100%;
        height: 100%;
    }

</style>
