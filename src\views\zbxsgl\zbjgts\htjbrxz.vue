<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" v-show="true" prop="USERID">
          <el-input style="width:100%;" placeholder="请输入用户账号" v-model="listQuery.USERID" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" v-show="true" prop="USERNAME">
          <el-input style="width:100%;" placeholder="请输入用户名" v-model="listQuery.USERNAME" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" v-show="true" prop="LONGNAME">
          <el-input style="width:100%;" placeholder="请输入组织机构名称" v-model="listQuery.LONGNAME" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="4" class="grid-cell">
        <div class="static-content-item" v-show="true" style="display: flex;padding-left: 10px;text-align: right;">
          <el-button ref="button91277" @click="getDataList" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
          <el-button ref="button91277" @click="submit" type="success">
            确定
          </el-button>
        </div>

      </el-col>
    </el-row>

    <div class="container-wrapper" v-show="true">
      <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                :border="true" :show-summary="false" size="default" :stripe="false" v-loading="loading"
                @current-change="handleCurrentChange"
                :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
        <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
        <el-table-column v-if="true" prop="USERID" label="用户账号" :fixed="false" align="center"
                         :show-overflow-tooltip="true" width="220"></el-table-column>
        <el-table-column v-if="true" prop="USERNAME" label="用户名" :fixed="false" align="center"
                         :show-overflow-tooltip="true" width="120"></el-table-column>
        <el-table-column v-if="true" prop="LONGNAME" label="组织机构全称" :fixed="false" align="left"
                         :show-overflow-tooltip="true"></el-table-column>
      </el-table>
      <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                     :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                     class="lui-pagination"
                     @size-change="getDataList" @current-change="getDataList" :total="total">
      </el-pagination>
    </div>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "../../../lib/axiosUtil";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import {ElMessage} from "element-plus";

export default defineComponent({
  name: '',
  components: {Search},
  props: {
  },
  setup(props, {emit}) {
    const state = reactive({
      tableData:[],
      listQuery: {
        page: 1,
        size: 10,
      },
      loading: false,
      total: 0,
      checkRow:null,
    })
    const getDataList = () => {
      state.loading=true
      let params={
        ...state.listQuery,
      }
      axiosUtil.get('/backend/xsgl/zbjgts/selectHtjbr', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
        state.loading=false
      });
    }
    const submit = () => {
      if(!state.checkRow){
        ElMessage.error('请选择经办人')
        return
      }
      emit('getRes',state.checkRow)
    }
    const handleCurrentChange = (e) => {
      console.log(e)
      state.checkRow=e
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    onMounted(()=>{
      getDataList()
    })
    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      submit,
      handleCurrentChange
    }
  }

})
</script>

<style scoped>

</style>
