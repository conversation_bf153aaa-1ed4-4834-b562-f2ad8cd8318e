<template>
    <div style="height: calc(100% - 40px);">
        <el-form :model="state" ref="vForm" label-width="0" :inline="false" size="default" style="height: 100%"
            class="lui-page" :disabled="!editable">
            <el-row :gutter="20" style="margin-bottom: 20px" v-if="editable">
                <el-col :span="24">
                    <el-button style="float: right" ref="button9527" type="primary" @click="chooseRow">
                        增加
                    </el-button>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <el-table class="lui-table" :data="tableData" width="100%" size="default" :stripe="true"
                        height="calc(100vh - 320px)" v-loading="tableLoading">
                        <el-table-column type="index" :index="indexMethod" label="序号" align="center" width="50" />

                        <el-table-column prop="ZSMC" label="证书名称" header-align="center" align="center"
                            show-overflow-tooltip />
                        <el-table-column prop="ZSBH" label="证书编号" header-align="center" align="center" />

                        <el-table-column prop="ZSDJ" label="资质等级" header-align="center" align="center" />

                        <el-table-column prop="YXQJS" label="证书到期时间" header-align="center" align="center">
                            <template #default="scope">
                                {{ formatDate(scope.row.YXQJS) }}
                            </template>
                        </el-table-column>

                        <el-table-column label="附件" align="center">
                            <template #fileList="{ row, $index }">
                                <vsfileupload :maxSize="10" @getFileList="getFileList" :index="$index"
                                    :ref="addRefs($index)" :editable="false" :busId="row.ZSYWID" :key="row.ZSYWID"
                                    ywlb="DWZTBGFJ" busType="dwxx" :limit="100"></vsfileupload>
                            </template>
                        </el-table-column>


                        <el-table-column label="操作" header-align="center" align="center" width="150" v-if="editable">
                            <template #default="{ row }">
                                <div class="table-col-btn">
                                    <!-- <el-button class="lui-table-button" @click="chooseRow(row)">选择</el-button> -->
                                    <!-- <el-button class="lui-table-button" @click="insertRow(row)">插入行</el-button> -->
                                    <!-- <el-button class="lui-table-button" @click="setNull(row, $index)">清空</el-button> -->
                                    <el-button v-if="row.SHZT != 1 && row.EXTENSION?.SJLY !== 'MBSC'"
                                        class="lui-table-button" @click="deleteRow(row, $index)">删除</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
            </el-row>
        </el-form>
    </div>
    <el-dialog custom-class="lui-dialog" title="信息项选择" v-model="chooseVisible" :close-on-click-modal="false"
        :close-on-press-escape="false" :destroy-on-close="true" top="50px" width="1200px" @close="() => { }">
        <zzxxXz :key="editIndex" v-if="currentRow" :currentRow="currentRow" @updateChooseData="handleUpdateChooseData"
            @updateEditData="updateEditData" @close="chooseVisible = false" :TYXYDM="TYXYDM" :DWYWID="DWYWID" />
    </el-dialog>
</template>
<script setup>
import {
    reactive,
    computed,
    onMounted,
    ref,
    defineProps,
    nextTick,
    defineEmits,
    getCurrentInstance,
    watch,
} from "vue";
import { axios } from "@src/assets/core/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import zzxxXz from "./zzxx_xz.vue";
import { v4 as uuidv4 } from "uuid";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import axiosUtil from "../../../../../lib/axiosUtil";
import { InfoFilled } from "@element-plus/icons-vue";
import {defineExpose} from 'vue'

// 处理从子组件返回的选择数据
const handleUpdateChooseData = (selectedRows) => {
    // 如果是批量选择，selectedRows 会是数组
    if (Array.isArray(selectedRows)) {
        selectedRows.forEach(row => {
            const UUID = uuidv4().replace(/-/g, "");
            const newRow = {
                ...row,
                CJRZH: row.CJRZH || '',
                TYXYDM: TYXYDM.value,
                ZSYWID: UUID,
                ZSWYBS: UUID,
                EXTENSION: row.EXTENSION || {},
                ZSCYZBS:DWYWID.value,
            };
            tableData.value.push(newRow);
        });
    }
    chooseVisible.value = false;
};

// 日期转化
const formatDate = (dateStr) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

const DWYWID = ref(props.DWYWID)

const refs = ref([]);
const addRefs = (id) => {
    return (el) => {
        refs.value[id] = el;
    };
}

const getFileList = (res) => {
    tableData.value[res.index].fileList = res.fileList;
};

const props = defineProps({
    params: {
        type: Object,
        default: () => ({})
    },
    DWYWID: {
        type: String,
        default: ''
    },
    TYXYDM: {
        type: String,
        default: ''
    },
    editable:{
        type: Boolean,
        default: true
    }
});

const editable = ref(props.editable)
const TYXYDM = ref(props.TYXYDM)

const chooseVisible = ref(false)
const tableData = ref(props.params.ZZXX ||[])
const form = ref({
    index: 0,
    ZSMC: '',
    ZSBH: '',
    ZSDJ: '',
    YXQJS: ''
})
const indexMethod = (index) => {
    return index + 1;
};

const insertRow = (row, index) => {
    const UUID = uuidv4().replace(/-/g, "");
    tableData.value.splice(index + 1, 0, {
        CJRZH: row.CJRZH,
        TYXYDM: TYXYDM.value,
        ZSYWID: UUID,
        ZSWYBS: UUID,
        ZSMC: row.ZSMC,
        ZSBH: row.ZSBH,
        ZSDJ: row.ZSDJ,
        YXQJS: row.YXQJS
    });
};

watch(
    () => props.params.ZZXX,
    (val) => {
        if (!val) return;
        if (val) {
            val.forEach((x) => {
                const UUID = uuidv4().replace(/-/g, "");
                x.ZSYWID = x.ZSYWID || UUID;
                x.ZSWYBS = x.ZSWYBS || UUID;
            });
        }
        tableData.value = val;
    },
    {
        immediate: true,
    }
);

const currentRow = ref({});
const editIndex = ref(0);
const updateChooseData = (val) => {
    changeData(currentRow.value, val, editIndex.value, false)
};
const changeData = (oldRow, newRow, index, visible) => {
    // 如果是批量添加，直接返回
    if (Array.isArray(newRow)) return;

    let params = {
        newId: oldRow.ZSYWID,
        oldId: newRow.ZSZSJID,
        cover: true
    }

    axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
        if (!oldRow.EXTENSION) {
            if (JSON.stringify(oldRow.EXTENSION?.SJLY) === 'MBSC') {
                if (!newRow.EXTENSION) {
                    newRow.EXTENSION = {
                        SJLY: 'MBSC'
                    }
                } else {
                    newRow.EXTENSION.SJLY = 'MBSC'
                }
            }
        }


        oldRow.EXTENSION = newRow.EXTENSION
        oldRow.ZSZSJID = newRow.ZSZSJID
        oldRow.ZSMC = newRow.ZSMC
        oldRow.ZSBH = newRow.ZSBH
        oldRow.ZSDJ = newRow.ZSDJ
        oldRow.YXQJS = newRow.YXQJS
        // refs.value[index].loadFileList()
        chooseVisible.value = visible;
    })
}

const updateEditData = (row) => {
    tableData.value.forEach((item, index) => {
        if (item.ZSZSJID === row.ZSZSJID) {
            changeData(item, row, index, true)
        }
    })
}

const chooseRow = () => {
    // currentRow.value = row;
    // editIndex.value = index;
    chooseVisible.value = true;
};
const deleteRow = (row, index) => {
    ElMessageBox.confirm("是否删除此条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(() => {
            const index = tableData.value.findIndex(item => item.ZSYWID === row.ZSYWID);
            if (index !== -1) {
                tableData.value.splice(index, 1);
            }
            ElMessage({
                message: "删除成功!",
                type: "success",
            });
        })
        .catch(() => { });
};
const vForm = ref(null);
const validateForm = () => {
    return new Promise((resolve, reject) => {
        if (tableData.value.find(item => (item.SFBT == '1' && !item.ZSZSJID))) {
            reject({ mgs: [{ message: '请完成资质信息' }] })
        } else {
            resolve(true)
        }
    })
};
defineExpose({
    form:tableData.value,
    validateForm,
});
</script>
<style scoped>
:deep(.el-table-fixed-column--right) {
    background-color: rgba(255, 255, 255, 1) !important;
}

:deep(.el-form-item) {
    margin-bottom: 0;
}

:deep(.el-form-item__error) {
    top: 50%;
    transform: translateY(-50%);
    left: 40px;
}
</style>
