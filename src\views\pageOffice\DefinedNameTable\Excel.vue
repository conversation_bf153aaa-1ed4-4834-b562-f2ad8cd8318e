<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Save() {
	pageofficectrl.SaveDataPage = "/DefinedNameTable/save";
	pageofficectrl.WebSave();
	//在这里写您保存后的代码，比如判断保存结果pageofficectrl.CustomSaveResult
	alert(pageofficectrl.CustomSaveResult);
}
function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("保存", "Save", 1);
	pageofficectrl.Caption = "简单的给Excel赋值";
}

function openFile() {
	// 发起GET请求到后端Controller的路由
	return request({
		url: '/DefinedNameTable/Excel',
		method: 'get',
	});
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit, Save };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Excel">
		表格的数据是使用后台程序填充进去的
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>