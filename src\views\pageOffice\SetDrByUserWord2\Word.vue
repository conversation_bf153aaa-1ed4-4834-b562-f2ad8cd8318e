<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');
const paramValue =ref('');

function exit() {
	pageofficectrl.CloseWindow();
}
function Save() {
	pageofficectrl.SaveDataPage = "/SetDrByUserWord2/save?userName="+`${paramValue.value}`;
	//在这里写您保存前的代码
	pageofficectrl.WebSave();
	//在这里写您保存后的代码，比如判断保存结果pageofficectrl.CustomSaveResult
}

//全屏/还原
function IsFullScreen() {
	pageofficectrl.FullScreen = !pageofficectrl.FullScreen;
}
function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("保存", "Save", 1);
	pageofficectrl.AddCustomToolButton("全屏/还原", "IsFullScreen", 4);
}
function openFile() {
	// 发起GET请求到后端Controller的路由
	return request({
		url: '/SetDrByUserWord2/Word?userName=' + paramValue.value,
		method: 'get',
	});
}

onMounted(() => {
	// 请求后端打开文件
	//使用pageofficectrl.WindowParams获取获取父页面中POBrowser.openWindow()方法的第三个参数的值,获取到的值为string类型
	paramValue.value = pageofficectrl.WindowParams;
	
	openFile().then(response => {
		poHtmlCode.value = response.pageoffice;
		paramValue.value = response.userName;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit,Save,IsFullScreen,exit };//其中OnPageOfficeCtrlInit必须

})
</script>
<template>
	<div class="Word">
		<div>
			<div class="flow4">
				<input type="button" @click="exit()" value="关闭窗口" />
				<strong>当前用户：</strong>
				<span style="color: Red;">{{ paramValue }}</span>
			</div>
			<div style="height: 800px; width: auto" v-html="poHtmlCode" />
		</div>

	</div>
</template>
