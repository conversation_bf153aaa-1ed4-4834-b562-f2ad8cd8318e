<template>
  <el-form ref="vForm" :rules="rules" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">


      <el-col :span="6" class="grid-cell">
        <el-form-item label="" v-show="true" prop="XMMC">
          <el-input style="width:100%;" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="6" class="grid-cell">
        <el-form-item label="" v-show="true" prop="XMMC">
          <el-select v-model="listQuery.TSZT" class="full-width-input" clearable>
            <el-option v-for="(item, index) in TSZTOptions" :key="index" :label="item.DMMC"
                       :value="item.DMXX" ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="4" class="grid-cell">
        <div class="static-content-item" v-show="true" style="display: flex;padding-left: 10px;text-align: right;">
          <el-button ref="button91277" @click="getDataList" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>

          <el-button ref="button91277" @click="getHtjbr" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            更新合同经办人
          </el-button>
          <el-button ref="button91277" @click="getHtxx" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            更新合同信息
          </el-button>
          <el-button ref="button91277" @click="getOrg" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            更新组织机构
          </el-button>
          <!-- <el-button ref="button91277" @click="getHtWork" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            更新合同环节
          </el-button> -->
        </div>
      </el-col>
    </el-row>

    <el-row ref="grid71868" :gutter="12" v-show="true">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper" v-show="true">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false" v-loading="loading"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
            <el-table-column v-if="true" prop="XMMC" label="项目名称" :fixed="false" align="left"
                             :show-overflow-tooltip="true" min-width="120"></el-table-column>
            <el-table-column v-if="true" prop="XMLB" label="项目专业" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="120"></el-table-column>
            <el-table-column v-if="true" prop="SSDWMC" label="主办单位" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200"></el-table-column>
            <el-table-column v-if="true" prop="DWMC" label="中标单位" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200"></el-table-column>
            <el-table-column v-if="true" prop="ZBJ" label="中标价（万元）" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200"></el-table-column>
            <!-- <el-table-column v-if="true" prop="GSRQZ" label="公示结束日期" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200"></el-table-column> -->
            <el-table-column v-if="true" prop="SHZT" label="推送状态" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200">
              <template #default="{row}">
                <div v-if="row.SHZT==='0'">未推送</div>
                <div v-if="row.SHZT==='2'">已推送</div>
              </template>
            </el-table-column>
            <el-table-column v-if="true" prop="HT_RULESERIALNUM" label="合同编号" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200"></el-table-column>
            <el-table-column v-if="true" prop="HT_CONTRACTNUM" label="合同序号" :fixed="false" align="center"
                             :show-overflow-tooltip="true" width="200"></el-table-column>
            <el-table-column v-if="true" prop="CZ" label="操作" align="center" width="200" fixed="right">
              <template #default="scope">
                <div v-if="scope.row.SHZT==='0'">
<!--                  <el-button size="small" class="lui-table-button" type="primary" @click="delRow()">删除</el-button>-->
                  <el-button size="small" class="lui-table-button" type="primary" @click="pushRow(scope.row)">推送</el-button>
<!--                  <el-button size="small" class="lui-table-button" type="primary" @click="pushRow(scope.row,'KFXT')">推送科服系统</el-button>-->
                </div>
                <div v-else>
                    <el-button size="small" class="lui-table-button" type="primary" @click="back(scope.row)">撤回</el-button>
                </div>
                <!-- <el-button size="small" class="lui-table-button" type="primary" @click="viewData(scope.row)">打印凭证</el-button> -->
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog
        custom-class="lui-dialog"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="合同推送"
        @closed="closeForm"
        top="1vh"
        z-index="1000"
        width="1200px">
      <div>
        <ZbtsEdit v-if="dialogVisible" :params="params" @closeDialog="closeForm"></ZbtsEdit>
      </div>
    </el-dialog>
  </el-form>
</template>

<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
}
  from 'vue'

// 请求方法 get,post,put,del,downloadFile
import vsAuth from "@src/lib/vsAuth";
import {ElMessage, ElMessageBox} from "element-plus";
import comFun from "@src/lib/comFun";
import axiosUtil from "../../../lib/axiosUtil";
import ZbtsEdit from "./zbtsEdit";

import { Search, Upload, Plus} from '@element-plus/icons-vue'

export default defineComponent({
  name: '',
  components: {Search,Plus,Upload,ZbtsEdit},
  props: {},
  setup() {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        TSZT:'',
        page: 1,
        size: 10,
      },
      TSZTOptions:[{DMXX:'0',DMMC:'未推送'},{DMXX:'2',DMMC:'已推送'}],
      tableData: [],
      total: 0,
      rules: {},
      dialogVisible: false,
      params: {},
      processParams: {
        activityId: "new",
        processId: "",
        Processversion: "1"
      },
      isKfdw:false,
      
      loading: false,
      showDialog:false
    })


    const getDataList = () => {
      console.log(state.userInfo)
      state.loading=true
      let params={
        ...state.listQuery
      }
      if(!state.isKfdw){
          params.loginName=state.userInfo.userLoginName;
      }
      axiosUtil.get('/backend/xsgl/zbjgts/selectDtsList', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
        state.loading=false
      });
    }
    const getOrganByUserId = () => {
      let params={
        USER_ID: state.userInfo.userId
      }
      axiosUtil.get('/backend/common/selectOrganByUserId', params).then((res) => {
        if(res.data&&res.data.list>0){
          if(res.data.list[0].ORGNA_TWO_ID=='36650027'){//科服中心
              state.isKfdw=true;
          }
        }
      });
    }
    //获取合同经办人
    const getHtjbr = () => {
      
      axiosUtil.post('/backend/htgl/GetContractOperatorList', {}).then((res) => {
          if(res){
              if(res.meta&&!res.meta.success){
                    ElMessage({
                      message: res.meta.message,
                      customClass: 'myMessageClass',
                      type: 'error',})
              }else{
                getDataList();
                ElMessage({
                      message:  '获取成功！',
                      customClass: 'myMessageClass',
                      type: 'success',})
              }
          }else{
              getDataList();
              ElMessage({
                message: '获取成功！',
                customClass: "myMessageClass",
                type: 'success',
              })
          }
      });
    }

    //获取合同信息
    const getHtxx = () => {
      
      axiosUtil.post('/backend/htgl/GetContractInfoNoSign', {startTime:comFun.addDate(-10),endTime:comFun.getNowDate()}).then((res) => {
          if(res){
              if(res.meta&&!res.meta.success){
                    ElMessage({
                      message: res.meta.message,
                      customClass: 'myMessageClass',
                      type: 'error',})
              }else{
                getDataList();
                ElMessage({
                      message:  '获取成功！',
                      customClass: 'myMessageClass',
                      type: 'success',})
              }
          }else{
              getDataList();
              ElMessage({
                message: '获取成功！',
                customClass: "myMessageClass",
                type: 'success',
              })
          }
          
      });
    }
    //获取组织机构
    const getOrg = () => {
      
      axiosUtil.post('/backend/htgl/GetOrganises', {startTime:comFun.addDate(-10),endTime:comFun.getNowDate()}).then((res) => {
          if(res){
              if(res.meta&&!res.meta.success){
                    ElMessage({
                      message: res.meta.message,
                      customClass: 'myMessageClass',
                      type: 'error',})
              }else{
                getDataList();
                ElMessage({
                      message:  '获取成功！',
                      customClass: 'myMessageClass',
                      type: 'success',})
              }
          }else{
              getDataList();
              ElMessage({
                message: '获取成功！',
                customClass: "myMessageClass",
                type: 'success',
              })
          }
          
      });
    }

    //获取合同环节
    const getHtWork = () => {
      
      axiosUtil.post('/backend/htgl/readHtxxWorkFlowName', {startTime:comFun.addDate(-10),endTime:comFun.getNowDate()}).then((res) => {
          if(res){
              if(res.meta&&!res.meta.success){
                    ElMessage({
                      message: res.meta.message,
                      customClass: 'myMessageClass',
                      type: 'error',})
              }else{
                axiosUtil.post('/backend/gcyztb/zbjgts/updateXmmcByHtmc', {});
                getDataList();
                ElMessage({
                      message:  '获取成功！',
                      customClass: 'myMessageClass',
                      type: 'success',})
              }
          }else{
              getDataList();
              ElMessage({
                message: '获取成功！',
                customClass: "myMessageClass",
                type: 'success',
              })
          }
          
      });
    }

    
    //撤回推送
    const back = (row) => {
      ElMessageBox.confirm(
          '确定撤回该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        axiosUtil.post('/backend/xsgl/zbjgts/backTshtxx', row).then(res => {
          if (res.message === 'success' && res.data=='success') {
              console.log(res);
              getDataList();
                ElMessage({
                  message: '撤回成功！',
                  customClass: "myMessageClass",
                  type: 'success',
                })
          }else{
            var message="";
            if(res.data.indexOf('已生成合同')!=-1){
                  message="已生产合同，请登录合同系统进行相关操作!";
            }else{
                  message= res.data
            }
            ElMessage({
                message: message,
                type: 'error',
              })
          }
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }

    
    const delRow = (XMID) => {
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        axiosUtil.del('/backend/gcyztb/?XMID=' + XMID, {XMID}).then((res) => {
          getDataList()
          ElMessage({
            message: '删除成功',
            customClass: "myMessageClass",
            type: 'success',
          })
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }


    // 推送
    const pushRow = (row) => {
      var glhtid=row.GLHTID;
      if(!glhtid){
          glhtid=comFun.newId();
      }
      state.params = {editable: true, id: glhtid, XSJGMXID: row.XSJGMXID, operation: 'add'}
      state.dialogVisible = true
    }

    //查看
    const viewData = (row) => {
      state.params = {editable: false, id: row.XMID, operation: 'view'}
      state.dialogVisible = true
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }


    onMounted(() => {
      getOrganByUserId();
      getDataList();
    })

    return {
      ...toRefs(state),
      indexMethod,
      getDataList,
      closeForm,
      delRow,
      viewData,
      pushRow,
      getHtjbr,
      getHtxx,
      back,
      getOrganByUserId,
      getHtWork,
      getOrg

    }
  }
})

</script>

<style scoped>
.container {
  padding: 8px 5px;
}
:deep(.lui-card-form .el-form-item__label){
  border-bottom: none;
}
:deep(.el-cascader-menu__wrap.el-scrollbar__wrap) {
  height: 400px !important;
}
</style>
