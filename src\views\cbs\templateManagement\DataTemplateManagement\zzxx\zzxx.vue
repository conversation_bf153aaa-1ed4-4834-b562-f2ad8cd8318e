<template>
  <div style="height: calc(100% - 40px);">
    <el-form
      :model="state"
      ref="vForm"
      label-width="0"
      :inline="false"
      size="default"
      style="height: 100%"
      class="lui-page"
      :disabled="!editable"
    >
      <el-table
          class="lui-table"
        highlight-current-row
        size="default"
        ref="table"
        fit
        height="100%"
        :border="false"
        :data="state.tableData"
      >
        <EleProTableColumn
          v-for="prop in state.tableColumn"
          :col="prop"
          :key="prop.columnKey"
        >
          <template #fileList="{ row, $index }">
            <vsfileupload
                :maxSize="10"
              @getFileList="getFileList"
              :index="$index"
              :ref="addRefs($index)"
              :editable="false"
              :busId="row.ZSYWID"
              :key="row.ZSYWID"
              ywlb="DWZTBGFJ"
              busType="dwxx"
              :limit="100"
            ></vsfileupload>
          </template>
          <template #select="{ row }">
            <el-select
              size="normal"
              v-model="row[prop.prop]"
              clearable
              placeholder="请选择"
              @change="changePro(row)"
            >
              <el-option
                v-for="(item, index) in proDetails"
                :key="index"
                :value="item.ZYBM"
                :label="item.ZYMC"
              ></el-option>
            </el-select>
          </template>
          <template #info="{ row }">
            <el-tooltip :content="row[prop.prop]" placement="bottom" effect="light">
              <el-icon style="cursor: pointer;" :size="18"><InfoFilled/></el-icon>
            </el-tooltip>
          </template>
          <!-- <template #input="{ row }">
                        <el-input v-model="row[prop.prop]" placeholder="请输入"></el-input>
                    </template> -->
          <template #input="{ row, $index }">
            <el-form-item
              v-if="$index > -1"
              :prop="`tableData.${$index}.${prop.prop}`"
              :rules="[
                {
                  required: prop.required,
                  message: `请输入${prop.label}`,
                  trigger: 'blur',
                },
                {
                  max: prop.maxlength,
                  message: `最多输入${prop.maxlength}个字符`,
                  trigger: 'change',
                },
              ]"
            >
              <el-input
                v-model="row[prop.prop]"
                :maxlength="prop.maxlength"
                placeholder="请输入"
                v-tooltip="{
                  newValue: row[prop.prop],
                  oldValue: resultTableData?.find(i => i.ZSWYBS == row.ZSWYBS)?.[prop.prop],
                  label: resultTableData?.find(i => i.ZSWYBS == row.ZSWYBS)?.[prop.prop]
                }"
              ></el-input>
            </el-form-item>
          </template>
          <template #titleinput="{ row }">
            <el-input
              v-if="row.SHZT != 1"
              v-model="row[prop.prop]"
              placeholder="请输入"
            ></el-input>
            <span v-else>{{ row[prop.prop] }}</span>
          </template>
          <template #time="{ row }">
            <el-date-picker
              v-model="row[prop.prop]"
              v-tooltip="{
                newValue: row[prop.prop],
                oldValue: resultTableData?.find(i => i.ZSWYBS == row.ZSWYBS)?.[prop.prop],
                label: resultTableData?.find(i => i.ZSWYBS == row.ZSWYBS)?.[prop.prop],
              }"
              type="date"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            >
            </el-date-picker>
          </template>
          <template #format="{ row }">
            {{ row[prop.prop]?row[prop.prop].replace(" 00:00:00", ""): '' }}
          </template>
          <template #ZYMC="{ row }">
            <el-input v-model="row.ZYMC"></el-input>
          </template>
          <template #opration="{ row, $index }">
            <div>
               <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button>
              <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button>
               <el-button class="lui-table-button" @click="setNull(row, $index)">清空</el-button>
              <el-button v-if="row.SHZT != 1 && row.EXTENSION?.SJLY!=='MBSC'" class="lui-table-button" @click="deleteRow(row, $index)"
                >删除</el-button
              >
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-form>
  </div>
  <el-dialog
      custom-class="lui-dialog"
    title="信息项选择"
    v-model="state.chooseVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    top="50px"
    width="1200px"
    @close="() => {}"
  >
    <zzxxXz
      :key="editIndex"
      :currentRow="currentRow"
      @updateChooseData="updateChooseData"
      @updateEditData="updateEditData"
      @close="state.chooseVisible = false"
      :TYXYDM="TYXYDM"
    />
  </el-dialog>
</template>
<script setup>
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
import { axios } from "@src/assets/core/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import zzxxXz from "./zzxx_xz.vue";
import { v4 as uuidv4 } from "uuid";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import axiosUtil from "../../../../../lib/axiosUtil";
import {InfoFilled} from "@element-plus/icons-vue";

const refs = ref([]);
const addRefs=(id)=> {
  return (el) => {
    refs.value[id] = el;
  };
}

const getFileList = (res) => {
  state.tableData[res.index].fileList = res.fileList;
};

const props = defineProps({
  defaultData: {
    type: Array,
    defaultData: () => [],
  },
  proDetails: {
    type: Array,
    defaultData: () => [],
  },
  // 结果表数据，比对用
  resultTableData: {
    type: Object,
    default: () => null,
  },
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  }
});
const changePro = (row) => {
  row.ZYMC = props.proDetails.filter((x) => x.ZYBM == row.ZYBM)[0].ZYMC;
};
const state = reactive({
  chooseVisible:false,
  tableData: [{}],
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    {
      label: "专业名称",
      prop: "ZYMC",
      align: "center",
      showOverflowTooltip: true,
      width: 100,
      // slot: "select",
    },
    {
      label: "业务要求",
      prop: "YWYQ",
      align: "center",
      showOverflowTooltip: true,
      width: 80,
      slot: "info"
    },
    {
      label: "录入资料说明",
      prop: "LRZLSM",
      align: "center",
      showOverflowTooltip: true,
      width: 80,
      slot: "info"
    },
    {
      label: "信息项",
      prop: "XXXMC",
      align: "center",
      showOverflowTooltip: true,
      width: 150,
    },
    {
      label: "资质信息名称",
      prop: "ZSMC",
      align: "center",
      minWidth: 150,
      // slot: "input",
      required: true,
      maxlength: 128,
    },
    {
      label: "证书编号",
      prop: "ZSBH",
      align: "center",
      minWidth: 150,
      // slot: "input",
      required: true,
      maxlength: 64,
    },
    {
      label: "资质等级",
      prop: "ZSDJ",
      align: "center",
      width: 150,
      // slot: "input",
      maxlength: 40,
    },
    // {
    //   label: "有效开始日期",
    //   prop: "YXQKS",
    //   align: "center",
    //   width: 150,
    //   slot: "time",
    // },
    {
      label: "证书到期日期",
      prop: "YXQJS",
      align: "center",
      width: 150,
      slot: "format",
    },
    // {
    //   label: "发证部门",
    //   prop: "FZBM",
    //   align: "center",
    //   width: 150,
    //   slot: "input",
    //   maxlength: 128,
    // },
    {
      label: "附件",
      prop: "fileList",
      headerAlign: "center",
      align: "left",
      showOverflowTooltip: true,
      width: 150,
      slot: "fileList",
    },
    {
      label: "操作",
      align: "center",
      width: 200,
      fixed: "right",
      slot: "opration",
      hide: !props.editable
    },
  ],
});
watch(
  () => props.defaultData,
  (val) => {
    if (val) {
      val.forEach((x) => {
        const UUID = uuidv4().replace(/-/g, "");
        x.ZSYWID = x.ZSYWID || UUID;
        x.ZSWYBS = x.ZSWYBS || UUID;
      });
    }
    state.tableData = val;
  },
  {
    immediate: true,
  }
);
const copyRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, "");
  state.tableData.splice(index, 0, { ...row, ZSYWID: UUID, ZSWYBS: UUID ,SHZT:''});
};
const insertRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, "");
  state.tableData.splice(index + 1, 0, {
    ZSYWID: UUID,
    ZSWYBS: UUID,
    ZYMC: row.ZYMC,
    ZYBM: row.ZYBM,
    XXXMC: row.XXXMC,
    MBMXID: row.MBMXID,
    YWYQ: row.YWYQ,
    LRZLSM: row.LRZLSM,
    XXX: "",
    ZSMC: "",
    ZSDJ: "",
    YXQKS: null,
    YXQJS: null,
    FZBM: "",
    FJ: "",
  });
};

const setNull = (row, index) => {
    row.ZSZSJID='';
    row.XXX= '';
    row.ZSMC= '';
    row.ZSBH = '';
    row.ZSDJ= '';
    row.YXQKS= '';
    row.YXQJS= '';
    row.FZBM= '';
};

const currentRow = ref({});
const editIndex = ref(0);
const updateChooseData = (val) => {
    if(state.tableData.find(item=>item.ZSZSJID===val.ZSZSJID && currentRow.value.ZYBM===item.ZYBM)){
      ElMessage.error('该信息项已选择，请勿重复选择');
      return;
    }

    changeData(currentRow.value,val,editIndex.value,false);

};
const changeData = (oldRow,newRow,index,visible) => {
  let params={
    newId: oldRow.ZSYWID,
    oldId: newRow.ZSZSJID,
    cover: true
  }

  axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
    if(oldRow.EXTENSION?.SJLY==='MBSC'){
      if(!newRow.EXTENSION){
        newRow.EXTENSION={
          SJLY: 'MBSC'
        }
      }else {
        newRow.EXTENSION.SJLY='MBSC'
      }
    }



    oldRow.EXTENSION=newRow.EXTENSION
    oldRow.ZSZSJID=newRow.ZSZSJID
    oldRow.ZSMC=newRow.ZSMC
    oldRow.ZSBH=newRow.ZSBH
    oldRow.ZSDJ=newRow.ZSDJ
    oldRow.YXQJS=newRow.YXQJS
    refs.value[index].loadFileList()
    state.chooseVisible = visible;
  })
}

const updateEditData = (row) => {
  state.tableData.forEach((item,index)=>{
    if(item.ZSZSJID===row.ZSZSJID){
      changeData(item,row,index,true)
    }
  })
}

const chooseRow = (row, index) => {
  currentRow.value=row;
  editIndex.value = index;
  state.chooseVisible = true;
};
const deleteRow = (row, index) => {
  ElMessageBox.confirm("是否删除此条数据？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      state.tableData.splice(index, 1);
      ElMessage({
        message: "删除成功!",
        type: "success",
      });
    })
    .catch(() => {});
};
const vForm = ref(null);
const validateForm = () => {
  return new Promise((resolve, reject) => {
    if(state.tableData.find(item=>(item.SFBT=='1' && !item.ZSZSJID))){
      reject({mgs:[{message:'请完成资质信息'}]})
    }else {
      resolve(true)
    }
  })
};
defineExpose({
  validateForm,
});
</script>
<style scoped>
:deep(.el-table-fixed-column--right) {
  background-color: rgba(255, 255, 255, 1) !important;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form-item__error) {
  top: 50%;
  transform: translateY(-50%);
  left: 40px;
}
</style>
