'use strict';
require('../check-versions')();
const utils = require('../utils');
const webpack = require('webpack');
const devBuildConfig = require('../dev/dev.conf.js');
const devServerConfigAll = require('../dev/<EMAIL>');
const devServerConfigCustom = require('../dev/<EMAIL>');
const { merge } = require('webpack-merge');
const chalk = require('chalk');
const webpackConfig = require('../dev/webpack.conf');
const FriendlyErrorsPlugin = require('clean-friendly-errors-webpack-plugin');
const HOST = process.env.HOST || devServerConfigCustom.host;
let PORT = (process.env.PORT && Number(process.env.PORT)) || devServerConfigCustom.port;

const SUCCMSG = chalk.green(utils.sign + `

    [框架示例模式，如想运行您基于框架开发的项目请运行：npm run dev]

    最后编译时间：{date}

    应用程序运行在这里: ${chalk.white('http://{host}:{port}{path}')}`);

const devWebpackConfig = merge(webpackConfig, {
   entry: {
        app: ['./examples/main.js']
    },
    plugins:[
        new FriendlyErrorsPlugin({
            compilationSuccessInfo: {
                messages: [SUCCMSG.format({
                    date: (new Date()).format('yyyy-MM-dd hh:mm:ss'),
                    host: HOST,
                    port: PORT,
                    path: '/'
                })]
            },
            onErrors: devBuildConfig.notifyOnErrors
                ? utils.createNotifierCallback()
                : undefined
        })
    ]
});



module.exports = devWebpackConfig;

