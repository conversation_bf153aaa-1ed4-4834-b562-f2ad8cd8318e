<template>
  <div style="height: calc(100% - 40px);">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" style="margin-bottom: 20px" v-if="editable">
        <el-col :span="24">
          <el-button style="float: right" ref="button9527" type="primary" @click="addData">
            增加
          </el-button>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 330px)"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
            <el-table-column prop="XMMC" label="项目名称" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="JSDW" label="建设单位" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="HTMC" label="合同名称" align="center"
                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
            <el-table-column prop="GCFW" label="工程范围" align="center"
                             :show-overflow-tooltip="true" min-width="120"></el-table-column>
            <el-table-column prop="HTRQKS" label="合同日期开始" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="scope">
                {{ scope.row.HTRQKS ?  scope.row.HTRQKS.substring(0,10) : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="HTRQJS" label="合同日期结束" align="center"
                             :show-overflow-tooltip="true" width="120">
              <template #default="scope">
                {{ scope.row.HTRQJS ?  scope.row.HTRQJS.substring(0,10) : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="HTJE" label="合同金额（万元）" align="center"
                             :show-overflow-tooltip="true" width="100"></el-table-column>
            <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
              <template #default="scope">
                  <span v-if="editable && (scope.row.isEdit === 'Y' && !isFirstZr)">
                    <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑
                    </el-button>
                    <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.$index)">删除
                    </el-button>
                  </span>
                <span v-else>
                    <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                    </el-button>
                  </span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-form>


    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="业绩信息"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <cbsyjEdit v-if="dialogVisible" :currentRow="currentRow" @close="closeForm" @submit="getSubmit"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

  import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, ref} from "vue";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import cbsyjEdit from "@views/cbs/templateManagement/DataTemplateManagement/cbsyj/cbsyjEdit";
  import { getCheckIsFirstZr } from "@src/api/sccbsgl.js";


export default defineComponent({
  name: '',
  components: {cbsyjEdit},
  props: {
    defaultData: {
      type: Array,
      defaultData: () => [],
    },
    proDetails: {
      type: Array,
      defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData: {
      type: Object,
      default: () => null,
    },
    // 是否查看模式
    editable: {
      type: Boolean,
      default: true
    },
    TYXYDM: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      tableData: [],
      currentRow: {},
      dialogVisible: false
    })

    watch(() => props.defaultData, (val) => {
      if (val) {
        val.forEach((x) => {
          const UUID = comFun.newId();
          x.CBSYJID = x.CBSYJID || UUID;
          x.CBSYJBS = x.CBSYJBS || UUID;
        });
      }
      state.tableData = val;
    }, {immediate: true,})

    const addData = () => {
      state.currentRow = {editable: true, id: comFun.newId(), operation: 'add',YJWYBS: comFun.newId(), isEdit: 'Y'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.currentRow = {...row,editable: true, id: row.CBSYJID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.currentRow = {...row,editable: false, id: row.CBSYJID, operation: 'view'}
      state.dialogVisible = true
    }

    const deleteRow = (index) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.tableData.splice(index, 1)
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }

    const getSubmit = (value,isContinue) => {
      if(value.operation==='add'){
        state.tableData.push(value)
      }else {
        let row=state.tableData.find(x=>x.CBSYJID===value.CBSYJID)
        Object.assign(row,value)
      }

      if(isContinue){
        state.currentRow={editable: true, id: comFun.newId(), operation: 'add', isEdit: 'Y'};
      }else {
        state.dialogVisible=false
      }
    }

    const closeForm = () => {
      state.dialogVisible=false
    }

    // 判断是否为首次准入
    let isFirstZr = ref(true);
    const checkIsFirstZr = () => {
      getCheckIsFirstZr({
        tyxydm: props.TYXYDM,
      })
              .then(({ data }) => {
                isFirstZr.value = data.rows.length == 0;
              })
              .catch((err) => {
                ElMessage.error("查询失败");
              });
    };

    onMounted(() => {
      // 判断是否为首次准入
      checkIsFirstZr();
    })

    return {
      ...toRefs(state),
      deleteRow,
      addData,
      editRow,
      viewRow,
      closeForm,
      getSubmit

    }
  }

})
</script>

<style scoped>

</style>
