<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'
import { POBrowser } from 'js-pageoffice'

const titleText = ref('');
const count = ref(0);

function updateCount(value) {
	count.value = count.value + parseInt(value);
	document.getElementById("Text1").value = count.value;
	return count.value.toString();
}

function open_pageoffice(vue_page_url) {
	POBrowser.openWindow(vue_page_url, 'width=1200px;height=800px;');
}


onMounted(async () => {
	try {
		const response = await request({
			url: '/index',
			method: 'get',
		});
		titleText.value = response;
	} catch (error) {
		console.error('Failed to fetch title:', error);
	}
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { updateCount };//其中OnPageOfficeCtrlInit必须
});

</script>

<template>
	<div class="Word">
		<a href="#" @click.prevent="open_pageoffice('Word')">POBrowser方式打开Word文档</a><br><br><br>
		<div>Count=<input id="Text1" type="text" value="0" /></div>
		<router-view></router-view>
	</div>
</template>

<style scoped>  
a{  
  display: block;
   margin-top: 30px;
  text-decoration: underline;  
}  
</style>