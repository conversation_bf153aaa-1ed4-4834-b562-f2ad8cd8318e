<template>
    <div>
        <el-form ref="vForm" class="lui-page" label-position="left" label-width="80px"
                 size="default">
            <el-row >
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="单位名称" prop="DWMC">
                        <el-input ref="input45296" v-model="listQuery.DWMC" type="text" clearable style="width: 80%">
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6" class="grid-cell">
                    <el-form-item label="队伍类型" prop="DWLX">
                        <el-select v-model="listQuery.DWLX" style="width:90%;">
                            <el-option v-for="(item, index) in DWLXOptions" :key="index" :label="item.DMMC" :value="item.DMXX"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="6" class="grid-cell">
                    <div class="static-content-item" style="display: flex;">
                        <el-button ref="button91277" @click="getDataList" type="primary">
                            查询
                        </el-button>
                      <el-button type="primary" @click="exportData()">导出</el-button>
                      <el-button @click="closeForm">关闭</el-button>
                    </div>
                </el-col>
            </el-row>

            <el-row ref="grid71868" :gutter="12">
                <el-col :span="24" class="grid-cell">
                    <div class="container-wrapper">
                        <el-table ref="datatable91634" :data="tableData" height="400px"
                                  :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                            <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                            <el-table-column prop="CBSDWQC" label="单位名称" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                            <el-table-column prop="DWMC" label="队伍名称" align="center"
                                             :show-overflow-tooltip="true" min-width="160"></el-table-column>
                          <el-table-column prop="TBLX" label="队伍类型" align="center"
                                           :show-overflow-tooltip="true" min-width="160"></el-table-column>
                        </el-table>

                    </div>
                </el-col>
            </el-row>

        </el-form>

    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
    import axiosUtil from "@lib/axiosUtil";
    import {ElMessage} from "element-plus";
    import comFun from "@lib/comFun";

    export default defineComponent({
        name: '',
        components: {},
        props: {
            params: {
                type: Object,
                required: true
            },
        },
        setup(props, {emit}) {
            const state = reactive({
                listQuery: {
                    DWLX: '',
                },
                tableData: [],
                total: 0,
                checkList: [],

                FMLBOptions: [],
                FMQDXWOptions: [],
                ZRZYTree: [],
                DWLXOptions: [
                    {
                        DMXX: '',
                        DMMC: '全部',
                    },{
                        DMXX: 'ZTTB',
                        DMMC: '暂停投标',
                    },{
                        DMXX: 'GJZLDQ',
                        DMMC: '关键资料将要到期',
                    }
                ]
            });

            const getDataList = () => {
                let params = {
                    ...state.listQuery,
                    zyList: props.params.YWBM_ARR
                };
                axiosUtil.post('/backend/xsgl/xssqgl/queryCbsByZyList', params).then(res => {
                    state.tableData = res.data || [];
                })

            };

            const exportData = ()=>{
                let column = [[
                    { field: 'CBSDWQC', title: '单位名称'},
                    { field: 'DWMC', title: '队伍名称'},
                    { field: 'TBLX', title: '队伍类型'},
                ]];

                let queryParam = {
                    ...state.listQuery,
                    zyList: props.params.YWBM_ARR
                };
                let params = {
                    title: "承包商信息",
                    name: "承包商信息",
                    params: queryParam,
                    url: '/excel/cbsZyxxExport',
                };
                params.column = column;
                axiosUtil.exportExcel('/backend/commonExport/magicExcel', params)
            };


            const closeForm = () => {
                emit('close');
            };

            onMounted(() => {
                getDataList();
            });

            return {
                ...toRefs(state),
                getDataList,
                closeForm,
                exportData,
            }
        }

    })
</script>

<style scoped>

</style>
