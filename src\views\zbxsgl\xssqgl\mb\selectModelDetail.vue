<template>
  <el-container>
    <el-main class="vs-workbench-main-scoped">
      <!--数据表格-->
      <el-row>
        <!-- 表格 -->
        <el-table ref="datatable91634" :data="tableData" height="720px" class="lui-table"
                  :border="true" :show-summary="false" size="default" :stripe="false"
                  :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
          <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
          <el-table-column prop="PJYS" label="评价因素" header-align="center" align="center"></el-table-column>
          <el-table-column prop="PJBZ" label="评价标准" header-align="center" align="center"></el-table-column>
          <el-table-column prop="PJFZ" label="分值" header-align="center" align="center"></el-table-column>
        </el-table>
      </el-row>
    </el-main>
  </el-container>
</template>
<script>
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
} from 'vue'
import axiosUtil from "../../../../lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {},
  props: {
    model: String,
    ZBPJMBBS: String,
    PSLXDM: String,
    ZBSBS: String,
  },
  setup(props, {emit}) {
    const state = reactive({
      model: 'cbszr',
      // 当前页码
      currentPage: 1,
      // 每页的数据条数
      pageSize: 10,
      total: 0,
      //样本类别
      selectmodelTheme: "",
      //弹出框
      dialogFormVisible: false,

      //弹出查看框
      dialogViewFormVisible: false,

      activeIndex: "1",
      //表数据
      tableData: [],
      multipleSelection: [],
      recent: 0,
      form: {},
    })
    /**
     * 序号
     */
    const indexMethod = (index) => {
      return index + state.pageSize * (state.currentPage - 1) + 1;
    }
    /**
     * 页面数据条数改变时
     */
    const handleSizeChange = (val) => {
      state.currentPage = 1;
      state.pageSize = val;
      queryData();
    }
    /**
     * 翻页
     */
    const handleCurrentChange = (val) => {
      state.currentPage = val;
      queryData();
    }
    /**
     * 每页多少条数据
     */
    const handleSelect = (item) => {
      state.activeIndex = item;
      if (item == 2) {
        state.pageSize = 10;
        state.currentPage = 1;
      }
      queryData();
    }
    /**
     * @Params: {{Params}}
     * @Description: 获取数据
     */
    const queryData = () => {
      getMbData();
    }
    const getMbData = () => {
      let param = {
        zbpjmbbs: props.ZBPJMBBS,
      };
      axiosUtil.get('/backend/gcyztb/cgwjgl/selectZbpjmbMx', param).then(res=>{
        state.tableData = res.data
      })
    }
    const handleClear = () => {
      state.storeName = "";
      state.selectmodelTheme = "";
      state.blockName = "";
    }

    //查看
    const viewRow = (row, index) => {
      state.dialogViewFormVisible = true;
      state.id = row.id;

    }

    const handleClose = () => {
      state.dialogFormVisible = false;

      queryData();
    }

    const handleViewClose = () => {
      state.dialogViewFormVisible = false;

    }
    //分页多行变少行，点击翻页不刷新问题
    const pageClick = (e) => {
      if (!state.tableData.length) {
        return false;
      }
      let dom = e.target;
      if (
          dom.className === "btn-next" ||
          (dom.className === "el-icon el-icon-arrow-right" &&
              dom.parentNode.className !== "btn-next disabled")
      ) {
        state.currentPage += 1;
        state.currentPage >= Math.ceil(state.total / state.pageSize)
            ? (state.currentPage = Math.ceil(state.total / state.pageSize))
            : state.currentPage;
      } else if (
          dom.className === "btn-prev" ||
          (dom.className === "el-icon el-icon-arrow-left" &&
              dom.parentNode.className !== "btn-prev disabled")
      ) {
        state.currentPage -= 1;
        state.currentPage <= 1 ? (state.currentPage = 1) : state.currentPage;
      } else if (
          dom.className === "el-icon more btn-quicknext el-icon-d-arrow-right"
      ) {
        state.currentPage = Math.ceil(state.total / state.pageSize);
      } else if (
          dom.className === "el-icon more btn-quickprev el-icon-d-arrow-left"
      ) {
        state.currentPage = 1;
      } else if (dom.className === "number") {
        state.currentPage = Number(dom.innerHTML);
      } else {
        return false;
      }
      queryData();
    }
    onMounted(() => {
      queryData();
    })
    return {
      ...toRefs(state),
      indexMethod
    }
  }
})

</script>
<style scoped>
.dialog-footer {
  text-align: center;
}

.el-dialog__body {
  padding-top: 1px;
}

* >>> .el-form-item__content .el-input__inner .el-textarea__inner {
  width: 98%;
}

</style>
