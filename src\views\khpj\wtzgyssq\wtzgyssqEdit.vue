<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目所属单位：" prop="WTXX.JSDWMC">
            <div style="margin-left: 10px">{{formData.WTXX.JSDWMC}}</div>
          </el-form-item>
        </el-col>


        <el-col :span="8" class="grid-cell">
          <el-form-item label="项目名称：" prop="WTXX.XMMC">
            <div style="margin-left: 10px">{{formData.WTXX.XMMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="承包商名称：" prop="WTXX.CBSDWQC">
            <div style="margin-left: 10px">{{formData.WTXX.CBSDWQC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="队伍名称：" prop="WTXX.DWMC">
            <div style="margin-left: 10px">{{formData.WTXX.DWMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="子项目名称：" prop="WTXX.PJNR">
            <div style="margin-left: 10px">{{formData.WTXX.ZXMMC}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="问题发现时间：" prop="WTXX.WTFXSJ">
            <el-date-picker
                v-model="formData.WTXX.WTFXSJ"
                :disabled="true"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="评分标准：" prop="WTXX.PFBZ">
            <div style="margin-left: 10px">{{formData.WTXX.PFBZ}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="整改截止日期：" prop="WTXX.ZGJZSJ">
            <el-date-picker
                v-model="formData.WTXX.ZGJZSJ"
                disabled
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="问题描述：" prop="WTXX.WTMS">
            <el-input v-model="formData.WTXX.WTMS" :rows="3"
                      type="textarea" clearable
                      :disabled="true"/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="附件资料：" prop="ZL">
            <vsfileupload style="margin-left: 10px" :busId="params.id"
                          :key="params.id"
                          :editable="false" ywlb="wtsb"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider />

      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="验收申请说明：" prop="YSSQSM">
            <el-input v-model="formData.YSSQSM" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="附件资料：" prop="FJZL">
            <vsfileupload style="margin-left: 10px" v-model:files="FJZLFiles" :busId="WTZGID"
                          :key="WTZGID"
                          :editable="editable" ywlb="yssq"/>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="提报人：" prop="TBR">
            <div style="margin-left: 10px">{{formData.CJRXM}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="提报时间：" prop="TBSJ">
            <div style="margin-left: 10px">{{formData.CJSJ}}</div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="联系人：" prop="LXR">
            <el-input v-model="formData.LXR" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="联系电话：" prop="LXDH">
            <el-input v-model="formData.LXDH" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

<!--      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">-->
<!--        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>-->
<!--        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>-->
<!--        <el-button @click="closeForm">返回</el-button>-->
<!--      </div>-->
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import vsfileupload from "@views/components/vsfileupload";
import comFun from "@lib/comFun";
import {ElMessage} from "element-plus";
import axiosUtil from "@lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {vsfileupload},
  props: {
    params: {
      type: Object,
      required: true
    },
    value: {
      type: Object,
      default: {}
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      XMWTID: props.params.id,
      formData: {
        WTXX: {},

      },
      rules: {
        YSSQSM: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXR: [{
          required: true,
          message: '字段值不可为空',
        }],
        LXDH: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      WTZGID: null,

      FJZLFiles: []
    })

    const getFormData = () => {
      let params={
        XMWTID: state.XMWTID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/wtzgsq/selectWtysqById', params).then((res) => {
        state.formData=res.data
        if(!state.formData.SHZT){
          state.formData={
            ...state.formData,
            CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
            CJRXM: vsAuth.getAuthInfo().permission.userName,
            CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
            CJSJ: comFun.getNowTime(),
            SHZT: '0'
          }
        }
        state.WTZGID=state.formData.WTZGID || comFun.newId()

        state.loading=false
      })
    }


    const saveData = (value) => {
      let type=value==='1' ? 'submit' : 'save'
      return new Promise((resolve, reject) => {
        if (type === 'save') {
          resolve(submitForm(type))
        } else {
          validateForm().then(() => {
            resolve(submitForm(type))
          }).catch(msg => {
            ElMessage.error(msg)
            reject(msg)
          })
        }
      })


    }

    const submitForm = (type) => {
      return new Promise((resolve) => {
        let data = {
          ...props.value,
          businessId: props.value.businessId || props.params.id,
          processInstanceName: state.formData.WTXX.XMMC + '-问题整改验收申请',
          conditionStr: "lczx=1",
        }
        emit("update:value", data)

        if(!['1','new'].includes(props.value.activityId)){
          resolve(true)
          return
        }

        let params= {
          ...state.formData,
          XGRZH: state.userInfo.userLoginName,
          XGSJ: comFun.getNowTime(),
          WTZGID: state.WTZGID,
          XMWTID: state.XMWTID
        }
        if(type==='submit'){
          params.SHZT='1'
        }
        state.loading=true
        axiosUtil.post('/backend/sckhpj/wtzgsq/saveWtzgForm',params).then(res=>{
          state.loading = false
          resolve(true)
        })
      })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm,
    }
  }

})
</script>

<style scoped>

</style>
