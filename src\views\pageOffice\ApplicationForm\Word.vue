<script setup>
import request from '@/utils/request';
import { ref, onMounted, reactive } from 'vue'

const poHtmlCode = ref('');
const dialogFormVisible = ref(false);
const dialogFormVisible1 = ref(false);

const formLabelWidth = ref('100px');

const formRef = ref(null);
const formRef1 = ref(null);

const form = reactive({
	name: '',
	gender: '男',
	idCard: '',
	startDate: '',
	position: '',
	salary: ''
});

const form1 = reactive({
	name: '',
	gender: '男',
	age: 18,
	nationality: '',
	documentType: '',
	documentNumber: '',
	reason: ''
});

const rules = ref({
	name: [
		{ required: true, message: '请输入姓名', trigger: 'blur' }
	]
});

function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("保存", "Save", 1);
	pageofficectrl.AddCustomToolButton("填写收入证明", "dialogOpen()", 0);
	pageofficectrl.AddCustomToolButton("填写个税申请表", "dialogOpen1()", 0);
}
function Save() {
	//使用SaveFilePage属性设置后端保存方法的Controller路由地址，这个地址必须从"/"开始
	pageofficectrl.SaveFilePage = "/ApplicationForm/save";
	//在这里写您保存前的代码
	pageofficectrl.WebSave();
	//在这里写您保存后的代码，比如判断保存结果pageofficectrl.CustomSaveResult
}
function openFile() {
	return request({
		url: '/ApplicationForm/Word',
		method: 'get',
	})
}

function dialogOpen() {
	//定位光标到书签处
	pageofficectrl.word.LocateDataRegion("PO_zhengming");

	// 当对话框打开时执行的逻辑
	pageofficectrl.Enabled = false;
	dialogFormVisible.value = true;
}

function dialogClose(){
	pageofficectrl.Enabled = true;
	dialogFormVisible1.value = false;
}

function cancel() {
	pageofficectrl.Enabled = true;
	dialogFormVisible.value = false;

}

function confirm() {
	formRef.value.validate((valid) => {
		if (valid) {
			// 执行确认逻辑
			//将form表单的值回填到word中
			pageofficectrl.word.SetValueToDataRegion('PO_name', form.name);
			pageofficectrl.word.SetValueToDataRegion('PO_sex', form.gender);
			pageofficectrl.word.SetValueToDataRegion('PO_idCard', form.idCard);
			//将日期转换
			pageofficectrl.word.SetValueToDataRegion('PO_startDate', formatDate(form.startDate));
			pageofficectrl.word.SetValueToDataRegion('PO_job', form.position);
			pageofficectrl.word.SetValueToDataRegion('PO_salary', form.salary);


			dialogFormVisible.value = false;
			pageofficectrl.Enabled = true;
		} else {
			// 表单验证失败逻辑
		}
	});
}

function dialogOpen1() {
	//定位光标到书签处
	pageofficectrl.word.LocateDataRegion("PO_shenqingbiao");
	// 当对话框打开时执行的逻辑
	pageofficectrl.Enabled = false;
	dialogFormVisible1.value = true;
}


function dialogClose1(){
	pageofficectrl.Enabled = true;
	dialogFormVisible1.value = false;
}

function cancel1() {
	pageofficectrl.Enabled = true;
	dialogFormVisible1.value = false;

}

function confirm1() {
	formRef1.value.validate((valid) => {
		if (valid) {
			// 执行确认逻辑
			//将form表单的值回填到word中
			pageofficectrl.word.SetValueToDataRegion('PO_PersonName', form1.name);
			if ('男' == form1.gender) {
				pageofficectrl.word.SetValueToDataRegion('PO_PersonGender', '☑男 □女');
			} else {
				pageofficectrl.word.SetValueToDataRegion('PO_PersonGender', '□男 ☑女');
			}
			pageofficectrl.word.SetValueToDataRegion('PO_PersonAge', form1.age.toString());
			pageofficectrl.word.SetValueToDataRegion('PO_PersonNation', form1.nationality);
			pageofficectrl.word.SetValueToDataRegion('PO_PersonIDType', form1.documentType);
			pageofficectrl.word.SetValueToDataRegion('PO_PersonID', form1.documentNumber);
			pageofficectrl.word.SetValueToDataRegion('PO_PersonReason', form1.reason);

			dialogFormVisible1.value = false;
			pageofficectrl.Enabled = true;
		} else {
			// 表单验证失败逻辑
		}
	});
}
//日期转换
 function formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit, Save, dialogOpen1, dialogOpen };//其中OnPageOfficeCtrlInit必须

})
</script>
<template>
	<div class="Word">
		<!--个人收入证明-->
		<el-dialog v-model="dialogFormVisible" title="收入证明-个人信息"  @close="dialogClose" @open="dialogOpen">
			<el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
				<el-form-item label="姓名" prop="name">
					<el-input v-model="form.name" style="width: 400px;"></el-input>
				</el-form-item>
				<el-form-item label="性别">
					<el-radio-group v-model="form.gender">
						<el-radio label="男">男</el-radio>
						<el-radio label="女">女</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="身份证号码">
					<el-input v-model="form.idCard" style="width: 400px;"></el-input>
				</el-form-item>
				<el-form-item label="工作开始日期">
					<el-date-picker v-model="form.startDate" type="date" placeholder="选择日期"
						style="width: 400px;"></el-date-picker>
				</el-form-item>
				<el-form-item label="职务">
					<el-input v-model="form.position" style="width: 400px;"></el-input>
				</el-form-item>
				<el-form-item label="工资">
					<el-input v-model="form.salary" style="width: 400px;"></el-input>元
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel">取 消</el-button>
					<el-button type="primary" @click="confirm">确 定</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- Form -->
		<!--个人信息申请表-->
		<el-dialog v-model="dialogFormVisible1" title="纳税申请表-个人信息" @close="dialogClose1" @open="dialogOpen1">
			<el-form :model="form1" :rules="rules" ref="formRef1">
				<el-form-item label="姓名" :label-width="formLabelWidth" prop="name">
					<el-input v-model="form1.name" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="性别" :label-width="formLabelWidth">
					<el-radio-group v-model="form1.gender">
						<el-radio label="男">男</el-radio>
						<el-radio label="女">女</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="年龄" :label-width="formLabelWidth">
					<el-input-number v-model="form1.age" :min="1" :max="100"></el-input-number>
				</el-form-item>
				<el-form-item label="国籍" :label-width="formLabelWidth">
					<el-select v-model="form1.nationality" placeholder="请选择国籍">
						<el-option label="中国" value="中国"></el-option>
						<el-option label="美国" value="美国"></el-option>
						<!-- 其他国籍选项 -->
					</el-select>
				</el-form-item>
				<el-form-item label="证件类型" :label-width="formLabelWidth">
					<el-select v-model="form1.documentType" placeholder="请选择证件类型">
						<el-option label="身份证" value="身份证"></el-option>
						<el-option label="护照" value="护照"></el-option>
						<!-- 其他证件类型选项 -->
					</el-select>
				</el-form-item>
				<el-form-item label="证件号" :label-width="formLabelWidth">
					<el-input v-model="form1.documentNumber" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="申请理由" :label-width="formLabelWidth">
					<el-input type="textarea" v-model="form1.reason"></el-input>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel1">取 消</el-button>
					<el-button type="primary" @click="confirm1">确 定</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
	</div>
</template>
<style>
.el-dialog__footer {
	background-color: transparent;
	text-align: right;
}
</style>