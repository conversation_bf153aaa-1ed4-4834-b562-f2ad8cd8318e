<template>
  <!-- 未分配专业列表 -->
  <el-container>
    <el-main>
      <el-table
          class="lui-table"
        :data="tableData"
        ref="listTable"
        height="100%"
        row-key="ZYID"
        default-expand-all
        border
        stripe
        v-loading="loading"
        @select="handleSelectionChange"
      >
        <EleProTableColumn v-for="prop in tableColumn" :col="prop" :key="prop.columnKey">
          <template #ZYFL="{ row }">
            <div v-if="row.ZYFL">{{ row.ZYFL }} 类</div>
            <div v-else>--</div>
          </template>
          <template #SFZY="{ row }">
            <div>
              {{ row.SFZY ? ["非自营", "自营"][Number(row.SFZY)] : "--" }}
            </div>
          </template>
        </EleProTableColumn>
      </el-table>
    </el-main>
    <el-footer height="auto">
      <!-- Footer content -->
      <div class="bottom-btn" style="margin-bottom: 10px">
        <el-button type="primary" size="default" @click="emits('close')">返回</el-button>
        <el-button type="primary" size="default" @click="confirm">确定</el-button>
      </div>
    </el-footer>
  </el-container>
</template>
<script setup>
import { ref, reactive, onMounted, computed, nextTick, watch } from "vue";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import cascaderTree from "@src/lib/cascaderTree.js";
import comFun from "@src/lib/comFun.js";
import {
  getZrmbglZrzy, //未分配模板专业
} from "@src/api/sccbsgl.js";
import { ElMessage } from "element-plus";
const props = defineProps({
  defaultData: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(["close", "update"]);

const tableColumn = ref([
  { label: "序号", type: "index", width: 55, align: "center" },
  {
    type: "selection",
    width: 55,
    align: "center",
    selectable: (row) => row.SFMJ == "1",
    headerSlot: "selectHeader",
  },
  {
    label: "专业名称",
    prop: "ZYMC",
    headerAlign: "center",
    align: "left",
    showOverflowTooltip: true,
  },
  {
    label: "专业级别",
    prop: "ZYJB",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "专业编码",
    prop: "ZYBM",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    label: "专业分类",
    prop: "ZYBM",
    align: "center",
    showOverflowTooltip: true,
    slot: "ZYFL",
  },
  {
    label: "是否自营",
    prop: "ZYBM",
    align: "center",
    showOverflowTooltip: true,
    slot: "SFZY",
  },
  {
    label: "范围描述",
    prop: "ZYMS",
    align: "center",
    showOverflowTooltip: true,
  },
]);
// 表格数据
const tableData = ref([]);
// 已选择的企业/队伍
let selRow = ref([]);
watch(
  () => props.defaultData,
  (val) => {
    if (val) selRow.value = val;
  },
  {
    immediate: true,
  }
);
/**获取数据 */
const loading = ref(false);
const listTable = ref(null);
const orgData = ref([]);
const zyMap = new Map();
const getDataList = () => {
  loading.value = true;
  getZrmbglZrzy()
    .then(({ data }) => {
      data?.forEach((i) => zyMap.set(i.ZYBM, i.ZYBMC));
      // 保留原始数据，用于快速勾选回显已选中数据
      orgData.value = data;
      tableData.value = new cascaderTree(orgData.value ?? [], "ZYBM", "FZYBM").init();
      nextTick(() => {
        selRow.value.forEach((item) => {
          const row = orgData.value.find((i) => i.ZYID == item.ZYID);
          if (row) listTable.value.toggleRowSelection(row, true);
        });
      });
    })
    .catch(() => {
      ElMessage({
        message: "查询模板列表失败",
        type: "error",
      });
    })
    .finally(() => {
      loading.value = false;
    });
};
onMounted(getDataList);
/**选择变更 */
const handleSelectionChange = (selection, row) => {
  // 判断当前元素是否被删除
  const isNoDelItem = selection.find((i) => i.ZYID == row.ZYID);
  if (!isNoDelItem) {
    const index = selRow.value.findIndex((i) => i.ZYID == row.ZYID);
    selRow.value.splice(index, 1);
    return;
  }
  // 筛选出新选中的数据存入selRow
  selRow.value.push(...selection.filter((i) => !selectedIds.value.includes(i.ZYID)));
};
// 复选框改变时,计算选中的模板ID
const selectedIds = computed(() => {
  if (!selRow.value.length) return [];
  return [...new Set(selRow.value.map((item) => item.ZYID))];
});
const confirm = () => {
  if (!selRow.value.length) {
    ElMessage.warning("请先选择一个专业");
    return;
  }
  // 设置专业大类小类名称，用于回显
  selRow.value.forEach((item) => {
    const [parent, current] = comFun.getParentIdArr(item.ZYBM,tableData.value,'ZYBM','FZYBM');
    item.DLZYMC = parent.ZYBMC;
    item.ZLZYMC = current.ZYBMC;
    item.XLZYMC = item.ZYBMC;
  });
  emits("update", selRow.value);
};
</script>
<style lang="scss" scoped>
.el-container {
  height: 85vh;
  :deep(.el-form--inline .el-form-item) {
    margin-bottom: 0;
  }
  :deep(.el-table__header-wrapper) {
    .el-table-column--selection {
      .el-checkbox {
        display: none;
      }
    }
  }
  .bottom-btn {
    margin-top: 10px;
    text-align: center;
  }
}
</style>
