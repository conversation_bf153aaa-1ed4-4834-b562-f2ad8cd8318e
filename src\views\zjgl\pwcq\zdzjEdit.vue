<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="120px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="姓名：" prop="XM">
            <el-input v-model="formData.XM" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="身份证号：" prop="SFZH">
            <el-input v-model="formData.SFZH" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="工作单位：" prop="GZDW">
            <el-input v-model="formData.GZDW" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="专家类别：" prop="ZJLB">
            <el-select v-model="formData.ZJLB" class="full-width-input" :disabled="!editable" clearable>
              <el-option v-for="(item, index) in ZJLBOptions" :key="index" :label="item.DMMC"
                        :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="专业：" >
            <el-cascader
                @change="changeCSZY"
                clearable
                v-model="formData.XCSZYBM"
                :options="XCSZYOptions"
                :disabled="!editable"
                :show-all-levels="false"
                :props="{expandTrigger: 'hover',label:'ZYMC',value:'ZYBM',emitPath:false}"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="手机号：" prop="RKZJBXX.LXDH">
            <el-input v-model="formData.RKZJBXX.LXDH" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="统一账号：" prop="RYZH">
            <el-input v-model="formData.RYZH" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      ZJBS: props.params.id,
      formData: {
        ZJLX: 'WB',
        RKZJBXX:{},
        CJR: vsAuth.getAuthInfo().permission.userLoginName,
        CJSJ: comFun.getNowTime(),
        SHZT:'1',
        ZJZT: '1'

      },
      rules:{
        XM: [{
          required: true,
          message: '字段值不可为空',
        }],
        // SFZH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        GZDW: [{
          required: true,
          message: '字段值不可为空',
        }],
        XCSZYMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        // ZJLB: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        'RKZJBXX.LXDH': [{
          required: true,
          message: '字段值不可为空',
        }],
        RYZH: [{
          required: true,
          message: '字段值不可为空',
        }],
      },
      XCSZYOptions:[],
      ZJLBOptions: [],
    })

    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }
    }
    const changeCSZY = () => {
      state.formData.XCSZYMC=state.XCSZYOptions.find(item=>item.ZYBM===state.formData.XCSZYBM)?.ZYMC
    }
    const getZYData = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy', {}).then((res) => {
        state.XCSZYOptions = comFun.treeData(res.data, 'ZYBM', 'FZYBM', 'children', '0')
      });
    }

    const submitForm = (type) => {
      let params= {
        ...state.formData,
        ZJBS: state.ZJBS,
      }

      state.loading=true
      axiosUtil.post('/backend/zjgl/pwcq/saveWbzjForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })

    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const closeForm = () => {
      emit('close')
    }
    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }

    onMounted(() => {
      getZYData();
      getDMBData('ZJLB', 'ZJLBOptions')
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm,
      changeCSZY

    }
  }

})
</script>

<style scoped>

</style>
