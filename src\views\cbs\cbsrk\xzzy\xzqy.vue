<!-- 选择区域 -->
<template>
  <el-form ref="form" class="lui-page" size="default" label-width="0" inline>
    <div class="table">
      <el-table
          class="lui-table"
          :data="tableData"
          height="calc(100vh - 260px)"
          border
          v-loading="tableLoading"
      >
        <el-table-column
            label="专业名称"
            prop="ZYMC"
            header-align="center"
            align="center"
        ></el-table-column>
        <el-table-column
            label="服务区域"
            prop="FWQY"
            header-align="center"
            align="center"
        >
          <template v-slot="scope">
            <!--            <el-select v-model="scope.row.FWQY" clearable placeholder="请选择">-->
            <!--              <el-option-->
            <!--                  v-for="(item, index) in fwqyOptions"-->
            <!--                  :key="index"-->
            <!--                  :label="item.DMMC"-->
            <!--                  :value="item.DMXX"-->
            <!--              ></el-option>-->
            <!--            </el-select>-->
            <el-checkbox size="small"
                         v-model="scope.row.checkAll"
                         :indeterminate="scope.row.isIndeterminate"
                         @change="(value)=>handleCheckAllChange(value,scope.row)">
              全选
            </el-checkbox>
            <el-checkbox-group v-model="scope.row.FWQY" size="small" @change="(value)=>handleCheckedCitiesChange(value,scope.row)">
              <el-checkbox v-for="(item, index) in fwqyOptions"
                           :key="index"
                           :label="item.DMXX">{{item.DMMC}}</el-checkbox>
              <!--              <el-checkbox label="QT">-->
              <!--                <el-input v-if="scope.row.FWQY&&scope.row.FWQY.includes('QT')" v-model="scope.row.QTFWQYMC" placeholder="请输入内容"></el-input>-->
              <!--                <div v-else>其它</div>-->
              <!--              </el-checkbox>-->
            </el-checkbox-group>
          </template>
        </el-table-column>
        <el-table-column
            label="操作"
            header-align="center"
            align="center"
            width="100"
        >
          <template v-slot="scope">
            <div>
              <el-button type="text" @click="handleDelete(scope.row, scope.$index)"
              >删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-row class="btn" justify="end" align="center">
      <el-button type="primary" :disabled="confirmLoading" @click="preStep">上一步</el-button>
      <el-button type="primary" v-if="props.change == '1'" :disabled="confirmLoading" @click="handleChange">确定</el-button>
      <el-button type="primary" v-else :disabled="confirmLoading" @click="handleSave">确定</el-button>
    </el-row>
  </el-form>
</template>

<script setup>
import outerBox from "@components/common/outerBox";
import {
  ref,
  reactive,
  getCurrentInstance,
  onMounted,
  defineProps,
  watch,
  inject,
} from "vue";
import axiosUtil from "@lib/axiosUtil";
import { ElMessage, ElMessageBox } from "element-plus";
import tabFun from "@src/lib/tabFun";
import { getCommonsSelectDMB } from "@src/api/gcyztb.js";

postCbsyjSaveXzqy;
import { postCbsyjSaveXzqy } from "@src/api/sccbsgl.js";
import { v4 as uuidv4 } from "uuid";
import { mixin } from "@src/assets/core/index";
const { vsuiRoute, vsuiEventbus } = mixin();
const tableLoading = ref(false);
const emits = defineEmits(["preStep", "handleChange"]);
let tableData = ref([]);
let fwqyOptions = ref([]);
const uuId = inject("uuId");
const props = defineProps({
  selectData: {
    type: Object,
    default: () => {},
  },
  LY:{
    type: String,
    default: "",
  },
  ZRLX: {
    type: String,
    default: "",
  },
  change: {
    type: String,
    default: "",
  },
});
watch(
    () => props.selectData,
    (val) => {
      tableData.value = val;
    },
    {
      immediate: true,
    }
);

const handleCheckAllChange = (val,row) => {
  row.FWQY = val ? fwqyOptions.value.map(item=>item.DMXX) : []
  row.isIndeterminate = false
}

const handleCheckedCitiesChange = (value,row) => {
  const checkedCount = value.length
  row.checkAll = checkedCount === fwqyOptions.value.length
  row.isIndeterminate = checkedCount > 0 && checkedCount < fwqyOptions.value.length
}

const getArea = () => {
  getCommonsSelectDMB({
    DMLBID: "FWQY",
  })
      .then(({ data }) => {
        // console.log(data);
        fwqyOptions.value = data;
      })
      .catch((err) => {});
};
/**查询表格数据 */
const queryTableData = () => {
  // TODO 查询
  /* tableData.value.push(
      ...[
        { ZYMC: "信息系统技术服务", FWQY: "sl" },
        { ZYMC: "数据技术服务", FWQY: "xc" },
        { ZYMC: "工控技术服务", FWQY: "sl" },
      ]
    ); */
};

const handleChange = () => {
  let flag = false;
  tableData.value.forEach((x) => {
    if (!x.FWQY||x.FWQY.length===0) {
      flag = true;
    }
  });
  if (flag) {
    ElMessage({
      message: "请选择服务区域",
      type: "warning",
    });
    return;
  }
  emits("handleChange", tableData);
};

/**删除 */
const handleDelete = (val, index) => {
  ElMessageBox.confirm("是否删除此条数据？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        // TODO 删除
        ElMessage({
          message: "已删除",
          type: "success",
        });
        queryTableData();
      })
      .catch(() => {
        ElMessage({
          message: "已取消删除",
          type: "info",
        });
      });
};
/**上一步 */
const preStep = () => {
  emits("preStep");
};
/**确定 */
const confirmLoading = ref(false);
const handleSave = () => {
  if(tableData.value.some(i => !i.FWQY||i.FWQY.length===0)){
    ElMessage.warning('请选择服务区域')
    return
  }
  confirmLoading.value = true;
  let fwqyParams=[]
  tableData.value.forEach(item=>{
    item.FWQY.forEach(ii=>{
      let _FWQYMC=''
      if(ii==='QT') _FWQYMC = item.QTFWQYMC
      else _FWQYMC = fwqyOptions.value.find((i) => i.DMXX === ii).DMMC
      fwqyParams.push({
        FWQYID: uuidv4().replace(/-/g, ""), //"主键id",
        ZYMXID: item.ZYMXID, //"专业明细ID",
        FWQYWYBS: ii, //"服务区域唯一标识",
        FWQYBM: ii, //"服务区域编码",
        FWQYMC: _FWQYMC, //"服务区域名称",
      })
    })
  })
  postCbsyjSaveXzqy(fwqyParams).then(({ data }) => {
    if(fwqyParams.length!==0){
      ElMessage.success("保存区域成功");
    }
    tabFun.openNewTabClose(
        "承包商基本信息",
        "/contractors/cbsjbxxIndex",
        {
          uuId, //队伍业务ID
          MBID: tableData.value.map((i) => i.ZRMBID).join(","), //模板ID
          MBLX: "QY", //模板类型
          ZYFLDM: tableData.value.map((i) => i.ZYBM).join(","), //专业分类代码
          YWLXDM: "ZR", //业务类型代码
          editable:true,
          from:props.LY=='CBSBL'?'CBSBL':'YWBMBA'
        },
        {}
    );
    // vsuiEventbus.emit("reloadCbsjbxx", {});
    vsuiEventbus.emit("reloadCbsjbxx", {
      uuId, //队伍业务ID
      MBID: tableData.value.map((i) => i.ZRMBID).join(","), //模板ID
      MBLX: "QY", //模板类型
      ZYFLDM: tableData.value.map((i) => i.ZYBM).join(","), //专业分类代码
      YWLXDM: "ZR", //业务类型代码
    });
    // tabFun.closeTabByPath('/contractors/zrfqIndex')
  })
      .catch((err) => {
        console.log(err);
      }).finally(() => {
    confirmLoading.value = false
  })
};
onMounted(() => {
  // console.error(props.selectData)
  // console.log(tableData.value)
  // let isHave=tableData.value.some(i=>i.FZYBM==='SYGC')
  // console.error(isHave)
  // if(!isHave){
  //   tableLoading.value=true
  //   confirmLoading.value=true
  //   if(props.change === '1'){
  //     handleChange()
  //   }else {
  //     handleSave()
  //   }
  // }

  queryTableData();
  getArea();
});
</script>

<style scoped src="../../style/index.css"></style>
<style scoped>
.container {
  overflow: hidden;
  width: calc(100% - 20px);
  height: 100%;
}

.out-box-content {
  height: calc(100% - 140px);
}

/*.table {*/
/*  height: calc(100% - 40px);*/
/*  overflow: hidden;*/
/*}*/

.btn {
  margin-top: 10px;
}
</style>
