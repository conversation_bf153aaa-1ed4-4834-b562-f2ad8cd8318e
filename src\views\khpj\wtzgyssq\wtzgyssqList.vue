<template>
  <div>
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="PJLB">
            <el-input ref="input45296" placeholder="请输入建设单位" v-model="listQuery.JSDW" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="PJLB">
            <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="PJLB">
            <el-input ref="input45296" placeholder="请输入承包商名称" v-model="listQuery.CBSMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="PJLB">
            <el-input ref="input45296" placeholder="请输入队伍名称" v-model="listQuery.DWMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell" style="margin-right: 150px">
          <el-form-item label="" prop="PJLB">
            <div style="display: flex;gap: 10px">
              <el-date-picker
                  v-model="listQuery.KSSJ"
                  type="date"
                  clearable
                  style="width: 150px"
                  placeholder="发现时间开始"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
              <div>至</div>
              <el-date-picker
                  v-model="listQuery.JSSJ"
                  type="date"
                  clearable
                  style="width: 150px"
                  placeholder="发现时间结束"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
            </div>
          </el-form-item>
        </el-col>


        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 280px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="JSDWMC" label="建设单位" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="XMMC" label="项目名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="CBSDWQC" label="承包商名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="DWMC" label="队伍名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="WTFXSJ" label="问题发现时间" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="WTMS" label="问题描述" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="JFBZ" label="评分标准" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="JFFZ" label="记分分值" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="ZL" label="附件资料" align="center"
                               :show-overflow-tooltip="true" min-width="200">
                <template #default="{row}">
                  <vsfileupload :busId="row.XMWTID"
                                :key="row.XMWTID"
                                :editable="false" ywlb="wtsb"/>
                </template>
              </el-table-column>
              <el-table-column prop="YSZT" label="问题验收状态" align="center"
                               :show-overflow-tooltip="true" width="120">
                <template #default="{row}">
                  <el-tag type="primary" v-if="row.YSZT==='1'">已提交</el-tag>
                  <el-tag type="success" v-else-if="row.YSZT==='9'">已通过</el-tag>
                  <el-tag type="danger" v-else-if="row.SFJZ==='1'">已截止</el-tag>
                  <el-tag type="warning" v-else>未申请</el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                  <el-button v-if="(!scope.row.YSZT || scope.row.YSZT==='0') && scope.row.SFJZ==='0'" size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">发起验收申请
                  </el-button>
                  <el-button v-else size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="问题整改情况"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <auditFrame v-if="dialogVisible" :business-params="params"
                    :process-params="processParams" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import wtzgyssqEdit from "@views/khpj/wtzgyssq/wtzgyssqEdit";
import axiosUtil from "@lib/axiosUtil";
import vsfileupload from "@views/components/vsfileupload";
import auditFrame from "@views/workflow/AuditFrame";
import vsAuth from "@lib/vsAuth";

export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,wtzgyssqEdit,vsfileupload,auditFrame},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      isAdmin:false,
      listQuery: {
        page: 1,
        size: 10,
        userLoginName:vsAuth.getAuthInfo().permission.userLoginName
      },
      tableData: [{}],
      total: 0,
      params: {},
      dialogVisible: false,

      processParams:{
        activityId: "new",
        processId: "KHPJ_WTZGYS",
        processVersion:"1"
      }
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
      }
      if(state.isAdmin){
        params.userLoginName='';
      }
      axiosUtil.get('/backend/sckhpj/wtzgsq/selectXzgwtPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    // 新增
    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.params = {editable: true, id: row.XMWTID, operation: 'edit'}
      state.dialogVisible = true
    }
    const viewRow = (row) => {
      state.params = {editable: false, id: row.XMWTID, operation: 'view'}
      state.dialogVisible = true
    }
    const deleteRow = (row) => {
      ElMessageBox.confirm('你确定删除改用户，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        ElMessage.success({
          message: '删除成功!'
        });
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }
    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      let roleList=vsAuth.getAuthInfo().permission.roleList;
      if(roleList&&roleList.length>0){
        for(let i=0;i<roleList.length;i++){
          if(roleList[i].roleCode=='SCGL_SCGLY'||roleList[i].roleCode=='XSGL_YTQGFGBBS'){//市场管理员
            state.isAdmin=true;
            break;
          }
        }
      }
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      addData,
      closeForm,
      deleteRow,
      viewRow
    }
  }

})
</script>

<style scoped>

</style>
