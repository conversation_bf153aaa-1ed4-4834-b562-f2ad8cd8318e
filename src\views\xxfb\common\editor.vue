<template>
  <!-- <div class="d-flex">
    <div style="width: 50%">
      <TinymceEditor v-model="content" @input="inputContent"/>
    </div>
    <div class="right" v-html="content"></div>
  </div> -->
</template>

<script setup>
// import {ref} from 'vue'
// import TinymceEditor from "../../components/TinymceEditor"

// const content = ref("Hello World")

// const inputContent = (newVal) => {
//   console.log(newVal)
//   content.value = newVal
// }
</script>

<style>
/*.d-flex {*/
/*    display: flex;*/
/*    gap: 10px;*/

/*    .right {*/
/*        flex: 1;*/
/*        box-shadow: 0 1px 10px 3px #dbdbdb;*/
/*        margin-right: 10px;*/
/*        padding: 10px;*/
/*        box-sizing: border-box;*/
/*    }*/
/*}*/
</style>
