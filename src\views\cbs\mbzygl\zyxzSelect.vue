<template>
    <div class="zhyy-list-container">
        <div class="zhyy-list-main">
            <el-row class="zhyy-list-searchArea" style="text-align: left">
                <el-col :span="24">
                    <label>专业类别：</label>
                    <el-select  style="width: 110px" v-model="params.mblx" placeholder="请选择">
                        <el-option
                            v-for="item in params.mblxArry"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <el-button type="primary" style="margin-left:10px" @click="getDataList()">查询</el-button>
                    <el-button type="primary" style="margin-left:10px;float: right" @click="confirm()">确定</el-button>
                </el-col>
            </el-row>
           <!--表格数据-->
            <el-row class="zhyy-list-tableArea">
                <el-table
                    class="customer-no-border-table"
                    :data="tableData"
                    border="1px"
                    width="100%"
                    :height="pageHeight"
                >
                    <el-table-column
                        type="index"
                        :index="indexMethod"
                        label="序号"
                        align="center"
                        width="50"
                    ></el-table-column>
                    <el-table-column
                        prop="YWMC"
                        label="专业类别"
                        header-align="center"
                        align="center">
                        <el-table-column
                            prop="YWMC"
                            label="大类"
                            header-align="center"
                            align="center">
                        </el-table-column>
                        <el-table-column
                            prop="YWMC"
                            label="中类"
                            header-align="center"
                            align="center">
                        </el-table-column>
                        <el-table-column
                            prop="YWMC"
                            label="小类"
                            header-align="center"
                            align="center">
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-row>
            <div class="pagination">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="params.currentPage"
                    :page-sizes="[10, 20, 30, 40]"
                    :page-size="params.pageSize"
                    layout="total, sizes, prev, pager, next "
                    :total="params.total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import axiosUtil from "../../../lib/axiosUtil";

let pageHeight = computed(() =>{
    return "calc(100vh - 300px)"
})
let dwEditable = ref(false)
let tableData = reactive([])
let multipleSelection = reactive([])
let form = reactive({})
let params = reactive({
    mbmc:"",
    mblx:"",
    mblxArry:[
        { value: '1', label: '企业' },
        { value: '2', label: '队伍' }
    ],
    ZT:'',
    value:'',
    appUser:'',
    dwzt:'',
    model:'dwjdkh',
    //项目名称/编码
    inputXmmcbm : "",
    //工程类别取值
    inputGclb : "",
    //工程类别
    //项目年度
    xmnd: String(new Date().getFullYear()),
    // 当前页码
    currentPage: 1,
    // 每页的数据条数
    pageSize: 10,
    total: 0,
    recent: 0,
    //行数据ID
    editRowId: null,
    clickArr: [],
    formLabelWidth: "100px",
    tableStyle: "width:100%;height:calc(100vh - 310px);",
    modelTemesMap: {},
    SJYWBM:'',
    ZYLB:"",
    KHBS:'',
    edit:'',
    khqj:[],
    KHMC:'',
    FPJDX:"",
    FJDLX:"",
    jyData: [],
    zylbJsonArray: []
})
const confirm = () =>{
    //确定，将数据传输到上一个页面
}
const  deleteRow = (index, row) =>{
    axiosUtil.get('/sldwgl/dwkh/deleteDwkh', row).then(res =>{
        if(res.data.meta.success){
            ElMessage({
                message: '删除成功',
                type: 'success'
            })
            getDataList()
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
    // let result= await util.get('/sldwgl/dwkh/deleteDwkh', row, this.model);
    // if (result.data.meta.message=='ok'){
    //     this.$message.success("删除成功")
    //     this.getDataList()
    // }else {
    //     this.$message.error(result.data.meta.message)
    // }
}
const  pubRow = (index, row, status) =>{
    row.SHZT = status;
    axiosUtil.get('/sldwgl/dwkh/pubRow', row).then(res =>{
        if(res.data.meta.success){
            ElMessage({
                message: '操作成功',
                type: 'success'
            })
            getDataList()
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
    // let result= await util.get('/sldwgl/dwkh/pubRow', row, this.model);
    // if (result.data.meta.message=='ok'){
    //     this.$message.success("操作成功")
    //     this.getDataList()
    // }else {
    //     this.$message.error(result.data.meta.message)
    // }
}
const  adddwkh = () =>{
    params.KHBS = util.newId();
    params.edit = true
    dwkhdialogVisible.value = true
}
const  viewDwKh = (index, row) =>{
    params.khqj[0] = row.PJKSRQ;
    params.khqj[1] = row.PJJSRQ;
    params.SJYWBM = row.SCLBBM;
    params.FPJDX = row.PJDX;
    params.FJDLX = row.JDLX;
    params.KHMC = row.KHMC;
    params.edit = false;
    params.KHBS = row.KHBS;
    dwkhdialogVisible.value = true
}
//关闭对话框
const  onClose = () => {
    params.ZYLB = [];
    params.khqj = [];
    params.SJYWBM = "";
    params.PJDX = "";
    params.JDLX = "";
    getDataList()
    dwkhdialogVisible.value = false;
}
const  getAppUser = () =>{
    // params.appUser=await util.getAppUser();
}
/**
 * 序号
 */
const  indexMethod = (index) => {
    return index + params.pageSize * (params.currentPage - 1) + 1;
}
/**
 * 页面数据条数改变时
 */
const  handleSizeChange = (val) => {
    params.currentPage = 1;
    params.pageSize = val;
    getDataList()
}
/**
 * 翻页
 */
const  handleCurrentChange = (val) => {
    params.currentPage = val;
    getDataList()
}
/**
 * @Params: {{Params}}
 * @Description: 获取数据
 */
const  getDataList = () => {
    let param = {
        mbmc:params.mbmc,
        mblx:params.mblx
    };
    axiosUtil.get('/sldwgl/mbgl/queryMb',param).then(res =>{
        if(res.data.meta.success){
            tableData.length = 0
            tableData.push(...res.data.data.rows)
            params.total = res.data.data.total
        }
    })
    // let pageData=util.getObjectResult(await util.get('/sldwgl/mbgl/queryMb',params,this.model))
    // console.log(pageData,'pageData')
    // this.tableData=pageData.rows;
    // this.total=pageData.total
}
//分页多行变少行，点击翻页不刷新问题
const  pageClick = e => {
    if (!tableData.length) {
        return false;
    }
    let dom = e.target;
    if (dom.className === "btn-next" || (dom.className === "el-icon el-icon-arrow-right" &&dom.parentNode.className !== "btn-next disabled")) {
        params.currentPage += 1;
        params.currentPage >= Math.ceil(params.total / params.pageSize) ? (params.currentPage = Math.ceil(params.total / params.pageSize)) : params.currentPage;
    } else if (dom.className === "btn-prev" || (dom.className === "el-icon el-icon-arrow-left" && dom.parentNode.className !== "btn-prev disabled")) {
        params.currentPage -= 1;
        params.currentPage <= 1 ? (params.currentPage = 1) : params.currentPage;
    } else if (dom.className === "el-icon more btn-quicknext el-icon-d-arrow-right") {
        params.currentPage = Math.ceil(params.total / params.pageSize);
    } else if (dom.className === "el-icon more btn-quickprev el-icon-d-arrow-left") {
        params.currentPage = 1;
    } else if (dom.className === "number") {
        params.currentPage = Number(dom.innerHTML);
    } else {
        return false;
    }
    getDataList()
}
const  querySqlbData = () => {
    let param = {
        zymc : params.inputZymc ,
        gclbdm:params.gclbdm,
        CBSDWBS:params.cbsdwbs
    }
    axiosUtil.get('/sldwgl/cbsDwxx/whDwzy', param).then(res =>{
        if(res.data.meta.success){
            let rows = res.data.data
            params.jyData = rows
            for (let i = 0; i <rows.length ; i++) {
                rows[i].value=rows[i].YWBM
                rows[i].label=rows[i].YWMC
            }
            let treeData = transData(rows, 'YWBM', 'PYWBM', 'children');
            params.zylbJsonArray = treeData;
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'warning'
            })
        }
    })
    // let result = await util.get('/sldwgl/cbsDwxx/whDwzy', param, this.model);
    // if(result.data.meta.success){
    //     var rows = result.data.data;
    //     this.jyData=rows
    //     for (let i = 0; i <rows.length ; i++) {
    //         rows[i].value=rows[i].YWBM
    //         rows[i].label=rows[i].YWMC
    //     }
    //     let treeData = this.transData(rows, 'YWBM', 'PYWBM', 'children');

    //     params.zylbJsonArray = treeData;
    // }else{
    //     this.$message({type: "warning", message: result.data.meta.message});
    // }
}
/**
 * json格式转树状结构
 * @param   {json}      json数据
 * @param   {String}    id的字符串
 * @param   {String}    父id的字符串
 * @param   {String}    children的字符串
 * @return  {Array}     数组
 */
const  transData = (a, idStr, pidStr, childrenStr) =>{
    let r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
    for(; i < len; i++){
        a[i]['isparent'] = false;
        hash[a[i][id]] = a[i];
    }
    for(; j < len; j++){
        let aVal = a[j], hashVP = hash[aVal[pid]];
        if(hashVP){
            !hashVP[children] && (hashVP[children] = []);
            hashVP[children].push(aVal);
        }else{
            r.push(aVal);
        }
    }
    return r;
}
const  setIsParent = (arr) => {
    for(let j=0; j < arr.length; j++){
        if(arr[j].children && arr[j].children.length > 0){
            arr[j].isparent = true;
            setIsParent(arr[j].children);
        }
    }
}
const  handleChangeLb = (value) => {
    params.ZYLB = value
    params.SJYWBM = value[2];
    getDataList();
}
onMounted(() =>{
    getDataList();
    getAppUser();
    querySqlbData();
})
</script>

<style scoped>
::v-deep .el-cascader__dropdown{
    height: 250px;
}
.dialog-footer {
    text-align: center;
}
.el-cascader-menu__wrap{
    height: 250px;
}
body .el-table th.gutter {
    display: table-cell !important;
}

</style>