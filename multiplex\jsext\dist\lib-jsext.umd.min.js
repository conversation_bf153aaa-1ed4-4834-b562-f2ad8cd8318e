!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("@ahcui/lib-jsext")):"function"==typeof define&&define.amd?define(["@ahcui/lib-jsext"],t):"object"==typeof exports?exports.VsuiLibJSExt=t(require("@ahcui/lib-jsext")):e.VsuiLibJSExt=t(e["@ahcui/lib-jsext"])}(this,function(e){return function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)t.d(r,o,function(t){return e[t]}.bind(null,o));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=1)}([function(t,n){t.exports=e},function(e,t,n){e.exports=n(2)},function(e,t,n){"use strict";n.r(t);var r=n(0),o=n.n(r);n.d(t,"default",function(){return o.a}),n.d(t,"Date",function(){return r.Date}),n.d(t,"String",function(){return r.String})}]).default});