<!-- 引入申请信息 -->
<template>
  <el-form :model="form" ref="vForm" class="lui-card-form" :rules="vFormRules" size="default" label-position="left" label-width="140px" :disabled="!editable">
    <el-row :gutter="0" class="grid-row">
      <el-col :span="24" class="grid-cell">
        <el-form-item label="队伍名称" prop="DWMC">
          <el-input v-model="form.DWMC"></el-input>
        </el-form-item>
      </el-col>
        <el-col :span="24" class="grid-cell">
            <el-form-item label="申请投标类型" prop="SQTBLX">
                <span>{{ form.SQTBLX }}</span>
            </el-form-item>
        </el-col>
      <!-- <el-col :span="8" class="grid-cell">
        <el-form-item label="类别" prop="DWLB">
          <el-select v-model="form.DWLB" clearable placeholder="请选择" :disabled="true">
            <el-option v-for="(item, index) in teamTypeList" :key="index" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col> -->
      <el-col :span="24" class="grid-cell" v-if="form.DWLB==='GKZB'">
        <el-form-item label="招标项目：" prop="SQTBFW">
          <el-input v-model="form.XMMC" type="text" placeholder="请选择" clearable :disabled="true">
            <template #append v-if="editable">
              <el-button @click="chooseZBXM">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24" class="grid-cell">

<!--      <el-form-item label="申请服务范围" prop="SQFWFW">-->
<!--        <el-table class="lui-table"-->
<!--                  :data="form.DWZYXSList"-->
<!--                  height="300px"-->
<!--                  border>-->
<!--          <el-table-column-->
<!--              label="专业名称"-->
<!--              prop="ZYMC"-->
<!--              header-align="center"-->
<!--              align="center"-->
<!--          ></el-table-column>-->
<!--          <el-table-column-->
<!--              label="服务区域"-->
<!--              prop="FWQY"-->
<!--              header-align="center"-->
<!--              align="center">-->
<!--            <template v-slot="scope">-->
<!--              <el-checkbox-group v-model="scope.row.FWQY" size="small" :disabled="true">-->
<!--                <el-checkbox v-for="(item, index) in fwqyOptions"-->
<!--                             :key="index"-->
<!--                             :label="item.DMXX">{{item.DMMC}}</el-checkbox>-->
<!--              </el-checkbox-group>-->
<!--            </template>-->
<!--          </el-table-column>-->

<!--        </el-table>-->



<!--      </el-form-item>-->
      </el-col>
      <el-col :span="12" class="grid-cell">
          <el-form-item label="推荐单位/部门" prop="TJDW">
            <div style="display: flex;justify-content: flex-start;width: 100%;">
              <!--                            <el-input v-model="form.TJDW" disabled style="flex: 1"></el-input>
                                          <el-button type="primary" style="margin-left: 10px;">选择</el-button>-->
              <!-- <el-select v-model="form.TJDW" clearable placeholder="请选择">
                  <el-option v-for="(item, index) in organizations" :key="index" :label="item.NAME"
                             :value="item.CODE"></el-option>
              </el-select> -->
    <!--          <el-tree-select-->
    <!--              v-model="form.TJDW"-->
    <!--              :data="organizations"-->
    <!--              :render-after-expand="false"-->
    <!--              :props="pop"-->
    <!--              :check-strictly="true"-->
    <!--              :default-expand-all="true"-->
    <!--              placeholder="请选择"-->
    <!--              v-tooltip="{newValue:form.TJDW,oldValue:resultTableData?.TJDW,label:organizationsDic[resultTableData?.TJDW]}"-->
    <!--          />-->
              <!-- <el-select
                  v-model="form.TJDW"
                  placeholder="请选择推荐单位"
                  clearable>
                <el-option
                    v-for="item in tjdw"
                    :key="item.DMXX"
                    :label="item.DMMC"
                    :value="item.DMXX"
                >
                </el-option>
              </el-select> -->
              <el-cascader v-model="form.TJDW" :options="tjdw" filterable placeholder="请选择推荐单位"
                             :props="{label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false}"
                             clearable :disabled="form.DWLB==='C'" />
            </div>
          </el-form-item>
      </el-col>
        <el-col :span="12" class="grid-cell" v-if="form.DWLB === 'C' && form.IS_EJDW === 'Y'">
            <el-form-item label="推荐科室" prop="EXTENSION.TJKS">
                <div style="display: flex;justify-content: flex-start;width: 100%;">
                    <el-cascader v-model="form.EXTENSION.TJKS" :options="tjks" filterable placeholder="请选择推荐科室"
                                 :props="{label:'ORGNA_NAME',value:'ORGNA_ID',emitPath: false}"
                                 clearable />
                </div>
            </el-form-item>
        </el-col>
      <!--<el-col :span="8" class="grid-cell">
        <el-form-item label="引入类型" prop="SQLBDM">
          <el-select v-tooltip="{newValue:form.SQLBDM,oldValue:resultTableData?.SQLBDM,label:yrlxList.find(i => i.value == resultTableData?.SQLBDM)?.label}" v-model="form.SQLBDM" clearable placeholder="请选择">
            <el-option v-for="(item, index) in yrlxList" :key="index" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="备案类型" prop="BALXDM" v-if="form.SQLBDM == 'BADJ'">
          <el-select v-tooltip="{newValue:form.BALXDM,oldValue:resultTableData?.BALXDM,label:balxList.find(i => i.value == resultTableData?.BALXDM)?.label}" v-model="form.BALXDM" clearable placeholder="请选择">
            <el-option v-for="(item, index) in balxList" :key="index" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>-->
      <!--<el-col :span="24" class="grid-cell">
          <el-form-item :class="{'data-change': resultTableData?.BZ !== form.BZ}" label="备注" prop="BZ">
            <el-input v-model="form.BZ" v-tooltip="{newValue:form.BZ,oldValue:resultTableData?.BZ,label:resultTableData?.BZ}" :rows="3" placeholder="因特殊原因备案的请在此说明"></el-input>
          </el-form-item>
      </el-col>-->
        <el-col :span="24" class="grid-cell">
            <el-form-item label="相关附件">
                <vsfileupload
                        style="margin-left: 10px"
                        :busId="form.DWYWID"
                        :editable="editable"
                        :key="form.DWYWID"
                        ywlb="YRSQXXXGFJ"
                        busType="dwxx"
                        :limit="10"
                ></vsfileupload>
            </el-form-item>
        </el-col>
      <!-- <el-col :span="8" class="grid-cell">
        <el-form-item label="准入证号" prop="zrzh">
          <el-input v-model="form.zrzh" disabled placeholder="自动生成"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="有效截止日期" prop="YXQJS">
          <el-input v-model="form.YXQJS" disabled placeholder="自动生成"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="发证日期" prop="fzrq">
          <el-input v-model="form.fzrq" disabled placeholder="自动生成"></el-input>
        </el-form-item>
      </el-col> -->
    </el-row>
<!--    <el-row class="collapse">-->
<!--      <el-collapse v-model="activeName" accordion>-->
<!--        <el-collapse-item title="相关附件" name="1">-->
<!--          <vsFileUploadTable style="width: 100%;height: 320px" YWLX="YRSQXX" key="params.id" :busId="form.DWYWID" v-model:fileTableData="fileTableData"  :editable="editable"/>-->
<!--        </el-collapse-item>-->
<!--      </el-collapse>-->
<!--    </el-row>-->

<!-- v-if="form.DWLX==='CBS'" -->
    <el-table
        class="lui-table"
        :data="form.DWZYList"
        height="300px"
        border>
      <el-table-column
          label="专业名称"
          prop="ZYMC"
          header-align="center"
          align="center"
      ></el-table-column>
      <el-table-column
          label="服务区域"
          prop="FWQY"
          header-align="center"
          align="center">
        <template v-slot="scope">
          <el-tag type="primary" v-for="(item,index) in scope.row.FWQY" :key="index" style="margin: 2px">
            {{getFwqymc(item).ORGNA_NAME}}
          </el-tag>

          <!-- {{tjdw.find((i) => i.ORGNA_ID === scope.row.FWQY[0]).ORGNA_NAME }} -->
          <!-- <el-checkbox size="small"
                       v-model="scope.row.checkAll" :disabled="!editable"
                       :indeterminate="scope.row.isIndeterminate"
                       @change="(value)=>handleCheckAllChange(value,scope.row)">
            全选
          </el-checkbox>
          <el-checkbox-group v-model="scope.row.FWQY" size="small" @change="(value)=>handleCheckedCitiesChange(value,scope.row)" :disabled="!editable">
            <el-checkbox v-for="(item, index) in fwqyOptions"
                         :key="index"
                         :label="item.DMXX">{{item.DMMC}}</el-checkbox>
          </el-checkbox-group> -->
        </template>
      </el-table-column>

    </el-table>




  </el-form>

  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="false"
      v-if="ZBXMParams.dialogVisible"
      v-model="ZBXMParams.dialogVisible"
      title="招标项目选择"
      z-index="1000"
      top="6vh"
      width="1100px">
    <div>
      <xmChoose @close="ZBXMParams.dialogVisible=false" @submit="getCgxmRes" :params="ZBXMParams.params"/>
    </div>
  </el-dialog>
</template>

<script setup>
import {defineProps, getCurrentInstance, watch, reactive, ref} from 'vue'
import {vue} from '../../../../src/assets/core'
import vsFileUploadTable from "../../components/vsFileUploadTable.vue";
import {getCommonGetUserByParam, getCommonSelectDMB} from '@src/api/cbsxx.js'
import {getCbsyjGetEntOrganizations} from '@src/api/sccbsgl.js'
import cascaderTree from "@src/lib/cascaderTree.js";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {getCommonsSelectDMB} from "@src/api/gcyztb";
import xmChoose from "@views/cbs/cbsyj/xmChoose";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
let form = vue.reactive({})
const props = defineProps({
    defaultData: {
        type: Array,
        defaultData: () => [],
    },
    // 结果表数据，比对用
    resultTableData:{
        type: Object,
        default: () => null
    },
    // 是否查看模式
    editable: {
        type: Boolean,
        default: true
    },
  CBSDWQC: {
    type: String,
  },
    // 临时准入类型
    LSZRLX: {
        type: String,
        default: ""
    },
});
watch(
    () => props.defaultData,
    (val) => {
        form = val;
        if(form && form.DWZYList){
          form.DWZYList.forEach(item=>{
            item.FWQY=item.QYList.map(item=>item.FWQYBM)
          })
          form.DWZYXSList.forEach(item=>{
            item.FWQY=item.QYList.map(item=>item.FWQYBM)
          })
        }
    },
    {
        immediate: true,
    }
);
const fileTableData = vue.ref([]);
let _this = getCurrentInstance()
var teamTypeList = vue.ref([])
const getTeamTypeList = ()=>{
    getCommonSelectDMB({DMLBID: 'DWLB'}).then(res=>{
        teamTypeList.value = res.data.map(x=>{
            return {
                label: x.DMMC,
                value: x.DMXX
            }
        })
    })
}
var sqtblxList = vue.ref([]);
const getSqtblxList = ()=>{
    getCommonSelectDMB({DMLBID: 'SQTBLX'}).then(res=>{
        sqtblxList.value = res.data.map(x=>{
            return {
                label: x.DMMC,
                value: x.DMXX
            }
        })
    })
}

var organizations = vue.ref([]);
const pop = reactive({
    parent: 'PORGID',
    value: 'CODE',
    label: 'NAME',
    children: 'children',
});
// 部门/单位 字典
const organizationsDic = ref({})
const getEntOrganizations = ()=>{
    getCbsyjGetEntOrganizations({}).then(res=>{
        if(!res.data) return
        organizationsDic.value = res.data.reduce((t, i) => {
            t[i.CODE] = i.NAME;
            return t
        }, {})
        if (res.data) organizations.value = new cascaderTree(res.data, "ORGID", "PORGID").init();
    })
}
const getYrlxList = ()=>{
    getCommonSelectDMB({DMLBID: 'YRLX'}).then(res=>{
        yrlxList.value = res.data.map(x=>{
            return {
                label: x.DMMC,
                value: x.DMXX
            }
        })
    })
}

let yrlxList = vue.ref([])
const queryBalx = () => {
    getCommonSelectDMB({DMLBID: 'BALX'}).then(res=>{
        balxList.value = res.data.map(x=>{
            return {
                label: x.DMMC,
                value: x.DMXX
            }
        })
    })
}
let balxList = vue.ref([])
let activeName = vue.ref('1')
let tableData = vue.reactive([])
let fileList = vue.reactive([])
let currentRow = vue.reactive({})
/**查询备案类型 */


/** 查询相关附件 */
const queryXgfj = () => {
    tableData.length = 0
    tableData.push(...[
        {fileType: '推荐函', fileName: '', fileList: []},
        {fileType: '廉洁从业责任书', fileName: '', fileList: []},
        {fileType: '商业伙伴合规尽职调查表', fileName: '', fileList: []},
        {fileType: '申报队伍质量、健康、安全、环保体系及相关文件', fileName: '', fileList: []},
        {fileType: '申报队伍近两年安全事故说明', fileName: '', fileList: []},
        {fileType: '其他附件', fileName: '', fileList: []},
    ])
}
const setCurrentRow = row => {
    currentRow = row
}
const onFileChange = (file, files) => {
    console.log(_this);
    console.log(file, files);
    fileList = files
    let ind = tableData.indexOf(currentRow)
    tableData[ind].fileName = fileList.map(item => item.name).join(',')
    tableData[ind].files = fileList
    console.log('tableData', tableData);
}
vue.onMounted(() => {
    getSqtblxList();
    getEntOrganizations();
    queryXgfj()
    getTjdw()
    // C类队伍时触发
    tjdwChange();
    // getArea()
})
const vForm = ref(null);
const vFormRules = ref({
    DWMC:[
        { required: false, message: '请输入企业(队伍)名称', trigger: 'blur' },
        { max: 128, message: '最多输入128个字符', trigger: 'blur' },
    ],
    TJDW:[
        { required: true, message: '请选择推荐单位/部门', trigger: 'change' },
    ],
    'EXTENSION.TJKS': [
        { required: true, message: '请选择推荐科室', trigger: 'change' },
    ],
    SQLBDM:[
        { required: true, message: '请选择引入类型', trigger: 'change' },
    ],
    BALXDM:[
        { required: true, message: '请选择备案类型', trigger: 'change' },
    ],
    SQFWFW:[
        { required: false, message: '请输入申请服务范围', trigger: 'blur' },
        { max: 4000, message: '最多输入4000个字符', trigger: 'blur' },
    ],
    BZ:[
        { required: false, message: '请输入备注', trigger: 'blur' },
        { max: 256, message: '最多输入256个字符', trigger: 'blur' },
    ],

})
const validateForm = () => {
  return Promise.all([vForm.value.validate(),validateFWQY()])
}
const tjdw = ref([]);
const getTjdw = () => {
    axiosUtil.get('/backend/common/selectTwoOrgAndJgbmList', {LSZRLX: props.LSZRLX}).then((res) => {
      tjdw.value = res.data;
      //comFun.treeData(res.data || [],'ORGNA_ID','PORGNA_ID','children','1458257119443951634')
  });
};
const tjks = ref([]);
const getTjks = () => {
    axiosUtil.get('/backend/common/queryZjdwByPorgnaId', {orgnaId: form.DWZYList[0].QYList[0].FWQYBM}).then((res) => {
        tjks.value = res.data;
    });
};

let fwqyOptions = ref([]);
const handleCheckAllChange = (val,row) => {
  row.FWQY = val ? fwqyOptions.value.map(item=>item.DMXX) : []
  row.isIndeterminate = false
  changeQy();
}

const getFwqymc = (val) => {
  if(val === 'all'){
    return {ORGNA_NAME: '全局'};
  }
  for(let i=0;i<tjdw.value.length;i++){
    if(tjdw.value[i].ORGNA_ID === val){
      return tjdw.value[i];
    }
  }
  return {};
}

const handleCheckedCitiesChange = (value,row) => {
  const checkedCount = value.length
  row.checkAll = checkedCount === fwqyOptions.value.length
  row.isIndeterminate = checkedCount > 0 && checkedCount < fwqyOptions.value.length
  changeQy()
}

const changeQy = () => {
  form.DWZYList.forEach(item=>{
    let fwqyParams=[]
    item.FWQY.forEach(ii=>{
      let _FWQYMC=''
      if(ii==='QT') _FWQYMC = item.QTFWQYMC
      else _FWQYMC = fwqyOptions.value.find((i) => i.DMXX === ii).DMMC
      fwqyParams.push({
        FWQYID: comFun.newId(), //"主键id",
        ZYMXID: item.ZYMXID, //"专业明细ID",
        FWQYWYBS: ii, //"服务区域唯一标识",
        FWQYBM: ii, //"服务区域编码",
        FWQYMC: _FWQYMC, //"服务区域名称",
      })
    })
    item.QYList=fwqyParams
  })
}

const validateFWQY = () => {
  return new Promise((resolve, reject) => {
    if(form.DWLX==='CBS'){
      let WKQY=form.DWZYList.find(item=>!item.FWQY || item.FWQY.length===0)
      if(WKQY){
        reject({
          FWQY:[{
            message: `请选择${WKQY.ZYMC}专业服务区域`
          }]
        })
      }else {

        resolve()
      }
    }else {
      resolve()
    }
  })
}

const getArea = () => {
  getCommonsSelectDMB({
    DMLBID: "FWQY",
  })
      .then(({ data }) => {
        // console.log(data);
        fwqyOptions.value = data;
        form.DWZYList?.forEach(item=>{
          handleCheckedCitiesChange(item.FWQY || [],item)
        })
      })
      .catch((err) => {});
};


const ZBXMParams=reactive({
  dialogVisible: false,
  params: {}
})
const chooseZBXM = () => {
  if(!props.CBSDWQC){
    ElMessage.warning('请填写企业名称')
    return
  }
  ZBXMParams.dialogVisible=true
  ZBXMParams.params.CBSDWQC=props.CBSDWQC
}
const getCgxmRes = (res) => {
  form.SQTBFW=res.XMID
  form.XMMC=res.XMMC
  ZBXMParams.dialogVisible=false
};

const tjdwChange = () => {
    // C类准入，展示推荐科室
    if(props.defaultData.DWLB === 'C' && props.defaultData.IS_EJDW === 'Y' && form.DWZYList && form.DWZYList.length > 0) {
        getTjks();
    }
};

defineExpose({
  validateForm
})
</script>

<style scoped>
.el-form .el-row > .el-form-item {
    width: 100%;
}

.el-form .el-row > .el-form-item > .el-input, .el-select {
    width: 100%;
}

.el-collapse {
    width: 100%;
}

.el-collapse .el-collapse-item ::v-deep .el-collapse-item__header {
    background-color: #e3e6f6;
    padding-left: 10px;
}
</style>
