import axiosUtil from '@src/lib/axiosUtil.js';
const baseUrl = process.env.NODE_ENV == "production" ? "/backend" : "/backend"


// 流程回调函数-流程办理人回调函数
// @method postWorkFlowFindUserByRoleId
// @type post
// @return url
//postWorkFlowFindUserByRoleId: `/workFlow/findUserByRoleId`,

// eslint-disable-next-line
export function postWorkFlowFindUserByRoleId(params) {
    return axiosUtil.post(`${baseUrl}/workFlow/findUserByRoleId`, params)
}
// 流程回调函数-流程办理结束回调函数
// @method postWorkFlowUpdateBusinessByPorcessInstance
// @type post
// @return url
//postWorkFlowUpdateBusinessByPorcessInstance: `/workFlow/updateBusinessByPorcessInstance`,

// eslint-disable-next-line
export function postWorkFlowUpdateBusinessByPorcessInstance(params) {
    return axiosUtil.post(`${baseUrl}/workFlow/updateBusinessByPorcessInstance`, params)
}
// 流程回调函数-流程业务参数查询回调函数
// @method postWorkFlowQueryTzbl
// @type post
// @return url
//postWorkFlowQueryTzbl: `/workFlow/queryTzbl`,

// eslint-disable-next-line
export function postWorkFlowQueryTzbl(params) {
    return axiosUtil.post(`${baseUrl}/workFlow/queryTzbl`, params)
}




