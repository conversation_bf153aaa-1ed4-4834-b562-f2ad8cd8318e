<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="left" label-width="150px"
           size="default" v-loading="loading">
    <el-collapse v-model="openCollapse">
      <jbxx-card v-model:form-data="formData" :params="params"/>
      <el-collapse-item title="专业信息" name="1">
        <zyxx-card v-model:form-data="formData" :params="params"/>
      </el-collapse-item>
      <el-collapse-item title="扩展信息" name="2">
        <kzxx-card v-model:form-data="formData" :params="params"/>
      </el-collapse-item>
      <el-collapse-item title="变更信息" name="3">
        <el-row :gutter="12" class="grid-row no-border-bottom">
          <el-col :span="24" class="grid-cell">
            <el-form-item label="变更原因：" prop="RKZJBXX.BGYY">
              <el-input type="textarea" v-model="formData.RKZJBXX.BGYY" rows="3"
                        :disabled="!(editable && pageType == 'info')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell">
            <el-form-item label="变更说明：" prop="RKZJBXX.BGSM">
              <el-input type="textarea" v-model="formData.RKZJBXX.BGSM" rows="3"
                        :disabled="!(editable && pageType == 'info')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
    <div style="display:flex;justify-content: center;gap: 20px;margin-top: 20px">
      <div class="static-content-item">
        <el-button type="success" @click="validateForm('save')">暂存</el-button>
      </div>
      <div class="static-content-item">
        <el-button type="primary" @click="validateForm('submit')">提交</el-button>
      </div>
      <div class="static-content-item">
        <el-button @click="closePage">返回</el-button>
      </div>
    </div>
  </el-form>

</template>

<script>
import {
  defineComponent,
  ref,
  shallowRef,
  toRefs,
  reactive,
  getCurrentInstance,
  watchEffect, onMounted, watch
}
  from 'vue'
import axiosUtil from "../../../lib/axiosUtil";
import {Plus} from '@element-plus/icons-vue'
import comFun from "../../../lib/comFun";
import vsAuth from "../../../lib/vsAuth";
import {ElMessage} from 'element-plus'


import KzxxCard from "@views/zjgl/zjcrk/compoents/kzxxCard";
import LlxxCard from "@views/zjgl/zjcrk/compoents/llxxCard";
import zyxxCard from "@views/zjgl/zjcrk/compoents/zyxxCard";
import jbxxCard from "@views/zjgl/zjcrk/compoents/jbxxCard";

export default defineComponent({
  components: {LlxxCard, KzxxCard,zyxxCard,jbxxCard},
  props: {
    params: {
      required: true,
      type: Object
    },
    kzxxParams: {
      required: true,
      type: Object
    },
    llxxParams: {
      required: true,
      type: Object
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      loading: false,
      editable: false,
      openCollapse: ['1', '2','3'],
      userInfo: vsAuth.getAuthInfo().permission,
      Plus: Plus,
      pageType: props.params.pageType, //页面类型，取决于哪个按钮打开的页面
      formData: {
        XM: null,
        XB: '1',
        SFZH: null,
        GZDW: null,
        XL: null,

        XCSZYBM: null,
        CSZYGZSJ: null,
        ZJLX: null,
        SBZY: null,
        SBZYMX: [],

        ZJLB: null,
        ZJJB: null,

        RKZJBXX: {},

        tableData1: [],
        tableData2: [{LX: '专家工作经历证明', WJMC: ''}],
        fileTableData: [],
      },

      kzxxParams: props.kzxxParams,
      llxxParams: props.llxxParams,

//树插件数据映射
      cascaderProps: {
        value: 'ZYBM',
        label: 'ZYMC',
        children: 'children',
        expandTrigger: 'hover',
        emitPath: false //只显示末级
      },

      rules: {
        XM: [{
          required: true,
          message: '字段值不可为空',
        }],
        XB: [{
          required: true,
          message: '字段值不可为空',
        }],
        SFZH: [{
          required: false,
          message: '字段值不可为空',
        }],
        GZDW: [{
          required: true,
          message: '字段值不可为空',
        }],
        'RKZJBXX.CSNY': [{
          required: false,
          message: '字段值不可为空',
        }],
        'RKZJBXX.BGYY': [{
          required: true,
          message: '字段值不可为空',
        }],
        'RKZJBXX.BGSM': [{
          required: true,
          message: '字段值不可为空',
        }],
        XL: [{
          required: false,
          message: '字段值不可为空',
        }],

        // XCSZYBM: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // CSZYGZSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJLX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // SBZY: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJLB: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZJJB: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],

        // ZJBH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZC: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BGDH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZZDH: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // DZYX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // TXDZ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // MQSZD: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BYYX: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // SXZY: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // BYSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // CJGZSJ: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZYZG: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZYZGZS: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // ZZZT: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // XZZW: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // pictureupload90411: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        // pictureupload40039: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
      },
      XBOptions: [],
      XLOptions: [],
      XCSZYOptions: [],
      ZJLXOptions: [],
      ZJLBOptions: [],
      ZJJBOptions: [],
      ZCOptions: [],
      ZZZTOptions: [],
      ZJZTOptions: [],

      dialogVisible: false,
      dialogDWXZVisible: false,

    })

    const getFormDate = (ZJBS) => {
      state.loading=true
      axiosUtil.get('/backend/zjgl/zjbggl/selectZjMxByZjbs', {ZJBS}).then((res) => {
        if (res.data && res.data.length > 0) {
          state.loading=false
          state.formData = {...state.formData, ...res.data[0]}
          state.formData.XCSZYBM = res.data[0].XCSZY
          if (state.formData.SBZYMX.length > 0) {
            state.formData.SBZYMX.forEach(item => {
              item.WJID = props.params.id
            })
          }
        }
      })
    }
    const getEditFormDate = (GLZJMXID) => {
      state.loading=true
      axiosUtil.get('/backend/zjgl/zjrkgl/selectRkzj', {GLZJMXID}).then((res) => {
        if (res.data && res.data.length > 0) {
          state.loading=false
          state.formData = {...state.formData, ...res.data[0]}
          if (state.formData.SBZYMX.length > 0) {
            state.formData.SBZYMX.forEach(item => {
              item.WJID = props.params.id
            })
          }
        }
      })
    }
    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state[resList] = res.data
    }

    const instance = getCurrentInstance()
    const validateForm = (type) => {
      if (type === 'submit') {
        state.formData.SHZT = '2'
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            //TODO: 提交表单
            submitForm(type)
          } else {
            console.log(123123)
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
          }
        })
      } else if (type === 'save') {
        state.formData.SHZT = '0'
        submitForm(type)
      }

    }
    const closePage = () => {
      emit('closeForm')
    }
    const submitForm = (type) => {
      let params
      if (props.params.operation.indexOf('add') > -1) {
        let GLZJID = comFun.newId()
        let ZJBS = props.params.ZJBS
        params = {
          GLZJID,
          HDFL: 'BG',
          SQSJ: comFun.getNowTime(),
          CJR: state.userInfo.userLoginName,
          CJSJ: comFun.getNowTime(),
          SHZT: state.formData.SHZT,
          MX: {
            ...state.formData,
            GLZJID, ZJBS
          }
        }
      } else if (props.params.operation.indexOf('edit') > -1) {
        params = {
          GLZJID: state.formData.GLZJID,
          XGR: state.userInfo.userLoginName,
          XGSJ: comFun.getNowTime(),
          SHZT: state.formData.SHZT,
          MX: {
            ...state.formData,
          }
        }
      }
      if (type == 'submit') {
        params.SHR = state.userInfo.userLoginName
        params.SHRQ = comFun.getNowTime();
      }
      console.log(params)
      axiosUtil.post('/backend/zjgl/zjbggl/saveBgzj', params).then(res => {
        console.log(res)
        if (res.message === 'success') {
          emit('closeForm')
          ElMessage({
            message: `${type === 'submit' ? '提交' : '保存'}成功`,
            type: 'success',
          })
        }
      })

    }
    const resetForm = () => {
      instance.proxy.$refs['vForm'].resetFields()
    }
    const closeDialog = () => {
      state.dialogVisible = false
    }
    const getCheck = (e, ZZY) => {
      console.log(e, ZZY)
      let res = []
      e.forEach(item => {
        res.push({
          ZJXGZYBS: comFun.newId(),
          WJID: props.params.id,
          SFZZY: item.ZYBM === ZZY ? '1' : '0',
          ZYBM: item.ZYBM,
          ZYMC: item.ZYMC
        })
      })
      state.formData.SBZYMX = res
      state.dialogVisible = false

    }
    const getDWCheck = (value) => {
      state.formData.GZDW = value.ORGNA_NAME
      state.dialogDWXZVisible = false
    }
//初始化专家专业列表
    const getZjZyList = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy').then((res) => {
        var treeData = comFun.transData(res.data, 'ZYBM', 'FZYBM', 'children');
        state.XCSZYOptions = treeData
      });
    }

    const xcszyHandler = (e) => {
      console.log(e);
    }


    onMounted(() => {
      getDMBData('XB', 'XBOptions')
      getDMBData('XL', 'XLOptions')
      // getDMBData('XCSZY', 'XCSZYOptions')
      getDMBData('ZJLX', 'ZJLXOptions')
      getDMBData('ZJLB', 'ZJLBOptions')
      getDMBData('ZJJB', 'ZJJBOptions')
      getDMBData('ZC', 'ZCOptions')
      getDMBData('ZZZT', 'ZZZTOptions')
      getDMBData('ZJZT', 'ZJZTOptions')
      //初始化专家专业
      getZjZyList();

      state.editable = props.params.editable
      state.formData.GLZJMXID = props.params.id
      if (props.params.operation.indexOf('add') > -1) {
        getFormDate(props.params.ZJBS)
      } else {
        getEditFormDate(props.params.id)
      }
    })
    watch(() => state.formData.SBZYMX, (newValue) => {
      let inputShow = []
      newValue.forEach(item => {
        if (item.SFZZY === '1') {
          inputShow.push(item.ZYMC + '(主专业)')
        } else {
          inputShow.push(item.ZYMC)
        }

      })
      console.error(inputShow)
      state.formData.RKZJBXX.SBZY = inputShow.join('、')
      state.formData.SBZY = inputShow.join('、')
    },{deep: true})
    return {
      ...toRefs(state),
      validateForm,
      submitForm,
      resetForm,
      closePage,
      closeDialog,
      getCheck,
      getDWCheck,
      getEditFormDate,
      getZjZyList,
      xcszyHandler
    }
  }
})

</script>


<style scoped>
/deep/ .el-input.is-disabled .el-input__wrapper {
  background-color: unset;
}

/deep/ .el-collapse-item__header {
  background-color: #dcdfef !important;
  padding-left: 10px;
  margin-bottom: 10px;
  height: 30px;
  font-size: 16px;
  color: black !important;
}

:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}
</style>
