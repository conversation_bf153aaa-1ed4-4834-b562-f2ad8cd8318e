<template>
    <div>
        <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
                 label-width="160px"
                 size="default" v-loading="loading" @submit.prevent>
            <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px">
                基本信息
            </div>

            <el-row :gutter="0" class="grid-row">
                <el-col :span="8" class="grid-cell">
                    <el-form-item label="考核时间：" prop="PJZQ">
                        <el-select v-model="formData.PJZQ" class="full-width-input"
                                   :disabled="!editable||operation!='add'" @change="getPjmxkfList"
                                   clearable>
                            <el-option v-for="(item, index) in KHSJOptions" :key="index" :label="item.ZQMC"
                                       :value="item.PJZQID" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="考核专业：" prop="PJZYBM">
                        <el-select v-model="formData.PJZYBM" class="full-width-input"
                                   :disabled="!editable||operation!='add'" @change="changeKhzy" filterable
                                   clearable>
                            <el-option v-for="(item, index) in KHZYOptions" :key="index" :label="item.ZYMC"
                                       :value="item.PJZYID" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="8" class="grid-cell">
                    <el-form-item label="建设单位：" prop="PJBMMC">
                        <el-input v-model="formData.PJBMMC"
                                  type="text" clearable
                                  :disabled="true"/>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="grid-cell no-border-bottom">
                    <el-form-item label="备注：" prop="BZ">
                        <el-input v-model="formData.BZ" :rows="3"
                                  type="textarea" clearable
                                  :disabled="!editable"/>
                    </el-form-item>
                </el-col>

            </el-row>

            <el-divider/>

            <div style="display: flex;gap: 20px">
                <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px">
                    项目明细
                </div>
            </div>

            <div style="margin-top: 20px">
                <div style="text-align: right;padding-bottom: 10px">
                    <el-button ref="button91277" @click="getHZMXList" type="primary" v-if="editable">汇总计算</el-button>
                </div>
                <el-table ref="datatable91634" :data="formData.KMKHHZList" height="calc(300px)" class="lui-table"
                          :border="true" :show-summary="false" size="default" :stripe="false"
                          :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                    <el-table-column type="index" width="80" fixed="left" label="序号" align="center"/>
                    <el-table-column prop="CBSDWQC" label="承包商名称" align="center"
                                     :show-overflow-tooltip="true" min-width="200">
                    </el-table-column>
                    <el-table-column prop="DWMC" label="队伍名称" align="center"
                                     :show-overflow-tooltip="true" min-width="160">
                    </el-table-column>
                    <el-table-column prop="XMMC" label="项目名称" align="center"
                                     :show-overflow-tooltip="true" min-width="200">
                    </el-table-column>
                    <el-table-column prop="ZXMMC" label="单项工程名称" align="center"
                                     :show-overflow-tooltip="true" min-width="200">
                    </el-table-column>

                    <el-table-column prop="EJDWPJ" label="二级单位评价" align="center">
                        <el-table-column prop="EJDW_GS" label="问题个数" align="center"
                                         min-width="100">
                            <template #default="scope">
                                <el-button size="small" class="lui-table-button" type="primary"
                                           @click="viewRow(scope.row,'JSDW')">{{scope.row.EJDW_GS}}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column prop="EJDW_JF" label="记分" align="center"
                                         min-width="100">
                        </el-table-column>
                    </el-table-column>
                    <el-table-column prop="ZYBMPJ" label="专业部门评价" align="center">
                        <el-table-column prop="ZYBM_GS" label="问题个数" align="center"
                                         min-width="100">
                            <template #default="scope">
                                <el-button size="small" class="lui-table-button" type="primary"
                                           @click="viewRow(scope.row,'ZYBM')">{{scope.row.ZYBM_GS}}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column prop="ZYBM_JF" label="记分" align="center"
                                         min-width="100">

                        </el-table-column>
                    </el-table-column>
                    <el-table-column prop="AQBMPJ" label="安全部门评价" align="center">
                        <el-table-column prop="AQBM_GS" label="问题个数" align="center"
                                         min-width="100">
                            <template #default="scope">
                                <el-button size="small" class="lui-table-button" type="primary"
                                           @click="viewRow(scope.row,'AQBM')">{{scope.row.AQBM_GS}}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column prop="AQBM_JF" label="记分" align="center"
                                         min-width="100">

                        </el-table-column>
                    </el-table-column>
                    <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                        <template #default="scope">
                  <span>
                    <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                    </el-button>
                    <el-button size="small" v-if="editable" class="lui-table-button" type="primary"
                               @click="deleteRow(scope.row)">删除
                    </el-button>
                  </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>


            <!--<div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
                <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
                <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
                <el-button @click="closeForm">返回</el-button>
            </div>-->

        </el-form>

        <el-dialog
                custom-class="lui-dialog"
                :close-on-click-modal="false"
                v-if="dialogXMXZVisible"
                v-model="dialogXMXZVisible"
                title="问题详情"
                z-index="1000"
                top="6vh"
                width="90%">
            <div>
                <xmwtsbQuery v-if="dialogXMXZVisible" :key="XMID" :XMID="XMID" :PJZT="PJZT" :KHPJID="KHPJID"
                             @close="dialogXMXZVisible=false"/>
            </div>
        </el-dialog>

    </div>
</template>

<script>

    import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, computed} from "vue";
    import vsAuth from "@lib/vsAuth";
    import {ElMessage} from "element-plus";
    import axiosUtil from "@lib/axiosUtil";
    import vsfileupload from "@views/components/vsfileupload";
    import comFun from "@lib/comFun";
    import xmwtsbQuery from "@views/khpj/cxtj/xmwtsbQuery.vue";

    export default defineComponent({
        name: '',
        components: {xmwtsbQuery, vsfileupload},
        props: {
            params: {
                type: Object,
                required: true
            },
            value: {
                type: Object,
                default: {}
            }
        },
        setup(props, {emit}) {
            const state = reactive({
                userInfo: vsAuth.getAuthInfo().permission,
                loading: false,
                editable: props.params.editable,
                operation: props.params.operation,
                KHPJID: props.params.id,
                XMID: null,
                PJZT: null,
                formData: {
                    CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
                    CJRXM: vsAuth.getAuthInfo().permission.userName,
                    CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
                    PJBMMC: vsAuth.getAuthInfo().permission.orgnaName,
                    PJBMID: vsAuth.getAuthInfo().permission.orgnaId,
                    CJSJ: comFun.getNowTime(),
                    SHZT: '0',
                    PJLX: 'ZQXKH',
                    KMKHHZList: []
                },
                tableRules: {
                    ZBDF: [{
                        required: true,
                        message: '请输入',
                    }],
                },
                rules: {
                    PJZQ: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                    PJZYBM: [{
                        required: true,
                        message: '字段值不可为空',
                    }],
                },
                KHSJOptions: [],
                KHZYOptions: [],
                EJDWOptions: [],

                showTable: '',

                dialogXMXZVisible: false,
            })
            const getFormData = () => {
                let params = {
                    KHPJID: state.KHPJID
                }
                state.loading = true
                axiosUtil.get('/backend/sckhpj/khmxtb/selectKhtbhz', params).then((res) => {
                    state.formData = res.data
                    state.loading = false
                })

            }
            //提报问题汇总
            const getHZMXList = () => {
                //保存基础信息
                let saveParams = {
                    ...state.formData,
                    KHPJID: state.KHPJID,
                    XGRZH: state.userInfo.userLoginName,
                    XGSJ: comFun.getNowTime()
                }
                state.loading = true
                axiosUtil.post('/backend/sckhpj/khmxtb/saveYjpjKhpj', saveParams).then(res => {
                    let params = {
                        PJZQID: state.formData.PJZQ,
                        PJZYBM: state.formData.PJZYBM,
                        PJBMID: vsAuth.getAuthInfo().permission.orgnaId,
                        KHPJID: state.KHPJID,
                        userName: vsAuth.getAuthInfo().permission.userName,
                        userLoginName: vsAuth.getAuthInfo().permission.userLoginName
                    }
                    axiosUtil.get('/backend/sckhpj/khmxtb/khWthz', params).then((res) => {
                        state.formData.KMKHHZList = res.data
                        state.loading = false
                    })
                })
            }

            const deleteRow = (row) => {
                axiosUtil.del('/backend/sckhpj/khmxtb/delKhmxtbxq?DXMPJID=' + row.DXMPJID).then((res) => {
                    getFormData();
                })
            }

            const viewRow = (row, PJZT) => {
                state.dialogXMXZVisible = true
                state.XMID = row.XMID
                state.PJZT = PJZT
            }


            const saveData = (val) => {

                // 转化流程页面传参
                let type = val === '1' ? 'submit' : 'save'
                return new Promise(async (resolve, reject) => {
                    if (type === 'save') {
                        submitForm(type)
                    } else {
                        let res
                        try {
                            res = await validateForm()
                        } catch (resMsg) {
                            ElMessage.error(resMsg)
                        }
                        if (res) {
                            let res2 = await submitForm(type)
                            if (res2) {
                                resolve(true)
                            } else {
                                reject('保存失败')
                                return;
                            }
                        } else {
                            reject('校验未通过')
                            return;
                        }
                    }

                    let data = {
                        ...props.value,
                        businessId: props.value.businessId || props.params.id,
                        processInstanceName: state.formData.PJBMMC + state.formData.PJZQMC + state.formData.PJZYMC + '-考核明细审批',
                        conditionStr: "lczx=1",
                    };
                    state.loading = false
                    emit("update:value", data);
                    resolve(true)
                })

            }

            const submitForm = (type) => {
                return new Promise((resolve, reject) => {
                    let params = {
                        ...state.formData,
                        KHPJID: state.KHPJID,
                        XGRZH: state.userInfo.userLoginName,
                        XGSJ: comFun.getNowTime()
                    }
                    if (type === 'submit') {
                        params.SHZT = '1'
                    }
                    console.log(params)
                    state.loading = true
                    axiosUtil.post('/backend/sckhpj/khmxtb/saveYjpjKhpj', params).then(res => {
                        //ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
                        closeForm()
                        state.loading = false
                      resolve(true)
                    })
                })
            }


            const instance = getCurrentInstance()
            const validateForm = () => {
                return new Promise(resolve => {
                    instance.proxy.$refs['vForm'].validate(valid => {
                        if (valid) {
                            resolve(true)
                        } else {
                            ElMessage({
                                message: '请完善页面信息',
                                type: 'error',
                            })
                            resolve(false)
                        }
                    })
                })
            }

            const changeKhzy = (value) => {
                if (value) {
                    let zyqk = state.KHZYOptions.find(item => item.PJZYID === value)
                    state.formData.PJZYMC = zyqk.ZYMC
                    getPjmxkfList()
                } else {
                    state.formData.KMKHHZList = []
                }
            }

            const getPjmxkfList = () => {
                state.KHSJOptions.forEach(item => {
                    if(item.PJZQID === state.formData.PJZQ) {
                        state.formData.PJZQMC = item.ZQMC;
                    }
                })
                if (state.formData.PJZQ && state.formData.PJZYBM) {
                    //根据专业周期、专业id、部门id查询考核
                    let params = {
                        PJZQID: state.formData.PJZQ,
                        PJZYBM: state.formData.PJZYBM,
                        PJBMID: vsAuth.getAuthInfo().permission.orgnaId,
                    }
                    axiosUtil.get('/backend/sckhpj/khmxtb/selectKhpjByParams', params).then((res) => {
                        if (res.data.length > 0) {
                            state.formData.PJZYMC = '';
                            state.formData.PJZYBM = '';
                            state.formData.PJZQ = '';
                            ElMessage.error('该考核填报已存在，请重新选择考核周期和专业！');
                        }
                    })
                } else {
                    state.formData.KMKHHZList = []
                }
            }

            const getEjdwList = () => {
                axiosUtil.get('/backend/common/selectEjdwList', null).then(res => {
                    state.EJDWOptions = res.data || []
                })
            }

            const getKhsjList = () => {
                axiosUtil.get('/backend/sckhpj/khmxtb/selectKhqjList', null).then(res => {
                    state.KHSJOptions = res.data || []
                })
            }

            const getKhzyList = () => {
                axiosUtil.get('/backend/sckhpj/khmxtb/selectKhzyList', null).then(res => {
                    state.KHZYOptions = res.data || []
                })
            }


            const closeForm = () => {
                emit('close')
            }

            onMounted(() => {
                if (props.params.operation !== 'add') {
                    getFormData()
                }
                getEjdwList()
                getKhsjList()
                getKhzyList()
            })

            return {
                ...toRefs(state),
                saveData,
                closeForm,
                getKhsjList,
                getKhzyList,
                changeKhzy,
                getPjmxkfList,
                getHZMXList,
                deleteRow,
                viewRow

            }
        }

    })
</script>

<style scoped>

</style>
