<template>
    <el-container>
      <el-main class="vs-workbench-main-scoped">
        <!-- 查询条件区域 -->
        <el-row :gutter="15" style="padding-bottom:12px">
          <el-col :span="24">
            <el-input
              v-model="params.inputZymc"
              placeholder="回车按照名称搜索专业或管理部门"
              style="width:250px"
              @keyup.enter="queryData"
              clearable
            ></el-input>
            <el-button type="primary" style="margin-left:10px" @click="queryData">查询</el-button>
           <!-- <el-button type="warning" style="margin-left:10px" @click="clearSelect">清空</el-button>-->
            <el-button type="success" style="margin-left:10px" @click="confirmSelect">确定</el-button>
          </el-col>
        </el-row>
      <!--数据表格-->
      <el-row>
        <!-- 表格 -->
        <el-table
            :data="tableData"
            border="1px"
            width="100%"
            height="calc(100vh - 260px)"
            row-key="YWBM"
            ref="accountTable"
            v-loading="params.loading"
            @row-click="onSelect"
            @row-dblclick="confirmSelect"
            :tree-props="{children: 'children'}"
            highlight-current-row
            :header-cell-style="{color: '#000000', fontSize: '14px'}"
        >
            <el-table-column
                type="index"
                label="序号"
                align="center"
                width="50"
            ></el-table-column>
            <el-table-column
                prop="YJYWMC"
                label="大类业务名称"
                header-align="center"
                width="300"
                align="left">
            </el-table-column>
            <el-table-column
                prop="EJYWMC"
                label="中类业务名称"
                header-align="center"
                width="250"
                align="left">
            </el-table-column>
            <el-table-column
                prop="SJYWMC"
                label="小类业务名称"
                header-align="center"
                width="300"
                align="left">
            </el-table-column>
            <el-table-column
                prop="GLBMMC"
                label="管理部门名称"
                header-align="center"
                width="260"
                align="left">
            </el-table-column>
            <el-table-column type="selection" width="50" align="center" :reserve-selection="true" :selectable="selectable"></el-table-column>
        </el-table>
      </el-row>
      </el-main>
    </el-container>
</template>

<script setup>
import { ElMessage, ElMessageBox, ElNotification } from "element-plus"
import { reactive, getCurrentInstance, onMounted } from "vue"
import axiosUtil from "../../../lib/axiosUtil"

let emits = defineEmits(['handleConfirm'])
let _this = getCurrentInstance()
let props = defineProps({
    model: String,
    kj: {
    type: Boolean,
    default: true
    },
    isMulti: {
        type: Boolean,
        default: true
    },
    form: {
        type: String,
        default: ''
    },
    cbsdwbs:String,
    gclbdm:String,
    GUID:String
})
let params = reactive({
    inputZymc : "",
    currentPage: 1, //初始页
    pagesize: 10,
    loading:false,      //表数据
    formLabelWidth: "100px",
    tableStyle: "width:100%;height:calc(100vh - 310px);",
    modelTemesMap: {}
})
let tableData = reactive([])
const selectable = (row,index) =>{
    return true
}
const handleSizeChange = size => {
    params.pagesize = size;
}
const handleCurrentChange = currentPage => {
    params.currentPage = currentPage;
}
const selectMore = val =>{
    for(let i = 0;i < val.length;i++){
    if(val[i].children){
        _this.$refs.accountTable.toggleRowSelection(val[i]);
        val.splice(i,1);
    }
    }
}
const loadData =() => {
    let param = {
    zymc : params.inputZymc ,
    gclbdm:params.gclbdm,
    CBSDWBS:params.cbsdwbs
    };
    axiosUtil.get('/sldwgl/mbgl/queryDwzy', param).then(res =>{
        if(res.data.meta.success){
            let rows = res.data.data;
            tableData.length = 0;
            tableData.push(...rows)
            params.total=res.data.data.total
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
    // let result = await util.get('/sldwgl/mbgl/queryDwzy', param, this.model);
    // if(result.data.meta.success){
    //     let rows = result.data.data;
    //     this.tableData = rows;
    //     this.total=result.data.data.total
    // }else{
    // this.$message({type: "warning", message: result.data.meta.message});
    // }
}
/**
 * json格式转树状结构
 * @param   {json}      json数据
 * @param   {String}    id的字符串
 * @param   {String}    父id的字符串
 * @param   {String}    children的字符串
 * @return  {Array}     数组
 */
const transData = (a, idStr, pidStr, childrenStr) =>{
    let r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
    for(; i < len; i++){
        a[i]['isparent'] = false;
        hash[a[i][id]] = a[i];
    }
    for(; j < len; j++){
        let aVal = a[j], hashVP = hash[aVal[pid]];
        if(hashVP){
            !hashVP[children] && (hashVP[children] = []);
            hashVP[children].push(aVal);
        }else{
            r.push(aVal);
        }
    }
    return r;
}
const setIsParent = (arr) => {
    for(let j=0; j < arr.length; j++){
        if(arr[j].children && arr[j].children.length > 0){
        arr[j].isparent = true;
        setIsParent(arr[j].children);
        }
    }
}
/**
 * 序号
 */
const indexMethod = (index) => {
    return index + 1
}
/**
 * @Params: {{Params}}
 * @Description: 获取数据
 */
const queryData = () => {
    loadData()

}
const checkCheck = (value) =>{}
const confirmSelect = () =>{
    params.loading = true;
    let selectedRow = _this.$refs.accountTable.selection;
    let dataId = '';
    for(let i in  selectedRow){
        if(selectedRow[i].hasOwnProperty('children')){
            ElMessage({
                message: '请选择子类专业!',
                type: 'warning'
            })
        return;
        }
        if(!props.kj){
        if(selectedRow.length>1){
            let data = selectedRow[i]['GUID'];
            let newData = data.substring(0,4);
            if(dataId != newData){
                ElMessage({
                    message: '请选择同一父类的子专业!',
                    type: 'warning'
                })
                return;
            }
        }
        }
    }
    if(selectedRow.length == 0){
        ElNotification({title: "提示", message: "请先选择行！", type: "info", duration: 2000, offset: 90})
        return;
    }
    emits('handleConfirm', selectedRow, this.from)
    params.loading = false;
}
const clearSelect = () =>{
    ElMessageBox.confirm('您确实要清除父页面的专业信息?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
    }).then(() => {
        emits('handleClear', this.from);
    }).catch(() => {
        ElMessage({
            message: '已取消',
            type: 'info'
        })
    });
    // this.$confirm('您确实要清除父页面的专业信息?', '提示', {
    // confirmButtonText: '确定',
    // cancelButtonText: '取消',
    // type: 'warning'
    // }).then(() => {
    //     this.$emit('handleClear', this.from);
    // }).catch(() => {});
}
onMounted(() =>{
    loadData()
})
</script>

<style scoped>
.dialog-footer {
  text-align: center;
}

::v-deep .el-form-item__content .el-input__inner .el-textarea__inner {
  width: 98%;
}
.tb-edit .el-input {
    display: none
}
.tb-edit .current-row .el-input {
    display: block
}
.tb-edit .current-row .el-input+span {
    display: none
}
</style>