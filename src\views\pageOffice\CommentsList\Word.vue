<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');
const commentText = ref("");
function OnPageOfficeCtrlInit() {
	pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function Save() {
	pageofficectrl.SaveFilePage = "/CommentsList/save";
	pageofficectrl.WebSave();
}

function AfterDocumentOpened() {
	refreshList();
}

function insertComment() {
	pageofficectrl.word.InsertComment(commentText.value);

}
//获取当前痕迹列表
function refreshList() {
	let commentsListJson = JSON.parse(pageofficectrl.word.CommentsAsJson)
	document.getElementById("ul_Comments").innerHTML = "";
	for (let item of commentsListJson) {
		let itemDate = dateFormat(item.date, 'yyyy-MM-dd HH:mm');
		document.getElementById("ul_Comments").innerHTML += "<li><a href='#' onclick='goToComment(" + item.id + ")'>"
			+ itemDate + "<br />[" + item.author + "]:" + pageofficectrl.word.GetTextFromComment(parseInt(item.id)) + "</a></li>"
	}
}

//GMT时间格式转换为CST
function dateFormat(date, format) {
	var date = new Date((date - 25569) * 86400 * 1000);
	date.setHours(date.getHours() - 8);
	var o = {
		'M+': date.getMonth() + 1, //month
		'd+': date.getDate(), //day
		'H+': date.getHours(), //hour
		'm+': date.getMinutes(), //minute
		's+': date.getSeconds(), //second
		'q+': Math.floor((date.getMonth() + 3) / 3), //quarter
		'S': date.getMilliseconds() //millisecond
	};

	if (/(y+)/.test(format))
		format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));

	for (var k in o)
		if (new RegExp('(' + k + ')').test(format))
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));

	return format;
}

//定位到当前批注
function goToComment(index) {
	pageofficectrl.word.SelectComment(index);
}
//刷新列表
function refresh_click() {
	refreshList();
}

function openFile() {
	return request({
		url: '/CommentsList/Word',
		method: 'get',
	})
}

onMounted(() => {
	// 请求后端打开文件
	openFile().then(response => {
		poHtmlCode.value = response;
	});
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.goToComment=goToComment;
	window.POPageMounted = { OnPageOfficeCtrlInit, Save, AfterDocumentOpened};//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<div style=" width:1300px; height:700px;">
			<div id="Div_Comments" style=" float:left; width:200px; height:700px; border:solid 1px red;">
				<h3>批注列表</h3>
				<input type="button" name="refresh" value="刷新" @click="refresh_click()" />
				<ul id="ul_Comments">

				</ul>
			</div>
			<div style=" width:1050px; height:1200px; float:right;">
				<input id="Button1" type="button" value="插入批注" @click="insertComment()" />
				<input id="Text1" type="text" v-model="commentText" />
				<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
				<div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
			</div>
		</div>
	</div>
</template>
