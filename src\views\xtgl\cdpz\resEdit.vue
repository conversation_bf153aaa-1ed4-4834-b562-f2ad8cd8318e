<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="140px"
           size="default" v-loading="loading" @submit.prevent>
    <el-row :gutter="0" class="grid-row">
      <el-col :span="16" class="grid-cell">
        <el-form-item label="父级资源名称：" prop="RES_PNAME">
          <el-cascader
              v-model="formData.RES_PID"
              :options="pidTree"
              :props="{label: 'RES_NAME',value:'RES_ID',emitPath: false,checkStrictly : true}"
              @change="PIDChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="父级资源代码：" prop="RES_PID">
          <el-input v-model="formData.RES_PID" type="text" placeholder="请输入" clearable :disabled="true">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="资源层级：" prop="RES_FLOOR_NUM">
          <el-input v-model="formData.RES_FLOOR_NUM" type="text" placeholder="请输入" clearable :disabled="true">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell">
        <el-form-item label="资源名称：" prop="RES_NAME">
          <el-input v-model="formData.RES_NAME" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell no-border-bottom">
        <el-form-item label="应用资源编号：" prop="RES_CODE">
          <el-input v-model="formData.RES_CODE" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell no-border-bottom">
        <el-form-item label="资源路径：" prop="RES_PVALUE">
          <el-input v-model="formData.RES_PVALUE" type="text" placeholder="请输入" clearable :disabled="!editable">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell no-border-bottom">
        <el-form-item label="资源类型：" prop="RES_TYPE">
          <el-select v-model="formData.RES_TYPE" placeholder="请选择" :disabled="!editable" clearable>
            <el-option v-for="item in TYPEOptions" :key="item.value" :label="item.label"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell no-border-bottom">
        <el-form-item label="是否管理资源：" prop="IS_MANRES">
          <el-select v-model="formData.IS_MANRES" placeholder="请选择" :disabled="!editable" clearable>
            <el-option v-for="item in MANRESOptions" :key="item.value" :label="item.label"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" class="grid-cell no-border-bottom">
        <el-form-item label="排序号：" prop="RES_ORDER">
          <el-input v-model="formData.RES_ORDER" type="text" placeholder="请输入" clearable :disabled="true"
          @input="formData.RES_ORDER=formData.RES_ORDER.replace(/[^\d]/g, '')">
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="16" class="grid-cell no-border-bottom">
        <el-form-item label="授权角色：" prop="roleList">
          <el-select
              v-model="formData.roleList"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择角色">
            <el-option
                v-for="item in roleList"
                :key="item.ROLE_ID"
                :label="item.ROLE_NAME"
                :value="item.ROLE_ID"
            />
          </el-select>
        </el-form-item>
      </el-col>

    </el-row>

    <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
      <el-button type="success" @click="saveData()" v-if="editable">保存</el-button>
      <el-button @click="closeForm">返回</el-button>
    </div>
  </el-form>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "../../../lib/vsAuth";
import axiosUtil from "../../../lib/axiosUtil";
import comFun from "../../../lib/comFun";
import {ElMessage} from "element-plus";


export default defineComponent({
  name: '',
  components: {},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      editable: props.params.editable,
      resId: props.params.id,
      formData:{
        RES_PNAME:null,
        RES_PID:null,
        RES_FLOOR_NUM: null,
        RES_NAME: null,
        RES_ORDER: null,
        RES_CODE: null,
        RES_PVALUE: null,
        RES_ICON: 'fa fa-bars',
        RES_TYPE:'1',
        IS_MANRES:'0',

        roleList:[]

      },
      rules: {
        RES_NAME: [{
          required: true,
          message: '字段值不可为空',
        }],
        RES_TYPE: [{
          required: true,
          message: '字段值不可为空',
        }],
        IS_MANRES: [{
          required: true,
          message: '字段值不可为空',
        }],
        RES_ORDER: [{
          required: true,
          message: '字段值不可为空',
        }],
        RES_CODE: [{
          required: true,
          validator: async (rule, value, callback) => {
            if (!value) {
              callback(new Error('字段值不可为空'))
            } else {
              let isCF = await checkProp(value,'RES_CODE')
              if(Boolean(isCF.data)){
                callback()
              }else {
                callback(new Error('资源编码不能重复'))
              }
            }
          },
        }],
      },
      TYPEOptions:[{label:'菜单',value:'1'},{label:'链接',value:'2'}],
      MANRESOptions:[{label:'系统管理资源',value:'1'},{label:'应用管理资源',value:'2'},{label:'普通业务资源',value:'0'}],
      loading: false,
      pidTree:[],
      pidList:[],
      roleList: []

    })

    const getFormData = () => {
      let params = {
        resId: state.resId
      }
      state.loading = true
      axiosUtil.get('/backend/common/cdgl/selectResInfoById', params).then((res) => {
        state.formData = res.data
        state.formData.roleList = state.formData.roleList.map(item=>item.ROLE_ID)
        state.formData.RES_PNAME=props.params.pNode.RES_NAME
        getResList()
      });
    }

    const treeData = (data, id, parentId, childName, root) => {
      let cloneData = data
      return cloneData.filter((father) => {
        father[childName] = cloneData.filter((child) => {
          return father[id] === child[parentId]
        })
        return father[parentId] === root
      })
    }


    const getResList = () => {
      state.loading = true
      axiosUtil.get('/backend/common/cdgl/selectResListByPid', {}).then((res) => {
        let data=res.data || []
        data=data.filter(item=> item.RES_ID!==state.resId)
        state.pidList=JSON.parse(JSON.stringify(data))
        state.pidTree = [{
          RES_ID:'0',
          RES_NAME: '根节点',
          children: treeData(data,'RES_ID','RES_PID','children','0')

        }]
        state.loading = false
      });
    }

    const checkProp = (value,prop) => {
      let params={
        resId: state.resId,
        value: value,
        prop: prop
      }
      return axiosUtil.get('/backend/common/cdgl/checkProp', params)
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if(valid){
            resolve(true)
          }else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const submitForm = () => {
      let params
      if(props.params.operation === 'add'){
        params={
          RES_ID: props.params.id,
          ...state.formData,
          CREATOR: state.userInfo.userId,
          CREATE_TIME: comFun.getNowTime(),
          ZT: 'add'
        }
      }else {
        params={
          ...state.formData
        }
        console.log(props.params.pNode)
      }
      axiosUtil.post('/backend/common/cdgl/saveResInfo', params).then((res) => {
        ElMessage.success('保存成功')
      });
    }

    const saveData = () => {
      validateForm().then(res=>{
        if(res){
          submitForm()
        }
      })
    }

    const getRoleList = () => {
      axiosUtil.get('/backend/common/selectRoleList', null).then((res) => {
        state.roleList = res.data
      });
    }

    const closeForm = () => {
      emit('close')
    }

    const PIDChange = (value) => {

    }

    onMounted(() => {
      getRoleList()
      if (props.params.operation !== 'add') {
        getFormData()
      }else {
        state.formData.RES_FLOOR_ID=props.params.pNode.RES_FLOOR_ID+','+props.params.id
        state.formData.RES_PID=props.params.pNode.RES_ID
        state.formData.RES_PNAME=props.params.pNode.RES_NAME
        state.formData.RES_FLOOR_NUM=(Number(props.params.pNode.RES_FLOOR_NUM)+1)+''
        state.formData.RES_ORDER=props.params.PXH
        getResList()
      }
    })

    return {
      ...toRefs(state),
      saveData,
      closeForm,
      PIDChange

    }
  }

})
</script>

<style scoped>
:deep(.required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}

:deep(.is-required .el-form-item__label):before {
  content: "*";
  color: #F56C6C;
  margin-right: 4px;
}
</style>
