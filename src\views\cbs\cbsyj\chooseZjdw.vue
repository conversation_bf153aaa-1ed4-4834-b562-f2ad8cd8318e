<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button type="primary" @click="getDataList">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
            <el-button type="primary" class="lui-button-add" @click="addData">
              <el-icon>
                <Plus/>
              </el-icon>
              新增
            </el-button>
            <el-button type="primary" @click="exportData">
              <el-icon>
                <Upload/>
              </el-icon>
              导出
            </el-button>
          </div>
        </el-col>

        <el-col :span="2" class="grid-cell" style="margin-left: auto">
          <el-button type="primary" @click="submitCheck">
            <el-icon>
              <Check/>
            </el-icon>
            确定
          </el-button>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 300px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      @selection-change="handleSelectionChange"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="selection" header-align="center" align="center"
                               :selectable="(row) =>row.SYZT !== '1'"
                               width="80"></el-table-column>
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="QYMC" label="企业名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="DWMC" label="队伍名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="DWBM" label="编码" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="CJSJ" label="创建时间" align="center"
                               :show-overflow-tooltip="true" width="180"></el-table-column>
              <el-table-column prop="SHZT" label="状态" align="center"
                               :show-overflow-tooltip="true" width="100">
                <template #default="{row}">
                  <el-tag class="ml-2" type="success" v-if="row.SYZT == '1'">已使用</el-tag>
                  <el-tag class="ml-2" type="warning" v-else>未使用</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="180" fixed="right">
                <template #default="{row}">
                  <el-button size="small" class="lui-table-button" type="primary" @click="editRow(row)">编辑
                  </el-button>
                  <el-button v-if="row.SYZT !== '1'" size="small" class="lui-table-button" type="primary" @click="deleteRow(row)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogZJDWVisible"
        v-model="dialogZJDWVisible"
        title="新增队伍"
        @closed="dialogZJDWVisible=false"
        z-index="1000"
        top="5vh"
        append-to-body
        width="800px">
      <div>
        <chooseDwqy :zyOptions="zyOptions" v-if="dialogZJDWVisible" @sure="sure"
                    :tableData="checkList" @close="dialogZJDWVisible=false"/>
      </div>
    </el-dialog>


    <el-dialog
        v-model="dialogVisible"
        title="数据维护"
        width="30%"
        custom-class="lui-dialog">
      <el-form
          class="lui-card-form"
          ref="saveFormRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
          size="default"
          status-icon>
        <el-row :gutter="0" class="grid-row">
          <el-col :span="24" class="grid-cell">
            <el-form-item label="队伍名称" prop="DWMC" >
              <el-input v-model="formData.DWMC" />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell">
            <el-form-item label="队伍编码" prop="DWBM">
              <el-input v-model="formData.DWBM" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
            <span class="dialog-footer">
              <el-button size="default" @click="closeForm">取消</el-button>
              <el-button size="default" type="primary" :disabled="confirmLoading" @click="saveForm">
                确定
              </el-button>
            </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Check, Plus, Search, Upload} from "@element-plus/icons-vue";
import vsAuth from "@lib/vsAuth";
import chooseDwqy from "@views/cbs/cbsyj/chooseDwqy";
import {deleteZjdwglRemove, getZjdwglExport} from "@src/api/sccbsgl";


export default defineComponent({
  name: '',
  components: {Check, Search, Upload, Plus,chooseDwqy},
  props: {
    zyOptions: {
      type: Array,
      default: []
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      confirmLoading: false,
      listQuery: {},
      tableData: [],
      params: {},
      dialogVisible: false,
      dialogZJDWVisible: false,
      defaultForm:{
        DWWYBS: null,
        DWMC: null,
        DWBM: null,
        SYZT: '0',
        CJRZH: null,
        CJRXM: null,
        CJDWID: null,
        CJSJ: null,
        XGRZH: null,
        XGSJ: null,
        SHZT: '0',//审核状态：0保存；1提交；2审核通过；,
        DELETE_FLAG: '0'//删除标志  删除：1；默认0
      },
      formData:{

      },
      rules:{
        DWMC: [
          { required: true, message: '请输入队伍名称', trigger: 'blur' },
        ],
        DWBM: [
          { required: true, message: '请输入队伍编码', trigger: 'blur' },
          {
            trigger: "blur",
            asyncValidator: () => {
              return new Promise((r, j) => {
                axiosUtil.get('/backend/sccbsgl/zsjgl/zjdwgl/queryDwbmUnique', {dwbm: state.formData.DWBM, dwwybs: state.formData.DWWYBS}).then(res => {
                  if (res.data.length > 0) {
                    j("队伍编码已存在")
                  } else {
                    r()
                  }
                }).catch(err => {
                  j(err)
                })
              })
            },
          },
        ],
      },
      checkList: []
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        orgid: state.userInfo.orgnaId,
      }
      axiosUtil.get('/backend/sccbsgl/zsjgl/zjdwgl/query', params).then((res) => {
        state.tableData = res.data.map(x => {
          return {
            unitName: x.DWMC,
            dwbm: x.DWBM,
            id: x.DWWYBS,
            zsjid: x.DWWYBS,
            specials: [],
            specialbk: [],
            ...x
          }
        })
      });
    }

    // 新增
    const addData = () => {
      state.formData={
        ...state.defaultForm
      };
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.formData = row;
      state.dialogVisible = true
    }

    const deleteRow = (row) => {
      ElMessageBox.confirm('是否确认删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteZjdwglRemove({id: row.DWWYBS, status: row.SYZT}).then(res => {
          ElMessage({
            type: 'success',
            message: '删除成功!'
          });
          getDataList();
        }).catch(e => {
          ElMessage({
            type: 'error',
            message: '删除失败!'
          });
        }).finally(() => {
        })
      }).catch(() => {
      });
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['saveFormRef'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const saveForm = () => {
      validateForm().then(res => {
        if (res) {

          let params={
            ...state.formData
          }
          let type = params.DWWYBS? 'edit': 'add'
          if(type == 'add'){
            params.CJSJ = comFun.getNowTime();
            params.CJRXM = state.userInfo.userName;
            params.CJRZH = state.userInfo.userLoginName;
            params.DWWYBS = comFun.newId()
            params.CJDWID = state.userInfo.orgnaId
            params.SYZT = '0'
          }else{
            params.XGSJ = comFun.getNowTime();
            params.XGRZH = state.userInfo.userLoginName;
          }
          state.confirmLoading=true
          axiosUtil.post('/backend/sccbsgl/zsjgl/zjdwgl/save',params).then(res=>{
            ElMessage.success('保存成功')
            getDataList()
            state.dialogVisible=false
            state.confirmLoading=false
          })

        }
      })
    }

    const handleSelectionChange = (val) => {
      state.checkList=val
    }
    const submitCheck = () => {
      if(state.checkList.length===0){
        ElMessage.warning('请选择队伍！')
        return
      }
      state.dialogZJDWVisible=true
    }

    const sure = (value) => {
      emit('sure',value)
      state.dialogZJDWVisible=false
    }

    const exportData = ()=>{
      const params = {
        ...state.listQuery,
        orgid: state.userInfo.orgnaId,
      }
      getZjdwglExport(params,`子级单位信息${new Date().getTime()}.xlsx`);
    }

    const closeForm = () => {
      state.dialogVisible = false;
      getDataList();
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      editRow,
      addData,
      closeForm,
      saveForm,
      handleSelectionChange,
      submitCheck,
      sure,
      deleteRow,
      exportData

    }
  }

})
</script>

<style scoped>

</style>
