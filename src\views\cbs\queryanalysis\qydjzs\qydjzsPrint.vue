<template>
  <div v-loading="loading">
    <div class="button-context">
      <el-button size="default" type="primary" @click="exportPdf">导出</el-button>
<!--      <el-button size="default" type="primary" v-print="'#page-context'">打印</el-button>-->
    </div>
    <div style="padding: 100px">
      <div id="page-context" class="context">
        <div class="context-page">
          <div class="title-text">市场队伍资质证书</div>
          <div class="describe-text"></div>
          <div class="qrcode">
            <QrcodeVue :size="220" :value="qrcodeData" level="H"/>
          </div>
<!--          <div class="tip-text">*手机端可登录石化办公或使用微信“扫一扫“扫描以上二维码查证验真</div>-->
<!--          <div class="tip-text">*电脑端通过外网：**************:8081/s1d或</div>-->
<!--          <div class="tip-text">胜利油田局城网：http://dvgl.slof.com：8091/查证验真</div>-->
          <div class="date-text">发证日期： {{formData.FZRQ}}</div>
        </div>
        <div class="context-page">
          <div class="pageNum-text">第1/1页</div>
          <div class="item-context">
            <div class="item-row">
              <div class="item-label">
                专业大类：
              </div>
              <div class="item-text" style="font-weight: bolder">
                {{formData.DLZYMC}}
              </div>
            </div>

            <div class="item-row">
              <div class="item-label">
                企业名称：
              </div>
              <div class="item-text">
                {{formData.CBSDWQC}}
              </div>
            </div>

            <div class="item-row">
              <div class="item-label">
                法人代表：
              </div>
              <div class="item-text">
                {{formData.FDDBRXM}}
              </div>
            </div>

            <div class="item-row">
              <div class="item-label">
                服务区域：
              </div>
              <div class="item-text">
                {{formData.FWZY}}
              </div>
            </div>

<!--            <div class="item-row">-->
<!--              <div class="item-label">-->
<!--                服务专业：-->
<!--              </div>-->
<!--              <div class="item-text">-->
<!--                工业控制自动化设备设施建设与运维，数据服务建设与运维，信息安全建设与运维，信息基础设施建设与运维，信息机房建设与运维，应用系统开发与运维-->
<!--              </div>-->
<!--            </div>-->

            <div class="item-row">
              <div class="item-label">
                下属队伍：
              </div>
              <div class="item-text">
                {{formData.DWLX==='CBS' ? '暂无队伍' : formData.DWMC}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import QrcodeVue from 'qrcode.vue'
import html2Canvas from 'html2canvas';
import JsPDF from 'jspdf';
import axiosUtil from "@lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {QrcodeVue},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  directives: {print},
  setup(props, {emit}) {
    const state = reactive({
      qrcodeData: '',
      formData: {

      },
      loading: false
    })

    const getFormData = () => {
      let params={
        DWYWID: props.params.id,
        DLZYBM: props.params.DLZYBM
      }
      state.loading=true
      axiosUtil.get('/backend/sccbsgl/report/qydjzs/selectQydjzsById', params).then((res) => {
        state.formData=res.data
        if(state.formData.DWLX==='CBS'){
          state.qrcodeData=window.location.origin+'/app/qyjbxxView?id='+props.params.id
        }else if(state.formData.DWLX==='DW'){
          state.qrcodeData=window.location.origin+'/app/dwjbxxView?id='+props.params.id
        }
        state.loading=false
      })
    }




    const exportPdf = () => {
      let html= document.getElementById('page-context');
      html2Canvas(html, {
        allowTaint: false,
        taintTest: false,
        logging: false,
        useCORS: false,
        dpi: window.devicePixelRatio * 1,
        scale: 4 // 按比例增加分辨率
      }).then(canvas => {
        const ctx = canvas.getContext('2d');
        const a4w = 277;
        const a4h = 190; // A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
        const imgHeight = Math.floor(a4h * canvas.width / a4w); // 按A4显示比例换算一页图像的像素高度
        const page = document.createElement('canvas');
        page.width = canvas.width;
        page.height = imgHeight;// 可能内容不足一页
        page.getContext('2d').fillStyle = '#FFFFFF';
        page.getContext('2d').fillRect(0, 0, page.width, page.height)
        page.getContext('2d').putImageData(ctx.getImageData(0, 0, canvas.width, canvas.height), 0, 0);
        const pdf = new JsPDF('l', 'mm', 'a4'); // A4纸，纵向
        pdf.addImage(page.toDataURL('image/jpeg', 1.0), 'JPEG', 10, 10, a4w, Math.min(a4h, a4w * page.height / page.width))
        pdf.save('导出' + '.pdf')
      })
    }



    onMounted(() => {
      getFormData()
    })

    return {
      ...toRefs(state),
      exportPdf,

    }
  }

})
</script>

<style scoped>
.context{
  display: flex;
  color: black;
  font-family: 宋体;
}
.button-context{
  position: absolute;
  right: 10px;
  top: 80px;
}
.context-page{
  flex: 1;
  min-height: 750px;
  border: 1px solid black;
  display: flex;
  align-items: center;
  flex-direction: column;
  position: relative;
}
.title-text{
  font-size: 55px;
  font-weight: bolder;
  letter-spacing: 10px;
  margin-top: 150px;
}
.describe-text{
  font-size: 21px;
  margin-top: 40px;
}
.tip-text{
  margin-top: 10px;
  color: red;
}
.qrcode{
  margin-top: 40px;
}
.date-text{
  font-size: 25px;
  position: absolute;
  bottom: 25px;
  right: 50px;
}
.pageNum-text{
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 19px;
}
.item-context{
  width: 80%;
  margin-top: 80px;
  font-size: 25px;
}
.item-row{
  display: flex;
  margin-bottom: 15px;
}
.item-label{
  font-weight: bolder;
  flex-shrink: 0;
}

</style>
