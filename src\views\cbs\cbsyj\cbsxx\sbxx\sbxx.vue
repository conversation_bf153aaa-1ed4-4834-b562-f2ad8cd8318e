<template>
  <div style="height: calc(100% - 40px);">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0" size="default">
      <el-row :gutter="20" style="margin-bottom: 20px" v-if="editable">
        <el-col :span="24">
          <el-button style="float: right" ref="button9527" type="primary" @click="addData">
            增加
          </el-button>
        </el-col>
      </el-row>
      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <el-table highlight-current-row size="default" ref="table" class="lui-table" fit :border="false"
            :data="state.tableData" height="calc(100vh - 330px)">
            <EleProTableColumn v-for="prop in state.tableColumn" :col="prop" :key="prop.columnKey">

              <template #info="{ row }">
                <el-tooltip :content="row[prop.prop]" placement="bottom" effect="light">
                  <el-icon style="cursor: pointer;" :size="18">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </template>
              <template #select="{ row }">
                <el-select size="normal" v-model="row[prop.prop]" clearable placeholder="请选择" @change="changePro(row)">
                  <el-option v-for="(item, index) in proDetails" :key="index" :value="item.ZYBM"
                    :label="item.ZYMC"></el-option>
                </el-select>
              </template>
              <template #input="{ row }">
                <el-input v-model="row[prop.prop]" placeholder="请输入"></el-input>
              </template>
              <template #titleinput="{ row }">
                <el-input v-if="row.SHZT != 1" v-model="row[prop.prop]" placeholder="请输入"></el-input>
                <span v-else>{{ row[prop.prop] }}</span>
              </template>
              <template #format="{ row }">
                {{ row[prop.prop] ? row[prop.prop].replace(" 00:00:00", "") : '' }}
              </template>
              <template #opration="{ row, $index }">
                <div v-if="editable">
                  <!-- <el-button class="lui-table-button" @click="chooseRow(row, $index)">选择</el-button> -->
                  <el-button class="lui-table-button" @click="editRow(row, $index)">查看</el-button>
                  <!--              <el-button class="lui-table-button" @click="copyRow(row, $index)">复制</el-button>-->
                  <!-- <el-button class="lui-table-button" @click="insertRow(row, $index)">插入行</el-button> -->
                  <el-button v-if="row.SHZT != 1 && row.EXTENSION?.SJLY !== 'MBSC'" class="lui-table-button"
                    @click="deleteRow(row, $index)">删除</el-button>
                </div>
                <div v-else>
                  <el-button class="lui-table-button" @click="viewRow(row, $index)">查看</el-button>
                </div>
              </template>
            </EleProTableColumn>
          </el-table>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <el-dialog custom-class="lui-dialog" title="设备信息查看" v-model="editVisible" :close-on-click-modal="false"
    :close-on-press-escape="false" :destroy-on-close="true" top="100px" width="1200px" @close="() => { }">
    <sbxxEdit :editData="editData" :editable="false" @updateData="updateData" @close="editVisible = false"
      :resultTableData="editDataWYBS" />
  </el-dialog>

  <el-dialog custom-class="lui-dialog" title="信息项选择" v-model="state.chooseVisible" :close-on-click-modal="false"
    :close-on-press-escape="false" :destroy-on-close="true" top="50px" width="1200px" @close="() => { }">
    <sbxxXz :key="editIndex" :currentRow="currentRow" @updateChooseData="updateChooseData"
      @updateEditData="updateEditData" @close="state.chooseVisible = false" :TYXYDM="TYXYDM" />
  </el-dialog>

</template>
<script setup>
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
  provide
} from "vue";
import { axios } from "@src/assets/core/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import sbxxEdit from './sbxxEdit.vue'
import { v4 as uuidv4 } from "uuid";
import axiosUtil from "../../../../../lib/axiosUtil";
import sbxxXz from "./sbxx_xz.vue";
import { InfoFilled } from "@element-plus/icons-vue";
import vsAuth from '../../../../../lib/vsAuth'
import {defineExpose} from 'vue'

const state = reactive({
  chooseVisible: false,
  tableData: [],
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 55,
      align: "center",
    },
    // {
    //   label: "专业名称",
    //   prop: "ZYMC",
    //   align: "center",
    //   showOverflowTooltip: true,
    //   width: 100,
    // },
    // {
    //   label: "业务要求",
    //   prop: "YWYQ",
    //   align: "center",
    //   showOverflowTooltip: true,
    //   width: 80,
    //   slot: "info"
    // },
    // {
    //   label: "录入资料说明",
    //   prop: "LRZLSM",
    //   align: "center",
    //   showOverflowTooltip: true,
    //   width: 80,
    //   slot: "info"
    // },
    // {
    //   label: "信息项",
    //   prop: "XXXMC",
    //   align: "center",
    //   showOverflowTooltip: true,
    //   width: 150,
    // },
    {
      label: "设备名称",
      prop: "SBMC",
      align: "center",
      // minWidth: 150,
    },
    {
      label: "规格型号",
      prop: "GGXH",
      align: "center",
      width: 150,
    },
    {
      label: "生产厂家",
      prop: "SCCJ",
      align: "center",
      // width: 150,
    },
    {
      label: "出厂日期",
      prop: "TCRQ",
      align: "center",
      width: 150,
      slot: 'format'
    },
    {
      label: "数量",
      prop: "SL",
      align: "center",
      width: 150,
    },
    {
      label: "操作",
      align: "center",
      width: 200,
      fixed: "right",
      slot: "opration",
    },
  ],
});

const currentRow = ref({});
const refs = ref([]);

const updateChooseData = (val) => {
  console.log("tableData===",state.tableData)
  // 判断val是单个对象还是数组
  const selectedData = Array.isArray(val) ? val : [val];
  console.log("selectedData===",selectedData)

  const existingItems = []; // 存储已存在的项
  const newItems = []; // 存储新的项
  
  // 遍历传入的数据
  selectedData.forEach(item => {
    // 检查是否已存在相同SBZSJID和ZYBM的记录
    const isExist = state.tableData.some(
      tableItem => tableItem.SBZSJID === item.SBZSJID 
    );
    
    if (isExist) {
      existingItems.push(item.SBMC); // 记录已存在的设备名称
    } else {
      newItems.push(item); // 记录新设备
    }
  });
  
  console.log("newItems====",newItems)

  // 如果有已存在的项，显示提示信息
  if (existingItems.length > 0) {
    ElMessage.error(`以下设备已选择，请勿重复选择: ${existingItems.join('、')}`);
  }
  
  // 如果有新项，添加到表格中
  if (newItems.length > 0) {
    // 如果是批量选择模式，需要为每个新项创建一行
    newItems.forEach(item => {
      const newRow = {
        ...item,
        SHZT:'',
        CJRZH:vsAuth.getAuthInfo().permission.userLoginName,
        CYWYBS:uuidv4().replace(/-/g, '')
      };
      
      // 添加到表格数据中
      state.tableData.push(newRow);
    });
  }
  
  // 如果没有新项且没有已存在的项
  if (existingItems.length === 0 && newItems.length === 0) {
    ElMessage.warning('没有可添加的设备');
  }
  
  // 关闭选择对话框
  state.chooseVisible = false;
};

const changeData = (oldRow, newRow, index, visible) => {
  let params = {
    newId: oldRow.DWSBID,
    oldId: newRow.SBZSJID,
    cover: true
  }

  axiosUtil.get('/backend/common/copyFileInfo', params).then((res) => {
    if (oldRow.EXTENSION?.SJLY === 'MBSC') {
      if (!newRow.EXTENSION) {
        newRow.EXTENSION = {
          SJLY: 'MBSC'
        }
      } else {
        newRow.EXTENSION.SJLY = 'MBSC'
      }
    }

    oldRow.SBZSJID = newRow.SBZSJID
    oldRow.SBLX = newRow.SBLX
    oldRow.SBMC = newRow.SBMC
    oldRow.GGXH = newRow.GGXH
    oldRow.SCCJ = newRow.SCCJ
    oldRow.EXTENSION = newRow.EXTENSION
    oldRow.SL = newRow.SL
    oldRow.TCRQ = newRow.TCRQ
    oldRow.SCDXRQ = newRow.SCDXRQ
    oldRow.SBNL = newRow.SBNL
    oldRow.BZSM = newRow.BZSM
    state.chooseVisible = visible;
  })
}


const updateEditData = (row) => {
  state.tableData.forEach((item, index) => {
    if (item.SBZSJID === row.SBZSJID) {
      changeData(item, row, index, true)
    }
  })
}

const chooseRow = (row, index) => {
  currentRow.value = row;
  editIndex.value = index;
  state.chooseVisible = true;
};

const props = defineProps({
  defaultData: {
    type: Array,
    defaultData: () => [],
  },
  proDetails: {
    type: Array,
    defaultData: () => [],
  },
  // 结果表数据，比对用
  resultTableData: {
    type: Object,
    default: () => null,
  },
  // 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  }
});
const editable = ref(props.editable)

const addData = () => {
      // // 批量选择模式，清空当前行
      // currentRow.value = {
      //   DWCYID: uuidv4().replace(/-/g, ""),
      //   CYWYBS: uuidv4().replace(/-/g, ""),
      //   editable: true,
      //   operation: 'add',
      //   ZSXX: [],
      //   EXTENSION: {}
      // };
      state.chooseVisible = true;
    }

const changePro = (row) => {
  row.ZYMC = props.proDetails.filter(x => x.ZYBM == row.ZYBM)[0].ZYMC
}
watch(
  () => props.defaultData, val => {
    if (!val) return;
  val?.forEach(x => {
    const UUID = uuidv4().replace(/-/g, '');
    x.DWSBID = x.DWSBID || UUID;
    x.SBWYBS = x.SBWYBS || UUID;
  })
  state.tableData = val;
}, {
  immediate: true
})
const copyRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, '');
  state.tableData.splice(index, 0, { ...row, DWSBID: UUID, SBWYBS: UUID, SHZT: '' });
};
const insertRow = (row, index) => {
  const UUID = uuidv4().replace(/-/g, '');
  state.tableData.splice(index + 1, 0, {
    DWSBID: UUID,
    SBWYBS: UUID,
    ZYMC: row.ZYMC,
    ZYBM: row.ZYBM,
    XXXMC: row.XXXMC,
    MBMXID: row.MBMXID,
    YWYQ: row.YWYQ,
    LRZLSM: row.LRZLSM,
    XXX: null,
    ZZXXMC: null,
    ZZDJ: null,
    YXKSRQ: null,
    YXJSRQ: null,
    FZBM: null,
    FJ: null,
    PP: null
  });
};
// 查看持证状况
const viewZS = (row) => {
  console.log(row);
};

const info = ref({});
const editVisible = ref(false);
const editIndex = ref(0);
const editData = ref({});
const editRow = (row, index) => {
  editIndex.value = index;
  editData.value = row;
  editVisible.value = true;
};

const viewRow = (row, index) => {
  editIndex.value = index;
  editData.value = row;
  editVisible.value = true;
}

const editDataWYBS = computed(() => {
  return props.resultTableData?.find(i => i.SBWYBS == editData.value?.SBWYBS)
})
const updateData = (val, isAdd) => {
  console.log(val, isAdd);
  if (isAdd) {
    state.tableData.splice(editIndex.value, 1, val);
    const UUID = uuidv4().replace(/-/g, '');
    insertRow({
      DWSBID: UUID,
      SBWYBS: UUID,
      ZYMC: val.ZYMC,
      ZYBM: val.ZYBM,
      XXXMC: val.XXXMC,
      MBMXID: val.MBMXID,
      YWYQ: val.YWYQ,
      LRZLSM: val.LRZLSM,
    }, editIndex.value);
    editRow(state.tableData[editIndex.value + 1], editIndex.value + 1);

  } else {
    state.tableData.splice(editIndex.value, 1, val);
    editVisible.value = false;
  }
};

const deleteRow = (row, index) => {
  ElMessageBox.confirm('是否删除此条数据？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    state.tableData.splice(index, 1)
    ElMessage({
      message: '删除成功!',
      type: 'success'
    })

  }).catch(() => {

  })
}

const validateForm = () => {
  return new Promise((resolve, reject) => {

    if (state.tableData.find(item => (item.SFBT == '1' && !item.SBZSJID))) {
      reject({ mgs: [{ message: '请完善设备信息！' }] })
    } else {
      for (let i = 0; i < state.tableData.length; i++) {
        let sbsl = state.tableData[i].SBSL;
        let xxxmc = state.tableData[i].XXXMC;
        let realrs = 0;
        for (let k = 0; k < state.tableData.length; k++) {
          if (state.tableData[k].SBZSJID && state.tableData[k].MBMXID === state.tableData[i].MBMXID) {
            realrs++;
          }
        }
        if (realrs < sbsl) {
          reject({ mgs: [{ message: xxxmc + '设备数量不满足' + sbsl + '个要求' }] });
          return;
        }
      }
      resolve(true)
    }
  })
};

defineExpose({
  form: state.tableData,
  validateForm,
});

</script>
<style scoped>
>>>.el-table-fixed-column--right {
  background-color: rgba(255, 255, 255, 1) !important;
}
</style>