<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input placeholder="请输入名称" v-model="state.MC" clearable></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="state.TYPE" clearable placeholder="请选择类型">
            <el-option
                v-for="(item, index) in state.options"
                :key="item.value"
                :label="item.text"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-button type="primary" @click="getTableData"><el-icon><Search/></el-icon>查询</el-button>
      </el-col>
    </el-row>
    <div class="zhyy-list-searchArea zhyy-list-searchAreaLast">
      <el-table :data="state.tableData" class="lui-table" border height="calc(100vh - 400px)">
        <el-table-column
            label="序号"
            header-align="center"
            align="center"
            type="index"
        ></el-table-column>
        <el-table-column label="名称" header-align="center" align="center">
          <template #default="{ row }">
          <span @click="openDialog(row)" style="color: blue; cursor: pointer">
            {{ row.MC }}
          </span>
          </template>
        </el-table-column>
        <el-table-column label="类型" prop="TYPE" header-align="center" align="center">
          <template #default="{ row }">
            <div>
              {{
                {
                  CBSZC: "承包商注册",
                  YHZC: "用户注册",
                }[row.TYPE]
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
            label="账号"
            prop="ZH"
            header-align="center"
            align="center"
        ></el-table-column>
        <el-table-column
            label="联系人姓名"
            prop="LXRXM"
            header-align="center"
            align="center"
        ></el-table-column>
        <el-table-column
            label="联系人电话"
            prop="LXRDH"
            header-align="center"
            align="center"
        ></el-table-column>
      </el-table>
      <el-pagination
          background
          layout="total, prev, pager, next"
          class="lui-pagination"
          :total="state.pagination.total || 0"
          style="margin-top: 10px"/>
    </div>
    <el-dialog
        custom-class="lui-dialog"
        v-model="dialogCheckList"
        top="50px"
        destroy-on-close
        width="800px"
        title="注册审核"
    >
      <contractorRegister
          v-if="checkType == 'CBSZC'"
          isCheck
          :defaultData="defaultData"
          @finish="finish"
          @close="dialogCheckList = false"
      ></contractorRegister>
      <userRegister
          v-else
          isCheck
          :defaultData="defaultData"
          @finish="finish"
          @close="dialogCheckList = false"
      ></userRegister>
    </el-dialog>
  </el-form>
</template>
<script setup>
import {
  reactive,
  computed,
  onMounted,
  ref,
  defineProps,
  nextTick,
  defineEmits,
  getCurrentInstance,
  watch,
} from "vue";
// isCheck
import userRegister from "@src/views/cbs/cbszc/userRegister.vue";
import contractorRegister from "@src/views/cbs/cbszc/contractorRegister.vue";
import { getExamineRegisterListPage } from "@src/api/sccbsgl.js";
import {getCommonGetUserByParam,getCommonSelectDMB} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
const loading = reactive(true);
const props = defineProps({
  activeName: {
    type: String,
    default: "",
  },
});

watch(
  () => props.activeName,
  (val) => {
    state.activeName = val;
    getTableData();
  }
);
const dialogCheckList = ref(false);
const state = reactive({
  MC: "",
  TYPE: "",
  activeName: "0",
  activeChildName: 0,
  RegesterLabelTitle: 0,
  CbsManageLabel: 0,
  ZtbManageLabel: 0,
  ZjManageLabel: 0,
  PjManageLabel: 0,
  tableData: [],
  pagination: {
    page: 1,
    size: 10,
    total: null,
  },
  options: [
    {
      text: "承包商注册",
      value: "CBSZC",
    },
    {
      text: "用户注册",
      value: "YHZC",
    },
  ],
  currentUser:{}
});

// 审核弹窗页
const checkType = ref("");
const defaultData = ref({});
const openDialog = (row) => {
  checkType.value = row.TYPE;
  defaultData.value = row;
  dialogCheckList.value = true;
};
const finish = () => {
  dialogCheckList.value = false;
  getUserInfo();
};
const getUserInfo = () => {
  let user = VSAuth.getAuthInfo().permission
  getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
    state.currentUser = res.data;
    getTableData()
  })
}

const emit = defineEmits(['taskNum']);
// 获取审核列表
const getTableData = () => {
  const { MC, TYPE, activeName,currentUser } = state;


  console.log(currentUser)
  getExamineRegisterListPage({
    SHZT: activeName === "0" ? "1" : "2,9",
    ORGID:currentUser.ORGNA_ID,
    MC,
    TYPE,

  })
    .then(({ data }) => {
      console.log(data.list);
      state.tableData = data.list ?? [];
      state.pagination.total = data.total;
      emit('update:taskNum',data.total)
    })
    .catch((err) => {});
};
onMounted(() => {
  getUserInfo();
});
</script>
<style scoped>
.zhyy-list-searchAreaLast {
  box-sizing: border-box;
  height: calc(100% - 85px);
  margin: 0;
}
</style>
