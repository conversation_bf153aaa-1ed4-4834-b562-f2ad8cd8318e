<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="data.queryForm.qymc" placeholder="请输入企业名称" @keyup.enter="query"></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.qygs" clearable placeholder="选择单位系统归属">
            <el-option v-for="(item, index) in data.systemUnits" :key="index + 'bgyy'" :label="item.DMMC"
                       :value="item.DMXX"></el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.dwzt" clearable placeholder="选择状态">
            <el-option v-for="(item, index) in data.teamStatus" :key="index + 'dwzt'" :label="item.DMMC"
                       :value="item.DMXX"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10" class="grid-cell">
        <div style="display: flex">
          <el-button @click="resetQuery"><el-icon><RefreshRight/></el-icon>重置</el-button>
          <el-button type="primary" @click="query"><el-icon><Search/></el-icon>查询</el-button>
          <el-button type="primary" @click="exportData"><el-icon><Upload/></el-icon>导出</el-button>
        </div>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          highlight-current-row
          ref="table"
          size="default"
          height="calc(100vh - 250px)"
          fit
          border
          :data="data.tableData"
          v-loading="tableLoading"
      >
        <EleProTableColumn
            v-for="prop in data.tableColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #view="{row}">
                        <span @click="goCbsXq(row)" style="cursor: pointer; color: #5F80C7;">
                            {{ row[prop.prop] }}
                        </span>
          </template>
        </EleProTableColumn>
      </el-table>
      <el-pagination
          background
          v-model:current-page="data.queryForm.page"
          v-model:page-size="data.queryForm.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="data.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
  </el-form>
</template>
<script setup>
import {onMounted, reactive, ref,useAttrs} from "vue";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getReportQymx, getReportQymxExport, getTeamreslutGetProDetails} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam, getCommonSelectDMB} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import TabFun from "@src/lib/tabFun";
import {mixin} from "@src/assets/core/index";
import { Search, Upload, Plus,RefreshRight} from '@element-plus/icons-vue'
import axiosUtil from "@lib/axiosUtil";
import cbsjbxxIndex from "@views/cbs/cbsyj/index.vue"
import comFun from "@lib/comFun";

const {vsuiEventbus} = mixin();
const attrs = useAttrs();
const {zybm,qylx} = attrs;
const data = reactive({

  total: 0,
  queryForm: {
    qymc: null,
    qygs: null,
    dwzt: null,
    page: 1,
    size: 20
  },
  systemUnits: [],
  teamStatus: [],
  currentUser: {},
  tableData: [],
  tableColumn: [
    {
      label: "序号",
      type: "index",
      width: 50,
      align: "left"
    },
    {
      label: "企业名称",
      prop: "CBSDWQC",
      align: "left",
      showOverflowTooltip: true,
      slot: "view"
    },
    {
      label: "统一信用代码",
      prop: "TYXYDM",
      align: "left",
      showOverflowTooltip: true,
    },
    // {
    //   label: "企业类型",
    //   prop: "QYLXDM",
    //   align: "center",
    //   showOverflowTooltip: true,
    // },
    {
      label: "单位系统归属",
      prop: "DWXTGSMC",
      align: "center",
      width: 150,
      showOverflowTooltip: true,
    },
    {
      label: "注册地址",
      prop: "ZCDZ",
      align: "center",
      width: 300,
      showOverflowTooltip: true,
    },
    {
      label: "联系人",
      prop: "LXRXM",
      align: "left",
      showOverflowTooltip: true,
    },
    {
      label: "联系人手机",
      prop: "LXRSJH",
      align: "left",
      showOverflowTooltip: true,
    },
    {
      label: "状态",
      prop: "DWZTMC",
      align: "center",
      showOverflowTooltip: true,
    },
    {
      label: "推荐单位",
      prop: "TJDWMC",
      align: "center",
      showOverflowTooltip: true,
    },
    // {
    //   label: "队伍数量",
    //   prop: "DW_COUNT",
    //   align: "center",
    //   showOverflowTooltip: true,
    // }
  ],
  entType: {},
  entUnit: {},
})

const resetQuery = () => {
  data.queryForm = {
    qymc: null,
    qygs: null,
    dwzt: null,
    page: 1,
    size: 10
  }
}
const tableLoading = ref(false);
const query = () => {
  tableLoading.value = true;
  getReportQymx({
    ...data.queryForm,
    orgnaId: data.currentUser.ORGNA_ID
  }).then(res => {
    console.log(res)
    data.tableData = res.data.list
    data.total = res.data.total

  }).catch((err) => {
    console.log(err);
  }).finally(() => {
    tableLoading.value = false
  })


}

const exportData = () => {
  let column = [[
    { field: 'CBSDWQC', title: '企业名称'},
    { field: 'TYXYDM', title: '统一信用代码'},
    // { field: 'QYLXDM', title: '企业类型'},
    { field: 'DWXTGSMC', title: '单位系统归属'},
    { field: 'ZCDZ', title: '注册地址'},
    { field: 'LXRXM', title: '联系人'},
    { field: 'LXRSJH', title: '联系人手机'},
    { field: 'DWZTMC', title: '状态'},
    { field: 'TJDWMC', title: '推荐单位'},
  ]]
  let params = {
    title: "企业信息",
    name: "企业信息",
    params: data.queryForm,
    url: '/excel/qyxxExport',
  }
  params.column = column
  axiosUtil.exportExcel('/backend/excelExport/magicExcel', params)
}

const getUserInfo = () => {
  let user = VSAuth.getAuthInfo().permission
  getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
    data.currentUser = res.data;
    query();
  })
}

const handleSizeChange = (val) => {
  data.queryForm.size = val;
  query();
}
const handleCurrentChange = (val) => {
  data.queryForm.page = val;
  query();
}

const comBelong = () => {
  getCommonSelectDMB({DMLBID: 'DWXTGS'}).then(res => {
    data.systemUnits = res.data;
    res.data.forEach(x => {
      data.entUnit[x.DMXX] = x.DMMC
    })
  })
}

const getTeamStatus = () => {
  getCommonSelectDMB({DMLBID: 'DWZT'}).then(res => {
    data.teamStatus = res.data
  })
}

const getEntType = () => {
  getCommonSelectDMB({DMLBID: 'QYLX'}).then(res => {
    res.data.forEach(x => {
      data.entType[x.DMXX] = x.DMMC
    })
  })
}

const goCbsXq = ({DWYWID, DWLX, EXTENSION,CBSDWQC}) => {
  let ex = JSON.parse(EXTENSION)
  getTeamreslutGetProDetails({dwid: DWYWID, dwlx: DWLX}).then(({data}) => {
    let param = {
      DWYWID,
      NEWTYPE: 'BG'
    }
    TabFun.addTabByCustomName(
        CBSDWQC,
        'cbsjbxx'+comFun.getNowTime().replaceAll(':','').replaceAll('-','').replaceAll(' ',''),
        cbsjbxxIndex,
        {
          uuId: DWYWID, //队伍业务ID
          MBID: ex?ex.MBID:'', //模板ID
          MBLX: "QY", //模板类型、
          ZYFLDM: data.ZYFLDM, //专业分类代码
          YWLXDM: "BG", //业务类型代码
          editable: false,//是否查看,
          isVIewJgxx: true
        },
        {}
    );
    // vsuiEventbus.emit("reloadCbsjbxx", {
    //   uuId: DWYWID, //队伍业务ID
    //   MBID: ex?ex.MBID:'', //模板ID
    //   MBLX: "QY", //模板类型、
    //   ZYFLDM: data.ZYFLDM, //专业分类代码
    //   YWLXDM: "BG", //业务类型代码
    //   editable: false,//是否查看,
    //   isVIewJgxx: true
    // });
  })
}


onMounted(() => {
  if(zybm){
    data.queryForm.zybm = zybm;
  }
  if(qylx){
    data.queryForm.qygs = qylx;
  }
  getEntType();
  comBelong();
  getTeamStatus();
  getUserInfo();
})
</script>

<style scoped>

</style>
