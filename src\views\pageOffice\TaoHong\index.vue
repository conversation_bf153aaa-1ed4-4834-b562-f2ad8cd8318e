<script setup>
import request from '@/utils/request'
import { ref, onMounted } from 'vue'
import { POBrowser } from "js-pageoffice"

const titleText = ref('');


onMounted(async () => {
	try {
		const response = await request({
			url: '/index',
			method: 'get',
		});
		titleText.value = response;
	} catch (error) {
		console.error('Failed to fetch title:', error);
	}
});

function open_pageoffice(vue_page_url) {

	//openWindow()第三个参数用来向弹出的PageOffice浏览器（POBrowser）窗口传递参数(参数长度不限)，支持json格式字符串。
	//此处为了方便演示，我们传递了file_id和file_name两个参数，具体以您实际开发为准。
	POBrowser.openWindow(vue_page_url, 'width=1150px;height=900px;');
}

</script>

<template>
	<div class="Word">
		<h3 style="text-align:center;margin-top: 25px">演示：模板套红</h3>
			<div style="width:600px;margin-top: 15px; font-size:14px;">			
			</div>
		<div align="center" class="zz-content mc clearfix pd-28">
			<div class="zz-talbeBox mc">
				<table class="zz-talbe">
					<thead>
						<tr>
							<th width="20%" style="text-align:center;">
								文档名称
							</th>
							<th width="20%" style="text-align:center;">
								创建日期
							</th>
							<th width="60%" style="text-align:center;">
								操作
							</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td style="text-align:center;">测试文件</td>
							<td style="text-align:center;">2013-05-30</td>
							<td style="text-align:center;">
								<a href="#" @click.prevent="open_pageoffice('Word')"><span
										style=" color:Green;">编辑正文</span></a>&nbsp;&nbsp;&nbsp;
								<a href="#" @click.prevent="open_pageoffice('taoHong')"><span
										style=" color:Green;">套红</span></a>&nbsp;&nbsp;&nbsp;
								<a href="#" @click.prevent="open_pageoffice('readOnly')"><span
										style=" color:Green;">正式发文</span></a>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
