<template>
   <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="XMMC">
            <el-input ref="input45296" placeholder="请输入项目名称" v-model="formData.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="XMBH">
            <el-input ref="input45296" placeholder="请输入项目编号" v-model="formData.XMBH" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="YWFL">
            <el-input ref="input45296" placeholder="请输入业务分类" v-model="formData.YWFL" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="" prop="HTXH">
            <el-input ref="input45296" placeholder="请输入合同序号" v-model="formData.HTXH" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="search" type="primary">
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)" v-loading="loading"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table" 
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="XMMC" label="项目名称" align="left" header-align="center"
                               :show-overflow-tooltip="true" min-width="120">
                    <template #default="scope">
                        <el-button type="text" @click="xsxqView(scope.row)">{{scope.row.XMMC}}</el-button>
                    </template>     
              </el-table-column>
              <el-table-column prop="XMBH" label="项目编号" align="center" header-align="center"
                               :show-overflow-tooltip="true" min-width="80"></el-table-column>
              <el-table-column prop="XMLB" label="业务分类" align="center" header-align="center"
                               :show-overflow-tooltip="true" min-width="120"></el-table-column>
              <el-table-column prop="JSDW" label="建设单位" align="center" header-align="center"
                               :show-overflow-tooltip="true" min-width="80"></el-table-column>
              <el-table-column prop="TSZT" label="推送状态" align="center" header-align="center"
                               :show-overflow-tooltip="true" min-width="60">
                    <template #default="scope">
                        <span v-if="scope.row.TSZT === '0'">未推送</span>
                        <span v-if="scope.row.TSZT === '1'" style="color: green">已推送</span>
                    </template>        
              </el-table-column>
              <el-table-column prop="HTXH" label="合同序号" align="center" header-align="center"
                               :show-overflow-tooltip="true" min-width="80"></el-table-column>
              <el-table-column prop="HTHJMC" label="合同环节名称" align="center" header-align="center"
                               :show-overflow-tooltip="true" min-width="80">
                    <template #default="scope">
                        <el-button type="text" v-if="scope.row.TSZT === '1'" @click="htlxView(scope.row)">合同履行</el-button>
                    </template>  
              </el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" min-width="90" fixed="right">
                <template #default="scope">
                  <el-button v-if="scope.row.TSZT === '0'" size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">推送CMIS
                  </el-button>
                  <el-button v-if="scope.row.TSZT === '1'" size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">终止推送
                  </el-button>
                  <el-button v-if="scope.row.TSZT === '1'" size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">更新合同
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="formData.page" v-model:page-size="formData.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="sizeChange" @current-change="pageChange" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="htxxVisible"
        v-model="htxxVisible"
        title="合同信息查看"
        @closed="closeForm"
        z-index="1000"
        width="1000px">
      <div>
        <htxxView v-if="htxxVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import htxxView from "../htzb/htxxView.vue"
export default defineComponent({
  components: {htxxView},
  setup(props, {emit}) {
    const state = reactive({
        tableData: [{XMMC: '1',TSZT: '0'},{XMMC: '1',TSZT: '1'}],
        formData: {
            XMMC: '',
            XMBH: '',
            YWFL: '',
            HTXH: '',
            page: 1,
            size: 10,
        },
        params: {},
        htxxVisible: false,
        loading: false,
        total: 0,
    });

    const search = (() =>{
        state.formData.page = 1;
        loadData();
    });

    const loadData = (() =>{
        // state.loading = true;
        // axiosUtil.get('/backend/xsgl/dygl/queryDyglList', state.formData).then((res) => {
        //     state.loading = false;
        //     state.tableData = res.data.list
        //     state.total = res.data.total
        // });
    })

    //编辑
    const editRow = ((row) =>{
    })

    //查看合同信息
    const htlxView = ((row) =>{
        state.params = {
            XMID: row.XMID
        }
        state.htxxVisible = true;
    })

    //查看选商详情
    const xsxqView = (() =>{
        state.params = {
            
        }
    })

    const indexMethod = (index) => {
      return (state.formData.page - 1) * state.formData.size + index + 1;
    }

    const sizeChange = ((val) =>{
      state.formData.size = val;
      search();
    })

    const pageChange = ((val) =>{
        state.formData.page = val;
        loadData();
    })

    const closeForm = (() =>{
        state.htxxVisible = false;
    })
    
    onMounted(() => {
      search();
    })

    return {
      ...toRefs(state),
      search,
      sizeChange,
      pageChange,
      indexMethod,
      editRow,
      closeForm,
      htlxView,
      xsxqView,
    }
  }

})
</script>

<style scoped>

</style>
