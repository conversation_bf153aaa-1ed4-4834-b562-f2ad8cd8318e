<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="190px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="16" class="grid-cell">
          <el-form-item label="招标项目名称：" prop="XMMC">
            <div style="margin-left: 10px">{{ formData.XMXX.XMMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="招标项目编号：" prop="XMBH">
            <div style="margin-left: 10px">{{ formData.XMXX.XMBH }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="招标单位：" prop="ZGDWID">
            <div style="margin-left: 10px">{{ formData.XMXX.SSDWMC }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="通知内容：" prop="GGLX">
            <el-select v-model="formData.GGLX" class="full-width-input" clearable :disabled="!editable">
              <el-option v-for="(item, index) in GGLXOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="disGGlx(item)"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell" style="align-items: center;display: flex">
          <el-button style="margin-left: 20px" size="large" link @click="viewScgg"
                     v-if="formData.GGLX==='CQGG' && formData.XMXX.SCGGID"
                     type="primary">查看上次公告</el-button>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="通知发送方式：" prop="TZFS">
            <el-select v-model="formData.TZFS" class="full-width-input" clearable :disabled="!editable">
              <el-option v-for="(item, index) in TZFSOptions" :key="index" :label="item.DMMC"
                         :disabled="['DJTP','GKZB'].includes(formData.XMXX.XSFS) && item.DMXX==='DXYQ'"
                         :value="item.DMXX"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="公告内容：" prop="GGNR">
            <newQuillEditor style="width: 100%;background-color: white" v-model="formData.GGNR" :editable="editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="公告文件：" prop="GGWJ">
            <vsfileupload
                style="margin-left: 10px"
                :editable="editable"
                :busId="params.id"
                :key="params.id"
                ywlb="GGWJ"
                busType="GGWJ"
                :limit="100"
            ></vsfileupload>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="grid-cell">
          <el-form-item label="确认是否参加截止时间：" prop="QRSFCJJZSJ">
            <el-date-picker
                v-model="formData.QRSFCJJZSJ"
                :disabled="!editable"
                type="datetime"
                clearable
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="招标文件获取时间：" prop="ZBWJHQSJ">
            <div style="display: flex;gap: 10px;align-items: center">
              <el-date-picker
                  v-model="formData.ZBWJHQSJKS"
                  :disabled="!editable"
                  type="datetime"
                  clearable
                  placeholder="请选择"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
              <div>至</div>
              <el-date-picker
                  v-model="formData.ZBWJHQSJJS"
                  :disabled="!editable"
                  type="datetime"
                  clearable
                  placeholder="请选择"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="投标文件递交截止时间：" prop="TBWJDJJZSJ">
            <el-date-picker
                v-model="formData.TBWJDJJZSJ"
                :disabled="!editable"
                type="datetime"
                clearable
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item label="开标时间：" prop="KBSJ">
            <el-date-picker
                v-model="formData.KBSJ"
                :disabled="!editable"
                type="datetime"
                clearable
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom" v-if="formData.TZFS==='DXYQ'">
          <el-form-item label="拟邀请单位：" prop="NYQDW">
            <el-table ref="datatable91634" :data="formData.XMXX.DWList" height="250px"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
              <el-table-column prop="CBSDWQC" label="单位名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
<!--              <el-table-column prop="DWMC" label="队伍名称" align="center"-->
<!--                               :show-overflow-tooltip="true" min-width="160"></el-table-column>-->
              <el-table-column prop="TJLY" label="推荐理由" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="LXR" label="联系人" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="LXDH" label="联系电话" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
              <el-table-column prop="EMAIL" label="邮箱地址" align="center"
                               :show-overflow-tooltip="true" width="160"></el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="上次公告查看"
        @closed="dialogVisible=false"
        z-index="1000"
        top="5vh"
        width="1200px">
      <div>
        <xstzfbView v-if="dialogVisible" :params="ggParams" @close="dialogVisible=false"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import newQuillEditor from "@views/components/newQuillEditor";
import vsfileupload from "@views/components/vsfileupload";
import xstzfbView from "@views/zbxsgl/xstzfb/xstzfbView";


export default defineComponent({
  name: '',
  components: {newQuillEditor, vsfileupload,xstzfbView},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      GGID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        YWZT: '1',

        XMXX: {}
      },
      rules: {
        GGLX: [{
          required: true,
          message: '字段值不可为空',
        }],
        TZFS: [{
          required: true,
          message: '字段值不可为空',
        }],
        GGNR: [{
          required: true,
          message: '字段值不可为空',
        }],
        QRSFCJJZSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        TBWJDJJZSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        ZBWJHQSJ: [{
          required: true,
          validator: (rule, value, callback) => {
            if (!state.formData.ZBWJHQSJKS) {
              callback(new Error('开始时间不能为空'))
            } else if (!state.formData.ZBWJHQSJJS) {
              callback(new Error('结束时间不能为空'))
            } else {
              callback()
            }
          }
        }],
        KBSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      GGLXOptions: [],
      TZFSOptions: [],

      dialogVisible: false,
      ggParams: {}
    })

    const getFormData = () => {
      let params = {
        GGID: state.GGID
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/xstzfb/selectXstzById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      })
    }


    const saveData = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let title = ''
      if (props.params.XSFS === 'GKZB') {
        title = '-招标公告';
      } else if (props.params.XSFS === 'YQZB') {
        title = '-投标邀请函';
      } else if (props.params.XSFS === 'GKJB' || props.params.XSFS === 'GKJJ') {
        title = '-采购公告';
      } else if (props.params.XSFS === 'JB' || props.params.XSFS === 'JJ') {
        title = '-采购邀请函';
      }
      let params = {
        ...state.formData,
        GGID: state.GGID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        GGMC: state.formData.XMXX.XMMC + title
      }
      if (type === 'submit') {
        params.SHZT = '2'
        if (params.TZFS === 'DXYQ') {
          params.XXTX = {
            YWLX: '选商通知',
            YWMC: '关于 ' + params.XMXX.XMMC + ' 的选商通知',
            BUSINESSID: state.GGID,
            FSR: state.userInfo.userName,
            FSRZH: state.userInfo.userLoginName,
            SHZT: '0',
            JSSJ: comFun.getNowTime(),
            BYZD1: 'xstzgg'
          }

        }

      }
      state.loading = true
      axiosUtil.post('/backend/xsgl/xstzfb/saveXstzForm', params).then(res => {
        ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading = false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getXmxx = () => {
      let params = {
        WJID: state.formData.WJID
      }
      axiosUtil.get('/backend/xsgl/xstzfb/selectZbxmxx', params).then(res => {
        state.formData.XMXX = res.data
        state.formData.ZBWJHQSJKS = res.data.ZBWJHQSJKS
        state.formData.ZBWJHQSJJS = res.data.ZBWJHQSJJS
        state.formData.TBWJDJJZSJ = res.data.TBWJDJJZSJ
        state.formData.KBSJ = res.data.JHKBSJ
      })
    }

    const viewScgg = () => {
      state.ggParams={editable: false, id: state.formData.XMXX.SCGGID, operation: 'view'}
      state.dialogVisible=true
    }

    const disGGlx = (item) => {
      if(state.formData.XMXX.SCGGID && item.DMXX==='CQGG'){
        return false
      }

      if(['GKZB','YQZB'].includes(state.formData.XMXX.XSFS) && item.DMXX==='XSGG'){
        return false
      }

      if(['JB','JJ','GKJJ','GKJB'].includes(state.formData.XMXX.XSFS) && item.DMXX==='CGGG'){
        return false
      }

      return true
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      } else {
        state.formData.WJID = props.params.WJID
        getXmxx()
      }
      getDMBData("GGLX", "GGLXOptions")
      getDMBData("TZFS", "TZFSOptions")
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      viewScgg,
      disGGlx
    }
  }

})
</script>

<style scoped>

</style>
