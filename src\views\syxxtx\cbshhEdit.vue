<template>
    <el-form ref="vForm" class="lui-page" label-position="right" label-width="0" size="default">
        <el-row>
            <el-col :span="24" style="text-align: center;">
                <span class="title red">{{ formData.GGMC }}</span>
            </el-col>
            <el-col :span="24" style="padding: 10px 10%;">
                <span style="float: left;"> 发布时间: {{ formData.GGSJKS }} </span>
                <span style="float: right;"> 发布单位: {{ formData.FBDWMC }} </span>
            </el-col>
        </el-row>
        <el-row style="line-height: 28px;">
            <el-col :span="24" style="text-align: center;">
                <span class="wjtitle">测试测试111</span>
            </el-col>
            <el-col :span="24" style="padding: 10px 40px;">
                <div class="title">山东胜软科技股份有限公司 :</div>
                <div class="content">
                    我公司定于 2023年08月11日，在乌鲁木齐市新市区长春南路466号西北油田科研生产园区B106会议室 对西北油田HSE管理系统提升项目进行独家谈判。
                </div>
                <div class="content">
                    请于【确认是否参加投标时间】前在中石化西北油田市场信息网中确认是否参加本次谈判，招标人逾期未收到《确认通知》回函的视为自动放弃，其投标文件一律不接收。
                </div>
                <div class="content">
                    发 包 人：中国石油化工股份有限公司西北油田分公司
                </div>
                <div class="content">
                    地 址：乌鲁木齐
                </div>
                <div class="content">
                    联 系 人：孙元疆
                </div>
                <div class="content">
                    联系电话：18999621268
                </div>
                <div class="content">
                    传真电话
                </div>
                <div class="content">
                    中国石油化工股份有限公司西北油田分公司
                </div>
                <div class="content">
                    2025年06月19日（盖章）
                </div>
            </el-col>
        </el-row>
        <el-row style="padding: 10px 40px;">
            <el-col :span="24" style="text-align: right;padding-bottom: 10px;">
                <el-button v-if="editable" size="default" type="primary" @click="comfirmHhxx"> 确定 </el-button>
                <el-button size="default" @click="toAskList"> 答疑列表 </el-button>
            </el-col>
            <el-col :span="24">
                <el-table 
                    :data="qydwList" 
                    class="lui-table"
                    border
                    stripe
                    size="default" 
                    highlight-current-row>
                    <el-table-column prop="DWMC" label="队伍名称" align="center" min-width="200"></el-table-column>
                    <el-table-column label="回函信息" align="center" min-width="200">
                        <template #default="scope">
                            <el-radio-group v-model="scope.row.SFCJTB" :disabled="!editable">
                                <el-radio v-for="(item,index) in hhxxList" :key="index" :label="item.DMXX">{{item.DMMC}}</el-radio>
                            </el-radio-group>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <!-- <el-row style="padding: 10px 40px;">
            <el-col :span="24" style="text-align: right;padding-bottom: 10px;">
                <el-button size="default" type="primary" @click="comfirmHhxx"> 保存 </el-button>
            </el-col>
            <el-col :span="24">
                <el-table 
                    :data="cybdList" 
                    class="lui-table"
                    border
                    stripe
                    size="default" 
                    highlight-current-row>
                    <el-table-column type="index" width="60" align="center"/>
                    <el-table-column prop="BDMC" label="标段名称" header-align="center" align="left" min-width="200"></el-table-column>
                    <el-table-column prop="YJJE" label="预计金额（万元）" header-align="center" align="right" min-width="120"></el-table-column>
                    <el-table-column label="参与" align="center" min-width="120">
                        <template #default="scope">
                            <el-checkbox v-model="scope.row.SFCY"></el-checkbox>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row> -->
        <el-row v-if="showFiles" style="padding: 10px 40px;">
            <!-- <el-col :span="8">
                <el-form-item label="联系人：" label-width="160px">
                    <el-input v-model="formData.LXR" placeholder="请输入" clearable >
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="联系电话：" label-width="160px">
                    <el-input v-model="formData.LXR" placeholder="请输入" clearable >
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="联系邮箱：" label-width="160px">
                    <el-input v-model="formData.LXR" placeholder="请输入" clearable >
                    </el-input>
                </el-form-item>
            </el-col> -->
            <el-col :span="24">
                <el-form-item label="招标文件下载：" label-width="160px">
                    <vsfileupload style="width:100%" :busId="formData.WJID" :key="formData.WJID" :editable="false" ywlb="ZBWJ" busType="ZBWJ">
                    </vsfileupload>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="相关附件下载：" label-width="160px">
                    <vsfileupload style="width:100%" :busId="formData.WJID" :key="formData.WJID" :editable="false" ywlb="XGFJ" busType="ZBWJ">
                    </vsfileupload>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="报价模板下载：" label-width="160px">
                    <el-button size="small" type="primary" class="lui-table-button" @click="downloadModel">下载</el-button>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="投标文件上传：" label-width="160px">
                    <vsfileupload style="width:100%" :busId="formData.HHID" :key="formData.HHID" :editable="formData.SFKYSCTBWJ==='1'" ywlb="TBWJ" busType="TBWJ" accept=".zip,.ZIP" >
                    </vsfileupload>
                </el-form-item>
                <span class="red"> 请上传加密的ZIP文件，如未加密以废标处理！（密码中不能带有！@#￥%&*?等特殊字符）</span>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24" style="text-align: center;">
                <el-button type="primary" @click="closeMsg" v-if="params.operation==='msg'">已阅</el-button>
                <el-button @click="closeForm">返回</el-button>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axiosUtil from "@lib/axiosUtil";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import VSAuth from "@src/lib/vsAuth";
import comFun from "@lib/comFun";
const props = defineProps({
    params: {
        type: Object,
        default: () => ({})
    },
});
const emit = defineEmits(["close","closeMsg","toAskList"]);
const formData = ref({});
// 邀请单位列表
const qydwList = ref([]);
// 展示附件
const showFiles = ref(false);
// 是否可编辑
const editable = ref(true);
// 参与标段列表
// const cybdList = ref([
//     {BDMC: '标段一一一一',YJJE: 100, SFCY: true},
//     {BDMC: '标段二二二二',YJJE: 200, SFCY: false}
// ])
onMounted(() => {
    queryDMBList();
    queryHhxxForm();
});

// 回函信息类型
const hhxxList = ref([]);
const queryDMBList = () => {
    axiosUtil.get('/backend/cbsxx/common/selectDMB', {
        DMLBID: "HHXX"
    }).then(res=>{
        hhxxList.value = res.data;
    })
}
// 查询表单
const queryHhxxForm = () => {
    axiosUtil.get('/backend/xsgl/xstzfb/queryCbshhxx', {
        deptId: VSAuth.getAuthInfo().permission.orgnaId,
        ggid: props.params.id
    }).then(res=>{
        formData.value = res.data.form;
        qydwList.value = res.data.list;
        if(res.data.form.HHID != null || formData.value.SFKYQRCJ === '0'){
            editable.value = false;
        }
        for(let i=0;i<res.data.list.length;i++){
            if(res.data.list[i].SFCJTB === '1'){
                showFiles.value = true;
            }
            
        }
    })
}
// 确认
const comfirmHhxx = () => {
    let hhid = formData.value.HHID ? formData.value.HHID : comFun.newId();
    for(let i=0;i<qydwList.value.length;i++){
        qydwList.value[i].HHXQID = comFun.newId();
        qydwList.value[i].HHID = hhid;
        qydwList.value[i].YXBZ = "1";
        if(!qydwList.value[i].SFCJTB){
            ElMessage.warning("请选择是否参加投标！");
            return;
        }
    }
    let params = {
        HHID: hhid,
        GGID: props.params.id,
        WJID: formData.value.WJID,
        CJRZH: VSAuth.getAuthInfo().permission.userLoginName,
        CJRXM: VSAuth.getAuthInfo().permission.userName,
        CJDWID: VSAuth.getAuthInfo().permission.orgnaId,
        DWMC: VSAuth.getAuthInfo().permission.orgnaName,
        SHZT: '1',
        YWZT: '1',
        SFSCBS: '0',
        rows: qydwList.value
    }
    ElMessageBox.confirm("确认后不可修改，确定要提交吗?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        axiosUtil.post('/backend/xsgl/xstzfb/saveCbshhxx', 
            params
        ).then(res=>{
            if(res.data.success){
                ElMessage.success("保存成功！");
                queryHhxxForm();
            }else{
                ElMessage.error("保存失败！");
            }
        })
    }).catch(() => {

    });
}

// 答疑列表
const toAskList = () => {
    emit("toAskList",formData.value.FAID);
}

// 下载模版
const downloadModel = () => {
    const link = document.createElement('a');
    link.href = "/backend/exportController/exportBjModel?hhid=" + formData.value.HHID;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

const closeForm = () => {
    emit('close')
}

const closeMsg = () => {
    emit('closeMsg')
}

defineExpose({});
</script>

<style scoped>
.title{
    font-size: 18px;
    font-weight: bold;
}
.wjtitle{
    font-size: 20px;
    font-weight: bold;
}
.red{
    color: #b23223;
}
.content{
    text-indent: 2em;
    font-size: 16px;
}
</style>