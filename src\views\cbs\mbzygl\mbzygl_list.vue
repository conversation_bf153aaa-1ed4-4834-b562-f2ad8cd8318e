<template>
    <div class="zhyy-list-container" style=" height: calc(100vh - 150px);overflow: auto;">
        <div class="zhyy-list-main">
            <el-row class="zhyy-list-searchArea">
                <el-col :span="24">
                    <label style="margin-left:10px">专业类别:</label>
                    <el-input
                        style="width: 200px"
                        placeholder="搜索大类/中类/小类专业"
                        v-model="params.ZYLB"
                        clearable>
                    </el-input>
                    <label>企业模板：</label>
                    <el-input
                        style="width: 150px"
                        placeholder="企业模板名称"
                        v-model="params.qymbmc"
                        clearable>
                    </el-input>
                    <label>队伍模板：</label>
                    <el-input
                        style="width: 150px"
                        placeholder="队伍模板名称"
                        v-model="params.dwmbmc"
                        clearable>
                    </el-input>
                    <el-button type="primary" style="margin-left:10px;" @click="getDataList()">查询</el-button>
                    <el-button type="primary" style="margin-left:10px;" @click="adddwkh()" >新增</el-button>
                  <el-button type="primary" style="margin-left:10px;" @click="exportExcel()">导出</el-button>
                </el-col>
            </el-row>
           <!--表格数据-->
            <el-row class="zhyy-list-tableArea">
                <!--&lt;!&ndash; 表格 &ndash;&gt;-->
                <el-table
                    class="customer-no-border-table"
                    :data="tableData"
                    border="1px"
                    width="100%"
                    stripe
                    :height="pageHeight"
                >
                    <el-table-column
                        type="index"
                        :index="indexMethod"
                        label="序号"
                        align="center"
                        width="50"
                    ></el-table-column>
                    <el-table-column
                        prop="YWMC"
                        label="专业类别"
                        header-align="center"
                        align="center">
                        <el-table-column
                            prop="YJYWMC"
                            label="大类"
                            header-align="center"
                            align="center">
                        </el-table-column>
                        <el-table-column
                            prop="EJYWMC"
                            label="中类"
                            header-align="center"
                            align="center">
                        </el-table-column>
                        <el-table-column
                            prop="SJYWMC"
                            label="小类"
                            header-align="center"
                            align="center">
                        </el-table-column>
                    </el-table-column>
                    <el-table-column
                        prop="QYMBMC"
                        label="企业模板"
                        header-align="center"
                        align="center">
                        <template #default="scope">
                            <span style="cursor: pointer;color: #2d8cf0" @click="showMb(scope.$index,scope.row,'qy')">
                                {{scope.row.QYMBMC}}</span>
                            <span v-if="scope.row.QYZT==2" style="font-size: 5px;color: red">(已禁用)</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="DWMBMC"
                        label="队伍模板"
                        header-align="center"
                        align="center"
                    >
                        <template #default="scope">
                            <span style="cursor: pointer;color: #2d8cf0" @click="showMb(scope.$index,scope.row,'dw')">
                                {{scope.row.DWMBMC}}</span>
                            <span v-if="scope.row.DWZT==2" style="font-size: 5px;color: red">(已禁用)</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="CZR"
                        label="操作人"
                        header-align="center"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="CZSJ"
                        label="操作时间"
                        header-align="center"
                        align="center"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="SHDWSL"
                        label="操作"
                        header-align="center"
                        align="center"
                    >
                        <template #default="scope">
                            <el-button
                                @click="deleteRow(scope.$index,scope.row)"
                                type="text"
                                size="small">
                                删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <!--&lt;!&ndash;分页区域&ndash;&gt;-->
            <div class="pagination">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="params.currentPage"
                    :page-sizes="[10, 20, 30, 40]"
                    :page-size="params.pageSize"
                    layout="total, sizes, prev, pager, next ,jumper"
                    :total="params.total"
                ></el-pagination>
            </div>
        </div>
        <el-dialog
                title="引进模板关联维护"
                center
                v-model="dwkhdialogVisible"
                v-if="dwkhdialogVisible"
                :append-to-body="true"
                width="85%"
                top="3vh"
                style="margin-top: 0vh"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :before-close="onClose"
                class="abow_dialog"
        >
            <div class="el-dialog-div">
                <div style="text-align: center">
                    <mbzyglAdd  @close="onClose" v-if="dwkhdialogVisible"></mbzyglAdd>
                </div>
            </div>
        </el-dialog>
        <el-dialog
                title="引进模板查看"
                center
                v-model="mbdialogVisible"
                v-if="mbdialogVisible"
                :append-to-body="true"
                width="85%"
                top="3vh"
                style="margin-top: 0vh"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :before-close="onClose"
                class="abow_dialog"
        >
            <div class="el-dialog-div">
                <div style="text-align: center">
                    <zrmbgMx  :appUser="params.appUser" :edit="edit" :MBID="params.MBID"  @close="onClose"></zrmbgMx>
                    <el-button type="primary" @click="onClose" size="mini" >
                        返回
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { computed, onMounted, reactive, ref } from "vue";
import mbzyglAdd from "./mbzyglAdd";
import zrmbgMx from "../zrmbgl/zrmbgMx";
import axiosUtil from "../../../lib/axiosUtil";
import { ElMessage } from "element-plus";
let mbdialogVisible = ref(false)
let dwkhdialogVisible = ref(false)
let dwEditable = ref(false)
let form = reactive({
    mbmc:"",
    mbms:"",
    mblx:"",
})
let params = reactive({
    qymbmc:"",
    dwmbmc:"",
    mblxArry:[
        { value: '1', label: '企业' },
        { value: '2', label: '队伍' }
    ],
    ZT:'',
    LX:'WB',
    value:'',
    appUser:'',
    dwzt:'',
    model:'dwjdkh',
    //项目名称/编码
    inputXmmcbm : "",
    //工程类别取值
    inputGclb : "",
    //工程类别
    //项目年度
    xmnd: String(new Date().getFullYear()),
    // 当前页码
    currentPage: 1,
    // 每页的数据条数
    pageSize: 10,
    total: 0,
    recent: 0,
    zylbJsonArray:[],
    //行数据ID
    editRowId: null,
    clickArr: [],
    formLabelWidth: "100px",
    tableStyle: "width:100%;height:calc(100vh - 310px);",
    modelTemesMap: {},
    SJYWBM:'',
    ZYLB:"",
    KHBS:'',
    edit:'',
    khqj:[],
    KHMC:'',
    FPJDX:"",
    FJDLX:"",
    edit:"",
    MBID:"",
    jyData: []
})
let tableData = reactive([])
let multipleSelection = reactive({})
let pageHeight = computed(() =>{
    return "calc(100vh - 295px)"
})

const exportExcel = () => {
      let searchParam = {
        qymbmc:params.qymbmc,
        dwmbmc:params.dwmbmc,
        ZYLB:params.ZYLB
      };
      let finparams = {
        title: "引进模板关联管理导出",
        name: "引进模板关联管理导出",
        params: searchParam,
        column: [
          [
             {
              field: "YJYWMC",
              title: "大类专业",
              width: 200,
              halign: "center",
              align: "left",
            },
			
            {
              field: "EJYWMC",
              title: "中类专业",
              width: 160,
              halign: "center",
              align: "left",
            },
            {
              field: "SJYWMC",
              title: "小类专业",
              width: 160,
              halign: "center",
              align: "left",
            },
			{
              field: "QYMBMC",
              title: "企业模板",
              width: 200,
              halign: "center",
              align: "left",
            },
			{
              field: "DWMBMC",
              title: "队伍模板",
              width: 160,
              halign: "center",
              align: "left",
            },
            {
              field: "CZR",
              title: "操作人",
              width: 160,
              halign: "center",
              align: "left",
            },
			{
              field: "CZSJ",
              title: "操作时间",
              width: 160,
              halign: "center",
              align: "left",
            },
          ],
        ],
      };
      axiosUtil.exportExcel("/sldwgl/mbgl/exportzrmb", finparams)
    //   axios({
    //     method: "post",
    //     url: "/sldwgl/mbgl/exportzrmb",
    //     data: finparams,
    //     responseType: "blob", //二进制流
    //   })
    //     .then((resp) => {
    //       const { headers } = resp;
    //       const blob = new Blob([resp.data], { type: headers["Content-Type"] });
    //       var url = window.URL.createObjectURL(blob);
    //       var aLink = document.createElement("a");
    //       aLink.style.display = "none";
    //       aLink.href = url;
    //       aLink.setAttribute("download", finparams.title + ".xls");
    //       document.body.appendChild(aLink);
    //       aLink.click();
    //       document.body.removeChild(aLink); //下载完成移除元素
    //       window.URL.revokeObjectURL(url); //释放掉blob对象 */
    //     })
    //     .catch((error) => {
    //       this.$notify({
    //         title: "警告",
    //         message: "下载出错" + error,
    //         type: "warning",
    //         duration: 2000,
    //         offset: 80,
    //       });
    //     });
    //   return null;
}
const showMb = (index, row, type) =>{
    if (type=='dw'){
        params.MBID=row.DWMB;
    }
    if (type=='qy'){
        params.MBID=row.QYMB;
    }
    params.edit='view';

    mbdialogVisible.value=true
}
const deleteRow = (index, row) =>{
    // let ableDelete=await util.postJson('/sldwgl/mbgl/ableDelete', row, this.model);
    axiosUtil.post('/sldwgl/mbgl/ableDelete', row).then(res =>{
        if(res.data.data.length > 0){
            ElMessage({
                message: '当前模板关系已被使用，无法删除',
                type: 'warning'
            })
            return
        }else{
            axiosUtil.post('/sldwgl/mbgl/deletMbZygx', row).then(resp =>{
                if(resp.data.meta.success){
                    ElMessage({
                        message: '删除成功',
                        type: 'success'
                    })
                    getDataList()
                }else{
                    ElMessage({
                        message: resp.data.meta.message,
                        type: 'error'
                    })
                }
            })
        }
    })
    // if (ableDelete.data.data.length>0){
    //     this.$message.warning("当前模板关系已被使用，无法删除")
    //     return
    // }
    // let result= await util.postJson('/sldwgl/mbgl/deletMbZygx', row, this.model);
    // if (result.data.meta.message=='ok'){
    //     this.$message.success("删除成功")
    //     this.getDataList()
    // }else {
    //     this.$message.error(result.data.meta.message)
    // }
}
const pubRow = (index,row,status) =>{
    row.SHZT=status;
    axiosUtil.get('/sldwgl/dwkh/pubRow', row).then(res =>{
        if(res.data.meta.success){
            ElMessage({
                message: '操作成功',
                type: 'success'
            })
            getDataList()
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
    // let result= await util.get('/sldwgl/dwkh/pubRow', row, this.model);
    // if (result.data.meta.message=='ok'){
    //     this.$message.success("操作成功")
    //     this.getDataList()
    // }else {
    //     this.$message.error(result.data.meta.message)
    // }
}
const adddwkh = () =>{
    params.edit=true
    dwkhdialogVisible.value=true
}
const viewmbgx = (index,row) =>{
    dwkhdialogVisible.value=true
}
//关闭对话框
const onClose = () => {
    getDataList()
    dwkhdialogVisible.value = false;
    mbdialogVisible.value = false
}
const getAppUser = () =>{
    // this.appUser=await util.getAppUser();
    return new Promise((resolve, reject) =>{
        resolve()
    })
}
/**
* 序号
*/
const indexMethod = (index) => {
    return index + params.pageSize * (params.currentPage - 1) + 1;
}
/**
 * 页面数据条数改变时
 */
const handleSizeChange = (val) => {
    params.currentPage = 1;
    params.pageSize = val;
    getDataList();
}
/**
* 翻页
*/
const handleCurrentChange = (val) => {
    params.currentPage = val;
    getDataList();
}
/**
* @Params: {{Params}}
* @Description: 获取数据
*/
const getDataList = () => {
    /* cbsbs:this.appUser.orgnaId,*/
    let param = {
        pageNum:params.currentPage,
        pageSize:params.pageSize,
        qymbmc:params.qymbmc,
        dwmbmc:params.dwmbmc,
        ZYLB:params.ZYLB
    };
    // let pageData=util.getObjectResult(await util.postJson('/sldwgl/mbgl/queryZrmbGl',params,this.model))
    axiosUtil.post('/sldwgl/mbgl/queryZrmbGl',param).then(res =>{
        if(res.data.meta.success){
            tableData.length = 0
            tableData.push(...res.data.data.rows)
            params.total = res.data.data.total
        }
    })
    // this.tableData=pageData.rows;
    // this.total=pageData.total

}
//分页多行变少行，点击翻页不刷新问题
const pageClick = (e) => {
    if (!tableData.length) {
        return false;
    }
    let dom = e.target;
    if (dom.className === "btn-next" || (dom.className === "el-icon el-icon-arrow-right" && dom.parentNode.className !== "btn-next disabled")) {
        params.currentPage += 1;
        params.currentPage >= Math.ceil(params.total / params.pageSize) ? (params.currentPage = Math.ceil(params.total / params.pageSize)) : params.currentPage;
    } else if (dom.className === "btn-prev" || (dom.className === "el-icon el-icon-arrow-left" && dom.parentNode.className !== "btn-prev disabled")) {
        params.currentPage -= 1;
        params.currentPage <= 1 ? (params.currentPage = 1) : params.currentPage;
    } else if ( dom.className === "el-icon more btn-quicknext el-icon-d-arrow-right") {
        params.currentPage = Math.ceil(params.total / params.pageSize);
    } else if (dom.className === "el-icon more btn-quickprev el-icon-d-arrow-left") {
        params.currentPage = 1;
    } else if (dom.className === "number") {
        params.currentPage = Number(dom.innerHTML);
    } else {
        return false;
    }
    getDataList();
}
const querySqlbData = () => {
    let param = {
        zymc: params.inputZymc ,
        gclbdm: params.gclbdm,
        CBSDWBS: params.cbsdwbs
    };
    axiosUtil.get('/sldwgl/cbsDwxx/whDwzy', param).then(res =>{
        if(res.data.meta.success){
            let rows = res.data.data
            params.jyData = rows
            for (let i = 0; i < rows.length ; i++) {
                rows[i].value=rows[i].YWBM
                rows[i].label=rows[i].YWMC
                let treeData = transData(rows, 'YWBM', 'PYWBM', 'children')
                params.zylbJsonArray = treeData
            }
        }else{
            ElMessage({
                message: res.data.meta.message,
                type: 'error'
            })
        }
    })
    // let result = await util.get('/sldwgl/cbsDwxx/whDwzy', param, this.model);
    // if(result.data.meta.success){
    //     var rows = result.data.data;
    //     this.jyData=rows
    //     for (let i = 0; i <rows.length ; i++) {
    //         rows[i].value=rows[i].YWBM
    //         rows[i].label=rows[i].YWMC
    //     }
    //     var treeData = this.transData(rows, 'YWBM', 'PYWBM', 'children');

    //     this.zylbJsonArray = treeData;
    // }else{
    //     this.$message({type: "warning", message: result.data.meta.message});
    // }
}
/**
* json格式转树状结构
* @param   {json}      json数据
* @param   {String}    id的字符串
* @param   {String}    父id的字符串
* @param   {String}    children的字符串
* @return  {Array}     数组
*/
const transData = (a, idStr, pidStr, childrenStr) =>{
    let r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
    for(; i < len; i++){
        a[i]['isparent'] = false;
        hash[a[i][id]] = a[i];
    }
    for(; j < len; j++){
        let aVal = a[j], hashVP = hash[aVal[pid]];
        if(hashVP){
            !hashVP[children] && (hashVP[children] = []);
            hashVP[children].push(aVal);
        }else{
            r.push(aVal);
        }
    }
    return r;
}
const setIsParent = (arr) => {
    for(var j=0; j < arr.length; j++){
        if(arr[j].children && arr[j].children.length > 0){
            arr[j].isparent = true;
            setIsParent(arr[j].children);
        }
    }
}
const handleChangeLb = (value) => {
    params.ZYLB=value[value.length-1];
    getDataList();
}
onMounted(() =>{
    getDataList();
    getAppUser();
    querySqlbData();
})
</script>

<style scoped>
    ::v-deep .el-cascader__dropdown{
        height: 250px;
    }

    .dialog-footer {
        text-align: center;
    }
    .el-cascader-menu__wrap{
        height: 250px;
    }

    body .el-table th.gutter {
        display: table-cell !important;
    }
</style>