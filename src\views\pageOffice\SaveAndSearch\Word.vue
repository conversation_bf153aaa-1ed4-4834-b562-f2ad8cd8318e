<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');
const searchKey = ref('');
const open_params = ref('');

function OnPageOfficeCtrlInit() {
	// PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
	pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function Save() {
	//使用SaveFilePage属性设置后端保存方法的Controller路由地址，这个地址必须从"/"开始，并且也可以向此路由地址传递json字符串参数，示例如下：
	let saveFileUrl = "/SaveAndSearch/save";
	let paramValue = new URLSearchParams(open_params.value);//为了简单起见，这里直接使用打开时的参数。
	pageofficectrl.SaveFilePage = `${saveFileUrl}?${paramValue.toString()}`;
	//在这里写您保存前的代码
	pageofficectrl.WebSave();
	//在这里写您保存后的代码，比如判断保存结果pageofficectrl.CustomSaveResult
}

function SetKeyWord(visible) {
	if (searchKey.value == "null" || "" == searchKey.value) {
		alert("关键字为空。");
		return;
	}
	let falg = true;
	pageofficectrl.word.HomeKey(6);
	while (falg) {
		if (pageofficectrl.word.FindNextText(searchKey.value)) {
			if (visible) {
				pageofficectrl.word.SetHighlightToSelection(7);
			} else {
				pageofficectrl.word.SetHighlightToSelection(0);
			}
		} else {
			pageofficectrl.word.HomeKey(6)
			break;
		}
	}
}

const fetchData = async () => {
	try {
		//使用pageofficectrl.WindowParams获取获取父页面(当前为：/SaveAndSearch/index.vue)中POBrowser.openWindow()方法的第三个参数的值,获取到的值为string类型
		open_params.value = JSON.parse(pageofficectrl.WindowParams);
		searchKey.value = open_params.value.key;
		const response = await request({
			url: '/SaveAndSearch/Word',
			method: 'get',
			params: open_params.value
		});
		poHtmlCode.value = response;
	} catch (error) {
		console.error('There has been a problem with your axios operation:', error);
	}
};

onMounted(() => {
	// 请求后端打开文件
	fetchData();
	//将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
	window.POPageMounted = { OnPageOfficeCtrlInit, Save };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
	<div class="Word">
		<input name="button" id="Button1" type="button" @click="SetKeyWord(true)" value="高亮显示关键字" />
		<input name="button" id="Button2" type="button" @click="SetKeyWord(false)" value="取消关键字显示" />
		<!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
		<div style="width:auto; height:860px;" v-html="poHtmlCode"></div>
	</div>
</template>
