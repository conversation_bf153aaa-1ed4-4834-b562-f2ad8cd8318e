<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right" label-width="140px"
             size="default" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="审核意见" prop="result">
            <el-input v-model="formData.result" :rows="4"
                      type="textarea" clearable
                      show-word-limit :maxlength="100"
                      placeholder="请输入"/>
          </el-form-item>
        </el-col>

<!--        <el-col :span="24" class="grid-cell no-border-bottom" v-if="formData.suggestflg==='0'">-->
<!--          <el-form-item label="重新走流程" prop="sfcxzlc">-->
<!--            <el-radio-group v-model="formData.sfcxzlc">-->
<!--              <el-radio label="1" size="large">是</el-radio>-->
<!--              <el-radio label="0" size="large">否</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
      </el-row>
      <el-row :gutter="0">
        <div style="width: 100%;text-align: center;margin-top: 10px">
          <el-button type="primary" @click="submit">提交</el-button>
          <el-button @click="closeForm">取消</el-button>
        </div>
      </el-row>
    </el-form>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";


export default defineComponent({
  name: '',
  components: {},
  props: {
    operateParams: {
      type: Object,
      required: true
    },
    processParams: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      formData:{
        ...props.operateParams,
        sfcxzlc: '1'
      },
      rules: {
        result:{
          required: true,
          message: '审核意见不可为空',
        }
      }
    })

    const submit = () => {
      emit("getFinishRes",state.formData)
    }
    
    const closeForm = () => {
      emit("closeForm")
    }
    onMounted(() => {

    })

    return {
      ...toRefs(state),
      closeForm,
      submit

    }
  }

})
</script>

<style scoped>

</style>
