<template>
  <div class="context">
    <div class="context-form" style="boxShadow: var(--el-box-shadow)">
       <el-table
          border
          highlight-current-row
          ref="listTable"
          :data="tableData"
          :row-key="getRowKey"
          :header-cell-style="{ background: '#F4F7FA' }"
          @row-click="onSelect"
        >
          <el-table-column
            align="center"
            label="序号"
            type="index"
            min-width="5%"
            width="50px"
            :index="indexMethod"
          >
          </el-table-column>
          <el-table-column
            min-width="5%"
            prop="ORGNA_NAME"
            align="right"
            header-align="center"
            label="组织机构"
          ></el-table-column>
          <el-table-column
            min-width="5%"
            prop="ORGNA_TWO_NAME"
            align="right"
            header-align="center"
            label="二级单位"
          ></el-table-column>
          <el-table-column
            min-width="10%"
            width=150
            label="操作"
            align="center"
            header-align="center"
          >
            <template #default="scope">
              <el-button v-if="scope.row.NOW_ORGNA_ID!=scope.row.ORGNA_ID"  @click="submit(scope.row)" type="text" size="mini">切换</el-button>
              <el-button v-else   type="text" size="mini">当前单位</el-button>
            </template>
          </el-table-column>
        </el-table>

    </div>


  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {ElMessage, ElMessageBox} from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import tabFun from "@lib/tabFun";
import vsAuth from "../../../lib/vsAuth";


export default defineComponent({
  name: '',
  components: {},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      tableData:[],
    })
    
    const getOrgList = () => {
       axiosUtil.get('/backend/common/selectGlbmByUserId',{USER_ID:vsAuth.getAuthInfo().permission.userId}).then(res=>{
          state.tableData = res.data
       })
    }

    const instance = getCurrentInstance()
  


    const submit = (row) => {
          ElMessageBox.confirm(
              '确定切换组织机构?',
              '警告',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
              }
          ).then(() => {
            axiosUtil.get('/backend/common/changeOrgnaId',{USER_ID:vsAuth.getAuthInfo().permission.userId,ORGNA_ID:row.ORGNA_ID}).then(res=>{
                ElMessage.success("切换"+row.ORGNA_NAME+"成功");
                location.reload();
            })
          }).catch(() => {})
    }

    onMounted(() => {
        getOrgList()
    })

    return {
      ...toRefs(state),
      getOrgList,
      submit
    }
  }

})
</script>

<style scoped>
.context {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}

.context-form {
  width: 1000px;
  height: 300px;
  background-color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
