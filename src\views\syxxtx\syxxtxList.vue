<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="listQuery.YWMC" placeholder="请输入业务名称"></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="4" class="grid-cell">
        <div class="static-content-item" style="display: flex;">
          <el-button ref="button91277" @click="getDataList" type="primary">
            <el-icon>
              <Search/>
            </el-icon>
            查询
          </el-button>
        </div>
      </el-col>
    </el-row>
    <el-row ref="grid71868" :gutter="12">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 400px)"
                    :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
            <el-table-column prop="YWLX" label="业务类型" align="center"
                             :show-overflow-tooltip="true" width="160">
            </el-table-column>
            <el-table-column prop="YWMC" label="业务名称" align="left"
                             :show-overflow-tooltip="true" min-width="200">
              <template #default="{row}">
                <el-button link size="small" type="primary" @click="viewRow(row)">{{row.YWMC}}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="JSSJ" label="接收时间" align="center"
                             :show-overflow-tooltip="true" width="180"></el-table-column>
            <el-table-column prop="FSR" label="发送人" align="center"
                             :show-overflow-tooltip="true" width="100"></el-table-column>
            <el-table-column prop="BLSJ" label="办理时间" align="center"
                             :show-overflow-tooltip="true" width="180"></el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </el-form>

  <el-dialog
      custom-class="lui-dialog"
      :close-on-click-modal="false"
      v-if="dialogVisible"
      v-model="dialogVisible"
      :title="title"
      @closed="closeForm"
      z-index="1000"
      destroy-on-close
      top="5vh"
      width="1200px">
    <div>
      <syxxtxEdit v-if="dialogVisible" :params="params" @close="closeForm" @toAskList="toAskList"/>
    </div>
  </el-dialog>

</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch, markRaw} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import TabFun from "@lib/tabFun";
import syxxtxEdit from "@views/syxxtx/syxxtxEdit";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import vsAuth from "@lib/vsAuth";
import xbdyglList from "@views/zbxsgl/xbdygl/xbdyglList";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus, syxxtxEdit},
  props: {
    status: String,
    activeName: String,
    taskNum: Number,
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        page: 1,
        size: 10,
      },
      tableData: [],
      total: 0,
      params: {},
      title: '',
      dialogVisible: false,
    })

    watch(() => props.activeName, (val) => {
      if (val) {
        getDataList();
      }
    })

    const getDataList = () => {
      const params = {
        ...state.listQuery,
        SHZT: props.activeName,
        loginName: state.userInfo.userLoginName
      }
      axiosUtil.get('/backend/xxtx/selectxxtxPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
        emit('update:taskNum',res.data.total)
      });
    }


    const viewRow = (row) => {
      state.params = {editable: props.activeName==='0', id: row.ID, operation: 'view'}
      state.title=row.YWMC+ '-查看'
      state.dialogVisible = true
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    const toAskList = (faid) => {
      state.dialogVisible = false;
      TabFun.addTabByCustomName(
        "答疑列表", 
        "dylb", 
        markRaw(xbdyglList), 
        {params: { faid: faid,pageType: 'YF' } },
        {}
      );
    }
    

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      viewRow,
      closeForm,
      toAskList,
    }
  }

})
</script>

<style scoped>

</style>
