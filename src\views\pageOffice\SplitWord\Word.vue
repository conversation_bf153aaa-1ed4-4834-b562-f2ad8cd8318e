<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function Save() {
  pageofficectrl.SaveDataPage = "/SplitWord/save";
  pageofficectrl.WebSave();
}

function OnPageOfficeCtrlInit() {
  // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
  pageofficectrl.AddCustomToolButton("保存", "Save", 1);
}

function openFile() {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/SplitWord/Word',
    method: 'get',
  });
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit ,Save};//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <div class="Word">
    <div style=" font-size:14px; line-height:20px;">
      演示说明：<br />点击“保存”按钮，PageOffice会把文档中三个数据区域（PO_test1，PO_test2，PO_test3）中的内容保存为三个独立的子文件（new1.doc，new2.doc，new3.doc）到“static/doc/SplitWor”
      目录下。
    </div>
    <div style="color: red;font-size:14px; line-height:20px;">
      Word拆分功能只有企业版支持，并且文档的打开模式必须是OpenModeType.docSubmitForm，需要设置数据区域的属性dataRegion1.setSubmitAsFile(true)。<br /><br />
    </div>
    <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
    <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
  </div>
</template>
