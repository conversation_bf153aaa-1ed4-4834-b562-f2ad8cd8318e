<template>
    <div class="zhyy-list-container">
        <div class="zhyy-list-main">
            <el-row class="zhyy-list-searchArea" style="text-align: left">
                <el-col :span="24">
                    <label>模板名称：</label>
                    <el-input
                        style="width: 150px"
                        placeholder="请输入模板名称"
                        v-model="params.mbmc"
                        clearable>
                    </el-input>
                    <label>模板类型：</label>
                    <el-select  style="width: 110px" v-model="params.mblx" placeholder="请选择">
                        <el-option
                            v-for="item in params.mblxArry"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <el-button type="primary" style="margin-left:10px" @click="getDataList()">查询</el-button>
                    <el-button type="primary" style="margin-left:10px;float: right" @click="confirm()">确定</el-button>
                </el-col>
            </el-row>
           <!--表格数据-->
            <el-row class="zhyy-list-tableArea">
                <!--&lt;!&ndash; 表格 &ndash;&gt;-->
                <el-table
                    class="customer-no-border-table"
                    :data="tableData"
                    border="1px"
                    width="100%"
                    :height="pageHeight"
                    :header-cell-style="{color: '#000000', fontSize: '14px'}"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column
                        type="selection"
                        :selectable="selectable"
                        width="55">
                    </el-table-column>
                    <el-table-column
                        type="index"
                        :index="indexMethod"
                        label="序号"
                        align="center"
                        width="50"
                    ></el-table-column>
                    <el-table-column
                        prop="MBLXMC"
                        label="模板类型"
                        header-align="center"
                        align="center">
                    </el-table-column>
                    <el-table-column
                        prop="MBMC"
                        label="模板名称"
                        header-align="center"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="MBMS"
                        label="描述"
                        header-align="center"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="CZR"
                        label="操作人"
                        header-align="center"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="CZSJ"
                        label="操作时间"
                        header-align="center"
                        align="center"
                    ></el-table-column>
                </el-table>
            </el-row>
            <!--&lt;!&ndash;分页区域&ndash;&gt;-->
            <div class="pagination">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="params.currentPage"
                    :page-sizes="[10, 20, 30, 40]"
                    :page-size="params.pageSize"
                    layout="total, sizes, prev, pager, next "
                    :total="params.total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";

let emits = defineEmits(['confirm'])
let pageHeight = computed(() =>{
    return "calc(100vh - 300px)"
})
let params = reactive({
    mbmc:"",
    mblx:"",
    mblxArry:[
        { value: '1', label: '企业' },
        { value: '2', label: '队伍' }
    ],
    appUser:'',
    dwzt:'',
    model:'dwjdkh',
    // 当前页码
    currentPage: 1,
    // 每页的数据条数
    pageSize: 10,
    total: 0,
    recent: 0,
    //行数据ID
    editRowId: null,
    clickArr: [],
    formLabelWidth: "100px",
    tableStyle: "width:100%;height:calc(100vh - 310px);",
    modelTemesMap: {},
})
let dwEditable = ref(false)
//表数据
let tableData = reactive([])
let multipleSelection = reactive([])
let form = reactive({})
const handleSelectionChange = val => {
    multipleSelection.length = 0
    multipleSelection.push(...val)
}
const confirm = () =>{
    let dwcount=0;
    let qycount=0;
    if (multipleSelection.length==0){
        ElMessage({
            message: '请选择一条数据后再点击确定',
            type: 'warning'
        })
        return
    }
    if (multipleSelection.length==1 && multipleSelection[0].MBLXBM=='2'){
        ElMessage({
            message: '选择的数据必须包含企业模板',
            type: 'warning'
        })
        return
    }
    for (let i = 0; i <multipleSelection.length; i++) {
        if (multipleSelection[i].MBLXBM=='2'){
            dwcount++;
        }
        if (multipleSelection[i].MBLXBM=='1'){
            qycount++;
        }
    }
    if (dwcount>1){
        ElMessage({
            message: '只能选择一条队伍信息',
            type: 'warning'
        })
        return
    }
    if (qycount>1){
        ElMessage({
            message: '只能选择一条企业信息',
            type: 'warning'
        })
        return
    }
    //确定，将数据传输到上一个页面
    emits("confirm",multipleSelection)
}
//关闭对话框
const onClose =() => {
    params.ZYLB=[]
    params.khqj=[]
    params.SJYWBM="",
    params.PJDX="",
    params.JDLX=""
    getDataList()
    dwkhdialogVisible.value = false;
}
const getAppUser = () =>{
    // this.appUser=await util.getAppUser();
}
/**
 * 序号
 */
const indexMethod = index => {
    return index + params.pageSize * (params.currentPage - 1) + 1;
}
/**
 * 页面数据条数改变时
 */
const handleSizeChange = val => {
    params.currentPage = 1;
    params.pageSize = val;
    getDataList();
}
/**
 * 翻页
 */
const handleCurrentChange = val => {
    params.currentPage = val;
    getDataList();
}
/**
 * @Params: {{Params}}
 * @Description: 获取数据
 */
const getDataList = () => {
    /* cbsbs:this.appUser.orgnaId,*/
    let param = {
        LOGINNAME:params.appUser.loginName,
        mbmc:params.mbmc,
        mblx:params.mblx,
        pageNum:params.currentPage,
        pageSize:params.pageSize,
    };
    axiosUtil.post('/sldwgl/mbgl/queryGlMb',param).then(res =>{
        if(res.data.meta.success){
            tableData.length = 0
            tableData.push(...res.data.data.rows)
            params.total = res.data.data.total
        }
    })
    // let pageData=util.getObjectResult(await util.postJson('/sldwgl/mbgl/queryGlMb',params,this.model))
    // this.tableData=pageData.rows;
    // this.total=pageData.total

}
//分页多行变少行，点击翻页不刷新问题
const pageClick = e => {
    if (!tableData.length) {
        return false;
    }
    let dom = e.target;
    if (dom.className === "btn-next" || (dom.className === "el-icon el-icon-arrow-right" && dom.parentNode.className !== "btn-next disabled")) {
        params.currentPage += 1;
        params.currentPage >= Math.ceil(params.total / params.pageSize) ? (params.currentPage = Math.ceil(params.total / params.pageSize)) : params.currentPage;
    } else if (dom.className === "btn-prev" || (dom.className === "el-icon el-icon-arrow-left" && dom.parentNode.className !== "btn-prev disabled")) {
        params.currentPage -= 1;
        params.currentPage <= 1 ? (params.currentPage = 1) : params.currentPage;
    } else if (dom.className === "el-icon more btn-quicknext el-icon-d-arrow-right") {
        params.currentPage = Math.ceil(params.total / params.pageSize);
    } else if (dom.className === "el-icon more btn-quickprev el-icon-d-arrow-left") {
        params.currentPage = 1;
    } else if (dom.className === "number") {
        params.currentPage = Number(dom.innerHTML);
    } else {
        return false;
    }
    getDataList();
}
onMounted(() =>{
    getDataList();
    getAppUser();
    querySqlbData();
})
</script>

<style scoped>
    ::v-deep .el-cascader__dropdown{
        height: 250px;
    }

    .dialog-footer {
        text-align: center;
    }
    .el-cascader-menu__wrap{
        height: 250px;
    }

    body .el-table th.gutter {
        display: table-cell !important;
    }
</style>