<template>
  <el-row :gutter="0" class="grid-row">
    <el-col :span="8" class="grid-cell">
      <el-form-item label="专家编号：" prop="RKZJBXX.ZJBH">
        <el-input v-model="formData.RKZJBXX.ZJBH" type="text" clearable :disabled="true"></el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="统一身份账号：" prop="RYZH">
        <el-input v-model="formData.RYZH" type="text" clearable :disabled="!editable"></el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="职称：" prop="RKZJBXX.ZC">
        <el-select v-model="formData.RKZJBXX.ZC" class="full-width-input" :disabled="!editable" clearable>
          <el-option v-for="(item, index) in options.ZC" :key="index" :label="item.DMMC"
                     :value="item.DMXX"></el-option>
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="联系电话：" prop="RKZJBXX.LXDH">
        <el-input v-model="formData.RKZJBXX.LXDH" type="text" :disabled="!editable" clearable></el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="工作区域：" prop="RKZJBXX.GZQY">
        <el-input v-model="formData.RKZJBXX.GZQY" type="text" :disabled="!editable" clearable></el-input>
      </el-form-item>
    </el-col>
<!--    <el-col :span="8" class="grid-cell">-->
<!--      <el-form-item label="电子邮箱：" prop="RKZJBXX.DZYX">-->
<!--        <el-input v-model="formData.RKZJBXX.DZYX" type="text" :disabled="!editable" clearable></el-input>-->
<!--      </el-form-item>-->
<!--    </el-col>-->
<!--    <el-col :span="16" class="grid-cell">-->
<!--      <el-form-item label="通讯地址：" prop="RKZJBXX.TXDZ">-->
<!--        <el-input v-model="formData.RKZJBXX.TXDZ" type="text" :disabled="!editable" clearable></el-input>-->
<!--      </el-form-item>-->
<!--    </el-col>-->
<!--    <el-col :span="8" class="grid-cell">-->
<!--      <el-form-item label="目前所在地：" prop="RKZJBXX.MQSZD">-->
<!--        <el-input v-model="formData.RKZJBXX.MQSZD" type="text" :disabled="!editable" clearable></el-input>-->
<!--      </el-form-item>-->
<!--    </el-col>-->
    <el-col :span="8" class="grid-cell">
      <el-form-item label="毕业院校：" prop="RKZJBXX.BYYX">
        <el-input v-model="formData.RKZJBXX.BYYX" type="text" :disabled="!editable" clearable></el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell">
      <el-form-item label="所学专业：" prop="RKZJBXX.SXZY">
        <el-input v-model="formData.RKZJBXX.SXZY" type="text" :disabled="!editable" clearable></el-input>
      </el-form-item>
    </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="参加工作时间：" prop="RKZJBXX.CJGZSJ">
            <el-date-picker v-model="formData.RKZJBXX.CJGZSJ" type="date" class="full-width-input" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled="!editable" clearable :editable="false"></el-date-picker>
          </el-form-item>
        </el-col>
<!--    <el-col :span="8" class="grid-cell">-->
<!--      <el-form-item label="毕业时间：" prop="RKZJBXX.BYSJ">-->
<!--        <el-date-picker v-model="formData.RKZJBXX.BYSJ" type="date" class="full-width-input" format="YYYY-MM-DD"-->
<!--                        value-format="YYYY-MM-DD" :disabled="!editable" clearable :editable="false"></el-date-picker>-->
<!--      </el-form-item>-->
<!--    </el-col>-->

    <el-col :span="8" class="grid-cell">
      <el-form-item label="职业资格：" prop="RKZJBXX.ZYZG">
        <el-input v-model="formData.RKZJBXX.ZYZG" type="text" :disabled="!editable" clearable></el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell no-border-bottom">
      <el-form-item label="职业资格证书：" prop="fileupload94939">
        <vsfileupload v-model:files="formData.fileupload94939" :busId="params.id" :key="params.id" ywlb="dwxx"
                      busType="dwxx"
                      :editable="editable"/>
      </el-form-item>
    </el-col>
<!--    <el-col :span="8" class="grid-cell">-->
<!--      <el-form-item label="在职状态：" prop="RKZJBXX.ZZZT">-->
<!--        <el-select v-model="formData.RKZJBXX.ZZZT" class="full-width-input" :disabled="!editable" clearable>-->
<!--          <el-option v-for="(item, index) in options.ZZZT" :key="index" :label="item.DMMC"-->
<!--                     :value="item.DMXX"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--    </el-col>-->
<!--    <el-col :span="16" class="grid-cell">-->
<!--      <el-form-item label="行政职务：" prop="RKZJBXX.XZZW">-->
<!--        <el-input v-model="formData.RKZJBXX.XZZW" type="text" :disabled="!editable" clearable></el-input>-->
<!--      </el-form-item>-->
<!--    </el-col>-->
    <el-col :span="8" class="grid-cell">
      <el-form-item label="行政职务：" prop="XZZW">
        <el-input v-model="formData.XZZW" type="text" :disabled="!editable" clearable></el-input>
      </el-form-item>
    </el-col>
    <el-col :span="8" class="grid-cell picture-cell">
      <el-form-item label="个人照片：" prop="pictureupload40039">
        <vsfileupload-pic v-model:files="formData.pictureupload40039" :busId="params.id" :key="params.id"
                          listType="picture" :limit="1" ywlb="ddzx" busType="ddzx" :editable="editable"/>
      </el-form-item>

    </el-col>

<!--    <el-col :span="8" class="grid-cell picture-cell">-->
<!--      <el-form-item label="职业资格证书：" prop="fileupload94939">-->
<!--        <vsfileupload  v-model:files="formData.fileupload94939" :busId="params.id" :key="params.id" ywlb="dwxx"-->
<!--                      busType="dwxx"-->
<!--                      :editable="editable"/>-->
<!--      </el-form-item>-->
<!--    </el-col>-->
  </el-row>
</template>

<script>
import {defineComponent, onMounted, reactive, toRefs} from 'vue';
import VsfileuploadPic from "../../../components/vsfileuploadPic";
import Vsfileupload from "../../../components/vsfileupload";
import axiosUtil from "../../../../lib/axiosUtil";

export default defineComponent({
  components: {VsfileuploadPic, Vsfileupload},
  props: {
    formData:{
      required:true,
      type: Object
    },
    params:{
      type: Object
    }
    // formData：{
    //   require： true

  },
  setup(props, {emit}) {

    const state = reactive({
      editable:false,
      options:{}
    })
    const getDMBData = async (DMLBID) => {
      let params = {
        DMLBID
      }
      let res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params)
      state.options[DMLBID] = res.data
    }
    onMounted(()=>{
      getDMBData('ZC')
      getDMBData('ZZZT')
      state.editable = props.params.editable
    })
    return{
      ...toRefs(state),
    }
  },

})
</script>

<style scoped>

</style>
