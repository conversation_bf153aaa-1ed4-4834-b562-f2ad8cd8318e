<!-- 流程跟踪页面 -->
<template>
  <el-form :model="formData" ref="vForm" class="lui-page" label-position="left" label-width="0" size="default">
    <div class="zhyy-list-main">
      <el-row :gutter="10" style="height: 35px">
        <el-col :span="6">
          <span>发起时间：{{ formData.KSSJ }}</span>
        </el-col>
        <el-col :span="6">
          <span> 发起人：{{ formData.OWNERNAME }} </span>
        </el-col>
        <el-col :span="6">
          <span>流程状态：</span>
          <span v-if="formData.STATUS == '1'">运行中</span>
          <span v-else-if="formData.STATUS == '3'">结束</span>
          <span v-else-if="formData.STATUS == '4'">已终止</span>
          <span v-else>未发起</span>
        </el-col>
        <el-col :span="6">
          <span>完成时间：{{ formData.WCSJ }}</span>
        </el-col>
      </el-row>
      <hr />
      <el-row :gutter="24" style="height: 50px">
        <el-col :span="24" align="center">
          <H3>业务办理跟踪详情</H3>
        </el-col>
      </el-row>
      <el-table :data="tableData" style="min-height: 300px" :border="true" class="lui-table">
        <el-table-column type="index" width="60" label="序号" align="center" />
        <el-table-column prop="TASKNAME" label="业务环节" header-align="center" align="left"></el-table-column>
        <el-table-column prop="CLR" label="办理人" header-align="center" align="left" width="200">
          <template #default="scope">
            <span v-if="scope.row.STATUS == 1">{{ scope.row.CANDIDATESNAME }}</span>
            <span v-else>{{ scope.row.USERNAME }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="TASKSTATUS" label="进度状态" width="80" header-align="center" align="center">
          <template #default="scope">
            <span v-if="scope.row.TASKSTATUS == '1'">待办</span>
            <span v-else-if="scope.row.TASKSTATUS == '9'">完成</span>
          </template>
        </el-table-column>
        <el-table-column prop="FINISHEDTIME" label="办理时间" width="180" header-align="center" align="center">
        </el-table-column>
        <el-table-column prop="SUGGESTFLG" label="审核结果" width="120" header-align="center" align="center">
          <template #default="scope">
            <span v-if="scope.row.SUGGESTFLG == '1'">同意</span>
            <span v-else-if="scope.row.SUGGESTFLG == '0'">不同意</span>
          </template>
        </el-table-column>
        <el-table-column prop="RESULT" label="审查意见" header-align="center" align="left" width="300">
        </el-table-column>
      </el-table>
    </div>
  </el-form>
  <el-dialog custom-class="lui-dialog" v-if="dialogVisible" v-model="dialogVisible" title="选择人员" width="80%"
    append-to-body top="1vh">
    <searchUserList v-if="dialogVisible" @closeForm="closeForm" @confirmData="confirmData" :onlyOne="true" />
  </el-dialog>
</template>
<script>
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import axiosUtil from "@lib/axiosUtil";
import searchUserList from "./searchUserList.vue";
import vsflow from "@views/vsflow/index.js";

export default {
  name: "MonitorForm",
  props: {
    model: {
      type: String,
      default: 'scgl'
    },
    queryParams: Object,
    showChange: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      formData: {},
      tableData: [],
      dialogVisible: false,
      changeRow: {}
    };
  },
  components: { searchUserList },
  computed: {},
  watch: {},
  methods: {
    /**
     * 加载数据
     */
    async loadData() {
      const resultList = await vsflow.getTasksMonitor(this.queryParams.processInstanceId);
      if (resultList.length > 0) {
        this.formData = resultList[0];
        this.tableData = resultList;
      }
    },
  },
  created() { },
  mounted() {
    this.loadData();
  },
};
</script>
<style scoped></style>
