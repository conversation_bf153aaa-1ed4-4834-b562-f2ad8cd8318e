<template>
  <div style="height: calc(100% - 40px);">
        <el-form style="height: 100%;" class="lui-page" size="default"  v-loading="state.loading">
          <el-row :gutter="20" class="lui-search-form">
            <el-col :span="6" class="grid-cell">
              <el-form-item label="" prop="userName">
                <el-input ref="input45296" placeholder="项目名称" @input="getDataList"
                          v-model="state.listQuery.XMMC" type="text" clearable>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="1" class="grid-cell" style="margin-left: auto">
              <el-button type="primary" class="lui-button-add" @click="addRow"><el-icon><Plus/></el-icon>添加</el-button>
            </el-col>
          </el-row>
            <el-table
                    highlight-current-row
                    size="default"
                    ref="table"
                    class="lui-table"
                    fit
                    height="50vh"
                    :border="false"
                    :data="state.tableData"
            >
                <EleProTableColumn
                        v-for="prop in state.tableColumn"
                        :col="prop"
                        :key="prop.columnKey"
                >
                  <template #format="{ row }">
                    {{ row[prop.prop]?row[prop.prop].replace(" 00:00:00", ""): '' }}
                  </template>
                    <template #opration="{ row, $index }">
                        <div>
                          <el-button class="lui-table-button" @click="chooseData(row, $index)">选择</el-button>
                          <el-button class="lui-table-button" @click="editRow(row, $index)">编辑</el-button>
                          <el-button class="lui-table-button" @click="delRow(row, $index)">删除</el-button>
                        </div>
                    </template>
                </EleProTableColumn>
            </el-table>
        </el-form>
    </div>

  <el-dialog
      title="业绩信息编辑"
      v-model="editVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      top="100px"
      width="1200px"
      @close="()=>{getDataList()}">
    <yjxxEdit :editData="editData" @updateData="updateData" @close="editVisible = false" :editable="true"/>
  </el-dialog>
</template>
<script setup>
import {
    reactive,
    computed,
    onMounted,
    ref,
    defineProps,
    nextTick,
    defineEmits,
    getCurrentInstance,
    watch,
} from "vue";
import {axios,auth} from "@src/assets/core/index";
import {ElMessage, ElMessageBox, ElLoading} from "element-plus";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {v4 as uuidv4} from "uuid";
import axiosUtil from "../../../../../lib/axiosUtil";
import { Search, Select, Plus} from '@element-plus/icons-vue'
import yjxxEdit from './yjxx_xzEdit.vue'
import comFun from "../../../../../lib/comFun";

const state = reactive({
    tableData: [],
  listQuery:{},
  loading: false,
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 55,
            align: "center",
        },
        {
            label: "项目名称",
            prop: "XMMC",
            align: "center",
        },
        {
            label: "甲方单位",
            prop: "JSDW",
            align: "center",
        },
        {
            label: "合同开始日期",
            prop: "HTRQKS",
            align: "center",
            width: 150,
            slot: 'format'
        },
        {
            label: "合同结束日期",
            prop: "HTRQJS",
            align: "center",
            width: 150,
            slot: 'format'
        },
        {
            label: "合同金额(万元)",
            prop: "HTJE",
            align: "center",
            width: 150,
        },
        {
            label: "操作",
            align: "center",
            width: 150,
            fixed: "right",
            slot: "opration",
        },
    ],
});

const addRow = () => {
  const userinfo = auth.getPermission();
  let userParams={
    CJRZH: props.TYXYDM,
    CJRXM: userinfo.userName,
    CJDWID: userinfo.orgnaId,
    CJSJ: comFun.getNowTime(),
    SHZT: '1',
  }
  let params={
    edit: true,
    YJZSJID: comFun.newId(),
    ...userParams,
  }

  editIndex.value = state.tableData.length;
  editData.value = params;
  editVisible.value = true;

}


const editRow = (row,index) => {
  editIndex.value = index;
  editData.value = row;
  editVisible.value = true;
}

const updateData = (val, isAdd) => {
  saveRow(val,isAdd)
};

const saveRow = (params,isAdd) => {
  axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveFwyjXxx',{tableData: [params]}).then(r=>{
    ElMessage.success('保存成功')
    if (isAdd) {
      addRow()
    } else {
      editVisible.value = false;
    }
    emit('updateEditData',params)
  })
}

const delRow = (row, index) => {
  ElMessageBox.confirm(
      '确定删除该条数据?',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(() => {
    let params={
      YJZSJID: row.YJZSJID,
      SHZT: '2'
    }
    axiosUtil.post('/backend/sccbsgl/cbsyj/ytxxx/save/saveFwyjXxx',{tableData: [params]}).then(r=>{
      state.tableData.splice(index,1)
      ElMessage.success('删除成功')
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除',
    })
  })
}


const props = defineProps({
// 是否查看模式
  editable: {
    type: Boolean,
    default: true
  },
  TYXYDM: {
    type: String,
    default: ''
  }

});
const emit=defineEmits(['updateChooseData','updateEditData'])
const chooseData = (row, index) => {
  emit('updateChooseData',row)
};

const getDataList = () => {
  const userinfo = auth.getPermission();
  let params = {
    ...state.listQuery,
    orgId: userinfo.orgnaId,
    TYXYDM: props.TYXYDM,
  }
  state.loading = true
  axiosUtil.get('/backend/sccbsgl/cbsyj/ytxxx/selectFwyjXxx', params).then((res) => {
    state.tableData = res.data || []
    state.loading = false
  })
}

onMounted(()=>{
  getDataList()
})

const editVisible = ref(false);
const editIndex = ref(0);
const editData = ref({});


</script>
<style scoped>
>>> .el-table-fixed-column--right{
    background-color: rgba(255, 255, 255, 1) !important;
}
</style>