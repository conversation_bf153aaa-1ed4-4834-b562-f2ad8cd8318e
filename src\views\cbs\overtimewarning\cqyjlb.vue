<template>
  <el-form ref="vForm" class="lui-page" label-position="left" label-width="0px"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.yjlx" placeholder="请选择预警类型" clearable>
            <el-option label="已超期" value="0"/>
            <el-option label="一个月内将超期" value="30"/>
            <el-option label="三个月内将超期" value="90"/>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-input v-model="data.queryForm.dwmc" placeholder="请输入名称" @keyup.enter="query"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-form-item label="">
          <el-select v-model="data.queryForm.dwzt" placeholder="请输入状态" clearable>
            <el-option label="正常" value="ZC"/>
            <el-option label="暂停" value="ZT"/>
            <el-option label="延期期限" value="YQQX"/>
            <el-option label="取消" value="QX"/>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" class="grid-cell">
        <el-button @click="resetQuery"><el-icon><RefreshRight/></el-icon>重置</el-button>
        <el-button type="primary" @click="query">
          <el-icon><Search/></el-icon>查询
        </el-button>
      </el-col>
    </el-row>
    <div class="container-wrapper">
      <el-table
          class="lui-table"
          highlight-current-row
          ref="table"
          fit
          size="default"
          height="calc(100vh - 250px)"
          border
          :data="data.tableData"
          v-loading="tableLoading"
      >
        <EleProTableColumn
            v-for="prop in data.tableColumn"
            :col="prop"
            :key="prop.columnKey"
        >
          <template #opration="{row,$index}">
            <div>
              <el-button class="lui-table-button" @click="resetQuery">提醒企业变更</el-button>
              <br>
              <el-button class="lui-table-button" @click="edit(row)">直接延期期限</el-button>
            </div>
          </template>
          <template #status="{row,$index}">
            <span v-if="row.DWZT=='ZC'">正常</span>
            <span v-if="row.DWZT=='QX'">取消</span>
            <span v-if="row.DWZT=='ZT'">暂停</span>
            <span v-if="row.DWZT=='YQQX'">延期期限</span>
          </template>
        </EleProTableColumn>
      </el-table>
      <el-pagination
          background
          v-model:current-page="data.queryForm.page"
          v-model:page-size="data.queryForm.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes"
          class="lui-pagination"
          :total="data.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
        v-model="data.saveVisible"
        title="数据维护"
        width="1100px"
        custom-class="lui-dialog"
    >
      <el-form
          ref="saveFormRef"
          :model="data.saveForm"
          :rules="rules"
          class="lui-card-form"
          label-width="120px"
          size="default"
          status-icon
      >
        <el-row class="grid-row">
          <el-col :span="14" class="grid-cell">
            <el-form-item label="管理对象" prop="CBSDWQC">
              <el-input v-model="data.saveForm.CBSDWQC" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="10" class="grid-cell">
            <el-form-item label="企业名称" prop="DWMC">
              <el-input v-model="data.saveForm.DWMC" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="grid-cell">
            <el-form-item label="当前状态" prop="DWZT">
              <el-select v-model="data.saveForm.DWZT" disabled style="width: 100%;">
                <el-option v-for="(item, index) in data.teamStatus" :key="index + 'zt'" :label="item.name" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="grid-cell">
            <el-form-item label="变更后状态" prop="BGHZT" style="width: 100%;">
              <el-select v-model="data.saveForm.BGHZT" disabled>
                <el-option v-for="(item, index) in data.teamStatus" :key="index + 'bgh'" :label="item.name" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10" class="grid-cell">
            <el-form-item label="延期期限" prop="DWBM" v-if="data.saveForm.BGHZT == 'YQQX'">
              <el-date-picker
                  v-model="data.saveForm.date"
                  type="daterange"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  :shortcuts="data.shortcuts"
                  range-separator="-"
                  start-placeholder="延期开始时间"
                  end-placeholder="延期结束时间"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24" class="grid-cell">

            <el-form-item label="延期说明" prop="BGSM" style="height:80px;">
              <el-input v-model="data.saveForm.BGSM" type="textarea" />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="grid-cell">

            <el-form-item label="相关附件" prop="SYZT">
              <vsfileupload :busId="data.saveForm.RCGLID" :key="data.saveForm.RCGLID" ywlb="DWZTBGFJ" busType="dwxx"
                            :limit="100" style="margin-left: 10px" :maxSize="10"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
            <span class="dialog-footer">
              <el-button @click="data.saveVisible = false">取消</el-button>
              <el-button type="primary" @click="save">
                确定
              </el-button>
            </span>
      </template>
    </el-dialog>
  </el-form>
</template>
<script setup>
import {
    ref,
    reactive,
    onMounted,
} from "vue";
import {ElMessage, ElMessageBox} from 'element-plus';
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getDwrcglDwcqyj,postDwrcglDwyqqx} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam,getCommonSelectDMB} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import {v4 as uuidv4} from "uuid";
import comFun from "@src/lib/comFun";
import vsfileupload from "@src/views/components/vsfileupload.vue";
import { Search, Upload, Plus,RefreshRight} from '@element-plus/icons-vue'


const data = reactive({
    total: 0,
    shortcuts: [
        {
            text: '三个月',
            value: () => {
                const end = new Date()
                const start = data.saveForm.DWYXQJS?new Date(data.saveForm.DWYXQJS): new Date()
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90)
                return [start, end]
            },
        },{
            text: '半年',
            value: () => {
                const end = new Date()
                const start = data.saveForm.DWYXQJS?new Date(data.saveForm.DWYXQJS): new Date()
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180)
                return [start, end]
            },
        },{
            text: '一年',
            value: () => {
                const end = new Date()
                const start = data.saveForm.DWYXQJS?new Date(data.saveForm.DWYXQJS): new Date()
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365)
                return [start, end]
            },
        },
    ],
    queryForm: {
        yjlx: null,
        dwmc: null,
        dwzt: null,
        page: 1,
        size: 10
    },
    changeReason:[],
    saveForm: {
        date: ''
    },
    teamStatus:[
        {
            value: 'ZC',
            name: '正常'
        },
        {
            value: 'ZT',
            name: '暂停'
        },
        {
            value: 'QX',
            name: '取消'
        },
        {
            value: 'YQQX',
            name: '延期期限'
        },
    ],
    currentUser: {},
    saveVisible: false,
    tableData: [],
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 60,
            align: "center"
        },
        {
            label: "企业名称",
            prop: "CBSDWQC",
            align: "left",
            showOverflowTooltip: true,
        },
        // {
        //     label: "队伍名称",
        //     prop: "DWMC",
        //     align: "left",
        //     showOverflowTooltip: true,
        // },
        {
            label: "当前状态",
            prop: "DWZT",
            align: "center",
            slot: 'status'
        },
        {
            label: "状态有效期",
            prop: "DWYXQJS",
            align: "center",
            width: 150,
        },
        {
            label: "预警说明",
            prop: "YJYY",
            align: "left",
            showOverflowTooltip: true,
            width: 400,
        },
        {
            label: "操作",
            align: "center",
            width: 150,
            fixed: "right",
            slot: "opration",
        },
    ],
})

const resetQuery = () => {
    data.queryForm = {
        yjlx: null,
        dwmc: null,
        dwzt: null,
        page: 1,
        size: 10
    }
}
const tableLoading = ref(false);
const query = () => {
    tableLoading.value = true;
    getDwrcglDwcqyj(data.queryForm).then(res => {
        console.log(res)
        data.tableData = res.data.list
        data.total = res.data.total;
    }).catch((err) => {
        console.log(err);
    }).finally(() => {
        tableLoading.value = false
    })

}
const edit = (row, index) => {
    console.log(row)
    data.saveForm = {
        RCGLID: uuidv4().replace(/-/g,''),
        date:[
            row.DWYXQJS || comFun.getNowDate(),
            comFun.refDate(row.DWYXQJS || comFun.getNowDate(),30)
        ],
        ...row,
        BGHZT: 'YQQX'
    }
    console.log(data.saveForm)
    data.saveVisible = true;

}
const getChangeReason = ()=>{
    getCommonSelectDMB({DMLBID: 'DWZTBGYY'}).then(res=>{
        data.changeReason = res.data
    })
}
const saveFormRef = ref(null)
const save = () => {
    data.saveForm.YXQKS = data.saveForm.date[0]
    data.saveForm.YXQJS = data.saveForm.date[1]
    let bgh = {
        ZTKSSJ:  data.saveForm.YXQKS ,
        ZTJSRQ: data.saveForm.YXQJS,
        ZT: data.saveForm.BGHZT,
    }
    let bgq = {
        ZTKSSJ:  data.saveForm.DWYXQKS,
        ZTJSRQ: data.saveForm.DWYXQJS,
        ZT: data.saveForm.DWZT,
    }
    data.saveForm.CJSJ = comFun.getNowTime();
    data.saveForm.CJRXM = data.currentUser.USER_NAME;
    data.saveForm.CJRZH = data.currentUser.USER_LOGINNAME;
    data.saveForm.CJDWID = data.currentUser.ORGNA_ID
    data.saveForm.BGH = JSON.stringify(bgh)
    data.saveForm.BGQ = JSON.stringify(bgq)
    data.saveForm.DWZT = data.saveForm.BGHZT
    console.log(data.saveForm);
    postDwrcglDwyqqx(data.saveForm).then(res => {
        ElMessage({
            type: 'success',
            message: '保存成功!'
        });
        query();
        data.saveVisible = false;
    }).catch(e => {
        ElMessage({
            type: 'error',
            message: '保存失败!'
        });
    })
}

const getUserInfo = () => {
    let user = VSAuth.getAuthInfo().permission
    getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
        data.currentUser = res.data;
    })
}

const handleSizeChange = (val) => {
    data.queryForm.size = val;
    query();
}
const handleCurrentChange = (val) => {
    data.queryForm.page = val;
    query();
}


onMounted(() => {
    getChangeReason();
    getUserInfo();
    query();
})
</script>

<style scoped>

</style>
