{"name": "Project", "version": "1.0.0", "description": "vsui.vue@2.1.0项目模板,Powered By @vsui/vue-multiplex核心库", "author": "", "private": true, "license": "", "repository": "", "scripts": {"browser": "npx browserslist", "dev": " cross-env  webpack serve --progress --config  build/project/webpack.conf.dev.js", "dev:demo": " cross-env  webpack serve --progress --config build/demo/webpack.conf.dev.js", "build": " node build/project/webpack.conf.build.js", "build:demo": " node build/demo/webpack.conf.build.js", "debug-webpack": "node --inspect-brk build/webpack.dev.conf.js --progress", "build:api": "node script/api-generator-low-code"}, "bakDevDependencies": {"@babel/core": "^7.14.3", "@babel/plugin-proposal-decorators": "^7.14.2", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/plugin-transform-runtime": "^7.14.3", "@babel/preset-env": "^7.14.4", "@babel/runtime": "^7.14.0", "@babel/runtime-corejs3": "^7.14.0", "babel-loader": "^8.2.2"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vsui/lib-vsui-auth4-vseaf": "file:lib-vsui-auth4-vseaf", "@vsui/vue-multiplex": "file:multiplex", "@vue-office/pdf": "^1.4.1", "@vueup/vue-quill": "^1.0.0-alpha.40", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "0.27.2", "crypto-js": "^4.2.0", "echarts": "^5.5.1", "element-plus": "2.2.8", "html2canvas": "^1.4.1", "js-pageoffice": "6.3.3", "jspdf": "^2.5.1", "jsplumb": "^2.15.6", "less": "^4.2.0", "less-loader": "^12.2.0", "mitt": "3.0.0", "mockjs": "1.1.0", "pdfjs-dist": "^3.9.179", "qrcode.vue": "^3.4.1", "signature_pad": "^5.0.7", "vue": "3.2.33", "vue-demi": "^0.13.11", "vue-esign": "^1.1.4", "vue-grid-layout": "^3.0.0-beta1", "vue-pdf-embed": "^1.1.6", "vue-router": "4.0.15", "vue3-pdf": "^4.2.6", "vue3-pdf-app": "^1.0.3", "vue3-preview-image": "^1.2.3", "vue3-print-nb": "^0.1.4", "vuedraggable": "^4.1.0", "vuex": "4.0.2"}, "devDependencies": {"@webpack-cli/serve": "^1.4.0", "autoprefixer": "^10.2.5", "build": "^0.1.4", "chalk": "^4.1.1", "clean-friendly-errors-webpack-plugin": "^2.1.0", "copy-webpack-plugin": "9.0.0", "core-js": "^3.33.2", "cross-env": "7.0.3", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "3.0.1", "file-loader": "^6.2.0", "handlebars": "^4.7.6", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.6.0", "node-notifier": "^10.0.1", "ora": "^5.4.0", "postcss": "^8.4.32", "postcss-import": "^15.1.0", "postcss-loader": "^7.0.2", "rimraf": "^3.0.2", "sass": "~1.3.0", "sass-loader": "~10.1.1", "semver": "^7.3.5", "shelljs": "^0.8.3", "url-loader": "^4.1.1", "vue-loader": "^17.0.0", "vue-style-loader": "^4.1.3", "webpack": "^5.89.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.11.1", "webpack-merge": "^5.7.3"}, "engines": {"node": ">= 14.17.0", "npm": ">= 6.13.15"}}