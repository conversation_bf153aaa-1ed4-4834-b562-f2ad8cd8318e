<!-- 选择子级单位 -->
<template>
  <el-form style="height: 100%;width: 100%" size="default" class="lui-page">
    <div style="width: 100%;text-align: right;padding-bottom: 10px">
      <el-button @click="goEdit" type="primary">子级单位维护</el-button>
    </div>
    <el-table ref="multipleTableRef" class="lui-table" height="calc(100% - 110px)" :data="props.tableData" border
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" header-align="center" align="center"
                       :selectable="
                    (row) =>
                      row.SYZT != '1'
                  "
                       width="80"></el-table-column>
      <el-table-column label="序号" type="index" header-align="center" align="center"
                       width="80"></el-table-column>
      <el-table-column prop="unitName" header-align="left" align="left" show-overflow-tooltip>
        <template #header>
          <div>
            {{ props.childUnit.unitName }}
          </div>
        </template>
        <template #default="scope">
          <div>
            {{ scope.row.unitName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="单位编码" prop="dwbm" header-align="left" align="left"></el-table-column>
    </el-table>
    <el-row justify="center" align="center" style="margin-top: 10px;">
      <el-button @click="submit">确定</el-button>
    </el-row>
  </el-form>
</template>

<script setup>
import outerBox from "@components/common/outerBox.vue";
import {defineEmits, defineProps, onMounted} from "vue";
import {ElMessage} from "element-plus";
const emits = defineEmits(["handleSure", "handleReturn", "onSubmit","sure",'goEdit']);



const goEdit = ()=>{
    emits('goEdit')
}

let props = defineProps({
    childUnit: {
        type: Object,
        default: {unitName: '子级单位', code: ''}
    },
    tableData: {
        type: Array,
        default: []
    },
    selected: {
        type: Array,
        default: {unitName: '子级单位1', code: ''}
    },
  dialogTitle:{
    type: String,
  }
})

const handleSelectionChange = val => {
    props.selected.length = 0
    props.selected.push(...val)
}

const submit = ()=>{
    console.log(props.selected)
    if(props.selected.length == 0){
        ElMessage({
            message: '请选择子级单位！',
            type: 'warning'
        })
        return;
    }
    emits('sure',props.selected)
}
onMounted(() => {
  emits('update:dialogTitle','选择子级单位')
})
</script>

<style scoped>
.el-container {
    height: 100%;
}

.out-box-content {
    height: calc(100% - 70px);
    padding: 20px 10px;
}
</style>
