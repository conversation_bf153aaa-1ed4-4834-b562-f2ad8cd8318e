<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="">
            <el-input v-model="data.queryForm.dwmc" placeholder="请输入队伍名称" @keyup.enter="query"></el-input>
          </el-form-item>
        </el-col>


        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="query" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table
                highlight-current-row
                ref="table"
                height="calc(100vh - 230px)"
                class="lui-table"
                fit
                border
                :border="false"
                :data="data.tableData"
            >
              <EleProTableColumn
                  v-for="prop in data.tableColumn"
                  :col="prop"
                  :key="prop.columnKey"
              >
                <template #opration="{row,$index}">
                  <div>
                    <!--todo-->
                    <el-button class="lui-table-button" @click="goWrite(row)">发起增项</el-button>
                  </div>
                </template>
                <template #status="{row,$index}">
                  <span v-if="row.DWZT=='ZC'">正常</span>
                  <span v-if="row.DWZT=='QX'">取消</span>
                  <span v-if="row.DWZT=='ZT'">暂停</span>
                  <span v-if="row.DWZT=='YQ'">延长期限</span>
                </template>
              </EleProTableColumn>
            </el-table>

            <el-pagination
                v-model:current-page="data.queryForm.page"
                v-model:page-size="data.queryForm.size"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, prev, pager, next, sizes"
                class="lui-pagination"
                :total="data.total"
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup>
import {onMounted, onUnmounted, reactive,defineProps} from "vue";
import EleProTableColumn from "@src/components/ele-pro-table-column";
import {getBgGetChangedTeamById, getCbszxAddTeamList, getCbszxCopyAddInfo} from '@src/api/sccbsgl.js'
import {getCommonGetUserByParam} from '@src/api/cbsxx.js'
import VSAuth from "@src/lib/vsAuth";
import {v4 as uuidv4} from "uuid";
import tabFun from "@src/lib/tabFun";
import {mixin} from '@src/assets/core/index';
import zxzyzx from "./index.vue";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
const {vsuiEventbus} = mixin()
const props = defineProps({
    ZRLX: {
        type: String,
        default: "",
    },
});
const data = reactive({
    total: 0,
    queryForm: {
        dwmc: null,
        page: 1,
        size: 10
    },
    changeReason:[],
    saveForm: {
        date: ''
    },
    teamStatus:[
        {
            value: 'ZC',
            name: '正常'
        },
        {
            value: 'ZT',
            name: '暂停'
        },
        {
            value: 'QX',
            name: '取消'
        },
        {
            value: 'YQ',
            name: '延长期限'
        },
    ],
    currentUser: {},
    saveVisible: false,
    tableData: [],
    tableColumn: [
        {
            label: "序号",
            type: "index",
            width: 55,
            align: "center"
        },
        {
            label: "队伍名称",
            prop: "DWMC",
            align: "left",
            showOverflowTooltip: true,
            width: '200px'
            // slot: "faultHours",
        },
        {
            label: "申请类别",
            prop: "SQLBMC",
            align: "center",
            width: '200px'
            // slot: "faultHours",
        },
        {
            label: "备案类型",
            prop: "BALXMC",
            align: "center",
            width: '200px'
            // slot: "faultHours",
        },
        {
            label: "队伍类型",
            prop: "DWLBMC",
            align: "center",
            width: '200px'
            // slot: "faultHours",
        },
        {
            label: "服务范围",
            prop: "fwfw",
            align: "LEFT",
            showOverflowTooltip: true,
        },
        {
            label: "操作",
            align: "center",
            width: 150,
            fixed: "right",
            slot: "opration",
        },
    ],
})

const resetQuery = () => {
    data.queryForm = {
        yjlx: null,
        dwmc: null,
        dwzt: null,
        page: 1,
        size: 10
    }
}
const goWrite = (row) => {


    if(row.DWLX == ("CBS")){
        let zybms = row.ZYBM.split(',')
        let newId = uuidv4().replace(/-/g, '');
        getCbszxCopyAddInfo({
            dwywidNew:newId,
            dwywidResut: row.DWYWID,
            newType: 'ZX'
        }).then(res=>{
            tabFun.addTabByCustomName("增项-专业选择", "zxzyzx", zxzyzx, {uuId: newId,dwlx: 'QY',zybms: zybms}, {});
        })
    }else if(row.DWLX == ("DW")){
        let zybms = row.ZYBM.split(',')
        let newId = uuidv4().replace(/-/g, '');
        getCbszxCopyAddInfo({
            dwywidNew:newId,
            dwywidResut: row.DWYWID,
            newType: 'ZX'
        }).then(res=>{
            getBgGetChangedTeamById({dwid:newId}).then(r=>{
                tabFun.addTabByCustomName("增项-专业选择", "zxzyzx", zxzyzx, {uuId: newId,dwlx: 'DW',zybms: zybms,NEWDWYWID:r.data.DWYWID, ZRLX:props.ZRLX}, {});
            })
        })
    }

    return false;



    // tabFun.addTabByRoutePath('引入申请信息', '/contractors/yrsqxxIndex', {DWYWID: row.DWYWID, type: 'cqyj'}, );
    // router.push("/contractors/yrsqxx")
}

const query = () => {
    getCbszxAddTeamList({
        ...data.queryForm,
        orgid: data.currentUser.ORGNA_ID,
        dwlb: props.ZRLX
    }).then(res => {
        data.tableData = res.data.list
        data.total = res.data.total
    })

}

const getUserInfo = () => {
    let user = VSAuth.getAuthInfo().permission
    getCommonGetUserByParam({userName: user.userLoginName}).then(res => {
        data.currentUser = res.data;
        query();
    })
}

const handleSizeChange = (val) => {
    data.queryForm.size = val;
    query();
}
const handleCurrentChange = (val) => {
    data.queryForm.page = val;
    query();
}


onMounted(() => {
    getUserInfo();
    vsuiEventbus.on('reloadTableData',getUserInfo);
})
onMounted(() => {

})
onUnmounted(()=>{
    // timer.value = null;
    vsuiEventbus.off('reloadTableData',getUserInfo);
})
</script>

<style scoped>
.header {
    padding: 10px 10px 0;
    height: 50px;
    line-height: 50px;
}

.main {
    height: calc(100% - 155px);
    padding: 10px;
}
.footer {
    height: 100px;
    line-height: 100px;
}
</style>
