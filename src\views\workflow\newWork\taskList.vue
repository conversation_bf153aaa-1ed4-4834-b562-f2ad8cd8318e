<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="RWMC">
            <el-input ref="input45296" placeholder="请输入任务名称" v-model="listQuery.RWMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 400px)" v-loading="loading"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="PROCESS_NAME" label="业务类型" header-align="center" align="center" width="150">
              </el-table-column>
              <el-table-column prop="PROCESSINSTANCENAME" label="任务名称" header-align="center" align="left">
                <template #default="scope">
                  <el-button type="text" @click="onAudit(scope.row)">
                    {{ scope.row.PROCESSINSTANCENAME+"-"+scope.row.TASKNAME }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="业务环节" prop="TASKNAME" header-align="center" align="center" width="200">
                <template #default="scope">
                  {{scope.row.TASKNAME || '已结束'}}
                </template>
              </el-table-column>
              <el-table-column label="发送人" prop="OWNERNAME" header-align="center" width="100" align="center"></el-table-column>
              <el-table-column label="接收时间" prop="CREATEDTIME" header-align="center" align="center" width="180"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                  <el-button size="small" class="lui-table-button" type="primary" @click="monitorRow(scope.row)">跟踪
                  </el-button>
                  <el-button v-if="listQuery.STATUS==='9'" size="small" class="lui-table-button" type="primary" @click="repealTask(scope.row)">撤回
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="title"
        @closed="dialogVisible=false"
        z-index="1000"
        fullscreen
        append-to-body
        width="100%">
      <div>
        <auditFrame v-if="dialogVisible" :businessParams="params" :processParams="processParams" @close="closeForm"/>
      </div>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogGZVisible"
        v-model="dialogGZVisible"
        title="流程跟踪"
        @closed="dialogGZVisible=false"
        z-index="1000"
        top="5vh"
        append-to-body
        width="1400px">
      <div>
        <monitorForm v-if="dialogGZVisible" :monitorParams="monitorParams" @close="closeForm"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, watch} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import vsAuth from "@lib/vsAuth";
import newWork from "@views/workflow/newWork/index";
import monitorForm from "@views/workflow/newWork/monitorForm";
import auditFrame from "@views/workflow/newWork/auditFrame";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus, monitorForm, auditFrame},
  props: {
    activeName: String,
    taskNum: Number,
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      listQuery: {
        page: 1,
        size: 10,
        STATUS: '1'
      },

      title: '',
      params: {},
      processParams: {},

      dialogVisible: false,
      tableData: [],
      total: 0,
      loading: false,

      dialogGZVisible: false,
      monitorParams: {}

    })

    watch(() => props.activeName, (val) => {
          if (val) {
            state.listQuery.STATUS = props.activeName === '0' ? '1' : '9'
            getDataList();
          }
        }
    )

    const getDataList = () => {
      let params = {
        ...state.listQuery,
        loginName: state.userInfo.userLoginName,
      }
      state.loading = true
      axiosUtil.get('/backend/workFlow/wf5/selectTaskList', params).then(res => {
        state.tableData = res.data.list || []
        state.total = res.data.total
        emit('update:taskNum', res.data.total)
        state.loading = false
      })
    }

    const monitorRow = (row) => {
      state.monitorParams = row
      state.dialogGZVisible = true
    }

    const onAudit = (row) => {
      let processBM=newWork.processMap[row.PROCESSID]
      state.processParams={
        ...row,
        activityId: row.ACT_DESC,
        processId: row.PROCESSID,
        processName: row.PROCESSINSTANCENAME,
        processInstanceId: row.PROCESSINSTANCEID,
        busiUrl: newWork.processData[processBM].busiUrl,
        examUrl: newWork.processData[processBM].examUrl[row.ACT_DESC],
        processBM: processBM,
        status: state.listQuery.STATUS
      }
      state.params={
        editable: state.listQuery.STATUS==='1' && ['new','1'].includes(row.ACT_DESC),
        id: row.PROCESSINSTANCEID,
        operation: state.listQuery.STATUS==='1' ? 'examine' : 'view'
      }
      state.title=row.PROCESSINSTANCENAME+"-"+(state.listQuery.STATUS==='1' ? '审核' : '查看')
      state.dialogVisible=true
    }

    const repealTask = (row) => {
      ElMessageBox.confirm('确定撤回该流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.get(newWork.repealTaskUrl,{taskId: row.TASKID}).then(res=>{
          ElMessage.success('撤回成功')
          getDataList()
        })
      }).catch(() => {});

    }


    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }

    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    onMounted(() => {
      getDataList()
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      closeForm,
      monitorRow,
      onAudit,
      repealTask

    }
  }

})
</script>

<style scoped>

</style>
