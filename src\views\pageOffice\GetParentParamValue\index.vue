<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'
import { POBrowser } from 'js-pageoffice'

const titleText = ref('');

function open_pageoffice(vue_page_url, param) {
	POBrowser.openWindow(vue_page_url, 'width=1200px;height=800px;', param);
}

onMounted(async () => {
	try {
		const response = await request({
			url: '/index',
			method: 'get',
		});
		titleText.value = response;
	} catch (error) {
		console.error('Failed to fetch title:', error);
	}
});

</script>

<template>
	<div class="Word">
		<a href="#" @click.prevent="open_pageoffice('Word', '张三')">POBrowser方式打开Word文档</a><br><br><br>
		<router-view></router-view>
	</div>
</template>
<style scoped>
a {
	display: block;
	margin-top: 30px;
	text-decoration: underline;
}
</style>
