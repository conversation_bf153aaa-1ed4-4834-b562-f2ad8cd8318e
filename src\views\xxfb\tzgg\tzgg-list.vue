<template>
  <el-form ref="vForm" class="lui-page" label-position="right" label-width="0"
           size="default" @submit.prevent>
    <el-row :gutter="20" class="lui-search-form">
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="input45296">
          <el-input
              v-model="listQuery.BT"
              placeholder="请输入标题"
              class="form-control"
              clearable
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" class="grid-cell">
        <el-form-item label="" prop="select14540">
          <el-select style="width: 200px" v-model="listQuery.ZT" placeholder="请选择发布状态" clearable>
            <el-option
                v-for="item in ztData"
                :key="item.CODE"
                :label="item.NAME"
                :value="item.CODE"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="4" class="grid-cell">
        <el-button type="primary" @click="onSearch"><el-icon>
          <Search/>
        </el-icon>
          查询
        </el-button>
        <el-button type="primary" class="lui-button-add" @click="add"><el-icon>
          <Plus/>
        </el-icon>
          新增
        </el-button>
      </el-col>

    </el-row>
    <el-row ref="grid71868" :gutter="12">
      <el-col :span="24" class="grid-cell">
        <div class="container-wrapper">
          <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 250px)" class="lui-table"
                    :border="true" :show-summary="false" size="default" :stripe="false"
                    v-loading="listLoading"
                    :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
            <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
                :index="indexMethod"
            ></el-table-column>
            <el-table-column
                property="BT"
                label="标题"
                min-width="300"
                header-align="center"
                align="left"
            ></el-table-column>
            <el-table-column
                property="WHSJ"
                label="发布时间"
                header-align="center"
                align="center"
                min-width="100"
            ></el-table-column>
            <el-table-column
                property="ORGNA_NAME"
                label="发布单位"
                header-align="center"
                align="center"
                min-width="150"
            ></el-table-column>
            <el-table-column
                property="GZLXMC"
                label="信息类型"
                header-align="center"
                align="center"
                width="100"
            ></el-table-column>
            <el-table-column
                property="ZTS"
                label="发布状态"
                header-align="center"
                align="center"
                width="100"
            >
              <template #default="scope">
                <span v-if="scope.row.ZT=='0'">未发布</span>
                <span v-if="scope.row.ZT=='1'">已发布</span>
              </template>
            </el-table-column>
            <el-table-column
                property="CZ"
                label="操作"
                header-align="center"
                align="center"
                min-width="200"
            >
              <template #default="scope">
                <el-button
                    @click="editClick(scope.row, scope.$index)"
                    class="lui-table-button">编辑
                </el-button>
                <el-button
                    @click="fbClick(scope.row, scope.$index)"
                    class="lui-table-button"
                    v-if="scope.row.ZT == '0'">发布
                </el-button>
                <el-button
                    class="lui-table-button"
                    @click="deleteClick(scope.row, scope.$index)">删除
                </el-button>
                <el-button
                    @click="viewClick(scope.row, scope.$index)"
                    class="lui-table-button">查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                         :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                         class="lui-pagination"
                         @size-change="getDataList" @current-change="getDataList" :total="total">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog
        custom-class="lui-dialog"
        v-if="showDialog"
        v-model="showDialog"
        title="信息发布维护"
        @closed="closeDialog"
        top="1vh"
        z-index="1000"
        width="80%"
    >
      <xxfbEdit
          v-if="showDialog"
          :XXFBID="XXFBID"
          :SFXYFK="SFXYFK"
          :userid="userid"
          :GZLX="GZLX"
          :pageFlag="pageFlag"
          @closeForm="closeDialog"
          :key="indexRy"
      ></xxfbEdit>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        v-if="showViewDialog"
        v-model="showViewDialog"
        :title="title"
        @closed="closeDialog"
        top="1vh"
        z-index="1000"
        width="80%"
    >
      <xxfbView
          v-if="showViewDialog"
          :XXFBID="XXFBID"
          :SFXYFK="SFXYFK"
          :userid="userid"
          :GZLX="GZLX"
          :pageFlag="pageFlag"
          @closeForm="closeDialog"
          :key="indexRy"
      ></xxfbView>
    </el-dialog>
  </el-form>
</template>
<script>
import {
  defineComponent,
  ref,
  shallowRef,
  toRefs,
  reactive,
  getCurrentInstance,
  watchEffect, onMounted
}
  from 'vue';
import axiosUtil from "../../../lib/axiosUtil";
import xxfbEdit from "../common/xxfbEdit";
import xxfbView from "../common/xxfbView";
import {ElMessage, ElMessageBox} from 'element-plus';
import { Search, Upload, Plus} from '@element-plus/icons-vue'
import comFun from '@src/lib/comFun.js';

export default defineComponent({
  // components: {xxfbEdit, xxfbView},
  components: {Search,Plus,Upload,xxfbEdit,xxfbView},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props) {
    const state = reactive({
      listQuery: {
        BT: "",
        ZT: "",
        page: 1,
        size: 10,
      },
      ztData: [
        {NAME: "未发布", CODE: "0"},
        {NAME: "已发布", CODE: "1"},
      ],
      tableData: [],
      params:{},
      XXFBID: null,
      SFXYFK: null,
      showDialog: false,
      showViewDialog: false,
      indexRy: 0,
      indexTab: 0,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      pageFlag: "",
      userid: null,
      GZLX: "",//类型
      showDialogQs: false,
      // 加载动画
      listLoading: true,
      title:'',
    });
    const onSearch = () => {
      state.listQuery.page = 1;
      state.listQuery.size = 10;
      getDataList();
    }
    const getDataList = (tableName, SFZYRY) => {
      state.listLoading = true;
      let params={
        ...state.listQuery,
      }
      axiosUtil.get('/backend/xxfb/common/selectGzxfList', params).then((res) => {
        state.tableData = res.data.list;
        state.total = res.data.total;
        state.listLoading = false;
      });
    }
    const fbClick = (row) => {
      let param = row;
      param.ZT = '1';
      axiosUtil.post('/backend/xxfb/common/saveXxfb', param).then((res) => {
        ElMessage({message: '发布成功', type: 'success',})
      });
    }
    //   点击查看
    const view = (row) => {
      state.id = row.DWCYBS;
      state.params.editable = false;
      state.params.operation = 'view';
      state.params.id = state.id;
      state.params.DWBS = state.DWBS;
      state.params.CBSBS = props.params.CBSBS;
      state.ryqkShow = true;
    }
    // 点击新增
    const add = () => {
      state.XXFBID = comFun.newId();
      state.GZLX = 'TZGG'
      state.showDialog = true;
      state.pageFlag = "add";
    }
    // 点击编辑
    const editClick = (row) => {
      state.XXFBID = row.XXFBID;
      state.showDialog = true;
      state.pageFlag = "edit";
    }

    //查看
    const viewClick = (row) => {
      state.title=row.GZLXMC+'查看'
      state.XXFBID = row.XXFBID;
      state.showViewDialog = true;
      state.pageFlag = "view";
    }
    // 点击删除
    const deleteClick = (row) => {
      ElMessageBox.confirm(
          '确定删除该条数据?',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
        axiosUtil.post('/backend/xxfb/common/deleteXxfb', row).then((res) => {
          ElMessage({message: '删除成功', type: 'success',})
          getDataList()
        });
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
    }

    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeDialog = () => {
      state.showDialog = false;
      getDataList()
    }
    onMounted(() => {
      getDataList()
    })
    return {
      ...toRefs(state),
      onSearch,
      getDataList,
      closeDialog,
      indexMethod,
      add,
      editClick,
      deleteClick,
      view,
      viewClick,
      fbClick
    }
  }
})
</script>
<style scoped>
:deep(.lui-card-form .el-form-item__label){
  border-bottom: none;
}
</style>