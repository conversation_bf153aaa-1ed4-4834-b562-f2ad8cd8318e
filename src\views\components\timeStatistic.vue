<template>
  <div>
    {{timeDifference}}
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount, onUnmounted} from "vue";


export default defineComponent({
  name: '',
  components: {},
  props: {
    value:{
      type: String
    }
  },
  setup(props, {emit}) {
    const state = reactive({
      timeDifference: '',
      intervalId: null
    })
    const init = () => {
      if(props.value){
        state.timeDifference=getDifTime(props.value)
        state.intervalId=setInterval(()=>{
          state.timeDifference=getDifTime(props.value)
        },1000)
      }
    }

    const getDifTime = (value) => {
      let nowTime=new Date()
      let befTime=new Date(value)
      // 计算时间差（以毫秒为单位）
      let diff = Math.abs(nowTime - befTime);

      // 将时间差转换为小时、分钟和秒
      const hours = Math.floor(diff / (1000 * 60 * 60));
      diff %= (1000 * 60 * 60);
      const minutes = Math.floor(diff / (1000 * 60));
      diff %= (1000 * 60);
      const seconds = Math.floor(diff / 1000);

      // 格式化输出
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`

    }

    onMounted(() => {
      init()
    })

    onUnmounted(()=>{
      if(state.intervalId){
        clearInterval(state.intervalId)
      }
    })

    return {
      ...toRefs(state),

    }
  }

})
</script>

<style scoped>

</style>
