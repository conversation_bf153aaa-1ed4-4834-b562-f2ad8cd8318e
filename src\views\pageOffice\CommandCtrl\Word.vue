<script setup>
import request from '@/utils/request';
import { ref, onMounted } from 'vue'

const poHtmlCode = ref('');

function OnPageOfficeCtrlInit() {
  pageofficectrl.CustomToolbar = false;//隐藏自定义工具栏
  pageofficectrl.OfficeToolbars = false;//隐藏office工具栏

  pageofficectrl.DisableSave = true;  //禁止保存
  pageofficectrl.DisableSaveAs = true; //禁止另存
  pageofficectrl.DisablePrint = true; //禁止打印
}

function openFile() {
  // 发起GET请求到后端Controller的路由
  return request({
    url: '/CommandCtrl/Word',
    method: 'get',
  });
}

onMounted(() => {
  // 请求后端打开文件
  openFile().then(response => {
    poHtmlCode.value = response;
  });
  //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  window.POPageMounted = { OnPageOfficeCtrlInit };//其中OnPageOfficeCtrlInit必须

})
</script>

<template>
  <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
  <div style="width:auto; height:900px;" v-html="poHtmlCode"></div>
</template>
