<template>
  <el-form :model="formData" ref="vForm" :rules="rules" class="lui-page" label-position="left" label-width="0"
           size="default">
    <el-row :span="24" style="text-align: center;margin-top: 15px;display: flex;align-items: center;gap: 10px">
      <el-input type="text" placeholder="请输入专业名称" v-model="listQuery.ZYMC" style="width: 200px" clearable></el-input>
      <el-button type="success" @click="getDataList"><el-icon><Search/></el-icon>查询</el-button>
    </el-row>
    <div class="container-wrapper" style="margin-top: 10px">
      <el-table
          class="lui-table"
          :data="tableData"
          style="width: 100%; margin-bottom: 20px"
          row-key="ID"
          border
          height="calc(80vh - 250px)"
          ref="singleTableRef"
          highlight-current-row
          default-expand-all

          @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="ZYMC" label="专业" sortable/>
        <el-table-column prop="SFZZY" label="主专业" header-align="center" align="center" width="70" v-if="showZZY">
          <template #default="{row}">
            <el-radio-group v-model="ZZY">
              <el-radio :disabled="!ZZYEditable(row.ZYBM)" :label="row.ZYBM"><br></el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-row :span="24" style="text-align: center">
            <span style="width: 100%;text-align: center">
                <el-button type="success" @click="confirmZy">确定</el-button>
                <el-button type="primary" @click="cancel">取消</el-button>
            </span>

    </el-row>
  </el-form>
</template>
<script>
import {
  defineComponent,
  toRefs,
  reactive,
  defineEmits,
  ref,
  getCurrentInstance,
  onMounted, computed
}
  from 'vue'
// 请求方法 get,post,put,del,downloadFile
import axiosUtil from "@lib/axiosUtil";
import {mixin} from '@src/assets/core';
import {ElMessage, ElMessageBox} from "element-plus";
import { Search, Upload, Plus} from '@element-plus/icons-vue'
export default defineComponent({
  name: '',
  components: {Search,Plus,Upload},
  props: {
    CBSBS: {
      type: String
    },
    showZZY: {
      type: Boolean,
      default: true
    },
  },

  setup(props, {emit}) {
    const ZZYEditable = computed(()=>(ZYBM)=>{
      let res=false
      state.currentRow.forEach(item=>{
        if(item.ZYBM===ZYBM){
          res=true
        }
      })
      if(state.ZZY===ZYBM&&!res){
        state.ZZY=null
      }
      return res
    })
    const state = reactive({
      listQuery: {
        ZYMC: '',
        ZT: '',
        page: 1,
        size: 10,
      },
      ZZY:null,
      currentRow: [],
      tableData: [],
      total: 0,
      rules: {},
      formData:{},

    })
    // 深度合并对象
    const instance = getCurrentInstance()
    const getDataList = () => {
      axiosUtil.get('/backend/zjgl/zjrkgl/selectZjzy', state.listQuery).then((res) => {
        var treeData = transData(res.data, 'ZYBM', 'FZYBM', 'children');
        state.tableData = treeData
        state.total = res.data.total
      });
    }
    let singleTableRef = ref(null)
    const resetListQuery = () => {
      state.listQuery.CBSMC = ''
      state.listQuery.ZT = ''
    }
    const handleSelectionChange = (val) => {
      state.currentRow = val
    }
    const selectEnable = (row) => {
      return row.SFMJ == '1'
    }


    const confirmZy = () => {
      emit('parentMethod', state.currentRow,state.ZZY)
    }
    /**
     * json格式转树状结构
     * @param   {json}      json数据
     * @param   {String}    id的字符串
     * @param   {String}    父id的字符串
     * @param   {String}    children的字符串
     * @return  {Array}     数组
     */
    const transData = (a, idStr, pidStr, childrenStr) => {
      var r = [], hash = {}, id = idStr, pid = pidStr, children = childrenStr, i = 0, j = 0, len = a.length;
      for (; i < len; i++) {
        a[i]['isparent'] = false;
        hash[a[i][id]] = a[i];
      }
      for (; j < len; j++) {
        var aVal = a[j], hashVP = hash[aVal[pid]];
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = []);
          hashVP[children].push(aVal);
        } else {
          r.push(aVal);
        }
      }
      //this.setIsParent(r);
      return r;
    }

    const setIsParent = (arr) => {
      for (var j = 0; j < arr.length; j++) {
        if (arr[j].children && arr[j].children.length > 0) {
          arr[j].isparent = true;
          this.setIsParent(arr[j].children);
        }
      }
    }
    const openDialog = (value) => {
      state.dialogData = value
      state.dialogVisible = true
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }
    onMounted(async () => {
      getDataList()

    })

    const cancel = () => {
      emit("close")
    }
    return {
      ...toRefs(state),
      getDataList,
      singleTableRef,
      confirmZy,
      resetListQuery,
      handleSelectionChange,
      selectEnable,
      transData,
      setIsParent,
      openDialog,
      closeForm,
      cancel,
      ZZYEditable
    }
  }
})
</script>
<style scoped>
/deep/.el-table .el-table__cell {
  padding: 0;
}
</style>
