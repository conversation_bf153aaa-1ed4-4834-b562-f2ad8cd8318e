<template>
  <div>
    <div class="task-step">
      <div class="task-step-node" v-for="(item,index) in taskStepList" :key="index">
        <div class="task-step-text">{{item.ACTIVITYNAME}}</div>
        <div><el-icon :class="[getStatus(item.STATUS)?.class]" :size="50"><LocationFilled/></el-icon></div>
        <div :class="['task-step-point',getStatus(item.STATUS)?.class]"><el-icon :size="15"><Select/></el-icon></div>
        <div class="task-step-time">{{item.SCOMPLETEDATE}}</div>
        <div :class="{'task-step-line': true, 'task-step-line-pass': item.STATUS==='3'}"
             v-if="index!==taskStepList.length-1"></div>
        <div class="task-step-blr" v-for="i in item.BLR">{{`${i.BLRMC}（${getStatus(item.STATUS)?.item}）`}}</div>
      </div>
    </div>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import {LocationFilled,Select} from '@element-plus/icons-vue'
import axiosUtil from "../../lib/axiosUtil";

export default defineComponent({
  name: '',
  components: {LocationFilled,Select},
  props: {
    processParams: {
      type: Object,
      default: {},
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      checkedTask: '',
      taskStepList:[],

      statusList:[
        {item: '未到达' ,class:'task-color-wait',value:'0'},
        {item: '待审批' ,class:'task-color-doing',value:'1'},
        {item: '退回' ,class:'task-color-back',value:'2'},
        {item: '通过' ,class:'task-color-pass',value:'3'},
      ]

    })

    const getTaskData = () => {
      let params={
        businessId: props.processParams.businessId,
        processId:props.processParams.processId,
        processInstanceId:props.processParams.processInstanceId,
      }
      axiosUtil.get('/backend/workFlow/selectLctData', params).then((res) => {
        let lastActivityId=0
        let SHData = res.data.SHData || []
        let LCData = res.data.LCData || []
        if(SHData.length>0){
          lastActivityId=Number(SHData[SHData.length-1].TWFEP_ORDER)
        }
        LCData=LCData.filter(item=> Number(item.TWFEP_ORDER)>lastActivityId)
        let resTaskList=[]
        SHData.forEach((item,index)=>{
          let taskNode=resTaskList.find(i=>i.ACTIVITYID===item.TWFEP_ORDER)
          if(taskNode){
            taskNode.BLR.push({
              BLRMC: item.RECIPIENTNAME,
              BLRDW: item.RECIPIENTDEPTNAME,
            })
          }else {
            resTaskList.push({
              STATUS: item.STATUS,
              ACTIVITYNAME: item.TASKNAME,
              ACTIVITYID: item.TWFEP_ORDER,
              SCOMPLETEDATE: item.SCOMPLETEDATE,
              BLR: [{
                BLRMC: item.RECIPIENTNAME,
                BLRDW: item.RECIPIENTDEPTNAME,
              }],
            })
          }
        })
        for(let i=0;i<LCData.length;i++){
          if(i<LCData.length-1){
            if(LCData[i].BLR[0].USER_LOGINNAME!=LCData[i+1].BLR[0].USER_LOGINNAME){
                resTaskList.push({
                  ACTIVITYNAME: LCData[i].BUSINESSNAME,
                  ACTIVITYID: LCData[i].TWFEP_ORDER,
                  SCOMPLETEDATE: '',
                  STATUS: '0',
                  BLR: LCData[i].BLR.map(i=>{return{BLRMC: i.USER_NAME,BLRDW: ''}})
                })
            }
          }else{
            resTaskList.push({
              ACTIVITYNAME: LCData[i].BUSINESSNAME,
              ACTIVITYID: LCData[i].TWFEP_ORDER,
              SCOMPLETEDATE: '',
              STATUS: '0',
              BLR: LCData[i].BLR.map(i=>{return{BLRMC: i.USER_NAME,BLRDW: ''}})
            })
          }
        }
        /* LCData.forEach(item=>{
          console.log(item);
          if(item.PROCESSID=='ZTB_XSSQLC')
          resTaskList.push({
            ACTIVITYNAME: item.BUSINESSNAME,
            ACTIVITYID: item.TWFEP_ORDER,
            SCOMPLETEDATE: '',
            STATUS: '0',
            BLR: item.BLR.map(i=>{return{BLRMC: i.USER_NAME,BLRDW: ''}})
          })
        }) */
        
        state.taskStepList=resTaskList
      })
    }

    const getStatus = (status) => {
      return state.statusList.find(item=>item.value===status)
    }


    onMounted(() => {
      getTaskData()
    })

    return {
      ...toRefs(state),
      getStatus

    }
  }

})
</script>

<style scoped>
.task-step{
  /*overflow: auto;*/
  /*overflow: hidden;*/
  display: flex;
  gap: 10px;
  padding-bottom: 20px;
  padding-top: 20px;
  padding-right: 50px;
}
.task-step-node{
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 180px;
  flex-shrink: 0;
  /*background-color: red;*/

}
.task-step-point{
  width: 15px;
  height: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 3px solid;
  border-radius: 50%;
  z-index: 668;
}
.task-step-time {
  position: absolute;
  left: calc(50% - 15px);
  width: 150px;
  top: 100px;
  color: #8c939d;
}

.task-step-line{
  position: absolute;
  left: calc(50% + 9px);
  width: 172px;
  height: 6px;
  top: 82px;
  z-index: 666;
  background-color: #c8c9cc;
}
.task-step-line-pass{
  background-color: #67C23A;
}

.task-step-text{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  width: 100%;
  font-weight: bold;
  font-size: 16px;
}

.task-step-blr{
  margin-right: -80px;
  margin-top: 30px;

}

.task-color-pass{
  color: #67C23A;
  border-color: #67C23A;
}
.task-color-doing{
  color: #409EFF;
  border-color: #409EFF;
}

.task-color-wait{
  color: #fcb752;
  border-color: #fcb752;
}
.task-color-back{
  color: #F56C6C;
  border-color: #F56C6C;
}
</style>
