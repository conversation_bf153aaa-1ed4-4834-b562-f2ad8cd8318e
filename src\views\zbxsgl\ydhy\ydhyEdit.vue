<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="24" class="grid-cell">
          <el-form-item label="会议名称：" prop="HYMC">
            <el-input v-model="formData.HYMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="会议时间：" prop="KBSJ">
            <el-date-picker
                v-model="formData.KBSJ"
                :disabled="!editable"
                type="datetime"
                clearable
                style="width: 100%"
                placeholder="请选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="grid-cell">
          <el-form-item label="会议地点：" prop="PBDD">
            <el-input v-model="formData.PBDD" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="评标项目数量：" prop="PBSL">
            <div style="margin-left: 10px">{{ formData.PBXMList.length }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="请选择评标项目：" prop="PBXMList">
            <div style="display: flex;height: 40px;align-items: center" v-if="editable">
              <el-button style="margin-left: 10px" type="primary" @click="dialogPBXMVisible=true">添加项目</el-button>
              <div style="color: rgba(140,147,157,0.68);font-weight: bold;margin-left: 40px">提示：一次会议只能评审一个项目，请添加项目。
              </div>
            </div>
            <div style="width: 100%">
              <el-table ref="datatable91634" :data="formData.PBXMList" height="250px"
                        :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                        :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
                <el-table-column type="index" width="60" fixed="left" label="序号" align="center"/>
                <el-table-column prop="XMMC" label="项目名称" align="center"
                                 :show-overflow-tooltip="true" min-width="160"></el-table-column>
                <el-table-column prop="XMBH" label="项目编号" align="center"
                                 :show-overflow-tooltip="true" width="160"></el-table-column>
                <el-table-column prop="SSDWMC" label="所属单位" align="center"
                                 :show-overflow-tooltip="true" min-width="160"></el-table-column>
                <el-table-column prop="BDSL" label="标段数量" align="center"
                                 :show-overflow-tooltip="true" width="100"></el-table-column>
              </el-table>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>

    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogPBXMVisible"
        v-model="dialogPBXMVisible"
        title="评审项目选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <xmChoose @close="dialogPBXMVisible=false" @submit="getPsxmRes" :params="params"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import xmChoose from "@views/zbxsgl/ydhy/xmChoose";


export default defineComponent({
  name: '',
  components: {xmChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      PBHYBS: props.params.id,
      formData: {
        CJR: vsAuth.getAuthInfo().permission.userLoginName,
        CJSJ: comFun.getNowTime(),
        SHZT: '0',
        HYLX: 'PBHY',

        PBXMList: []
      },
      rules: {
        HYMC: [{
          required: true,
          message: '字段值不可为空',
        }],
        PBDD: [{
          required: true,
          message: '字段值不可为空',
        }],
        KBSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        PBXMList: [{
          required: true,
          message: '字段值不可为空',
        }],
      },

      dialogPBXMVisible: false
    })

    const getFormData = () => {
      let params = {
        PBHYBS: state.PBHYBS
      }
      state.loading = true
      axiosUtil.get('/backend/xsgl/pbhyyd/selectYdhyById', params).then((res) => {
        state.formData = res.data
        state.loading = false
      })
    }


    const saveData = (type) => {
      if (type === 'save') {
        submitForm(type)
      } else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params = {
        ...state.formData,
        PBHYBS: state.PBHYBS,
        XGR: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
        SHZT: '0',
      };
      if (type === 'submit') {
        params.SHZT = '2'
      }
      state.loading = true
      axiosUtil.post('/backend/xsgl/pbhyyd/saveHyydForm', params).then(res => {
        ElMessage.success(`${type === 'submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading = false
      })
    }

    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getPsxmRes = (value) => {
      value.forEach(item => {
        if (!state.formData.PBXMList.find(ii => ii.XMID === item.XMID)) {
          state.formData.PBXMList.push({
            ...item,
          })
        }
      })
      state.dialogPBXMVisible = false
    }

    const getDMBData = async (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      const res = await axiosUtil.get('/backend/cbsxx/common/selectDMB', params);
      state[resList] = res.data
    }

    const closeForm = () => {
      emit('close')
    }

    onMounted(() => {
      if (props.params.operation !== 'add') {
        getFormData()
      }
    })

    return {
      ...toRefs(state),
      closeForm,
      saveData,
      getPsxmRes
    }
  }

})
</script>

<style scoped>

</style>
