<template>
  <div class="container">
    <el-form ref="vForm" class="lui-page" label-position="left" label-width="0"
             size="default">
      <el-row :gutter="20" class="lui-search-form">
        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="XMMC">
            <el-input ref="input45296" placeholder="请输入项目名称" v-model="listQuery.XMMC" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell" style="margin-right: 150px">
          <el-form-item label="" prop="XMMC">
            <div style="display: flex;align-items: center;gap: 10px">
              <el-date-picker
                  v-model="listQuery.KSSJ"
                  type="date"
                  clearable
                  style="width: 150px"
                  placeholder="请选择开始时间"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
              <div>-</div>
              <el-date-picker
                  v-model="listQuery.JSSJ"
                  type="date"
                  clearable
                  style="width: 150px"
                  placeholder="请选择结束时间"
                  value-format="YYYY-MM-DD"
              ></el-date-picker>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="grid-cell">
          <el-form-item label="" prop="CJR">
            <el-input ref="input45296" placeholder="请输入创建人" v-model="listQuery.CJR" type="text" clearable>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class="grid-cell">
          <div class="static-content-item" style="display: flex;">
            <el-button ref="button91277" @click="getDataList" type="primary">
              <el-icon>
                <Search/>
              </el-icon>
              查询
            </el-button>
            <el-button ref="button9527" type="primary" class="lui-button-add" @click="addData">
              <el-icon>
                <Plus/>
              </el-icon>
              新建
            </el-button>
          </div>
        </el-col>
      </el-row>

      <el-row ref="grid71868" :gutter="12">
        <el-col :span="24" class="grid-cell">
          <div class="container-wrapper">
            <el-table ref="datatable91634" :data="tableData" height="calc(100vh - 230px)"
                      :border="true" :show-summary="false" size="default" :stripe="false" class="lui-table"
                      :highlight-current-row="true" :cell-style="{padding: '10px 0 '}">
              <el-table-column type="index" width="60" fixed="left" label="序号" align="center" :index="indexMethod"/>
              <el-table-column prop="XMMC" label="项目名称" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="SSDWMC" label="所属单位" align="center"
                               :show-overflow-tooltip="true" min-width="160"></el-table-column>
              <el-table-column prop="XMLBMC" label="项目类别" align="center"
                               :show-overflow-tooltip="true" width="130"></el-table-column>
              <el-table-column prop="XSFSMC" label="招标方式" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="ZZFSMC" label="招标组织方式" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="CJRXM" label="创建人" align="center"
                               :show-overflow-tooltip="true" width="100"></el-table-column>
              <el-table-column prop="CJSJ" label="创建时间" align="center"
                               :show-overflow-tooltip="true" width="180"></el-table-column>
              <el-table-column prop="CZ" label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                  <span v-if="scope.row.SHZT==='0'">
                    <el-button size="small" class="lui-table-button" type="primary" @click="editRow(scope.row)">编辑
                    </el-button>
                    <el-button size="small" class="lui-table-button" type="primary" @click="deleteRow(scope.row)">删除
                    </el-button>
                  </span>
                  <span v-else>
                    <el-button size="small" class="lui-table-button" type="primary" @click="viewRow(scope.row)">查看
                    </el-button>
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background v-model:current-page="listQuery.page" v-model:page-size="listQuery.size"
                           :page-sizes="[10, 20, 50, 100]" layout="total, prev, pager, next, sizes"
                           class="lui-pagination"
                           @size-change="getDataList" @current-change="getDataList" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="选商项目补录"
        @closed="closeForm"
        z-index="1000"
        top="5vh"
        width="1400px">
      <div>
        <xsxmblEdit v-if="dialogVisible" :params="params" @close="closeForm"/>
      </div>
    </el-dialog>




  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import axiosUtil from "@lib/axiosUtil";
import comFun from "@lib/comFun";
import {ElMessage, ElMessageBox} from "element-plus";
import {Plus, Search, Upload} from "@element-plus/icons-vue";
import vsAuth from "@lib/vsAuth";
import xsxmblEdit from "@views/zbxsgl/xsxmbl/xsxmblEdit";


export default defineComponent({
  name: '',
  components: {Search, Upload, Plus,xsxmblEdit},
  props: {},
  setup(props, {emit}) {
    const state = reactive({
      listQuery: {
        page: 1,
        size: 10,
        KSSJ: '',
      },
      tableData: [],
      total: 0,
      params: {},
      dialogVisible: false,




    })


    const getDataList = () => {
      const params = {
        ...state.listQuery,
        orgnaId: vsAuth.getAuthInfo().permission.orgnaId,
      }
      axiosUtil.get('/backend/xsgl/xsxmbl/selectXsxmblPage', params).then((res) => {
        state.tableData = res.data.list
        state.total = res.data.total
      });
    }

    // 新增
    const addData = () => {
      state.params = {editable: true, id: comFun.newId(), operation: 'add'}
      state.dialogVisible = true
    }
    // 编辑
    const editRow = (row) => {
      state.params = {editable: true, id: row.FAID, operation: 'edit'}
      state.dialogVisible = true
    }

    const viewRow = (row) => {
      state.params = {editable: false, id: row.FAID, operation: 'view'}
      state.dialogVisible = true
    }

    const deleteRow = (row) => {
      ElMessageBox.confirm('你确定删除该数据，删除后无法恢复请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axiosUtil.del('/backend/xsgl/xsxmbl/delXsblxx?FAID=' + row.FAID, null).then(res => {
          ElMessage.success({
            message: '删除成功!'
          });
          getDataList()
        })
      }).catch(() => {
        ElMessage.info({
          message: '已取消删除'
        });
      });
    }


    const indexMethod = (index) => {
      return (state.listQuery.page - 1) * state.listQuery.size + index + 1;
    }
    const closeForm = () => {
      state.dialogVisible = false
      getDataList()
    }

    const timeAddMonth = (dateString,monthNum) => {
      const [year, month, day] = dateString.split('-').map(Number)

      let date = new Date(year, month - 1, day);

      date.setMonth(date.getMonth() + monthNum);

      const newYear = date.getFullYear();
      const newMonth = String(date.getMonth() + 1).padStart(2, '0');
      const newDay = String(date.getDate()).padStart(2, '0');

      return `${newYear}-${newMonth}-${newDay}`;
    }





    onMounted(() => {
      getDataList()
      state.listQuery.KSSJ=timeAddMonth(comFun.getNowDate(),-1)
    })

    return {
      ...toRefs(state),
      getDataList,
      indexMethod,
      editRow,
      addData,
      closeForm,
      deleteRow,
      viewRow,
      timeAddMonth,

    }
  }

})
</script>

<style scoped>

</style>
