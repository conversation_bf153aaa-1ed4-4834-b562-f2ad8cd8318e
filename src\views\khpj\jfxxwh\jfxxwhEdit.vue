<template>
  <div>
    <el-form :model="formData" ref="vForm" :rules="rules" class="lui-card-form" label-position="right"
             label-width="160px"
             size="default" v-loading="loading" @submit.prevent>
      <div style="margin-top: 10px;margin-bottom: 10px;font-size:16px;font-weight: bold;
      border-bottom: 3px solid #2A96F9;width: 100px;text-align: center;padding-bottom: 2px">
        基本信息
      </div>
      <el-row :gutter="0" class="grid-row">
        <el-col :span="8" class="grid-cell">
          <el-form-item label="所属单位：" prop="JSDWID">
            <el-select v-model="formData.JSDWID" class="full-width-input"
                       :disabled="!editable" @change="(value)=>formData.JSDWMC=EJDWOptions.find(item=>item.ORGNA_ID===value)?.ORGNA_NAME"
                       clearable>
              <el-option v-for="(item, index) in EJDWOptions" :key="index" :label="item.ORGNA_NAME"
                         :value="item.ORGNA_ID" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="16" class="grid-cell">
          <el-form-item label="项目名称：" prop="XMMC">
            <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable>
              <!-- <template #append v-if="editable">
                <el-button @click="dialogXMXZVisible=true">选择</el-button>
              </template> -->
              <el-input v-model="formData.XMMC" type="text" placeholder="请输入" clearable :disabled="!editable">
            </el-input>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="承包商名称：" prop="PJNR">
            <div style="margin-left: 10px">{{formData.CBSDWQC}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="grid-cell">
          <el-form-item label="队伍名称：" prop="DWMC">
            <el-input v-model="formData.DWMC" type="text" readonly>
              <template #append v-if="editable">
                  <el-button @click="dialogDWXZVisible=true">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="单项工程名称：" prop="ZXMMC">
            <!-- <div style="margin-left: 10px">{{formData.ZXMMC}}</div> -->
            <el-input v-model="formData.ZXMMC" type="text" placeholder="请输入" clearable :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="奖罚时间：" prop="JFSJ">
            <el-date-picker
                v-model="formData.JFSJ"
                :disabled="!editable"
                type="date"
                clearable
                style="width: 100%"
                placeholder="请选择"
                value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="奖罚类型：" prop="JFLX">
            <el-select v-model="formData.JFLX" class="full-width-input"
                       :disabled="!editable"
                       clearable>
              <el-option v-for="(item, index) in JFLXOptions" :key="index" :label="item.DMMC"
                         :value="item.DMXX" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="grid-cell">
          <el-form-item label="金额：" prop="JE">
            <el-input v-model="formData.JE" type="text" placeholder="请输入" clearable :disabled="!editable"
                      @input="formData.JE=formData.JE.replace(/^([0-9-]\d*\.?\d{0,2})?.*$/, '$1')">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell no-border-bottom">
          <el-form-item label="奖罚原因：" prop="JFYY">
            <el-input v-model="formData.JFYY" :rows="3"
                      type="textarea" clearable
                      :disabled="!editable"/>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="grid-cell">
          <el-form-item label="附件资料：" prop="ZL">
            <vsfileupload style="margin-left: 10px" v-model:files="FJZLFiles" :busId="params.id"
                          :key="params.id"
                          :editable="editable" ywlb="jfxx"/>
          </el-form-item>
        </el-col>
      </el-row>


      <div style="width: 100%;margin-top: 20px;justify-content: center;display: flex">
        <el-button type="success" @click="saveData('save')" v-if="editable">保存</el-button>
        <el-button type="primary" @click="saveData('submit')" v-if="editable">提交</el-button>
        <el-button @click="closeForm">返回</el-button>
      </div>
    </el-form>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogXMXZVisible"
        v-model="dialogXMXZVisible"
        title="项目选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <xmChoose v-if="dialogXMXZVisible" @close="dialogXMXZVisible=false" @submit="getXMXZRes"/>
      </div>
    </el-dialog>

    <el-dialog
        custom-class="lui-dialog"
        :close-on-click-modal="false"
        v-if="dialogDWXZVisible"
        v-model="dialogDWXZVisible"
        title="队伍选择"
        z-index="1000"
        top="6vh"
        width="1100px">
      <div>
        <dwChoose v-if="dialogDWXZVisible" @close="dialogDWXZVisible=false" @submit="getDWXZRes"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent, reactive, toRefs, getCurrentInstance, onMounted, onBeforeMount} from "vue";
import vsAuth from "@lib/vsAuth";
import comFun from "@lib/comFun";
import axiosUtil from "@lib/axiosUtil";
import {ElMessage} from "element-plus";
import vsfileupload from "@views/components/vsfileupload";
import xmChoose from "@views/khpj/xmwtsb/xmChoose";
import dwChoose from "@views/khpj/xmwtsb/dwChoose";

export default defineComponent({
  name: '',
  components: {vsfileupload,xmChoose,dwChoose},
  props: {
    params: {
      type: Object,
      required: true
    },
  },
  setup(props, {emit}) {
    const state = reactive({
      userInfo: vsAuth.getAuthInfo().permission,
      loading: false,
      editable: props.params.editable,
      JFID: props.params.id,
      formData: {
        CJRZH: vsAuth.getAuthInfo().permission.userLoginName,
        CJRXM: vsAuth.getAuthInfo().permission.userName,
        CJDWID: vsAuth.getAuthInfo().permission.orgnaId,
        CJSJ: comFun.getNowTime(),
        SHZT: '0'
      },
      rules: {
        JSDWID: [{
          required: true,
          message: '字段值不可为空',
        }],
        // XMMC: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        JFSJ: [{
          required: true,
          message: '字段值不可为空',
        }],
        JFLX: [{
          required: true,
          message: '字段值不可为空',
        }],
        // JE: [{
        //   required: true,
        //   message: '字段值不可为空',
        // }],
        JFYY: [{
          required: true,
          message: '字段值不可为空',
        }],
      },


      EJDWOptions: [],
      JFLXOptions: [],

      FJZLFiles: [],
      dialogXMXZVisible: false,
      dialogDWXZVisible: false
    })

    const getFormData = () => {
      let params={
        JFID: state.JFID
      }
      state.loading=true
      axiosUtil.get('/backend/sckhpj/jfxxwh/selectJfxxById', params).then((res) => {
        state.formData=res.data
        state.loading=false
      })
    }

    const saveData = (type) => {
      if(type==='save'){
        submitForm(type)
      }else {
        validateForm().then(res => {
          if (res) {
            submitForm(type)
          }
        })
      }

    }

    const submitForm = (type) => {
      let params={
        ...state.formData,
        JFID: state.JFID,
        XGRZH: state.userInfo.userLoginName,
        XGSJ: comFun.getNowTime(),
      }
      if(type==='submit'){
        params.SHZT='1'
      }
      state.loading=true
      axiosUtil.post('/backend/sckhpj/jfxxwh/saveJfxxForm',params).then(res=>{
        ElMessage.success(`${type==='submit' ? '提交' : '保存'}成功`)
        closeForm()
        state.loading=false
      })
    }

    const getEjdwList = () => {
      axiosUtil.get('/backend/common/selectEjdwList',null).then(res=>{
        state.EJDWOptions=res.data || []
      })
    }


    const instance = getCurrentInstance()
    const validateForm = () => {
      return new Promise(resolve => {
        instance.proxy.$refs['vForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            ElMessage({
              message: '请完善页面信息',
              type: 'error',
            })
            resolve(false)
          }
        })
      })
    }

    const getXMXZRes = (value) => {
      state.formData.DWMC=value.DWMC
      state.formData.CBSDWQC=value.CBSDWQC
      state.formData.DWWYBS=value.DWWYBS
      state.formData.CBSWYBS=value.CBSWYBS
      state.formData.JSDWMC=value.JSDWMC
      state.formData.JSDWID=value.JSDWID
      state.formData.XMID=value.XMID
      state.formData.XMMC=value.XMMC
      state.formData.XMBM=value.XMBM
      state.formData.XMZYMC=value.XMZYMC
      state.formData.XMZYBM=value.XMZYBM
      state.formData.ZXMMC=value.ZXMMC
      state.formData.ZXMID=value.ZXMID
      state.dialogXMXZVisible=false

    }
    const getDWXZRes = (value) =>{
      state.formData.DWMC=value.DWMC
      state.formData.CBSDWQC=value.CBSDWQC
      state.formData.DWWYBS=value.DWWYBS
      state.formData.CBSWYBS=value.CBSWYBS
      state.dialogDWXZVisible = false
    }

    const closeForm = () => {
      emit('close')
    }

    const getDMBData = (DMLBID, resList) => {
      let params = {
        DMLBID
      }
      axiosUtil.get('/backend/common/selectDMB', params).then(res=>{
        state[resList] = res.data || []
      })
    }
    const getUserTwoOrg = () =>{
      axiosUtil.get('/backend/common/selectOrganByUserId', {USER_ID: state.userInfo.userId}).then(res=>{
        state.formData.JSDWID = res.data[0]?.ORGNA_TWO_ID
      })
    }

    onMounted(() => {
      if(props.params.operation!=='add'){
        getFormData()
      }
      getEjdwList()
      getDMBData('JFLX', 'JFLXOptions')
      getUserTwoOrg()

    })

    return {
      ...toRefs(state),
      saveData,
      getXMXZRes,
      getUserTwoOrg,
      getDWXZRes

    }
  }

})
</script>

<style scoped>

</style>
