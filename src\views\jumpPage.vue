<template>
  <div>

  </div>
</template>


<script>
  import VSAuth from "../lib/vsAuth";
  import router from "../assets/core/router/index.js"
  import { vue,auth,runtimeCfg ,mixin} from '../assets/core/index'
  import { createRouter, createWebHistory,useRouter } from 'vue-router'
  import axiosUtil from "@lib/axiosUtil";
  import { ElMessage } from 'element-plus';
  export default {
    components: {
    },
    setup(){
      const { currentRoute } = useRouter();
      const route = currentRoute.value;
      const {
        vsuiRouter,
      } = mixin();
      const checkYhxx = (token) =>{
        let param={
          token:token
        }
        axiosUtil.get('/backend/loginApp/checkJwt', param).then((res) => {
         if (res.status=='success'){
           router.push("/dashboard");
         }else{
           ElMessage({
             message: `登录过程出错，失败原因是：${res.message}`,
             dangerouslyUseHTMLString: true,
             type: 'error'
           })
           router.push("/login");
         }
        });
      }
      const queryIslogined = () => {
        console.log(VSAuth.getAuthInfo())
        if(VSAuth.getAuthInfo().isLogined){
          sessionStorage.setItem("userName",VSAuth.getAuthInfo().realName);
          sessionStorage.setItem("permission",JSON.stringify(VSAuth.getAuthInfo().permission));
          console.log(VSAuth.getAuthInfo().permission)
          sessionStorage.setItem("isLogin",true);
          // this.$router.push("/dashboard");
          router.push('/dashboard');
        }
        else{
          console.log( route.query,' this.$router.param')
          if (route.query.token){
            checkYhxx(route.query.token)
          }else{
            router.push("/login");
          }
          //this.$router.push("/login");
        }
      }
      vue.onMounted(()=>{
        queryIslogined()
      })

      return {
        queryIslogined,
        checkYhxx,
      }
    },

};
</script>

