import {vsuiapi} from "../assets/core";

export default {
    /**
     * 关闭当前tab且打开一个新的tab
     * @param {string} title 页面标题
     * @param {string} routePath 路由地址(必须是路由中存在的地址)，如希望使用非路由中的组件，请使用addTabByCustomName
     * @param {object} componentProps 组件默认参数(此参数为undefined的情况下，会使用路由定义中的默认参数)
     * @param {object} data 扩展数据，通过框架UI菜单操作默认传入的是菜单模块信息
     */
    openNewTabClose(title, routePath, componentProps, data) {
        vsuiapi.content.multipage.closeTabByIndex(vsuiapi.content.multipage.getCurrentTabInfo().index);
        vsuiapi.content.multipage.addTabByRoutePath(title, routePath, componentProps, data);
        vsuiapi.content.multipage.setCurrentTabByPath(routePath);
    },

    /**
     * 添加一个新的tab页面
     * @param {string} title 页面标题
     * @param {string} name 页面唯一字符串，用户自定义唯一识别符，如希望使用路由地址对应的组件，请使用addTabByRoutePath
     * @param {vue} componentInfo 组件对象
     * @param {object} componentProps 组件默认参数
     * @param {object} data 扩展数据，通过框架UI菜单操作默认传入的是菜单模块信息
     */
    addTabByCustomName(title, name, componentInfo, componentProps, data) {
        vsuiapi.content.multipage.addTabByCustomName(title, name, componentInfo, componentProps, data);
        try{
            vsuiapi.content.multipage.setCurrentTabByPath(name);
        }catch (e){}
    },

    /**
     * 添加一个新的tab页面
     * @param {string} title 页面标题
     * @param {string} routePath 路由地址(必须是路由中存在的地址)，如希望使用非路由中的组件，请使用addTabByCustomName
     * @param {object} componentProps 组件默认参数(此参数为undefined的情况下，会使用路由定义中的默认参数)
     * @param {object} data 扩展数据，通过框架UI菜单操作默认传入的是菜单模块信息
     */
    addTabByRoutePath(title, routePath, componentProps, data) {
        vsuiapi.content.multipage.addTabByRoutePath(title, routePath, componentProps, data);
        vsuiapi.content.multipage.setCurrentTabByPath(routePath);
    },

    /**
     * 获取当前选中项的信息
     * @returns {index:索引,tabItem:调用addTab时传入的信息}
     */
    getCurrentTabInfo() {
        return vsuiapi.content.multipage.getCurrentTabInfo()
    },

    /**
     * 通过索引设置当前选中项
     * @param {*} index
     */
    setCurrentTabByIndex(index) {
        vsuiapi.content.multipage.setCurrentTabByIndex(index)
    },

    /**
     * 通过名称（默认是路由地址,如果是自己创建的tab，则传入自定义的名称）设置当前选中项
     * @param {*} path 通过框架UI操作默认是路由地址,如果是自己创建的tab，则传入自定义的名称
     */
    setCurrentTabByPath(path) {
        vsuiapi.content.multipage.setCurrentTabByPath(path)
    },

    /**
     * 通过索引关闭指定的tab
     * @param {*} index
     */
    closeTabByIndex(index) {
        vsuiapi.content.multipage.closeTabByIndex(index)
    },

    /**
     * 通过名称（默认是路由地址,如果是自己创建的tab，则传入自定义的名称）关闭指定的tab
     * @param {*} path 通过框架UI操作默认是路由地址,如果是自己创建的tab，则传入自定义的名称
     */
    closeTabByPath(path) {
        vsuiapi.content.multipage.closeTabByPath(path)
    },

    /**
     * 获取tab的数量
     */
    getTabsLength() {
        return  vsuiapi.content.multipage.getTabsLength()
    },
    /**
     * 关闭当前页面
     */
    closeNowTab(){
        vsuiapi.content.multipage.closeTabByIndex(vsuiapi.content.multipage.getCurrentTabInfo().index);
    }



}
